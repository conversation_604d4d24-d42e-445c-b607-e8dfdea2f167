{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/fight/RogueUI.ts"], "names": ["_decorator", "Label", "Node", "EventMgr", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ButtonPlus", "BattleUI", "MainEvent", "RogueSelectIcon", "ccclass", "property", "RogueUI", "freshTimes", "excludeTimes", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onLoad", "on", "RogueSelectClick", "onRogueSelectClick", "btnClose", "addClick", "closeUI", "nodeFresh", "getComponentInChildren", "onFresh", "nodeExclude", "onCancel", "index", "rogueSelectIcons", "for<PERSON>ach", "element", "updateActive", "btn", "string", "updateStatus", "updateFreshTimes", "currentText", "match", "count", "parseInt", "updateCancelTimes", "openUI", "onDestroy", "targetOff", "onShow", "args", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAoBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC3BC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAGjBa,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAAC;AAAA;AAAA,6CAAD,C,2BAXb,MACaC,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAahCC,UAbgC,GAaZ,IAbY;AAAA,eAchCC,YAdgC,GAcV,IAdU;AAAA;;AAgBZ,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AAClDC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,gBAAtB,EAAwC,KAAKC,kBAA7C,EAAiE,IAAjE;AACA,eAAKC,QAAL,CAAcC,QAAd,CAAuB,KAAKC,OAA5B,EAAqC,IAArC;AACA,eAAKC,SAAL,CAAeC,sBAAf;AAAA;AAAA,wCAAkDH,QAAlD,CAA2D,KAAKI,OAAhE,EAAyE,IAAzE;AACA,eAAKC,WAAL,CAAiBF,sBAAjB;AAAA;AAAA,wCAAoDH,QAApD,CAA6D,KAAKM,QAAlE,EAA4E,IAA5E;AACA,eAAKhB,UAAL,GAAkB,KAAKY,SAAL,CAAeC,sBAAf,CAAsC1B,KAAtC,CAAlB;AACA,eAAKc,YAAL,GAAoB,KAAKc,WAAL,CAAiBF,sBAAjB,CAAwC1B,KAAxC,CAApB;AAEH;;AACOqB,QAAAA,kBAAkB,CAACS,KAAD,EAAgB;AACtC,eAAKC,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqBJ,KAArB;AACH,WAFD;AAGH;;AAEDH,QAAAA,OAAO,GAAG;AACN,eAAKI,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqB,CAArB;AACH,WAFD;AAGH;;AACDL,QAAAA,QAAQ,GAAG;AACP,eAAKE,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqB,CAArB;AACH,WAFD;AAGA,cAAIC,GAAG,GAAG,KAAKP,WAAL,CAAiBF,sBAAjB;AAAA;AAAA,uCAAV;;AACA,cAAIS,GAAG,CAACT,sBAAJ,CAA2B1B,KAA3B,EAAkCoC,MAAlC,IAA4C,IAAhD,EAAsD;AAClDD,YAAAA,GAAG,CAACT,sBAAJ,CAA2B1B,KAA3B,EAAkCoC,MAAlC,GAA2C,IAA3C;AACA,iBAAKL,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,cAAAA,OAAO,CAACI,YAAR,CAAqB,CAArB;AACH,aAFD;AAGH,WALD,MAKO;AACHF,YAAAA,GAAG,CAACT,sBAAJ,CAA2B1B,KAA3B,EAAkCoC,MAAlC,GAA2C,IAA3C;AACA,iBAAKL,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,cAAAA,OAAO,CAACI,YAAR,CAAqB,CAArB;AACH,aAFD;AAGH;AACJ;;AACDC,QAAAA,gBAAgB,GAAG;AACf,gBAAMC,WAAW,GAAG,KAAK1B,UAAL,CAAgBuB,MAApC;AACA,gBAAMI,KAAK,GAAGD,WAAW,CAACC,KAAZ,CAAkB,KAAlB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,gBAAIC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAApB;;AACA,gBAAIC,KAAK,GAAG,CAAZ,EAAe;AACXA,cAAAA,KAAK;AACL,mBAAK5B,UAAL,CAAgBuB,MAAhB,GAA0B,QAAOK,KAAM,EAAvC;AACH;AACJ;AACJ;;AACDE,QAAAA,iBAAiB,GAAG;AAChB,gBAAMJ,WAAW,GAAG,KAAKzB,YAAL,CAAkBsB,MAAtC;AACA,gBAAMI,KAAK,GAAGD,WAAW,CAACC,KAAZ,CAAkB,KAAlB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,gBAAIC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAApB;;AACA,gBAAIC,KAAK,GAAG,CAAZ,EAAe;AACXA,cAAAA,KAAK;AACL,mBAAK3B,YAAL,CAAkBsB,MAAlB,GAA4B,QAAOK,KAAM,EAAzC;AACH;AACJ;AACJ;;AACY,cAAPjB,OAAO,GAAG;AACZ,gBAAM;AAAA;AAAA,8BAAMoB,MAAN;AAAA;AAAA,mCAAN;AACA;AAAA;AAAA,8BAAMpB,OAAN,CAAcZ,OAAd;AACH;;AACSiC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEW,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAAG;;AACnC,cAANC,MAAM,CAAC,GAAGD,IAAJ,EAAgC,CAAG;;AAClC,cAAPE,OAAO,CAAC,GAAGF,IAAJ,EAAgC,CAAG;;AAvFhB,O;;;;;iBAGT,I;;;;;;;iBAEL,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEkB,E", "sourcesContent": ["import { _decorator, Button, Label, Node } from 'cc';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { BaseUI, UILayer, UIMgr } from '../../UIMgr';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport { BattleUI } from '../BattleUI';\r\nimport List from '../../common/components/list/List';\r\nimport { MainEvent } from '../MainEvent';\r\nimport { RogueSelectIcon } from './RogueSelectIcon';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"RogueUI\")\r\nexport class RogueUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus = null;\r\n    @property(Node)\r\n    nodeFresh: Node = null;\r\n    @property(Node)\r\n    nodeExclude: Node = null;\r\n    @property(Node)\r\n    nodeAbility: Node = null;\r\n    @property([RogueSelectIcon])\r\n    rogueSelectIcons: RogueSelectIcon[] = [];\r\n\r\n    freshTimes: Label = null;\r\n    excludeTimes: Label = null;\r\n\r\n    public static getUrl(): string { return \"ui/main/fight/RogueUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    protected onLoad(): void {\r\n        EventMgr.on(MainEvent.RogueSelectClick, this.onRogueSelectClick, this);\r\n        this.btnClose.addClick(this.closeUI, this);\r\n        this.nodeFresh.getComponentInChildren(ButtonPlus).addClick(this.onFresh, this);\r\n        this.nodeExclude.getComponentInChildren(ButtonPlus).addClick(this.onCancel, this);\r\n        this.freshTimes = this.nodeFresh.getComponentInChildren(Label);\r\n        this.excludeTimes = this.nodeExclude.getComponentInChildren(Label);\r\n\r\n    }\r\n    private onRogueSelectClick(index: number) {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(index);\r\n        });\r\n    }\r\n\r\n    onFresh() {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(0);\r\n        });\r\n    }\r\n    onCancel() {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(0);\r\n        });\r\n        let btn = this.nodeExclude.getComponentInChildren(ButtonPlus);\r\n        if (btn.getComponentInChildren(Label).string == \"排除\") {\r\n            btn.getComponentInChildren(Label).string = \"取消\";\r\n            this.rogueSelectIcons.forEach(element => {\r\n                element.updateStatus(2);\r\n            });\r\n        } else {\r\n            btn.getComponentInChildren(Label).string = \"排除\";\r\n            this.rogueSelectIcons.forEach(element => {\r\n                element.updateStatus(1);\r\n            });\r\n        }\r\n    }\r\n    updateFreshTimes() {\r\n        const currentText = this.freshTimes.string;\r\n        const match = currentText.match(/\\d+/);\r\n        if (match) {\r\n            let count = parseInt(match[0], 10);\r\n            if (count > 0) {\r\n                count--;\r\n                this.freshTimes.string = `剩余次数：${count}`;\r\n            }\r\n        }\r\n    }\r\n    updateCancelTimes() {\r\n        const currentText = this.excludeTimes.string;\r\n        const match = currentText.match(/\\d+/);\r\n        if (match) {\r\n            let count = parseInt(match[0], 10);\r\n            if (count > 0) {\r\n                count--;\r\n                this.excludeTimes.string = `剩余次数：${count}`;\r\n            }\r\n        }\r\n    }\r\n    async closeUI() {\r\n        await UIMgr.openUI(BattleUI)\r\n        UIMgr.closeUI(RogueUI)\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this)\r\n    }\r\n\r\n    async onShow(...args: any[]): Promise<void> { }\r\n    async onHide(...args: any[]): Promise<void> { }\r\n    async onClose(...args: any[]): Promise<void> { }\r\n}\r\n"]}