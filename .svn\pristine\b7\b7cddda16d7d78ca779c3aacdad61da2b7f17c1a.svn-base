import { _decorator, Component, Sprite, tween, isValid, UITransform, view } from 'cc';
import { GameConst } from '../../const/GameConst';
import { GameIns } from '../../GameIns';
import GameEnum from '../../const/GameEnum';
import GameMapRun from '../map/GameMapRun';

const { ccclass, property } = _decorator;

@ccclass('NodeMove')
export default class NodeMove extends Component {
    private moveSpeed: number = 0;       // 垂直移动速度
    private sideswaySpeed: number = 0;  // 水平移动速度
    private layer: number = 0;          // 节点所属图层

    /**
     * 设置节点的移动数据
     * @param xSpeed 水平移动速度
     * @param ySpeed 垂直移动速度
     * @param layer 节点所属图层
     */
    setData(xSpeed: number, ySpeed: number, layer: number): void {
        this.sideswaySpeed = xSpeed;
        this.moveSpeed = ySpeed;
        this.layer = layer;
    }

    /**
     * 每帧更新节点的位置
     * @param deltaTime 时间增量
     */
    update(deltaTime: number): void {
        if (!GameConst.GameAble) {
            return;
        }

        // 限制 deltaTime 的最大值
        if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
        }

        const gameState = GameIns.gameRuleManager.gameState;
        if (
            gameState === GameEnum.GameState.Battle ||
            gameState === GameEnum.GameState.Sortie ||
            gameState === GameEnum.GameState.Ready ||
            gameState === GameEnum.GameState.WillOver ||
            gameState === GameEnum.GameState.Over
        ) {
            // 更新节点位置
            let posX = this.node.position.x - deltaTime * this.moveSpeed;
            let posY = this.node.position.y + deltaTime * this.sideswaySpeed;
            this.node.setPosition(posX, posY);

            // 检查节点是否超出视图范围
            /*const layerData = GameMapRun.instance.LayerData.get(this.layer);
            if (this.node.y + this.node.getComponent(UITransform).height < layerData.ViewBot) {
                // const enemyComponent = this.getComponent(EnemyBuild);
                // if (enemyComponent) {
                //     EnemyManager.EnemyMgr.removePlane(enemyComponent);
                // }
            } else if (
                this.node.position.x + this.node.getComponent(UITransform).width < -view.getVisibleSize().width / 2 - 500 ||
                this.node.position.x > view.getVisibleSize().width / 2 + 500
            ) {
                // 节点超出屏幕宽度范围
            }*/
        }
    }
}