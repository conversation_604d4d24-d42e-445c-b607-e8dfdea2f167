
import { _decorator, Label } from 'cc';

import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { DataMgr } from '../../../Data/DataManager';
import { EventMgr } from '../../../event/EventManager';
import { MyApp } from '../../../MyApp';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt } from '../../UIMgr';
import { OpenEquipInfoUISource } from './PlaneTypes';

const { ccclass, property } = _decorator;

@ccclass('PlaneEquipInfoUI')
export class PlaneEquipInfoUI extends BaseUI {
    public static getUrl(): string { return "ui/main/plane/PlaneEquipInfoUI"; }
    public static getLayer(): UILayer { return UILayer.PopUp }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: true }
    }
    private _planeEquipInfo: csproto.cs.ICSItem = null;

    @property(ButtonPlus)
    replaceEquipBtn: ButtonPlus = null;
    @property(ButtonPlus)
    unEquipBtn: ButtonPlus = null;
    @property(ButtonPlus)
    levelUpEquipBtn: ButtonPlus = null;
    @property(ButtonPlus)
    multiLevelUpEquipBtn: ButtonPlus = null;

    protected onLoad(): void {
        this.levelUpEquipBtn.addClick(this.onClickLevelUpEquip, this)
        this.multiLevelUpEquipBtn.addClick(this.onClickMultiLevelUpEquip, this)
        this.unEquipBtn.addClick(this.onClickUnEquip, this)
        this.replaceEquipBtn.addClick(this.onClickReplaceEquip, this)
    }

    async onShow(planeEquipInfo: csproto.cs.ICSItem, source: OpenEquipInfoUISource): Promise<void> {
        const tbEquip = MyApp.lubanMgr.table.TbEquip
        if (source == OpenEquipInfoUISource.DisPlay) {
            this.replaceEquipBtn.node.active = false;
            this.unEquipBtn.node.parent.active = true
        } else {
            this.replaceEquipBtn.node.active = true
            this.unEquipBtn.node.parent.active = false
            const equipped = DataMgr.equip.eqSlots.getEquippedByClass(tbEquip.get(planeEquipInfo.item_id)?.equipClass)
            if (equipped) {
                this.replaceEquipBtn.getComponentInChildren(Label).string = "替换"
            } else {
                this.replaceEquipBtn.getComponentInChildren(Label).string = "装备"
            }
        }
        this.getComponentInChildren(Label).string = MyApp.lubanTables.TbEquip.get(planeEquipInfo.item_id)?.name
        this._planeEquipInfo = planeEquipInfo;
    }

    private onClickReplaceEquip() {
        DataMgr.equip.eqSlots.equip(this._planeEquipInfo)
    }

    private onClickUnEquip() {
        DataMgr.equip.eqSlots.unequip(this._planeEquipInfo.guid)
        UIMgr.hideUI(PlaneEquipInfoUI)
    }

    private onClickLevelUpEquip() {
        //EventMgr.emit(PlaneUIEvent.LevelUpEquip)
    }
    private onClickMultiLevelUpEquip() {
        //EventMgr.emit(PlaneUIEvent.MultiLevelUpEquip)
    }

    async onHide(...args: any[]): Promise<void> { }
    async onClose(...args: any[]): Promise<void> {
        EventMgr.targetOff(this)
    }
    protected update(dt: number): void {
    }

}

