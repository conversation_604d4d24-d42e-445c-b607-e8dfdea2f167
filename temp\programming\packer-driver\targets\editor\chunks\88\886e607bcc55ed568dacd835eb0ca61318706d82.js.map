{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts"], "names": ["GameEnum", "GameType", "Common", "Expedition", "Gold", "Boss", "GameState", "Idle", "Ready", "<PERSON><PERSON><PERSON>", "Battle", "Pause", "WillOver", "Over", "BossIn", "EnemyAction", "<PERSON><PERSON><PERSON>", "GoUp", "Track", "Transform", "AttackPrepare", "AttackIng", "AttackOver", "Leave", "EnemyType", "Normal", "Missile", "<PERSON><PERSON><PERSON>", "Ligature", "LigatureLine", "LigatureUnit", "Build", "Ship", "ShipHeart", "Train", "ParkourItem", "GoldShip", "GoldBox", "BossLigature", "BossUnit", "BossNormal", "BossSnake", "BossUFO", "EnemyCollideLevel", "None", "MainBullet", "Main", "EnemyDestroyType", "Die", "TrackOver", "TimeOver", "BossAction", "Appear", "Switch", "Blast", "Enemy<PERSON>uff", "Ice", "Fire", "<PERSON><PERSON><PERSON>", "EnemyAttr", "Doctor", "Shield"], "mappings": ";;;;;;;;;;;;;;0BACWA,Q,GAAW;AAElB;AACJ;AACA;AACIC,QAAAA,QAAQ,EAAE;AACNC,UAAAA,MAAM,EAAE,CADF;AAENC,UAAAA,UAAU,EAAE,CAFN;AAGNC,UAAAA,IAAI,EAAE,CAHA;AAINC,UAAAA,IAAI,EAAE;AAJA,SALQ;;AAYlB;AACJ;AACA;AACIC,QAAAA,SAAS,EAAE;AACPC,UAAAA,IAAI,EAAE,CADC;AAEPC,UAAAA,KAAK,EAAE,CAFA;AAGPC,UAAAA,MAAM,EAAE,CAHD;AAIPC,UAAAA,MAAM,EAAE,CAJD;AAKPC,UAAAA,KAAK,EAAE,CALA;AAMPC,UAAAA,QAAQ,EAAE,CANH;AAOPC,UAAAA,IAAI,EAAE,CAPC;AAQPC,UAAAA,MAAM,EAAE;AARD,SAfO;;AA0BlB;AACJ;AACA;AACIC,QAAAA,WAAW,EAAE;AACTC,UAAAA,KAAK,EAAE,CADE;AACA;AACTC,UAAAA,IAAI,EAAE,CAFG;AAED;AACRC,UAAAA,KAAK,EAAE,CAHE;AAGA;AACTC,UAAAA,SAAS,EAAE,CAJF;AAII;AACbC,UAAAA,aAAa,EAAE,CALN;AAKQ;AACjBC,UAAAA,SAAS,EAAE,CANF;AAMI;AACbC,UAAAA,UAAU,EAAE,CAPH;AAOK;AACdC,UAAAA,KAAK,EAAE,CARE,CAQA;;AARA,SA7BK;;AAwClB;AACJ;AACA;AACIC,QAAAA,SAAS,EAAE;AACPC,UAAAA,MAAM,EAAE,CADD;AAEPC,UAAAA,OAAO,EAAE,CAFF;AAGPC,UAAAA,MAAM,EAAE,CAHD;AAIPC,UAAAA,QAAQ,EAAE,CAJH;AAKPC,UAAAA,YAAY,EAAE,CALP;AAMPC,UAAAA,YAAY,EAAE,CANP;AAOPC,UAAAA,KAAK,EAAE,CAPA;AAQPC,UAAAA,IAAI,EAAE,CARC;AASPC,UAAAA,SAAS,EAAE,CATJ;AAUPC,UAAAA,KAAK,EAAE,CAVA;AAWPC,UAAAA,WAAW,EAAE,EAXN;AAYPC,UAAAA,QAAQ,EAAE,EAZH;AAaPC,UAAAA,OAAO,EAAE,EAbF;AAcPC,UAAAA,YAAY,EAAE,EAdP;AAePC,UAAAA,QAAQ,EAAE,KAfH;AAgBPC,UAAAA,UAAU,EAAE,KAhBL;AAiBPC,UAAAA,SAAS,EAAE,KAjBJ;AAkBPC,UAAAA,OAAO,EAAE;AAlBF,SA3CO;;AA+DlB;AACJ;AACA;AACIC,QAAAA,iBAAiB,EAAE;AACfC,UAAAA,IAAI,EAAE,CADS;AAEfC,UAAAA,UAAU,EAAE,CAFG;AAGfC,UAAAA,IAAI,EAAE;AAHS,SAlED;;AAuElB;AACJ;AACA;AACIC,QAAAA,gBAAgB,EAAE;AACdC,UAAAA,GAAG,EAAE,CADS;AAEdzB,UAAAA,KAAK,EAAE,CAFO;AAGd0B,UAAAA,SAAS,EAAE,CAHG;AAIdC,UAAAA,QAAQ,EAAE;AAJI,SA1EA;;AAiFlB;AACJ;AACA;AACIC,QAAAA,UAAU,EAAE;AACR1B,UAAAA,MAAM,EAAE,CADA;AAER2B,UAAAA,MAAM,EAAE,CAFA;AAGRjC,UAAAA,SAAS,EAAE,CAHH;AAIRC,UAAAA,aAAa,EAAE,CAJP;AAKRC,UAAAA,SAAS,EAAE,CALH;AAMRC,UAAAA,UAAU,EAAE,CANJ;AAOR+B,UAAAA,MAAM,EAAE,CAPA;AAQRC,UAAAA,KAAK,EAAE;AARC,SApFM;;AA+FlB;AACJ;AACA;AACIC,QAAAA,SAAS,EAAE;AACPC,UAAAA,GAAG,EAAE,CADE;AAEPC,UAAAA,IAAI,EAAE,CAFC;AAGPC,UAAAA,KAAK,EAAE;AAHA,SAlGO;;AAwGlB;AACJ;AACA;AACIC,QAAAA,SAAS,EAAE;AACPC,UAAAA,MAAM,EAAE,CADD;AAEPC,UAAAA,MAAM,EAAE;AAFD;AA3GO,O;;yBAiHP7D,Q", "sourcesContent": ["\r\nexport let GameEnum = {\r\n\r\n    /**\r\n * 游戏类型\r\n */\r\n    GameType: {\r\n        Common: 0,\r\n        Expedition: 1,\r\n        Gold: 2,\r\n        Boss: 3,\r\n    },\r\n\r\n    /**\r\n     * 游戏状态\r\n     */\r\n    GameState: {\r\n        Idle: 0,\r\n        Ready: 1,\r\n        Sortie: 2,\r\n        Battle: 3,\r\n        Pause: 4,\r\n        WillOver: 5,\r\n        Over: 6,\r\n        BossIn: 7,\r\n    },\r\n\r\n    /**\r\n     * 敌人行为类型\r\n     */\r\n    EnemyAction: {\r\n        Sneak: 0,// 潜行行为\r\n        GoUp: 1,//上浮\r\n        Track: 2,// 跟踪行为\r\n        Transform: 3,// 变形行为\r\n        AttackPrepare: 4,// 准备攻击行为\r\n        AttackIng: 5,// 攻击中行为\r\n        AttackOver: 6,// 攻击结束行为\r\n        Leave: 7,//离开\r\n    },\r\n\r\n    /**\r\n     * 敌人类型\r\n     */\r\n    EnemyType: {\r\n        Normal: 0,\r\n        Missile: 1,\r\n        Turret: 2,\r\n        Ligature: 3,\r\n        LigatureLine: 4,\r\n        LigatureUnit: 5,\r\n        Build: 6,\r\n        Ship: 7,\r\n        ShipHeart: 8,\r\n        Train: 9,\r\n        ParkourItem: 10,\r\n        GoldShip: 11,\r\n        GoldBox: 12,\r\n        BossLigature: 13,\r\n        BossUnit: 10001,\r\n        BossNormal: 20001,\r\n        BossSnake: 21001,\r\n        BossUFO: 22001,\r\n    },\r\n    /**\r\n     * 敌人碰撞等级\r\n     */\r\n    EnemyCollideLevel: {\r\n        None: 0,\r\n        MainBullet: 1,\r\n        Main: 2,\r\n    },\r\n    /**\r\n     * 敌人销毁类型\r\n     */\r\n    EnemyDestroyType: {\r\n        Die: 0,\r\n        Leave: 1,\r\n        TrackOver: 2,\r\n        TimeOver: 3,\r\n    },\r\n\r\n    /**\r\n     * Boss 行为类型\r\n     */\r\n    BossAction: {\r\n        Normal: 0,\r\n        Appear: 1,\r\n        Transform: 2,\r\n        AttackPrepare: 3,\r\n        AttackIng: 4,\r\n        AttackOver: 5,\r\n        Switch: 6,\r\n        Blast: 7,\r\n    },\r\n\r\n    /**\r\n     * 敌人 Buff 类型\r\n     */\r\n    EnemyBuff: {\r\n        Ice: 1,\r\n        Fire: 2,\r\n        Treat: 100,\r\n    },\r\n\r\n    /**\r\n     * 敌人属性类型\r\n     */\r\n    EnemyAttr: {\r\n        Doctor: 1,\r\n        Shield: 2,\r\n    },\r\n}\r\n\r\nexport default GameEnum;"]}