System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, LevelDataEventTrigger, LevelDataEventTriggerType, LevelDataEventTriggerAudio, _crd;

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "./LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "./LevelDataEventTrigger", _context.meta, extras);
  }

  _export("LevelDataEventTriggerAudio", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      LevelDataEventTrigger = _unresolved_2.LevelDataEventTrigger;
      LevelDataEventTriggerType = _unresolved_2.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2c409YhHM1D75QzXo1WcN+4", "LevelDataEventTriggerAudio", undefined);

      _export("LevelDataEventTriggerAudio", LevelDataEventTriggerAudio = class LevelDataEventTriggerAudio extends (_crd && LevelDataEventTrigger === void 0 ? (_reportPossibleCrUseOfLevelDataEventTrigger({
        error: Error()
      }), LevelDataEventTrigger) : LevelDataEventTrigger) {
        constructor() {
          super((_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio);
          this.audioUUID = "";
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4fe15d7c15d60995c75cf21b0b41abb596380631.js.map