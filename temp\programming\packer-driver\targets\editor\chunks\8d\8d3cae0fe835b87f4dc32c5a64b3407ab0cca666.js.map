{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts"], "names": ["_decorator", "Vec2", "v2", "PolygonCollider2D", "FCollider", "ColliderType", "Intersection", "ccclass", "property", "menu", "FPolygonCollider", "type", "worldPoints", "worldEdge", "Polygon", "points", "_points", "value", "initCollider", "isConvex", "isConcavePolygon", "onLoad", "collider", "node", "getComponent", "map", "p", "x", "y", "offset", "init", "entity", "initBaseData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,iB,OAAAA,iB;;AAGxBC,MAAAA,S;AAAaC,MAAAA,Y,iBAAAA,Y;;AACXC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA8BT,U;;yBAOfU,gB,WAFpBH,OAAO,CAAC,kBAAD,C,UACPE,IAAI,CAAC,yBAAD,C,UAQAD,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACV,IAAD;AAAR,OAAD,C,UAERO,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACV,IAAD;AAAR,OAAD,C,0CAXb,MAEqBS,gBAFrB;AAAA;AAAA,kCAEwD;AAAA;AAAA;AAAA,eAC7CE,WAD6C,GACvB,CAACV,EAAE,CAAC,CAAC,GAAF,EAAO,CAAP,CAAH,EAAcA,EAAE,CAAC,CAAD,EAAI,EAAJ,CAAhB,EAAyBA,EAAE,CAAC,GAAD,EAAM,CAAN,CAA3B,CADuB;AAAA,eAE7CW,SAF6C,GAEzB,EAFyB;;AAAA;AAAA;;AAGrC,YAAJF,IAAI,GAAG;AACd,iBAAO;AAAA;AAAA,4CAAaG,OAApB;AACH;;AAKgB,YAANC,MAAM,GAAW;AACxB,iBAAO,KAAKC,OAAZ;AACH;;AACgB,YAAND,MAAM,CAACE,KAAD,EAAgB;AAC7B,eAAKD,OAAL,GAAeC,KAAf;AACH;;AAEMC,QAAAA,YAAY,GAAS;AACxB,gBAAMA,YAAN;AACA,eAAKC,QAAL,GAAgB,CAAC;AAAA;AAAA,4CAAaC,gBAAb,CAA8B,KAAKL,MAAnC,CAAjB;AACH;;AAEDM,QAAAA,MAAM,GAAS;AACX,cAAIC,QAAQ,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBrB,iBAAvB,CAAf;;AACA,cAAImB,QAAJ,EAAc;AACV,iBAAKP,MAAL,GAAcO,QAAQ,CAACP,MAAT,CAAgBU,GAAhB,CAAoBC,CAAC,IAAIxB,EAAE,CAACwB,CAAC,CAACC,CAAH,EAAMD,CAAC,CAACE,CAAR,CAA3B,CAAd;AACA,iBAAKC,MAAL,GAAc3B,EAAE,CAACoB,QAAQ,CAACO,MAAT,CAAgBF,CAAjB,EAAoBL,QAAQ,CAACO,MAAT,CAAgBD,CAApC,CAAhB;AACH;AACJ;;AAEDE,QAAAA,IAAI,CAACC,MAAD,EAASF,MAAY,GAAG3B,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA1B,EAAkC;AAClC,eAAK8B,YAAL,CAAkBD,MAAlB,EAA0BF,MAA1B;AACH;;AAhCmD,O;;;;;iBAQ1B,CAAC3B,EAAE,CAAC,CAAC,EAAF,EAAM,CAAC,EAAP,CAAH,EAAeA,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAjB,EAA4BA,EAAE,CAAC,EAAD,EAAK,EAAL,CAA9B,EAAwCA,EAAE,CAAC,CAAC,EAAF,EAAM,EAAN,CAA1C,C", "sourcesContent": ["import { _decorator, Vec2, v2, PolygonCollider2D } from 'cc';\nconst { ccclass, property, menu } = _decorator;\n\nimport FCollider, { ColliderType } from \"./FCollider\";\nimport { Intersection } from \"./Intersection\";\n\n@ccclass('FPolygonCollider')\n@menu(\"碰撞组件Ex/FPolygonCollider\")\nexport default class FPolygonCollider extends FCollider {\n    public worldPoints: Vec2[] = [v2(-100, 0), v2(0, 50), v2(100, 0)];\n    public worldEdge: Vec2[] = [];\n    public get type() {\n        return ColliderType.Polygon;\n    }\n\n    @property({ type: [Vec2] })\n    private _points: Vec2[] = [v2(-50, -50), v2(50, -50), v2(50, 50), v2(-50, 50)];\n    @property({ type: [Vec2] })\n    public get points(): Vec2[] {\n        return this._points;\n    }\n    public set points(value: Vec2[]) {\n        this._points = value;\n    }\n\n    public initCollider(): void {\n        super.initCollider();\n        this.isConvex = !Intersection.isConcavePolygon(this.points);\n    }\n\n    onLoad(): void {\n        let collider = this.node.getComponent(PolygonCollider2D);\n        if (collider) {\n            this.points = collider.points.map(p => v2(p.x, p.y));\n            this.offset = v2(collider.offset.x, collider.offset.y);\n        }\n    }\n\n    init(entity, offset: Vec2 = v2(0, 0)) {\n        this.initBaseData(entity, offset);\n    }\n}\n"]}