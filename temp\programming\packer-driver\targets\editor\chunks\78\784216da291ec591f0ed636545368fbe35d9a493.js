System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BulletGroupData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _crd, ccclass, property, BulletData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletGroupData(extras) {
    _reporterNs.report("BulletGroupData", "./EventGroupData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BulletGroupData = _unresolved_2.BulletGroupData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "be0b4sF0ulLq5+nHWXeK6jD", "BulletData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 子弹数据
       * 所有时间相关的，单位都是秒(s)
       */

      _export("BulletData", BulletData = (_dec = ccclass("BulletData"), _dec2 = property({
        displayName: '是否朝向行进方向'
      }), _dec3 = property({
        displayName: '是否追踪目标'
      }), _dec4 = property({
        displayName: '是否可被破坏'
      }), _dec5 = property({
        displayName: '命中时是否被销毁'
      }), _dec6 = property({
        displayName: '子弹持续时间'
      }), _dec7 = property({
        displayName: '子弹伤害'
      }), _dec8 = property({
        displayName: '子弹速度'
      }), _dec9 = property({
        displayName: '子弹加速度'
      }), _dec10 = property({
        displayName: '加速度方向'
      }), _dec11 = property({
        displayName: '延迟销毁时间'
      }), _dec12 = property({
        type: [_crd && BulletGroupData === void 0 ? (_reportPossibleCrUseOfBulletGroupData({
          error: Error()
        }), BulletGroupData) : BulletGroupData],
        displayName: '事件组'
      }), _dec(_class = (_class2 = class BulletData {
        constructor() {
          _initializerDefineProperty(this, "isFacingMoveDir", _descriptor, this);

          // 是否朝向行进方向
          _initializerDefineProperty(this, "isTrackingTarget", _descriptor2, this);

          // 是否追踪目标
          _initializerDefineProperty(this, "isDestructive", _descriptor3, this);

          // 是否可被破坏
          _initializerDefineProperty(this, "isDestructiveOnHit", _descriptor4, this);

          // 命中时是否被销毁
          // @property({displayName: '子弹Prefab'})
          // prefab : string;                          // 子弹Prefab: 考虑包含拖尾特效、颜色、缩放等
          _initializerDefineProperty(this, "duration", _descriptor5, this);

          // 子弹持续时间(超出后销毁回收)
          _initializerDefineProperty(this, "damage", _descriptor6, this);

          // 子弹伤害
          _initializerDefineProperty(this, "speed", _descriptor7, this);

          // 子弹速度
          _initializerDefineProperty(this, "acceleration", _descriptor8, this);

          // 子弹加速度
          _initializerDefineProperty(this, "accelerationAngle", _descriptor9, this);

          // 加速度方向(角度)
          _initializerDefineProperty(this, "delayDestroy", _descriptor10, this);

          // 延迟销毁时间
          _initializerDefineProperty(this, "eventGroupData", _descriptor11, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "isFacingMoveDir", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isTrackingTarget", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "isDestructive", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "isDestructiveOnHit", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "duration", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 10;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "damage", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "acceleration", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "accelerationAngle", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "delayDestroy", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "eventGroupData", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=784216da291ec591f0ed636545368fbe35d9a493.js.map