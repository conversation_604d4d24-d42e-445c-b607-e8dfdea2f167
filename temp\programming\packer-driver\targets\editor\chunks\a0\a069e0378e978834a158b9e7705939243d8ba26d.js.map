{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Data/gm/GM.ts"], "names": ["GM", "res", "ResGM", "MyApp", "_gmMap", "Map", "init", "tbGM", "lubanTables", "TbGM", "getDataList", "for<PERSON>ach", "e", "gmInfo", "get", "tabID", "push", "cfg", "set", "localClientGM", "tabIDList", "Array", "from", "keys", "getCmdBtnListByTabID", "filter", "v", "undefined", "registerClientGMHandler", "cmd", "onSendClick", "gmList", "info", "find", "desc", "name", "tabName", "GMTabID", "BATTLE", "args", "update"], "mappings": ";;;iDASaA,E;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,G,iBAAAA,G;AAAKC,MAAAA,K,iBAAAA,K;;AACLC,MAAAA,K,iBAAAA,K;;;;;;;oBAQIH,E,GAAN,MAAMA,EAAN,CAA0B;AAAA;AAC7B;AAD6B,eAErBI,MAFqB,GAEgB,IAAIC,GAAJ,EAFhB;AAAA;;AAGtBC,QAAAA,IAAI,GAAS;AAChB,gBAAMC,IAAI,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,IAA/B;AACAF,UAAAA,IAAI,CAACG,WAAL,GAAmBC,OAAnB,CAA2BC,CAAC,IAAI;AAC5B,kBAAMC,MAAM,GAAG,KAAKT,MAAL,CAAYU,GAAZ,CAAgBF,CAAC,CAACG,KAAlB,CAAf;;AACA,gBAAIF,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACG,IAAP,CAAY;AAAEC,gBAAAA,GAAG,EAAEL;AAAP,eAAZ;AACH,aAFD,MAEO;AACH,mBAAKR,MAAL,CAAYc,GAAZ,CAAgBN,CAAC,CAACG,KAAlB,EAAyB,CAAC;AAAEE,gBAAAA,GAAG,EAAEL;AAAP,eAAD,CAAzB;AACH;AACJ,WAPD;AAQA,eAAKO,aAAL;AACH;;AAEY,YAATC,SAAS,GAAG;AACZ,iBAAOC,KAAK,CAACC,IAAN,CAAW,KAAKlB,MAAL,CAAYmB,IAAZ,EAAX,CAAP;AACH;;AAEDC,QAAAA,oBAAoB,CAACT,KAAD,EAA+B;AAAA;;AAC/C,iBAAO,0BAAKX,MAAL,CAAYU,GAAZ,CAAgBC,KAAhB,uCAAwBU,MAAxB,CAA+BC,CAAC,IAAIA,CAAC,CAACT,GAAF,KAAUU,SAA9C,MAA4D,EAAnE;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,uBAAuB,CAACb,KAAD,EAAqBc,GAArB,EAAkCC,WAAlC,EAAyE;AACnG,cAAIC,MAAM,GAAG,KAAK3B,MAAL,CAAYU,GAAZ,CAAgBC,KAAhB,CAAb;;AACA,cAAI,CAACgB,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,CAAC;AAAEd,cAAAA,GAAG,EAAE;AAAA;AAAA,kCAAU;AAAEF,gBAAAA,KAAK,EAAEA,KAAT;AAAgBc,gBAAAA,GAAG,EAAEA;AAArB,eAAV,CAAP;AAA8CC,cAAAA,WAAW,EAAEA;AAA3D,aAAD,CAAT;;AACA,iBAAK1B,MAAL,CAAYc,GAAZ,CAAgBH,KAAhB,EAAuBgB,MAAvB;AACH,WAHD,MAGO;AACH,kBAAMC,IAAI,GAAGD,MAAM,CAACE,IAAP,CAAYD,IAAI,IAAIA,IAAI,CAACf,GAAL,CAASY,GAAT,KAAiBA,GAArC,CAAb;;AACA,gBAAIG,IAAJ,EAAU;AACNA,cAAAA,IAAI,CAACF,WAAL,GAAmBA,WAAnB;AACH,aAFD,MAEO;AACHC,cAAAA,MAAM,CAACf,IAAP,CAAY;AACRC,gBAAAA,GAAG,EAAE;AAAA;AAAA,oCAAU;AACXF,kBAAAA,KAAK,EAAEA,KADI;AAEXc,kBAAAA,GAAG,EAAEA,GAFM;AAGXK,kBAAAA,IAAI,EAAE,EAHK;AAIXC,kBAAAA,IAAI,EAAE,EAJK;AAKXC,kBAAAA,OAAO,EAAE;AALE,iBAAV,CADG;AAOJN,gBAAAA,WAAW,EAAEA;AAPT,eAAZ;AASH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYX,QAAAA,aAAa,GAAG;AACpB;AACA,eAAKS,uBAAL,CAA6B;AAAA;AAAA,0BAAIS,OAAJ,CAAYC,MAAzC,EAAiD,qBAAjD,EAAyEC,IAAD,IAAU;AAC9E,mBAAO,wBAAP;AACH,WAFD,EAFoB,CAMpB;;AACA,eAAKX,uBAAL,CAA6B;AAAA;AAAA,0BAAIS,OAAJ,CAAYC,MAAzC,EAAiD,sBAAjD,EAA0EC,IAAD,IAAU;AAC/E,mBAAO,yBAAP;AACH,WAFD;AAGH;;AAEMC,QAAAA,MAAM,GAAS,CACrB;;AArE4B,O", "sourcesContent": ["import { res, ResGM } from '../../AutoGen/Luban/schema';\nimport { MyApp } from \"../../MyApp\";\nimport { IData } from \"../DataManager\";\n\nexport interface GMInfo {\n    cfg?: ResGM;\n    onSendClick?(args: string): string;\n}\n\nexport class GM implements IData {\n    /**key:gm界面页签名 value:gm结构信息 */\n    private _gmMap: Map<res.GMTabID, GMInfo[]> = new Map()\n    public init(): void {\n        const tbGM = MyApp.lubanTables.TbGM\n        tbGM.getDataList().forEach(e => {\n            const gmInfo = this._gmMap.get(e.tabID)\n            if (gmInfo) {\n                gmInfo.push({ cfg: e })\n            } else {\n                this._gmMap.set(e.tabID, [{ cfg: e }])\n            }\n        });\n        this.localClientGM()\n    }\n\n    get tabIDList() {\n        return Array.from(this._gmMap.keys());\n    }\n\n    getCmdBtnListByTabID(tabID: res.GMTabID): GMInfo[] {\n        return this._gmMap.get(tabID)?.filter(v => v.cfg !== undefined) || []\n    }\n\n    /**\n     * 客户端注册gm按钮点击事件(GM.xlxs表里配置了才会有button显示)\n     * @param tabID gm界面页签名\n     * @param cmd gm命令\n     * @param onSendClick 点击事件(返回值会显示在console)\n     */\n    public registerClientGMHandler(tabID: res.GMTabID, cmd: string, onSendClick: (args: string) => string) {\n        let gmList = this._gmMap.get(tabID)\n        if (!gmList) {\n            gmList = [{ cfg: new ResGM({ tabID: tabID, cmd: cmd }), onSendClick: onSendClick }]\n            this._gmMap.set(tabID, gmList)\n        } else {\n            const info = gmList.find(info => info.cfg.cmd === cmd)\n            if (info) {\n                info.onSendClick = onSendClick\n            } else {\n                gmList.push({\n                    cfg: new ResGM({\n                        tabID: tabID,\n                        cmd: cmd,\n                        desc: \"\",\n                        name: \"\",\n                        tabName: \"\",\n                    }), onSendClick: onSendClick\n                })\n            }\n        }\n    }\n\n    /**\n     * 本地客户端gm指令例子(这个是例子，可以在其他代码模块调用,不要在这里调后面会做bundle，不然可能会有引用问题)\n     */\n    private localClientGM() {\n        //配置表配置过数据，显示button,如何配置了没有和表里命令对上，就会发给服务器\n        this.registerClientGMHandler(res.GMTabID.BATTLE, \"//local_client_test\", (args) => {\n            return \"local_client_test---完成\"\n        })\n\n        //没有配置表数据，不显示button\n        this.registerClientGMHandler(res.GMTabID.BATTLE, \"//local_client_test1\", (args) => {\n            return \"local_client_test1---完成\"\n        })\n    }\n\n    public update(): void {\n    }\n}"]}