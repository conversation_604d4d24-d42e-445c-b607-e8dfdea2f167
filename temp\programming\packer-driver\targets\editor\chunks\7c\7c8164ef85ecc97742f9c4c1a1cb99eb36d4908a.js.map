{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/utils.ts"], "names": ["LevelEditorUtils", "Node", "getOrAddNode", "node_parent", "name", "node", "getChildByName", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;wEAGaA,gB;;;;;;;;;AAHJC,MAAAA,I,OAAAA,I;;;;;;;;;kCAGID,gB,GAAN,MAAMA,gBAAN,CAAuB;AACA,eAAZE,YAAY,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AAC9D,cAAIC,IAAI,GAAGF,WAAW,CAACG,cAAZ,CAA2BF,IAA3B,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIJ,IAAJ,CAASG,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACI,QAAZ,CAAqBF,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AARyB,O", "sourcesContent": ["import { Node } from \"cc\";\r\n\r\n\r\nexport class LevelEditorUtils {\r\n    public static getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n}"]}