System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, error, SingletonBase, GameConst, EnemyWave, GameIns, Tools, BossBase, MyApp, WaveManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyWave(extras) {
    _reporterNs.report("EnemyWave", "../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossBase(extras) {
    _reporterNs.report("BossBase", "../ui/plane/boss/BossBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageData(extras) {
    _reporterNs.report("StageData", "../data/StageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../wave/Wave", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      error = _cc.error;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      EnemyWave = _unresolved_4.EnemyWave;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      Tools = _unresolved_6.Tools;
    }, function (_unresolved_7) {
      BossBase = _unresolved_7.default;
    }, function (_unresolved_8) {
      MyApp = _unresolved_8.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a93d1u5aJFFpIGyblYeA0H7", "WaveManager", undefined);

      __checkObsolete__(['error', 'JsonAsset', 'resources', 'Vec2']);

      _export("default", WaveManager = class WaveManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get enemyCreateAble() {
          return this._bEnemyCreateAble;
        }

        set enemyCreateAble(value) {
          this._bEnemyCreateAble = value;
        }

        constructor() {
          super();
          this._waveNorDatasMap = new Map();
          this._enemyOver = false;
          this._enemyActions = null;
          this._bEnemyCreateAble = false;
          this._bEnemyNorCreateAble = false;
          this._waveActionArr = [];
          this._waveArr = [];
          this._waveNumArr = [];
          this._waveTimeArr = [];
          this._enemyCreateTime = 0;
          this._enemyActionIndex = 0;
          this._waveIndex = 0;
          this._waveCreateTime = 0;
          this._waveIndexOver = [];
          this._curEnemyAction = null;
          this._bossCreateDelay = 0;
          this._bossCreateTime = 0;
          this._bossToAddArr = [];
          this._bShowBossWarning = false;
          this.initConfig();
        }

        initConfig() {
          let waveDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbWave.getDataList();

          for (let waveData of waveDatas) {
            const wave = new (_crd && EnemyWave === void 0 ? (_reportPossibleCrUseOfEnemyWave({
              error: Error()
            }), EnemyWave) : EnemyWave)();
            wave.loadJson(waveData);
            const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];
            group.push(wave);

            this._waveNorDatasMap.set(wave.enemyGroupID, group);
          }
        }

        mainReset() {
          this.reset();
        }

        reset() {
          this._enemyOver = false;
          this._enemyActions = [];
          this._enemyActionIndex = 0;
          this._enemyCreateTime = 0;
          this._bEnemyCreateAble = false;
          this._bEnemyNorCreateAble = false;
          this._waveIndex = 0;
          this._waveCreateTime = 0;

          this._waveIndexOver.splice(0);

          this._curEnemyAction = null;
          this._waveArr = [];
          this._waveActionArr = [];
          this._waveNumArr = [];
          this._waveTimeArr = [];
          this._bShowBossWarning = false;
          this._bossCreateTime = 0;
        }

        setEnemyActions(actions) {
          this._enemyActions = actions;
        }

        gameStart() {
          this._bEnemyCreateAble = true;
          this._bEnemyNorCreateAble = true;
          this._waveArr = [];
          this._waveActionArr = [];
          this._waveNumArr = [];
          this._waveTimeArr = [];
        }

        getNorWaveDatas(groupID) {
          return this._waveNorDatasMap.get(groupID);
        }
        /**
        * 更新游戏逻辑
        * @param deltaTime 每帧的时间增量
        */


        updateGameLogic(deltaTime) {
          this._updateCurAction(deltaTime);

          this._updateEnemy(deltaTime);

          this._updateBoss(deltaTime);
        }
        /**
         * 更新当前敌人行为
         * @param deltaTime 每帧的时间增量
         */


        _updateCurAction(deltaTime) {
          if (!this._enemyOver) {
            var _this$_enemyActions;

            if (this._enemyActionIndex >= (((_this$_enemyActions = this._enemyActions) == null ? void 0 : _this$_enemyActions.length) || 0)) {
              this._enemyOver = true;
              error("enemy over");
            } else if (this.enemyCreateAble && !this._curEnemyAction) {
              const action = this._enemyActions[this._enemyActionIndex];

              switch (action.type) {
                case 0:
                  this._enemyCreateTime += deltaTime;

                  if (this._enemyCreateTime >= action.enemyNorInterval || this._waveArr.length === 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).enemyManager.getNormalPlaneCount() === 0) {
                    this._curEnemyAction = action;
                  }

                  break;

                case 1:
                  if (this._waveArr.length === 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).enemyManager.getNormalPlaneCount() === 0) {
                    this._enemyCreateTime += deltaTime;

                    if (this._enemyCreateTime >= action.enemyNorInterval) {
                      this._curEnemyAction = action;
                    }
                  }

                  break;

                default:
                  if (action.type >= 100) {
                    console.warn("Boss stage", action.type, action.enemyNorIDs[0]);
                    this._bossCreateDelay = action.enemyNorInterval;
                    (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).bossManager.loadBossRes(action.type, action.enemyNorIDs[0]);

                    this._bossToAddArr.push(action);

                    this._enemyActionIndex++;
                  }

              }
            }
          }
        }
        /**
         * 更新敌人逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateEnemy(deltaTime) {
          // if (this.isShipStage && GameIns.enemyManager.isEnemyShipDead()) {
          //     this._waveArr.splice(0);
          //     this._enemyOver = true;
          // }
          this._updateEnemyCreate(deltaTime);

          if (this._curEnemyAction) {
            if (!this._updateNorEnemys(deltaTime)) {
              this._curEnemyAction = null;
              this._enemyActionIndex++;
              this._enemyCreateTime = 0;
            }
          }
        }

        addWaveByLevel(wave, pos) {
          const enemyWave = (_crd && EnemyWave === void 0 ? (_reportPossibleCrUseOfEnemyWave({
            error: Error()
          }), EnemyWave) : EnemyWave).fromLevelWave(wave, pos);

          this._waveArr.push(enemyWave);

          this._waveNumArr.push(0);

          this._waveTimeArr.push(0);

          this._waveActionArr.push(this._curEnemyAction);
        }
        /**
         * 更新敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        async _updateEnemyCreate(deltaTime) {
          for (let i = 0; i < this._waveArr.length; i++) {
            const wave = this._waveArr[i];
            this._waveTimeArr[i] += deltaTime;
            const currentEnemyCount = this._waveNumArr[i];
            let posX = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).EnemyPos.x;
            let posY = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).EnemyPos.y;

            if (wave.bSetStartPos) {
              posX += wave.startPosX;
              posY += wave.startPosY;
            }

            const expPerEnemy = Math.floor(wave.exp / wave.enemyNum);

            for (let j = currentEnemyCount; j < wave.enemyNum; j++) {
              if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {
                this._waveNumArr[i]++;
                let enemy;
                const enemyPosX = posX + wave.posDX * (j + 1);
                const enemyPosY = posY + wave.posDY * (j + 1);

                switch (wave.type) {
                  case 0:
                    enemy = await (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).enemyManager.addPlane(wave.enemyID);

                    if (enemy) {
                      if (j < wave.firstShootDelay.length) {
                        enemy.setFirstShootDelay(wave.firstShootDelay[j]);
                      }

                      enemy.setStandByTime(0);
                      enemy.setExp(expPerEnemy);
                      enemy.initPropertyRate(this._waveActionArr[i].enemyNorRate);
                      enemy.initTrack(wave.trackGroups, wave.liveParam, enemyPosX, enemyPosY, wave.rotateSpeed); // if (
                      //     wave.normalLoot &&
                      //     Tools.arrContain(wave.normalLoot.enemys, j + 1)
                      // ) {
                      //     enemy.addLoot(
                      //         GameIns.lootManager.getLootData(wave.normalLoot.lootId)
                      //     );
                      // }
                    }

                    break;
                }
              }
            }

            if (wave.enemyNum <= this._waveNumArr[i]) {
              this._waveArr.splice(i, 1);

              this._waveNumArr.splice(i, 1);

              this._waveTimeArr.splice(i, 1);

              this._waveActionArr.splice(i, 1);

              i--;
            }
          }
        }
        /**
         * 更新普通敌人生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateNorEnemys(deltaTime) {
          if (this._bEnemyNorCreateAble) {
            if (this._waveIndex >= this._curEnemyAction.enemyNorIDs.length) {
              this._waveIndex = 0;
              return false;
            }

            const waveID = this._curEnemyAction.enemyNorIDs[this._waveIndex];
            this._waveCreateTime += deltaTime;
            const waveDatas = this.getNorWaveDatas(waveID);

            for (let i = 0; i < waveDatas.length; i++) {
              const wave = waveDatas[i];

              if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).arrContain(this._waveIndexOver, i) && this._waveCreateTime >= wave.groupInterval) {
                this._waveArr.push(wave);

                this._waveNumArr.push(0);

                this._waveTimeArr.push(0);

                this._waveActionArr.push(this._curEnemyAction);

                this._waveIndexOver.push(i);
              }
            }

            if (this._waveIndexOver.length >= waveDatas.length) {
              this._waveIndexOver.splice(0);

              this._waveCreateTime = 0;
              this._waveIndex++;
            }
          }

          return true;
        }
        /**
         * 更新 Boss 生成逻辑
         * @param deltaTime 每帧的时间增量
         */


        _updateBoss(deltaTime) {
          if (this._bossToAddArr.length > 0 && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.isEnemyOver() && (this._bShowBossWarning || (this._bShowBossWarning = true, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.bossWillEnter()))) {
            this._bossCreateTime += deltaTime;

            if (this._bossCreateTime > this._bossCreateDelay && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.bossResFinish) {
              const bossData = this._bossToAddArr[0];
              const boss = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.addBoss(bossData.type, bossData.enemyNorIDs[0]);

              if (boss instanceof (_crd && BossBase === void 0 ? (_reportPossibleCrUseOfBossBase({
                error: Error()
              }), BossBase) : BossBase)) {
                // if (GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {
                //     boss.setPropertyRate(BossBattleManager.getPropertyRate());
                // } else {
                boss.setPropertyRate(bossData.enemyNorRate); // }

                boss.setTip((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).stageManager.getBossTips());
              }

              this._bossToAddArr.splice(0, 1);
            }
          }
        }
        /**
         * 检查敌人是否全部结束
         * @returns 是否所有敌人都已结束
         */


        isEnemyOver() {
          return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;
        } //     /**
        //      * 设置当前波次的 ID（未实现具体逻辑）
        //      * @param waveID 波次 ID
        //      */
        //     setWaveID(waveID: number): void {
        //         // 该方法目前未实现具体逻辑
        //     }


      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js.map