{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts"], "names": ["_decorator", "Enum", "CCFloat", "LevelDataEventCondtionComb", "LevelDataEventCondtionType", "LevelDataEventCondtionDelayTime", "newCondition", "LevelElemUI", "ccclass", "property", "LevelCondition", "type", "visible", "_index", "DelayTime", "DelayDistance", "Wave", "data", "And", "_targetElem", "comb", "value", "_type", "delayTime", "time", "delayDistance", "distance", "targetElem", "targetElemID", "elemID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;;AACMC,MAAAA,0B,iBAAAA,0B;AAA4BC,MAAAA,0B,iBAAAA,0B;;AACpDC,MAAAA,+B,iBAAAA,+B;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;gCAGjBU,c,WADZF,OAAO,CAAC,gBAAD,C,UAKHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAACV,IAAI;AAAA;AAAA,qEADH;;AAENW,QAAAA,OAAO,GAAG;AACN,iBAAO,KAAKC,MAAL,IAAe,CAAtB;AACH;;AAJK,OAAD,C,UAaRJ,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAACV,IAAI;AAAA;AAAA;AADH,OAAD,C,UAYRQ,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAET,OADA;;AAENU,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BG,SAA/C;AACH;;AAJK,OAAD,C,UAaRL,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAET,OADA;;AAENU,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BI,aAA/C;AACH;;AAJK,OAAD,C,UAcRN,QAAQ,CAAC;AACNE,QAAAA,IAAI;AAAA;AAAA,sCADE;;AAENC,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BK,IAA/C;AACH;;AAJK,OAAD,C,2BAzDb,MACaN,cADb,CAC4B;AAAA;AAAA,eACjBG,MADiB,GACR,CADQ;AAAA,eAEjBI,IAFiB,GAEe;AAAA;AAAA,kFAAoC;AAAA;AAAA,wEAA2BC,GAA/D,CAFf;AAAA,eAuDjBC,WAvDiB,GAuDiB,IAvDjB;AAAA;;AAUT,YAAJC,IAAI,GAA+B;AAC1C,iBAAO,KAAKH,IAAL,CAAUG,IAAjB;AACH;;AACc,YAAJA,IAAI,CAACC,KAAD,EAAoC;AAC/C,eAAKJ,IAAL,CAAUG,IAAV,GAAiBC,KAAjB;AACH;;AAKc,YAAJV,IAAI,GAA+B;AAC1C,iBAAO,KAAKM,IAAL,CAAUK,KAAjB;AACH;;AACc,YAAJX,IAAI,CAACU,KAAD,EAAoC;AAC/C,cAAI,KAAKJ,IAAL,CAAUK,KAAV,IAAmBD,KAAvB,EAA8B;AAC1B,iBAAKJ,IAAL,GAAY;AAAA;AAAA,8CAAa;AAACG,cAAAA,IAAI,EAAE,KAAKH,IAAL,CAAUG,IAAjB;AAAuBE,cAAAA,KAAK,EAAED;AAA9B,aAAb,CAAZ;AACH;AACJ;;AAQmB,YAATE,SAAS,GAAW;AAC3B,iBAAQ,KAAKN,IAAN,CAA+CO,IAAtD;AACH;;AACmB,YAATD,SAAS,CAACF,KAAD,EAAgB;AAC/B,eAAKJ,IAAN,CAA+CO,IAA/C,GAAsDH,KAAtD;AACH;;AAQuB,YAAbI,aAAa,GAAW;AAC/B,iBAAQ,KAAKR,IAAN,CAAmDS,QAA1D;AACH;;AACuB,YAAbD,aAAa,CAACJ,KAAD,EAAgB;AACnC,eAAKJ,IAAN,CAAmDS,QAAnD,GAA8DL,KAA9D;AACH;;AASoB,YAAVM,UAAU,GAAuB;AACvC,iBAAO,KAAKR,WAAZ;AACJ;;AACoB,YAAVQ,UAAU,CAACN,KAAD,EAA4B;AAAA;;AAC7C,eAAKF,WAAL,GAAmBE,KAAnB;AACC,eAAKJ,IAAN,CAA0CW,YAA1C,oBAAyDP,KAAzD,oBAAyDA,KAAK,CAAEQ,MAAhE,4BAA0E,EAA1E;AACH;;AApEuB,O", "sourcesContent": ["import { _decorator, Enum, CCFloat } from 'cc';\r\nimport { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from '../../../leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionDelayTime } from '../../../leveldata/condition/LevelDataEventCondtionDelayTime';\r\nimport { newCondition } from '../../../leveldata/condition/newCondition';\r\nimport { LevelDataEventCondtionDelayDistance } from '../../../leveldata/condition/LevelDataEventCondtionDelayDistance';\r\nimport { LevelElemUI } from './LevelElemUI';\r\nimport { LevelDataEventCondtionWave } from '../../../leveldata/condition/LevelDataEventCondtionWave';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelCondition')\r\nexport class LevelCondition {\r\n    public _index = 0;\r\n    public data : LevelDataEventCondtion = new LevelDataEventCondtionDelayTime(LevelDataEventCondtionComb.And);\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventCondtionComb),\r\n        visible() {\r\n            return this._index != 0;\r\n        }\r\n    })\r\n    public get comb(): LevelDataEventCondtionComb {\r\n        return this.data.comb;\r\n    }\r\n    public set comb(value: LevelDataEventCondtionComb) {\r\n        this.data.comb = value;\r\n    }\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventCondtionType),\r\n    })\r\n    public get type(): LevelDataEventCondtionType {\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventCondtionType) {\r\n        if (this.data._type != value) {\r\n            this.data = newCondition({comb: this.data.comb, _type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCFloat,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.DelayTime ;\r\n        }\r\n    })\r\n    public get delayTime(): number {\r\n        return (this.data as LevelDataEventCondtionDelayTime).time;\r\n    }\r\n    public set delayTime(value: number) {\r\n        (this.data as LevelDataEventCondtionDelayTime).time = value;\r\n    }\r\n\r\n    @property({\r\n        type :CCFloat,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.DelayDistance;\r\n        }\r\n    })\r\n    public get delayDistance(): number {\r\n        return (this.data as LevelDataEventCondtionDelayDistance).distance;\r\n    }\r\n    public set delayDistance(value: number) {\r\n        (this.data as LevelDataEventCondtionDelayDistance).distance = value;\r\n    }\r\n\r\n    public _targetElem: LevelElemUI | null = null;\r\n    @property({\r\n        type: LevelElemUI,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.Wave;\r\n        }\r\n    })\r\n    public get targetElem(): LevelElemUI | null {\r\n         return this._targetElem;\r\n    }\r\n    public set targetElem(value: LevelElemUI | null) { \r\n        this._targetElem = value;\r\n        (this.data as LevelDataEventCondtionWave).targetElemID = value?.elemID ?? \"\";\r\n    }\r\n}"]}