System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, logInfo, csproto, WXLogin, _crd;

  function _reportPossibleCrUseOfLoginInfo(extras) {
    _reporterNs.report("LoginInfo", "../Network/NetMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIPlatformSDK(extras) {
    _reporterNs.report("IPlatformSDK", "./IPlatformSDK", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatformSDKUserInfo(extras) {
    _reporterNs.report("PlatformSDKUserInfo", "./IPlatformSDK", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  _export("WXLogin", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      logInfo = _unresolved_2.logInfo;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1ad1eh4Z79NkZfXtpLl5Wrr", "WXLogin", undefined);

      _export("WXLogin", WXLogin = class WXLogin {
        constructor() {
          this.authButton = null;
        }

        login(cb) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Login", "start wx login"); // @ts-ignore

          wx.login({
            complete(res) {
              (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                error: Error()
              }), logInfo) : logInfo)("Login", `complete wx login ${res.errMsg} ${res.code}`);
              cb(res.code ? null : res.errMsg, {
                accountType: (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                  error: Error()
                }), csproto) : csproto).cs.ACCOUNT_TYPE.ACCOUNT_TYPE_WXMINIGAME,
                code: res.code,
                serverAddr: "wss://m2test.5600.online:9101/"
              });
            }

          });
        }

        showUserInfoButton() {
          if (this.authButton) {
            this.authButton.show();
          }
        }

        hideUserInfoButton() {
          if (this.authButton) {
            this.authButton.hide();
          }
        }

        getUserInfo(cb, param) {
          let THIS = this; // @ts-ignore

          wx.getSetting({
            success(res) {
              console.error('WXLogin get userinfo auth setting', res.authSetting);

              if (!res.authSetting['scope.userInfo']) {
                console.error('WXLogin start authorize userinfo'); // @ts-ignore

                let sysInfo = wx.getSystemInfoSync(); // @ts-ignore

                var button = wx.createUserInfoButton({
                  type: 'text',
                  text: '',
                  //image : "SDK/WXGetUserInfo.png",
                  plain: true,
                  style: {
                    left: sysInfo.screenWidth * param.x,
                    //0,//buttonPosition.x,
                    top: sysInfo.screenHeight * (1 - param.y) + (sysInfo.safeArea.top - 20),
                    //1334-100,//buttonPosition.y+buttonPosition.height,
                    width: sysInfo.screenWidth * param.w,
                    //750,//buttonPosition.width,
                    height: sysInfo.screenHeight * param.h,
                    //100,//buttonPosition.height,
                    lineHeight: 40,
                    backgroundColor: '#ff0000',
                    color: '#ffffff',
                    textAlign: 'center',
                    fontSize: 16,
                    borderRadius: 4
                  }
                });
                button.show();
                button.onTap(res => {
                  if (res.userInfo) {
                    button.destroy();
                    THIS.authButton = null;
                    console.log('WXLogin get wx userinfo success', res.userInfo);
                    THIS.getUserInfo__(res.userInfo, cb, true);
                  }
                });
                THIS.authButton = button;
              } else {
                THIS.getUserInfo_(cb, false);
              }
            }

          });
        }

        getUserInfo__(userInfo, cb, hasTap) {
          cb(null, {
            name: userInfo.nickName,
            icon: userInfo.avatarUrl
          }, true);
        }

        getUserInfo_(cb, hasTap) {
          let THIS = this; // @ts-ignore

          wx.getUserInfo({
            complete(res) {
              console.error("WXLogin getUserInfo complete");

              if (res.userInfo) {
                THIS.getUserInfo__(res.userInfo, cb, hasTap);
              } else {
                cb("get userinfo error", null, false);
              }
            }

          });
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=60e675aaf4308064cc8b1ba390aad4aa1df38ac0.js.map