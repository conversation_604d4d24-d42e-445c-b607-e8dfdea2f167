{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts"], "names": ["_decorator", "Component", "NodeEventType", "Vec3", "GameIns", "GameEnum", "ccclass", "property", "Controller", "target", "_targetStartPos", "onLoad", "instance", "start", "node", "on", "TOUCH_START", "onTouchStart", "TOUCH_END", "onTouchEnd", "TOUCH_MOVE", "onTouchMove", "event", "mainPlaneManager", "mainPlane", "getPosition", "gameRuleManager", "gameState", "GameState", "Battle", "startPos", "getUIStartLocation", "location", "getUILocation", "posX", "x", "posY", "y", "onControl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,a,OAAAA,a;AAAiCC,MAAAA,I,OAAAA,I;;AACnEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;4BAGjBQ,U,WADZF,OAAO,CAAC,YAAD,C,2BAAR,MACaE,UADb,SACgCP,SADhC,CAC0C;AAAA;AAAA;AAAA,eAEtCQ,MAFsC,GAElB,IAFkB;AAGd;AAHc,eAItCC,eAJsC,GAId,IAAIP,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAJc;AAAA;;AAIE;;AAExC;AACJ;AACA;AACIQ,QAAAA,MAAM,GAAG;AACLH,UAAAA,UAAU,CAACI,QAAX,GAAsB,IAAtB,CADK,CACuB;AAC/B;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACc,WAA3B,EAAwC,KAAKC,YAA7C,EAA2D,IAA3D;AACA,eAAKH,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACgB,SAA3B,EAAsC,KAAKC,UAA3C,EAAuD,IAAvD;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAab,aAAa,CAACkB,UAA3B,EAAuC,KAAKC,WAA5C,EAAyD,IAAzD;AACH;AAED;AACJ;AACA;AACA;;;AACIJ,QAAAA,YAAY,CAACK,KAAD,EAAQ;AAChB,cAAIb,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CADgB,CACiC;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AACD,eAAKC,eAAL,GAAuBD,MAAM,CAACK,IAAP,CAAYW,WAAZ,EAAvB,CALgB,CAKkC;AACrD;AAED;AACJ;AACA;AACA;;;AACIJ,QAAAA,WAAW,CAACC,KAAD,EAAQ;AACf,cAAI;AAAA;AAAA,kCAAQI,eAAR,CAAwBC,SAAxB,GAAoC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAA3D,EAAmE;AAC/D,mBAD+D,CACvD;AACX;;AAED,cAAIpB,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CALe,CAKkC;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AAED,cAAIqB,QAAQ,GAAGR,KAAK,CAACS,kBAAN,EAAf;AACA,cAAIC,QAAQ,GAAGV,KAAK,CAACW,aAAN,EAAf,CAXe,CAWyB;;AAExC,cAAIC,IAAI,GAAGF,QAAQ,CAACG,CAAT,GAAaL,QAAQ,CAACK,CAAtB,GAA0B,KAAKzB,eAAL,CAAqByB,CAA1D;AACA,cAAIC,IAAI,GAAGJ,QAAQ,CAACK,CAAT,GAAaP,QAAQ,CAACO,CAAtB,GAA0B,KAAK3B,eAAL,CAAqB2B,CAA1D;AACA5B,UAAAA,MAAM,CAAC6B,SAAP,CAAiBJ,IAAjB,EAAuBE,IAAvB,EAfe,CAee;AACjC;AAED;AACJ;AACA;AACA;;;AACIjB,QAAAA,UAAU,CAACG,KAAD,EAAQ;AACd;AACA,cAAI,KAAKb,MAAT,EAAiB,CACb;AACH;AACJ;;AAjEqC,O,UAG/BG,Q,GAAW,I", "sourcesContent": ["import { _decorator, Component, Animation, NodeEventType, EventTouch, Vec2, Vec3 } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport GameEnum from '../../const/GameEnum';\r\nimport { MainPlane } from '../plane/mainPlane/MainPlane';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Controller')\r\nexport class Controller extends Component {\r\n\r\n    target: MainPlane = null; // 目标对象（主飞机）\r\n    static instance = null; // 单例实例\r\n    _targetStartPos: Vec3 = new Vec3(0, 0); // 目标起始位置\r\n\r\n    /**\r\n     * 加载时初始化\r\n     */\r\n    onLoad() {\r\n        Controller.instance = this; // 单例模式\r\n    }\r\n\r\n    /**\r\n     * 开始时绑定触摸事件\r\n     */\r\n    start() {\r\n        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);\r\n        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);\r\n    }\r\n\r\n    /**\r\n     * 触摸开始事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchStart(event) {\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n        this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置\r\n    }\r\n\r\n    /**\r\n     * 触摸移动事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchMove(event) {\r\n        if (GameIns.gameRuleManager.gameState < GameEnum.GameState.Battle) {\r\n            return; // 游戏未进入战斗状态时不处理\r\n        }\r\n\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n\r\n        let startPos = event.getUIStartLocation();\r\n        let location = event.getUILocation();   //得到手指鼠标位置,得到的是世界坐标\r\n\r\n        let posX = location.x - startPos.x + this._targetStartPos.x;\r\n        let posY = location.y - startPos.y + this._targetStartPos.y;\r\n        target.onControl(posX, posY); // 控制主飞机移动\r\n    }\r\n\r\n    /**\r\n     * 触摸结束事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchEnd(event) {\r\n        // 当前实现中，触摸结束事件未处理任何逻辑\r\n        if (this.target) {\r\n            // 可扩展逻辑\r\n        }\r\n    }\r\n}"]}