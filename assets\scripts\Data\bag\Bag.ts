import Long from 'long';
import csproto from '../../AutoGen/PB/cs_proto.js';
import { EventMgr } from '../../event/EventManager';
import { MyApp } from '../../MyApp';
import { logError } from '../../Utils/Logger';
import { DataEvent } from '../DataEvent';
import { IData } from "../DataManager";
export class Bag implements IData {
    // 物品槽位
    items: csproto.cs.ICSItem[] = [];

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, this.onGetItemListMsg.bind(this))
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_UPDATE_ITEM, this.onUpdateItemMsg.bind(this))
        this.refreshItems();
    }

    // 刷新物品 
    refreshItems() {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, { get_item_list: {} })
        EventMgr.emit(DataEvent.ItemsRefresh)
    }

    //物品推送
    onUpdateItemMsg(msg: csproto.cs.IS2CMsg) {
        const updateItems = msg.body.update_item.items
        switch (msg.body.update_item.reason) {
            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_ADD:
                updateItems.forEach(element => {
                    if (this.getItemByGuid(element.guid)) {
                        logError("PlaneUI", `item guid ${element.guid} already exist`)
                        return
                    }
                    this.items.push(element)
                });
                break;
            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_REMOVE:
                updateItems.forEach(element => {
                    const idx = this.items.findIndex(v => {
                        return v.guid.eq(element.guid)
                    })
                    if (idx < 0) {
                        logError("PlaneUI", `item guid ${element.guid} not exist`)
                        return
                    }
                    this.items.splice(idx, 1)
                });
                break;
            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_CHANGE:
                this.items = this.items.map(v => {
                    const item = updateItems.find(item => item.guid.eq(v.guid))
                    return item ? item : v
                })
                break;
            default:
                logError("PlaneUI", `unknown item update reason ${msg.body.update_item.reason}`)
                break;
        }
        EventMgr.emit(DataEvent.ItemsRefresh)
    }

    getItemByGuid(guid: Long): csproto.cs.ICSItem | null {
        return this.items.find(v => v.guid.eq(guid)) || null
    }

    public onGetItemListMsg(msg: csproto.cs.IS2CMsg): void {
        this.items = msg.body.get_item_list.items || []
    }

    public update(): void {
    }
}
