import { _decorator, SpriteAtlas, tween } from 'cc';
import ImageSequence from './ImageSequence';
import { GameIns } from '../../GameIns';
import { MyApp } from '../../../MyApp';

const { ccclass } = _decorator;

@ccclass('UIAnimMethods')
export default class UIAnimMethods {
    static frameToTime = 1 / 30; // 帧数转换为时间

    /**
     * 根据帧数计算时间
     * @param {number} from 起始帧
     * @param {number} to 结束帧
     * @returns {number} 时间（秒）
     */
    static fromTo(from, to) {
        return to < from ? 0 : (to - from) * this.frameToTime;
    }

    /**
     * 双按钮动画
     * @param {Node} buttonNode 按钮节点
     * @param {string} atlasName 图集名称
     * @returns {Promise<void>}
     */
    async doubleBTNAnim(buttonNode, atlasName) {
        const atlas = await MyApp.resMgr.loadAsync(atlasName,SpriteAtlas);
        if (atlas) {
            let effectLight = null;
            const frames = [];
            for (let i = 0; i < 8; i++) {
                const frameName = `effectRun_${i}`;
                const frame = atlas.getSpriteFrame(frameName);
                if (frame) {
                    frames.push(frame);
                }
            }

            const effectNode = buttonNode.getChildByName('effect_light');
            if (effectNode) {
                effectLight = effectNode.getComponent(ImageSequence);
                if (effectLight) {
                    effectLight.setData(frames, 15);
                }
            }

            tween(buttonNode)
                .to(UIAnimMethods.fromTo(1, 4), { scale: 1.15 })
                .to(UIAnimMethods.fromTo(4, 7), { scale: 0.9 })
                .call(async () => {
                    if (effectLight) {
                        effectLight.node.active = true;
                        await effectLight.play(1);
                        effectLight.node.active = false;
                    }
                })
                .to(UIAnimMethods.fromTo(7, 11), { scale: 1.05 })
                .to(UIAnimMethods.fromTo(11, 15), { scale: 1 })
                .delay(0.8)
                .union()
                .repeatForever()
                .start();
        }
    }
}