System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec6, _dec7, _dec8, _dec9, _dec10, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _crd, ccclass, property, eEmitterCondition, eBulletCondition, eEventConditionOp, EmitterConditionData, BulletConditionData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "eb3d9HdfN5BOZtq9mMk/S5m", "EventConditionData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eEmitterCondition", eEmitterCondition = /*#__PURE__*/function (eEmitterCondition) {
        eEmitterCondition[eEmitterCondition["Level_Duration"] = 1] = "Level_Duration";
        eEmitterCondition[eEmitterCondition["Level_Distance"] = 2] = "Level_Distance";
        eEmitterCondition[eEmitterCondition["Level_InfLevel"] = 3] = "Level_InfLevel";
        eEmitterCondition[eEmitterCondition["Level_ChallengeLevel"] = 4] = "Level_ChallengeLevel";
        eEmitterCondition[eEmitterCondition["Player_ActLevel"] = 5] = "Player_ActLevel";
        eEmitterCondition[eEmitterCondition["Player_PosX"] = 6] = "Player_PosX";
        eEmitterCondition[eEmitterCondition["Player_PosY"] = 7] = "Player_PosY";
        eEmitterCondition[eEmitterCondition["Player_LifePercent"] = 8] = "Player_LifePercent";
        eEmitterCondition[eEmitterCondition["Player_GainBuff"] = 9] = "Player_GainBuff";
        eEmitterCondition[eEmitterCondition["Unit_Life"] = 10] = "Unit_Life";
        eEmitterCondition[eEmitterCondition["Unit_LifePercent"] = 11] = "Unit_LifePercent";
        eEmitterCondition[eEmitterCondition["Unit_Duration"] = 12] = "Unit_Duration";
        eEmitterCondition[eEmitterCondition["Unit_PosX"] = 13] = "Unit_PosX";
        eEmitterCondition[eEmitterCondition["Unit_PosY"] = 14] = "Unit_PosY";
        eEmitterCondition[eEmitterCondition["Unit_Speed"] = 15] = "Unit_Speed";
        eEmitterCondition[eEmitterCondition["Unit_SpeedAngle"] = 16] = "Unit_SpeedAngle";
        eEmitterCondition[eEmitterCondition["Unit_Acceleration"] = 17] = "Unit_Acceleration";
        eEmitterCondition[eEmitterCondition["Unit_AccelerationAngle"] = 18] = "Unit_AccelerationAngle";
        eEmitterCondition[eEmitterCondition["Unit_DistanceToPlayer"] = 19] = "Unit_DistanceToPlayer";
        eEmitterCondition[eEmitterCondition["Unit_AngleToPlayer"] = 20] = "Unit_AngleToPlayer";
        eEmitterCondition[eEmitterCondition["Emitter_Active"] = 21] = "Emitter_Active";
        eEmitterCondition[eEmitterCondition["Emitter_InitialDelay"] = 22] = "Emitter_InitialDelay";
        eEmitterCondition[eEmitterCondition["Emitter_Prewarm"] = 23] = "Emitter_Prewarm";
        eEmitterCondition[eEmitterCondition["Emitter_PrewarmDuration"] = 24] = "Emitter_PrewarmDuration";
        eEmitterCondition[eEmitterCondition["Emitter_Duration"] = 25] = "Emitter_Duration";
        eEmitterCondition[eEmitterCondition["Emitter_ElapsedTime"] = 26] = "Emitter_ElapsedTime";
        eEmitterCondition[eEmitterCondition["Emitter_Loop"] = 27] = "Emitter_Loop";
        eEmitterCondition[eEmitterCondition["Emitter_LoopInterval"] = 28] = "Emitter_LoopInterval";
        eEmitterCondition[eEmitterCondition["Emitter_EmitInterval"] = 29] = "Emitter_EmitInterval";
        eEmitterCondition[eEmitterCondition["Emitter_EmitCount"] = 30] = "Emitter_EmitCount";
        eEmitterCondition[eEmitterCondition["Emitter_EmitOffsetX"] = 31] = "Emitter_EmitOffsetX";
        eEmitterCondition[eEmitterCondition["Emitter_Angle"] = 32] = "Emitter_Angle";
        eEmitterCondition[eEmitterCondition["Emitter_Count"] = 33] = "Emitter_Count";
        eEmitterCondition[eEmitterCondition["Bullet_Sprite"] = 34] = "Bullet_Sprite";
        eEmitterCondition[eEmitterCondition["Bullet_Scale"] = 35] = "Bullet_Scale";
        eEmitterCondition[eEmitterCondition["Bullet_ColorR"] = 36] = "Bullet_ColorR";
        eEmitterCondition[eEmitterCondition["Bullet_ColorG"] = 37] = "Bullet_ColorG";
        eEmitterCondition[eEmitterCondition["Bullet_ColorB"] = 38] = "Bullet_ColorB";
        eEmitterCondition[eEmitterCondition["Bullet_Duration"] = 39] = "Bullet_Duration";
        eEmitterCondition[eEmitterCondition["Bullet_ElapsedTime"] = 40] = "Bullet_ElapsedTime";
        eEmitterCondition[eEmitterCondition["Bullet_Speed"] = 41] = "Bullet_Speed";
        eEmitterCondition[eEmitterCondition["Bullet_Acceleration"] = 42] = "Bullet_Acceleration";
        eEmitterCondition[eEmitterCondition["Bullet_AccelerationAngle"] = 43] = "Bullet_AccelerationAngle";
        eEmitterCondition[eEmitterCondition["Bullet_FacingMoveDir"] = 44] = "Bullet_FacingMoveDir";
        eEmitterCondition[eEmitterCondition["Bullet_Destructive"] = 45] = "Bullet_Destructive";
        eEmitterCondition[eEmitterCondition["Bullet_DestructiveOnHit"] = 46] = "Bullet_DestructiveOnHit";
        return eEmitterCondition;
      }({}));

      _export("eBulletCondition", eBulletCondition = /*#__PURE__*/function (eBulletCondition) {
        eBulletCondition[eBulletCondition["Bullet_Duration"] = 100] = "Bullet_Duration";
        eBulletCondition[eBulletCondition["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
        eBulletCondition[eBulletCondition["Bullet_PosX"] = 102] = "Bullet_PosX";
        eBulletCondition[eBulletCondition["Bullet_PosY"] = 103] = "Bullet_PosY";
        eBulletCondition[eBulletCondition["Bullet_Damage"] = 104] = "Bullet_Damage";
        eBulletCondition[eBulletCondition["Bullet_Speed"] = 105] = "Bullet_Speed";
        eBulletCondition[eBulletCondition["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
        eBulletCondition[eBulletCondition["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
        eBulletCondition[eBulletCondition["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
        eBulletCondition[eBulletCondition["Bullet_Scale"] = 109] = "Bullet_Scale";
        eBulletCondition[eBulletCondition["Bullet_ColorR"] = 110] = "Bullet_ColorR";
        eBulletCondition[eBulletCondition["Bullet_ColorG"] = 111] = "Bullet_ColorG";
        eBulletCondition[eBulletCondition["Bullet_ColorB"] = 112] = "Bullet_ColorB";
        eBulletCondition[eBulletCondition["Bullet_FacingMoveDir"] = 113] = "Bullet_FacingMoveDir";
        eBulletCondition[eBulletCondition["Bullet_Destructive"] = 114] = "Bullet_Destructive";
        eBulletCondition[eBulletCondition["Bullet_DestructiveOnHit"] = 115] = "Bullet_DestructiveOnHit";
        return eBulletCondition;
      }({}));

      _export("eEventConditionOp", eEventConditionOp = /*#__PURE__*/function (eEventConditionOp) {
        eEventConditionOp[eEventConditionOp["Equal"] = 0] = "Equal";
        eEventConditionOp[eEventConditionOp["NotEqual"] = 1] = "NotEqual";
        eEventConditionOp[eEventConditionOp["Greater"] = 2] = "Greater";
        eEventConditionOp[eEventConditionOp["Less"] = 3] = "Less";
        eEventConditionOp[eEventConditionOp["GreaterEqual"] = 4] = "GreaterEqual";
        eEventConditionOp[eEventConditionOp["LessEqual"] = 5] = "LessEqual";
        return eEventConditionOp;
      }({}));
      /**
       * 发射器事件数据
       * 所有时间相关的，单位都是秒(s)
       */


      _export("EmitterConditionData", EmitterConditionData = (_dec = ccclass("EmitterConditionData"), _dec2 = property({
        type: Enum(eEmitterCondition),
        displayName: '条件类型'
      }), _dec3 = property({
        type: Enum(eEventConditionOp),
        displayName: '条件操作'
      }), _dec4 = property({
        displayName: '条件值'
      }), _dec5 = property({
        displayName: '条件值(string)'
      }), _dec(_class = (_class2 = class EmitterConditionData {
        constructor() {
          _initializerDefineProperty(this, "eventType", _descriptor, this);

          _initializerDefineProperty(this, "eventOp", _descriptor2, this);

          _initializerDefineProperty(this, "targetValue", _descriptor3, this);

          // 条件值: 例如持续时间、距离
          _initializerDefineProperty(this, "targetValueStr", _descriptor4, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "eventType", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eEmitterCondition.Level_Duration;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "eventOp", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eEventConditionOp.Equal;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "targetValue", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "targetValueStr", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      })), _class2)) || _class));
      /**
       * 子弹事件数据
       * 所有时间相关的，单位都是秒(s)
       */


      _export("BulletConditionData", BulletConditionData = (_dec6 = ccclass("BulletConditionData"), _dec7 = property({
        type: Enum(eBulletCondition),
        displayName: '条件类型'
      }), _dec8 = property({
        type: Enum(eEventConditionOp),
        displayName: '条件操作'
      }), _dec9 = property({
        displayName: '条件值'
      }), _dec10 = property({
        displayName: '条件值(string)'
      }), _dec6(_class4 = (_class5 = class BulletConditionData {
        constructor() {
          _initializerDefineProperty(this, "eventType", _descriptor5, this);

          _initializerDefineProperty(this, "eventOp", _descriptor6, this);

          _initializerDefineProperty(this, "targetValue", _descriptor7, this);

          // 条件值: 例如持续时间、距离
          _initializerDefineProperty(this, "targetValueStr", _descriptor8, this);
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "eventType", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eBulletCondition.Bullet_ElapsedTime;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "eventOp", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eEventConditionOp.Equal;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "targetValue", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "targetValueStr", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b135639c8a059fbc03ad82c9d6cd35f7f7087a71.js.map