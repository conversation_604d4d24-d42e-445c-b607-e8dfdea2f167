import { _decorator, Component, JsonAsset, Node, resources, view } from 'cc';
import { GameIns } from '../../GameIns';
import ExchangeMap from '../base/ExchangeMap';
import { GameConst } from '../../const/GameConst';
import GameEnum from '../../const/GameEnum';
import { builtin, Chapter } from '../../../AutoGen/Luban/schema';
import { MyApp } from '../../../MyApp';
import { LevelBaseUI } from './LevelBaseUI';
import { LevelData } from '../../../leveldata/leveldata';

const { ccclass, property } = _decorator;

enum RAND_STRATEGY {
    WEIGHT_PRUE = 1, // 纯权重随机
    WEIGHT_NO_REPEAT = 2, //权重随机，不重复
    ORDER = 3 // 按顺序
}

enum PRELOAD_STATE {
    NONE = 0,
    LOADING = 1,
    LOADED = 2
}

const PRELOAD_LEVEL_COUNT = 1; 

interface LevelBaseUIInfo {
    levelID: number;
    totalTime: number;
    speed: number;
}

/*
 * @description 加载策略：
 * 根据当前移动到的关卡，提前加载下一关的关卡（目前只提前加载的关卡数定为1）
*/
@ccclass('GameMapRun')
export default class GameMapRun extends Component {

    static instance: GameMapRun = null;

    private _levelList: number[] = [];
    private _chapterData: Chapter = null;
    private _lastSelectedId: number = -1;

    private _initOver = false;
    private _preloadState = PRELOAD_STATE.NONE;

    private _levelLoadIndex = 0; // 当前关卡加载索引
    private _levelIndex = 0; // 当前关卡索引（实时移动到的）
    private _levelUIInfoList: Array<LevelBaseUIInfo> = []; //已经加载的关卡的基本信息

    private _levelTotalDuration: number = 0; // 关卡总持续时间
    private _levelTotalHeight: number = 0; // 关卡总高度
    private _levelDistance: number = 0; // 当前关卡移动的距离
    private _levelDuration: number = 0; // 当前关卡的持续时间
    private _levelSpeed: number = 0; // 当前关卡的移动速度

    public get MapSpeed(): number {
        return this._levelSpeed;
    }

    public get ViewTop(): number {
        return view.getVisibleSize().height * -0.5;
    }

    onLoad() {
        GameMapRun.instance = this;
    }

    // 根据策略随机出关卡列表
    private _initLevelList(chapterID: number) {
        this._chapterData = MyApp.lubanTables.TbChapter.get(70001);/*(chapterID)*/;
        if (this._chapterData == null) {
            console.log('GameMapRun'," chapterData is null");
            return;
        }

        // 随机出关卡组
        const levelGroupList = this._randomSelection(this._chapterData.strategyList,this._chapterData.levelGroupCount,this._chapterData.strategy);
        if (levelGroupList.length === 0) {
            console.log('GameMapRun'," levelGroupList is null");
            return;
        }

        // 随机出关卡
        this._levelList = [];
        for (const levelGroupID of levelGroupList) {
            const levelGroupData = MyApp.lubanTables.TbLevelGroup.get(levelGroupID);
            if (levelGroupData == null) {
                console.log('GameMapRun'," levelGroupData is null");
                continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));
            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
        }
        console.log('GameMapRun',' _levelList ', this._levelList);
    }
    
    async initData(chapterID: number): Promise<void>{
        this.reset();
        this._initLevelList(chapterID);
        await this._loadNextLevelPrefab(true);
        this._initCurLevelData();
        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        if (levelBaseUI) {
            await new Promise<void>((resolve) => {
                // 确保 LevelBaseUI 的初始化完成（例如背景加载）
                const check = () => {
                    if (levelBaseUI.backgroundLayer?.backgrounds?.length > 0) {
                        resolve();
                    } else {
                        setTimeout(check, 100); // 轮询检查
                    }
                };
                check();
            });
        }
        this._initOver = true;
        this._preloadState = PRELOAD_STATE.LOADED;
    }


    initBattle(){
        
    }

    update(deltaTime: number){
        if (!GameConst.GameAble) {
            return;
        }

        if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667;
        }

        const gameState = GameIns.gameRuleManager.gameState;
        if (
            gameState !== GameEnum.GameState.Battle &&
            gameState !== GameEnum.GameState.Sortie &&
            gameState !== GameEnum.GameState.Ready &&
            gameState !== GameEnum.GameState.WillOver &&
            gameState !== GameEnum.GameState.Idle &&
            gameState !== GameEnum.GameState.Over
        ) {
            return;
        }

        if (!this._initOver) {
            return;
        }

        this._tick(deltaTime);
        this._checkNextLevelLoading();
    }

    clear() {
        // 清理加载的资源
        
    }

    mapEndChange() {
        // 处理地图结束切换
        ExchangeMap.me.endChange();
    }

    reset() {
        // 重置地图数据
        this.node.removeAllChildren();
        this._initOver = false;
        this._levelLoadIndex = -1;
        this._levelIndex = 0;
        this._levelDistance = 0;
        this._levelTotalDuration = 0;
        this._levelTotalHeight = 0;
        this._levelDuration = 0;
        this._levelList = [];   
        this._levelUIInfoList = [];
        this._chapterData = null;
        this._lastSelectedId = -1;

    }

    /**
     * 策略：
     * 1.严格按权重比例随机选择元素
     * 2.严格按权重比例随机选择元素，不重复
     * 3.按顺序选择元素
     * @param STList 带权重的元素数组
     * @param count 需要选择的元素数量
     * @returns 选中元素的ID数组
     */
    private _randomSelection(STList: builtin.randStrategy[], count: number, strategy: RAND_STRATEGY): number[] {
        if (STList.length === 0 || count <= 0) return [];

        const results: number[] = [];
        if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);
            
            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    const randomIndex = Math.floor(Math.random() * STList.length);
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }
            
            // 严格按权重比例随机选择
            for (let i = 0; i < count; i++) {
                // 生成[0, totalWeight)区间的随机数
                const randomValue = Math.random() * totalWeight;
                
                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                for (const item of STList) {
                    cumulativeWeight += item.Weight;
                    if (randomValue < cumulativeWeight) {
                        results.push(item.ID);
                        break;
                    }   
                }
            }
        } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);
            
            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    let randomIndex = Math.floor(Math.random() * STList.length);
                    // 避免重复选择相同的ID
                    if (i > 0 && STList[randomIndex].ID === results[i-1]) {
                        // 如果与上一次选择的相同，选择下一个（循环）
                        randomIndex = (randomIndex + 1) % STList.length;
                    }
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }
            
            // 创建副本以避免修改原始数据
            const tempList = [...STList];
            
            for (let i = 0; i < count; i++) {
                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
                if (this._lastSelectedId !== -1) {
                    const lastSelectedIndex = tempList.findIndex(item => item.ID === this._lastSelectedId);
                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                        // 将上一次选择的ID与下一个元素交换位置
                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] = 
                        [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                    }
                }
                
                // 生成[0, totalWeight)区间的随机数
                const randomValue = Math.random() * totalWeight;
                
                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                let selectedIndex = -1;
                
                for (let j = 0; j < tempList.length; j++) {
                    cumulativeWeight += tempList[j].Weight;
                    if (randomValue < cumulativeWeight) {
                        selectedIndex = j;
                        break;
                    }
                }
                
                // 如果未找到有效索引，选择最后一个元素
                if (selectedIndex === -1) {
                    selectedIndex = tempList.length - 1;
                }
                
                // 获取选中的ID
                const selectedId = tempList[selectedIndex].ID;
                results.push(selectedId);
                
                // 更新上一次选择的ID
                this._lastSelectedId = selectedId;
            }
        } else if (strategy === RAND_STRATEGY.ORDER) {
        // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            let currentIndex = 0;
            
            for (let i = 0; i < count; i++) {
                // 如果当前元素的ID为0，则重置到数组开头
                if (STList[currentIndex].ID === 0) {
                    currentIndex = 0;
                    
                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素
                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                        currentIndex++;
                    }
                    
                    // 如果所有元素ID都为0，则无法选择，跳出循环
                    if (currentIndex >= STList.length) {
                        break;
                    }
                }
                
                // 选择当前元素
                results.push(STList[currentIndex].ID);
                
                // 移动到下一个元素
                currentIndex++;
                
                // 如果到达数组末尾，回到开头
                if (currentIndex >= STList.length) {
                    currentIndex = 0;
                }
            }
        }

        return results;
    }

    private async _loadNextLevelPrefab(bFristLevel: boolean = false): Promise<void> {
        if (this._levelIndex >= this._chapterData.levelCount || this._levelIndex >= this._levelList.length) {
            console.log('GameMapRun',' no level to load');
            return;
        }

        // 计算实际需要加载的关卡数量
        const remainingLevels = this._chapterData.levelCount - (this._levelLoadIndex + PRELOAD_LEVEL_COUNT);
        console.log('GameMapRun',' levelCount:', this._chapterData.levelCount,'this._levelLoadIndex:', this._levelLoadIndex,'remainingLevels:', remainingLevels);
        const levelsToLoad = Math.min(PRELOAD_LEVEL_COUNT, remainingLevels);

        if (levelsToLoad <= 0) {
            console.log('GameMapRun',' no level to load');
            return;
        }

        
        this._levelLoadIndex += levelsToLoad;
        const loadPromises: Promise<void>[] = [];
        for (let i = 1; i <= levelsToLoad; i++) {
            const levelID = this._levelList[this._levelLoadIndex + i];
            console.log('GameMapRun',' ----- this._levelLoadIndex:', this._levelLoadIndex);
            const levelConfig = MyApp.lubanTables.TbLevel.get(levelID);
            if (levelConfig == null) {
                console.log('GameMapRun',' level data not found', levelID);
                return;
            }

            const prefabName = `Game/level/${levelConfig.prefab}`;
            const loadPromise = new Promise<void>((resolve) => {
                resources.load(prefabName, JsonAsset, async (err, prefab) => {
                    if (err) {
                        console.error('GameMapRun','加载关卡预制体失败:', err);
                        this._levelLoadIndex -= levelsToLoad;
                        resolve(); 
                        return;
                    }
                    
                    var levelBaseUI = this.node.getComponent(LevelBaseUI);
                    //const nodeLayer = new Node(`chapter${this._chapterData.id}`);
                    if (levelBaseUI == null) {
                        levelBaseUI = this.node.addComponent(LevelBaseUI);
                    }

                    const levelInfo = {
                        levelID: levelID,
                        levelIndex: this._levelLoadIndex
                    };
                    
                    var levelData = LevelData.fromJSON(prefab?.json);
                    if (bFristLevel) {
                        await levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);
                    } else {
                        levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);
                    }
                    //this.node.addChild(nodeLayer);
                    var levelBaseUIInfo = this._initLevelInfo(levelID, levelData.totalTime, levelData.backgroundLayer.speed);
                    this._levelUIInfoList.push(levelBaseUIInfo);
                    console.log('GameMapRun','加载关卡:', levelID, prefabName);
                    
                    resolve();
                });
            });

            loadPromises.push(loadPromise);
        }

        if (bFristLevel) {
            await Promise.all(loadPromises); // 首次加载需要等待
            this._preloadState = PRELOAD_STATE.LOADED;
        } else {
            await Promise.all(loadPromises)
            .then(() => {
                this._preloadState = PRELOAD_STATE.LOADED;
                console.log('GameMapRun','关卡预加载完成');
            })
            .catch((err) => {
                console.error('后台预加载失败:', err);
            });
        }
    }

    private _checkNextLevelLoading() {
        if (this._preloadState === PRELOAD_STATE.LOADED && (this._levelIndex + PRELOAD_LEVEL_COUNT)  > this._levelLoadIndex) {
            console.log('GameMapRun',' 开始加载到关卡:', this._levelIndex + PRELOAD_LEVEL_COUNT,'PRELOAD_STATE:', this._preloadState, 'this._levelLoadIndex:', this._levelLoadIndex);
            this._preloadState = PRELOAD_STATE.LOADING;
            this._loadNextLevelPrefab().catch(err => {
                console.error('GameMapRun',' Background loading failed:', err);
            });
        }
    }

    private _initCurLevelData() {
        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        const levelBase = this._levelUIInfoList[this._levelIndex];
        this._levelDistance = 0;
        this._levelTotalHeight = levelBaseUI.getLevelTotalHeightByIndex(this._levelIndex);
        levelBaseUI.setBackgroundLayerInfo(levelBase.speed, levelBase.totalTime);
        this._levelSpeed = levelBase.speed;
        this._levelDuration = 0;
    }

    private _tick(deltaTime: number) {

        if (this._levelIndex >= this._chapterData.levelCount - 1) {
            console.error('GameMapRun',' no level to tick');
            return;
        }

        if (this._levelTotalHeight <= 0) {
            return;
        }

        this._levelDuration += deltaTime;
        this._levelTotalDuration += deltaTime;
        this._levelDistance += this.MapSpeed * deltaTime;

        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        if (levelBaseUI != null) {
            //console.log('GameMapRun',' tick level', levelData.levelID, this._levelDuration);
            levelBaseUI.tick(deltaTime);
            if (this._levelDistance >= this._levelTotalHeight) {
                this._levelIndex++;  
                console.log('GameMapRun','关卡完成:', this._levelIndex - 1,'levelDistance:', this._levelDistance, 'levelTotalHeight:', this._levelTotalHeight);
                
                this._initCurLevelData();
            }
        }
    }

    private _initLevelInfo(levelID: number, time: number, speed: number): LevelBaseUIInfo {
        return {
            levelID: levelID,
            totalTime: time,
            speed: speed,
        };
    }
}