System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Sprite, Animation, Label, tween, UIOpacity, sp, UITransform, Tween, view, v3, size, SpriteAtlas, Plane, GameConst, GameIns, GameEnum, BattleLayer, FireShells, Bullet, EnemyEntity, BossUnit, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ox<PERSON><PERSON><PERSON>, Collider<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, GameResourceList, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _class3, _crd, ccclass, property, AnimState, MainPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "../Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainData(extras) {
    _reporterNs.report("MainData", "../../../data/MainData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../../layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFireShells(extras) {
    _reporterNs.report("FireShells", "./FireShells", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEntity(extras) {
    _reporterNs.report("EnemyEntity", "../enemy/EnemyEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossUnit(extras) {
    _reporterNs.report("BossUnit", "../boss/BossUnit", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectLayer(extras) {
    _reporterNs.report("EffectLayer", "../../layer/EffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../../../const/GameResourceList", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      Animation = _cc.Animation;
      Label = _cc.Label;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      sp = _cc.sp;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
      view = _cc.view;
      v3 = _cc.v3;
      size = _cc.size;
      SpriteAtlas = _cc.SpriteAtlas;
    }, function (_unresolved_2) {
      Plane = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameEnum = _unresolved_5.default;
    }, function (_unresolved_6) {
      BattleLayer = _unresolved_6.default;
    }, function (_unresolved_7) {
      FireShells = _unresolved_7.default;
    }, function (_unresolved_8) {
      Bullet = _unresolved_8.default;
    }, function (_unresolved_9) {
      EnemyEntity = _unresolved_9.default;
    }, function (_unresolved_10) {
      BossUnit = _unresolved_10.default;
    }, function (_unresolved_11) {
      EffectLayer = _unresolved_11.default;
    }, function (_unresolved_12) {
      FBoxCollider = _unresolved_12.default;
    }, function (_unresolved_13) {
      ColliderGroupType = _unresolved_13.ColliderGroupType;
    }, function (_unresolved_14) {
      MyApp = _unresolved_14.MyApp;
    }, function (_unresolved_15) {
      GameResourceList = _unresolved_15.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81e41b6hKRNqYJbS6uK9LDf", "MainPlane", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Sprite', 'Animation', 'Label', 'Vec2', 'tween', 'UIOpacity', 'instantiate', 'sp', 'UITransform', 'Tween', 'Color', 'view', 'v3', 'v2', 'SpriteFrame', 'size', 'SpriteAtlas']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 动画状态枚举
       */

      AnimState = /*#__PURE__*/function (AnimState) {
        AnimState[AnimState["idle"] = 0] = "idle";
        AnimState[AnimState["crazy"] = 1] = "crazy";
        AnimState[AnimState["willCancelCrazy"] = 2] = "willCancelCrazy";
        return AnimState;
      }(AnimState || {});

      _export("MainPlane", MainPlane = (_dec = ccclass("MainPlane"), _dec2 = property(Sprite), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property(sp.Skeleton), _dec6 = property(Animation), _dec7 = property(Animation), _dec8 = property(Animation), _dec(_class = (_class2 = (_class3 = class MainPlane extends (_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
        error: Error()
      }), Plane) : Plane) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "streak", _descriptor, this);

          _initializerDefineProperty(this, "skin", _descriptor2, this);

          _initializerDefineProperty(this, "hpBar", _descriptor3, this);

          _initializerDefineProperty(this, "blast", _descriptor4, this);

          _initializerDefineProperty(this, "attspeAnim", _descriptor5, this);

          _initializerDefineProperty(this, "scrAnim", _descriptor6, this);

          _initializerDefineProperty(this, "auxAnim", _descriptor7, this);

          // 血条相关
          this.hpbarBack = null;
          // 血条背景
          this.hpbarMid = null;
          // 血条中间部分
          this.hpbarFont = null;
          // 血条前景
          this.hpfont = null;
          // 血量文字
          this.hpMidActin = null;
          // 血条动画
          this.m_fireAnim = [];
          // 射击动画数组
          // // 飞机状态
          this.m_screenDatas = [];
          // 屏幕数据
          this.m_moveEnable = true;
          // 是否允许移动
          this.m_config = void 0;
          // 飞机配置
          this.m_collideComp = null;
          // 碰撞组件
          this.mainData = void 0;
          this.m_fires = [];
          // 射击点数组
          // // 飞机状态
          this.m_fireState = null;
          // 射击状态
          this.m_animState = AnimState.idle;
          // 动画状态
          this._hurtActTime = 0;
          // 受伤动画时间
          this._hurtActDuration = 0.5;
        }

        // 受伤动画持续时间

        /**
         * 生命周期方法：onLoad
         * 初始化主飞机的配置和数据
         */
        onLoad() {
          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager._mainConfig; // 获取主飞机的配置记录

          this.mainData = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData; // 获取主飞机的数据

          this.hpbarBack = this.hpBar.getChildByName("hpbarback").getComponent(Sprite);
          this.hpbarMid = this.hpBar.getChildByName("hpbarmid").getComponent(Sprite);
          this.hpbarFont = this.hpBar.getChildByName("hpbarfont").getComponent(Sprite);
          this.hpfont = this.hpBar.getChildByName("hpfont").getComponent(Label);
          this.m_collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.m_collideComp.init(this, size(40, 40)); // 初始化碰撞组件

          this.m_collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).PLAYER;
        }
        /**
         * 生命周期方法：start
         * 初始化主飞机的组件和状态
         */


        start() {
          this.enemy = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = true;
          this.m_collideComp.isEnable = true; // 初始化动画状态

          this.m_animState = AnimState.idle; // 初始化所有组件

          this.m_comps.forEach(comp => {
            comp.init(this);
          }); // 初始化飞机

          this.initPlane(); // 禁用射击

          this.setFireEnable(false);
        }

        update(dt) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) return; // 限制帧率

          if (dt > 0.2) dt = 0.016666666666667; // 游戏状态为战斗时更新逻辑

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable) {
            // 更新所有组件
            this.m_comps.forEach(comp => {
              comp.update(dt);
            });
          } // 更新受伤动画时间


          this._hurtActTime += dt;
        }
        /**
         * 初始化主飞机
         */


        async initPlane() {
          // 加载飞机资源
          await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_mainPlane + this.m_config.type, SpriteAtlas); // 添加火焰动画

          await this.addFireAnim(); // 获取火力状态

          this.getFireState(); // 根据屏幕等级更改屏幕数据

          this.changeScreenLv(this.mainData.screenLv); // 初始化图片

          this.initPic();
        }
        /**
        * 初始化变形图片
        */


        async initPic() {
          const attspe = this.attspeAnim.node.getChildByName("attspe");
          const atlas = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_mainPlane + this.m_config.type, SpriteAtlas);
          attspe.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[1]}1`);
          const position = this.m_config.transExt[0].split(",");
          attspe.setPosition(Number(position[0]), Number(position[1]));
          this.scrAnim.node.children.forEach(async child => {
            const scr = child.getChildByName("scr");
            scr.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[2]}1`);
            const pos = this.m_config.transExt[1].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]));
          });
          this.auxAnim.node.children.forEach(async child => {
            const aux = child.getChildByName("aux");
            aux.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[3]}1`);
            const pos = this.m_config.transExt[2].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]));
          });
        }
        /**
        * 改变屏幕等级
        * @param {number} level 屏幕等级
        */


        changeScreenLv(level) {
          if (level === 0) return;
          this.m_screenDatas = [];
          const attackKey = "shiftingatk";
          const attackData = this.m_config[`${attackKey}${level}`];

          for (let i = 0; i < attackData.length; i += 8) {
            const screenData = attackData.slice(i, i + 8);
            this.m_screenDatas.push(screenData);
          }

          this.m_screenDatas.forEach((data, index) => {
            if (this.m_fires[index] == null) {
              this.createAttackPoint(data);
            } else {
              this.changeScreen(index, data);
            }
          });

          for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {
            this.changeScreen(i, null);
          }
        }
        /**
         * 添加发射火焰动画
         */


        async addFireAnim() {
          for (let i = 0; i < this.m_config.zjdmtxzb.length; i += 2) {
            const x = this.m_config.zjdmtxzb[i];
            const y = this.m_config.zjdmtxzb[i + 1];
            const fireNode = new Node("fireNode");
            fireNode.addComponent(UITransform);
            fireNode.addComponent(UIOpacity);
            fireNode.setPosition(Number(x), Number(y));
            fireNode.parent = this.skin;
            this.m_fireAnim.push(fireNode);
            fireNode.getComponent(UIOpacity).opacity = 0;
            const skeletonData = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).spine_mainfire, sp.SkeletonData);
            const skeleton = fireNode.addComponent(sp.Skeleton);
            skeleton.skeletonData = skeletonData;
            skeleton.setAnimation(0, "play", true);
          }
        }

        initBattle() {
          this.node.active = true; // 获取火力状态

          this.getFireState();
          this.m_collideComp.isEnable = false;
          this.UpdateHp();
        }
        /**
        * 主飞机入场动画
        */


        planeIn() {
          const self = this;
          const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime; // 播放入场音效
          // frameWork.audioManager.playEffect("planeFristIn");
          // 设置初始位置和状态

          this.node.getComponent(UIOpacity).opacity = 255;
          this.node.parent = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).me.selfPlaneLayer;
          this.skin.getComponent(UIOpacity).opacity = 255;
          this.skin.setScale(1, 1);
          let posY = -view.getVisibleSize().height - (this.m_config.type === 711 ? 1000 : 80);
          this.node.setPosition(0, posY);
          this.node.setScale((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.getRatio(), (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.getRatio());
          this.stopFire();
          this.m_moveEnable = false;
          Tween.stopAllByTarget(this.node); // 地图加速 TODO
          // 飞机入场动画

          this.scheduleOnce(() => {
            const targetY = -view.getVisibleSize().height * 0.7;
            const targetX = this.node.position.x;
            tween(this.node).to(20 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY - 17)
            }).to(11 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY + 57)
            }).to(10 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY + 76)
            }).to(27 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              position: v3(targetX, targetY)
            }).call(() => {
              self.begine();
            }).start();
            tween(this.skin).to(20 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1.9, 1.9)
            }).to(11 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1.4, 1.4)
            }).to(10 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1, 1)
            }).to(27 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed, {
              scale: v3(1, 1)
            }).start();

            if (this.hpBar) {
              tween(this.hpBar.getComponent(UIOpacity)).to(0, {
                opacity: 0
              }).delay(31 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed).to(10 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                opacity: 255
              }).start();
            }

            this.scheduleOnce(() => {
              tween(this.streak.node).to(9 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 4)
              }).to(7 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 2)
              }).to(5 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.animSpeed, {
                scale: v3(1, 1)
              }).call(() => {
                self.addStreak();
              }).start();
            }, 2 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.animSpeed);
          }, 7 * frameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed);
        }
        /**
         * 退出战斗
         */


        battleQuit() {
          this.mainData.die = false; // GameIns.gameDataManager.battlePlaneActive = true;
          // 重置飞机状态

          this.skin.getComponent(UIOpacity).opacity = 255; // this.suitEffect.active = false;
          // if (this.downSuitCall) this.downSuitCall();
          // GameIns.mainPlaneManager.hideSkillNode();
          // this.quitGameSetPic();
          // this.clearMechaOverPic(true);
          // 重置动画节点位置
          // this.skinAnim.node.y = this.initPosSkinAnim;
          // this.mechaAnimNode.y = this.initPosMechaAnim;
          // this.suitAnimBot.y = this.initPosSuitBot;
          // this.suitAnimTop.y = this.initPosSuitTop;
        }
        /**
         * 减少血量
         * @param {number} damage 受到的伤害值
         */


        cutHp(damage) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.hurtTotal += damage;
          const newHp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp - damage;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp = Math.max(0, newHp);

          if (newHp < 0) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.hurtTotal += newHp;
          }

          this.UpdateHp();
          ;

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {
            this.toDie();
          }
        }
        /**
         * 增加血量
         * @param {number} heal 恢复的血量值
         */


        addHp(heal) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp = Math.min((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.maxhp, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp + heal);
          this.UpdateHp();
          ;
        }
        /**
         * 碰撞处理
         * @param {Object} collision 碰撞对象
         */


        onCollide(collision) {
          // if (this.m_skill && this.m_skill.invincible) return;
          let damage = 0;

          if (collision.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            damage = collision.entity.getAttack(this);
          } else if (collision.entity instanceof (_crd && EnemyEntity === void 0 ? (_reportPossibleCrUseOfEnemyEntity({
            error: Error()
          }), EnemyEntity) : EnemyEntity) || collision.entity instanceof (_crd && BossUnit === void 0 ? (_reportPossibleCrUseOfBossUnit({
            error: Error()
          }), BossUnit) : BossUnit)) {
            damage = collision.entity.getColliderAtk();
          }

          if (damage > 0) {
            this.cutHp(damage);

            this._playHurtAnim();

            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {
              this.toDie();
            }
          }
        }

        _playHurtAnim() {
          if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0; // 显示红屏效果

            (_crd && EffectLayer === void 0 ? (_reportPossibleCrUseOfEffectLayer({
              error: Error()
            }), EffectLayer) : EffectLayer).me.showRedScreen();
          }
        }
        /**
         * 控制飞机移动
         * @param {number} moveX 水平方向的移动量
         * @param {number} moveY 垂直方向的移动量
         */


        onControl(posX, posY) {
          if (!this.mainData.die && this.m_moveEnable) {
            // 限制飞机移动范围
            posX = Math.min(360, posX);
            posX = Math.max(-360, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewHeight, posY);
            this.node.setPosition(posX, posY);
          }
        }
        /**
         * 更新血量显示
         */


        UpdateHp() {
          if (this.hpBar && this.hpbarFont && this.hpbarMid) {
            // 停止当前血条动画
            if (this.hpMidActin) {
              this.hpMidActin.stop();
              this.hpMidActin = null;
            } // 更新血条前景的填充范围


            this.hpbarFont.fillRange = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainData.hp / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainData.maxhp; // 计算血条动画时间

            const duration = Math.abs(this.hpbarMid.fillRange - this.hpbarFont.fillRange); // 血条中间部分的动画

            this.hpMidActin = tween(this.hpbarMid).to(duration, {
              fillRange: this.hpbarFont.fillRange
            }).call(() => {
              this.hpMidActin = null;
            }).start(); // 更新血量文字

            this.hpfont.string = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainData.hp.toFixed(0);
          }
        }
        /**
         * 处理复活逻辑
         */


        onRelife() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.reviveCount += 1; // 增加复活次数

          this.relife(1); // 调用复活方法
        }
        /**
         * 执行复活
         * @param {number} reviveType 复活类型
         */


        relife(reviveType) {
          // this.playRelifeAim(); // 播放复活动画
          this.mainData.die = false; // 设置飞机为非死亡状态

          this.mainData.revive = true; // 设置复活状态

          this.scheduleOnce(() => {
            this.mainData.revive = false;
          }, 0.5);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = true; // 激活主飞机

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.hp = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainData.maxhp; // 恢复满血

          this.UpdateHp();
          ; // 触发血量更新事件
        }
        /**
         * 获取火力状态
         */


        getFireState() {
          const atkChallenge = this.mainData.atkAddRatio;
          this.m_fireState = {
            attack: this.mainData.attack,
            attackLv: 0,
            hpAttackLv: 0,
            speedLv: 0,
            cirtLv: 0,
            //cirtLevel,
            catapultLv: 0,
            //SkillManager.me.getLv(SkillType.catapult),
            atkChallenge: atkChallenge,
            fireIntensify: this.mainData.intensifyAtk
          };
        }
        /**
         * 获取攻击力
         * @returns {number} 当前攻击力
         */


        getAttack() {
          return this.m_fireState.attack;
        }

        toDie() {
          // 设置玩家状态为死亡
          this.mainData.die = true; // 禁用碰撞组件

          this.m_collideComp.isEnable = false; // 停止血条动画并设置血条为 0

          if (this.hpbarMid) {
            tween(this.hpbarMid).stop();
            this.hpbarMid.fillRange = 0;
          } // 播放死亡动画


          this._playDieAnim(); // 设置战斗状态为不可用


          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.battlePlaneActive = false;
        }
        /**
         * 播放死亡动画
         */


        _playDieAnim() {
          this.blast.node.getComponent(UIOpacity).opacity = 255; // 显示爆炸效果

          this.blast.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调

          this.blast.setAnimation(0, "play", false); // 播放爆炸动画
        }
        /**
         * 主飞机死亡动画结束后的处理逻辑
         */


        _dieAnimEnd() {
          // 隐藏爆炸动画节点
          this.blast.node.getComponent(UIOpacity).opacity = 0; // // 如果主飞机还有剩余生命次数
          // if (this.mainData.lifeNum > 0) {
          //     // 减少生命次数
          //     this.mainData.lifeNum--;
          //     // 更新剩余生命次数到全局数据
          //     // GameIns.gameDataManager.setLifeNum(this.mainData.lifeNum);
          //     // 触发复活逻辑
          //     this.relife(0);
          // } else {
          // 如果没有剩余生命次数，检查是否可以复活
          // const reviveCount = GameIns.gameDataManager.reviveCount;
          // if (this.mainData.relifeNum - reviveCount <= 0) {
          // // 如果是远征模式，结束当前章节
          // if (ExpeditionManager.isExpedition) {
          //     ExpeditionManager.sectionOver(false);
          // } else {
          //     // 否则触发战斗失败逻辑

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.battleFail(); // }
          // } else {
          //     // 如果可以复活，触发战斗死亡逻辑
          //     GameIns.battleManager.battleDie();
          // }
          // }
        }
        /**
         * 启用或禁用火力
         * @param {boolean} enable 是否启用火力
         */


        setFireEnable(enable) {
          if (this.m_config && this.m_config.type === 710) return;

          if (enable) {
            this.m_fireAnim.forEach(anim => {
              anim.getComponent(UIOpacity).opacity = 255; // 显示火力动画
            });
          } else {
            this.m_fireAnim.forEach(anim => {
              anim.getComponent(UIOpacity).opacity = 0; // 隐藏火力动画
            });
          }
        }
        /**
         * 设置飞机是否可移动
         * @param {boolean} enable 是否可移动
         */


        setMoveAble(enable) {
          this.m_moveEnable = enable;
        }
        /**
         * 设置碰撞是否可用
         * @param {boolean} enable 是否启用碰撞
         */


        setColAble(enable) {
          this.m_collideComp.isEnable = enable;
        }
        /**
         * 开始射击
         */


        beginFire() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable = true;
        }
        /**
         * 停止射击
         */


        stopFire() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.fireEnable = false;
        }
        /**
         * 开始战斗
         * @param {boolean} isContinue 是否继续战斗
         */


        begine(isContinue = false) {
          if (isContinue) {
            this.beginFire();
            this.m_moveEnable = true;

            if (this.m_collideComp) {
              this.m_collideComp.isEnable = true;
            }
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.onPlaneIn();
          }
        }
        /**
         * 改变屏幕上的火力点
         * @param {number} index 火力点索引
         * @param {Array|null} data 火力点数据
         */


        changeScreen(index, data) {
          if (data == null) {
            if (index < this.m_fires.length) {
              const fire = this.m_fires[index];
              fire.setData(null, this.m_fireState, false, this);
              fire.node.active = false;
            }
          } else {
            if (index < this.m_fires.length) {
              const fire = this.m_fires[index];
              fire.node.active = true;
              fire.setData(data, this.m_fireState, false, this);
            } else {
              this.createAttackPoint(data);
            }
          }
        }
        /**
         * 移除所有火力点
         */


        removeAllFire() {
          this.m_fires.forEach(fire => {
            fire.setData(null, this.m_fireState, false, this);
          });
        }
        /**
         * 创建攻击点
         * @param {Array} data 攻击点数据
         * @returns {FireShells} 创建的攻击点
         */


        createAttackPoint(data) {
          const fireNode = new Node("fire");
          fireNode.parent = this.node;
          const fire = fireNode.addComponent(_crd && FireShells === void 0 ? (_reportPossibleCrUseOfFireShells({
            error: Error()
          }), FireShells) : FireShells);
          fire.setData(data, this.m_fireState, false, this);
          this.m_fires.push(fire);
          return fire;
        }
        /**
         * 添加拖尾效果
         */


        addStreak() {// Tween.stopAllByTarget(this.streak.node)
          // const ani = new Tween(this.streak.node)
          //     .to(1, { opacity: 255, scale: v3(1 })
          //     .to(3, { opacity: 204, scale: 0.88 })
          //     .to(6, { opacity: 255, scale: 1 });
          // tween(this.streak.node).repeatForever(ani).start();
        }

      }, _class3.PrefabName = "MainPlane", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "streak", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "skin", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpBar", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "blast", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "attspeAnim", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "scrAnim", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "auxAnim", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3f1fb1b0ed938bdce99b77de634e782c06fd0829.js.map