System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, SpriteAtlas, tween, ImageSequence, MyApp, _dec, _class, _class2, _crd, ccclass, UIAnimMethods;

  function _reportPossibleCrUseOfImageSequence(extras) {
    _reporterNs.report("ImageSequence", "./ImageSequence", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      SpriteAtlas = _cc.SpriteAtlas;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      ImageSequence = _unresolved_2.default;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "797b3w/st9C8or8RkgorUS5", "UIAnimMethods", undefined);

      __checkObsolete__(['_decorator', 'SpriteAtlas', 'tween']);

      ({
        ccclass
      } = _decorator);

      _export("default", UIAnimMethods = (_dec = ccclass('UIAnimMethods'), _dec(_class = (_class2 = class UIAnimMethods {
        // 帧数转换为时间

        /**
         * 根据帧数计算时间
         * @param {number} from 起始帧
         * @param {number} to 结束帧
         * @returns {number} 时间（秒）
         */
        static fromTo(from, to) {
          return to < from ? 0 : (to - from) * this.frameToTime;
        }
        /**
         * 双按钮动画
         * @param {Node} buttonNode 按钮节点
         * @param {string} atlasName 图集名称
         * @returns {Promise<void>}
         */


        async doubleBTNAnim(buttonNode, atlasName) {
          const atlas = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(atlasName, SpriteAtlas);

          if (atlas) {
            let effectLight = null;
            const frames = [];

            for (let i = 0; i < 8; i++) {
              const frameName = `effectRun_${i}`;
              const frame = atlas.getSpriteFrame(frameName);

              if (frame) {
                frames.push(frame);
              }
            }

            const effectNode = buttonNode.getChildByName('effect_light');

            if (effectNode) {
              effectLight = effectNode.getComponent(_crd && ImageSequence === void 0 ? (_reportPossibleCrUseOfImageSequence({
                error: Error()
              }), ImageSequence) : ImageSequence);

              if (effectLight) {
                effectLight.setData(frames, 15);
              }
            }

            tween(buttonNode).to(UIAnimMethods.fromTo(1, 4), {
              scale: 1.15
            }).to(UIAnimMethods.fromTo(4, 7), {
              scale: 0.9
            }).call(async () => {
              if (effectLight) {
                effectLight.node.active = true;
                await effectLight.play(1);
                effectLight.node.active = false;
              }
            }).to(UIAnimMethods.fromTo(7, 11), {
              scale: 1.05
            }).to(UIAnimMethods.fromTo(11, 15), {
              scale: 1
            }).delay(0.8).union().repeatForever().start();
          }
        }

      }, _class2.frameToTime = 1 / 30, _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3719438ff916823b28f76e0f2744dc07ec3532d5.js.map