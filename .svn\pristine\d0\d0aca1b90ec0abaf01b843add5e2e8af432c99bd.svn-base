import { res, ResGM } from '../../AutoGen/Luban/schema';
import { MyApp } from "../../MyApp";
import { IData } from "../DataManager";

export interface GMInfo {
    cfg?: ResGM;
    onSendClick?(args: string): string;
}

export class GM implements IData {
    /**key:gm界面页签名 value:gm结构信息 */
    private _gmMap: Map<res.GMTabID, GMInfo[]> = new Map()
    public init(): void {
        const tbGM = MyApp.lubanTables.TbGM
        tbGM.getDataList().forEach(e => {
            const gmInfo = this._gmMap.get(e.tabID)
            if (gmInfo) {
                gmInfo.push({ cfg: e })
            } else {
                this._gmMap.set(e.tabID, [{ cfg: e }])
            }
        });
        this.localClientGM()
    }

    get tabIDList() {
        return Array.from(this._gmMap.keys());
    }

    getCmdBtnListByTabID(tabID: res.GMTabID): GMInfo[] {
        return this._gmMap.get(tabID)?.filter(v => v.cfg !== undefined) || []
    }

    /**
     * 客户端注册gm按钮点击事件(GM.xlxs表里配置了才会有button显示)
     * @param tabID gm界面页签名
     * @param cmd gm命令
     * @param onSendClick 点击事件(返回值会显示在console)
     */
    public registerClientGMHandler(tabID: res.GMTabID, cmd: string, onSendClick: (args: string) => string) {
        let gmList = this._gmMap.get(tabID)
        if (!gmList) {
            gmList = [{ cfg: new ResGM({ tabID: tabID, cmd: cmd }), onSendClick: onSendClick }]
            this._gmMap.set(tabID, gmList)
        } else {
            const info = gmList.find(info => info.cfg.cmd === cmd)
            if (info) {
                info.onSendClick = onSendClick
            } else {
                gmList.push({
                    cfg: new ResGM({
                        tabID: tabID,
                        cmd: cmd,
                        desc: "",
                        name: "",
                        tabName: "",
                    }), onSendClick: onSendClick
                })
            }
        }
    }

    /**
     * 本地客户端gm指令例子(这个是例子，可以在其他代码模块调用,不要在这里调后面会做bundle，不然可能会有引用问题)
     */
    private localClientGM() {
        //配置表配置过数据，显示button,如何配置了没有和表里命令对上，就会发给服务器
        this.registerClientGMHandler(res.GMTabID.BATTLE, "//local_client_test", (args) => {
            return "local_client_test---完成"
        })

        //没有配置表数据，不显示button
        this.registerClientGMHandler(res.GMTabID.BATTLE, "//local_client_test1", (args) => {
            return "local_client_test1---完成"
        })
    }

    public update(): void {
    }
}