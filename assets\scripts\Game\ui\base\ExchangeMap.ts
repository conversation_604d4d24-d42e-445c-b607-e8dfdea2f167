import { _decorator, Component, Node, Sprite, tween, misc, SpriteFrame, Vec3, resources, Tween, UITransform, view, UIOpacity, math } from 'cc';
import { GameIns } from '../../GameIns';
import { GameConst } from '../../const/GameConst';
import { MyApp } from '../../../MyApp';
import GameResourceList from '../../const/GameResourceList';


const { ccclass, property } = _decorator;

class MaskData {
    speed: number = 0;
    viewTop: number =  view.getVisibleSize().height / 2;
    viewBot: number = -view.getVisibleSize().height / 2;
}

@ccclass('ExchangeMap')
export default class ExchangeMap extends Component {
    @property(Node)
    bgMask: Node = null;

    _maskFrame: SpriteFrame = null;
    _curMainStage: number = 0;
    maskOver: boolean = false;
    canMask: boolean = false;
    freeNode: Node[] = [];
    initPos: Vec3[] = [];
    fream: number = 0;
    m_isLJStage: boolean = false;
    data: MaskData = null;
    initOver: boolean = false;

    static me: ExchangeMap = null;

    onLoad() {
        ExchangeMap.me = this;
        this.initPos = [];
        this.bgMask.children.forEach((child) => {
            this.initPos.push(child.position.clone());
        });
    }

    maskTextureInit() {
        this.bgMask.children.forEach((child, index) => {
            const sprite = child.getComponent(Sprite);
            sprite.spriteFrame = null;

            const mainStage = GameIns.gameDataManager.curMainStage;

            if (this._maskFrame && this._curMainStage !== mainStage) {
                MyApp.resMgr.releaseAssetByForce(this._maskFrame);
                this._maskFrame = null;
            }

            this._curMainStage = mainStage;

            resources.load(GameResourceList.texture_map_mask, SpriteFrame, (err, spriteFrame) => {
                sprite.spriteFrame = spriteFrame;
                this._maskFrame = spriteFrame;
            });
        });
    }

    clear() {
        this.freeNode.forEach((node) => {
            node.parent = this.bgMask;
        });

        this.bgMask.children.forEach((child) => {
            const sprite = child.getComponent(Sprite);
            sprite.spriteFrame = null;

            if (this._maskFrame) {
                MyApp.resMgr.releaseAssetByForce(this._maskFrame);
                this._maskFrame = null;
            }
        });
    }

    maskLayerinit() {
        this.node.setSiblingIndex(this.node.parent.children.length - 1);
        this.node.active = true;
        this.fream = 0;

        this.freeNode.forEach((node) => {
            node.parent = this.bgMask;
        });

        this.bgMask.children.forEach((child, index) => {

            const maskName = "mask1";
            if (this._maskFrame && this._maskFrame.name === maskName) {
                child.getComponent(Sprite).spriteFrame = this._maskFrame;
            } else {
                resources.load(`Game/texture/mask/${maskName}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                    child.getComponent(Sprite).spriteFrame = spriteFrame;
                });
            }

            child.setPosition(this.initPos[index].x, this.initPos[index].y);
            child.getComponent(UIOpacity).opacity = 255;
            Tween.stopAllByTarget(child);

            if (index === this.bgMask.children.length - 1) {
                child.active = false;
            }
        });

        this.bgMask.y = 0;
        this.data = new MaskData();
        this.data.speed = 5200;
        this.parkourStageTip();
    }

    async parkourStageTip() {


        // const subStage = GameIns.battleManager.isContinue
        //     ? GameIns.battleManager.subStage
        //     : GameIns.battleManager.subStage + 1;

        // const stage = Math.max(1, subStage);
        // const maxStage = Math.min(stage, CDMgr.getStageNum(GameIns.gameDataManager.curMainStage));

        // const isLJStage = ExpeditionManager.isExpedition
        //     ? StageManager.isLJStageInExpedition(ExpeditionManager.currentSection)
        //     : StageManager.isLJStage(maxStage);

        // this.m_isLJStage = isLJStage;

        // if (isLJStage) {
        //     const dialog = await GameIns.uiManager.createDialog(EndOfSuccess);
        //     if (dialog) {
        //         dialog.setData(true);
        //     }
        // }
    }

    maskLayerRun(deltaTime: number) {
        if (this.maskOver) {
            this.node.active = false;
            return;
        }

        if (!this.initOver) {
            return;
        }

        if (!this.m_isLJStage) {
            this.fream++;

            if (this.fream >= 42) {
                this.data.speed = math.lerp(this.data.speed, 2400, 0.1);

                if (this.fream === 60) {
                    this.bgMask.children.forEach((child) => {
                        tween(child.getComponent(UIOpacity)).to(0.8, { opacity: 153 }).start();
                    });
                }
            } else {
                this.bgMask.children.forEach((child) => {
                    child.getComponent(UIOpacity).opacity = 255;
                });
            }
        }

        this.bgMask.y -= deltaTime * this.data.speed;
        this.data.viewBot += deltaTime * this.data.speed;
        this.data.viewTop += deltaTime * this.data.speed;

        this.bgMask.children.forEach((child) => {
            if (this.data.viewTop > child.y - child.getComponent(UITransform).height / 2 && this.canMask) {
                child.active = true;
            }
        });

        if (
            this.bgMask.children.length > 0 &&
            this.data.viewBot > this.bgMask.children[0].y + this.bgMask.children[0].getComponent(UITransform).height / 2
        ) {
            const node = this.bgMask.children[0];
            this.freeNode.push(node);
            node.parent = null;
        }

        if (
            this.bgMask.children.length > 0 &&
            this.data.viewTop >
                this.bgMask.children[this.bgMask.children.length - 1].y +
                    this.bgMask.children[this.bgMask.children.length - 1].getComponent(UITransform).height / 2 -
                    500 &&
            this.canMask
        ) {
            const node = this.freeNode.pop();
            if (node) {
                node.parent = this.bgMask;
                node.y =
                    this.bgMask.children[this.bgMask.children.length - 2].y +
                    this.bgMask.children[this.bgMask.children.length - 2].getComponent(UITransform).height / 2 +
                    node.getComponent(UITransform).height / 2 -
                    500;
            }
        }

        this.maskOver = this.bgMask.children.length <= 0;
    }



    startChange() {
        this.initOver = false;
        this.maskLayerinit();
        this.initOver = true;
        this.maskOver = false;
        this.canMask = true;
    }

    endChange() {
        // if (GameIns.mainPlaneManager.isEndChange) {

            // if (isLJStage) {
            //     setTimeout(() => {
            //         this.canMask = false;
            //     }, 500);
            // } else {
                this.canMask = false;
            // }
        // }
    }

    update(deltaTime: number) {
        if (GameConst.GameAble) {
            this.maskLayerRun(deltaTime > 0.2 ? 0.016666666666667 : deltaTime);
        }
    }
}