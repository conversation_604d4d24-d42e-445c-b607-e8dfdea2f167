import { _decorator, Component, Node } from 'cc';
import List from '../../common/components/list/List';
import { FriendCellUI } from './FriendCellUI';
const { ccclass, property } = _decorator;

@ccclass('FriendAddUI')
export class FriendAddUI extends Component {
    @property(List)
    list: List;
    start() {
        this.list.node.active = true;
        this.list.numItems = 10;
    }

    update(deltaTime: number) {

    }
    onListRender(listItem: Node, row: number) {
        const cell = listItem.getComponent(FriendCellUI);
        if (cell !== null) {
            cell.setType(2);
            cell.txtName.string = "小师妹：" + (row + 11);
        }
    }
}


