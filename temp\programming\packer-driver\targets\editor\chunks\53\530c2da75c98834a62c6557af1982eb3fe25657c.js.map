{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendCellUI.ts"], "names": ["_decorator", "Component", "Label", "Node", "RichText", "EventMgr", "ButtonPlus", "UIMgr", "MainEvent", "PopupUI", "ccclass", "property", "FriendCellUI", "onLoad", "getComponent", "addClick", "onClick", "setPowerText", "btnGet", "onGet", "btnSend", "onSend", "btnIgnore", "onIgnore", "btnAgree", "onAgree", "btnApply", "onApply", "powerValue", "currentText", "txtPower", "string", "prefixColorRegex", "prefixMatch", "match", "prefixColorTag", "valueColorRegex", "valueMatch", "valueColorTag", "formattedValue", "toFixed", "toString", "setLastOnlineTime", "lastOnlineTimestamp", "txtOnline", "currentTimestamp", "Math", "floor", "Date", "now", "diffSeconds", "diffMinutes", "diffHours", "diffDays", "onDestroy", "emit", "BattleItemClick", "getComponentInChildren", "openUI", "onList<PERSON>ender", "listItem", "row", "name", "setType", "type", "node1", "active", "node2", "node3"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;;AACpCC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U,GAE9B;;8BAEaY,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACP,QAAD,C,UAERO,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAGRA,QAAQ,CAACR,IAAD,C,WAERQ,QAAQ;AAAA;AAAA,mC,2BA1Bb,MACaC,YADb,SACkCX,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AA4B9BY,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA8BC,QAA9B,CAAuC,KAAKC,OAA5C,EAAqD,IAArD;AACA,eAAKC,YAAL,CAAkB,OAAlB;AACA,eAAKC,MAAL,CAAYH,QAAZ,CAAqB,KAAKI,KAA1B,EAAiC,IAAjC;AACA,eAAKC,OAAL,CAAaL,QAAb,CAAsB,KAAKM,MAA3B,EAAmC,IAAnC;AACA,eAAKC,SAAL,CAAeP,QAAf,CAAwB,KAAKQ,QAA7B,EAAuC,IAAvC;AACA,eAAKC,QAAL,CAAcT,QAAd,CAAuB,KAAKU,OAA5B,EAAqC,IAArC;AACA,eAAKC,QAAL,CAAcX,QAAd,CAAuB,KAAKY,OAA5B,EAAqC,IAArC;AAEH;;AACMV,QAAAA,YAAY,CAACW,UAAD,EAA2B;AAC1C,gBAAMC,WAAW,GAAG,KAAKC,QAAL,CAAcC,MAAlC;AACA,gBAAMC,gBAAgB,GAAG,6BAAzB;AACA,gBAAMC,WAAW,GAAGJ,WAAW,CAACK,KAAZ,CAAkBF,gBAAlB,CAApB;AACA,cAAIG,cAAc,GAAG,iBAArB,CAJ0C,CAIF;;AACxC,gBAAMC,eAAe,GAAG,6BAAxB;AACA,gBAAMC,UAAU,GAAGR,WAAW,CAACK,KAAZ,CAAkBE,eAAlB,CAAnB;AACA,cAAIE,aAAa,GAAG,iBAApB,CAP0C,CAOH;;AACvC,cAAIL,WAAW,IAAIA,WAAW,CAAC,CAAD,CAA9B,EAAmC;AAC/BE,YAAAA,cAAc,GAAI,UAASF,WAAW,CAAC,CAAD,CAAI,GAA1C;AACH;;AACD,cAAII,UAAU,IAAIA,UAAU,CAAC,CAAD,CAA5B,EAAiC;AAC7BC,YAAAA,aAAa,GAAI,UAASD,UAAU,CAAC,CAAD,CAAI,GAAxC;AACH;;AACD,cAAIE,cAAJ;;AACA,cAAIX,UAAU,IAAI,IAAlB,EAAwB;AACpBW,YAAAA,cAAc,GAAG,CAACX,UAAU,GAAG,IAAd,EAAoBY,OAApB,CAA4B,CAA5B,IAAiC,GAAlD;AACH,WAFD,MAEO;AACHD,YAAAA,cAAc,GAAGX,UAAU,CAACa,QAAX,EAAjB;AACH;;AACD,eAAKX,QAAL,CAAcC,MAAd,GAAwB,GAAEI,cAAe,cAAaG,aAAc,GAAEC,cAAe,UAArF;AACH;;AAEMG,QAAAA,iBAAiB,CAACC,mBAAD,EAAoC;AACxD,cAAIA,mBAAmB,KAAK,CAA5B,EAA+B;AAC3B,iBAAKC,SAAL,CAAeb,MAAf,GAAwB,IAAxB;AACA;AACH;;AAED,gBAAMc,gBAAgB,GAAGC,IAAI,CAACC,KAAL,CAAWC,IAAI,CAACC,GAAL,KAAa,IAAxB,CAAzB,CANwD,CAMA;;AACxD,gBAAMC,WAAW,GAAGL,gBAAgB,GAAGF,mBAAvC;;AAEA,cAAIO,WAAW,GAAG,CAAlB,EAAqB;AACjB;AACA,iBAAKN,SAAL,CAAeb,MAAf,GAAwB,EAAxB;AACA;AACH;;AAED,gBAAMoB,WAAW,GAAGL,IAAI,CAACC,KAAL,CAAWG,WAAW,GAAG,EAAzB,CAApB;AACA,gBAAME,SAAS,GAAGN,IAAI,CAACC,KAAL,CAAWG,WAAW,GAAG,IAAzB,CAAlB;;AAEA,cAAIC,WAAW,GAAG,CAAlB,EAAqB;AACjB,iBAAKP,SAAL,CAAeb,MAAf,GAAwB,IAAxB;AACH,WAFD,MAEO,IAAIqB,SAAS,GAAG,CAAhB,EAAmB;AACtB,iBAAKR,SAAL,CAAeb,MAAf,GAAyB,GAAEoB,WAAY,KAAvC;AACH,WAFM,MAEA,IAAIC,SAAS,GAAG,EAAhB,EAAoB;AACvB,iBAAKR,SAAL,CAAeb,MAAf,GAAyB,GAAEqB,SAAU,KAArC;AACH,WAFM,MAEA;AACH,gBAAIC,QAAQ,GAAGP,IAAI,CAACC,KAAL,CAAWG,WAAW,GAAG,KAAzB,CAAf;AACA,gBAAIG,QAAQ,GAAG,CAAf,EAAkBA,QAAQ,GAAG,CAAX;AAClB,iBAAKT,SAAL,CAAeb,MAAf,GAAyB,GAAEsB,QAAS,IAApC;AACH;AACJ;;AAESC,QAAAA,SAAS,GAAS,CAC3B;;AAEOtC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASuC,IAAT,CAAc;AAAA;AAAA,sCAAUC,eAAxB,EAAyC,KAAKC,sBAAL,CAA4BvD,KAA5B,EAAmC6B,MAA5E;AACH;;AACOZ,QAAAA,KAAK,GAAG;AACZ;AAAA;AAAA,8BAAMuC,MAAN;AAAA;AAAA,kCAAsB,IAAtB;AACH;;AACOrC,QAAAA,MAAM,GAAG;AACb;AAAA;AAAA,8BAAMqC,MAAN;AAAA;AAAA,kCAAsB,IAAtB;AACH;;AACOnC,QAAAA,QAAQ,GAAG;AACf;AAAA;AAAA,8BAAMmC,MAAN;AAAA;AAAA,kCAAsB,KAAtB;AACH;;AACOjC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAMiC,MAAN;AAAA;AAAA,kCAAsB,KAAtB;AACH;;AACO/B,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAM+B,MAAN;AAAA;AAAA,kCAAsB,KAAtB;AACH,SAhHuC,CAiHxC;;;AACAC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtCD,UAAAA,QAAQ,CAACE,IAAT,GAAiB,GAAED,GAAI,EAAvB;AACH;;AAEME,QAAAA,OAAO,CAACC,IAAD,EAAe;AACzB,cAAIA,IAAI,KAAK,CAAb,EAAgB;AACZ,iBAAKC,KAAL,CAAWC,MAAX,GAAoB,IAApB;AACA,iBAAKC,KAAL,CAAWD,MAAX,GAAoB,KAApB;AACA,iBAAKE,KAAL,CAAWF,MAAX,GAAoB,KAApB;AACH,WAJD,MAIO,IAAIF,IAAI,KAAK,CAAb,EAAgB;AACnB,iBAAKC,KAAL,CAAWC,MAAX,GAAoB,KAApB;AACA,iBAAKC,KAAL,CAAWD,MAAX,GAAoB,IAApB;AACA,iBAAKE,KAAL,CAAWF,MAAX,GAAoB,KAApB;AACH,WAJM,MAIA,IAAIF,IAAI,KAAK,CAAb,EAAgB;AACnB,iBAAKC,KAAL,CAAWC,MAAX,GAAoB,KAApB;AACA,iBAAKC,KAAL,CAAWD,MAAX,GAAoB,KAApB;AACA,iBAAKE,KAAL,CAAWF,MAAX,GAAoB,IAApB;AACH;AACJ;;AApIuC,O;;;;;iBAGvB,I;;;;;;;iBAEI,I;;;;;;;iBAEF,I;;;;;;;iBAGL,I;;;;;;;;;;;;;;;;;iBAOA,I;;;;;;;;;;;;;;;;;iBAOA,I", "sourcesContent": ["import { _decorator, Component, Label, Node, RichText } from \"cc\";\r\nimport { EventMgr } from \"../../../event/EventManager\";\r\nimport { ButtonPlus } from \"../../common/components/button/ButtonPlus\";\r\nimport { UIMgr } from \"../../UIMgr\";\r\nimport { MainEvent } from \"../MainEvent\";\r\nimport { PopupUI } from \"../PopupUI\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n//参考 BagItem\r\n@ccclass('FriendCellUI')\r\nexport class FriendCellUI extends Component {\r\n\r\n    @property(Label)\r\n    txtName: Label = null;\r\n    @property(RichText)\r\n    txtPower: RichText = null;\r\n    @property(Label)\r\n    txtOnline: Label = null;\r\n\r\n    @property(Node)\r\n    node1: Node = null;\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus;\r\n    @property(ButtonPlus)\r\n    btnSend: ButtonPlus;\r\n\r\n    @property(Node)\r\n    node2: Node = null;\r\n    @property(ButtonPlus)\r\n    btnIgnore: ButtonPlus;\r\n    @property(ButtonPlus)\r\n    btnAgree: ButtonPlus;\r\n\r\n    @property(Node)\r\n    node3: Node = null;\r\n    @property(ButtonPlus)\r\n    btnApply: ButtonPlus;\r\n\r\n    protected onLoad(): void {\r\n        this.getComponent(ButtonPlus).addClick(this.onClick, this)\r\n        this.setPowerText(4567500);\r\n        this.btnGet.addClick(this.onGet, this)\r\n        this.btnSend.addClick(this.onSend, this)\r\n        this.btnIgnore.addClick(this.onIgnore, this)\r\n        this.btnAgree.addClick(this.onAgree, this)\r\n        this.btnApply.addClick(this.onApply, this)\r\n\r\n    }\r\n    public setPowerText(powerValue: number): void {\r\n        const currentText = this.txtPower.string;\r\n        const prefixColorRegex = /<color=([^>]+)>战力：<\\/color>/;\r\n        const prefixMatch = currentText.match(prefixColorRegex);\r\n        let prefixColorTag = \"<color=#00ff00>\"; // 默认颜色\r\n        const valueColorRegex = /<color=([^>]+)>\\d+<\\/color>/;\r\n        const valueMatch = currentText.match(valueColorRegex);\r\n        let valueColorTag = \"<color=#0fffff>\"; // 默认颜色\r\n        if (prefixMatch && prefixMatch[1]) {\r\n            prefixColorTag = `<color=${prefixMatch[1]}>`;\r\n        }\r\n        if (valueMatch && valueMatch[1]) {\r\n            valueColorTag = `<color=${valueMatch[1]}>`;\r\n        }\r\n        let formattedValue: string;\r\n        if (powerValue >= 1000) {\r\n            formattedValue = (powerValue / 1000).toFixed(1) + \"K\";\r\n        } else {\r\n            formattedValue = powerValue.toString();\r\n        }\r\n        this.txtPower.string = `${prefixColorTag}战力：</color>${valueColorTag}${formattedValue}</color>`;\r\n    }\r\n\r\n    public setLastOnlineTime(lastOnlineTimestamp: number): void {\r\n        if (lastOnlineTimestamp === 0) {\r\n            this.txtOnline.string = \"在线\";\r\n            return;\r\n        }\r\n\r\n        const currentTimestamp = Math.floor(Date.now() / 1000); // 当前时间戳（秒）\r\n        const diffSeconds = currentTimestamp - lastOnlineTimestamp;\r\n\r\n        if (diffSeconds < 0) {\r\n            // 时间戳异常（未来时间）\r\n            this.txtOnline.string = \"\";\r\n            return;\r\n        }\r\n\r\n        const diffMinutes = Math.floor(diffSeconds / 60);\r\n        const diffHours = Math.floor(diffSeconds / 3600);\r\n\r\n        if (diffMinutes < 1) {\r\n            this.txtOnline.string = \"刚刚\";\r\n        } else if (diffHours < 1) {\r\n            this.txtOnline.string = `${diffMinutes}分钟前`;\r\n        } else if (diffHours < 24) {\r\n            this.txtOnline.string = `${diffHours}小时前`;\r\n        } else {\r\n            var diffDays = Math.floor(diffSeconds / 86400);\r\n            if (diffDays > 7) diffDays = 7;\r\n            this.txtOnline.string = `${diffDays}天前`;\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    private onClick() {\r\n        EventMgr.emit(MainEvent.BattleItemClick, this.getComponentInChildren(Label).string);\r\n    }\r\n    private onGet() {\r\n        UIMgr.openUI(PopupUI, \"收了\");\r\n    }\r\n    private onSend() {\r\n        UIMgr.openUI(PopupUI, \"送了\");\r\n    }\r\n    private onIgnore() {\r\n        UIMgr.openUI(PopupUI, \"忽略了\");\r\n    }\r\n    private onAgree() {\r\n        UIMgr.openUI(PopupUI, \"同意了\");\r\n    }\r\n    private onApply() {\r\n        UIMgr.openUI(PopupUI, \"申请了\");\r\n    }\r\n    //cell的容器去手动调用\r\n    onListRender(listItem: Node, row: number) {\r\n        listItem.name = `${row}`\r\n    }\r\n\r\n    public setType(type: number) {\r\n        if (type === 1) {\r\n            this.node1.active = true;\r\n            this.node2.active = false;\r\n            this.node3.active = false;\r\n        } else if (type === 2) {\r\n            this.node1.active = false;\r\n            this.node2.active = true;\r\n            this.node3.active = false;\r\n        } else if (type === 3) {\r\n            this.node1.active = false;\r\n            this.node2.active = false;\r\n            this.node3.active = true;\r\n        }\r\n    }\r\n}"]}