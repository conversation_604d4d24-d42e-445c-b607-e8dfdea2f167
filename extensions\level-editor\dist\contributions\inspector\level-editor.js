'use strict';
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const path_1 = __importDefault(require("path"));
exports.template = `
<ui-prop type="dump" class="progress"></ui-prop>
<ui-prop type="dump" class="levelPrefab"></ui-prop>
<ui-prop type="dump" class="save"></ui-prop>
<ui-prop type="dump" class="levelPrefabUUID"></ui-prop>
<ui-prop >
    <ui-button class="btn-save">保存</ui-button>
    <ui-button class="btn-new">新建</ui-button>
</ui-prop>
`;
exports.$ = {
    progress: '.progress',
    levelPrefab: '.levelPrefab',
    save: '.save',
    levelPrefabUUID: '.levelPrefabUUID',
    btnSave: '.btn-save',
    btnNew: '.btn-new'
};
function update(dump) {
    // 使用 ui-porp 自动渲染，设置 prop 的 type 为 dump
    // render 传入一个 dump 数据，能够自动渲染出对应的界面
    // 自动渲染的界面修改后，能够自动提交数据
    this.dump = dump;
    this.$.progress.render(dump.value.progress);
    this.$.levelPrefab.render(dump.value.levelPrefab); // 这个是 levelPrefab 的 uuid，需要转成 JsonAsset 才能用
    this.$.save.dump = dump.value.save;
    this.$.levelPrefabUUID.dump = dump.value.levelPrefabUUID;
    // this.$.save.render(dump.value.save);
}
function ready() {
    this.$.btnSave.addEventListener('confirm', () => {
        console.log("panel save level");
        this.dump.value.save.value = true;
        this.$.save.dispatch('change-dump');
    });
    this.$.btnNew.addEventListener('confirm', async () => {
        console.log("panel new level:", Editor.Project.path);
        // Editor.Panel.open('level-editor.newlevel')
        const dirPath = path_1.default.join(Editor.Project.path, "assets", "resources", "Game", "level");
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Level', extensions: ['json'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path_1.default.relative(dirPath, retData.filePath);
        console.log("panel new level name:", name);
        const filePath = `db://assets/resources/Game/level/${name}`;
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', filePath, "{}");
        console.log("panel new level create asset rsp:", createRsp);
        this.dump.value.levelPrefabUUID.value = createRsp === null || createRsp === void 0 ? void 0 : createRsp.uuid;
        this.$.levelPrefabUUID.dispatch('change-dump');
        // Editor.Message.send('level-editor', 'new-level', name)
    });
}
//# sourceMappingURL=data:application/json;base64,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