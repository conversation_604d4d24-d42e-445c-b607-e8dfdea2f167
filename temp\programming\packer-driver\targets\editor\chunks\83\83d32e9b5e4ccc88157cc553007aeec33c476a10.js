System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, Label, Node, Sprite, EventMgr, BaseUI, UILayer, UIMgr, BattleUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _crd, ccclass, property, DialogueUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleUI(extras) {
    _reporterNs.report("BattleUI", "../BattleUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      Label = _cc.Label;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      EventMgr = _unresolved_2.EventMgr;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      BattleUI = _unresolved_4.BattleUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9297diwgAlCdK6VR7ziSeNu", "DialogueUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Label', 'Node', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DialogueUI", DialogueUI = (_dec = ccclass('DialogueUI'), _dec2 = property(Node), _dec3 = property(Sprite), _dec4 = property(Label), _dec5 = property(Node), _dec6 = property(Sprite), _dec7 = property(Label), _dec8 = property(Label), _dec9 = property(Button), _dec(_class = (_class2 = class DialogueUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "nodeLeft", _descriptor, this);

          _initializerDefineProperty(this, "characterImageLeft", _descriptor2, this);

          _initializerDefineProperty(this, "characterNameLeft", _descriptor3, this);

          _initializerDefineProperty(this, "nodeRight", _descriptor4, this);

          _initializerDefineProperty(this, "characterImageRight", _descriptor5, this);

          _initializerDefineProperty(this, "characterNameRight", _descriptor6, this);

          _initializerDefineProperty(this, "dialogueContent", _descriptor7, this);

          _initializerDefineProperty(this, "btnClick", _descriptor8, this);

          //data
          this.dialogueID = 0;
          this.dialogueContentList = [];
          this.dialogueIndex = 0;
        }

        static getUrl() {
          return "ui/main/dialogue/DialogueUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        onLoad() {
          const randomDialogues = ["111", "222", "333", "444"];

          for (let i = 0; i < randomDialogues.length; i++) {
            this.dialogueContentList.push(randomDialogues[i]);
          }

          this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];
          this.btnClick.node.on('click', this.onClick, this);
          this.nodeRight.active = false;
        }

        async onClick() {
          this.dialogueIndex++;

          if (this.dialogueIndex >= this.dialogueContentList.length) {
            this.dialogueIndex = 0;
            this.btnClick.node.active = false;
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(DialogueUI);
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
              error: Error()
            }), BattleUI) : BattleUI);
            return;
          }

          if (this.dialogueIndex % 2 === 0) {
            this.nodeRight.active = false;
            this.nodeLeft.active = true;
          } else {
            this.nodeLeft.active = false;
            this.nodeRight.active = true;
          }

          this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];
        }

        async onShow(dialogueID) {
          this.dialogueID = dialogueID;
        }

        async onHide() {}

        async onClose() {}

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeLeft", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "characterImageLeft", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "characterNameLeft", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "nodeRight", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "characterImageRight", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "characterNameRight", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "dialogueContent", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "btnClick", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=83d32e9b5e4ccc88157cc553007aeec33c476a10.js.map