System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, LevelBaseUI, _dec, _class, _crd, ccclass, LevelElemUI;

  function _reportPossibleCrUseOfLevelBaseUI(extras) {
    _reporterNs.report("LevelBaseUI", "./LevelBaseUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataElem(extras) {
    _reporterNs.report("LevelDataElem", "../../../leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      LevelBaseUI = _unresolved_2.LevelBaseUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "69f1fhd+eZC6rdr+rmLHhy9", "LevelElemUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Vec2']);

      ({
        ccclass
      } = _decorator);

      _export("LevelElemUI", LevelElemUI = (_dec = ccclass('LevelElemUI'), _dec(_class = class LevelElemUI extends Component {
        constructor(...args) {
          super(...args);
          this.elemID = "";
        }

        get time() {
          var _this$node$parent;

          const layerNode = (_this$node$parent = this.node.parent) == null ? void 0 : _this$node$parent.parent;

          if (!layerNode) {
            return 0;
          }

          const rootNode = layerNode.parent.parent;

          if (!rootNode) {
            return 0;
          }

          const baseUI = rootNode.getComponent(_crd && LevelBaseUI === void 0 ? (_reportPossibleCrUseOfLevelBaseUI({
            error: Error()
          }), LevelBaseUI) : LevelBaseUI);

          if (!baseUI) {
            return 0;
          }

          const layer = baseUI.floorLayers.find(layer => layer.node == layerNode) || baseUI.skyLayers.find(layer => layer.node == layerNode);

          if (!layer) {
            return 0;
          }

          return this.node.position.y / layer.speed;
        }

        onLoad() {
          if (this.elemID == "") {
            this.elemID = this.uuid;
          }
        }

        initByLevelData(data) {
          this.node.setPosition(data.position.x, data.position.y);
          this.elemID = data.elemID;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8c865788561ec68e967eb375cdb82731d4c28dfb.js.map