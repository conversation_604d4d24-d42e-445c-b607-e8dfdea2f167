import { _decorator, Prefab, assetManager } from 'cc';
import { LevelDataWave } from '../../../leveldata/leveldata';
import { LevelElemUI } from './LevelElemUI';
const { ccclass, property } = _decorator;

@ccclass('LevelWaveParam')
export class LevelWaveParam {
    public name: string = "";
    public value: number = 0;
}

@ccclass('LevelWaveUI')
export class LevelWaveUI extends LevelElemUI {
    public wavePrefab: Prefab | null = null;
    public planeID: number = 0;
    @property([LevelWaveParam])
    public params: LevelWaveParam[] = [];

    public initByLevelData(data: LevelDataWave) {
        super.initByLevelData(data);
        this.planeID = data.planeID;
        this.params = [];
        if (data.params) {
            for (let k in data.params) {
                var param = new LevelWaveParam()
                param.name = k
                param.value = data.params[k]
                this.params.push(param);
            }
        }
        if (data.waveUUID != "") {
            assetManager.loadAny({uuid:data.waveUUID}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelLayerUI initByLevelData load wave prefab err", err);
                    return
                }
                this.wavePrefab = prefab;
            })
        }
    }
}