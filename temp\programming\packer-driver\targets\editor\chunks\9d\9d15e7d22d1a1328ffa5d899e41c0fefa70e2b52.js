System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, ccenum, Component, director, loader, LabelComponent, ProgressBar, WECHAT, DevLoginUI, BattleUI, BottomUI, TopUI, UIMgr, logDebug, ResLoadingTips, _dec, _dec2, _dec3, _dec4, _dec5, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, warnCustom, groupCustom, GameLogLevel, ResUpdate;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDevLoginUI(extras) {
    _reporterNs.report("DevLoginUI", "../ui/DevLoginUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleUI(extras) {
    _reporterNs.report("BattleUI", "../ui/main/BattleUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "../ui/main/BottomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "../ui/main/TopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../Utils/Logger", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      ccenum = _cc.ccenum;
      Component = _cc.Component;
      director = _cc.director;
      loader = _cc.loader;
      LabelComponent = _cc.LabelComponent;
      ProgressBar = _cc.ProgressBar;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }, function (_unresolved_2) {}, function (_unresolved_3) {
      DevLoginUI = _unresolved_3.DevLoginUI;
    }, function (_unresolved_4) {
      BattleUI = _unresolved_4.BattleUI;
    }, function (_unresolved_5) {
      BottomUI = _unresolved_5.BottomUI;
    }, function (_unresolved_6) {
      TopUI = _unresolved_6.TopUI;
    }, function (_unresolved_7) {
      UIMgr = _unresolved_7.UIMgr;
    }, function (_unresolved_8) {
      logDebug = _unresolved_8.logDebug;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "30734ypWehPU5ZyhV2WDXQn", "ResUpdate", undefined);

      __checkObsolete__(['_decorator', 'ccenum', 'Component', 'director', 'loader', 'LabelComponent', 'ProgressBar']);

      ({
        ccclass,
        property
      } = _decorator);
      ResLoadingTips = class ResLoadingTips {
        constructor() {
          this.Progress = void 0;
          this.Context = void 0;
        }

      };

      if (WECHAT) {
        warnCustom = console.warn;

        console.warn = function (res) {
          if (typeof res == "string" && res.indexOf("文件路径在真机上可能无法读取") > -1) {
            return;
          } else {
            warnCustom(res);
          }
        };

        groupCustom = console.group;

        console.group = function (res) {
          if (typeof res == "string" && res.indexOf("读取文件/文件夹警告") > -1) {
            return;
          } else {
            groupCustom(res);
          }
        };
      }

      GameLogLevel = /*#__PURE__*/function (GameLogLevel) {
        GameLogLevel[GameLogLevel["TRACE"] = 0] = "TRACE";
        GameLogLevel[GameLogLevel["DEBUG"] = 1] = "DEBUG";
        GameLogLevel[GameLogLevel["LOG"] = 2] = "LOG";
        GameLogLevel[GameLogLevel["INFO"] = 3] = "INFO";
        GameLogLevel[GameLogLevel["WARN"] = 4] = "WARN";
        GameLogLevel[GameLogLevel["ERROR"] = 5] = "ERROR";
        return GameLogLevel;
      }(GameLogLevel || {});

      ccenum(GameLogLevel);

      _export("ResUpdate", ResUpdate = (_dec = ccclass("ResUpdate"), _dec2 = property(LabelComponent), _dec3 = property(LabelComponent), _dec4 = property(LabelComponent), _dec5 = property(ProgressBar), _dec(_class2 = (_class3 = class ResUpdate extends Component {
        constructor(...args) {
          super(...args);

          /* class member could be defined like this */
          // dummy = '';
          _initializerDefineProperty(this, "countLabel", _descriptor, this);

          _initializerDefineProperty(this, "perLabel", _descriptor2, this);

          _initializerDefineProperty(this, "versionLabel", _descriptor3, this);

          _initializerDefineProperty(this, "loadingBar", _descriptor4, this);
        }

        start() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).initializeLayers(); // Your initialization goes here.

          let THIS = this;
          director.preloadScene("Main", this.OnLoadProgress.bind(this), async () => {
            // dev 先load login UI
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
              error: Error()
            }), DevLoginUI) : DevLoginUI);
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
              error: Error()
            }), BattleUI) : BattleUI);
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
              error: Error()
            }), BottomUI) : BottomUI);
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
              error: Error()
            }), TopUI) : TopUI);
            director.loadScene("Main");
          });
        }

        OnLoadProgress(completedCount, totalCount, item) {
          let progress = completedCount / totalCount;
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("ResUpdate", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}, ${item.id}`);

          if (this.node == null) {
            return;
          }

          this.perLabel.string = (progress * 100).toFixed(2) + "%";
          this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')';
          this.loadingBar.progress = progress;
        }

        onLoad() {}

        onDestroy() {
          loader.onProgress = null;
        }

        update(deltaTime) {//if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)
          //{
          //    director.loadScene("MainScene")
          //}
          // Your update function goes here.
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "countLabel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "perLabel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "versionLabel", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class3.prototype, "loadingBar", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class3)) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9d15e7d22d1a1328ffa5d899e41c0fefa70e2b52.js.map