System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, Component, instantiate, Node, UITransform, LevelLayerUI, _dec, _class, _dec2, _class3, _dec3, _class5, _crd, ccclass, property, BackgroundsNodeName, LevelLayer, LevelBackgroundLayer, LevelBaseUI;

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayerUI(extras) {
    _reporterNs.report("LevelLayerUI", "./LevelLayerUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      LevelLayerUI = _unresolved_2.LevelLayerUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "154e0mDh+hNJbitYOMQLQ2O", "LevelBaseUI", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Component', 'instantiate', 'Node', 'Prefab', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);
      BackgroundsNodeName = "backgrounds";
      LevelLayer = (_dec = ccclass('LevelLayer'), _dec(_class = class LevelLayer {
        constructor() {
          this.node = null;
          this.speed = 0;
        }

      }) || _class);
      LevelBackgroundLayer = (_dec2 = ccclass('LevelBackgroundLayer'), _dec2(_class3 = class LevelBackgroundLayer extends LevelLayer {
        constructor(...args) {
          super(...args);
          this.backgrounds = [];
          this.backgroundsNode = null;
        }

      }) || _class3);

      _export("LevelBaseUI", LevelBaseUI = (_dec3 = ccclass('LevelBaseUI'), _dec3(_class5 = class LevelBaseUI extends Component {
        constructor(...args) {
          super(...args);
          this._totalTime = 10;
          // 当前关卡的时长
          this._lastLevelHeight = 0;
          // 上一关的关卡高度
          this._lastLevelOffsetY = 0;
          // 上一关的关卡偏移量
          this._backgroundLayerNode = null;
          this._floorLayersNode = null;
          this._skyLayersNode = null;
          this._backgroundLayer = null;
          this._floorLayers = [];
          this._skyLayers = [];
        }

        get floorLayers() {
          return this._floorLayers;
        }

        get skyLayers() {
          return this._skyLayers;
        }

        get backgroundLayer() {
          return this._backgroundLayer;
        }

        get TotalTime() {
          return this._totalTime;
        }

        getLevelTotalHeightByIndex(index) {
          var totalHeight = 0;

          if (this.backgroundLayer) {
            var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${index}`);

            if (lastBgNode) {
              const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);

              if (backgroundsNode) {
                backgroundsNode.children.forEach(bg => {
                  var _bg$getComponent;

                  const height = (_bg$getComponent = bg.getComponent(UITransform)) == null ? void 0 : _bg$getComponent.contentSize.height;
                  totalHeight += height;
                });
              }
            }
          }

          return totalHeight;
        }

        onLoad() {}

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        async levelPrefab(levelData, levelInfo, bFristLevel = false) {
          this._backgroundLayerNode = this._getOrAddNode(this.node, "BackgroundLayer");
          this._floorLayersNode = this._getOrAddNode(this.node, "FloorLayers");
          this._skyLayersNode = this._getOrAddNode(this.node, "SkyLayers");

          if (bFristLevel) {
            await this._initByLevelData(levelData, levelInfo);
          } else {
            this._initByLevelData(levelData, levelInfo);

            return Promise.resolve();
          }
        }

        setBackgroundLayerInfo(speed, time) {
          this.backgroundLayer.speed = speed;
          this._totalTime = time;
        }

        async _initByLevelData(data, levelInfo, bFristLevel = false) {
          //if (bFristLevel) {
          await this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo); //} else {
          //this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo);
          //}

          this._initLayers(this._floorLayersNode, this.floorLayers, data.floorLayers, bFristLevel);

          this._initLayers(this._skyLayersNode, this.skyLayers, data.skyLayers, bFristLevel);
        }

        _initLayers(parentNode, layers, dataLayers, bFristLevel) {
          dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).initByLevelData(layer, this._lastLevelOffsetY);
            layers.push(levelLayer);
          });
        }

        async _initBackgroundLayer(parentNode, data, levelInfo) {
          if (data.backgroundLayer.backgrounds.length > 0) {
            if (this._backgroundLayer === null) {
              this._backgroundLayer = new LevelBackgroundLayer();
              this._backgroundLayer.backgrounds = [];
            }

            this._backgroundLayer.speed = data.backgroundLayer.speed;
            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1280);
            const loadPromises = data.backgroundLayer.backgrounds.map(backgroundLayer => {
              return new Promise((resolve, reject) => {
                assetManager.loadAny({
                  uuid: backgroundLayer
                }, (err, prefab) => {
                  if (err) {
                    console.error('LevelBaseUI', 'initByLevelData load background prefab err', err);
                    reject(err);
                  } else {
                    this._backgroundLayer.backgrounds.push(prefab);

                    resolve();
                  }
                });
              });
            });
            await Promise.all(loadPromises); // 节点设置偏移

            var offsetY = 0;
            this._lastLevelHeight = 0;

            if (this.backgroundLayer) {
              var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${levelInfo.levelIndex - 1}`);

              if (lastBgNode) {
                offsetY = lastBgNode.getPosition().y;
                const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);

                if (backgroundsNode) {
                  backgroundsNode.children.forEach(bg => {
                    var _bg$getComponent2;

                    const height = (_bg$getComponent2 = bg.getComponent(UITransform)) == null ? void 0 : _bg$getComponent2.contentSize.height;
                    this._lastLevelHeight += height;
                  });
                }
              }
            }

            this._lastLevelOffsetY = this._lastLevelHeight + offsetY;
            console.log('LevelBaseUI', "_initBackgroundLayer _lastLevelHeight", this._lastLevelHeight, "offsetY", offsetY);
            this.backgroundLayer.node = this._addLayer(parentNode, `layer_${levelInfo.levelIndex}`).node;
            this.backgroundLayer.backgroundsNode = this._getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
            this.backgroundLayer.node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).initByLevelData(data.backgroundLayer, this._lastLevelOffsetY);
            this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
            var pos = 0;

            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode.children.length) {
              var _bg$getComponent3;

              var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode.children.length % this._backgroundLayer.backgrounds.length]);
              const height = (_bg$getComponent3 = bg.getComponent(UITransform)) == null ? void 0 : _bg$getComponent3.contentSize.height;
              bg.setPosition(0, pos, 0);
              pos += height;

              this._backgroundLayer.backgroundsNode.addChild(bg);
            }
          }
        }

        _addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
            error: Error()
          }), LevelLayerUI) : LevelLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        tick(deltaTime) {
          this._backgroundLayerNode.children.forEach(node => {
            node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).tick(deltaTime, this.backgroundLayer.speed);
          });

          this.floorLayers.forEach(layer => {
            var _layer$node;

            (_layer$node = layer.node) == null || _layer$node.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).tick(deltaTime, layer.speed);
          });
          this.skyLayers.forEach(layer => {
            var _layer$node2;

            (_layer$node2 = layer.node) == null || _layer$node2.getComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
              error: Error()
            }), LevelLayerUI) : LevelLayerUI).tick(deltaTime, layer.speed);
          });
        }

      }) || _class5));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0070a8c1917374c8ca16a9b7e1c823a6819eebdb.js.map