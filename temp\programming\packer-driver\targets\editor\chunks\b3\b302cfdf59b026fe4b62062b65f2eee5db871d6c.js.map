{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts"], "names": ["_decorator", "UITransform", "Vec3", "Entity", "BattleLayer", "Tools", "GameIns", "AimCircleScreen", "CircleZoomScreen", "LoftScreen", "AimSingleLineScreen", "ccclass", "property", "AttackPoint", "_bapData", "_owner", "_atkType", "_bulletEntity", "_isBoss", "_bulletNum", "_bulletInterval", "_bulletAttack", "_bulletCount", "_bulletCreateTime", "_bShootStart", "_bAttackOver", "_attackOverCount", "_attackOverTime", "_shootInterval", "_shootPauseTime", "_bIndex", "_bNum", "_targetType", "m_danmuConfig", "_bulletID", "m_screen", "bulletTurn", "_soundAble", "_soundId", "_soundTime", "_soundDuration", "_waveDatas", "_waveTime", "_waved<PERSON>um", "_waveIndexOver", "_waveDelayDuration", "_waveDelay", "initForBoss", "data", "owner", "atkType", "bulletIDs", "length", "waveIds", "node", "setPosition", "x", "y", "reset", "_refreshNextShoot", "initForBossUnit", "initForEnemy", "shootInterval", "bulletNum", "bulletInterval", "bulletAttackRate", "attack", "soundId", "soundDelay", "_setBullet", "bulletID", "bulletNums", "bulletIntervals", "bulletAttackRates", "attackOverDelay", "bulletManager", "getConfig", "getScreenComp", "init", "setBulletState", "shoot", "toFire", "updateGameLogic", "deltaTime", "isAttackOver", "_updateShoot", "_updateCreateEnemys", "sound<PERSON>ey", "setAttackOver", "i", "waveData", "arrC<PERSON>ain", "groupInterval", "enemyNum", "enemyInterval", "worldPos", "getComponent", "convertToWorldSpaceAR", "ZERO", "enemy", "enemyID", "localPos", "me", "enemyPlane<PERSON><PERSON>er", "convertToNodeSpaceAR", "enemyManager", "addPlane", "initTrack", "trackGroups", "liveParam", "startBattle", "push", "bustyle", "isOver", "getOwnEntity", "getAtkAnims", "atkAnim", "getAtkUnitId", "atkUnitId", "getAttackPointAngle"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAyCC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACxDC,MAAAA,M;;AACAC,MAAAA,W;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,e;;AACEC,MAAAA,gB,iBAAAA,gB;;AACFC,MAAAA,U;;AACAC,MAAAA,mB;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAGTa,W,WADpBF,OAAO,CAAC,aAAD,C,gBAAR,MACqBE,WADrB;AAAA;AAAA,4BACgD;AAAA;AAAA;AAAA,eAC5CC,QAD4C,GAC5B,IAD4B;AAAA,eAE5CC,MAF4C,GAE9B,IAF8B;AAAA,eAG5CC,QAH4C,GAGzB,CAHyB;AAAA,eAI5CC,aAJ4C,GAIvB,IAJuB;AAAA,eAK5CC,OAL4C,GAKzB,KALyB;AAAA,eAM5CC,UAN4C,GAMvB,CANuB;AAAA,eAO5CC,eAP4C,GAOlB,CAPkB;AAAA,eAQ5CC,aAR4C,GAQpB,CARoB;AAAA,eAS5CC,YAT4C,GASrB,CATqB;AAAA,eAU5CC,iBAV4C,GAUhB,CAVgB;AAAA,eAW5CC,YAX4C,GAWpB,KAXoB;AAAA,eAY5CC,YAZ4C,GAYpB,KAZoB;AAAA,eAa5CC,gBAb4C,GAajB,CAbiB;AAAA,eAc5CC,eAd4C,GAclB,CAdkB;AAAA,eAe5CC,cAf4C,GAenB,CAfmB;AAAA,eAgB5CC,eAhB4C,GAgBlB,CAhBkB;AAAA,eAiB5CC,OAjB4C,GAiB1B,CAjB0B;AAAA,eAkB5CC,KAlB4C,GAkB5B,CAlB4B;AAAA,eAmB5CC,WAnB4C,GAmBtB,CAnBsB;AAAA,eAoB5CC,aApB4C,GAoBvB,IApBuB;AAAA,eAqB5CC,SArB4C,GAqBxB,CArBwB;AAAA,eAsB5CC,QAtB4C,GAsB5B,IAtB4B;AAAA,eAuB5CC,UAvB4C,GAuBtB,KAvBsB;AAAA,eAwB5CC,UAxB4C,GAwBtB,KAxBsB;AAAA,eAyB5CC,QAzB4C,GAyBzB,CAzByB;AAAA,eA0B5CC,UA1B4C,GA0BvB,CA1BuB;AAAA,eA2B5CC,cA3B4C,GA2BnB,CA3BmB;AAAA,eA4B5CC,UA5B4C,GA4BxB,EA5BwB;AAAA,eA6B5CC,SA7B4C,GA6BtB,EA7BsB;AAAA,eA8B5CC,SA9B4C,GA8BtB,EA9BsB;AAAA,eA+B5CC,cA/B4C,GA+BjB,EA/BiB;AAAA,eAgC5CC,kBAhC4C,GAgCf,CAhCe;AAAA,eAiC5CC,UAjC4C,GAiCvB,CAjCuB;AAAA;;AAmC5CC,QAAAA,WAAW,CAACC,IAAD,EAAYC,KAAZ,EAAwB;AAC/B,eAAKjB,WAAL,GAAmB,CAAnB;AACA,eAAKlB,QAAL,GAAgBkC,IAAhB;AACA,eAAKjC,MAAL,GAAckC,KAAd;AACA,eAAK/B,OAAL,GAAe,IAAf;AACA,eAAKD,aAAL,GAAqBgC,KAArB;AACA,eAAKb,UAAL,GAAkB,KAAlB;AACA,eAAKN,OAAL,GAAe,CAAf;AACA,eAAKd,QAAL,GAAgB,KAAKF,QAAL,CAAcoC,OAA9B;;AAEA,kBAAQ,KAAKlC,QAAb;AACI,iBAAK,CAAL;AACI,mBAAKe,KAAL,GAAaiB,IAAI,CAACG,SAAL,CAAeC,MAA5B;AACA;;AACJ,iBAAK,CAAL;AACI,mBAAKrB,KAAL,GAAaiB,IAAI,CAACK,OAAL,CAAaD,MAA1B;AACA;AANR;;AASA,eAAKE,IAAL,CAAUC,WAAV,CAAsBP,IAAI,CAACQ,CAA3B,EAA8BR,IAAI,CAACS,CAAnC;AACA,eAAKC,KAAL;;AACA,eAAKC,iBAAL;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACZ,IAAD,EAAOC,KAAP,EAAc;AACzB,eAAKjB,WAAL,GAAmB,CAAnB,CADyB,CACH;;AACtB,eAAKlB,QAAL,GAAgBkC,IAAhB,CAFyB,CAEH;;AACtB,eAAKjC,MAAL,GAAckC,KAAd,CAHyB,CAGJ;;AACrB,eAAK/B,OAAL,GAAe,IAAf,CAJyB,CAIJ;;AACrB,eAAKkB,UAAL,GAAkB,KAAlB,CALyB,CAKA;;AACzB,eAAKN,OAAL,GAAe,CAAf,CANyB,CAMP;;AAClB,eAAKd,QAAL,GAAgB,KAAKF,QAAL,CAAcoC,OAA9B,CAPyB,CAOc;AAEvC;;AACA,kBAAQ,KAAKlC,QAAb;AACI,iBAAK,CAAL;AAAQ;AACJ,mBAAKe,KAAL,GAAaiB,IAAI,CAACG,SAAL,CAAeC,MAA5B;AACA;;AACJ,iBAAK,CAAL;AAAQ;AACJ,mBAAKrB,KAAL,GAAaiB,IAAI,CAACK,OAAL,CAAaD,MAA1B;AACA;AANR,WAVyB,CAmBzB;;;AACA,eAAKE,IAAL,CAAUC,WAAV,CAAsBP,IAAI,CAACQ,CAA3B,EAA8BR,IAAI,CAACS,CAAnC,EApByB,CAsBzB;;AACA,eAAKC,KAAL;;AACA,eAAKC,iBAAL;AACH;;AAEDE,QAAAA,YAAY,CAACb,IAAD,EAAYC,KAAZ,EAAwB;AAChC,eAAKS,KAAL;AACA,eAAKxC,OAAL,GAAe,KAAf;AACA,eAAKc,WAAL,GAAmB,CAAnB;AACA,eAAKjB,MAAL,GAAckC,KAAd;AACA,eAAKhC,aAAL,GAAqBgC,KAArB;AACA,eAAKnB,OAAL,GAAe,CAAf;AACA,eAAKC,KAAL,GAAa,CAAb;AACA,eAAKf,QAAL,GAAgB,CAAhB;AACA,eAAKa,eAAL,GAAuB,CAAvB;AACA,eAAKD,cAAL,GAAsBoB,IAAI,CAACc,aAA3B;AACA,eAAK3C,UAAL,GAAkB6B,IAAI,CAACe,SAAvB;AACA,eAAK3C,eAAL,GAAuB4B,IAAI,CAACgB,cAA5B;AACA,eAAK3C,aAAL,GAAqB2B,IAAI,CAACiB,gBAAL,GAAwB,KAAKlD,MAAL,CAAYmD,MAAzD;AACA,eAAK5B,QAAL,GAAgBU,IAAI,CAACmB,OAArB;AACA,eAAK3B,cAAL,GAAsBQ,IAAI,CAACoB,UAA3B;AACA,eAAK/B,UAAL,GAAkB,KAAKC,QAAL,GAAgB,CAAlC;AACA,eAAKgB,IAAL,CAAUC,WAAV,CAAsBP,IAAI,CAACQ,CAA3B,EAA8BR,IAAI,CAACS,CAAnC;;AACA,eAAKY,UAAL,CAAgBrB,IAAI,CAACsB,QAArB;AACH;;AAEDZ,QAAAA,KAAK,GAAG;AACJ,eAAK7B,eAAL,GAAuB,CAAvB;AACA,eAAKP,YAAL,GAAoB,CAApB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKE,YAAL,GAAoB,KAApB;AACA,eAAKW,UAAL,GAAkB,KAAlB;AACA,eAAKV,gBAAL,GAAwB,CAAxB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKU,UAAL,GAAkB,KAAlB;AACA,eAAKC,QAAL,GAAgB,CAAhB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACH;;AAEDoB,QAAAA,iBAAiB,GAAG;AAChB,eAAK9B,eAAL,GAAuB,CAAvB;;AAEA,kBAAQ,KAAKb,QAAb;AACI,iBAAK,CAAL;AACI,mBAAKY,cAAL,GAAsB,KAAKd,QAAL,CAAcgD,aAAd,CAA4B,KAAKhC,OAAjC,CAAtB;AACA,mBAAKX,UAAL,GAAkB,KAAKL,QAAL,CAAcyD,UAAd,CAAyB,KAAKzC,OAA9B,CAAlB;AACA,mBAAKV,eAAL,GAAuB,KAAKN,QAAL,CAAc0D,eAAd,CAA8B,KAAK1C,OAAnC,CAAvB;AACA,mBAAKT,aAAL,GAAqB,KAAKP,QAAL,CAAc2D,iBAAd,CAAgC,KAAK3C,OAArC,IAAgD,KAAKf,MAAL,CAAYmD,MAAjF;AACA,mBAAKvC,eAAL,GAAuB,KAAKb,QAAL,CAAc4D,eAAd,CAA8B,KAAK5C,OAAnC,CAAvB;;AACA,mBAAKuC,UAAL,CAAgB,KAAKvD,QAAL,CAAcqC,SAAd,CAAwB,KAAKrB,OAA7B,CAAhB;;AACA,mBAAKP,iBAAL,GAAyB,KAAKH,eAA9B;AACA;;AAEJ,iBAAK,CAAL;AACI;AACA;AACA;AACA;AACA;AACA;AACA;AAlBR;AAoBH;;AAEDiD,QAAAA,UAAU,CAACC,QAAD,EAAmB;AACzB,eAAKpC,SAAL,GAAiBoC,QAAjB;AACA,eAAKrC,aAAL,GAAqB;AAAA;AAAA,kCAAQ0C,aAAR,CAAsBC,SAAtB,CAAgCN,QAAhC,CAArB;AACA,eAAKnC,QAAL,GAAgB,KAAK0C,aAAL,EAAhB;AACA,eAAK1C,QAAL,CAAc2C,IAAd,CAAmB,IAAnB;AACA,eAAK3C,QAAL,CAAc4C,cAAd,CAA6B;AAAEb,YAAAA,MAAM,EAAE,KAAK7C;AAAf,WAA7B,EAA6D,KAAKJ,aAAlE;AACH;;AAED+D,QAAAA,KAAK,GAAG;AACJ,eAAK7C,QAAL,CAAc8C,MAAd,CAAqB,KAAK3D,YAA1B;AACH;;AAEoB,cAAf4D,eAAe,CAACC,SAAD,EAAoB;AACrC,cAAI,CAAC,KAAKC,YAAL,EAAL,EAA0B;AACtB,oBAAQ,KAAKpE,QAAb;AACI,mBAAK,CAAL;AACI,qBAAKqE,YAAL,CAAkBF,SAAlB;;AACA;;AACJ,mBAAK,CAAL;AACI,sBAAM,KAAKG,mBAAL,CAAyBH,SAAzB,CAAN;AACA;AANR;AAQH;AACJ;;AAEDE,QAAAA,YAAY,CAACF,SAAD,EAAoB;AAC5B,eAAK3D,YAAL,GAAoB,KAApB;AACA,eAAKK,eAAL,IAAwBsD,SAAxB;;AAEA,cAAI,KAAKtD,eAAL,IAAwB,KAAKD,cAAjC,EAAiD;AAC7C,gBAAI,KAAKN,YAAL,KAAsB,CAA1B,EAA6B;AACzB,mBAAKE,YAAL,GAAoB,IAApB;AACH;;AAED,gBAAI,KAAKa,UAAT,EAAqB;AACjB,mBAAKE,UAAL,IAAmB4C,SAAnB;;AACA,kBAAI,KAAK5C,UAAL,IAAmB,KAAKC,cAA5B,EAA4C;AACxC,sBAAM+C,QAAQ,GAAI,IAAG,KAAKjD,QAAS,EAAnC,CADwC,CAExC;AACA;AACA;;AACA,qBAAKD,UAAL,GAAkB,KAAlB;AACH;AACJ;;AAED,gBAAI,KAAKf,YAAL,GAAoB,KAAKH,UAA7B,EAAyC;AACrC,mBAAKI,iBAAL,IAA0B4D,SAA1B;;AACA,kBAAI,KAAK5D,iBAAL,IAA0B,KAAKH,eAAnC,EAAoD;AAChD,qBAAK4D,KAAL;AACA,qBAAKzD,iBAAL,IAA0B,KAAKH,eAA/B;AACA,qBAAKE,YAAL;AACH;AACJ,aAPD,MAOO;AACH,mBAAKI,gBAAL,IAAyByD,SAAzB;;AACA,kBAAI,KAAKzD,gBAAL,IAAyB,KAAKC,eAAlC,EAAmD;AAC/C,qBAAKG,OAAL;;AACA,oBAAI,KAAKA,OAAL,IAAgB,KAAKC,KAAzB,EAAgC;AAC5B,uBAAKyD,aAAL,CAAmB,IAAnB;AACH,iBAFD,MAEO;AACH,uBAAK9B,KAAL;;AACA,uBAAKC,iBAAL;AACH;AACJ;AACJ;AACJ;AACJ;;AAEwB,cAAnB2B,mBAAmB,CAACH,SAAD,EAAoB;AACzC,eAAKtD,eAAL,IAAwBsD,SAAxB;;AAEA,eAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhD,UAAL,CAAgBW,MAApC,EAA4CqC,CAAC,EAA7C,EAAiD;AAC7C,kBAAMC,QAAQ,GAAG,KAAKjD,UAAL,CAAgBgD,CAAhB,CAAjB;;AACA,gBAAI,CAAC;AAAA;AAAA,gCAAME,UAAN,CAAiB,KAAK/C,cAAtB,EAAsC6C,CAAtC,CAAD,IAA6C,KAAK5D,eAAL,IAAwB6D,QAAQ,CAACE,aAAlF,EAAiG;AAC7F,mBAAKlD,SAAL,CAAe+C,CAAf,KAAqBN,SAArB;;AAEA,qBAAO,KAAKxC,SAAL,CAAe8C,CAAf,IAAoBC,QAAQ,CAACG,QAA7B,IAAyC,KAAKnD,SAAL,CAAe+C,CAAf,KAAqBC,QAAQ,CAACI,aAA9E,EAA6F;AACzF,qBAAKpD,SAAL,CAAe+C,CAAf,IAAoB,CAApB;AAEA,sBAAMM,QAAQ,GAAG,KAAKzC,IAAL,CAAU0C,YAAV,CAAuB/F,WAAvB,EAAoCgG,qBAApC,CAA0D/F,IAAI,CAACgG,IAA/D,CAAjB;AACA,oBAAIC,KAAJ;;AAEA,oBAAIT,QAAQ,CAACU,OAAT,IAAoB,KAAxB,EAA+B,CAC3B;AACA;AACA;AACA;AACA;AACH,iBAND,MAMO;AACH,wBAAMC,QAAQ,GAAG;AAAA;AAAA,kDAAYC,EAAZ,CAAeC,eAAf,CAA+BP,YAA/B,CAA4C/F,WAA5C,EAAyDuG,oBAAzD,CAA8ET,QAA9E,CAAjB;AACAI,kBAAAA,KAAK,GAAG,MAAM;AAAA;AAAA,0CAAQM,YAAR,CAAqBC,QAArB,CAA8BhB,QAAQ,CAACU,OAAvC,CAAd;;AACA,sBAAID,KAAJ,EAAW;AACPA,oBAAAA,KAAK,CAACQ,SAAN,CAAgBjB,QAAQ,CAACkB,WAAzB,EAAsClB,QAAQ,CAACmB,SAA/C,EAA0DR,QAAQ,CAAC7C,CAAnE,EAAsE6C,QAAQ,CAAC5C,CAA/E;AACA0C,oBAAAA,KAAK,CAACW,WAAN;AACH;AACJ;;AAED,qBAAKnE,SAAL,CAAe8C,CAAf;AACH;;AAED,kBAAI,KAAK9C,SAAL,CAAe8C,CAAf,MAAsBC,QAAQ,CAACG,QAAnC,EAA6C;AACzC,qBAAKjD,cAAL,CAAoBmE,IAApB,CAAyBtB,CAAzB;AACH;AACJ;AACJ;;AAED,cAAI,KAAK7C,cAAL,CAAoBQ,MAApB,IAA8B,KAAKX,UAAL,CAAgBW,MAAlD,EAA0D;AACtD,gBAAI,KAAKtB,OAAL,GAAe,KAAKC,KAAxB,EAA+B;AAC3B,mBAAKD,OAAL;;AACA,kBAAI,KAAKA,OAAL,GAAe,KAAKC,KAAxB,EAA+B;AAC3B,qBAAK4B,iBAAL;AACH;AACJ,aALD,MAKO;AACH,mBAAKb,UAAL,IAAmBqC,SAAnB;;AACA,kBAAI,KAAKrC,UAAL,IAAmB,KAAKD,kBAA5B,EAAgD;AAC5C,qBAAK2C,aAAL,CAAmB,IAAnB;AACH;AACJ;AACJ;AACJ;;AAEDX,QAAAA,aAAa,GAAG;AACZ,kBAAQ,KAAK5C,aAAL,CAAmB+E,OAA3B;AACI,iBAAK,CAAL;AACI,qBAAO;AAAA;AAAA,8DAAwB,KAAK9E,SAA7B,EAAwC,IAAxC,CAAP;AAAsD;;AAC1D,iBAAK,CAAL;AACI,qBAAO;AAAA;AAAA,4CAAe,KAAKA,SAApB,EAA+B,IAA/B,CAAP;AAA6C;;AACjD,iBAAK,EAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACA,iBAAK,EAAL;AACI,qBAAO;AAAA;AAAA,sDAAoB,KAAKA,SAAzB,EAAoC,IAApC,CAAP;AAAkD;;AACtD,iBAAK,EAAL;AACI,qBAAO;AAAA;AAAA,wDAAqB,KAAKA,SAA1B,EAAqC,IAArC,CAAP;AAAmD;;AACvD;AACI,qBAAO,IAAP;AAAa;AAbrB;AAeH;;AAEDsD,QAAAA,aAAa,CAACyB,MAAD,EAAkB;AAC3B,eAAKxF,YAAL,GAAoBwF,MAApB;AACH;;AAED7B,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAK3D,YAAZ;AACH;;AAEDyF,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAKnG,MAAZ;AACH;;AAEDoG,QAAAA,WAAW,GAAG;AACV,iBAAO,KAAKrG,QAAL,CAAcsG,OAArB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAKvG,QAAL,CAAcwG,SAArB;AACH;;AAEDC,QAAAA,mBAAmB,GAAG,CAClB;AACA;AACA;AACH;;AAxT2C,O", "sourcesContent": ["import { _decorator, Component, Node, Vec2, misc, UITransform, Vec3 } from 'cc';\r\nimport Entity from './Entity';\r\nimport BattleLayer from '../layer/BattleLayer';\r\nimport { Tools } from '../../utils/Tools';\r\nimport { GameIns } from '../../GameIns';\r\nimport AimCircleScreen from '../bulletDanmu/AimCircleScreen';\r\nimport { CircleZoomScreen } from '../bulletDanmu/CircleZoomScreen';\r\nimport LoftScreen from '../bulletDanmu/LoftScreen';\r\nimport AimSingleLineScreen from '../bulletDanmu/AimSingleLineScreen';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('AttackPoint')\r\nexport default class AttackPoint extends Entity {\r\n    _bapData: any = null;\r\n    _owner: any = null;\r\n    _atkType: number = 0;\r\n    _bulletEntity: any = null;\r\n    _isBoss: boolean = false;\r\n    _bulletNum: number = 0;\r\n    _bulletInterval: number = 0;\r\n    _bulletAttack: number = 0;\r\n    _bulletCount: number = 0;\r\n    _bulletCreateTime: number = 0;\r\n    _bShootStart: boolean = false;\r\n    _bAttackOver: boolean = false;\r\n    _attackOverCount: number = 0;\r\n    _attackOverTime: number = 0;\r\n    _shootInterval: number = 0;\r\n    _shootPauseTime: number = 0;\r\n    _bIndex: number = 0;\r\n    _bNum: number = 0;\r\n    _targetType: number = 0;\r\n    m_danmuConfig: any = null;\r\n    _bulletID: number = 0;\r\n    m_screen: any = null;\r\n    bulletTurn: boolean = false;\r\n    _soundAble: boolean = false;\r\n    _soundId: number = 0;\r\n    _soundTime: number = 0;\r\n    _soundDuration: number = 0;\r\n    _waveDatas: any[] = [];\r\n    _waveTime: number[] = [];\r\n    _wavedNum: number[] = [];\r\n    _waveIndexOver: number[] = [];\r\n    _waveDelayDuration: number = 0;\r\n    _waveDelay: number = 0;\r\n\r\n    initForBoss(data: any, owner: any) {\r\n        this._targetType = 1;\r\n        this._bapData = data;\r\n        this._owner = owner;\r\n        this._isBoss = true;\r\n        this._bulletEntity = owner;\r\n        this.bulletTurn = false;\r\n        this._bIndex = 0;\r\n        this._atkType = this._bapData.atkType;\r\n\r\n        switch (this._atkType) {\r\n            case 0:\r\n                this._bNum = data.bulletIDs.length;\r\n                break;\r\n            case 1:\r\n                this._bNum = data.waveIds.length;\r\n                break;\r\n        }\r\n\r\n        this.node.setPosition(data.x, data.y);\r\n        this.reset();\r\n        this._refreshNextShoot();\r\n    }\r\n\r\n    /**\r\n     * 初始化 Boss 单位的攻击点\r\n     * @param {Object} data 攻击点数据\r\n     * @param {Object} owner 拥有者（Boss 实体）\r\n     */\r\n    initForBossUnit(data, owner) {\r\n        this._targetType = 1; // 设置目标类型为 Boss\r\n        this._bapData = data; // 存储攻击点数据\r\n        this._owner = owner; // 设置拥有者\r\n        this._isBoss = true; // 标记为 Boss\r\n        this.bulletTurn = false; // 子弹是否转向\r\n        this._bIndex = 0; // 当前攻击序列索引\r\n        this._atkType = this._bapData.atkType; // 攻击类型\r\n\r\n        // 根据攻击类型设置攻击序列数量\r\n        switch (this._atkType) {\r\n            case 0: // 子弹攻击\r\n                this._bNum = data.bulletIDs.length;\r\n                break;\r\n            case 1: // 波次攻击\r\n                this._bNum = data.waveIds.length;\r\n                break;\r\n        }\r\n\r\n        // 设置攻击点的位置\r\n        this.node.setPosition(data.x, data.y);\r\n\r\n        // 重置状态并刷新下一次攻击\r\n        this.reset();\r\n        this._refreshNextShoot();\r\n    }\r\n\r\n    initForEnemy(data: any, owner: any) {\r\n        this.reset();\r\n        this._isBoss = false;\r\n        this._targetType = 0;\r\n        this._owner = owner;\r\n        this._bulletEntity = owner;\r\n        this._bIndex = 0;\r\n        this._bNum = 1;\r\n        this._atkType = 0;\r\n        this._shootPauseTime = 0;\r\n        this._shootInterval = data.shootInterval;\r\n        this._bulletNum = data.bulletNum;\r\n        this._bulletInterval = data.bulletInterval;\r\n        this._bulletAttack = data.bulletAttackRate * this._owner.attack;\r\n        this._soundId = data.soundId;\r\n        this._soundDuration = data.soundDelay;\r\n        this._soundAble = this._soundId > 0;\r\n        this.node.setPosition(data.x, data.y);\r\n        this._setBullet(data.bulletID);\r\n    }\r\n\r\n    reset() {\r\n        this._shootPauseTime = 0;\r\n        this._bulletCount = 0;\r\n        this._bulletCreateTime = 0;\r\n        this._bAttackOver = false;\r\n        this.bulletTurn = false;\r\n        this._attackOverCount = 0;\r\n        this._attackOverTime = 0;\r\n        this._soundAble = false;\r\n        this._soundId = 0;\r\n        this._soundTime = 0;\r\n    }\r\n\r\n    _refreshNextShoot() {\r\n        this._shootPauseTime = 0;\r\n\r\n        switch (this._atkType) {\r\n            case 0:\r\n                this._shootInterval = this._bapData.shootInterval[this._bIndex];\r\n                this._bulletNum = this._bapData.bulletNums[this._bIndex];\r\n                this._bulletInterval = this._bapData.bulletIntervals[this._bIndex];\r\n                this._bulletAttack = this._bapData.bulletAttackRates[this._bIndex] * this._owner.attack;\r\n                this._attackOverTime = this._bapData.attackOverDelay[this._bIndex];\r\n                this._setBullet(this._bapData.bulletIDs[this._bIndex]);\r\n                this._bulletCreateTime = this._bulletInterval;\r\n                break;\r\n\r\n            case 1:\r\n                // this._waveDatas = WaveManager.getNorWaveDatas(this._bapData.waveIds[this._bIndex]);\r\n                // this._waveTime = Array(this._waveDatas.length).fill(0);\r\n                // this._wavedNum = Array(this._waveDatas.length).fill(0);\r\n                // this._waveIndexOver = [];\r\n                // this._waveDelayDuration = this._bapData.attackOverDelay[0];\r\n                // this._waveDelay = 0;\r\n                break;\r\n        }\r\n    }\r\n\r\n    _setBullet(bulletID: number) {\r\n        this._bulletID = bulletID;\r\n        this.m_danmuConfig = GameIns.bulletManager.getConfig(bulletID);\r\n        this.m_screen = this.getScreenComp();\r\n        this.m_screen.init(this);\r\n        this.m_screen.setBulletState({ attack: this._bulletAttack }, this._bulletEntity);\r\n    }\r\n\r\n    shoot() {\r\n        this.m_screen.toFire(this._bulletCount);\r\n    }\r\n\r\n    async updateGameLogic(deltaTime: number) {\r\n        if (!this.isAttackOver()) {\r\n            switch (this._atkType) {\r\n                case 0:\r\n                    this._updateShoot(deltaTime);\r\n                    break;\r\n                case 1:\r\n                    await this._updateCreateEnemys(deltaTime);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    _updateShoot(deltaTime: number) {\r\n        this._bShootStart = false;\r\n        this._shootPauseTime += deltaTime;\r\n\r\n        if (this._shootPauseTime >= this._shootInterval) {\r\n            if (this._bulletCount === 0) {\r\n                this._bShootStart = true;\r\n            }\r\n\r\n            if (this._soundAble) {\r\n                this._soundTime += deltaTime;\r\n                if (this._soundTime >= this._soundDuration) {\r\n                    const soundKey = `e${this._soundId}`;\r\n                    // if (GameIns.enemyManager.getSoundAble(soundKey)) {\r\n                    //     FrameWork.audioManager.playEffect(soundKey);\r\n                    // }\r\n                    this._soundAble = false;\r\n                }\r\n            }\r\n\r\n            if (this._bulletCount < this._bulletNum) {\r\n                this._bulletCreateTime += deltaTime;\r\n                if (this._bulletCreateTime >= this._bulletInterval) {\r\n                    this.shoot();\r\n                    this._bulletCreateTime -= this._bulletInterval;\r\n                    this._bulletCount++;\r\n                }\r\n            } else {\r\n                this._attackOverCount += deltaTime;\r\n                if (this._attackOverCount >= this._attackOverTime) {\r\n                    this._bIndex++;\r\n                    if (this._bIndex >= this._bNum) {\r\n                        this.setAttackOver(true);\r\n                    } else {\r\n                        this.reset();\r\n                        this._refreshNextShoot();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    async _updateCreateEnemys(deltaTime: number) {\r\n        this._shootPauseTime += deltaTime;\r\n\r\n        for (let i = 0; i < this._waveDatas.length; i++) {\r\n            const waveData = this._waveDatas[i];\r\n            if (!Tools.arrContain(this._waveIndexOver, i) && this._shootPauseTime >= waveData.groupInterval) {\r\n                this._waveTime[i] += deltaTime;\r\n\r\n                while (this._wavedNum[i] < waveData.enemyNum && this._waveTime[i] >= waveData.enemyInterval) {\r\n                    this._waveTime[i] = 0;\r\n\r\n                    const worldPos = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                    let enemy;\r\n\r\n                    if (waveData.enemyID >= 10000) {\r\n                        // const localPos = BattleLayer.me.enemyBulletLayer.convertToNodeSpaceAR(worldPos);\r\n                        // enemy = GameIns.enemyManager.addMissile(waveData.enemyID);\r\n                        // if (enemy) {\r\n                        //     enemy.initTracks(waveData.trackGroups, localPos.x, localPos.y);\r\n                        // }\r\n                    } else {\r\n                        const localPos = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(worldPos);\r\n                        enemy = await GameIns.enemyManager.addPlane(waveData.enemyID);\r\n                        if (enemy) {\r\n                            enemy.initTrack(waveData.trackGroups, waveData.liveParam, localPos.x, localPos.y);\r\n                            enemy.startBattle();\r\n                        }\r\n                    }\r\n\r\n                    this._wavedNum[i]++;\r\n                }\r\n\r\n                if (this._wavedNum[i] === waveData.enemyNum) {\r\n                    this._waveIndexOver.push(i);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._waveIndexOver.length >= this._waveDatas.length) {\r\n            if (this._bIndex < this._bNum) {\r\n                this._bIndex++;\r\n                if (this._bIndex < this._bNum) {\r\n                    this._refreshNextShoot();\r\n                }\r\n            } else {\r\n                this._waveDelay += deltaTime;\r\n                if (this._waveDelay >= this._waveDelayDuration) {\r\n                    this.setAttackOver(true);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    getScreenComp() {\r\n        switch (this.m_danmuConfig.bustyle) {\r\n            case 1:\r\n                return new AimSingleLineScreen(this._bulletID, true); // 瞄准单线弹幕，子弹沿直线发射并瞄准目标\r\n            case 3:\r\n                return new LoftScreen(this._bulletID, true); // 抛物线弹幕，子弹以抛物线轨迹发射\r\n            case 11:\r\n            case 51:\r\n            case 52:\r\n            case 56:\r\n                return new AimCircleScreen(this._bulletID, true); // 瞄准圆形弹幕，子弹以圆形方式发射并瞄准目标\r\n            case 25:\r\n                return new CircleZoomScreen(this._bulletID, true); // 圆形缩放弹幕，子弹以圆形方式发射并逐渐缩放\r\n            default:\r\n                return null; // 未匹配到任何弹幕类型时返回 null\r\n        }\r\n    }\r\n\r\n    setAttackOver(isOver: boolean) {\r\n        this._bAttackOver = isOver;\r\n    }\r\n\r\n    isAttackOver() {\r\n        return this._bAttackOver;\r\n    }\r\n\r\n    getOwnEntity() {\r\n        return this._owner;\r\n    }\r\n\r\n    getAtkAnims() {\r\n        return this._bapData.atkAnim;\r\n    }\r\n\r\n    getAtkUnitId() {\r\n        return this._bapData.atkUnitId;\r\n    }\r\n\r\n    getAttackPointAngle() {\r\n        // return this.bulletTurn\r\n        //     ? misc.radiansToDegrees(Vec2.UP.signAngle(this.node.position))\r\n        //     : 0;\r\n    }\r\n}"]}