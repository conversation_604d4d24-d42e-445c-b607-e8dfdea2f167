System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "cc/env"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, csproto, MyApp, DevLoginUI, BattleUI, BottomUI, TopUI, UIMgr, logError, logInfo, WECHAT, _dec, _class, _crd, ccclass, property, MainUI;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "./AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "./MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLoginUI(extras) {
    _reporterNs.report("DevLoginUI", "./ui/DevLoginUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleUI(extras) {
    _reporterNs.report("BattleUI", "./ui/main/BattleUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "./ui/main/BottomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "./ui/main/TopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "./ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "./Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "./Utils/Logger", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      DevLoginUI = _unresolved_4.DevLoginUI;
    }, function (_unresolved_5) {
      BattleUI = _unresolved_5.BattleUI;
    }, function (_unresolved_6) {
      BottomUI = _unresolved_6.BottomUI;
    }, function (_unresolved_7) {
      TopUI = _unresolved_7.TopUI;
    }, function (_unresolved_8) {
      UIMgr = _unresolved_8.UIMgr;
    }, function (_unresolved_9) {
      logError = _unresolved_9.logError;
      logInfo = _unresolved_9.logInfo;
    }, function (_ccEnv) {
      WECHAT = _ccEnv.WECHAT;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ae5246o93ZERJ5bpRQ+Btv5", "MainUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainUI", MainUI = (_dec = ccclass('MainUI'), _dec(_class = class MainUI extends Component {
        onLoad() {}

        async start() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff);

          if ((_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
            error: Error()
          }), DevLoginUI) : DevLoginUI).needLogin) {
            if (WECHAT) {
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).platformSDK.login((err, info) => {
                if (err) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)("MainUI", `login failed ${err}`);
                  return;
                }

                (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).netMgr.login(info);
              });
            } else {
              await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
                error: Error()
              }), DevLoginUI) : DevLoginUI);
            }
          }

          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
            error: Error()
          }), BattleUI) : BattleUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
            error: Error()
          }), BottomUI) : BottomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
            error: Error()
          }), TopUI) : TopUI); // TODO 为了显示临时背景图，去掉各个UI的背景图 by binbin

          if (false) {
            let disableAllSprite = uiClass => {
              let ui = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).get(uiClass);
              let sprites = ui.node.getComponentsInChildren(Sprite);
              sprites.forEach(sprite => {
                sprite.enabled = false;
              });
            };

            disableAllSprite(_crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
              error: Error()
            }), BattleUI) : BattleUI);
            disableAllSprite(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
              error: Error()
            }), BottomUI) : BottomUI);
            disableAllSprite(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
              error: Error()
            }), TopUI) : TopUI);
          }
        }

        onDestroy() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff);
        }

        onKickOff(msg) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("MainUI", "onKickOff");
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.disableReconnect();

          if (WECHAT) {} else {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && DevLoginUI === void 0 ? (_reportPossibleCrUseOfDevLoginUI({
              error: Error()
            }), DevLoginUI) : DevLoginUI);
          }
        }

        update(deltaTime) {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a62bce7c65bfc0783f10bcc13fb7efd330e855ae.js.map