{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Data/equip/EquipCombine.ts"], "names": ["EquipCombine", "csproto", "MyApp", "DataMgr", "_combineItems", "_combineEquipConfig", "_combineAllInfo", "clear", "num", "fill", "isMainMat", "itemid", "cfgs", "lubanTables", "TbEquip", "getDataList", "filter", "v", "consumeItems", "length", "id", "isFull", "findIndex", "guid", "eq", "add", "item", "pos", "tbEquip", "equipCfgList", "item_id", "count", "isCanCombineWith", "size", "gt", "deleteByGuid", "info", "getByGuid", "deleteByPos", "eqItem", "slice", "index", "getByPos", "getCombineResult", "combine", "newEquip", "materials", "for<PERSON>ach", "push", "netMgr", "sendMessage", "cs", "CS_CMD", "CS_CMD_EQUIP_COMBINE", "equip_combine", "combines", "equip_id", "prepareCombineAll", "newEquipConfigList", "originalBagItemMap", "Map", "bag", "items", "map", "bagItemMap", "Array", "from", "entries", "key", "value", "lackSubMaterialMap", "preliminaryCombines", "equipConfig", "mainMaterial", "subMaterials", "mainBagItem", "get", "maxByMainMaterial", "Math", "floor", "maxBySubMaterials", "hasLackSubMaterial", "subMaterial", "subBagItem", "set", "maxForThisMaterial", "min", "maxCount", "console", "log", "finalCombines", "subMaterialUsage", "currentUsage", "hasSubMaterialConflict", "materialId", "usage", "originalCount", "canCombineAll", "combineInfo", "material", "originalItem", "totalEquips", "combineAll"], "mappings": ";;;uDAMaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANNC,MAAAA,O;;AAGEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;8BAEIH,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACdI,aADc,GACwB,EADxB;AAAA,eAEdC,mBAFc,GAEkB,IAFlB;AAAA,eAGdC,eAHc,GAGqC,EAHrC;AAAA;;AAKtBC,QAAAA,KAAK,CAACC,GAAW,GAAG,CAAf,EAAkB;AACnB,eAAKH,mBAAL,GAA2B,IAA3B;AACA,eAAKD,aAAL,GAAqB,EAArB;;AACA,eAAKA,aAAL,CAAmBK,IAAnB,CAA4C,EAA5C,EAAgD,CAAhD,EAAmDD,GAAnD;AACH;;AAEDE,QAAAA,SAAS,CAACC,MAAD,EAA0B;AAC/B,gBAAMC,IAAI,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,WAA1B,GAAwCC,MAAxC,CAA+CC,CAAC,IAAIA,CAAC,CAACC,YAAF,CAAeC,MAAf,GAAwB,CAAxB,IAA6BF,CAAC,CAACC,YAAF,CAAe,CAAf,EAAkBE,EAAlB,IAAwBT,MAAzG,CAAb;AACA,iBAAOC,IAAI,CAACO,MAAL,GAAc,CAArB;AACH;;AAEDE,QAAAA,MAAM,GAAY;AACd,iBAAO,KAAKjB,aAAL,CAAmBkB,SAAnB,CAA6BL,CAAC,IAAIA,CAAC,CAACM,IAAF,CAAOC,EAAP,CAAU,CAAV,CAAlC,KAAmD,CAAC,CAApD,IAAyD,KAAKpB,aAAL,CAAmBe,MAAnB,GAA4B,CAA5F;AACH;;AAEDM,QAAAA,GAAG,CAACC,IAAD,EAA2BC,GAA3B,EAAkD;AACjD,cAAI,KAAKN,MAAL,EAAJ,EAAmB,OAAO,KAAP;;AACnB,cAAI,CAACM,GAAL,EAAU;AACNA,YAAAA,GAAG,GAAG,KAAKvB,aAAL,CAAmBkB,SAAnB,CAA6BL,CAAC,IAAIA,CAAC,CAACM,IAAF,CAAOC,EAAP,CAAU,CAAV,CAAlC,CAAN;AACH;;AACD,cAAIG,GAAG,GAAG,CAAV,EAAa;AACT,kBAAMC,OAAO,GAAG;AAAA;AAAA,gCAAMf,WAAN,CAAkBC,OAAlC;AACA,kBAAMe,YAAY,GAAGD,OAAO,CAACb,WAAR,GAAsBC,MAAtB,CAA6BC,CAAC;AAAA;;AAAA,qBAAI,qBAAAA,CAAC,CAACC,YAAF,CAAe,CAAf,uCAAmBE,EAAnB,KAAyBM,IAAI,CAACI,OAA9B,IACnD,sBAAAb,CAAC,CAACC,YAAF,CAAe,CAAf,wCAAmBV,GAAnB,KAA0BkB,IAAI,CAACK,KADgB;AAAA,aAA9B,CAArB;;AAEA,gBAAIF,YAAY,CAACV,MAAb,IAAuB,CAA3B,EAA8B;AAC1B,qBAAO,KAAP;AACH;;AACD,iBAAKd,mBAAL,GAA2BwB,YAAY,CAAC,CAAD,CAAvC;AACA,iBAAKtB,KAAL,CAAW,KAAKF,mBAAL,CAAyBa,YAAzB,CAAsCC,MAAjD;AACH,WATD,MASO,IAAI,CAAC,KAAKa,gBAAL,CAAsBN,IAAtB,CAAL,EAAkC;AACrC,mBAAO,KAAP;AACH;;AACD,eAAKtB,aAAL,CAAmBuB,GAAnB,IAA0BD,IAA1B;AACA,iBAAO,IAAP;AACH;;AAEDO,QAAAA,IAAI,GAAW;AACX,iBAAO,KAAK7B,aAAL,CAAmBY,MAAnB,CAA0BC,CAAC,IAAIA,CAAC,CAACM,IAAF,CAAOW,EAAP,CAAU,CAAV,CAA/B,EAA6Cf,MAApD;AACH;;AAEDgB,QAAAA,YAAY,CAACZ,IAAD,EAAa;AACrB,gBAAMa,IAAI,GAAG,KAAKC,SAAL,CAAed,IAAf,CAAb;;AACA,cAAIa,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAACT,GAAL,IAAY,CAAhB,EAAmB;AACf,mBAAKpB,KAAL;AACH,aAFD,MAEO;AACH,mBAAKH,aAAL,CAAmBgC,IAAI,CAACT,GAAxB,IAA+B,EAA/B;AACH;AACJ;AACJ;;AAEDW,QAAAA,WAAW,CAACX,GAAD,EAAc;AACrB,cAAIA,GAAG,IAAI,CAAX,EAAc;AACV,iBAAKpB,KAAL;AACH,WAFD,MAEO;AACH,iBAAKH,aAAL,CAAmBuB,GAAnB,IAA0B,EAA1B;AACH;AACJ;;AAEDK,QAAAA,gBAAgB,CAACO,MAAD,EAAsC;AAClD,cAAI,KAAKlC,mBAAL,IAA4B,IAAhC,EAAsC;AAClC,mBAAO,KAAP;AACH;;AACD,gBAAMa,YAAY,GAAG,KAAKb,mBAAL,CAAyBa,YAAzB,CAAsCsB,KAAtC,CAA4C,CAA5C,CAArB;;AACA,iBAAOtB,YAAY,CAACI,SAAb,CAAuBL,CAAC,IAAIA,CAAC,CAACG,EAAF,IAAQmB,MAAM,CAACT,OAAf,IAA0Bb,CAAC,CAACT,GAAF,IAAS+B,MAAM,CAACR,KAAtE,KAAgF,CAAvF;AACH;;AAEDM,QAAAA,SAAS,CAACd,IAAD,EAA+D;AACpE,gBAAMkB,KAAK,GAAG,KAAKrC,aAAL,CAAmBkB,SAAnB,CAA6BL,CAAC,IAAIA,CAAC,CAACM,IAAF,CAAOC,EAAP,CAAUD,IAAV,CAAlC,CAAd;;AACA,cAAIkB,KAAK,GAAG,CAAZ,EAAe;AACX,mBAAO,IAAP;AACH;;AACD,iBAAO;AAAEd,YAAAA,GAAG,EAAEc,KAAP;AAAcf,YAAAA,IAAI,EAAE,KAAKtB,aAAL,CAAmBqC,KAAnB;AAApB,WAAP;AACH;;AAEDC,QAAAA,QAAQ,CAACf,GAAD,EAAgE;AACpE,gBAAMS,IAAI,GAAG,KAAKhC,aAAL,CAAmBuB,GAAnB,CAAb;;AACA,cAAI,CAACS,IAAD,IAASA,IAAI,CAACb,IAAL,CAAUC,EAAV,CAAa,CAAb,CAAb,EAA8B;AAC1B,mBAAO,IAAP;AACH;;AACD,iBAAO;AAAEG,YAAAA,GAAF;AAAOD,YAAAA,IAAI,EAAEU;AAAb,WAAP;AACH;;AAEDO,QAAAA,gBAAgB,GAAa;AACzB,iBAAO,KAAKtC,mBAAZ;AACH;;AAEDuC,QAAAA,OAAO,GAAG;AACN,gBAAMC,QAAQ,GAAG,KAAKF,gBAAL,EAAjB;;AACA,cAAI,CAACE,QAAL,EAAe;AACX;AACH;;AACD,gBAAMC,SAAS,GAAG,EAAlB;;AACA,eAAK1C,aAAL,CAAmB2C,OAAnB,CAA2B,CAAC9B,CAAD,EAAIwB,KAAJ,KAAc;AACrCK,YAAAA,SAAS,CAACE,IAAV,CAAmD;AAC/C5B,cAAAA,EAAE,EAAEH,CAAC,CAACa,OADyC;AAE/CtB,cAAAA,GAAG,EAAEqC,QAAQ,CAAC3B,YAAT,CAAsBuB,KAAtB,EAA6BjC,GAFa;AAG/Ce,cAAAA,IAAI,EAAEN,CAAC,CAACM;AAHuC,aAAnD;AAKH,WAND;;AAOA;AAAA;AAAA,8BAAM0B,MAAN,CAAaC,WAAb,CAAyB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA3C,EAAiE;AAC7DC,YAAAA,aAAa,EAAE;AACXC,cAAAA,QAAQ,EAAE,CAAC;AACPC,gBAAAA,QAAQ,EAAEX,QAAQ,CAACzB,EADZ;AAEPZ,gBAAAA,GAAG,EAAE,CAFE;AAGPsC,gBAAAA,SAAS,EAAEA;AAHJ,eAAD;AADC;AAD8C,WAAjE;AASH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIW,QAAAA,iBAAiB,GAAG;AAChB;AACA,eAAKnD,eAAL,GAAuB,EAAvB,CAFgB,CAIhB;;AACA,gBAAMsB,OAAO,GAAG;AAAA;AAAA,8BAAMf,WAAN,CAAkBC,OAAlC,CALgB,CAOhB;;AACA,gBAAM4C,kBAAkB,GAAG9B,OAAO,CAACb,WAAR,GAAsBC,MAAtB,CAA6BC,CAAC,IAAIA,CAAC,CAACC,YAAF,CAAeC,MAAf,GAAwB,CAA1D,CAA3B,CARgB,CAUhB;;AACA,gBAAMwC,kBAAkB,GAAG,IAAIC,GAAJ,CACvB;AAAA;AAAA,kCAAQC,GAAR,CAAYC,KAAZ,CAAkBC,GAAlB,CAAsB9C,CAAC,IAAI,CACvBA,CAAC,CAACa,OADqB,EAEvB;AACIV,YAAAA,EAAE,EAAEH,CAAC,CAACa,OADV;AAEIC,YAAAA,KAAK,EAAEd,CAAC,CAACc,KAFb;AAGIR,YAAAA,IAAI,EAAEN,CAAC,CAACM;AAHZ,WAFuB,CAA3B,CADuB,CAA3B,CAXgB,CAsBhB;;AACA,gBAAMyC,UAAU,GAAG,IAAIJ,GAAJ,CACfK,KAAK,CAACC,IAAN,CAAWP,kBAAkB,CAACQ,OAAnB,EAAX,EAAyCJ,GAAzC,CAA6C,CAAC,CAACK,GAAD,EAAMC,KAAN,CAAD,KAAkB,CAC3DD,GAD2D,EAE3D,EAAE,GAAGC;AAAL,WAF2D,CAE9C;AAF8C,WAA/D,CADe,CAAnB,CAvBgB,CA8BhB;;AACA,gBAAMC,kBAAkB,GAAG,IAAIV,GAAJ,EAA3B,CA/BgB,CAiChB;;AACA,gBAAMW,mBAAmB,GAKtB,EALH,CAlCgB,CAyChB;;AACAb,UAAAA,kBAAkB,CAACX,OAAnB,CAA2ByB,WAAW,IAAI;AACtC;AACA,kBAAMC,YAAY,GAAGD,WAAW,CAACtD,YAAZ,CAAyB,CAAzB,CAArB;AACA,kBAAMwD,YAAY,GAAGF,WAAW,CAACtD,YAAZ,CAAyBsB,KAAzB,CAA+B,CAA/B,CAArB,CAHsC,CAKtC;;AACA,kBAAMmC,WAAW,GAAGX,UAAU,CAACY,GAAX,CAAeH,YAAY,CAACrD,EAA5B,CAApB;;AACA,gBAAI,CAACuD,WAAD,IAAgBA,WAAW,CAAC5C,KAAZ,KAAsB,CAA1C,EAA6C;AACzC,qBADyC,CACjC;AACX,aATqC,CAWtC;;;AACA,kBAAM8C,iBAAiB,GAAGC,IAAI,CAACC,KAAL,CAAWJ,WAAW,CAAC5C,KAAZ,GAAoB0C,YAAY,CAACjE,GAA5C,CAA1B;;AACA,gBAAIqE,iBAAiB,KAAK,CAA1B,EAA6B;AACzB,qBADyB,CACjB;AACX,aAfqC,CAiBtC;;;AACA,gBAAIG,iBAAiB,GAAGH,iBAAxB;AACA,gBAAII,kBAAkB,GAAG,KAAzB;;AAEA,iBAAK,MAAMC,WAAX,IAA0BR,YAA1B,EAAwC;AACpC,oBAAMS,UAAU,GAAGnB,UAAU,CAACY,GAAX,CAAeM,WAAW,CAAC9D,EAA3B,CAAnB;;AACA,kBAAI,CAAC+D,UAAL,EAAiB;AACb;AACAb,gBAAAA,kBAAkB,CAACc,GAAnB,CAAuBF,WAAW,CAAC9D,EAAnC,EAAuC8D,WAAW,CAAC1E,GAAnD;AACAyE,gBAAAA,kBAAkB,GAAG,IAArB;AACAD,gBAAAA,iBAAiB,GAAG,CAApB;AACA;AACH;;AAED,oBAAMK,kBAAkB,GAAGP,IAAI,CAACC,KAAL,CAAWI,UAAU,CAACpD,KAAX,GAAmBmD,WAAW,CAAC1E,GAA1C,CAA3B;;AACA,kBAAI6E,kBAAkB,KAAK,CAA3B,EAA8B;AAC1B;AACAf,gBAAAA,kBAAkB,CAACc,GAAnB,CAAuBF,WAAW,CAAC9D,EAAnC,EAAuC8D,WAAW,CAAC1E,GAAnD;AACAyE,gBAAAA,kBAAkB,GAAG,IAArB;AACAD,gBAAAA,iBAAiB,GAAG,CAApB;AACA;AACH;;AAEDA,cAAAA,iBAAiB,GAAGF,IAAI,CAACQ,GAAL,CAASN,iBAAT,EAA4BK,kBAA5B,CAApB;AACH,aAzCqC,CA2CtC;;;AACA,gBAAIJ,kBAAJ,EAAwB;AACpB;AACH,aA9CqC,CAgDtC;;;AACAV,YAAAA,mBAAmB,CAACvB,IAApB,CAAyB;AACrBQ,cAAAA,QAAQ,EAAEgB,WAAW,CAACpD,EADD;AAErBmE,cAAAA,QAAQ,EAAEP,iBAFW;AAGrBP,cAAAA,YAAY,EAAEA,YAHO;AAIrBC,cAAAA,YAAY,EAAEA;AAJO,aAAzB;AAMH,WAvDD,EA1CgB,CAmGhB;;AACA,cAAIJ,kBAAkB,CAACrC,IAAnB,GAA0B,CAA9B,EAAiC;AAC7BuD,YAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ;AACA;AACH,WAvGe,CAyGhB;;;AACA,gBAAMC,aAAa,GAAoC,EAAvD,CA1GgB,CA4GhB;;AACA,gBAAMC,gBAAgB,GAAG,IAAI/B,GAAJ,EAAzB,CA7GgB,CA+GhB;;AACA,eAAK,MAAMhB,OAAX,IAAsB2B,mBAAtB,EAA2C;AACvC;AACA,kBAAMI,WAAW,GAAGX,UAAU,CAACY,GAAX,CAAehC,OAAO,CAAC6B,YAAR,CAAqBrD,EAApC,CAApB;AACAuD,YAAAA,WAAW,CAAC5C,KAAZ,IAAqBa,OAAO,CAAC6B,YAAR,CAAqBjE,GAArB,GAA2BoC,OAAO,CAAC2C,QAAxD,CAHuC,CAKvC;;AACA,iBAAK,MAAML,WAAX,IAA0BtC,OAAO,CAAC8B,YAAlC,EAAgD;AAC5C,oBAAMkB,YAAY,GAAGD,gBAAgB,CAACf,GAAjB,CAAqBM,WAAW,CAAC9D,EAAjC,KAAwC,CAA7D;AACAuE,cAAAA,gBAAgB,CAACP,GAAjB,CAAqBF,WAAW,CAAC9D,EAAjC,EAAqCwE,YAAY,GAAGV,WAAW,CAAC1E,GAAZ,GAAkBoC,OAAO,CAAC2C,QAA9E;AAEA,oBAAMJ,UAAU,GAAGnB,UAAU,CAACY,GAAX,CAAeM,WAAW,CAAC9D,EAA3B,CAAnB;AACA+D,cAAAA,UAAU,CAACpD,KAAX,IAAoBmD,WAAW,CAAC1E,GAAZ,GAAkBoC,OAAO,CAAC2C,QAA9C;AACH,aAZsC,CAcvC;;;AACA,kBAAMzC,SAA+C,GAAG,EAAxD,CAfuC,CAiBvC;;AACAA,YAAAA,SAAS,CAACE,IAAV,CAAe;AACX5B,cAAAA,EAAE,EAAEwB,OAAO,CAAC6B,YAAR,CAAqBrD,EADd;AAEXZ,cAAAA,GAAG,EAAEoC,OAAO,CAAC6B,YAAR,CAAqBjE,GAArB,GAA2BoC,OAAO,CAAC2C,QAF7B;AAGXhE,cAAAA,IAAI,EAAEyC,UAAU,CAACY,GAAX,CAAehC,OAAO,CAAC6B,YAAR,CAAqBrD,EAApC,EAAyCG;AAHpC,aAAf,EAlBuC,CAwBvC;;AACA,iBAAK,MAAM2D,WAAX,IAA0BtC,OAAO,CAAC8B,YAAlC,EAAgD;AAC5C5B,cAAAA,SAAS,CAACE,IAAV,CAAe;AACX5B,gBAAAA,EAAE,EAAE8D,WAAW,CAAC9D,EADL;AAEXZ,gBAAAA,GAAG,EAAE0E,WAAW,CAAC1E,GAAZ,GAAkBoC,OAAO,CAAC2C,QAFpB;AAGXhE,gBAAAA,IAAI,EAAEyC,UAAU,CAACY,GAAX,CAAeM,WAAW,CAAC9D,EAA3B,EAAgCG;AAH3B,eAAf;AAKH;;AAEDmE,YAAAA,aAAa,CAAC1C,IAAd,CAAmB;AACfQ,cAAAA,QAAQ,EAAEZ,OAAO,CAACY,QADH;AAEfhD,cAAAA,GAAG,EAAEoC,OAAO,CAAC2C,QAFE;AAGfzC,cAAAA,SAAS,EAAEA;AAHI,aAAnB;AAKH,WAtJe,CAwJhB;;;AACA,cAAI+C,sBAAsB,GAAG,KAA7B;;AAEA,eAAK,MAAM,CAACC,UAAD,EAAaC,KAAb,CAAX,IAAkCJ,gBAAlC,EAAoD;AAAA;;AAChD,kBAAMK,aAAa,GAAG,0BAAArC,kBAAkB,CAACiB,GAAnB,CAAuBkB,UAAvB,4CAAoC/D,KAApC,KAA6C,CAAnE;;AACA,gBAAIgE,KAAK,GAAGC,aAAZ,EAA2B;AACvBH,cAAAA,sBAAsB,GAAG,IAAzB;AACAL,cAAAA,OAAO,CAACC,GAAR,CAAa,OAAMK,UAAW,UAASC,KAAM,OAAMC,aAAc,EAAjE;AACA;AACH;AACJ,WAlKe,CAoKhB;;;AACA,cAAIH,sBAAJ,EAA4B;AACxBL,YAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ;AACA;AACH,WAxKe,CA0KhB;;;AACA,cAAIQ,aAAa,GAAG,IAApB;;AAEA,eAAK,MAAMC,WAAX,IAA0BR,aAA1B,EAAyC;AACrC,iBAAK,MAAMS,QAAX,IAAuBD,WAAW,CAACpD,SAAnC,EAA8C;AAC1C,oBAAMsD,YAAY,GAAGzC,kBAAkB,CAACiB,GAAnB,CAAuBuB,QAAQ,CAAC/E,EAAhC,CAArB;;AACA,kBAAI,CAACgF,YAAD,IAAiBA,YAAY,CAACrE,KAAb,GAAqBoE,QAAQ,CAAC3F,GAAnD,EAAwD;AACpDyF,gBAAAA,aAAa,GAAG,KAAhB;AACA;AACH;AACJ;;AACD,gBAAI,CAACA,aAAL,EAAoB;AACvB,WAtLe,CAwLhB;;;AACA,cAAI,CAACA,aAAD,IAAkBP,aAAa,CAACvE,MAAd,KAAyB,CAA/C,EAAkD;AAC9CqE,YAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA;AACH,WA5Le,CA8LhB;;;AACA,eAAKnF,eAAL,GAAuBoF,aAAvB,CA/LgB,CAiMhB;;AACA,cAAIW,WAAW,GAAG,CAAlB;AACAX,UAAAA,aAAa,CAAC3C,OAAd,CAAsBH,OAAO,IAAI;AAC7ByD,YAAAA,WAAW,IAAIzD,OAAO,CAACpC,GAAvB;AACAgF,YAAAA,OAAO,CAACC,GAAR,CAAa,SAAQ7C,OAAO,CAACY,QAAS,UAASZ,OAAO,CAACpC,GAAI,GAA3D;AACH,WAHD;AAKAgF,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQY,WAAY,MAAjC;AACH;;AAEDC,QAAAA,UAAU,GAAG;AACT,cAAI,KAAKhG,eAAL,CAAqBa,MAArB,IAA+B,CAAnC,EAAsC;AAClC;AACH;;AACD;AAAA;AAAA,8BAAM8B,MAAN,CAAaC,WAAb,CAAyB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA3C,EAAiE;AAC7DC,YAAAA,aAAa,EAAE;AAAEC,cAAAA,QAAQ,EAAE,KAAKjD;AAAjB;AAD8C,WAAjE;AAGH;;AA9UqB,O", "sourcesContent": ["import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport Long from 'long';\nimport { ResEquip } from '../../AutoGen/Luban/schema';\nimport { MyApp } from '../../MyApp';\nimport { DataMgr } from '../DataManager';\n\nexport class EquipCombine {\n    private _combineItems: csproto.cs.ICSItem[] = []\n    private _combineEquipConfig: ResEquip = null;\n    private _combineAllInfo: csproto.cs.ICSEquipCombineOne[] = [];\n\n    clear(num: number = 3) {\n        this._combineEquipConfig = null;\n        this._combineItems = [];\n        this._combineItems.fill(<csproto.cs.ICSItem>{}, 0, num)\n    }\n\n    isMainMat(itemid: number): boolean {\n        const cfgs = MyApp.lubanTables.TbEquip.getDataList().filter(v => v.consumeItems.length > 0 && v.consumeItems[0].id == itemid)\n        return cfgs.length > 0\n    }\n\n    isFull(): boolean {\n        return this._combineItems.findIndex(v => v.guid.eq(0)) == -1 && this._combineItems.length > 0\n    }\n\n    add(item: csproto.cs.ICSItem, pos?: number): boolean {\n        if (this.isFull()) return false;\n        if (!pos) {\n            pos = this._combineItems.findIndex(v => v.guid.eq(0))\n        }\n        if (pos < 0) {\n            const tbEquip = MyApp.lubanTables.TbEquip\n            const equipCfgList = tbEquip.getDataList().filter(v => v.consumeItems[0]?.id == item.item_id &&\n                v.consumeItems[0]?.num == item.count)\n            if (equipCfgList.length == 0) {\n                return false\n            }\n            this._combineEquipConfig = equipCfgList[0]\n            this.clear(this._combineEquipConfig.consumeItems.length)\n        } else if (!this.isCanCombineWith(item)) {\n            return false;\n        }\n        this._combineItems[pos] = item\n        return true\n    }\n\n    size(): number {\n        return this._combineItems.filter(v => v.guid.gt(0)).length\n    }\n\n    deleteByGuid(guid: Long) {\n        const info = this.getByGuid(guid)\n        if (info) {\n            if (info.pos == 0) {\n                this.clear();\n            } else {\n                this._combineItems[info.pos] = {}\n            }\n        }\n    }\n\n    deleteByPos(pos: number) {\n        if (pos == 0) {\n            this.clear();\n        } else {\n            this._combineItems[pos] = {}\n        }\n    }\n\n    isCanCombineWith(eqItem: csproto.cs.ICSItem): boolean {\n        if (this._combineEquipConfig == null) {\n            return false\n        }\n        const consumeItems = this._combineEquipConfig.consumeItems.slice(1)\n        return consumeItems.findIndex(v => v.id == eqItem.item_id && v.num == eqItem.count) >= 0\n    }\n\n    getByGuid(guid: Long): { pos: number, item: csproto.cs.ICSItem } | null {\n        const index = this._combineItems.findIndex(v => v.guid.eq(guid))\n        if (index < 0) {\n            return null\n        }\n        return { pos: index, item: this._combineItems[index] }\n    }\n\n    getByPos(pos: number): { pos: number, item: csproto.cs.ICSItem } | null {\n        const info = this._combineItems[pos]\n        if (!info || info.guid.eq(0)) {\n            return null\n        }\n        return { pos, item: info }\n    }\n\n    getCombineResult(): ResEquip {\n        return this._combineEquipConfig\n    }\n\n    combine() {\n        const newEquip = this.getCombineResult()\n        if (!newEquip) {\n            return\n        }\n        const materials = []\n        this._combineItems.forEach((v, index) => {\n            materials.push(<csproto.cs.ICSEquipCombineMaterial>{\n                id: v.item_id,\n                num: newEquip.consumeItems[index].num,\n                guid: v.guid\n            })\n        })\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {\n            equip_combine: {\n                combines: [{\n                    equip_id: newEquip.id,\n                    num: 1,\n                    materials: materials\n                }]\n            }\n        })\n    }\n\n    /**\n     * 装备批量合成预处理方法\n     * 功能：检查背包材料是否足够合成多件相同装备，收集合成信息\n     * 规则：\n     * 1. 主材料不存在不记录缺失，只跳过该装备\n     * 2. 副材料不够要记录缺失\n     * 3. 不同主材料的装备共享副材料，如果副材料不够分配，整个批量合成失败\n     */\n    prepareCombineAll() {\n        // 初始化批量合成信息数组\n        this._combineAllInfo = [];\n\n        // 获取装备配置表\n        const tbEquip = MyApp.lubanTables.TbEquip;\n\n        // 筛选需要多个材料的装备（排除单材料装备）\n        const newEquipConfigList = tbEquip.getDataList().filter(v => v.consumeItems.length > 1);\n\n        // 创建背包物品映射（使用深拷贝避免修改原始数据）\n        const originalBagItemMap = new Map(\n            DataMgr.bag.items.map(v => [\n                v.item_id,\n                {\n                    id: v.item_id,\n                    count: v.count,\n                    guid: v.guid\n                }\n            ])\n        );\n\n        // 创建可修改的背包副本用于计算\n        const bagItemMap = new Map(\n            Array.from(originalBagItemMap.entries()).map(([key, value]) => [\n                key,\n                { ...value } // 深拷贝每个物品对象\n            ])\n        );\n\n        // 存储缺少的副材料信息（材料ID => 需要数量）\n        const lackSubMaterialMap = new Map<number, number>();\n\n        // 存储所有可合成的装备信息（第一轮初步计算）\n        const preliminaryCombines = <Array<{\n            equip_id: number;\n            maxCount: number;\n            mainMaterial: { id: number; num: number };\n            subMaterials: Array<{ id: number; num: number }>;\n        }>>[];\n\n        // 第一轮：计算每个装备基于自身材料的最大可合成数量\n        newEquipConfigList.forEach(equipConfig => {\n            // 获取主材料（第一个材料）和副材料\n            const mainMaterial = equipConfig.consumeItems[0];\n            const subMaterials = equipConfig.consumeItems.slice(1);\n\n            // 检查主材料是否存在 - 不存在直接跳过，不记录缺失\n            const mainBagItem = bagItemMap.get(mainMaterial.id);\n            if (!mainBagItem || mainBagItem.count === 0) {\n                return; // 主材料不存在或数量为0，跳过该装备（不记录缺失）\n            }\n\n            // 计算基于主材料最多可以合成多少件\n            const maxByMainMaterial = Math.floor(mainBagItem.count / mainMaterial.num);\n            if (maxByMainMaterial === 0) {\n                return; // 主材料数量不足合成1件，跳过该装备（不记录缺失）\n            }\n\n            // 计算基于副材料最多可以合成多少件\n            let maxBySubMaterials = maxByMainMaterial;\n            let hasLackSubMaterial = false;\n\n            for (const subMaterial of subMaterials) {\n                const subBagItem = bagItemMap.get(subMaterial.id);\n                if (!subBagItem) {\n                    // 副材料不存在，记录缺失\n                    lackSubMaterialMap.set(subMaterial.id, subMaterial.num);\n                    hasLackSubMaterial = true;\n                    maxBySubMaterials = 0;\n                    break;\n                }\n\n                const maxForThisMaterial = Math.floor(subBagItem.count / subMaterial.num);\n                if (maxForThisMaterial === 0) {\n                    // 副材料数量不足，记录缺失\n                    lackSubMaterialMap.set(subMaterial.id, subMaterial.num);\n                    hasLackSubMaterial = true;\n                    maxBySubMaterials = 0;\n                    break;\n                }\n\n                maxBySubMaterials = Math.min(maxBySubMaterials, maxForThisMaterial);\n            }\n\n            // 如果有副材料缺失，跳过该装备\n            if (hasLackSubMaterial) {\n                return;\n            }\n\n            // 添加到初步合成列表\n            preliminaryCombines.push({\n                equip_id: equipConfig.id,\n                maxCount: maxBySubMaterials,\n                mainMaterial: mainMaterial,\n                subMaterials: subMaterials\n            });\n        });\n\n        // 如果有副材料缺失，整个批量合成失败\n        if (lackSubMaterialMap.size > 0) {\n            console.log('副材料不足，批量合成失败');\n            return;\n        }\n\n        // 第二轮：处理副材料在不同装备间的分配冲突\n        const finalCombines = <csproto.cs.ICSEquipCombineOne[]>[];\n\n        // 创建一个副材料使用量统计表\n        const subMaterialUsage = new Map<number, number>();\n\n        // 初步确定每个装备的合成数量（先按最大可能计算）\n        for (const combine of preliminaryCombines) {\n            // 虚拟扣除材料\n            const mainBagItem = bagItemMap.get(combine.mainMaterial.id)!;\n            mainBagItem.count -= combine.mainMaterial.num * combine.maxCount;\n\n            // 统计副材料使用量\n            for (const subMaterial of combine.subMaterials) {\n                const currentUsage = subMaterialUsage.get(subMaterial.id) || 0;\n                subMaterialUsage.set(subMaterial.id, currentUsage + subMaterial.num * combine.maxCount);\n\n                const subBagItem = bagItemMap.get(subMaterial.id)!;\n                subBagItem.count -= subMaterial.num * combine.maxCount;\n            }\n\n            // 收集最终合成信息\n            const materials: csproto.cs.ICSEquipCombineMaterial[] = [];\n\n            // 主材料信息\n            materials.push({\n                id: combine.mainMaterial.id,\n                num: combine.mainMaterial.num * combine.maxCount,\n                guid: bagItemMap.get(combine.mainMaterial.id)!.guid\n            });\n\n            // 副材料信息\n            for (const subMaterial of combine.subMaterials) {\n                materials.push({\n                    id: subMaterial.id,\n                    num: subMaterial.num * combine.maxCount,\n                    guid: bagItemMap.get(subMaterial.id)!.guid\n                });\n            }\n\n            finalCombines.push({\n                equip_id: combine.equip_id,\n                num: combine.maxCount,\n                materials: materials\n            });\n        }\n\n        // 检查副材料是否真的足够（考虑不同装备间的共享）\n        let hasSubMaterialConflict = false;\n\n        for (const [materialId, usage] of subMaterialUsage) {\n            const originalCount = originalBagItemMap.get(materialId)?.count || 0;\n            if (usage > originalCount) {\n                hasSubMaterialConflict = true;\n                console.log(`副材料 ${materialId} 不足: 需要${usage}, 仅有${originalCount}`);\n                break;\n            }\n        }\n\n        // 如果有副材料分配冲突，整个批量合成失败\n        if (hasSubMaterialConflict) {\n            console.log('副材料分配冲突，批量合成失败');\n            return;\n        }\n\n        // 最终检查：确保所有材料都足够\n        let canCombineAll = true;\n\n        for (const combineInfo of finalCombines) {\n            for (const material of combineInfo.materials) {\n                const originalItem = originalBagItemMap.get(material.id);\n                if (!originalItem || originalItem.count < material.num) {\n                    canCombineAll = false;\n                    break;\n                }\n            }\n            if (!canCombineAll) break;\n        }\n\n        // 如果检查失败或没有可合成的装备，直接返回\n        if (!canCombineAll || finalCombines.length === 0) {\n            console.log('材料检查失败或无可合成装备');\n            return;\n        }\n\n        // 设置批量合成信息，供后续合成操作使用\n        this._combineAllInfo = finalCombines;\n\n        // 输出合成信息\n        let totalEquips = 0;\n        finalCombines.forEach(combine => {\n            totalEquips += combine.num;\n            console.log(`装备ID: ${combine.equip_id}, 可合成: ${combine.num}件`);\n        });\n\n        console.log(`总计可合成 ${totalEquips} 件装备`);\n    }\n\n    combineAll() {\n        if (this._combineAllInfo.length == 0) {\n            return\n        }\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {\n            equip_combine: { combines: this._combineAllInfo }\n        })\n    }\n}"]}