{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/BossData.ts"], "names": ["BossBaseData", "BossData", "BossAttackPointData", "BossAttackActionData", "UnitData", "BossUnitData", "BossBulletData", "color", "Color", "v2", "Vec2", "Tools", "EnemyCollider", "TrackGroup", "id", "atlas", "exp", "collideArr", "attack", "collide<PERSON><PERSON><PERSON>", "transformAudio", "blastParam", "blastShake", "<PERSON><PERSON><PERSON><PERSON>", "onlyLoot", "lootArr", "lootParam0", "lootParam1", "loadJson", "data", "hasOwnProperty", "parseInt", "split", "ta", "cs", "csArray", "push", "stringToNumber", "bla", "blaArray", "skArray", "sk", "stringToPoint", "atk", "col", "app", "fl", "loot", "lp0", "lp1", "subId", "units", "unitsOrder", "colliders", "hpParam", "blastType", "bombHurt", "leave", "nextBoss", "wayPointXs", "wayPointYs", "wayPointIntervals", "speeds", "attackIntervals", "snakePara<PERSON>", "trackGroups", "attackActions", "attackPoints", "dieFall<PERSON>elay", "blastCount", "va", "dashTrack", "freeTrackArr", "enemyId", "enemyRotate", "enemyPos", "enemyTrackGroup1", "enemyTrackGroup2", "bId", "sId", "us", "rid", "uaArray", "ua", "cosArray", "cos", "collider", "hpp", "Number", "bh", "dhA<PERSON>y", "dh", "ea", "way", "wayA<PERSON>y", "point", "x", "y", "wi", "sp", "ai", "ra", "raArray", "i", "length", "attackAction", "attackPointIndex", "attackPointKey", "attackPointData", "blp", "blpArray", "ft", "eid", "erotate", "eposArray", "epos", "position", "etrack1Array", "etrack1", "trackGroup", "etrack2Array", "etrack2", "bAvailable", "atkType", "atkUnitId", "atkAnim", "shootInterval", "bulletIDs", "bulletNums", "bulletIntervals", "bulletAttackRates", "attackOverDelay", "waveIds", "parts", "error", "animParts", "anim", "attackParts", "attackData", "waveParts", "wavePosition", "bAtkMove", "atkActId", "atkPointId", "uId", "type", "img", "imgPao", "dam", "pos", "ZERO", "hurtColor", "RED", "turn", "hp", "hpStage", "score", "blastSmoke", "mixArr", "param0", "am", "im", "imp", "hs", "sco", "colorData", "hc", "params", "parsedParams", "map", "param", "soA<PERSON>y", "so", "mixArray", "mix", "parsedMix", "v0", "image", "damageImage", "damagePos", "uiParam", "attr", "recoil", "hpFire", "hpFireParam", "atkPos", "dmg", "dp", "ui", "aa", "fhp", "fireArray", "fire", "atkIndex", "atkKey", "soundId", "bulletData"], "mappings": ";;;yIAQaA,Y,EA4DAC,Q,EA8NAC,mB,EA+FAC,oB,EA2BAC,Q,EAgGAC,Y,EA6DAC,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAzjBJC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AAClBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;8BACab,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACtBc,EADsB,GACT,CADS;AAAA,eAEtBC,KAFsB,GAEJ,EAFI;AAAA,eAGtBC,GAHsB,GAGR,CAHQ;AAAA,eAItBC,UAJsB,GAIG,EAJH;AAAA,eAKtBC,MALsB,GAKL,CALK;AAAA,eAMtBC,aANsB,GAME,CANF;AAAA,eAOtBC,cAPsB,GAOG,EAPH;AAAA,eAQtBC,UARsB,GAQG,EARH;AAAA,eAStBC,UATsB,GASD,EATC;AAAA,eAUtBC,WAVsB,GAUE,EAVF;AAAA,eAWtBC,QAXsB,GAWD,EAXC;AAAA,eAYtBC,OAZsB,GAYF,EAZE;AAAA,eAatBC,UAbsB,GAaC,EAbD;AAAA,eActBC,UAdsB,GAcC,EAdD;AAAA;;AAgBtB;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACf,EAAN,CAAlB;AAC/B,cAAIe,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC,KAAKf,KAAL,GAAac,IAAI,CAACd,KAAL,CAAWiB,KAAX,CAAiB,GAAjB,CAAb;AAClC,cAAIH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKd,GAAL,GAAWe,QAAQ,CAACF,IAAI,CAACb,GAAN,CAAnB;AAChC,cAAIa,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKV,cAAL,GAAsBS,IAAI,CAACI,EAA3B;;AAE/B,cAAIJ,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACK,EAAL,KAAY,EAA7C,EAAiD;AAC7C,kBAAMC,OAAO,GAAGN,IAAI,CAACK,EAAL,CAAQF,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAME,EAAX,IAAiBC,OAAjB,EAA0B;AACtB,kBAAID,EAAE,KAAK,EAAX,EAAe,KAAKjB,UAAL,CAAgBmB,IAAhB,CAAqB;AAAA;AAAA,kCAAMC,cAAN,CAAqBH,EAArB,EAAyB,GAAzB,CAArB;AAClB;AACJ;;AAED,cAAIL,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACS,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAGV,IAAI,CAACS,GAAL,CAASN,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMM,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB,KAAKjB,UAAL,CAAgBe,IAAhB,CAAqB;AAAA;AAAA,kCAAMC,cAAN,CAAqBC,GAArB,EAA0B,GAA1B,CAArB;AACnB;AACJ;;AAED,cAAIT,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMU,OAAO,GAAGX,IAAI,CAACY,EAAL,CAAQT,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMS,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAKnB,UAAL,CAAgBc,IAAhB,CAAqB;AAAA;AAAA,kCAAMM,aAAN,CAAoBD,EAApB,EAAwB,GAAxB,CAArB;AAClB;AACJ;;AAED,cAAIZ,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKZ,MAAL,GAAca,QAAQ,CAACF,IAAI,CAACc,GAAN,CAAtB;AAChC,cAAId,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKX,aAAL,GAAqBY,QAAQ,CAACF,IAAI,CAACe,GAAN,CAA7B;AAChC,cAAIf,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKP,WAAL,GAAmB;AAAA;AAAA,8BAAMc,cAAN,CAAqBR,IAAI,CAACgB,GAA1B,EAA+B,GAA/B,CAAnB;AAChC,cAAIhB,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACiB,EAAL,KAAY,EAA7C,EAAiD,KAAKtB,QAAL,GAAgB;AAAA;AAAA,8BAAMa,cAAN,CAAqBR,IAAI,CAACiB,EAA1B,EAA8B,GAA9B,CAAhB;AACjD,cAAIjB,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC,KAAKL,OAAL,GAAe;AAAA;AAAA,8BAAMY,cAAN,CAAqBR,IAAI,CAACkB,IAA1B,EAAgC,GAAhC,CAAf;AACjC,cAAIlB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKJ,UAAL,GAAkB;AAAA;AAAA,8BAAMW,cAAN,CAAqBR,IAAI,CAACmB,GAA1B,EAA+B,GAA/B,CAAlB;AAChC,cAAInB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKH,UAAL,GAAkB;AAAA;AAAA,8BAAMU,cAAN,CAAqBR,IAAI,CAACoB,GAA1B,EAA+B,GAA/B,CAAlB;AACnC;;AAtDqB,O;AAyD1B;AACA;AACA;;;0BACahD,Q,GAAN,MAAMA,QAAN,SAAuBD,YAAvB,CAAoC;AAAA;AAAA;AAAA,eACvCkD,KADuC,GACvB,CADuB;AAAA,eAEvCC,KAFuC,GAErB,EAFqB;AAAA,eAGvCC,UAHuC,GAGd,EAHc;AAAA,eAIvCC,SAJuC,GAIV,EAJU;AAAA,eAKvCC,OALuC,GAKnB,EALmB;AAAA,eAMvC/B,WANuC,GAMf,EANe;AAAA,eAOvCH,cAPuC,GAOd,EAPc;AAAA,eAQvCmC,SARuC,GAQnB,CARmB;AAAA,eASvCC,QATuC,GASpB,CAToB;AAAA,eAUvCC,KAVuC,GAUvB,CAVuB;AAAA,eAWvCC,QAXuC,GAWlB,EAXkB;AAAA,eAYvCC,UAZuC,GAYhB,EAZgB;AAAA,eAavCC,UAbuC,GAahB,EAbgB;AAAA,eAcvCC,iBAduC,GAcT,EAdS;AAAA,eAevCC,MAfuC,GAepB,EAfoB;AAAA,eAgBvCC,eAhBuC,GAgBX,EAhBW;AAAA,eAiBvCC,UAjBuC,GAiBnB,EAjBmB;AAAA,eAkBvCC,WAlBuC,GAkBX,EAlBW;AAAA,eAmBvCC,aAnBuC,GAmBhB,EAnBgB;AAAA,eAoBvCC,YApBuC,GAoBjB,EApBiB;AAAA,eAqBvCC,YArBuC,GAqBhB,CArBgB;AAAA,eAsBvCC,UAtBuC,GAsBlB,CAtBkB;AAAA,eAuBvCC,EAvBuC;AAAA,eAwBvCC,SAxBuC,GAwB3B,EAxB2B;AAAA,eAyBvCC,YAzBuC;AAAA,eA0BvCC,OA1BuC;AAAA,eA2BvCC,WA3BuC;AAAA,eA4BvCC,QA5BuC,GA4B5B,EA5B4B;AAAA,eA6BvCC,gBA7BuC,GA6BpB,EA7BoB;AAAA,eA8BvCC,gBA9BuC,GA8BpB,EA9BoB;AAAA;;AAgCvC;AACJ;AACA;AACA;AACIjD,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACiD,GAAN,CAAlB;AAChC,cAAIjD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKoB,KAAL,GAAanB,QAAQ,CAACF,IAAI,CAACkD,GAAN,CAArB;AAChC,cAAIlD,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKqB,KAAL,GAAa;AAAA;AAAA,8BAAMd,cAAN,CAAqBR,IAAI,CAACmD,EAA1B,EAA8B,GAA9B,CAAb;AAC/B,cAAInD,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACoD,GAAL,KAAa,EAA/C,EAAmD,KAAKvB,QAAL,GAAgB;AAAA;AAAA,8BAAMrB,cAAN,CAAqBR,IAAI,CAACoD,GAA1B,EAA+B,GAA/B,CAAhB;AACnD,cAAIpD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKd,GAAL,GAAWe,QAAQ,CAACF,IAAI,CAACb,GAAN,CAAnB;AAChC,cAAIa,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC,KAAK2B,KAAL,GAAa1B,QAAQ,CAACF,IAAI,CAAC4B,KAAN,CAArB;AAClC,cAAI5B,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKwC,EAAL,GAAU;AAAA;AAAA,8BAAMjC,cAAN,CAAqBR,IAAI,CAACyC,EAA1B,EAA8B,GAA9B,CAAV;;AAE/B,cAAIzC,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMoD,OAAO,GAAGrD,IAAI,CAACsD,EAAL,CAAQnD,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMmD,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAK/B,UAAL,CAAgBhB,IAAhB,CAAqB;AAAA;AAAA,kCAAMC,cAAN,CAAqB8C,EAArB,EAAyB,GAAzB,CAArB;AAClB;AACJ;;AAED,cAAItD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAMsD,QAAQ,GAAGvD,IAAI,CAACwD,GAAL,CAASrD,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMqD,GAAX,IAAkBD,QAAlB,EAA4B;AACxB,oBAAME,QAAQ,GAAG;AAAA;AAAA,mDAAjB;AACAA,cAAAA,QAAQ,CAAC1D,QAAT,CAAkByD,GAAlB;AACA,mBAAKhC,SAAL,CAAejB,IAAf,CAAoBkD,QAApB;AACH;AACJ;;AAED,cAAIzD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKwB,OAAL,GAAe;AAAA;AAAA,8BAAMjB,cAAN,CAAqBR,IAAI,CAAC0D,GAA1B,EAA+B,GAA/B,CAAf;AAChC,cAAI1D,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKP,WAAL,GAAmB;AAAA;AAAA,8BAAMc,cAAN,CAAqBR,IAAI,CAACgB,GAA1B,EAA+B,GAA/B,CAAnB;AAChC,cAAIhB,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKV,cAAL,GAAsBS,IAAI,CAACI,EAA3B;AAC/B,cAAIJ,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKyB,SAAL,GAAiBiC,MAAM,CAAC3D,IAAI,CAACS,GAAN,CAAvB;AAChC,cAAIT,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAK0B,QAAL,GAAgBgC,MAAM,CAAC3D,IAAI,CAAC4D,EAAN,CAAtB;AAC/B,cAAI5D,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKZ,MAAL,GAAca,QAAQ,CAACF,IAAI,CAACc,GAAN,CAAtB;AAChC,cAAId,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKX,aAAL,GAAqBY,QAAQ,CAACF,IAAI,CAACe,GAAN,CAA7B;;AAEhC,cAAIf,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAM4D,OAAO,GAAG7D,IAAI,CAAC8D,EAAL,CAAQ3D,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAM2D,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe,KAAKpB,SAAL,CAAenC,IAAf,CAAoB;AAAA;AAAA,kCAAMC,cAAN,CAAqBsD,EAArB,EAAyB,GAAzB,CAApB;AAClB;AACJ;;AAED,cAAI9D,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAAC+D,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKpB,YAAL,GAAoB;AAAA;AAAA,gCAAMnC,cAAN,CAAqBR,IAAI,CAAC+D,EAA1B,EAA8B,GAA9B,CAApB;AACH;;AAED,cAAI/D,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACgE,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAGjE,IAAI,CAACgE,GAAL,CAAS7D,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAM6D,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB;AACZ,sBAAME,KAAK,GAAG;AAAA;AAAA,oCAAMrD,aAAN,CAAoBmD,GAApB,EAAyB,GAAzB,CAAd;AACA,qBAAKlC,UAAL,CAAgBvB,IAAhB,CAAqB2D,KAAK,CAACC,CAA3B;AACA,qBAAKpC,UAAL,CAAgBxB,IAAhB,CAAqB2D,KAAK,CAACE,CAA3B;AACH;AACJ;AACJ;;AAED,cAAIpE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACqE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKrC,iBAAL,GAAyB;AAAA;AAAA,gCAAMxB,cAAN,CAAqBR,IAAI,CAACqE,EAA1B,EAA8B,GAA9B,CAAzB;AACH;;AAED,cAAIrE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACsE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKrC,MAAL,GAAc;AAAA;AAAA,gCAAMzB,cAAN,CAAqBR,IAAI,CAACsE,EAA1B,EAA8B,GAA9B,CAAd;AACH;;AAED,cAAItE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACuE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKrC,eAAL,GAAuB;AAAA;AAAA,gCAAM1B,cAAN,CAAqBR,IAAI,CAACuE,EAA1B,EAA8B,GAA9B,CAAvB;AACH;;AAED,cAAIvE,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACwE,EAAL,KAAY,EAA7C,EAAiD;AAC7C,kBAAMC,OAAO,GAAGzE,IAAI,CAACwE,EAAL,CAAQrE,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,OAAO,CAACE,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,kBAAID,OAAO,CAACC,CAAD,CAAP,KAAe,EAAnB,EAAuB;AACnB,sBAAME,YAAY,GAAG,IAAItG,oBAAJ,EAArB;AACAsG,gBAAAA,YAAY,CAAC7E,QAAb,CAAsB0E,OAAO,CAACC,CAAD,CAA7B;AACA,qBAAKrC,aAAL,CAAmB9B,IAAnB,CAAwBqE,YAAxB;AACH;AACJ;AACJ,WA5EqB,CA8EtB;;;AACA,cAAIC,gBAAgB,GAAG,CAAvB;;AACA,iBAAO,IAAP,EAAa;AACT,kBAAMC,cAAc,GAAG,MAAMD,gBAAgB,EAA7C;AACA,gBAAI,CAAC7E,IAAI,CAACC,cAAL,CAAoB6E,cAApB,CAAD,IAAwC9E,IAAI,CAAC8E,cAAD,CAAJ,KAAyB,EAArE,EAAyE;AAEzE,kBAAMC,eAAe,GAAG,IAAI1G,mBAAJ,EAAxB;AACA0G,YAAAA,eAAe,CAAChF,QAAhB,CAAyBC,IAAI,CAAC8E,cAAD,CAA7B;AACA,iBAAKxC,YAAL,CAAkB/B,IAAlB,CAAuBwE,eAAvB;AACH,WAvFqB,CAyFtB;;;AACA,cAAI/E,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACgF,GAAL,KAAa,EAA/C,EAAmD;AAC/C,kBAAMC,QAAQ,GAAGjF,IAAI,CAACgF,GAAL,CAAS7E,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,QAAQ,CAACN,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;AACtC,kBAAIO,QAAQ,CAACP,CAAD,CAAR,KAAgB,EAApB,EAAwB;AACpB,qBAAKlF,UAAL,CAAgBe,IAAhB,CAAqB;AAAA;AAAA,oCAAMC,cAAN,CAAqByE,QAAQ,CAACP,CAAD,CAA7B,EAAkC,GAAlC,CAArB;AACA,qBAAKlC,UAAL;AACH;AACJ;AACJ,WAlGqB,CAoGtB;;;AACA,cAAIxC,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMU,OAAO,GAAGX,IAAI,CAACY,EAAL,CAAQT,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG/D,OAAO,CAACgE,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,kBAAI/D,OAAO,CAAC+D,CAAD,CAAP,KAAe,EAAnB,EAAuB;AACnB,qBAAKjF,UAAL,CAAgBc,IAAhB,CAAqB;AAAA;AAAA,oCAAMM,aAAN,CAAoBF,OAAO,CAAC+D,CAAD,CAA3B,EAAgC,GAAhC,CAArB;AACH;AACJ;AACJ,WA5GqB,CA8GtB;;;AACA,cAAI1E,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,iBAAKsC,YAAL,GAAoBoB,MAAM,CAAC3D,IAAI,CAACkF,EAAN,CAA1B;AACH,WAjHqB,CAmHtB;;;AACA,cAAIlF,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACiB,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKtB,QAAL,GAAgB;AAAA;AAAA,gCAAMa,cAAN,CAAqBR,IAAI,CAACiB,EAA1B,EAA8B,GAA9B,CAAhB;AACH,WAtHqB,CAwHtB;;;AACA,cAAIjB,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,iBAAKL,OAAL,GAAe;AAAA;AAAA,gCAAMY,cAAN,CAAqBR,IAAI,CAACkB,IAA1B,EAAgC,GAAhC,CAAf;AACH,WA3HqB,CA6HtB;;;AACA,cAAIlB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKJ,UAAL,GAAkB;AAAA;AAAA,gCAAMW,cAAN,CAAqBR,IAAI,CAACmB,GAA1B,EAA+B,GAA/B,CAAlB;AACH,WAhIqB,CAkItB;;;AACA,cAAInB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKH,UAAL,GAAkB;AAAA;AAAA,gCAAMU,cAAN,CAAqBR,IAAI,CAACoB,GAA1B,EAA+B,GAA/B,CAAlB;AACH,WArIqB,CAuItB;;;AACA,cAAIpB,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAK2C,OAAL,GAAee,MAAM,CAAC3D,IAAI,CAACmF,GAAN,CAArB;AACH,WA1IqB,CA4ItB;;;AACA,cAAInF,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,iBAAK4C,WAAL,GAAmBc,MAAM,CAAC3D,IAAI,CAACoF,OAAN,CAAzB;AACH,WA/IqB,CAiJtB;;;AACA,cAAIpF,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,kBAAMoF,SAAS,GAAGrF,IAAI,CAACsF,IAAL,CAAUnF,KAAV,CAAgB,GAAhB,CAAlB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGW,SAAS,CAACV,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;AACvC,oBAAMa,QAAQ,GAAG;AAAA;AAAA,kCAAM/E,cAAN,CAAqB6E,SAAS,CAACX,CAAD,CAA9B,EAAmC,GAAnC,CAAjB;;AACA,kBAAIa,QAAQ,CAACZ,MAAT,KAAoB,CAAxB,EAA2B;AACvB,qBAAK7B,QAAL,CAAcvC,IAAd,CAAmB3B,EAAE,CAAC2G,QAAQ,CAAC,CAAD,CAAT,EAAcA,QAAQ,CAAC,CAAD,CAAtB,CAArB;AACH;AACJ;AACJ,WA1JqB,CA4JtB;;;AACA,cAAIvF,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,kBAAMuF,YAAY,GAAGxF,IAAI,CAACyF,OAAL,CAAatF,KAAb,CAAmB,GAAnB,CAArB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGc,YAAY,CAACb,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,kBAAIc,YAAY,CAACd,CAAD,CAAZ,KAAoB,EAApB,IAA0Bc,YAAY,CAACd,CAAD,CAAZ,CAAgBvE,KAAhB,CAAsB,GAAtB,EAA2BwE,MAA3B,GAAoC,CAAlE,EAAqE;AACjE,sBAAMe,UAAU,GAAG;AAAA;AAAA,+CAAnB;AACAA,gBAAAA,UAAU,CAAC3F,QAAX,CAAoByF,YAAY,CAACd,CAAD,CAAhC;AACA,qBAAK3B,gBAAL,CAAsBxC,IAAtB,CAA2BmF,UAA3B;AACH;AACJ;AACJ,WAtKqB,CAwKtB;;;AACA,cAAI1F,IAAI,CAACC,cAAL,CAAoB,SAApB,CAAJ,EAAoC;AAChC,kBAAM0F,YAAY,GAAG3F,IAAI,CAAC4F,OAAL,CAAazF,KAAb,CAAmB,GAAnB,CAArB;;AACA,iBAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,YAAY,CAAChB,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1C,kBAAIiB,YAAY,CAACjB,CAAD,CAAZ,KAAoB,EAApB,IAA0BiB,YAAY,CAACjB,CAAD,CAAZ,CAAgBvE,KAAhB,CAAsB,GAAtB,EAA2BwE,MAA3B,GAAoC,CAAlE,EAAqE;AACjE,sBAAMe,UAAU,GAAG;AAAA;AAAA,+CAAnB;AACAA,gBAAAA,UAAU,CAAC3F,QAAX,CAAoB4F,YAAY,CAACjB,CAAD,CAAhC;AACA,qBAAK1B,gBAAL,CAAsBzC,IAAtB,CAA2BmF,UAA3B;AACH;AACJ;AACJ;AACJ;;AAvNsC,O;AA2N3C;AACA;AACA;;;qCACarH,mB,GAAN,MAAMA,mBAAN,CAA0B;AAAA;AAAA,eAC7BwH,UAD6B,GACP,IADO;AAAA,eAE7BC,OAF6B,GAEX,CAFW;AAAA,eAG7BC,SAH6B,GAGT,CAHS;AAAA,eAI7BC,OAJ6B,GAIP,EAJO;AAAA,eAK7B7B,CAL6B,GAKjB,CALiB;AAAA,eAM7BC,CAN6B,GAMjB,CANiB;AAAA,eAO7B6B,aAP6B,GAOH,EAPG;AAAA,eAQ7BC,SAR6B,GAQP,EARO;AAAA,eAS7BC,UAT6B,GASN,EATM;AAAA,eAU7BC,eAV6B,GAUD,EAVC;AAAA,eAW7BC,iBAX6B,GAWC,EAXD;AAAA,eAY7BC,eAZ6B,GAYD,EAZC;AAAA,eAa7BC,OAb6B,GAaT,EAbS;AAAA;;AAe7B;AACJ;AACA;AACA;AACIxG,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAMwG,KAAK,GAAGxG,IAAI,CAACG,KAAL,CAAW,GAAX,CAAd;;AACA,cAAIqG,KAAK,CAAC7B,MAAN,GAAe,CAAnB,EAAsB;AAClB;AAAA;AAAA,gCAAM8B,KAAN,CAAY,4BAAZ,EAA0CzG,IAA1C;AACA;AACH;;AAED,eAAK8F,OAAL,GAAe5F,QAAQ,CAACsG,KAAK,CAAC,CAAD,CAAN,CAAvB;AACA,eAAKT,SAAL,GAAiB7F,QAAQ,CAACsG,KAAK,CAAC,CAAD,CAAN,CAAzB;AAEA,gBAAME,SAAS,GAAGF,KAAK,CAAC,CAAD,CAAL,CAASrG,KAAT,CAAe,GAAf,CAAlB;;AACA,eAAK,MAAMwG,IAAX,IAAmBD,SAAnB,EAA8B;AAC1B,gBAAIC,IAAI,KAAK,EAAb,EAAiB;AACb,mBAAKX,OAAL,CAAazF,IAAb,CAAkB;AAAA;AAAA,kCAAMC,cAAN,CAAqBmG,IAArB,EAA2B,GAA3B,CAAlB;AACH;AACJ;;AAED,kBAAQ,KAAKb,OAAb;AACI,iBAAK,CAAL;AAAQ;AACJ,oBAAMc,WAAW,GAAGJ,KAAK,CAAC,CAAD,CAAL,CAASrG,KAAT,CAAe,GAAf,CAApB;;AACA,kBAAI;AACA,oBAAIyG,WAAW,CAACjC,MAAZ,IAAsB,CAA1B,EAA6B;AACzB,uBAAKkB,UAAL,GAAkB,KAAlB;AACA;AACH;;AAED,sBAAMN,QAAQ,GAAG;AAAA;AAAA,oCAAM1E,aAAN,CAAoB+F,WAAW,CAAC,CAAD,CAA/B,EAAoC,GAApC,CAAjB;AACA,qBAAKzC,CAAL,GAASoB,QAAQ,CAACpB,CAAlB;AACA,qBAAKC,CAAL,GAASmB,QAAQ,CAACnB,CAAlB;;AAEA,qBAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkC,WAAW,CAACjC,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;AACzC,sBAAIkC,WAAW,CAAClC,CAAD,CAAX,KAAmB,EAAvB,EAA2B;AACvB,0BAAMmC,UAAU,GAAG;AAAA;AAAA,wCAAMrG,cAAN,CAAqBoG,WAAW,CAAClC,CAAD,CAAhC,EAAqC,GAArC,CAAnB;AACA,yBAAKuB,aAAL,CAAmB1F,IAAnB,CAAwBsG,UAAU,CAAC,CAAD,CAAlC;AACA,yBAAKX,SAAL,CAAe3F,IAAf,CAAoBsG,UAAU,CAAC,CAAD,CAA9B;AACA,yBAAKV,UAAL,CAAgB5F,IAAhB,CAAqBsG,UAAU,CAAC,CAAD,CAA/B;AACA,yBAAKT,eAAL,CAAqB7F,IAArB,CAA0BsG,UAAU,CAAC,CAAD,CAApC;AACA,yBAAKR,iBAAL,CAAuB9F,IAAvB,CAA4BsG,UAAU,CAAC,CAAD,CAAV,GAAgB,GAA5C;AACA,yBAAKP,eAAL,CAAqB/F,IAArB,CAA0BsG,UAAU,CAAC,CAAD,CAApC;AACH;AACJ;AACJ,eArBD,CAqBE,OAAOJ,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAMA,KAAN,CAAY,4BAAZ,EAA0CzG,IAA1C;AACH;;AACD;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,oBAAM8G,SAAS,GAAGN,KAAK,CAAC,CAAD,CAAL,CAASrG,KAAT,CAAe,GAAf,CAAlB;;AACA,kBAAI;AACA,oBAAI2G,SAAS,CAACnC,MAAV,IAAoB,CAAxB,EAA2B;AACvB,uBAAKkB,UAAL,GAAkB,KAAlB;AACA;AACH;;AAED,sBAAMkB,YAAY,GAAG;AAAA;AAAA,oCAAMlG,aAAN,CAAoBiG,SAAS,CAAC,CAAD,CAA7B,EAAkC,GAAlC,CAArB;AACA,qBAAK3C,CAAL,GAAS4C,YAAY,CAAC5C,CAAtB;AACA,qBAAKC,CAAL,GAAS2C,YAAY,CAAC3C,CAAtB;AAEA,qBAAKmC,OAAL,GAAe;AAAA;AAAA,oCAAM/F,cAAN,CAAqBsG,SAAS,CAAC,CAAD,CAA9B,EAAmC,GAAnC,CAAf;;AACA,oBAAIA,SAAS,CAACnC,MAAV,GAAmB,CAAvB,EAA0B;AACtB,uBAAK2B,eAAL,CAAqB/F,IAArB,CAA0BL,QAAQ,CAAC4G,SAAS,CAAC,CAAD,CAAV,CAAlC;AACH;AACJ,eAdD,CAcE,OAAOL,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAMA,KAAN,CAAY,4BAAZ,EAA0CzG,IAA1C;AACH;;AACD;;AAEJ;AACI;AAAA;AAAA,kCAAMyG,KAAN,CAAY,sBAAZ,EAAoC,KAAKX,OAAzC;AACA;AApDR;AAsDH;;AA1F4B,O;AA4FjC;AACA;AACA;;;sCACaxH,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eAC9B0I,QAD8B,GACV,KADU;AACH;AADG,eAE9BC,QAF8B,GAEX,CAFW;AAER;AAFQ,eAG9BC,UAH8B,GAGP,EAHO;AAAA;;AAGH;;AAE3B;AACJ;AACA;AACA;AACInH,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAMwG,KAAK,GAAG;AAAA;AAAA,8BAAMhG,cAAN,CAAqBR,IAArB,EAA2B,GAA3B,CAAd;;AACA,cAAI;AACA,gBAAIwG,KAAK,CAAC7B,MAAN,GAAe,CAAnB,EAAsB;AAClB,mBAAKqC,QAAL,GAAgBR,KAAK,CAAC,CAAD,CAAL,KAAa,CAA7B;AACA,mBAAKS,QAAL,GAAgBT,KAAK,CAAC,CAAD,CAArB;;AACA,mBAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAAK,CAAC7B,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,qBAAKwC,UAAL,CAAgB3G,IAAhB,CAAqBiG,KAAK,CAAC9B,CAAD,CAA1B;AACH;AACJ;AACJ,WARD,CAQE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAY,6BAAZ,EAA2CzG,IAA3C;AACH;AACJ;;AAtB6B,O;AAwBlC;AACA;AACA;;;0BACazB,Q,GAAN,MAAMA,QAAN,CAAe;AAAA;AAAA,eAClBU,EADkB,GACL,CADK;AAAA,eAElBkI,GAFkB,GAEJ,CAFI;AAAA,eAGlBC,IAHkB,GAGH,CAHG;AAAA,eAIlBT,IAJkB,GAIH,EAJG;AAAA,eAKlBU,GALkB,GAKF,EALE;AAAA,eAMlBC,MANkB,GAMC,EAND;AAAA,eAOlBC,GAPkB,GAOJ,EAPI;AAAA,eAQlBC,GARkB,GAQN3I,IAAI,CAAC4I,IARC;AAAA,eASlBC,SATkB,GASC/I,KAAK,CAACgJ,GATP;AAAA,eAUlBC,IAVkB,GAUD,EAVC;AAAA,eAWlBC,EAXkB,GAWL,CAXK;AAAA,eAYlBpG,OAZkB,GAYE,EAZF;AAAA,eAalBqG,OAbkB,GAaE,EAbF;AAAA,eAclBrE,QAdkB,GAce,IAdf;AAAA,eAelBsE,KAfkB,GAeF,CAfE;AAAA,eAgBlBvI,UAhBkB,GAgBO,EAhBP;AAAA,eAiBlBC,UAjBkB,GAiBO,EAjBP;AAAA,eAkBlBuI,UAlBkB,GAkBO,EAlBP;AAAA,eAmBlBC,MAnBkB,GAmBG,EAnBH;AAAA,eAoBlBC,MApBkB,GAoBC,EApBD;AAAA;;AAsBlB;AACJ;AACA;AACA;AACInI,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACf,EAAN,CAAlB;AAC/B,cAAIe,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKkH,GAAL,GAAWjH,QAAQ,CAACF,IAAI,CAACmH,GAAN,CAAnB;AAChC,cAAInH,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAK0G,IAAL,GAAY3G,IAAI,CAACmI,EAAjB;AAC/B,cAAInI,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACoI,EAAL,KAAY,EAA7C,EAAiD,KAAKf,GAAL,GAAWrH,IAAI,CAACoI,EAAL,CAAQjI,KAAR,CAAc,GAAd,CAAX;AACjD,cAAIH,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAACqI,GAAL,KAAa,EAA/C,EAAmD,KAAKf,MAAL,GAActH,IAAI,CAACqI,GAAL,CAASlI,KAAT,CAAe,GAAf,CAAd;AACnD,cAAIH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKsH,GAAL,GAAWvH,IAAI,CAACuH,GAAhB;AAChC,cAAIvH,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAK4H,EAAL,GAAU3H,QAAQ,CAACF,IAAI,CAAC6H,EAAN,CAAlB;AAC/B,cAAI7H,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKuH,GAAL,GAAW;AAAA;AAAA,8BAAM3G,aAAN,CAAoBb,IAAI,CAACwH,GAAzB,EAA8B,GAA9B,CAAX;AAChC,cAAIxH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKwB,OAAL,GAAe;AAAA;AAAA,8BAAMjB,cAAN,CAAqBR,IAAI,CAAC0D,GAA1B,EAA+B,GAA/B,CAAf;AAChC,cAAI1D,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAK6H,OAAL,GAAe;AAAA;AAAA,8BAAMtH,cAAN,CAAqBR,IAAI,CAACsI,EAA1B,EAA8B,GAA9B,CAAf;;AAC/B,cAAItI,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKwD,QAAL,GAAgB;AAAA;AAAA,iDAAhB;AACA,iBAAKA,QAAL,CAAc1D,QAAd,CAAuBC,IAAI,CAACe,GAA5B;AACH;;AACD,cAAIf,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAK8H,KAAL,GAAa7H,QAAQ,CAACF,IAAI,CAACuI,GAAN,CAArB;;AAChC,cAAIvI,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMuI,SAAS,GAAG;AAAA;AAAA,gCAAMhI,cAAN,CAAqBR,IAAI,CAACyI,EAA1B,EAA8B,GAA9B,CAAlB;;AACA,gBAAID,SAAS,CAAC7D,MAAV,IAAoB,CAAxB,EAA2B;AACvB,mBAAK+C,SAAL,GAAiBhJ,KAAK,CAAC8J,SAAS,CAAC,CAAD,CAAV,EAAeA,SAAS,CAAC,CAAD,CAAxB,EAA6BA,SAAS,CAAC,CAAD,CAAtC,CAAtB;AACH;AACJ;;AACD,cAAIxI,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC,KAAK2H,IAAL,GAAY;AAAA;AAAA,8BAAMpH,cAAN,CAAqBR,IAAI,CAAC4H,IAA1B,EAAgC,GAAhC,CAAZ;;AACjC,cAAI5H,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAMS,QAAQ,GAAGV,IAAI,CAACS,GAAL,CAASN,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMM,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB;AACZ,sBAAMiI,MAAM,GAAGjI,GAAG,CAACN,KAAJ,CAAU,GAAV,CAAf;AACA,sBAAMwI,YAAY,GAAGD,MAAM,CAACE,GAAP,CAAWC,KAAK,IAAI;AAAA;AAAA,oCAAMrI,cAAN,CAAqBqI,KAArB,EAA4B,GAA5B,CAApB,CAArB;AACA,qBAAKrJ,UAAL,CAAgBe,IAAhB,CAAqBoI,YAArB;AACH;AACJ;AACJ;;AACD,cAAI3I,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAMU,OAAO,GAAGX,IAAI,CAACY,EAAL,CAAQT,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAMS,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe;AACX,sBAAM8H,MAAM,GAAG9H,EAAE,CAACT,KAAH,CAAS,GAAT,CAAf;AACA,sBAAMwI,YAAY,GAAGD,MAAM,CAACE,GAAP,CAAWC,KAAK,IAAI;AAAA;AAAA,oCAAMrI,cAAN,CAAqBqI,KAArB,EAA4B,GAA5B,CAApB,CAArB;AACA,qBAAKpJ,UAAL,CAAgBc,IAAhB,CAAqBoI,YAArB;AACH;AACJ;AACJ;;AACD,cAAI3I,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,kBAAM6I,OAAO,GAAG9I,IAAI,CAAC+I,EAAL,CAAQ5I,KAAR,CAAc,GAAd,CAAhB;;AACA,iBAAK,MAAM4I,EAAX,IAAiBD,OAAjB,EAA0B;AACtB,kBAAIC,EAAE,KAAK,EAAX,EAAe;AACX,sBAAML,MAAM,GAAGK,EAAE,CAAC5I,KAAH,CAAS,GAAT,CAAf;AACA,sBAAMwI,YAAY,GAAGD,MAAM,CAACE,GAAP,CAAWC,KAAK,IAAI;AAAA;AAAA,oCAAMrI,cAAN,CAAqBqI,KAArB,EAA4B,GAA5B,CAApB,CAArB;AACA,qBAAKb,UAAL,CAAgBzH,IAAhB,CAAqBoI,YAArB;AACH;AACJ;AACJ;;AACD,cAAI3I,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAM+I,QAAQ,GAAGhJ,IAAI,CAACiJ,GAAL,CAAS9I,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAM8I,GAAX,IAAkBD,QAAlB,EAA4B;AACxB,kBAAIC,GAAG,KAAK,EAAZ,EAAgB;AACZ,sBAAMC,SAAS,GAAGD,GAAG,CAAC9I,KAAJ,CAAU,GAAV,CAAlB;AACA,qBAAK8H,MAAL,CAAY1H,IAAZ,CAAiB2I,SAAjB;AACH;AACJ;AACJ;;AACD,cAAIlJ,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAACmJ,EAAL,KAAY,EAA7C,EAAiD;AAC7C,iBAAKjB,MAAL,GAAc;AAAA;AAAA,gCAAM1H,cAAN,CAAqBR,IAAI,CAACmJ,EAA1B,EAA8B,GAA9B,CAAd;AACH;AACJ;;AA3FiB,O;AA6FtB;AACA;AACA;;;8BACa3K,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACtBS,EADsB,GACT,CADS;AAAA,eAEtBkI,GAFsB,GAER,CAFQ;AAAA,eAGtBC,IAHsB,GAGP,CAHO;AAAA,eAItBgC,KAJsB,GAIN,EAJM;AAAA,eAKtBC,WALsB,GAKE,EALF;AAAA,eAMtBC,SANsB,GAMJzK,IAAI,CAAC4I,IAND;AAAA,eAOtB8B,OAPsB,GAOF,EAPE;AAAA,eAQtBC,IARsB,GAQP,EARO;AAAA,eAStB3B,EATsB,GAST,CATS;AAAA,eAUtBpG,OAVsB,GAUF,EAVE;AAAA,eAWtBgC,QAXsB,GAWX,EAXW;AAAA,eAYtBgG,MAZsB,GAYJ,KAZI;AAAA,eAatBC,MAbsB,GAaL,CAbK;AAAA,eActBC,WAdsB,GAcP,EAdO;AAAA,eAetBnK,UAfsB,GAeT,EAfS;AAAA,eAgBtBC,UAhBsB,GAgBT,EAhBS;AAAA,eAiBtBmK,MAjBsB,GAiBL,EAjBK;AAAA;;AAmBtB;AACJ;AACA;AACA;AACI7J,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AACtB,cAAIA,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKhB,EAAL,GAAUiB,QAAQ,CAACF,IAAI,CAACf,EAAN,CAAlB;AAC/B,cAAIe,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKkH,GAAL,GAAWjH,QAAQ,CAACF,IAAI,CAACmH,GAAN,CAAnB;AAChC,cAAInH,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC,KAAKmH,IAAL,GAAYlH,QAAQ,CAACF,IAAI,CAACoH,IAAN,CAApB;AACjC,cAAIpH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKmJ,KAAL,GAAapJ,IAAI,CAACqH,GAAlB;AAChC,cAAIrH,IAAI,CAACC,cAAL,CAAoB,KAApB,KAA8BD,IAAI,CAAC6J,GAAL,KAAa,EAA/C,EAAmD,KAAKR,WAAL,GAAmBrJ,IAAI,CAAC6J,GAAL,CAAS1J,KAAT,CAAe,GAAf,CAAnB;AACnD,cAAIH,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAAC8J,EAAL,KAAY,EAA7C,EAAiD,KAAKR,SAAL,GAAiB;AAAA;AAAA,8BAAMzI,aAAN,CAAoBb,IAAI,CAAC8J,EAAzB,EAA6B,GAA7B,CAAjB;AACjD,cAAI9J,IAAI,CAACC,cAAL,CAAoB,IAApB,KAA6BD,IAAI,CAAC+J,EAAL,KAAY,EAA7C,EAAiD,KAAKR,OAAL,GAAe;AAAA;AAAA,8BAAM/I,cAAN,CAAqBR,IAAI,CAAC+J,EAA1B,EAA8B,GAA9B,CAAf;AACjD,cAAI/J,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC,KAAKuJ,IAAL,GAAYxJ,IAAI,CAACwJ,IAAjB;AACjC,cAAIxJ,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAK4H,EAAL,GAAU3H,QAAQ,CAACF,IAAI,CAAC6H,EAAN,CAAlB;AAC/B,cAAI7H,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKwB,OAAL,GAAe;AAAA;AAAA,8BAAMjB,cAAN,CAAqBR,IAAI,CAAC0D,GAA1B,EAA+B,GAA/B,CAAf;AAChC,cAAI1D,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKwD,QAAL,GAAgB;AAAA;AAAA,8BAAMjD,cAAN,CAAqBR,IAAI,CAACe,GAA1B,EAA+B,GAA/B,CAAhB;AAChC,cAAIf,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKwJ,MAAL,GAAcvJ,QAAQ,CAACF,IAAI,CAACgK,EAAN,CAAR,KAAsB,CAApC;AAC/B,cAAIhK,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKyJ,MAAL,GAAcxJ,QAAQ,CAACF,IAAI,CAACiK,GAAN,CAAtB;;AAChC,cAAIjK,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,kBAAMiK,SAAS,GAAGlK,IAAI,CAACmK,IAAL,CAAUhK,KAAV,CAAgB,GAAhB,CAAlB;;AACA,iBAAK,MAAMgK,IAAX,IAAmBD,SAAnB,EAA8B;AAC1B,kBAAIC,IAAI,KAAK,EAAb,EAAiB,KAAKR,WAAL,CAAiBpJ,IAAjB,CAAsB;AAAA;AAAA,kCAAMC,cAAN,CAAqB2J,IAArB,EAA2B,GAA3B,CAAtB;AACpB;AACJ;;AACD,cAAInK,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAMS,QAAQ,GAAGV,IAAI,CAACS,GAAL,CAASN,KAAT,CAAe,GAAf,CAAjB;;AACA,iBAAK,MAAMM,GAAX,IAAkBC,QAAlB,EAA4B;AACxB,kBAAID,GAAG,KAAK,EAAZ,EAAgB,KAAKjB,UAAL,CAAgBe,IAAhB,CAAqB;AAAA;AAAA,kCAAMC,cAAN,CAAqBC,GAArB,EAA0B,GAA1B,CAArB;AACnB;AACJ;;AACD,cAAIT,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKR,UAAL,GAAkB;AAAA;AAAA,8BAAMe,cAAN,CAAqBR,IAAI,CAACY,EAA1B,EAA8B,GAA9B,CAAlB;AAC/B,cAAIwJ,QAAQ,GAAG,CAAf;;AACA,iBAAO,IAAP,EAAa;AACT,kBAAMC,MAAM,GAAG,OAAOD,QAAQ,EAA9B;AACA,gBAAI,CAACpK,IAAI,CAACC,cAAL,CAAoBoK,MAApB,CAAD,IAAgCrK,IAAI,CAACqK,MAAD,CAAJ,KAAiB,EAArD,EAAyD;AACzD,iBAAKT,MAAL,CAAYrJ,IAAZ,CAAiB;AAAA;AAAA,gCAAMM,aAAN,CAAoBb,IAAI,CAACqK,MAAD,CAAxB,EAAkC,GAAlC,CAAjB;AACH;AACJ;;AAxDqB,O;AA0D1B;AACA;AACA;;;gCACa5L,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACxB6L,OADwB,GACN,CADM;AAAA,eAExBrE,aAFwB,GAEE,EAFF;AAAA,eAGxBC,SAHwB,GAGF,EAHE;AAAA,eAIxBC,UAJwB,GAID,EAJC;AAAA,eAKxBC,eALwB,GAKI,EALJ;AAAA,eAMxBC,iBANwB,GAMM,EANN;AAAA,eAOxBC,eAPwB,GAOI,EAPJ;AAAA;;AASxB;AACJ;AACA;AACA;AACIvG,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAMwG,KAAK,GAAGxG,IAAI,CAACG,KAAL,CAAW,GAAX,CAAd;AACA,eAAKmK,OAAL,GAAepK,QAAQ,CAACsG,KAAK,CAAC,CAAD,CAAN,CAAvB;;AACA,eAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,KAAK,CAAC7B,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,gBAAI8B,KAAK,CAAC9B,CAAD,CAAL,KAAa,EAAjB,EAAqB;AACjB,oBAAM6F,UAAU,GAAG;AAAA;AAAA,kCAAM/J,cAAN,CAAqBgG,KAAK,CAAC9B,CAAD,CAA1B,EAA+B,GAA/B,CAAnB;AACA,mBAAKuB,aAAL,CAAmB1F,IAAnB,CAAwBgK,UAAU,CAAC,CAAD,CAAlC;AACA,mBAAKrE,SAAL,CAAe3F,IAAf,CAAoBgK,UAAU,CAAC,CAAD,CAA9B;AACA,mBAAKpE,UAAL,CAAgB5F,IAAhB,CAAqBgK,UAAU,CAAC,CAAD,CAA/B;AACA,mBAAKnE,eAAL,CAAqB7F,IAArB,CAA0BgK,UAAU,CAAC,CAAD,CAApC;AACA,mBAAKlE,iBAAL,CAAuB9F,IAAvB,CAA4BgK,UAAU,CAAC,CAAD,CAAV,GAAgB,GAA5C;AACA,mBAAKjE,eAAL,CAAqB/F,IAArB,CAA0BgK,UAAU,CAAC,CAAD,CAApC;AACH;AACJ;AACJ;;AA3BuB,O", "sourcesContent": ["import { color, Color, v2, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { EnemyCollider } from \"./EnemyData\";\r\nimport { TrackGroup } from \"./EnemyWave\";\r\n\r\n/**\r\n * Boss 基础数据类\r\n */\r\nexport class BossBaseData {\r\n    id: number = 0;\r\n    atlas: string[] = [];\r\n    exp: number = 0;\r\n    collideArr: number[][] = [];\r\n    attack: number = 0;\r\n    collideAttack: number = 0;\r\n    transformAudio: string = \"\";\r\n    blastParam: number[][] = [];\r\n    blastShake: Vec2[] = [];\r\n    appearParam: number[] = [];\r\n    onlyLoot: number[] = [];\r\n    lootArr: number[] = [];\r\n    lootParam0: number[] = [];\r\n    lootParam1: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载 Boss 基础数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"id\")) this.id = parseInt(data.id);\r\n        if (data.hasOwnProperty(\"atlas\")) this.atlas = data.atlas.split(\";\");\r\n        if (data.hasOwnProperty(\"exp\")) this.exp = parseInt(data.exp);\r\n        if (data.hasOwnProperty(\"ta\")) this.transformAudio = data.ta;\r\n\r\n        if (data.hasOwnProperty(\"cs\") && data.cs !== \"\") {\r\n            const csArray = data.cs.split(\";\");\r\n            for (const cs of csArray) {\r\n                if (cs !== \"\") this.collideArr.push(Tools.stringToNumber(cs, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"bla\") && data.bla !== \"\") {\r\n            const blaArray = data.bla.split(\";\");\r\n            for (const bla of blaArray) {\r\n                if (bla !== \"\") this.blastParam.push(Tools.stringToNumber(bla, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"sk\")) {\r\n            const skArray = data.sk.split(\";\");\r\n            for (const sk of skArray) {\r\n                if (sk !== \"\") this.blastShake.push(Tools.stringToPoint(sk, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"atk\")) this.attack = parseInt(data.atk);\r\n        if (data.hasOwnProperty(\"col\")) this.collideAttack = parseInt(data.col);\r\n        if (data.hasOwnProperty(\"app\")) this.appearParam = Tools.stringToNumber(data.app, \",\");\r\n        if (data.hasOwnProperty(\"fl\") && data.fl !== \"\") this.onlyLoot = Tools.stringToNumber(data.fl, \",\");\r\n        if (data.hasOwnProperty(\"loot\")) this.lootArr = Tools.stringToNumber(data.loot, \",\");\r\n        if (data.hasOwnProperty(\"lp0\")) this.lootParam0 = Tools.stringToNumber(data.lp0, \",\");\r\n        if (data.hasOwnProperty(\"lp1\")) this.lootParam1 = Tools.stringToNumber(data.lp1, \",\");\r\n    }\r\n}\r\n\r\n/**\r\n * Boss 数据类\r\n */\r\nexport class BossData extends BossBaseData {\r\n    subId: number = 0;\r\n    units: number[] = [];\r\n    unitsOrder: number[][] = [];\r\n    colliders: EnemyCollider[] = [];\r\n    hpParam: number[] = [];\r\n    appearParam: number[] = [];\r\n    transformAudio: string = \"\";\r\n    blastType: number = 0;\r\n    bombHurt: number = 0;\r\n    leave: number = 0;\r\n    nextBoss: number[] = [];\r\n    wayPointXs: number[] = [];\r\n    wayPointYs: number[] = [];\r\n    wayPointIntervals: number[] = [];\r\n    speeds: number[] = [];\r\n    attackIntervals: number[] = [];\r\n    snakeParam: any[] = [];\r\n    trackGroups: TrackGroup[] = [];\r\n    attackActions: any[] = [];\r\n    attackPoints: any[] = [];\r\n    dieFallDelay: number = 0;\r\n    blastCount: number = 0;\r\n    va: number[];\r\n    dashTrack = [];\r\n    freeTrackArr: number[];\r\n    enemyId: number;\r\n    enemyRotate: number;\r\n    enemyPos = [];\r\n    enemyTrackGroup1 = [];\r\n    enemyTrackGroup2 = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载 Boss 数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"bId\")) this.id = parseInt(data.bId);\r\n        if (data.hasOwnProperty(\"sId\")) this.subId = parseInt(data.sId);\r\n        if (data.hasOwnProperty(\"us\")) this.units = Tools.stringToNumber(data.us, \",\");\r\n        if (data.hasOwnProperty(\"rid\") && data.rid !== \"\") this.nextBoss = Tools.stringToNumber(data.rid, \",\");\r\n        if (data.hasOwnProperty(\"exp\")) this.exp = parseInt(data.exp);\r\n        if (data.hasOwnProperty(\"leave\")) this.leave = parseInt(data.leave);\r\n        if (data.hasOwnProperty(\"va\")) this.va = Tools.stringToNumber(data.va, \",\");\r\n\r\n        if (data.hasOwnProperty(\"ua\")) {\r\n            const uaArray = data.ua.split(\";\");\r\n            for (const ua of uaArray) {\r\n                if (ua !== \"\") this.unitsOrder.push(Tools.stringToNumber(ua, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"cos\")) {\r\n            const cosArray = data.cos.split(\";\");\r\n            for (const cos of cosArray) {\r\n                const collider = new EnemyCollider();\r\n                collider.loadJson(cos);\r\n                this.colliders.push(collider);\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"hpp\")) this.hpParam = Tools.stringToNumber(data.hpp, \",\");\r\n        if (data.hasOwnProperty(\"app\")) this.appearParam = Tools.stringToNumber(data.app, \",\");\r\n        if (data.hasOwnProperty(\"ta\")) this.transformAudio = data.ta;\r\n        if (data.hasOwnProperty(\"bla\")) this.blastType = Number(data.bla);\r\n        if (data.hasOwnProperty(\"bh\")) this.bombHurt = Number(data.bh);\r\n        if (data.hasOwnProperty(\"atk\")) this.attack = parseInt(data.atk);\r\n        if (data.hasOwnProperty(\"col\")) this.collideAttack = parseInt(data.col);\r\n\r\n        if (data.hasOwnProperty(\"dh\")) {\r\n            const dhArray = data.dh.split(\";\");\r\n            for (const dh of dhArray) {\r\n                if (dh !== \"\") this.dashTrack.push(Tools.stringToNumber(dh, \",\"));\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ea\") && data.ea !== \"\") {\r\n            this.freeTrackArr = Tools.stringToNumber(data.ea, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"way\") && data.way !== \"\") {\r\n            const wayArray = data.way.split(\";\");\r\n            for (const way of wayArray) {\r\n                if (way !== \"\") {\r\n                    const point = Tools.stringToPoint(way, \",\");\r\n                    this.wayPointXs.push(point.x);\r\n                    this.wayPointYs.push(point.y);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"wi\") && data.wi !== \"\") {\r\n            this.wayPointIntervals = Tools.stringToNumber(data.wi, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"sp\") && data.sp !== \"\") {\r\n            this.speeds = Tools.stringToNumber(data.sp, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ai\") && data.ai !== \"\") {\r\n            this.attackIntervals = Tools.stringToNumber(data.ai, \",\");\r\n        }\r\n\r\n        if (data.hasOwnProperty(\"ra\") && data.ra !== \"\") {\r\n            const raArray = data.ra.split(\";\");\r\n            for (let i = 0; i < raArray.length; i++) {\r\n                if (raArray[i] !== \"\") {\r\n                    const attackAction = new BossAttackActionData();\r\n                    attackAction.loadJson(raArray[i]);\r\n                    this.attackActions.push(attackAction);\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析攻击点数据\r\n        let attackPointIndex = 0;\r\n        while (true) {\r\n            const attackPointKey = \"a\" + attackPointIndex++;\r\n            if (!data.hasOwnProperty(attackPointKey) || data[attackPointKey] === \"\") break;\r\n        \r\n            const attackPointData = new BossAttackPointData();\r\n            attackPointData.loadJson(data[attackPointKey]);\r\n            this.attackPoints.push(attackPointData);\r\n        }\r\n        \r\n        // 解析爆炸参数\r\n        if (data.hasOwnProperty(\"blp\") && data.blp !== \"\") {\r\n            const blpArray = data.blp.split(\";\");\r\n            for (let i = 0; i < blpArray.length; i++) {\r\n                if (blpArray[i] !== \"\") {\r\n                    this.blastParam.push(Tools.stringToNumber(blpArray[i], \",\"));\r\n                    this.blastCount++;\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析爆炸震动参数\r\n        if (data.hasOwnProperty(\"sk\")) {\r\n            const skArray = data.sk.split(\";\");\r\n            for (let i = 0; i < skArray.length; i++) {\r\n                if (skArray[i] !== \"\") {\r\n                    this.blastShake.push(Tools.stringToPoint(skArray[i], \",\"));\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析死亡掉落延迟\r\n        if (data.hasOwnProperty(\"ft\")) {\r\n            this.dieFallDelay = Number(data.ft);\r\n        }\r\n        \r\n        // 解析唯一掉落\r\n        if (data.hasOwnProperty(\"fl\") && data.fl !== \"\") {\r\n            this.onlyLoot = Tools.stringToNumber(data.fl, \",\");\r\n        }\r\n        \r\n        // 解析掉落数组\r\n        if (data.hasOwnProperty(\"loot\")) {\r\n            this.lootArr = Tools.stringToNumber(data.loot, \",\");\r\n        }\r\n        \r\n        // 解析掉落参数 0\r\n        if (data.hasOwnProperty(\"lp0\")) {\r\n            this.lootParam0 = Tools.stringToNumber(data.lp0, \",\");\r\n        }\r\n        \r\n        // 解析掉落参数 1\r\n        if (data.hasOwnProperty(\"lp1\")) {\r\n            this.lootParam1 = Tools.stringToNumber(data.lp1, \",\");\r\n        }\r\n        \r\n        // 解析敌人 ID\r\n        if (data.hasOwnProperty(\"eid\")) {\r\n            this.enemyId = Number(data.eid);\r\n        }\r\n        \r\n        // 解析敌人旋转角度\r\n        if (data.hasOwnProperty(\"erotate\")) {\r\n            this.enemyRotate = Number(data.erotate);\r\n        }\r\n        \r\n        // 解析敌人位置\r\n        if (data.hasOwnProperty(\"epos\")) {\r\n            const eposArray = data.epos.split(\"#\");\r\n            for (let i = 0; i < eposArray.length; i++) {\r\n                const position = Tools.stringToNumber(eposArray[i], \",\");\r\n                if (position.length === 2) {\r\n                    this.enemyPos.push(v2(position[0], position[1]));\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析敌人轨迹组 1\r\n        if (data.hasOwnProperty(\"etrack1\")) {\r\n            const etrack1Array = data.etrack1.split(\"#\");\r\n            for (let i = 0; i < etrack1Array.length; i++) {\r\n                if (etrack1Array[i] !== \"\" && etrack1Array[i].split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(etrack1Array[i]);\r\n                    this.enemyTrackGroup1.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n        \r\n        // 解析敌人轨迹组 2\r\n        if (data.hasOwnProperty(\"etrack2\")) {\r\n            const etrack2Array = data.etrack2.split(\"#\");\r\n            for (let i = 0; i < etrack2Array.length; i++) {\r\n                if (etrack2Array[i] !== \"\" && etrack2Array[i].split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(etrack2Array[i]);\r\n                    this.enemyTrackGroup2.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    \r\n}\r\n\r\n/**\r\n * Boss 攻击点数据类\r\n */\r\nexport class BossAttackPointData {\r\n    bAvailable: boolean = true;\r\n    atkType: number = 0;\r\n    atkUnitId: number = 0;\r\n    atkAnim: number[][] = [];\r\n    x: number = 0;\r\n    y: number = 0;\r\n    shootInterval: number[] = [];\r\n    bulletIDs: number[] = [];\r\n    bulletNums: number[] = [];\r\n    bulletIntervals: number[] = [];\r\n    bulletAttackRates: number[] = [];\r\n    attackOverDelay: number[] = [];\r\n    waveIds: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载攻击点数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\"#\");\r\n        if (parts.length < 3) {\r\n            Tools.error(\"BossAttackPointData error:\", data);\r\n            return;\r\n        }\r\n\r\n        this.atkType = parseInt(parts[0]);\r\n        this.atkUnitId = parseInt(parts[1]);\r\n\r\n        const animParts = parts[2].split(\";\");\r\n        for (const anim of animParts) {\r\n            if (anim !== \"\") {\r\n                this.atkAnim.push(Tools.stringToNumber(anim, \",\"));\r\n            }\r\n        }\r\n\r\n        switch (this.atkType) {\r\n            case 0: // 普通攻击\r\n                const attackParts = parts[3].split(\";\");\r\n                try {\r\n                    if (attackParts.length <= 1) {\r\n                        this.bAvailable = false;\r\n                        return;\r\n                    }\r\n\r\n                    const position = Tools.stringToPoint(attackParts[0], \",\");\r\n                    this.x = position.x;\r\n                    this.y = position.y;\r\n\r\n                    for (let i = 1; i < attackParts.length; i++) {\r\n                        if (attackParts[i] !== \"\") {\r\n                            const attackData = Tools.stringToNumber(attackParts[i], \",\");\r\n                            this.shootInterval.push(attackData[0]);\r\n                            this.bulletIDs.push(attackData[1]);\r\n                            this.bulletNums.push(attackData[2]);\r\n                            this.bulletIntervals.push(attackData[3]);\r\n                            this.bulletAttackRates.push(attackData[4] / 100);\r\n                            this.attackOverDelay.push(attackData[5]);\r\n                        }\r\n                    }\r\n                } catch (error) {\r\n                    Tools.error(\"BossAttackPointData error:\", data);\r\n                }\r\n                break;\r\n\r\n            case 1: // 波次攻击\r\n                const waveParts = parts[3].split(\";\");\r\n                try {\r\n                    if (waveParts.length <= 1) {\r\n                        this.bAvailable = false;\r\n                        return;\r\n                    }\r\n\r\n                    const wavePosition = Tools.stringToPoint(waveParts[0], \",\");\r\n                    this.x = wavePosition.x;\r\n                    this.y = wavePosition.y;\r\n\r\n                    this.waveIds = Tools.stringToNumber(waveParts[1], \",\");\r\n                    if (waveParts.length > 2) {\r\n                        this.attackOverDelay.push(parseInt(waveParts[2]));\r\n                    }\r\n                } catch (error) {\r\n                    Tools.error(\"BossAttackPointData error:\", data);\r\n                }\r\n                break;\r\n\r\n            default:\r\n                Tools.error(\"Unknown attack type:\", this.atkType);\r\n                break;\r\n        }\r\n    }\r\n}\r\n/**\r\n * Boss 攻击动作数据类\r\n */\r\nexport class BossAttackActionData {\r\n    bAtkMove: boolean = false; // 是否移动攻击\r\n    atkActId: number = 0; // 攻击动作 ID\r\n    atkPointId: number[] = []; // 攻击点 ID 列表\r\n\r\n    /**\r\n     * 从 JSON 数据加载攻击动作数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = Tools.stringToNumber(data, \",\");\r\n        try {\r\n            if (parts.length > 1) {\r\n                this.bAtkMove = parts[0] === 1;\r\n                this.atkActId = parts[1];\r\n                for (let i = 2; i < parts.length; i++) {\r\n                    this.atkPointId.push(parts[i]);\r\n                }\r\n            }\r\n        } catch (error) {\r\n            Tools.error(\"BossAttackActionData error:\", data);\r\n        }\r\n    }\r\n}\r\n/**\r\n * 单位数据类\r\n */\r\nexport class UnitData {\r\n    id: number = 0;\r\n    uId: number = 0;\r\n    type: number = 0;\r\n    anim: string = \"\";\r\n    img: string[] = [];\r\n    imgPao: string[] = [];\r\n    dam: string = \"\";\r\n    pos: Vec2 = Vec2.ZERO;\r\n    hurtColor: Color = Color.RED;\r\n    turn: number[] = [];\r\n    hp: number = 0;\r\n    hpParam: number[] = [];\r\n    hpStage: number[] = [];\r\n    collider: EnemyCollider | null = null;\r\n    score: number = 0;\r\n    blastParam: number[][] = [];\r\n    blastShake: number[][] = [];\r\n    blastSmoke: number[][] = [];\r\n    mixArr: string[][] = [];\r\n    param0: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载单位数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"id\")) this.id = parseInt(data.id);\r\n        if (data.hasOwnProperty(\"uId\")) this.uId = parseInt(data.uId);\r\n        if (data.hasOwnProperty(\"am\")) this.anim = data.am;\r\n        if (data.hasOwnProperty(\"im\") && data.im !== \"\") this.img = data.im.split(\",\");\r\n        if (data.hasOwnProperty(\"imp\") && data.imp !== \"\") this.imgPao = data.imp.split(\",\");\r\n        if (data.hasOwnProperty(\"dam\")) this.dam = data.dam;\r\n        if (data.hasOwnProperty(\"hp\")) this.hp = parseInt(data.hp);\r\n        if (data.hasOwnProperty(\"pos\")) this.pos = Tools.stringToPoint(data.pos, \",\");\r\n        if (data.hasOwnProperty(\"hpp\")) this.hpParam = Tools.stringToNumber(data.hpp, \",\");\r\n        if (data.hasOwnProperty(\"hs\")) this.hpStage = Tools.stringToNumber(data.hs, \",\");\r\n        if (data.hasOwnProperty(\"col\")) {\r\n            this.collider = new EnemyCollider();\r\n            this.collider.loadJson(data.col);\r\n        }\r\n        if (data.hasOwnProperty(\"sco\")) this.score = parseInt(data.sco);\r\n        if (data.hasOwnProperty(\"hc\")) {\r\n            const colorData = Tools.stringToNumber(data.hc, \",\");\r\n            if (colorData.length >= 3) {\r\n                this.hurtColor = color(colorData[0], colorData[1], colorData[2]);\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"turn\")) this.turn = Tools.stringToNumber(data.turn, \",\");\r\n        if (data.hasOwnProperty(\"bla\")) {\r\n            const blaArray = data.bla.split(\"#\");\r\n            for (const bla of blaArray) {\r\n                if (bla !== \"\") {\r\n                    const params = bla.split(\";\");\r\n                    const parsedParams = params.map(param => Tools.stringToNumber(param, \",\"));\r\n                    this.blastParam.push(parsedParams);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"sk\")) {\r\n            const skArray = data.sk.split(\"#\");\r\n            for (const sk of skArray) {\r\n                if (sk !== \"\") {\r\n                    const params = sk.split(\";\");\r\n                    const parsedParams = params.map(param => Tools.stringToNumber(param, \",\"));\r\n                    this.blastShake.push(parsedParams);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"so\")) {\r\n            const soArray = data.so.split(\"#\");\r\n            for (const so of soArray) {\r\n                if (so !== \"\") {\r\n                    const params = so.split(\";\");\r\n                    const parsedParams = params.map(param => Tools.stringToNumber(param, \",\"));\r\n                    this.blastSmoke.push(parsedParams);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"mix\")) {\r\n            const mixArray = data.mix.split(\";\");\r\n            for (const mix of mixArray) {\r\n                if (mix !== \"\") {\r\n                    const parsedMix = mix.split(\",\");\r\n                    this.mixArr.push(parsedMix);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"v0\") && data.v0 !== \"\") {\r\n            this.param0 = Tools.stringToNumber(data.v0, \",\");\r\n        }\r\n    }\r\n}\r\n/**\r\n * Boss 单位数据类\r\n */\r\nexport class BossUnitData {\r\n    id: number = 0;\r\n    uId: number = 0;\r\n    type: number = 0;\r\n    image: string = \"\";\r\n    damageImage: string[] = [];\r\n    damagePos: Vec2 = Vec2.ZERO;\r\n    uiParam: number[] = [];\r\n    attr: string = \"\";\r\n    hp: number = 0;\r\n    hpParam: number[] = [];\r\n    collider = [];\r\n    recoil: boolean = false;\r\n    hpFire: number = 0;\r\n    hpFireParam  = [];\r\n    blastParam = [];\r\n    blastShake = [];\r\n    atkPos: Vec2[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载 Boss 单位数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        if (data.hasOwnProperty(\"id\")) this.id = parseInt(data.id);\r\n        if (data.hasOwnProperty(\"uId\")) this.uId = parseInt(data.uId);\r\n        if (data.hasOwnProperty(\"type\")) this.type = parseInt(data.type);\r\n        if (data.hasOwnProperty(\"img\")) this.image = data.img;\r\n        if (data.hasOwnProperty(\"dmg\") && data.dmg !== \"\") this.damageImage = data.dmg.split(\";\");\r\n        if (data.hasOwnProperty(\"dp\") && data.dp !== \"\") this.damagePos = Tools.stringToPoint(data.dp, \",\");\r\n        if (data.hasOwnProperty(\"ui\") && data.ui !== \"\") this.uiParam = Tools.stringToNumber(data.ui, \",\");\r\n        if (data.hasOwnProperty(\"attr\")) this.attr = data.attr;\r\n        if (data.hasOwnProperty(\"hp\")) this.hp = parseInt(data.hp);\r\n        if (data.hasOwnProperty(\"hpp\")) this.hpParam = Tools.stringToNumber(data.hpp, \",\");\r\n        if (data.hasOwnProperty(\"col\")) this.collider = Tools.stringToNumber(data.col, \",\");\r\n        if (data.hasOwnProperty(\"aa\")) this.recoil = parseInt(data.aa) === 1;\r\n        if (data.hasOwnProperty(\"fhp\")) this.hpFire = parseInt(data.fhp);\r\n        if (data.hasOwnProperty(\"fire\")) {\r\n            const fireArray = data.fire.split(\";\");\r\n            for (const fire of fireArray) {\r\n                if (fire !== \"\") this.hpFireParam.push(Tools.stringToNumber(fire, \",\"));\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"bla\")) {\r\n            const blaArray = data.bla.split(\";\");\r\n            for (const bla of blaArray) {\r\n                if (bla !== \"\") this.blastParam.push(Tools.stringToNumber(bla, \",\"));\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"sk\")) this.blastShake = Tools.stringToNumber(data.sk, \",\");\r\n        let atkIndex = 0;\r\n        while (true) {\r\n            const atkKey = \"ap\" + atkIndex++;\r\n            if (!data.hasOwnProperty(atkKey) || data[atkKey] === \"\") break;\r\n            this.atkPos.push(Tools.stringToPoint(data[atkKey], \",\"));\r\n        }\r\n    }\r\n}\r\n/**\r\n * Boss 子弹数据类\r\n */\r\nexport class BossBulletData {\r\n    soundId: number = 0;\r\n    shootInterval: number[] = [];\r\n    bulletIDs: number[] = [];\r\n    bulletNums: number[] = [];\r\n    bulletIntervals: number[] = [];\r\n    bulletAttackRates: number[] = [];\r\n    attackOverDelay: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载子弹数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\";\");\r\n        this.soundId = parseInt(parts[0]);\r\n        for (let i = 1; i < parts.length; i++) {\r\n            if (parts[i] !== \"\") {\r\n                const bulletData = Tools.stringToNumber(parts[i], \",\");\r\n                this.shootInterval.push(bulletData[0]);\r\n                this.bulletIDs.push(bulletData[1]);\r\n                this.bulletNums.push(bulletData[2]);\r\n                this.bulletIntervals.push(bulletData[3]);\r\n                this.bulletAttackRates.push(bulletData[4] / 100);\r\n                this.attackOverDelay.push(bulletData[5]);\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}