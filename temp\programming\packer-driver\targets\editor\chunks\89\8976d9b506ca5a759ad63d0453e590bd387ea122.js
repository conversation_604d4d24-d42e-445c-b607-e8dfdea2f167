System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, macro, Tween, UITransform, tween, UIOpacity, GameConst, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, PfFrameAnim;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      macro = _cc.macro;
      Tween = _cc.Tween;
      UITransform = _cc.UITransform;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bf6d729CTJDro5lWUU2Mvuo", "PfFrameAnim", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'Vec2', 'macro', 'Color', 'Size', 'Tween', 'UITransform', 'tween', 'UIOpacity']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", PfFrameAnim = (_dec = ccclass('PfFrameAnim'), _dec2 = property(Sprite), _dec(_class = (_class2 = class PfFrameAnim extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "sprite", _descriptor, this);

          this.atlas = null;
          // 图集
          this.imageSrc = '';
          // 图片前缀
          this.count = 0;
          // 帧数
          this.duration = 0;
          // 每帧持续时间
          this.call = null;
          // 播放完成回调
          this.imageArr = [];
          // 存储帧的数组
          this.playTimes = 0;
          // 当前播放次数
          this.maxPlayTimes = macro.REPEAT_FOREVER;
          // 最大播放次数
          this.time = 0;
          // 当前帧时间
          this.index = 0;
          // 当前帧索引
          this.willStop = false;
          // 是否即将停止
          this.selfUpdate = true;
          // 是否自动更新
          this.stopCall = null;
          // 停止时的回调
          this.resetCall = null;
          // 重置时的回调
          this.sWidth = -1;
          // 自定义宽度
          this.sHeight = -1;
        }

        // 自定义高度

        /**
         * 初始化帧动画
         * @param {SpriteAtlas} atlas 图集
         * @param {string} imageSrc 图片前缀
         * @param {number} count 帧数
         * @param {number} duration 每帧持续时间
         * @param {Function} call 播放完成回调
         * @param {Function} stopCall 停止回调
         * @param {Function} resetCall 重置回调
         */
        init(atlas, imageSrc, count, duration, call = null, stopCall = null, resetCall = null) {
          this.node.getComponent(UIOpacity).opacity = 0;
          this.atlas = atlas;
          this.imageSrc = imageSrc;
          this.count = count;
          this.duration = duration;
          this.call = call;
          this.resetCall = resetCall;
          this.stopCall = stopCall;

          for (let i = 0; i < count; i++) {
            this.imageArr.push(this.atlas.getSpriteFrame(imageSrc + i));
          }

          if (!this.sprite) {
            this.sprite = this.node.addComponent(Sprite);
            this.sprite.sizeMode = Sprite.SizeMode.RAW;
            this.sprite.trim = false;
          }
        }
        /**
         * 设置混合模式
         */


        dstBlendFactor() {// this.sprite.dstBlendFactor = macro.ONE;
        }
        /**
         * 设置大小
         * @param {Size} size 大小
         * @param {number} mode 模式（1: 自定义大小，2: 切片模式）
         */


        setSize(size, mode = 1) {
          if (mode === 1) {
            this.sprite.sizeMode = Sprite.SizeMode.CUSTOM;
            this.node.getComponent(UITransform).setContentSize(size);
          } else if (mode === 2) {
            this.imageArr.forEach(frame => {
              frame.insetBottom = 20;
              frame.insetTop = 20;
              frame.insetLeft = 20;
              frame.insetRight = 20;
            });
            this.sprite.type = Sprite.Type.SLICED;
            this.sprite.sizeMode = Sprite.SizeMode.CUSTOM;
            this.node.getComponent(UITransform).setContentSize(size);
          }
        }
        /**
         * 设置颜色
         * @param {Color} color 颜色
         */


        setColor(color) {
          this.sprite.node.color = color;
        }
        /**
         * 重置动画
         * @param {number} maxPlayTimes 最大播放次数
         */


        reset(maxPlayTimes = 0) {
          this.playTimes = 0;
          this.maxPlayTimes = maxPlayTimes > 0 ? maxPlayTimes : macro.REPEAT_FOREVER;
          this.time = 0;
          this.index = 0;
          this.sprite.spriteFrame = this.imageArr[0];
          this.node.getComponent(UIOpacity).opacity = 255;

          if (this.resetCall) {
            this.resetCall();
          }

          this.willStop = false;
        }
        /**
         * 停止动画
         * @param {boolean} fadeOut 是否渐隐停止
         */


        stop(fadeOut = false) {
          if (fadeOut) {
            Tween.stopAllByTarget(this.node);
            tween(this.node.getComponent(UIOpacity)).to(4 * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ActionFrameTime, {
              opacity: 0
            }).start();
          } else {
            this.node.getComponent(UIOpacity).opacity = 0;
          }

          if (this.stopCall) {
            this.stopCall();
          }

          this.willStop = false;
        }
        /**
         * 设置即将停止
         */


        setWillStop() {
          this.willStop = true;
        }
        /**
         * 检查是否正在播放
         * @returns {boolean} 是否正在播放
         */


        isPlaying() {
          return this.node.getComponent(UIOpacity).opacity > 100;
        }
        /**
         * 更新动画逻辑
         * @param {number} dt 时间增量
         */


        updateGameLogic(dt) {
          if (this.node.getComponent(UIOpacity).opacity > 0) {
            this.time += dt;

            if (this.time >= this.duration) {
              this.index++;

              if (this.index >= this.count) {
                this.playTimes++;
                this.index = 0;

                if (this.call) {
                  this.call();
                }

                if (this.willStop) {
                  this.stop();
                }

                if (this.playTimes >= this.maxPlayTimes) {
                  this.stop();
                }
              } else {
                this.time = 0;
                this.sprite.spriteFrame = this.imageArr[this.index];

                if (this.sWidth >= 0) {
                  this.sprite.node.width = this.sWidth;
                }

                if (this.sHeight >= 0) {
                  this.sprite.node.height = this.sHeight;
                }
              }
            }
          }
        }
        /**
         * 每帧更新
         * @param {number} dt 时间增量
         */


        update(dt) {
          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            this.updateGameLogic(dt);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "sprite", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8976d9b506ca5a759ad63d0453e590bd387ea122.js.map