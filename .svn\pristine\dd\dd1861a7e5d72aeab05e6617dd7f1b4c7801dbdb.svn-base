import { _decorator, Component, EventTouch, Label, Node } from 'cc';
import { DataEvent } from 'db://assets/scripts/Data/DataEvent';
import { DataMgr } from 'db://assets/scripts/Data/DataManager';
import { EventMgr } from 'db://assets/scripts/event/EventManager';
import { MyApp } from 'db://assets/scripts/MyApp';
import { ButtonPlus } from '../../../../common/components/button/ButtonPlus';
import { UIMgr } from '../../../../UIMgr';
import { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';
import { PlaneUIEvent } from '../../PlaneEvent';
import { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';
const { ccclass, property } = _decorator;

@ccclass('EquipDisplay')
export class EquipDisplay extends Component {
    @property([ButtonPlus])
    private equipBtns: ButtonPlus[] = []

    protected onLoad(): void {
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)
        EventMgr.on(DataEvent.EquipSlotRefresh, this.onEquipSlotRefresh, this)
        this.equipBtns.forEach(v => v.addClick(this.onClickEquip, this))
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    private onTabChange(tabStatus: TabStatus) {
        if (tabStatus == TabStatus.Bag) {
            this.node.active = true
            this.onEquipDisplayRefresh();
        } else {
            this.node.active = false
        }
    }

    private onEquipSlotRefresh() {
        this.onEquipDisplayRefresh()
        DataMgr.bag.refreshItems();
        UIMgr.hideUI(PlaneEquipInfoUI)
    }

    private onEquipDisplayRefresh() {
        const TbEquip = MyApp.lubanTables.TbEquip
        DataMgr.equip.eqSlots.slots.forEach(v => {
            const btn = this.equipBtns[v.slot_id - 1]
            if (v.guid.gt(0)) {
                btn.getComponentInChildren(Label).string = TbEquip.get(v.equip_id)?.name
            } else {
                btn.getComponentInChildren(Label).string = '空'
            }
        })
    }

    private onClickEquip(event: EventTouch): void {
        const btnNode = event.target as Node;
        const slotID = parseInt(btnNode.name)
        const info = DataMgr.equip.eqSlots.getEquipSlotInfo(slotID)
        if (!info || info.guid.lte(0)) return
        UIMgr.openUI(PlaneEquipInfoUI, info, OpenEquipInfoUISource.DisPlay)
    }
}