System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Sprite, Vec2, tween, v2, misc, instantiate, UIOpacity, UITransform, v3, EnemyBase, Tools, GameIns, GameEnum, EnemyShootComponent, EnemyShootData, TrackComponent, GameConst, EnemyPlaneRole, PfFrameAnim, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, property, EnemyPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEnemyBase(extras) {
    _reporterNs.report("EnemyBase", "./EnemyBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyShootComponent(extras) {
    _reporterNs.report("EnemyShootComponent", "./EnemyShootComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneData(extras) {
    _reporterNs.report("EnemyPlaneData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyShootData(extras) {
    _reporterNs.report("EnemyShootData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyUIData(extras) {
    _reporterNs.report("EnemyUIData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneRole(extras) {
    _reporterNs.report("EnemyPlaneRole", "./EnemyPlaneRole", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPfFrameAnim(extras) {
    _reporterNs.report("PfFrameAnim", "../../base/PfFrameAnim", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "../../../data/EnemyWave", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      Vec2 = _cc.Vec2;
      tween = _cc.tween;
      v2 = _cc.v2;
      misc = _cc.misc;
      instantiate = _cc.instantiate;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      EnemyBase = _unresolved_2.default;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameEnum = _unresolved_5.default;
    }, function (_unresolved_6) {
      EnemyShootComponent = _unresolved_6.default;
    }, function (_unresolved_7) {
      EnemyShootData = _unresolved_7.EnemyShootData;
    }, function (_unresolved_8) {
      TrackComponent = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      EnemyPlaneRole = _unresolved_10.default;
    }, function (_unresolved_11) {
      PfFrameAnim = _unresolved_11.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0a450DDnFNJmY3HY0he9lIp", "EnemyPlane", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Sprite', 'Vec2', 'ParticleSystem', 'tween', 'v2', 'misc', 'instantiate', 'UIOpacity', 'ParticleSystem2D', 'UITransform', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlane = (_dec = ccclass('EnemyPlane'), _dec2 = property(_crd && EnemyPlaneRole === void 0 ? (_reportPossibleCrUseOfEnemyPlaneRole({
        error: Error()
      }), EnemyPlaneRole) : EnemyPlaneRole), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(Sprite), _dec(_class = (_class2 = (_class3 = class EnemyPlane extends (_crd && EnemyBase === void 0 ? (_reportPossibleCrUseOfEnemyBase({
        error: Error()
      }), EnemyBase) : EnemyBase) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "role", _descriptor, this);

          _initializerDefineProperty(this, "hpBg", _descriptor2, this);

          _initializerDefineProperty(this, "hpSpr", _descriptor3, this);

          _initializerDefineProperty(this, "hpWhite", _descriptor4, this);

          this._data = null;
          this._trackCom = null;
          this._shootCom = null;
          this._curAction = 0;
          this._removeCount = 0;
          this._removeTime = 0;
          this._curAngle = 0;
          this._destAngle = null;
          this._curDir = Vec2.ZERO;
          this._destDir = Vec2.ZERO;
          this._rotateSpeed = 0;
          this._leaveAct = -1;
          this._roleIndex = 1;
          this._curFormIndex = 0;
          this._curTrackType = -1;
          this._dieAnimEnd = false;
          this._initAngle = true;
          this._hpWhiteTween = null;
          this._bDieShoot = false;
          this.m_outTime = 0;
          this.dir = void 0;
        }

        preLoad() {
          super.preLoad();
          this._trackCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && TrackComponent === void 0 ? (_reportPossibleCrUseOfTrackComponent({
            error: Error()
          }), TrackComponent) : TrackComponent);
          this._shootCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && EnemyShootComponent === void 0 ? (_reportPossibleCrUseOfEnemyShootComponent({
            error: Error()
          }), EnemyShootComponent) : EnemyShootComponent);
        }

        preLoadUI(data) {
          this.role.preLoadUI(data);
        }

        async init(data) {
          this._reset();

          this._data = data;
          this.dieBullet = data.dieBullet;
          this.setUIData((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.getEnemyUIData(this._data.uiId));

          this._refreshProperty();

          await this._refreshUI();

          this._refreshColliders();

          this.setDir(v2(0, -1));
          this.setAngle(0);
          this.initAttr(data.attr); // if (this.hasAttribution(GameEnum.EnemyAttr.Shield)) {
          //     this.role.setEventCallback('shield', () => {
          //         this.showAttrShield();
          //     });
          // } else {
          //     this.role.setEventCallback('shield', null);
          // }
        }

        _reset() {
          super.reset();
          this._curAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAction.Track;
          this._removeTime = 0;
          this._removeCount = 0;
          this._dieAnimEnd = false;
          this._bDieShoot = false;
          this._curFormIndex = 0;
          this._curTrackType = -1;
          this._curAngle = 0;
          this._destAngle = null;
          this._curDir = Vec2.ZERO;
          this._destDir = Vec2.ZERO;
          this._rotateSpeed = 0;
          this._leaveAct = -1;
          this._initAngle = true;
          this.m_outTime = 0;
        }
        /**
         * 初始化组件
         */


        initComps() {
          // 调用父类的组件初始化方法
          super.initComps(); // 判断是否为特殊敌机（ID范围在50000到60000之间）

          const isSpecialEnemy = this._data.id >= 50000 && this._data.id < 60000; // 初始化射击组件

          this._shootCom.init(this, isSpecialEnemy ? this.role.role.node : this.node, null, isSpecialEnemy); // 设置攻击开始的回调


          this._shootCom.setAtkStartCall(() => {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackPrepare);
          }); // 设置攻击结束的回调


          this._shootCom.setAtkOverCall(() => {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackOver);
          });
        }
        /**
         * 初始化轨迹
         * @param {TrackData} trackData 轨迹数据
         * @param {number} offsetX X 轴偏移
         * @param {number} offsetY Y 轴偏移
         * @param {boolean} isLoop 是否循环
         * @param {number} rotateSpeed 旋转速度
         */


        initTrack(trackData, trackParams, offsetX, offsetY, rotateSpeed = 0) {
          this._rotateSpeed = rotateSpeed;

          this._trackCom.init(this, trackData, trackParams, offsetX, offsetY); // 设置轨迹的各种回调


          this._trackCom.setTrackGroupStartCall((track, groupType, groupIndex) => {});

          this._trackCom.setTrackGroupOverCall(groupType => {
            if (this.uiData.isAm && groupType === 0) {
              this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Transform);
            }
          });

          this._trackCom.setTrackOverCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver);
          });

          this._trackCom.setTrackLeaveCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
          });

          this._trackCom.setTrackStartCall(trackType => {
            this.setTrackType(trackType);
          });
        }
        /**
        * 设置首次射击的延迟时间
        * @param {number} delay 延迟时间
        */


        setFirstShootDelay(delay) {
          this._shootCom.setFirstShootDelay(delay);
        }
        /**
         * 开始战斗
         */


        startBattle() {
          super.startBattle();
          this.active = true;

          this._refreshHpBar();

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          super.updateGameLogic(deltaTime);

          if (this.isDead) {
            if (this._bDieShoot) {
              this._shootCom.updateGameLogic(deltaTime);
            } else {
              this._checkRemoveAble(deltaTime);
            }
          } else if (!this.isStandBy()) {
            this.role.updateGameLogic(deltaTime);

            if (!this.hasHurtBuff((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyBuff.Ice)) {
              this._trackCom.updateGameLogic(deltaTime);

              this._shootCom.updateGameLogic(deltaTime);

              this._updateAction(deltaTime);
            }
          }
        }
        /**
         * 更新当前行为
         * @param {number} deltaTime 帧间隔时间
         */


        _updateAction(deltaTime) {
          this._shootCom.setNextAble(false);

          switch (this._curAction) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Sneak:
              this._updateCurDir(deltaTime);

              this.collideAble = false;
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Track:
              this._updateCurDir(deltaTime);

              this._shootCom.setNextAble(this._trackCom.isMoving && this._data.bMoveAttack || !this._trackCom.isMoving && this._data.bStayAttack);

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Transform:
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackIng:
              this._updateCurDir(deltaTime);

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Leave:
              if (this._leaveAct === 0) {
                this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver);
              } else if (this._leaveAct > 0) {
                this._updateCurDir(deltaTime);
              }

              break;
          }
        }
        /**
         * 检查是否可以移除
         * @param {number} deltaTime 帧间隔时间
         */


        _checkRemoveAble(deltaTime) {
          if (this._dieAnimEnd) {
            this._removeCount += deltaTime;

            if (this._removeCount > this._removeTime) {
              this.removeAble = true;
            }
          }
        }
        /**
         * 设置方向
         * @param {Vec2} dir 方向向量
         */


        setDir(dir) {
          this.dir = dir;

          if (this._data.bTurnDir) {
            this.setAngle(-(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getDegreeForDir(dir));
          }
        }
        /**
         * 获取当前方向
         * @returns {Vec2} 当前方向向量
         */


        getDir() {
          return this._curDir;
        }
        /**
         * 设置目标方向
         * @param {Vec2} dir 目标方向向量
         */


        setDestDir(dir) {
          this._destDir = dir;

          if (this._initAngle) {
            this._initAngle = false;
            this.setDir(dir);
          } else if (this._rotateSpeed <= 0) {
            this.setDir(dir);
          }
        }
        /**
         * 设置当前角度
         * @param {number} angle 当前角度
         */


        setAngle(angle) {
          if (angle > 360) {
            angle -= 360;
          } else if (angle < -360) {
            angle += 360;
          }

          this._curAngle = angle;
          this.role.node.angle = angle;
        }
        /**
         * 设置目标角度
         * @param {number} angle 目标角度
         */


        setDestAngle(angle) {
          if (this._rotateSpeed > 0) {
            let delta = angle - this._curAngle;

            while (Math.abs(delta) > 360) {
              angle += angle > this._curAngle ? -360 : 360;
              delta = angle - this._curAngle;
            }

            if (Math.abs(delta) > 180) {
              angle += angle > this._curAngle ? -360 : 360;
            }

            this._destAngle = angle;
          }
        }
        /**
         * 检查是否可以存活
         * @returns {boolean} 是否可以存活
         */


        checkLiveAble() {
          if (this._curAction === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAction.Track) {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Leave);
            return true;
          }

          return false;
        }
        /**
         * 设置形态索引
         * @param {number} index 形态索引
         */


        setFormIndex(index) {
          this._curFormIndex = index;

          if (this._curFormIndex >= 0 && this._data.bAttackAbles[this._curFormIndex]) {
            const shootData = new (_crd && EnemyShootData === void 0 ? (_reportPossibleCrUseOfEnemyShootData({
              error: Error()
            }), EnemyShootData) : EnemyShootData)();
            shootData.attackInterval = this._data.attackInterval;
            shootData.attackNum = this._data.attackNum;
            shootData.attackPointArr = this._data.attackPointArr[this._curFormIndex];
            shootData.attackArrNum = shootData.attackPointArr.length;

            this._shootCom.setShootData(shootData);
          } else {
            this._shootCom.setShootData(null);
          }
        }
        /**
         * 设置敌机的轨迹类型
         * @param trackData 轨迹数据
         */


        setTrackType(trackData) {
          if (!trackData) {
            return;
          }

          const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime;

          switch (trackData.type) {
            case 2:
              // 渐变出现
              this._trackCom.setTrackAble(false);

              this.collideAble = false;
              let tail = this.node.getChildByName("tail");

              if (!tail) {
                tail = new Node();
                this.node.addChild(tail);
                (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).enemyManager.setPlaneFrame(tail.addComponent(Sprite), "ashow_0");
                tail.getComponent(UITransform).anchorY = 0;
              }

              tail.active = true;
              tail.setScale(1, 2.2);
              tween(tail).delay(0.02).to(5 * frameTime, {
                scale: v3(1, 2.8)
              }).to(2 * frameTime, {
                scale: v3(1, 0.5)
              }).to(frameTime, {
                scale: v3(1, 0)
              }).call(() => {
                tail.active = false;
              }).start();
              this.scheduleOnce(() => {
                this._playTailEffects(trackData, frameTime);
              }, 7 * frameTime);
              tween(this.node).to(7 * frameTime, {
                position: v3(trackData.endX + this._trackCom.trackOffsetX, trackData.endY + this._trackCom.trackOffsetY)
              }).call(() => {
                this.setPos(trackData.endX + this._trackCom.trackOffsetX, trackData.endY + this._trackCom.trackOffsetY);
                this.collideAble = true;
              }).delay(0.2).call(() => {
                this._trackCom.trackFinish = true;

                this._trackCom.setTrackAble(true);
              }).start();
              break;

            case 3:
              // 瞬间出现
              this._trackCom.setTrackAble(false);

              this.collideAble = false;
              this.setPos(trackData.endX + this._trackCom.trackOffsetX, trackData.endY + this._trackCom.trackOffsetY);

              const appearNode = this._createAppearEffect(frameTime);

              this.scheduleOnce(() => {// this.role.opacity = 255;
                // this.role.blueShow(() => {
                //     this._trackCom.trackFinish = true;
                //     this.collideAble = true;
                //     this._trackCom.setTrackAble(true);
                //     // this.hpBg.node.opacity = this._data.hpParam === 1 ? 255 : 0;
                // });
              }, 2 * frameTime);
              break;

            case 4:
            case 5:
              // 隐身或显现
              if (this._curTrackType !== 4 && this._curTrackType !== 5) {
                this._trackCom.setTrackAble(false);

                this._shootCom.active = false;
                this.collideAble = false; // this.hpBg.node.opacity = 0;
                // this.role.playCloakeHideAnim(() => {
                //     this._trackCom.setTrackAble(true);
                // });
              } else {
                this._shootCom.active = false;

                this._trackCom.setTrackAble(false); // this.role.playCloakeShowAnim(() => {
                //     this._shootCom.active = true;
                //     this._trackCom.setTrackAble(true);
                //     this.collideAble = true;
                //     // this.hpBg.node.opacity = 255;
                // });

              }

              break;

            default:
              if (this._curTrackType === 4 || this._curTrackType === 5) {
                this._shootCom.active = false;

                this._trackCom.setTrackAble(false); // this.role.playCloakeShowAnim(() => {
                //     this._shootCom.active = true;
                //     this._trackCom.setTrackAble(true);
                //     this.collideAble = true;
                //     // this.hpBg.node.opacity = 255;
                // });

              }

              break;
          }

          this._curTrackType = trackData.type;
        }
        /**
         * 播放尾部特效
         * @param trackData 轨迹数据
         * @param frameTime 每帧时间
         */


        _playTailEffects(trackData, frameTime) {
          const tailEffects = ["tail2", "tail1", "tail0"];
          const tailFrames = ["ashow_1", "ashow_2", "ashow_3"];
          const scales = [1.8, 1.55, {
            scaleX: 2.2,
            scaleY: 0.5
          }];
          tailEffects.forEach((name, index) => {
            let tail = this.node.getChildByName(name);

            if (!tail) {
              tail = new Node();
              tail.name = name;
              this.node.addChild(tail);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(tail.addComponent(Sprite), tailFrames[index]);
            }

            tail.active = true; // tail.opacity = 255;

            tail.setScale(1, 1);
            let scale = typeof scales[index] === "object" ? v3(scales[index].scaleX, scales[index].scaleY) : v3(scales[index], scales[index]);
            typeof scale;
            tween(tail).to(5 * frameTime, {
              scale: scale
            }) // .to(5 * frameTime, { opacity: 0 })
            .call(() => {
              tail.active = false;
            }).start();
          });
        }
        /**
         * 创建瞬间出现的特效
         * @param frameTime 每帧时间
         * @returns 创建的特效节点
         */


        _createAppearEffect(frameTime) {
          let appearNode = this.node.getChildByName("appear");

          if (!appearNode) {
            appearNode = instantiate((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameResManager.frameAnim);
            appearNode.name = "appear";
            this.node.addChild(appearNode);
            const frameAnim = appearNode.getComponent(_crd && PfFrameAnim === void 0 ? (_reportPossibleCrUseOfPfFrameAnim({
              error: Error()
            }), PfFrameAnim) : PfFrameAnim);
            frameAnim.init((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.enemyAtlas, "as_", 13, frameTime, () => {
              frameAnim.stop();
              appearNode.active = false;
            });
          }

          appearNode.active = true;

          if (this.uiData.showParam.length > 3) {
            appearNode.setScale(this.uiData.showParam[0], this.uiData.showParam[1]);
            appearNode.setPosition(this.uiData.showParam[2], this.uiData.showParam[3]);
          } // BattleManager.audioManager.playEffect("e_appear_3");


          appearNode.getComponent(_crd && PfFrameAnim === void 0 ? (_reportPossibleCrUseOfPfFrameAnim({
            error: Error()
          }), PfFrameAnim) : PfFrameAnim).reset();
          return appearNode;
        }
        /**
         * 设置敌机的当前行为
         * @param {number} action 行为类型（枚举值）
         */


        setAction(action) {
          if (this._curAction !== action) {
            this._curAction = action; // 停止射击并启用轨迹

            this._shootCom.setIsShooting(false);

            this._trackCom.setTrackAble(true);

            switch (this._curAction) {
              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Sneak:
                // 潜行行为
                this.hpBg.node.getComponent(UIOpacity).opacity = 0; // this.role.playSneakAnim();

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Track:
                // 跟踪行为
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Transform:
                // 变形行为
                this._trackCom.setTrackAble(false);

                this._shootCom.stopShoot();

                this._roleIndex++;
                this.role.playAnim("transform", () => {
                  this.role.playAnim("idle" + this._roleIndex, null);
                  this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                    error: Error()
                  }), GameEnum) : GameEnum).EnemyAction.Track);

                  this._shootCom.setNextShootAtOnce();
                }) || (this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track), this._shootCom.setNextShootAtOnce());
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
                // 准备攻击行为
                this.playAtkAnim();
                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.AttackIng);
                this.role.startAttack();
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackIng:
                // 攻击中行为
                this._shootCom.startShoot();

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackOver:
                // 攻击结束行为
                this.role.attackOver();
                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track);
                break;

              default:
                break;
            }
          }
        }
        /**
         * 更新当前方向
         * @param {number} deltaTime 帧间隔时间
         */


        _updateCurDir(deltaTime) {
          if (this._data.bTurnDir && this._destAngle !== null && this._destAngle !== this._curAngle) {
            const direction = this._destAngle > this._curAngle ? 1 : -1;
            const angleChange = deltaTime * this._rotateSpeed * direction;
            let newAngle = this._curAngle + angleChange;

            if (direction > 0) {
              if (newAngle > this._destAngle) newAngle = this._destAngle;
            } else {
              if (newAngle < this._destAngle) newAngle = this._destAngle;
            }

            const rotatedDir = v2(this._curDir.x, this._curDir.y).rotate(misc.degreesToRadians(angleChange));
            this.dir = rotatedDir; // 使用旋转后的新向量

            this.setAngle(newAngle);
          }
        }
        /**
         * 刷新敌机的属性
         */


        _refreshProperty() {
          this.curHp = this._data.hp;
          this.maxHp = this._data.hp;
          this.defence = this._data.defence;
          this.attack = this._data.attack;
          this.collideLevel = this._data.collideLevel;
          this.collideAtk = this._data.collideAttack;
          this.bCollideDead = this._data.bCollideDead;
        }
        /**
         * 刷新敌机的 UI
         * @param {boolean} isRespawn 是否为重生
         */


        async _refreshUI(isRespawn = false) {
          await this.role.init(this.uiData, this, this._data.param);

          if (!isRespawn) {
            try {
              this.hpBg.node.getComponent(UIOpacity).opacity = 255;
              this.hpBg.node.setPosition(this.uiData.hpParam[0], this.uiData.hpParam[1]);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.hpBg, `hp${this.uiData.hpParam[2]}_0`);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.hpSpr, `hp${this.uiData.hpParam[2]}_1`);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.hpWhite, `hp${this.uiData.hpParam[2]}_2`);
            } catch (error) {// this.hpBg.node.y = this.icon.node.height >> 1;
            }
          }
        }
        /**
         * 刷新敌机的碰撞器
         */


        _refreshColliders() {
          let scale = this.role.node.scale.x;
          const colliderData = this.uiData.collider;
          this.setCollideData([colliderData[0], colliderData[1] * scale, colliderData[2] * scale, colliderData[3] * scale, colliderData[4] * scale]);
        }
        /**
        * 刷新血条
        */


        _refreshHpBar() {
          const hpRatio = this.curHp / this.maxHp;
          const isDecreasing = hpRatio < this.hpSpr.fillRange; // 更新血条显示

          this.hpSpr.fillRange = hpRatio; // 停止之前的血条动画

          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          } // 如果血量减少，播放白色血条的动画


          if (isDecreasing) {
            const duration = Math.abs(this.hpWhite.fillRange - this.hpSpr.fillRange);
            this._hpWhiteTween = tween(this.hpWhite).to(duration, {
              fillRange: this.hpSpr.fillRange
            }).call(() => {
              this._hpWhiteTween = null;
            }).start();
          } else {
            this.hpWhite.fillRange = hpRatio;
          }
        }
        /**
         * 受到伤害时的处理
         */


        onHurt() {
          this._refreshHpBar();

          this.checkHp();

          if (!this.isDead) {// this.role.winkWhite();
          }
        }
        /**
         * 检查血量
         * @returns {boolean} 是否死亡
         */


        checkHp() {
          if (super.checkHp()) {
            return true;
          }

          return false;
        }
        /**
         * 移除 Buff 时的处理
         * @param {number} buffType Buff 类型
         */


        onRemoveBuff(buffType) {
          super.onRemoveBuff(buffType); // if (buffType === GameEnum.EnemyBuff.Ice) {
          //     this.role.resumeAnim();
          // }
        }
        /**
         * 添加 Buff 时的处理
         * @param {number} buffType Buff 类型
         */


        onAddBuff(buffType) {
          super.onAddBuff(buffType); // if (buffType === GameEnum.EnemyBuff.Ice) {
          //     this.role.pauseAnim();
          // }
        }
        /**
         * 敌机死亡时的处理
         * @param {number} destroyType 销毁类型
         */


        onDie(destroyType) {
          super.onDie(destroyType);
          this.hpBg.node.getComponent(UIOpacity).opacity = 0;
          this.willRemove();

          switch (destroyType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die:
              this.playDieAnim();

              this._checkDieShoot();

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver:
              this._dieAnimEnd = true;
              break;
          }
        }
        /**
         * 准备移除敌机
         */


        willRemove() {
          // if (this.role) {
          //     this.role.stopAnim();
          // }
          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          }

          this.hpWhite.fillRange = 0;
        }
        /**
         * 检查死亡时是否需要射击
         */


        _checkDieShoot() {// if (this._data.dieShoot.length > 0 && !Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {
          //     this._bDieShoot = true;
          //     const shootData = new EnemyShootData();
          //     shootData.attackInterval = this._data.attackInterval;
          //     shootData.attackNum = 1;
          //     shootData.attackArrNum = 1;
          //     shootData.attackPointArr = [this._data.dieShoot];
          //     this._shootCom.setShootData(shootData);
          //     this._shootCom.setAtkOverCall(() => {
          //         this._bDieShoot = false;
          //         this.role.getComponent(UIOpacity).opacity = 0;
          //     });
          //     this._shootCom.setNextShoot();
          // }
        }
        /**
         * 播放攻击动画
         */


        playAtkAnim() {
          this.role.playAnim(`atk${this._roleIndex}`, () => {
            this.role.playAnim(`idle${this._roleIndex}`, null);
          });
        }
        /**
         * 播放死亡动画
         */


        playDieAnim() {
          super.playDieAnim();
          this.scheduleOnce(() => {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).removeChildByName(this.node, "fire");
            this.checkLoot(); // this.role.getComponent(UIOpacity).opacity = 0;
          }, 0.1);
        }
        /**
         * 死亡动画结束时的处理
         */


        onDieAnimEnd() {
          this._dieAnimEnd = true;
        }
        /**
         * 获取当前角度
         * @returns {number} 当前角度
         */


        get angle() {
          return this.role ? this.role.node.angle : 0;
        }
        /**
         * 设置当前角度
         * @param {number} value 角度值
         */


        set angle(value) {
          if (this.role) {
            this.role.node.angle = value;
          }
        }
        /**
         * 设置角色透明度
         * @param {number} opacity 透明度值
         */


        setRoleOpacity(opacity) {// if (this.role) {
          //     this.role.opacity = opacity;
          // }
        }
        /**
         * 获取子弹发射的角度
         * @returns {number} 子弹发射角度
         */


        getFireBulletAngle() {
          // if (this._data.id >= 50000 && this._data.id < 60000) {
          //     return this.role.role.node.angle;
          // }
          return 0;
        }
        /**
         * 检查敌机是否在屏幕内
         * @param {number} deltaTime 帧间隔时间
         */


        checkInScreen(deltaTime) {
          if (this.itemParent === this) {
            if ((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {
              this.m_outTime += deltaTime;

              if (this.m_outTime > 10) {
                this.isDead = true;
                this.removeAble = true;
              }
            } else {
              this.m_outTime = 0;
            }
          }
        }

      }, _class3.PrefabName = 'EnemyPlane', _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "role", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "hpBg", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpSpr", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "hpWhite", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0e075f664a7f46e3e9c8b454b52c8229d823e8a1.js.map