import { _decorator, Component, Label, Node } from 'cc';
import List from '../../common/components/list/List';
import { FriendCellUI } from './FriendCellUI';
const { ccclass, property } = _decorator;

@ccclass('FriendListUI')
export class FriendListUI extends Component {

    @property(List)
    list: List;

    start() {
        this.list.node.active = true;
        this.list.numItems = 10;
    }

    update(deltaTime: number) {

    }

    onListRender(listItem: Node, row: number) {
        const cell = listItem.getComponent(FriendCellUI);
        if (cell !== null) {
            cell.setType(1);
            cell.txtName.string = "小师妹：" + (row + 1);
        }
    }
}


