{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/GameMapData.ts"], "names": ["GameMapData", "Vec2", "Tools", "loadSprite", "Map", "loadImageSque", "speed", "speeds", "layers", "PosInfo", "nodeMove", "nodeAngle", "triggerTime", "ZERO", "timeInterval", "frameTime", "loopTimes", "nowTimes", "nowTriggerTime", "tempY", "tempH", "nowUseNode", "freeNode", "index", "itemIndex", "spriteName", "starPos", "turePosoffSet", "ViewBot", "ViewMid", "ViewTop", "scale", "clear", "splice", "getTiggerTime", "Math", "random", "y", "x", "getTimeInterval", "random_int"], "mappings": ";;;4FAKq<PERSON>,W;;;;;;;;;;;;;;;AALOC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,K,iBAAAA,K;;;;;;;;;yBAIYF,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,eAE7BG,UAF6B,GAEhB,IAAIC,GAAJ,EAFgB;AAEgB;AAFhB,eAG7BC,aAH6B,GAGb,IAAID,GAAJ,EAHa;AAGF;AAHE,eAI7BE,KAJ6B,GAIrB,CAJqB;AAIlB;AAJkB,eAK7BC,MAL6B,GAKpB,EALoB;AAKhB;AALgB,eAM7BC,MAN6B,GAMpB,EANoB;AAMhB;AANgB,eAO7BC,OAP6B,GAOnB,EAPmB;AAOf;AAPe,eAQ7BC,QAR6B,GAQlB,EARkB;AAQd;AARc,eAS7BC,SAT6B,GASjB,EATiB;AASb;AATa,eAU7BC,WAV6B,GAUfX,IAAI,CAACY,IAVU;AAUJ;AAVI,eAW7BC,YAX6B,GAWdb,IAAI,CAACY,IAXS;AAWH;AAXG,eAY7BE,SAZ6B,GAYjB,CAZiB;AAYd;AAZc,eAa7BC,SAb6B,GAajB,CAbiB;AAad;AAbc,eAc7BC,QAd6B,GAclB,CAdkB;AAcf;AAde,eAe7BC,cAf6B,GAeZ,CAfY;AAeT;AAfS,eAgB7BC,KAhB6B,GAgBrB,CAhBqB;AAgBlB;AAhBkB,eAiB7BC,KAjB6B,GAiBrB,CAjBqB;AAiBlB;AAjBkB,eAkB7BC,UAlB6B,GAkBH,EAlBG;AAkBC;AAlBD,eAmB7BC,QAnB6B,GAmBlB,EAnBkB;AAmBd;AAnBc,eAoB7BC,KApB6B,GAoBrB,CApBqB;AAoBlB;AApBkB,eAqB7BC,SArB6B,GAqBjB,CArBiB;AAqBd;AArBc,eAsB7BC,UAtB6B,GAsBhB,EAtBgB;AAsBZ;AAtBY,eAuB7BC,OAvB6B,GAuBnB,CAvBmB;AAuBhB;AAvBgB,eAwB7BC,aAxB6B,GAwBb,CAxBa;AAwBV;AAxBU,eAyB7BC,OAzB6B,GAyBnB,CAzBmB;AAyBhB;AAzBgB,eA0B7BC,OA1B6B,GA0BnB,CA1BmB;AA0BhB;AA1BgB,eA2B7BC,OA3B6B,GA2BnB,CA3BmB;AA2BhB;AA3BgB,eA4B7BC,KA5B6B,GA4BrB,CA5BqB;AAAA;;AA4BlB;;AAGX;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKrB,SAAL,GAAiB,EAAjB;AACA,eAAKD,QAAL,GAAgB,EAAhB;AACA,eAAKkB,OAAL,GAAe,CAAf;AACA,eAAKE,OAAL,GAAe,CAAf;AACA,eAAK3B,UAAL,CAAgB6B,KAAhB;AACA,eAAKT,KAAL,GAAa,CAAb;AACA,eAAKjB,KAAL,GAAa,CAAb;AACA,eAAKe,UAAL,CAAgBY,MAAhB,CAAuB,CAAvB;AACA,eAAKX,QAAL,CAAcW,MAAd,CAAqB,CAArB;AACA,eAAKT,SAAL,GAAiB,CAAjB;AACA,eAAKO,KAAL,GAAa,CAAb;AACA,eAAKtB,OAAL,GAAe,EAAf;AACA,eAAKiB,OAAL,GAAe,CAAf;AACA,eAAKnB,MAAL,GAAc,EAAd;AACA,eAAKkB,UAAL,GAAkB,EAAlB;AACA,eAAKE,aAAL,GAAqB,CAArB;AACA,eAAKf,WAAL,GAAmBX,IAAI,CAACY,IAAxB;AACA,eAAKC,YAAL,GAAoBb,IAAI,CAACY,IAAzB;AACA,eAAKE,SAAL,GAAiB,CAAjB;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKC,QAAL,GAAgB,CAAhB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACIgB,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKhB,cAAL,KAAwB,CAA5B,EAA+B;AAC3B,iBAAKA,cAAL,GAAsBiB,IAAI,CAACC,MAAL,MAAiB,KAAKxB,WAAL,CAAiByB,CAAjB,GAAqB,KAAKzB,WAAL,CAAiB0B,CAAvD,IAA4D,KAAK1B,WAAL,CAAiB0B,CAAnG;AACH;;AACD,iBAAO,KAAKpB,cAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIqB,QAAAA,eAAe,GAAG;AACd,iBAAO;AAAA;AAAA,8BAAMC,UAAN,CAAiB,KAAK1B,YAAL,CAAkBwB,CAAnC,EAAsC,KAAKxB,YAAL,CAAkBuB,CAAxD,CAAP;AACH;;AA5E4B,O", "sourcesContent": ["import { Node, SpriteFrame, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { logInfo } from \"../../Utils/Logger\";\r\n\r\n\r\nexport default class GameMapData {\r\n\r\n    loadSprite = new Map<string, SpriteFrame>(); // 加载的精灵\r\n    loadImageSque = new Map(); // 加载的图片队列\r\n    speed = 0; // 当前速度\r\n    speeds = []; // 不同层的速度\r\n    layers = []; // 地图层\r\n    PosInfo = []; // 位置信息\r\n    nodeMove = []; // 节点移动信息\r\n    nodeAngle = []; // 节点角度信息\r\n    triggerTime = Vec2.ZERO; // 触发时间范围\r\n    timeInterval = Vec2.ZERO; // 时间间隔范围\r\n    frameTime = 0; // 帧时间\r\n    loopTimes = 0; // 循环次数\r\n    nowTimes = 0; // 当前循环次数\r\n    nowTriggerTime = 0; // 当前触发时间\r\n    tempY = 0; // 临时 Y 坐标\r\n    tempH = 0; // 临时高度\r\n    nowUseNode: Array<Node> = []; // 当前使用的节点\r\n    freeNode = []; // 空闲节点\r\n    index = 0; // 当前索引\r\n    itemIndex = 0; // 项目索引\r\n    spriteName = []; // 精灵名称\r\n    starPos = 0; // 起始位置\r\n    turePosoffSet = 0; // 真实位置偏移\r\n    ViewBot = 0; // 视图底部\r\n    ViewMid = 0; // 视图中间\r\n    ViewTop = 0; // 视图顶部\r\n    scale = 0; // 缩放比例\r\n\r\n\r\n    /**\r\n     * 清空地图数据\r\n     */\r\n    clear() {\r\n        this.nodeAngle = [];\r\n        this.nodeMove = [];\r\n        this.ViewBot = 0;\r\n        this.ViewTop = 0;\r\n        this.loadSprite.clear();\r\n        this.index = 0;\r\n        this.speed = 0;\r\n        this.nowUseNode.splice(0);\r\n        this.freeNode.splice(0);\r\n        this.itemIndex = 0;\r\n        this.scale = 0;\r\n        this.PosInfo = [];\r\n        this.starPos = 0;\r\n        this.speeds = [];\r\n        this.spriteName = [];\r\n        this.turePosoffSet = 0;\r\n        this.triggerTime = Vec2.ZERO;\r\n        this.timeInterval = Vec2.ZERO;\r\n        this.frameTime = 0;\r\n        this.loopTimes = 0;\r\n        this.nowTimes = 0;\r\n        this.nowTriggerTime = 0;\r\n    }\r\n\r\n    /**\r\n     * 获取触发时间\r\n     * @returns {number} 当前触发时间\r\n     */\r\n    getTiggerTime() {\r\n        if (this.nowTriggerTime === 0) {\r\n            this.nowTriggerTime = Math.random() * (this.triggerTime.y - this.triggerTime.x) + this.triggerTime.x;\r\n        }\r\n        return this.nowTriggerTime;\r\n    }\r\n\r\n    /**\r\n     * 获取时间间隔\r\n     * @returns {number} 随机时间间隔\r\n     */\r\n    getTimeInterval() {\r\n        return Tools.random_int(this.timeInterval.x, this.timeInterval.y);\r\n    }\r\n}"]}