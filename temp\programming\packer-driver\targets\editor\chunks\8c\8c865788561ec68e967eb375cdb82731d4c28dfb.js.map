{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts"], "names": ["_decorator", "Component", "LevelBaseUI", "ccclass", "LevelElemUI", "elemID", "time", "layerNode", "node", "parent", "rootNode", "baseUI", "getComponent", "layer", "floorLayers", "find", "skyLayers", "position", "y", "speed", "onLoad", "uuid", "initByLevelData", "data", "setPosition", "x"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACZC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcH,U;;6BAGPI,W,WADZD,OAAO,CAAC,aAAD,C,gBAAR,MACaC,WADb,SACiCH,SADjC,CAC2C;AAAA;AAAA;AAAA,eAoBhCI,MApBgC,GAoBvB,EApBuB;AAAA;;AACxB,YAAJC,IAAI,GAAW;AAAA;;AACtB,gBAAMC,SAAS,wBAAG,KAAKC,IAAL,CAAUC,MAAb,qBAAG,kBAAkBA,MAApC;;AACA,cAAI,CAACF,SAAL,EAAgB;AACZ,mBAAO,CAAP;AACH;;AACD,gBAAMG,QAAQ,GAAGH,SAAS,CAACE,MAAV,CAAiBA,MAAlC;;AACA,cAAI,CAACC,QAAL,EAAe;AACX,mBAAO,CAAP;AACH;;AACD,gBAAMC,MAAM,GAAGD,QAAQ,CAACE,YAAT;AAAA;AAAA,yCAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACT,mBAAO,CAAP;AACH;;AACD,gBAAME,KAAK,GAAGF,MAAM,CAACG,WAAP,CAAmBC,IAAnB,CAAyBF,KAAD,IAAWA,KAAK,CAACL,IAAN,IAAcD,SAAjD,KAA+DI,MAAM,CAACK,SAAP,CAAiBD,IAAjB,CAAuBF,KAAD,IAAWA,KAAK,CAACL,IAAN,IAAcD,SAA/C,CAA7E;;AACA,cAAI,CAACM,KAAL,EAAY;AACR,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAKL,IAAL,CAAUS,QAAV,CAAmBC,CAAnB,GAAuBL,KAAK,CAACM,KAApC;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,KAAKf,MAAL,IAAe,EAAnB,EAAuB;AACnB,iBAAKA,MAAL,GAAc,KAAKgB,IAAnB;AACH;AACJ;;AACMC,QAAAA,eAAe,CAACC,IAAD,EAAsB;AACxC,eAAKf,IAAL,CAAUgB,WAAV,CAAsBD,IAAI,CAACN,QAAL,CAAcQ,CAApC,EAAuCF,IAAI,CAACN,QAAL,CAAcC,CAArD;AACA,eAAKb,MAAL,GAAckB,IAAI,CAAClB,MAAnB;AACH;;AA7BsC,O", "sourcesContent": ["import { _decorator, Component, Vec2 } from 'cc';\r\nimport { LevelBaseUI } from './LevelBaseUI';\r\nimport { LevelDataElem } from '../../../leveldata/leveldata';\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('LevelElemUI')\r\nexport class LevelElemUI extends Component {\r\n    public get time(): number {\r\n        const layerNode = this.node.parent?.parent\r\n        if (!layerNode) {\r\n            return 0;\r\n        }\r\n        const rootNode = layerNode.parent.parent\r\n        if (!rootNode) {\r\n            return 0;\r\n        }\r\n        const baseUI = rootNode.getComponent(LevelBaseUI)\r\n        if (!baseUI) {\r\n            return 0;\r\n        }\r\n        const layer = baseUI.floorLayers.find((layer) => layer.node == layerNode) || baseUI.skyLayers.find((layer) => layer.node == layerNode)\r\n        if (!layer) {\r\n            return 0;\r\n        }\r\n        return this.node.position.y / layer.speed;\r\n    }\r\n    public elemID = \"\";\r\n    protected onLoad(): void {\r\n        if (this.elemID == \"\") {\r\n            this.elemID = this.uuid;\r\n        }\r\n    }\r\n    public initByLevelData(data: LevelDataElem) {\r\n        this.node.setPosition(data.position.x, data.position.y);\r\n        this.elemID = data.elemID;\r\n    }\r\n}"]}