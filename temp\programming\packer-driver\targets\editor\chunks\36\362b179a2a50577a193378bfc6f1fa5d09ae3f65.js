System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseScreen, GameIns, BossEntity, _dec, _class, _crd, ccclass, property, CircleScreen;

  function _reportPossibleCrUseOfBaseScreen(extras) {
    _reporterNs.report("BaseScreen", "./BaseScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossEntity(extras) {
    _reporterNs.report("BossEntity", "../plane/boss/BossEntity", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseScreen = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      BossEntity = _unresolved_4.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "cdec4Bx7kxGW6bk/BkXL50b", "CircleScreen", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", CircleScreen = (_dec = ccclass("CircleScreen"), _dec(_class = class CircleScreen extends (_crd && BaseScreen === void 0 ? (_reportPossibleCrUseOfBaseScreen({
        error: Error()
      }), BaseScreen) : BaseScreen) {
        /**
         * 构造函数
         * @param config 配置数据
         * @param mainEntity 主实体
         * @param follow 是否跟随
         */
        constructor(config, mainEntity, follow = false) {
          super();
          this.m_follow = false;
          this.props = void 0;
          this.m_follow = follow;
          this.setData(config, mainEntity);
          const params = this.m_config.para;
          let bulletNum = params[0];
          const beginAngle = params[1];
          const endAngle = params[2];
          const radius = params[3] || 0;
          const posOffset = this.m_config.offset;
          this.props = {
            bulletNum,
            beginAngle,
            endAngle,
            radius,
            posOffset
          };
        }
        /**
         * 更新逻辑
         * @param deltaTime 时间增量
         */


        update(deltaTime) {// 可根据需求实现更新逻辑
        }
        /**
         * 初始化逻辑
         */


        onInit() {// 可根据需求实现初始化逻辑
        }
        /**
         * 发射子弹
         */


        async fire() {
          if (this.m_follow) {
            let target = null;

            if (this.m_enemy) {
              target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).mainPlaneManager.mainPlane;
            } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).planeManager.enemyTarget) {
              target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).planeManager.enemyTarget;
            }

            if (!target) return;
            if (target instanceof (_crd && BossEntity === void 0 ? (_reportPossibleCrUseOfBossEntity({
              error: Error()
            }), BossEntity) : BossEntity) && (target.isDead || !target.isDamageable())) return;
          }

          const attackPoint = this.getAttackPoint();
          const bulletNum = this.props.bulletNum;

          if (bulletNum === 1 && this.m_config.bustyle !== 37) {
            // 单发子弹逻辑
            const angle = (this.props.endAngle + this.props.beginAngle) / 2 + 90;
            let x = attackPoint.x;
            let y = attackPoint.y;

            if (this.props.radius !== 0) {
              x += Math.sin(angle / 180 * Math.PI) * this.props.radius;
              y += Math.cos(angle / 180 * Math.PI) * this.props.radius;
            }

            const bullet = await this.createBullet();

            if (bullet) {
              bullet.init(this.m_enemy, {
                x,
                y,
                angle
              }, this.m_bulletState, this.m_mainEntity);
            }
          } else {
            // 多发子弹逻辑
            const angleStep = (this.props.endAngle - this.props.beginAngle) / (bulletNum - 1);
            const x = attackPoint.x;
            const y = attackPoint.y;

            if (this.props.radius === 0) {
              // 没有半径，子弹从同一点发射
              for (let i = 0; i < bulletNum; i++) {
                const angle = this.props.beginAngle + angleStep * i - 90;
                const bullet = await this.createBullet();

                if (bullet) {
                  bullet.init(this.m_enemy, {
                    x,
                    y,
                    angle
                  }, this.m_bulletState, this.m_mainEntity);
                }
              }
            } else {
              // 有半径，子弹沿圆周发射
              for (let i = 0; i < bulletNum; i++) {
                const angle = this.props.beginAngle + angleStep * i + 90;
                const bullet = await this.createBullet();
                const bulletX = x + Math.sin(angle / 180 * Math.PI) * this.props.radius;
                const bulletY = y + Math.cos(angle / 180 * Math.PI) * this.props.radius;

                if (bullet) {
                  bullet.init(this.m_enemy, {
                    x: bulletX,
                    y: bulletY,
                    angle
                  }, this.m_bulletState, this.m_mainEntity);
                }
              }
            }
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=362b179a2a50577a193378bfc6f1fa5d09ae3f65.js.map