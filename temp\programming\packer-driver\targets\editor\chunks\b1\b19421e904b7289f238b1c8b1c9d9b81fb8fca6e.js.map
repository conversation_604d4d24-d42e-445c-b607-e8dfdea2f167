{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts"], "names": ["PlaneUIEvent", "TabChange", "SortTypeChange", "BagItemClick", "UpdateBagGrids", "UpdateMergeEquipStatus"], "mappings": ";;;;;;;;;;;;;;8BAAaA,Y,GAAe;AACxBC,QAAAA,SAAS,EAAE,wBADa;AAExBC,QAAAA,cAAc,EAAE,6BAFQ;AAGxBC,QAAAA,YAAY,EAAE,2BAHU;AAIxBC,QAAAA,cAAc,EAAE,6BAJQ;AAKxBC,QAAAA,sBAAsB,EAAE;AALA,O", "sourcesContent": ["export const PlaneUIEvent = {\n    TabChange: 'PlaneUIEvent_TabChange',\n    SortTypeChange: 'PlaneUIEvent_SortTypeChange',\n    BagItemClick: 'PlaneUIEvent_BagItemClick',\n    UpdateBagGrids: 'PlaneUIEvent_UpdateBagGrids',\n    UpdateMergeEquipStatus: 'PlaneUIEvent_UpdateEquipMergeStatus',\n}"]}