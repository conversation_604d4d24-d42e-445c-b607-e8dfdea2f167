import { _decorator, Prefab, CCString, assetManager, CCInteger, AudioClip } from 'cc';
import { LevelDataEventTriggerLog } from '../../../leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from '../../../leveldata/trigger/LevelDataEventTrigger';
import { newTrigger } from '../../../leveldata/trigger/newTrigger';
import { LevelDataEventTriggerAudio } from '../../../leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerWave } from '../../../leveldata/trigger/LevelDataEventTriggerWave';
import { LevelWaveParam } from './LevelWaveUI';
import { LevelElemUI } from './LevelElemUI';
import { LevelCondition } from './LevelCondition';
import { LevelDataEventCondtionType } from '../../../leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from '../../../leveldata/condition/LevelDataEventCondtionWave';
import { LevelDataEvent } from '../../../leveldata/leveldata';
const { ccclass, property } = _decorator;

@ccclass('LevelEventTrigger')
export class LevelEventTrigger{
    public _index = 0;
    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();

    public get type(): LevelDataEventTriggerType{
        return this.data._type;
    }
    public set type(value: LevelDataEventTriggerType) {
        if (this.data._type != value) {
            this.data = newTrigger({_type: value});
        }
    }

    @property({
        type :CCString,
        visible () {
            return this.type == LevelDataEventTriggerType.Log ;
        }
    })
    public get message(): string {
        return (this.data as LevelDataEventTriggerLog).message;
    }
    public set message(value: string) {
        (this.data as LevelDataEventTriggerLog).message = value;
    }

    public _audio: AudioClip = null;
    @property({
        type :AudioClip,
        visible () {
            return this.type == LevelDataEventTriggerType.Audio;
        }
    })
    public get audio(): AudioClip {
        return this._audio;
    }
    public set audio(value: AudioClip) {
        this._audio = value;
        if (value) {
            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerAudio).audioUUID = "";
        }
    }

    public _wave: Prefab = null;
    @property({
        type: Prefab,
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get wave(): Prefab {
        return this._wave;
    }
    public set wave(value: Prefab) {
        this._wave = value;
        if (value) {
            (this.data as LevelDataEventTriggerWave).waveUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerWave).waveUUID = "";
        }
    }

    @property({
        type :CCInteger,
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get planeID(): number {
        return (this.data as LevelDataEventTriggerWave).planeID;
    }
    public set planeID(value: number) {
        (this.data as LevelDataEventTriggerWave).planeID = value;
    }

    public _params: LevelWaveParam[] = [];
    @property({
        type: [LevelWaveParam],
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get params(): LevelWaveParam[] {
        return this._params;
    }
    public set params(value: LevelWaveParam[]) {
        this._params = value;
        let waveTrigger = this.data as LevelDataEventTriggerWave;
        waveTrigger.params = {};
        for (let p of this._params) {
            waveTrigger.params[p.name] = p.value;
        }
    }

}

@ccclass('LevelEventUI')
export class LevelEventUI extends LevelElemUI {
    @property([LevelCondition])
    public conditions: LevelCondition[] = [];
    @property([LevelEventTrigger])
    public triggers: LevelEventTrigger[] = [];

    public update(dt: number): void {
        for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;
            if (cond.type == LevelDataEventCondtionType.Wave 
                && (cond.data as LevelDataEventCondtionWave).targetElemID != "" 
                && cond._targetElem == null) {
                const elems = this.node.scene.getComponentsInChildren(LevelElemUI);
                for (let elem of elems) {
                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {
                        cond._targetElem = elem;
                        break;
                    }
                }
            }
        }
    }

    public initByLevelData(data: LevelDataEvent) {
        super.initByLevelData(data)
        if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
                const condition = new LevelCondition();
                condition._index = i;
                condition.data = data.conditions[i];
                this.conditions.push(condition);
            }
        }
        if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
                const trigger = new LevelEventTrigger();
                trigger._index = i;
                trigger.data = data.triggers[i];
                this.triggers.push(trigger);
                if (trigger.data._type == LevelDataEventTriggerType.Audio) {
                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;
                    assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {
                        if (err) {
                            console.error("LevelEventUI initByLevelData load audio err", err);
                            return;
                        }
                        trigger._audio = audio;
                    });
                }
                if (trigger.data._type == LevelDataEventTriggerType.Wave) {
                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;
                    let uuid = waveTrigger.waveUUID;
                    assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {
                        if (err) {
                            console.error("LevelEventUI initByLevelData load wave prefab err", err);
                            return;
                        }
                        trigger._wave = prefab;
                    });
                    trigger._params = []
                    for (let k in waveTrigger.params) {
                        let param = new LevelWaveParam();
                        param.name = k;
                        param.value = waveTrigger.params[k];
                        trigger._params.push(param);
                    }
                }
            }
        }
    }
}