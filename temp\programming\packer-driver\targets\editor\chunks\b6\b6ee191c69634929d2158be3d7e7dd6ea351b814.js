System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, logDebug, List, ListItem, BaseUI, UILayer, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, PlaneCombineResultUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../../../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfListItem(extras) {
    _reporterNs.report("ListItem", "../../common/components/list/ListItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfmockPlaneEquipInfo(extras) {
    _reporterNs.report("mockPlaneEquipInfo", "./PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      logDebug = _unresolved_2.logDebug;
    }, function (_unresolved_3) {
      List = _unresolved_3.default;
    }, function (_unresolved_4) {
      ListItem = _unresolved_4.default;
    }, function (_unresolved_5) {
      BaseUI = _unresolved_5.BaseUI;
      UILayer = _unresolved_5.UILayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "28242/7G9BMhotLvgorUZOf", "PlaneCombineResultUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlaneCombineResultUI", PlaneCombineResultUI = (_dec = ccclass('PlaneCombineResultUI'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec3 = property(Node), _dec(_class = (_class2 = class PlaneCombineResultUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "list", _descriptor, this);

          _initializerDefineProperty(this, "singleResult", _descriptor2, this);

          this._results = [];
        }

        static getUrl() {
          return "ui/main/plane/PlaneCombineResultUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).PopUp;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        onLoad() {}

        async onShow(results) {
          this._results = results;

          if (results.length > 1) {
            this.list.node.active = true;
            this.singleResult.active = false;
            this.list.numItems = results.length;
            return;
          }

          this.list.node.active = false;
          this.singleResult.active = true;
          this.singleResult.getComponentInChildren(Label).string = this._results[0].name;
        }

        async onHide() {}

        async onClose() {}

        onListRender(item, index) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", `index:${index} id:${item.getComponent(_crd && ListItem === void 0 ? (_reportPossibleCrUseOfListItem({
            error: Error()
          }), ListItem) : ListItem).listId}`);
          item.getComponentInChildren(Label).string = this._results[index].name;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "list", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "singleResult", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b6ee191c69634929d2158be3d7e7dd6ea351b814.js.map