{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts"], "names": ["HurtEffectManager", "Font", "instantiate", "Label", "NodePool", "ParticleSystem2D", "Prefab", "SpriteAtlas", "Tween", "UITransform", "v3", "warn", "SingletonBase", "GameIns", "Tools", "GameConst", "ImageSequence", "UIAnimMethods", "PfFrameAnim", "GameResourceList", "EnemyEffectLayer", "MyApp", "coolTime", "ratio", "hurt<PERSON>um", "hurtEffect", "m_hurtEffects", "Map", "m_<PERSON><PERSON><PERSON>as", "m_spriteFrames", "m_spriteCounts", "m_spriteNameAndCounts", "m_nameAndImgs", "m_hurtParticlePF", "m_hurt<PERSON><PERSON><PERSON>", "m_hurtNums", "m_hurtFont", "m_bulletDieAnim", "preLoad", "clear", "battleManager", "addLoadCount", "resMgr", "load", "atlas_hurtEffects", "error", "atlas", "getSpriteFrames", "uniqueNames", "i", "length", "name", "split", "arrC<PERSON>ain", "push", "count", "frame", "set", "for<PERSON>ach", "frames", "getSpriteFrame", "effectInstances", "effect", "getComponent", "setData", "checkLoadFinish", "HurtEffect", "prefab", "HurtNum", "loadDir", "font_hurtNum", "fonts", "font", "instances", "labelNode", "label", "string", "Hurt0", "particles", "particleNode", "<PERSON><PERSON>", "releaseAssetByForce", "clearMapForCompArr", "particle", "node", "destroy", "createHurtEffect", "pos", "particleType", "scale", "getHurtParticle", "me", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "convertToNodeSpaceAR", "parent", "setPosition", "setScale", "active", "stopped", "resetSystem", "scheduleOnce", "stopSystem", "pushHurtParticle", "setTimeout", "has", "get", "shift", "newParticle", "playBulletDieAnim", "position", "isPlaneOutScreen", "animNode", "gameResManager", "frameAnim", "animComponent", "init", "bulletManager", "enemyComAtlas", "ActionFrameTime", "stop", "put", "x", "y", "reset", "createHurtNumByType", "damage", "isCirt", "fontType", "lab", "GetHurtNumsByCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startHurtAni", "pool", "pop", "opacity", "Math", "ceil", "toString", "stopAllByTarget", "tween", "to", "fromTo", "call", "pushHurtNums", "start"], "mappings": ";;;wTAiBaA,iB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAUC,MAAAA,I,OAAAA,I;;AACzHC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,a;;AACAC,MAAAA,a;;AACAC,MAAAA,W;;AAEAC,MAAAA,gB;;AACAC,MAAAA,gB;;AAEEC,MAAAA,K,kBAAAA,K;;;;;;;;;mCAIIrB,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,0CAAiE;AAAA;AAAA;AAAA,eAEpEsB,QAFoE,GAEzD,CAFyD;AAAA,eAGpEC,KAHoE,GAG5D,GAH4D;AAAA,eAKpEC,OALoE,GAKlD,IALkD;AAAA,eAMpEC,UANoE,GAM/C,IAN+C;AAAA,eAQpEC,aARoE,GAQpD,IAAIC,GAAJ,EARoD;AAAA,eASpEC,WAToE,GAStD,IATsD;AAAA,eAUpEC,cAVoE,GAUnD,EAVmD;AAAA,eAWpEC,cAXoE,GAWnD,EAXmD;AAAA,eAYpEC,qBAZoE,GAY5C,IAAIJ,GAAJ,EAZ4C;AAAA,eAapEK,aAboE,GAapD,IAAIL,GAAJ,EAboD;AAAA,eAcpEM,gBAdoE,GAcjD,IAAIN,GAAJ,EAdiD;AAAA,eAepEO,eAfoE,GAelD,IAAIP,GAAJ,EAfkD;AAAA,eAgBpEQ,UAhBoE,GAgBvD,IAAIR,GAAJ,EAhBuD;AAAA,eAiBpES,UAjBoE,GAiBvD,IAAIT,GAAJ,EAjBuD;AAAA,eAmBpEU,eAnBoE,GAmBlD,IAAIjC,QAAJ,EAnBkD;AAAA;;AAqBpE;AACJ;AACA;AACIkC,QAAAA,OAAO,GAAG;AACN,eAAKJ,eAAL,CAAqBK,KAArB;AACA,eAAKN,gBAAL,CAAsBM,KAAtB;AACA,eAAKF,eAAL,CAAqBE,KAArB;AACA,eAAKR,qBAAL,CAA2BQ,KAA3B;AACA,eAAKP,aAAL,CAAmBO,KAAnB;;AAEA,cAAI,CAAC,KAAKX,WAAV,EAAuB;AACnB;AAAA;AAAA,oCAAQY,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBC,iBAAnC,EAAqDrC,WAArD,EAAkE,CAACsC,KAAD,EAAOC,KAAP,KAAiB;AAC/E,mBAAKlB,WAAL,GAAmBkB,KAAnB;AACA,mBAAKjB,cAAL,GAAsB,KAAKD,WAAL,CAAiBmB,eAAjB,EAAtB;AAEA,oBAAMC,WAAW,GAAG,EAApB;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpB,cAAL,CAAoBqB,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;AACjD,sBAAME,IAAI,GAAG,KAAKtB,cAAL,CAAoBoB,CAApB,EAAuBE,IAAvB,CAA4BC,KAA5B,CAAkC,GAAlC,EAAuC,CAAvC,CAAb;;AACA,oBAAI,CAAC;AAAA;AAAA,oCAAMC,UAAN,CAAiBL,WAAjB,EAA8BG,IAA9B,CAAL,EAA0C;AACtCH,kBAAAA,WAAW,CAACM,IAAZ,CAAiBH,IAAjB;AACH;AACJ;;AAED,mBAAKrB,cAAL,GAAsB,EAAtB;;AACA,mBAAK,MAAMqB,IAAX,IAAmBH,WAAnB,EAAgC;AAC5B,oBAAIO,KAAK,GAAG,CAAZ;;AACA,qBAAK,MAAMC,KAAX,IAAoB,KAAK3B,cAAzB,EAAyC;AACrC,sBAAIsB,IAAI,KAAKK,KAAK,CAACL,IAAN,CAAWC,KAAX,CAAiB,GAAjB,EAAsB,CAAtB,CAAb,EAAuC;AACnCG,oBAAAA,KAAK;AACR;AACJ;;AACD,qBAAKxB,qBAAL,CAA2B0B,GAA3B,CAA+BN,IAA/B,EAAqCI,KAArC;AACH;;AAED,mBAAK7B,aAAL,CAAmBa,KAAnB;AACA,mBAAKR,qBAAL,CAA2B2B,OAA3B,CAAmC,CAACH,KAAD,EAAQJ,IAAR,KAAiB;AAChD,sBAAMQ,MAAM,GAAG,EAAf;;AACA,qBAAK,IAAIV,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIM,KAArB,EAA4BN,CAAC,EAA7B,EAAiC;AAC7BU,kBAAAA,MAAM,CAACL,IAAP,CAAY,KAAK1B,WAAL,CAAiBgC,cAAjB,CAAiC,GAAET,IAAK,IAAGF,CAAE,EAA7C,CAAZ;AACH;;AAED,sBAAMY,eAAe,GAAG,EAAxB;AACA,sBAAMC,MAAM,GAAG5D,WAAW,CAAC,KAAKuB,UAAN,CAAX,CAA6BsC,YAA7B;AAAA;AAAA,mDAAf;AACAD,gBAAAA,MAAM,CAACE,OAAP,CAAeL,MAAf;AACAE,gBAAAA,eAAe,CAACP,IAAhB,CAAqBQ,MAArB;AAEA,qBAAKpC,aAAL,CAAmB+B,GAAnB,CAAuBN,IAAvB,EAA6BU,eAA7B;AACA,qBAAK7B,aAAL,CAAmByB,GAAnB,CAAuBN,IAAvB,EAA6BQ,MAA7B;AACH,eAbD;AAeA;AAAA;AAAA,sCAAQnB,aAAR,CAAsByB,eAAtB;AACH,aAxCD;AAyCH;;AAED,cAAI,CAAC,KAAKxC,UAAV,EAAsB;AAClB;AAAA;AAAA,oCAAQe,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBuB,UAAnC,EAA8C5D,MAA9C,EAAsD,CAACuC,KAAD,EAAOsB,MAAP,KAAkB;AACpE,mBAAK1C,UAAL,GAAkB0C,MAAlB;AACA;AAAA;AAAA,sCAAQ3B,aAAR,CAAsByB,eAAtB;AACH,aAHD;AAIH;;AAED,cAAI,CAAC,KAAKzC,OAAV,EAAmB;AACf;AAAA;AAAA,oCAAQgB,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiByB,OAAnC,EAA4C9D,MAA5C,EAAoD,CAACuC,KAAD,EAAOsB,MAAP,KAAkB;AAClE,mBAAK3C,OAAL,GAAe2C,MAAf;AACA;AAAA;AAAA,sCAAQ3B,aAAR,CAAsByB,eAAtB;AACH,aAHD;AAIH;;AAED,cAAI,CAAC,KAAK7B,UAAV,EAAsB;AAClB,iBAAKA,UAAL,GAAkB,IAAIT,GAAJ,EAAlB;AACH;;AAED;AAAA;AAAA,kCAAQa,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAa2B,OAAb,CAAqB;AAAA;AAAA,oDAAiBC,YAAtC,EAAoDrE,IAApD,EAA0D,CAAC4C,KAAD,EAAO0B,KAAP,KAAiB;AACvEA,YAAAA,KAAK,CAACb,OAAN,CAAec,IAAD,IAAU;AACpB,kBAAIA,IAAJ,EAAU;AACN,qBAAKpC,UAAL,CAAgBqB,GAAhB,CAAoBe,IAAI,CAACrB,IAAzB,EAA+BqB,IAA/B;AACH;AACJ,aAJD;AAMA,iBAAKrC,UAAL,CAAgBI,KAAhB;AACA,iBAAKH,UAAL,CAAgBsB,OAAhB,CAAwB,CAACc,IAAD,EAAOrB,IAAP,KAAgB;AACpC,oBAAMsB,SAAS,GAAG,EAAlB;;AACA,mBAAK,IAAIxB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,sBAAMyB,SAAS,GAAGxE,WAAW,CAAC,KAAKsB,OAAN,CAA7B;AACA,sBAAMmD,KAAK,GAAGD,SAAS,CAACX,YAAV,CAAuB5D,KAAvB,CAAd;AACAwE,gBAAAA,KAAK,CAACC,MAAN,GAAe,EAAf;AACAD,gBAAAA,KAAK,CAACH,IAAN,GAAaA,IAAb;AACAC,gBAAAA,SAAS,CAACnB,IAAV,CAAeqB,KAAf;AACH;;AACD,mBAAKxC,UAAL,CAAgBsB,GAAhB,CAAoBN,IAApB,EAA0BsB,SAA1B;AACH,aAVD;AAYA;AAAA;AAAA,oCAAQjC,aAAR,CAAsByB,eAAtB;AACH,WArBD;AAuBA;AAAA;AAAA,kCAAQzB,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,oDAAiBkC,KAAnC,EAA0CvE,MAA1C,EAAiD,CAACuC,KAAD,EAAOsB,MAAP,KAAkB;AAC/D,iBAAKlC,gBAAL,CAAsBwB,GAAtB,CAA0B,SAA1B,EAAqCU,MAArC;AACA,kBAAMW,SAAS,GAAG,EAAlB;AACA,iBAAK5C,eAAL,CAAqBuB,GAArB,CAAyB,SAAzB,EAAoCqB,SAApC;;AAEA,iBAAK,IAAI7B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,oBAAM8B,YAAY,GAAG7E,WAAW,CAACiE,MAAD,CAAhC;AACAW,cAAAA,SAAS,CAACxB,IAAV,CAAeyB,YAAY,CAAChB,YAAb,CAA0B1D,gBAA1B,CAAf;AACH;;AAED;AAAA;AAAA,oCAAQmC,aAAR,CAAsByB,eAAtB;AACH,WAXD;AAYH;AAGD;AACJ;AACA;;;AACI1B,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKX,WAAL,IAAoB,CAAC;AAAA;AAAA,sCAAUoD,KAAnC,EAA0C;AACtC;AAAA;AAAA,gCAAMtC,MAAN,CAAauC,mBAAb,CAAiC,KAAKrD,WAAtC;AACA,iBAAKA,WAAL,GAAmB,IAAnB;AACH;;AAED;AAAA;AAAA,8BAAMsD,kBAAN,CAAyB,KAAKxD,aAA9B;AACA,eAAKG,cAAL,GAAsB,EAAtB;AACA,eAAKC,cAAL,GAAsB,EAAtB;AACA,eAAKC,qBAAL,CAA2BQ,KAA3B;AACA,eAAKP,aAAL,CAAmBO,KAAnB;AACA,eAAKN,gBAAL,CAAsBM,KAAtB;AAEA,eAAKL,eAAL,CAAqBwB,OAArB,CAA8BoB,SAAD,IAAe;AACxC,iBAAK,MAAMK,QAAX,IAAuBL,SAAvB,EAAkC;AAC9B,kBAAIK,QAAQ,IAAIA,QAAQ,CAACC,IAAzB,EAA+B;AAC3BD,gBAAAA,QAAQ,CAACC,IAAT,CAAcC,OAAd;AACH;AACJ;AACJ,WAND;AAQA,eAAKnD,eAAL,CAAqBK,KAArB;AACA;AAAA;AAAA,8BAAM2C,kBAAN,CAAyB,KAAK/C,UAA9B;AACA,eAAKE,eAAL,CAAqBE,KAArB;AACH;;AAED+C,QAAAA,gBAAgB,CAACC,GAAD,EAAYC,YAAZ,EAA0BC,KAAK,GAAG,CAAlC,EAAqC;AACjD,gBAAMN,QAAQ,GAAG,KAAKO,eAAL,CAAqBF,YAArB,CAAjB;;AACA,cAAIL,QAAJ,EAAc;AACVI,YAAAA,GAAG,GAAG;AAAA;AAAA,sDAAiBI,EAAjB,CAAoBC,eAApB,CAAoC7B,YAApC,CAAiDtD,WAAjD,EAA8DoF,oBAA9D,CAAmFN,GAAnF,CAAN;AACAJ,YAAAA,QAAQ,CAACC,IAAT,CAAcU,MAAd,GAAuB;AAAA;AAAA,sDAAiBH,EAAjB,CAAoBC,eAA3C;AACAT,YAAAA,QAAQ,CAACC,IAAT,CAAcW,WAAd,CAA0BR,GAA1B;AACAJ,YAAAA,QAAQ,CAACC,IAAT,CAAcY,QAAd,CAAuBP,KAAvB,EAA8BA,KAA9B;AACAN,YAAAA,QAAQ,CAACC,IAAT,CAAca,MAAd,GAAuB,IAAvB;;AAEA,gBAAId,QAAQ,CAACe,OAAb,EAAsB;AAClBf,cAAAA,QAAQ,CAACgB,WAAT;AACH;;AAEDhB,YAAAA,QAAQ,CAACiB,YAAT,CAAsB,MAAM;AACxB,kBAAIjB,QAAQ,IAAIA,QAAQ,CAACC,IAAzB,EAA+B;AAC3BD,gBAAAA,QAAQ,CAACkB,UAAT;AACA,qBAAKC,gBAAL,CAAsBnB,QAAtB,EAAgCK,YAAhC;AACH;AACJ,aALD,EAKG,IALH;AAMH;AACJ;;AAEDc,QAAAA,gBAAgB,CAACnB,QAAD,EAAWK,YAAY,GAAG,SAA1B,EAAqC;AACjDe,UAAAA,UAAU,CAAC,MAAM;AACb,gBAAIpB,QAAQ,IAAIA,QAAQ,CAACC,IAAzB,EAA+B;AAC3B,kBAAI,CAAC,KAAKlD,eAAL,CAAqBsE,GAArB,CAAyBhB,YAAzB,CAAL,EAA6C;AACzCA,gBAAAA,YAAY,GAAG,SAAf;AACH;;AAED,oBAAMV,SAAS,GAAG,KAAK5C,eAAL,CAAqBuE,GAArB,CAAyBjB,YAAzB,CAAlB;;AACA,kBAAIV,SAAJ,EAAe;AACXA,gBAAAA,SAAS,CAACxB,IAAV,CAAe6B,QAAf;AACAA,gBAAAA,QAAQ,CAACC,IAAT,CAAca,MAAd,GAAuB,KAAvB;AACH,eAHD,MAGO;AACHd,gBAAAA,QAAQ,CAACC,IAAT,CAAcC,OAAd;AACH;AACJ;AACJ,WAdS,EAcP,GAdO,CAAV;AAeH;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,eAAe,CAACF,YAAY,GAAG,SAAhB,EAA2B;AAEtC,cAAI,CAAC,KAAKtD,eAAL,CAAqBsE,GAArB,CAAyBhB,YAAzB,CAAL,EAA6C;AACzCA,YAAAA,YAAY,GAAG,SAAf;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAK5C,eAAL,CAAqBuE,GAArB,CAAyBjB,YAAzB,CAAlB;;AACA,cAAIV,SAAS,IAAIA,SAAS,CAAC5B,MAAV,GAAmB,CAApC,EAAuC;AACnC,kBAAMiC,QAAQ,GAAGL,SAAS,CAAC4B,KAAV,EAAjB;;AACA,gBAAIvB,QAAQ,IAAIA,QAAQ,CAACC,IAAzB,EAA+B;AAC3BD,cAAAA,QAAQ,CAACC,IAAT,CAAca,MAAd,GAAuB,IAAvB;AACA,qBAAOd,QAAP;AACH;AACJ;;AAED,gBAAMhB,MAAM,GAAG,KAAKlC,gBAAL,CAAsBwE,GAAtB,CAA0BjB,YAA1B,CAAf;;AACA,cAAIrB,MAAJ,EAAY;AACR,kBAAMwC,WAAW,GAAGzG,WAAW,CAACiE,MAAD,CAAX,CAAoBJ,YAApB,CAAiC1D,gBAAjC,CAApB;AACA,mBAAOsG,WAAP;AACH;;AAED,iBAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,QAAAA,iBAAiB,CAACC,QAAD,EAAW;AACxB,cAAI;AAAA;AAAA,8BAAMC,gBAAN,CAAuBD,QAAvB,CAAJ,EAAsC;AAEtC,cAAIE,QAAQ,GAAG,KAAK1E,eAAL,CAAqBoE,GAArB,EAAf;;AACA,cAAI,CAACM,QAAL,EAAe;AACXA,YAAAA,QAAQ,GAAG7G,WAAW,CAAC;AAAA;AAAA,oCAAQ8G,cAAR,CAAuBC,SAAxB,CAAtB;AACA,kBAAMC,aAAa,GAAGH,QAAQ,CAAChD,YAAT;AAAA;AAAA,2CAAtB;AACAmD,YAAAA,aAAa,CAACC,IAAd,CACI;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,aAD1B,EAEI,MAFJ,EAGI,CAHJ,EAII;AAAA;AAAA,wCAAUC,eAJd,EAKI,MAAM;AACFJ,cAAAA,aAAa,CAACK,IAAd;AACA,mBAAKlF,eAAL,CAAqBmF,GAArB,CAAyBT,QAAzB;AACH,aARL;AAUH;;AAEDA,UAAAA,QAAQ,CAACjB,MAAT,GAAkB;AAAA;AAAA,oDAAiBH,EAAjB,CAAoBC,eAAtC;AACAmB,UAAAA,QAAQ,CAAChB,WAAT,CAAqBc,QAAQ,CAACY,CAA9B,EAAiCZ,QAAQ,CAACa,CAA1C;AAEA,gBAAMR,aAAa,GAAGH,QAAQ,CAAChD,YAAT;AAAA;AAAA,yCAAtB;;AACA,cAAImD,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACS,KAAd;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,mBAAmB,CAACf,QAAD,EAAgBgB,MAAhB,EAA+BC,MAAc,GAAG,KAAhD,EAAuD;AACtE,cAAID,MAAM,IAAI,CAAd,EAAiB;AAEjB,gBAAME,QAAQ,GAAGD,MAAM,GAAG,eAAH,GAAqB,cAA5C;AACA,gBAAME,GAAU,GAAG,KAAKC,kBAAL,CAAwBF,QAAxB,EAAkCF,MAAlC,CAAnB;;AAEA,cAAIG,GAAG,IAAI;AAAA;AAAA,oDAAiBrC,EAAjB,CAAoBuC,YAA/B,EAA6C;AACzCF,YAAAA,GAAG,CAAC5C,IAAJ,CAASU,MAAT,GAAkB;AAAA;AAAA,sDAAiBH,EAAjB,CAAoBuC,YAAtC;AACAF,YAAAA,GAAG,CAAC5C,IAAJ,CAASW,WAAT,CAAqBc,QAAQ,CAACY,CAA9B,EAAiCZ,QAAQ,CAACa,CAAT,GAAa,EAA9C;AAEA,iBAAKS,YAAL,CAAkBH,GAAlB,EAAuBD,QAAvB;AACH;AACJ;;AAEDE,QAAAA,kBAAkB,CAACF,QAAD,EAAWF,MAAX,EAAmB;AACjC,cAAIrG,OAAO,GAAG,IAAd;AACA,gBAAM4G,IAAI,GAAG,KAAKjG,UAAL,CAAgBsE,GAAhB,CAAoBsB,QAApB,CAAb;;AAEA,cAAIK,IAAJ,EAAU;AACN,gBAAIA,IAAI,CAAClF,MAAL,GAAc,CAAlB,EAAqB;AACjB1B,cAAAA,OAAO,GAAG4G,IAAI,CAACC,GAAL,EAAV;AACH,aAFD,MAEO;AACH7G,cAAAA,OAAO,GAAGtB,WAAW,CAAC,KAAKsB,OAAN,CAAX,CAA0BuC,YAA1B,CAAuC5D,KAAvC,CAAV;AACAqB,cAAAA,OAAO,CAACgD,IAAR,GAAe,KAAKpC,UAAL,CAAgBqE,GAAhB,CAAoBsB,QAApB,CAAf;AACH;AACJ;;AAEDvG,UAAAA,OAAO,CAAC4D,IAAR,CAAakD,OAAb,GAAuB,GAAvB;AACA9G,UAAAA,OAAO,CAAC4D,IAAR,CAAaa,MAAb,GAAsB,IAAtB;AACAzE,UAAAA,OAAO,CAACoD,MAAR,GAAiB2D,IAAI,CAACC,IAAL,CAAUX,MAAV,EAAkBY,QAAlB,EAAjB;AAEA,iBAAOjH,OAAP;AACH;;AAED2G,QAAAA,YAAY,CAAC3G,OAAD,EAAUuG,QAAV,EAAoB;AAC5B,gBAAMxG,KAAK,GAAG,KAAKA,KAAnB;AACAf,UAAAA,KAAK,CAACkI,eAAN,CAAsBlH,OAAO,CAAC4D,IAA9B;AAEA,cAAIuD,KAAY,GAAG,IAAnB;;AACA,kBAAQZ,QAAR;AACI,iBAAK,cAAL;AACIvG,cAAAA,OAAO,CAAC4D,IAAR,CAAaY,QAAb,CAAsB,IAAtB,EAA4B,IAA5B;AACA2C,cAAAA,KAAK,GAAG,IAAInI,KAAJ,CAAUgB,OAAO,CAAC4D,IAAlB,EACHwD,EADG,CACA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADA,EAC4B;AAAEpD,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAD5B,EAEHqH,EAFG,CAEA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,EAAxB,CAFA,EAE6B;AAAEpD,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAF7B,EAGHqH,EAHG,CAGA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,EAArB,EAAyB,EAAzB,CAHA,EAG8B;AAAEhC,gBAAAA,QAAQ,EAAEnG,EAAE,CAACc,OAAO,CAAC4D,IAAR,CAAaqC,CAAd,EAAiBjG,OAAO,CAAC4D,IAAR,CAAasC,CAAb,GAAiB,EAAlC,CAAd;AAAqDjC,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAA9D,eAH9B,CAAR;AAIA;;AAEJ,iBAAK,eAAL;AACIC,cAAAA,OAAO,CAAC4D,IAAR,CAAaY,QAAb,CAAsB,IAAtB,EAA4B,IAA5B;AACA2C,cAAAA,KAAK,GAAG,IAAInI,KAAJ,CAAUgB,OAAO,CAAC4D,IAAlB,EACHwD,EADG,CACA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADA,EAC4B;AAAEpD,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAD5B,EAEHqH,EAFG,CAEA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAFA,EAE4B;AAAEpD,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAF5B,EAGHqH,EAHG,CAGA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,CAArB,EAAwB,EAAxB,CAHA,EAG6B;AAAEpD,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAAX,eAH7B,EAIHqH,EAJG,CAIA;AAAA;AAAA,kDAAcC,MAAd,CAAqB,EAArB,EAAyB,EAAzB,CAJA,EAI8B;AAAEhC,gBAAAA,QAAQ,EAAEnG,EAAE,CAACc,OAAO,CAAC4D,IAAR,CAAaqC,CAAd,EAAiBjG,OAAO,CAAC4D,IAAR,CAAasC,CAAb,GAAiB,EAAlC,CAAd;AAAqDjC,gBAAAA,KAAK,EAAE/E,EAAE,CAAC,OAAOa,KAAR,EAAe,OAAOA,KAAtB;AAA9D,eAJ9B,CAAR;AAKA;;AAEJ;AACIZ,cAAAA,IAAI,CAAC,4CAAD,CAAJ;AAnBR;;AAsBAgI,UAAAA,KAAK,CAACG,IAAN,CAAW,MAAM;AACb,iBAAKC,YAAL,CAAkBhB,QAAlB,EAA4BvG,OAA5B;AACH,WAFD,EAEGwH,KAFH;AAGH;;AAEDD,QAAAA,YAAY,CAAChB,QAAD,EAAWvG,OAAX,EAAoB;AAC5B,cAAIA,OAAO,IAAIA,OAAO,CAAC4D,IAAvB,EAA6B;AACzB5D,YAAAA,OAAO,CAACoD,MAAR,GAAiB,EAAjB;AACApD,YAAAA,OAAO,CAAC4D,IAAR,CAAaa,MAAb,GAAsB,KAAtB;AAEA,kBAAMmC,IAAI,GAAG,KAAKjG,UAAL,CAAgBsE,GAAhB,CAAoBsB,QAApB,CAAb;;AACA,gBAAIK,IAAJ,EAAU;AACNA,cAAAA,IAAI,CAAC9E,IAAL,CAAU9B,OAAV;AACH,aAFD,MAEO;AACHA,cAAAA,OAAO,CAAC4D,IAAR,CAAaC,OAAb;AACH;AACJ;AACJ;;AA9VmE,O", "sourcesContent": ["import { director, Font, game, instantiate, Label, NodePool, ParticleSystem2D, Prefab, SpriteAtlas, Tween, UITransform, v3, Vec3, warn } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport ImageSequence from \"../ui/base/ImageSequence\";\r\nimport UIAnimMethods from \"../ui/base/UIAnimMethods\";\r\nimport PfFrameAnim from \"../ui/base/PfFrameAnim\";\r\nimport ResourceList from \"../const/GameResourceList\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport EnemyEffectLayer from \"../ui/layer/EnemyEffectLayer\";\r\nimport FCollider from \"../collider-system/FCollider\";\r\nimport { MyApp } from \"../../MyApp\";\r\n\r\n\r\n\r\nexport class HurtEffectManager extends SingletonBase<HurtEffectManager> {\r\n\r\n    coolTime = 5;\r\n    ratio = 0.8;\r\n\r\n    hurtNum: Prefab = null;\r\n    hurtEffect: Prefab = null;\r\n\r\n    m_hurtEffects = new Map();\r\n    m_hurtAtlas = null;\r\n    m_spriteFrames = [];\r\n    m_spriteCounts = [];\r\n    m_spriteNameAndCounts = new Map();\r\n    m_nameAndImgs = new Map();\r\n    m_hurtParticlePF = new Map();\r\n    m_hurtParticles = new Map();\r\n    m_hurtNums = new Map();\r\n    m_hurtFont = new Map();\r\n\r\n    m_bulletDieAnim = new NodePool();\r\n\r\n    /**\r\n     * 预加载资源\r\n     */\r\n    preLoad() {\r\n        this.m_hurtParticles.clear();\r\n        this.m_hurtParticlePF.clear();\r\n        this.m_bulletDieAnim.clear();\r\n        this.m_spriteNameAndCounts.clear();\r\n        this.m_nameAndImgs.clear();\r\n\r\n        if (!this.m_hurtAtlas) {\r\n            GameIns.battleManager.addLoadCount(1);\r\n            MyApp.resMgr.load(GameResourceList.atlas_hurtEffects,SpriteAtlas, (error,atlas) => {\r\n                this.m_hurtAtlas = atlas;\r\n                this.m_spriteFrames = this.m_hurtAtlas.getSpriteFrames();\r\n\r\n                const uniqueNames = [];\r\n                for (let i = 1; i < this.m_spriteFrames.length; i++) {\r\n                    const name = this.m_spriteFrames[i].name.split(\"_\")[0];\r\n                    if (!Tools.arrContain(uniqueNames, name)) {\r\n                        uniqueNames.push(name);\r\n                    }\r\n                }\r\n\r\n                this.m_spriteCounts = [];\r\n                for (const name of uniqueNames) {\r\n                    let count = 0;\r\n                    for (const frame of this.m_spriteFrames) {\r\n                        if (name === frame.name.split(\"_\")[0]) {\r\n                            count++;\r\n                        }\r\n                    }\r\n                    this.m_spriteNameAndCounts.set(name, count);\r\n                }\r\n\r\n                this.m_hurtEffects.clear();\r\n                this.m_spriteNameAndCounts.forEach((count, name) => {\r\n                    const frames = [];\r\n                    for (let i = 1; i <= count; i++) {\r\n                        frames.push(this.m_hurtAtlas.getSpriteFrame(`${name}_${i}`));\r\n                    }\r\n\r\n                    const effectInstances = [];\r\n                    const effect = instantiate(this.hurtEffect).getComponent(ImageSequence);\r\n                    effect.setData(frames);\r\n                    effectInstances.push(effect);\r\n\r\n                    this.m_hurtEffects.set(name, effectInstances);\r\n                    this.m_nameAndImgs.set(name, frames);\r\n                });\r\n\r\n                GameIns.battleManager.checkLoadFinish();\r\n            });\r\n        }\r\n\r\n        if (!this.hurtEffect) {\r\n            GameIns.battleManager.addLoadCount(1);\r\n            MyApp.resMgr.load(GameResourceList.HurtEffect,Prefab, (error,prefab) => {\r\n                this.hurtEffect = prefab;\r\n                GameIns.battleManager.checkLoadFinish();\r\n            });\r\n        }\r\n\r\n        if (!this.hurtNum) {\r\n            GameIns.battleManager.addLoadCount(1);\r\n            MyApp.resMgr.load(GameResourceList.HurtNum, Prefab, (error,prefab) => {\r\n                this.hurtNum = prefab;\r\n                GameIns.battleManager.checkLoadFinish();\r\n            });\r\n        }\r\n\r\n        if (!this.m_hurtFont) {\r\n            this.m_hurtFont = new Map();\r\n        }\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.loadDir(GameResourceList.font_hurtNum, Font, (error,fonts) => {\r\n            fonts.forEach((font) => {\r\n                if (font) {\r\n                    this.m_hurtFont.set(font.name, font);\r\n                }\r\n            });\r\n\r\n            this.m_hurtNums.clear();\r\n            this.m_hurtFont.forEach((font, name) => {\r\n                const instances = [];\r\n                for (let i = 0; i < 3; i++) {\r\n                    const labelNode = instantiate(this.hurtNum);\r\n                    const label = labelNode.getComponent(Label);\r\n                    label.string = \"\";\r\n                    label.font = font;\r\n                    instances.push(label);\r\n                }\r\n                this.m_hurtNums.set(name, instances);\r\n            });\r\n\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.Hurt0, Prefab,(error,prefab) => {\r\n            this.m_hurtParticlePF.set(\"default\", prefab);\r\n            const particles = [];\r\n            this.m_hurtParticles.set(\"default\", particles);\r\n\r\n            for (let i = 0; i < 10; i++) {\r\n                const particleNode = instantiate(prefab);\r\n                particles.push(particleNode.getComponent(ParticleSystem2D));\r\n            }\r\n\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n\r\n    /**\r\n     * 清理资源\r\n     */\r\n    clear() {\r\n        if (this.m_hurtAtlas && !GameConst.Cache) {\r\n            MyApp.resMgr.releaseAssetByForce(this.m_hurtAtlas);\r\n            this.m_hurtAtlas = null;\r\n        }\r\n\r\n        Tools.clearMapForCompArr(this.m_hurtEffects);\r\n        this.m_spriteFrames = [];\r\n        this.m_spriteCounts = [];\r\n        this.m_spriteNameAndCounts.clear();\r\n        this.m_nameAndImgs.clear();\r\n        this.m_hurtParticlePF.clear();\r\n\r\n        this.m_hurtParticles.forEach((particles) => {\r\n            for (const particle of particles) {\r\n                if (particle && particle.node) {\r\n                    particle.node.destroy();\r\n                }\r\n            }\r\n        });\r\n\r\n        this.m_hurtParticles.clear();\r\n        Tools.clearMapForCompArr(this.m_hurtNums);\r\n        this.m_bulletDieAnim.clear();\r\n    }\r\n\r\n    createHurtEffect(pos: Vec3, particleType, scale = 1) {\r\n        const particle = this.getHurtParticle(particleType) as ParticleSystem2D;\r\n        if (particle) {\r\n            pos = EnemyEffectLayer.me.hurtEffectLayer.getComponent(UITransform).convertToNodeSpaceAR(pos);\r\n            particle.node.parent = EnemyEffectLayer.me.hurtEffectLayer;\r\n            particle.node.setPosition(pos);\r\n            particle.node.setScale(scale, scale);\r\n            particle.node.active = true;\r\n\r\n            if (particle.stopped) {\r\n                particle.resetSystem();\r\n            }\r\n\r\n            particle.scheduleOnce(() => {\r\n                if (particle && particle.node) {\r\n                    particle.stopSystem();\r\n                    this.pushHurtParticle(particle, particleType);\r\n                }\r\n            }, 0.12);\r\n        }\r\n    }\r\n\r\n    pushHurtParticle(particle, particleType = \"default\") {\r\n        setTimeout(() => {\r\n            if (particle && particle.node) {\r\n                if (!this.m_hurtParticles.has(particleType)) {\r\n                    particleType = \"default\";\r\n                }\r\n\r\n                const particles = this.m_hurtParticles.get(particleType);\r\n                if (particles) {\r\n                    particles.push(particle);\r\n                    particle.node.active = false;\r\n                } else {\r\n                    particle.node.destroy();\r\n                }\r\n            }\r\n        }, 200);\r\n    }\r\n\r\n    /**\r\n     * 获取伤害粒子\r\n     * @param {string} particleType 粒子类型\r\n     * @returns {ParticleSystem2D|null} 粒子系统\r\n     */\r\n    getHurtParticle(particleType = \"default\") {\r\n\r\n        if (!this.m_hurtParticles.has(particleType)) {\r\n            particleType = \"default\";\r\n        }\r\n\r\n        const particles = this.m_hurtParticles.get(particleType);\r\n        if (particles && particles.length > 0) {\r\n            const particle = particles.shift();\r\n            if (particle && particle.node) {\r\n                particle.node.active = true;\r\n                return particle;\r\n            }\r\n        }\r\n\r\n        const prefab = this.m_hurtParticlePF.get(particleType);\r\n        if (prefab) {\r\n            const newParticle = instantiate(prefab).getComponent(ParticleSystem2D);\r\n            return newParticle;\r\n        }\r\n\r\n        return null;\r\n    }\r\n    /**\r\n     * 播放子弹死亡动画\r\n     * @param {Vec2} position 动画位置\r\n     */\r\n    playBulletDieAnim(position) {\r\n        if (Tools.isPlaneOutScreen(position)) return;\r\n\r\n        let animNode = this.m_bulletDieAnim.get();\r\n        if (!animNode) {\r\n            animNode = instantiate(GameIns.gameResManager.frameAnim);\r\n            const animComponent = animNode.getComponent(PfFrameAnim);\r\n            animComponent.init(\r\n                GameIns.bulletManager.enemyComAtlas,\r\n                \"die_\",\r\n                7,\r\n                GameConst.ActionFrameTime,\r\n                () => {\r\n                    animComponent.stop();\r\n                    this.m_bulletDieAnim.put(animNode);\r\n                }\r\n            );\r\n        }\r\n\r\n        animNode.parent = EnemyEffectLayer.me.hurtEffectLayer;\r\n        animNode.setPosition(position.x, position.y);\r\n\r\n        const animComponent = animNode.getComponent(PfFrameAnim);\r\n        if (animComponent) {\r\n            animComponent.reset();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 根据类型创建伤害数字\r\n     * @param {Object} collider 碰撞体\r\n     * @param {Object} entity 实体对象\r\n     * @param {number} damage 伤害值\r\n     * @param {Object} offset 偏移量\r\n     */\r\n    createHurtNumByType(position:Vec3, damage:number, isCirt:boolean = false) {\r\n        if (damage <= 0) return;\r\n\r\n        const fontType = isCirt ? \"yellowHurtNum\" : \"whiteHurtNum\";\r\n        const lab: Label = this.GetHurtNumsByCount(fontType, damage);\r\n\r\n        if (lab && EnemyEffectLayer.me.hurtNumLayer) {\r\n            lab.node.parent = EnemyEffectLayer.me.hurtNumLayer;\r\n            lab.node.setPosition(position.x, position.y - 30);\r\n\r\n            this.startHurtAni(lab, fontType);\r\n        }\r\n    }\r\n\r\n    GetHurtNumsByCount(fontType, damage) {\r\n        let hurtNum = null;\r\n        const pool = this.m_hurtNums.get(fontType);\r\n\r\n        if (pool) {\r\n            if (pool.length > 0) {\r\n                hurtNum = pool.pop();\r\n            } else {\r\n                hurtNum = instantiate(this.hurtNum).getComponent(Label);\r\n                hurtNum.font = this.m_hurtFont.get(fontType);\r\n            }\r\n        }\r\n\r\n        hurtNum.node.opacity = 255;\r\n        hurtNum.node.active = true;\r\n        hurtNum.string = Math.ceil(damage).toString();\r\n\r\n        return hurtNum;\r\n    }\r\n\r\n    startHurtAni(hurtNum, fontType) {\r\n        const ratio = this.ratio;\r\n        Tween.stopAllByTarget(hurtNum.node);\r\n\r\n        let tween: Tween = null;\r\n        switch (fontType) {\r\n            case \"whiteHurtNum\":\r\n                hurtNum.node.setScale(0.15, 0.15);\r\n                tween = new Tween(hurtNum.node)\r\n                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.53 * ratio, 1.53 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(6, 11), { scale: v3(0.47 * ratio, 0.47 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(11, 32), { position: v3(hurtNum.node.x, hurtNum.node.y + 13), scale: v3(0.47 * ratio, 0.47 * ratio) });\r\n                break;\r\n\r\n            case \"yellowHurtNum\":\r\n                hurtNum.node.setScale(0.16, 0.16);\r\n                tween = new Tween(hurtNum.node)\r\n                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.75 * ratio, 1.75 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(6, 9), { scale: v3(0.44 * ratio, 0.44 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(9, 12), { scale: v3(0.52 * ratio, 0.52 * ratio) })\r\n                    .to(UIAnimMethods.fromTo(12, 31), { position: v3(hurtNum.node.x, hurtNum.node.y + 21), scale: v3(0.52 * ratio, 0.52 * ratio) });\r\n                break;\r\n\r\n            default:\r\n                warn(\"Unknown font type in createHurtNumInTarget\");\r\n        }\r\n\r\n        tween.call(() => {\r\n            this.pushHurtNums(fontType, hurtNum);\r\n        }).start();\r\n    }\r\n\r\n    pushHurtNums(fontType, hurtNum) {\r\n        if (hurtNum && hurtNum.node) {\r\n            hurtNum.string = \"\";\r\n            hurtNum.node.active = false;\r\n\r\n            const pool = this.m_hurtNums.get(fontType);\r\n            if (pool) {\r\n                pool.push(hurtNum);\r\n            } else {\r\n                hurtNum.node.destroy();\r\n            }\r\n        }\r\n    }\r\n}"]}