System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, EventMgr, BaseUI, UILayer, UIMgr, ButtonPlus, BattleUI, MainEvent, RogueSelectIcon, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, RogueUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleUI(extras) {
    _reporterNs.report("BattleUI", "../BattleUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainEvent(extras) {
    _reporterNs.report("MainEvent", "../MainEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRogueSelectIcon(extras) {
    _reporterNs.report("RogueSelectIcon", "./RogueSelectIcon", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      EventMgr = _unresolved_2.EventMgr;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      ButtonPlus = _unresolved_4.ButtonPlus;
    }, function (_unresolved_5) {
      BattleUI = _unresolved_5.BattleUI;
    }, function (_unresolved_6) {
      MainEvent = _unresolved_6.MainEvent;
    }, function (_unresolved_7) {
      RogueSelectIcon = _unresolved_7.RogueSelectIcon;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e094c3NOSVC84pbIiaj27xx", "RogueUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("RogueUI", RogueUI = (_dec = ccclass("RogueUI"), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property([_crd && RogueSelectIcon === void 0 ? (_reportPossibleCrUseOfRogueSelectIcon({
        error: Error()
      }), RogueSelectIcon) : RogueSelectIcon]), _dec(_class = (_class2 = class RogueUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnClose", _descriptor, this);

          _initializerDefineProperty(this, "nodeFresh", _descriptor2, this);

          _initializerDefineProperty(this, "nodeExclude", _descriptor3, this);

          _initializerDefineProperty(this, "nodeAbility", _descriptor4, this);

          _initializerDefineProperty(this, "rogueSelectIcons", _descriptor5, this);

          this.freshTimes = null;
          this.excludeTimes = null;
        }

        static getUrl() {
          return "ui/main/fight/RogueUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        onLoad() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && MainEvent === void 0 ? (_reportPossibleCrUseOfMainEvent({
            error: Error()
          }), MainEvent) : MainEvent).RogueSelectClick, this.onRogueSelectClick, this);
          this.btnClose.addClick(this.closeUI, this);
          this.nodeFresh.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onFresh, this);
          this.nodeExclude.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onCancel, this);
          this.freshTimes = this.nodeFresh.getComponentInChildren(Label);
          this.excludeTimes = this.nodeExclude.getComponentInChildren(Label);
        }

        onRogueSelectClick(index) {
          this.rogueSelectIcons.forEach(element => {
            element.updateActive(index);
          });
        }

        onFresh() {
          this.rogueSelectIcons.forEach(element => {
            element.updateActive(0);
          });
        }

        onCancel() {
          this.rogueSelectIcons.forEach(element => {
            element.updateActive(0);
          });
          let btn = this.nodeExclude.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus);

          if (btn.getComponentInChildren(Label).string == "排除") {
            btn.getComponentInChildren(Label).string = "取消";
            this.rogueSelectIcons.forEach(element => {
              element.updateStatus(2);
            });
          } else {
            btn.getComponentInChildren(Label).string = "排除";
            this.rogueSelectIcons.forEach(element => {
              element.updateStatus(1);
            });
          }
        }

        updateFreshTimes() {
          const currentText = this.freshTimes.string;
          const match = currentText.match(/\d+/);

          if (match) {
            let count = parseInt(match[0], 10);

            if (count > 0) {
              count--;
              this.freshTimes.string = `剩余次数：${count}`;
            }
          }
        }

        updateCancelTimes() {
          const currentText = this.excludeTimes.string;
          const match = currentText.match(/\d+/);

          if (match) {
            let count = parseInt(match[0], 10);

            if (count > 0) {
              count--;
              this.excludeTimes.string = `剩余次数：${count}`;
            }
          }
        }

        async closeUI() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
            error: Error()
          }), BattleUI) : BattleUI);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(RogueUI);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        async onShow(...args) {}

        async onHide(...args) {}

        async onClose(...args) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "nodeFresh", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "nodeExclude", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "nodeAbility", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "rogueSelectIcons", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8f4e7067566f0df7c133c3401333e88129c15345.js.map