{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts"], "names": ["LevelDataEventCondtionWave", "LevelDataEventCondtion", "LevelDataEventCondtionType", "constructor", "comb", "Wave", "targetElemID"], "mappings": ";;;kFAEaA,0B;;;;;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,sB,iBAAAA,sB;AAAoDC,MAAAA,0B,iBAAAA,0B;;;;;;;4CAEhDF,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,4DAAgE;AAEnEG,QAAAA,WAAW,CAACC,IAAD,EAAmC;AAC1C,gBAAMA,IAAN,EAAY;AAAA;AAAA,wEAA2BC,IAAvC;AAD0C,eADvCC,YACuC,GADxB,EACwB;AAE7C;;AAJkE,O", "sourcesContent": ["import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from \"./LevelDataEventCondtion\";\r\n\r\nexport class LevelDataEventCondtionWave extends LevelDataEventCondtion {\r\n    public targetElemID = \"\";\r\n    constructor(comb: LevelDataEventCondtionComb) {\r\n        super(comb, LevelDataEventCondtionType.Wave);\r\n    }\r\n}"]}