{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts"], "names": ["LevelDataEventTrigger", "LevelDataEventTriggerType", "constructor", "type", "_type", "Log"], "mappings": ";;;iBAUaA,qB;;;;;;;;;;;;;2CANDC,yB,0BAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;AAAAA,QAAAA,yB,CAAAA,yB;eAAAA,yB;;;uCAMCD,qB,GAAN,MAAMA,qBAAN,CAA4B;AAE/BE,QAAAA,WAAW,CAACC,IAAD,EAAmC;AAAA,eADvCC,KACuC,GADJH,yBAAyB,CAACI,GACtB;AAC1C,eAAKD,KAAL,GAAaD,IAAb;AACH;;AAJ8B,O", "sourcesContent": ["import { LevelDataEventTriggerAudio } from \"./LevelDataEventTriggerAudio\";\r\nimport { LevelDataEventTriggerLog } from \"./LevelDataEventTriggerLog\";\r\nimport { LevelDataEventTriggerWave } from \"./LevelDataEventTriggerWave\";\r\n\r\nexport enum LevelDataEventTriggerType {\r\n    Log = 0,\r\n    Audio = 1,\r\n    Wave = 2,\r\n}\r\n\r\nexport class LevelDataEventTrigger {\r\n    public _type: LevelDataEventTriggerType = LevelDataEventTriggerType.Log;\r\n    constructor(type : LevelDataEventTriggerType) {\r\n        this._type = type;\r\n    }\r\n}"]}