import { _decorator, director, Label, Node, sys } from 'cc';

import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { DropDown } from '../common/components/dropdown/DropDown';
import { LoadingUI } from '../LoadingUI';
import { BaseUI, UILayer, UIMgr } from '../UIMgr';
import { BottomUI } from './BottomUI';
import { DialogueUI } from './dialogue/DialogueUI';
import { RogueUI } from './fight/RogueUI';
import { FriendUI } from './friend/FriendUI';
import { MapModeUI } from './MapModeUI';
import { PlaneUI } from './plane/PlaneUI';
import { ShopUI } from './ShopUI';
import { TalentUI } from './TalentUI';
import { TopUI } from './TopUI';

const { ccclass, property } = _decorator;

interface Item {
    id: number;
    name: string;
}

const items: Item[] = [
    { id: 0, name: "默认" },
    { id: 1, name: "好友" },
    { id: 2, name: "对话框" },
    { id: 3, name: "肉鸽" },
    { id: 4, name: "剧情章节" },
];

@ccclass('BattleUI')
export class BattleUI extends BaseUI {
    public static getUrl(): string { return "ui/main/BattleUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    @property(ButtonPlus)
    btnBattle: ButtonPlus;
    @property(DropDown)
    tabDropDown: DropDown = null;

    protected onLoad(): void {
        this.btnBattle.addClick(this.onBattleClick, this);
    }

    getItemIds(): number[] {
        return items.map(item => item.id);
    }

    public onShow(...args: any[]): Promise<void> {
        this.tabDropDown.node.active = sys.isBrowser
        this.scheduleOnce(() => {
            this.tabDropDown.init(this.getItemIds(), this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this))
        }, 0.1);

        return
    };

    private onDropDownOptionRender(nd: Node, optKey: number) {
        const item = items.find(item => item.id === optKey);
        nd.getComponentInChildren(Label).string = item.name;
    }

    private async onDropDownOptionClick(optKey: number) {
        const item = items.find(item => item.id === optKey);
        switch (item.name) {
            case "好友":
                await UIMgr.openUI(FriendUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "对话框":
                await UIMgr.openUI(DialogueUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "肉鸽":
                await UIMgr.openUI(RogueUI)
                UIMgr.closeUI(BattleUI)
                break;
            case "转盘":
                break;
            case "剧情章节":
                await UIMgr.openUI(MapModeUI)
                UIMgr.closeUI(BattleUI)
                break;
            default:
        }
    }

    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected onDestroy(): void {
        this.unscheduleAllCallbacks();
    }
    protected update(dt: number): void {
    }

    async onBattleClick() {
        await UIMgr.openUI(LoadingUI)
        UIMgr.closeUI(BattleUI)
        UIMgr.closeUI(BottomUI)
        UIMgr.closeUI(PlaneUI)
        UIMgr.closeUI(TalentUI)
        UIMgr.closeUI(ShopUI)
        UIMgr.closeUI(TopUI)
        director.preloadScene("Game", async () => {
            director.loadScene("Game")
        })
    }
}

