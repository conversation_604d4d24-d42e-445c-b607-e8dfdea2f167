{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts"], "names": ["_decorator", "Component", "Label", "DataEvent", "DataMgr", "EventMgr", "MyApp", "ButtonPlus", "UIMgr", "PlaneEquipInfoUI", "PlaneUIEvent", "OpenEquipInfoUISource", "TabStatus", "ccclass", "property", "EquipDisplay", "onLoad", "on", "TabChange", "onTabChange", "EquipSlotRefresh", "onEquipSlotRefresh", "equipBtns", "for<PERSON>ach", "v", "addClick", "onClickEquip", "onDestroy", "targetOff", "tabStatus", "Bag", "node", "active", "onEquipDisplayRefresh", "bag", "refreshItems", "hideUI", "TbEquip", "lubanTables", "equip", "eqSlots", "slots", "btn", "slot_id", "guid", "gt", "getComponentInChildren", "string", "get", "equip_id", "name", "event", "btnNode", "target", "slotID", "parseInt", "info", "getEquipSlotInfo", "lte", "openUI", "DisPlay"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;;AACnCC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,qB,kBAAAA,qB;AAAuBC,MAAAA,S,kBAAAA,S;;;;;;;;;OAC1B;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;8BAGjBe,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AAAA;AAAA,mCAAD,C,2BAFb,MACaC,YADb,SACkCd,SADlC,CAC4C;AAAA;AAAA;;AAAA;AAAA;;AAI9Be,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,WAAzC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,sCAAUG,gBAAtB,EAAwC,KAAKC,kBAA7C,EAAiE,IAAjE;AACA,eAAKC,SAAL,CAAeC,OAAf,CAAuBC,CAAC,IAAIA,CAAC,CAACC,QAAF,CAAW,KAAKC,YAAhB,EAA8B,IAA9B,CAA5B;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOT,QAAAA,WAAW,CAACU,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA,iBAAKC,qBAAL;AACH,WAHD,MAGO;AACH,iBAAKF,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;AACJ;;AAEOX,QAAAA,kBAAkB,GAAG;AACzB,eAAKY,qBAAL;AACA;AAAA;AAAA,kCAAQC,GAAR,CAAYC,YAAZ;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEOH,QAAAA,qBAAqB,GAAG;AAC5B,gBAAMI,OAAO,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBD,OAAlC;AACA;AAAA;AAAA,kCAAQE,KAAR,CAAcC,OAAd,CAAsBC,KAAtB,CAA4BlB,OAA5B,CAAoCC,CAAC,IAAI;AACrC,kBAAMkB,GAAG,GAAG,KAAKpB,SAAL,CAAeE,CAAC,CAACmB,OAAF,GAAY,CAA3B,CAAZ;;AACA,gBAAInB,CAAC,CAACoB,IAAF,CAAOC,EAAP,CAAU,CAAV,CAAJ,EAAkB;AAAA;;AACdH,cAAAA,GAAG,CAACI,sBAAJ,CAA2B5C,KAA3B,EAAkC6C,MAAlC,mBAA2CV,OAAO,CAACW,GAAR,CAAYxB,CAAC,CAACyB,QAAd,CAA3C,qBAA2C,aAAyBC,IAApE;AACH,aAFD,MAEO;AACHR,cAAAA,GAAG,CAACI,sBAAJ,CAA2B5C,KAA3B,EAAkC6C,MAAlC,GAA2C,GAA3C;AACH;AACJ,WAPD;AAQH;;AAEOrB,QAAAA,YAAY,CAACyB,KAAD,EAA0B;AAC1C,gBAAMC,OAAO,GAAGD,KAAK,CAACE,MAAtB;AACA,gBAAMC,MAAM,GAAGC,QAAQ,CAACH,OAAO,CAACF,IAAT,CAAvB;AACA,gBAAMM,IAAI,GAAG;AAAA;AAAA,kCAAQjB,KAAR,CAAcC,OAAd,CAAsBiB,gBAAtB,CAAuCH,MAAvC,CAAb;AACA,cAAI,CAACE,IAAD,IAASA,IAAI,CAACZ,IAAL,CAAUc,GAAV,CAAc,CAAd,CAAb,EAA+B;AAC/B;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,oDAA+BH,IAA/B,EAAqC;AAAA;AAAA,8DAAsBI,OAA3D;AACH;;AA/CuC,O;;;;;iBAEN,E", "sourcesContent": ["import { _decorator, Component, EventTouch, Label, Node } from 'cc';\nimport { DataEvent } from 'db://assets/scripts/Data/DataEvent';\nimport { DataMgr } from 'db://assets/scripts/Data/DataManager';\nimport { EventMgr } from 'db://assets/scripts/event/EventManager';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { ButtonPlus } from '../../../../common/components/button/ButtonPlus';\nimport { UIMgr } from '../../../../UIMgr';\nimport { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';\nimport { PlaneUIEvent } from '../../PlaneEvent';\nimport { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';\nconst { ccclass, property } = _decorator;\n\n@ccclass('EquipDisplay')\nexport class EquipDisplay extends Component {\n    @property([ButtonPlus])\n    private equipBtns: ButtonPlus[] = []\n\n    protected onLoad(): void {\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)\n        EventMgr.on(DataEvent.EquipSlotRefresh, this.onEquipSlotRefresh, this)\n        this.equipBtns.forEach(v => v.addClick(this.onClickEquip, this))\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onTabChange(tabStatus: TabStatus) {\n        if (tabStatus == TabStatus.Bag) {\n            this.node.active = true\n            this.onEquipDisplayRefresh();\n        } else {\n            this.node.active = false\n        }\n    }\n\n    private onEquipSlotRefresh() {\n        this.onEquipDisplayRefresh()\n        DataMgr.bag.refreshItems();\n        UIMgr.hideUI(PlaneEquipInfoUI)\n    }\n\n    private onEquipDisplayRefresh() {\n        const TbEquip = MyApp.lubanTables.TbEquip\n        DataMgr.equip.eqSlots.slots.forEach(v => {\n            const btn = this.equipBtns[v.slot_id - 1]\n            if (v.guid.gt(0)) {\n                btn.getComponentInChildren(Label).string = TbEquip.get(v.equip_id)?.name\n            } else {\n                btn.getComponentInChildren(Label).string = '空'\n            }\n        })\n    }\n\n    private onClickEquip(event: EventTouch): void {\n        const btnNode = event.target as Node;\n        const slotID = parseInt(btnNode.name)\n        const info = DataMgr.equip.eqSlots.getEquipSlotInfo(slotID)\n        if (!info || info.guid.lte(0)) return\n        UIMgr.openUI(PlaneEquipInfoUI, info, OpenEquipInfoUISource.DisPlay)\n    }\n}"]}