System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, tween, misc, UIOpacity, BossHurt, GameConst, _dec, _class, _crd, ccclass, property, BossUnitBase;

  function _reportPossibleCrUseOfBossHurt(extras) {
    _reporterNs.report("BossHurt", "./BossHurt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      tween = _cc.tween;
      misc = _cc.misc;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      BossHurt = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5da76SvtXBCCovJQ2kfvHY6", "BossUnitBase", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec2', 'tween', 'misc', 'UIOpacity', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossUnitBase = (_dec = ccclass('BossUnitBase'), _dec(_class = class BossUnitBase extends (_crd && BossHurt === void 0 ? (_reportPossibleCrUseOfBossHurt({
        error: Error()
      }), BossHurt) : BossHurt) {
        constructor(...args) {
          super(...args);
          this.owner = null;
          this._curHp = 0;
          this._maxHp = 0;
          this.defence = 0;
          this.blastParam = [];
          this.blastShake = [];
          this._whiteNode = null;
          this._winkCount = 0;
          this._bWinkWhite = false;
          this._winkAct = null;
        }

        initWinkWhite(whiteNode) {
          this._whiteNode = whiteNode;
          this._winkAct = tween().to(0, {
            opacity: 255
          }).to(3 * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime, {
            opacity: 0
          });

          if (this._whiteNode) {
            this._whiteNode.getComponent(UIOpacity).opacity = 0;
          }
        }

        setPropertyRate(rates) {
          if (rates.length > 2) {
            this._curHp *= rates[0];
            this._maxHp = this._curHp;
            this.attack *= rates[1];
            this._collideAtk *= rates[2];
          }
        }

        get curHp() {
          return this._curHp;
        }

        set curHp(value) {
          this._curHp = value;
        }

        get maxHp() {
          return this._maxHp;
        }

        set maxHp(value) {
          this._maxHp = value;
        }

        getAngleToOwner() {
          let angle = 0;
          let parent = this.node.parent;

          while (parent && parent !== this.owner.node) {
            angle += parent.angle;
            parent = parent.parent;
          }

          return angle;
        }

        getScenePos() {
          let pos = new Vec2(this.node.position.x, this.node.position.y).rotate(misc.degreesToRadians(this.getAngleToOwner()));
          let parent = this.node.parent;
          let scaleX = 1;
          let scaleY = 1;

          while (parent && parent.name !== 'enemyPlane') {
            scaleX *= parent.scale.x;
            scaleY *= parent.scale.y;
            pos.x += parent.position.x;
            pos.y += parent.position.y;
            parent = parent.parent;
          }

          pos.x *= scaleX;
          pos.y *= scaleY;
          return pos;
        }

        updateGameLogic(deltaTime) {
          if (!this.isDead) {
            this.m_comps.forEach(comp => {
              comp.update(deltaTime);
            });

            if (this._bWinkWhite) {
              this._winkCount++;

              if (this._winkCount > 10) {
                this._winkCount = 0;
                this._bWinkWhite = false;
              }
            }
          }
        }

        hurt(damage) {
          if (this.isDead || !this.active) {
            return false;
          }

          this.changeHp(-damage);
          this.onHurt();
          return true;
        }

        onHurt() {
          this.winkWhite();
        }

        changeHp(amount) {
          let change = amount;
          let newHp = this._curHp + amount;

          if (newHp < 0) {
            change = -this._curHp;
          }

          this._curHp = newHp;

          if (this._curHp < 0) {
            this._curHp = 0;
          }

          this.onHpChange(change);
          this.checkHp();
        }

        onHpChange(change) {
          if (this.owner && this.owner.hpChange) {
            this.owner.hpChange(change);
          }
        }

        checkHp() {
          if (this.curHp <= 0) {
            this.die();
            return true;
          }

          return false;
        }

        die() {
          if (!this.isDead) {
            this.isDead = true;
            this.onDie();
          }
        }

        onDie() {
          this.playDieAnim();
        }

        winkWhite() {
          if (!this._bWinkWhite) {
            this._bWinkWhite = true;

            if (this._whiteNode && this._winkAct) {
              this._winkAct.clone(this._whiteNode).start();
            }
          }
        }

        playDieAnim() {
          this.onDieAnimEnd();
        }

        onDieAnimEnd() {}

        getHpPercent() {
          return this.curHp / this.maxHp;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c0c3f21978816781e1f232c8f86e1c08b24ccc87.js.map