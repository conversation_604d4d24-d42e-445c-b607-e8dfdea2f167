import { _decorator, Button, EditBox } from 'cc';
import csproto from '../AutoGen/PB/cs_proto.js';
import { MyApp } from '../MyApp';
import { DevLoginData } from '../PlatformSDK/DevLoginData';
import { logDebug, logError } from '../Utils/Logger';

import { DataMgr } from '../Data/DataManager';
import { uiSelect } from './common/components/SelectList/uiSelect';
import { GmButtonUI } from './gm/GmButtonUI';
import { BaseUI, UILayer, UIMgr } from './UIMgr';
const { ccclass, property } = _decorator;

@ccclass('DevLoginUI')
export class Dev<PERSON>oginUI extends BaseUI {
    public static getUrl(): string { return "ui/DevLoginUI" };
    public static getLayer(): UILayer { return UILayer.Top }
    static needLogin = true;

    @property(Button)
    loginButton: Button = null!;
    @property(EditBox)
    usernameEditBox: EditBox = null!;
    @property(EditBox)
    passwordEditBox: EditBox = null!;

    @property(uiSelect)
    serverSelect: uiSelect = null!;

    private _onGetRoleBound: (msg: csproto.cs.IS2CMsg) => void = this.onGetRole.bind(this);

    async onHide(...args: any[]): Promise<void> {
    }

    async onShow(...args: any[]): Promise<void> {
        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound);

        DevLoginData.serverList.forEach((value, key) => {
            this.serverSelect.itemDatas.push(key)
            this.serverSelect.itemDatas.push(key + "1")
        });
        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)
        this.serverSelect.onChooseItem = (itemData: string) => {
            logDebug("LoginUI", `choose server ${itemData}`)
            DevLoginData.instance.servername = itemData;
        }
        this.usernameEditBox.string = DevLoginData.instance.user;
        this.passwordEditBox.string = DevLoginData.instance.password;
    }

    async onClose(...args: any[]): Promise<void> {
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound)
    }

    onLoginButtonClick() {
        var username = this.usernameEditBox.string;
        var password = this.passwordEditBox.string;
        DevLoginData.instance.user = username;
        DevLoginData.instance.password = password;

        MyApp.platformSDK.login((err, info) => {
            if (err) {
                logError("DevLoginUI", `login failed ${err}`);
                return;
            }
            MyApp.netMgr.login(info);
        });
    }

    onGetRole(msg: csproto.cs.IS2CMsg) {
        UIMgr.closeUI(DevLoginUI);
        DevLoginUI.needLogin = false;
        DataMgr.init()
        UIMgr.openUI(GmButtonUI)
    }
}