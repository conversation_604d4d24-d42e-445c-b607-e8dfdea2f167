{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts"], "names": ["_decorator", "size", "UIOpacity", "UITransform", "v2", "Vec2", "Vec3", "EnemyEntity", "GameEnum", "EnemyEffectComp", "EnemyAttrComponent", "Tools", "BattleLayer", "GameConst", "Bullet", "GameIns", "MainPlane", "ColliderGroupType", "FBoxCollider", "ccclass", "property", "EnemyBase", "uiData", "scaleType", "propertyRate", "_curHp", "exp", "maxHp", "defence", "resist", "hurtBuffMap", "Map", "_hurtBuffTime", "_fireDemage", "_fireHurtCd", "_fireHurtTime", "_isFireCirt", "_buffCount<PERSON>rr", "collide<PERSON>omp", "_collideLevel", "EnemyCollideLevel", "Main", "bCollideDead", "damaged", "_isTracked", "_countTime", "_bStandBy", "_standByTime", "_standByEnd", "_lootArr", "_lootItemArr", "_curLoot", "_lootHp", "_lootNeedHp", "_lootHpUnit", "_itemParent", "_isItem", "effectComp", "attrCom", "dieBullet", "bullets", "itemParent", "value", "isItem", "collideLevel", "collideAble", "isEnable", "preLoad", "addComponent", "init", "groupType", "ENEMY_NORMAL", "node", "getComponent", "addScript", "reset", "collideAtk", "clear", "removeAllBuffEffect", "setUIData", "data", "setCollideData", "collider", "initComps", "setScaleType", "type", "getScaleType", "initAttr", "attr", "hasAttribution", "setExp", "m_comps", "for<PERSON>ach", "comp", "startBattle", "addLoot", "loot", "push", "updateGameLogic", "deltaTime", "isDead", "checkStandby", "updateSkillResist", "update", "active", "opacity", "key", "time", "get", "set", "removeBuff", "initPropertyRate", "rates", "length", "curHp", "attack", "getUIData", "setStandByTime", "isStandBy", "setPos", "x", "y", "isTracked", "setPosition", "getDir", "ZERO", "getAngle", "isFullBlood", "getMaxHp", "hp", "getHpPercent", "changeHp", "delta", "checkHp", "die", "EnemyDestroyType", "Die", "setCurHp", "getCurHp", "isDamageable", "sceneLayer", "position", "convertToWorldSpaceAR", "me", "enemyPlane<PERSON><PERSON>er", "convertToNodeSpaceAR", "ViewCenter", "setDamaged", "getSkillResist", "skillType", "addBuff", "buffType", "params", "isCritical", "Enemy<PERSON>uff", "Ice", "Fire", "<PERSON><PERSON><PERSON>", "count", "onAddBuff", "delete", "<PERSON><PERSON><PERSON><PERSON>", "onRemoveBuff", "buffD<PERSON>", "skillResistUIDict", "fireDamage", "hpParam", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity", "offset", "setCollideEntity", "setCollideOffset", "setCollideScale", "widthScale", "heightScale", "onCollide", "getAttack", "hurtEffectManager", "createHurtNumByType", "getPosition", "finalDamage", "Math", "max", "hurt", "damage", "_checkHurtLoot", "onHurt", "checkLoot", "destroyType", "onDie", "bullet", "dieRemove", "EnemyType", "Ligature", "LigatureLine", "<PERSON><PERSON><PERSON>", "GoldBox", "LigatureUnit", "worldPosition", "enemyManager", "checkEnemyDieBomb", "will<PERSON><PERSON><PERSON>", "showAttrShield", "playDieAnim", "blastSound", "scheduleOnce", "onDieAnimEnd", "addBullet", "removeBullet", "index", "indexOf", "splice"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACtDC,MAAAA,W;;AACAC,MAAAA,Q;;AACAC,MAAAA,e;;AACAC,MAAAA,kB;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,W;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,M;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;AACWC,MAAAA,iB,kBAAAA,iB;;AACbC,MAAAA,Y;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;yBAGTqB,S,GADpBF,O,UAAD,MACqBE,SADrB;AAAA;AAAA,sCACmD;AAAA;AAAA;AAAA,eAE/CC,MAF+C,GAEtC,IAFsC;AAAA,eAG/CC,SAH+C,GAGnC,CAAC,CAHkC;AAAA,eAI/CC,YAJ+C,GAIhC,EAJgC;AAAA,eAK/CC,MAL+C,GAKtC,CALsC;AAAA,eAM/CC,GAN+C,GAMzC,CANyC;AAAA,eAO/CC,KAP+C,GAOvC,CAPuC;AAAA,eAQ/CC,OAR+C,GAQrC,CARqC;AAAA,eAS/CC,MAT+C,GAStC,EATsC;AAAA,eAU/CC,WAV+C,GAUjC,IAAIC,GAAJ,EAViC;AAAA,eAW/CC,aAX+C,GAW/B,IAAID,GAAJ,EAX+B;AAAA,eAY/CE,WAZ+C,GAYjC,CAZiC;AAAA,eAa/CC,WAb+C,GAajC,CAbiC;AAAA,eAc/CC,aAd+C,GAc/B,CAd+B;AAAA,eAe/CC,WAf+C,GAejC,KAfiC;AAAA,eAgB/CC,aAhB+C,GAgB/B,IAAIN,GAAJ,EAhB+B;AAAA,eAiB/CO,WAjB+C,GAiBpB,IAjBoB;AAAA,eAkB/CC,aAlB+C,GAkB/B;AAAA;AAAA,oCAASC,iBAAT,CAA2BC,IAlBI;AAAA,eAmB/CC,YAnB+C,GAmBhC,KAnBgC;AAAA,eAoB/CC,OApB+C,GAoBrC,KApBqC;AAAA,eAqB/CC,UArB+C,GAqBlC,KArBkC;AAAA,eAsB/CC,UAtB+C,GAsBlC,CAtBkC;AAAA,eAuB/CC,SAvB+C,GAuBnC,KAvBmC;AAAA,eAwB/CC,YAxB+C,GAwBhC,CAxBgC;AAAA,eAyB/CC,WAzB+C,GAyBjC,KAzBiC;AAAA,eA0B/CC,QA1B+C,GA0BpC,EA1BoC;AAAA,eA2B/CC,YA3B+C,GA2BhC,EA3BgC;AAAA,eA4B/CC,QA5B+C,GA4BpC,IA5BoC;AAAA,eA6B/CC,OA7B+C,GA6BrC,CA7BqC;AAAA,eA8B/CC,WA9B+C,GA8BjC,CA9BiC;AAAA,eA+B/CC,WA/B+C,GA+BjC,CA/BiC;AAAA,eAgC/CC,WAhC+C,GAgCjC,IAhCiC;AAAA,eAiC/CC,OAjC+C,GAiCrC,KAjCqC;AAAA,eAkC/CC,UAlC+C,GAkClC,IAlCkC;AAAA,eAmC/CC,OAnC+C,GAmCrC,IAnCqC;AAAA,eAoC/CC,SApC+C,GAoCnC,KApCmC;AAAA,eAqC/CC,OArC+C,GAqCrC,EArCqC;AAAA;;AAuCjC,YAAVC,UAAU,GAAG;AACb,iBAAO,KAAKN,WAAZ;AACH;;AACa,YAAVM,UAAU,CAACC,KAAD,EAAQ;AAClB,eAAKP,WAAL,GAAmBO,KAAnB;AACH;;AACS,YAANC,MAAM,GAAG;AACT,iBAAO,KAAKP,OAAZ;AACH;;AACS,YAANO,MAAM,CAACD,KAAD,EAAQ;AACd,eAAKN,OAAL,GAAeM,KAAf;AACH;;AACe,YAAZE,YAAY,GAAG;AACf,iBAAO,KAAKzB,aAAZ;AACH;;AACe,YAAZyB,YAAY,CAACF,KAAD,EAAQ;AACpB,eAAKvB,aAAL,GAAqBuB,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACmB,YAAXG,WAAW,GAAG;AACd,iBAAO,KAAK3B,WAAL,IAAoB,KAAKA,WAAL,CAAiB4B,QAA5C;AACH;AAED;AACJ;AACA;AACA;;;AACmB,YAAXD,WAAW,CAACH,KAAD,EAAQ;AACnB,cAAI,KAAKxB,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB4B,QAAjB,GAA4BJ,KAA5B;AACH;AACJ;AACD;AACJ;AACA;;;AACIK,QAAAA,OAAO,GAAG;AACN;AACA,eAAK7B,WAAL,GAAmB,KAAK8B,YAAL;AAAA;AAAA,2CAAnB;AACA,eAAK9B,WAAL,CAAiB+B,IAAjB,CAAsB,IAAtB;AACA,eAAK/B,WAAL,CAAiBgC,SAAjB,GAA6B;AAAA;AAAA,sDAAkBC,YAA/C,CAJM,CAMN;;AACA,eAAKd,UAAL,GAAkB,KAAKe,IAAL,CAAUC,YAAV;AAAA;AAAA,iDAAlB,CAPM,CASN;;AACA,eAAKf,OAAL,GAAe;AAAA;AAAA,8BAAMgB,SAAN,CAAgB,KAAKF,IAArB;AAAA;AAAA,uDAAf;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,KAAK,GAAG;AACJ,gBAAMA,KAAN;AACA,eAAKrD,MAAL,GAAc,IAAd;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKE,MAAL,GAAc,CAAd;AACA,eAAKE,KAAL,GAAa,CAAb;AACA,eAAKD,GAAL,GAAW,CAAX;AACA,eAAKkD,UAAL,GAAkB,CAAlB;AACA,eAAK9C,WAAL,CAAiB+C,KAAjB;;AACA,eAAK7C,aAAL,CAAmB6C,KAAnB;;AACA,eAAKhD,MAAL,GAAc,EAAd;AACA,eAAKe,UAAL,GAAkB,KAAlB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,SAAL,GAAiB,KAAjB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKR,aAAL,GAAqB;AAAA;AAAA,oCAASC,iBAAT,CAA2BC,IAAhD;AACA,eAAKE,OAAL,GAAe,KAAf;AACA,eAAKM,QAAL,GAAgB,EAAhB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKC,QAAL,GAAgB,IAAhB;AACA,eAAKC,OAAL,GAAe,CAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,WAAL,GAAmB,IAAnB;AACA,eAAKC,OAAL,GAAe,KAAf;AACA,eAAKsB,mBAAL;AACA,eAAKnB,SAAL,GAAiB,KAAjB;AACA,eAAKC,OAAL,GAAe,EAAf;AACH;AAED;AACJ;AACA;AACA;;;AACImB,QAAAA,SAAS,CAACC,IAAD,EAAO;AACZ,eAAK1D,MAAL,GAAc0D,IAAd;;AACA,cAAI,KAAK1D,MAAT,EAAiB;AACb,iBAAK2D,cAAL,CAAoB,KAAK3D,MAAL,CAAY4D,QAAhC;AACA,iBAAKC,SAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,IAAD,EAAO;AACf,eAAK9D,SAAL,GAAiB8D,IAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAK/D,SAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIgE,QAAAA,QAAQ,CAACC,IAAD,EAAO;AACX,eAAK9B,OAAL,CAAaW,IAAb,CAAkB,IAAlB,EAAwBmB,IAAI,IAAI,EAAhC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACD,IAAD,EAAO;AACjB,iBAAO,KAAK9B,OAAL,IAAgB,KAAKA,OAAL,CAAa+B,cAAb,CAA4BD,IAA5B,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,MAAM,CAAChE,GAAD,EAAM;AACR,eAAKA,GAAL,GAAWA,GAAX;AACH;AAED;AACJ;AACA;;;AACIyD,QAAAA,SAAS,GAAG;AACR,eAAKQ,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACxB,IAAL,CAAU,IAAV;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACIyB,QAAAA,WAAW,GAAG;AACV,eAAK7B,WAAL,GAAmB,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACI8B,QAAAA,OAAO,CAACC,IAAD,EAAO;AACV,eAAK/C,QAAL,CAAcgD,IAAd,CAAmBD,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,cAAI,KAAKC,MAAL,IAAe,KAAKC,YAAL,CAAkBF,SAAlB,CAAnB,EAAiD;AAC7C;AACH,WAHsB,CAKvB;;;AACA,eAAKG,iBAAL,CAAuBH,SAAvB,EANuB,CAQvB;;AACA,cAAI,KAAKzC,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAawC,eAAb,CAA6BC,SAA7B;AACH;;AAED,eAAKvD,UAAL,GAAkB,KAAlB,CAbuB,CAevB;;AACA,eAAK+C,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACU,MAAL,CAAYJ,SAAZ;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,SAAD,EAAY;AACpB,eAAKtD,UAAL,IAAmBsD,SAAnB;;AACA,cAAI,KAAKrD,SAAT,EAAoB;AAChB,gBAAI,KAAKD,UAAL,GAAkB,KAAKE,YAA3B,EAAyC;AACrC,mBAAKyD,MAAL,GAAc,IAAd;AACA,mBAAK1D,SAAL,GAAiB,KAAjB;AACA,mBAAKD,UAAL,GAAkB,CAAlB;AACA,mBAAKG,WAAL,GAAmB,IAAnB;AACA,mBAAKwB,IAAL,CAAUC,YAAV,CAAuBvE,SAAvB,EAAkCuG,OAAlC,GAA4C,GAA5C;AACA,mBAAKX,WAAL;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIQ,QAAAA,iBAAiB,CAACH,SAAD,EAAY;AACzB,eAAKrE,WAAL,CAAiB8D,OAAjB,CAAyB,CAAC9B,KAAD,EAAQ4C,GAAR,KAAgB;AACrC,kBAAMC,IAAI,GAAG,KAAK3E,aAAL,CAAmB4E,GAAnB,CAAuBF,GAAvB,CAAb;;AACA,gBAAIC,IAAI,KAAK,IAAT,IAAiBA,IAAI,GAAG,CAA5B,EAA+B;AAC3B,mBAAK3E,aAAL,CAAmB6E,GAAnB,CAAuBH,GAAvB,EAA4BC,IAAI,GAAGR,SAAnC;AACH,aAFD,MAEO;AACH,mBAAKW,UAAL,CAAgBJ,GAAhB;AACH;AACJ,WAPD;AAQH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,gBAAgB,CAACC,KAAD,EAAQ;AACpB,eAAKxF,YAAL,GAAoBwF,KAApB;;AACA,cAAI,KAAKxF,YAAL,CAAkByF,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAKC,KAAL,IAAc,KAAK1F,YAAL,CAAkB,CAAlB,CAAd;AACA,iBAAKG,KAAL,GAAa,KAAKuF,KAAlB;AACA,iBAAKC,MAAL,IAAe,KAAK3F,YAAL,CAAkB,CAAlB,CAAf;AACA,iBAAKoD,UAAL,IAAmB,KAAKpD,YAAL,CAAkB,CAAlB,CAAnB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI4F,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAK9F,MAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACI+F,QAAAA,cAAc,CAACV,IAAD,EAAO;AACjB,eAAK7D,SAAL,GAAiB,IAAjB;AACA,eAAKC,YAAL,GAAoB4D,IAApB;AACA,eAAKnC,IAAL,CAAUC,YAAV,CAAuBvE,SAAvB,EAAkCuG,OAAlC,GAA4C,CAA5C;AACH;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAKxE,SAAZ;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIyE,QAAAA,MAAM,CAACC,CAAD,EAAIC,CAAJ,EAAOC,SAAS,GAAG,KAAnB,EAA0B;AAC5B,eAAKlD,IAAL,CAAUmD,WAAV,CAAsBH,CAAtB,EAAyBC,CAAzB;AACA,eAAK7E,UAAL,GAAkB8E,SAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACiB,YAATA,SAAS,GAAG;AACZ,iBAAO,KAAK9E,UAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIgF,QAAAA,MAAM,GAAG;AACL,iBAAOvH,IAAI,CAACwH,IAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,iBAAO,KAAKb,KAAL,IAAc,KAAKvF,KAA1B;AACH;AAED;AACJ;AACA;AACA;;;AACIqG,QAAAA,QAAQ,GAAG;AACP,iBAAO,KAAKrG,KAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAALuF,KAAK,GAAG;AACR,iBAAO,KAAKzF,MAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAALyF,KAAK,CAACe,EAAD,EAAK;AACV,eAAKxG,MAAL,GAAcwG,EAAd;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAKhB,KAAL,GAAa,KAAKvF,KAAzB;AACH;AAED;AACJ;AACA;AACA;;;AACIwG,QAAAA,QAAQ,CAACC,KAAD,EAAQ;AACZ,eAAKlB,KAAL,IAAckB,KAAd;;AACA,cAAI,KAAKlB,KAAL,GAAa,CAAjB,EAAoB;AAChB,iBAAKA,KAAL,GAAa,CAAb;AACH,WAFD,MAEO,IAAI,KAAKA,KAAL,GAAa,KAAKvF,KAAtB,EAA6B;AAChC,iBAAKuF,KAAL,GAAa,KAAKvF,KAAlB;AACH;;AACD,eAAK0G,OAAL;AACH;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,OAAO,GAAG;AACN,cAAI,KAAKnB,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKoB,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,GAAnC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,QAAQ,CAACR,EAAD,EAAK;AACT,eAAKf,KAAL,GAAae,EAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,QAAQ,GAAG;AACP,iBAAO,KAAKxB,KAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKC,UAAL,GAAkB,CAAlB,IAAuB,CAAC,KAAK9F,SAAjC,EAA4C;AACxC,gBAAI,KAAKH,OAAT,EAAkB;AACd,qBAAO,IAAP;AACH;;AACD,gBAAIkG,QAAQ,GAAG,KAAKrE,IAAL,CAAUqE,QAAzB;;AACA,gBAAI,KAAKhF,UAAL,KAAoB,IAAxB,EAA8B;AAC1BgF,cAAAA,QAAQ,GAAG,KAAKrE,IAAL,CAAUC,YAAV,CAAuBtE,WAAvB,EAAoC2I,qBAApC,CAA0DxI,IAAI,CAACuH,IAA/D,CAAX;AACAgB,cAAAA,QAAQ,GAAG;AAAA;AAAA,8CAAYE,EAAZ,CAAeC,eAAf,CAA+BvE,YAA/B,CAA4CtE,WAA5C,EAAyD8I,oBAAzD,CAA8EJ,QAA9E,CAAX;AACH;;AACD,mBACIA,QAAQ,CAACpB,CAAT,GAAa,CAAb,IACAoB,QAAQ,CAACrB,CAAT,GAAa,CAAC;AAAA;AAAA,wCAAU0B,UAAV,CAAqB1B,CADnC,IAEAqB,QAAQ,CAACrB,CAAT,GAAa;AAAA;AAAA,wCAAU0B,UAAV,CAAqB1B,CAHtC;AAKH;;AACD,iBAAO,CAAC,KAAK1E,SAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIqG,QAAAA,UAAU,CAACrF,KAAD,EAAQ;AACd,eAAKnB,OAAL,GAAemB,KAAf;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIsF,QAAAA,cAAc,CAACC,SAAD,EAAY;AACtB,iBAAO,KAAKxH,MAAL,CAAYwH,SAAZ,KAA0B,CAAjC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACC,QAAD,EAAWC,MAAX,EAAmBC,UAAnB,EAA+B;AAClC,eAAK3H,WAAL,CAAiB+E,GAAjB,CAAqB0C,QAArB,EAA+B,IAA/B;;AACA,kBAAQA,QAAR;AACI,iBAAK;AAAA;AAAA,sCAASG,SAAT,CAAmBC,GAAxB;AACI,mBAAK3H,aAAL,CAAmB6E,GAAnB,CAAuB0C,QAAvB,EAAiCC,MAAM,CAAC,CAAD,CAAvC;;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAASE,SAAT,CAAmBE,IAAxB;AACI,mBAAKxH,WAAL,GAAmBqH,UAAnB;;AACA,mBAAKzH,aAAL,CAAmB6E,GAAnB,CAAuB0C,QAAvB,EAAiCC,MAAM,CAAC,CAAD,CAAvC;;AACA,mBAAKvH,WAAL,GAAmBuH,MAAM,CAAC,CAAD,CAAN,KAAc,CAAd,GAAkB,KAAKvH,WAAvB,GAAqCuH,MAAM,CAAC,CAAD,CAA9D;AACA,mBAAKtH,WAAL,GAAmBsH,MAAM,CAAC,CAAD,CAAzB;AACA;;AACJ,iBAAK;AAAA;AAAA,sCAASE,SAAT,CAAmBG,KAAxB;AACI,oBAAMC,KAAK,GAAG,KAAKzH,aAAL,CAAmBuE,GAAnB,CAAuB2C,QAAvB,KAAoC,CAAlD;;AACA,mBAAKlH,aAAL,CAAmBwE,GAAnB,CAAuB0C,QAAvB,EAAiCO,KAAK,GAAG,CAAzC;;AACA;AAbR;;AAeA,eAAKC,SAAL,CAAeR,QAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIzC,QAAAA,UAAU,CAACyC,QAAD,EAAW;AACjB,eAAKzH,WAAL,CAAiBkI,MAAjB,CAAwBT,QAAxB;;AACA,eAAKvH,aAAL,CAAmBgI,MAAnB,CAA0BT,QAA1B;;AACA,cAAIU,YAAY,GAAG,IAAnB;;AACA,cAAIV,QAAQ,KAAK;AAAA;AAAA,oCAASG,SAAT,CAAmBG,KAApC,EAA2C;AACvC,kBAAMC,KAAK,GAAG,KAAKzH,aAAL,CAAmBuE,GAAnB,CAAuB2C,QAAvB,CAAd;;AACA,gBAAIO,KAAK,GAAG,CAAZ,EAAe;AACX,mBAAKzH,aAAL,CAAmBwE,GAAnB,CAAuB0C,QAAvB,EAAiCO,KAAK,GAAG,CAAzC;;AACA,kBAAIA,KAAK,GAAG,CAAR,GAAY,CAAhB,EAAmB;AACfG,gBAAAA,YAAY,GAAG,KAAf;AACH;AACJ;AACJ;;AACD,cAAIA,YAAJ,EAAkB;AACd,iBAAKC,YAAL,CAAkBX,QAAlB;AACH;AACJ;AAED;AACJ;AACA;;;AACIzE,QAAAA,mBAAmB,GAAG;AAClB,eAAKzC,aAAL,CAAmBwC,KAAnB;;AACA,cAAI,KAAKpB,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBqB,mBAAhB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIoF,QAAAA,YAAY,CAACX,QAAD,EAAW;AACnB,cAAI,KAAK9F,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBqD,UAAhB,CAA2ByC,QAA3B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIQ,QAAAA,SAAS,CAACR,QAAD,EAAW;AAChB,cAAI,KAAK9F,UAAL,IAAmB,KAAKnC,MAA5B,EAAoC;AAChC,kBAAM6I,QAAQ,GAAG,KAAK7I,MAAL,CAAY8I,iBAAZ,CAA8Bb,QAA9B,CAAjB;;AACA,oBAAQA,QAAR;AACI,mBAAK;AAAA;AAAA,wCAASG,SAAT,CAAmBC,GAAxB;AACI,oBAAIQ,QAAQ,IAAIA,QAAQ,CAAClD,MAAT,GAAkB,CAAlC,EAAqC;AACjC,uBAAKxD,UAAL,CAAgB6F,OAAhB,CAAwBC,QAAxB,EAAkCY,QAAlC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,wCAAST,SAAT,CAAmBE,IAAxB;AACI,sBAAMS,UAAU,GAAG,MAAM,KAAK/I,MAAL,CAAYgJ,OAAZ,CAAoB,CAApB,CAAzB;;AACA,oBAAI,CAACH,QAAD,IAAaE,UAAjB,EAA6B;AACzBF,kBAAAA,QAAQ,CAAC,CAAD,CAAR,CAAY,CAAZ,IAAiBE,UAAjB;AACH;;AACD,qBAAK5G,UAAL,CAAgB6F,OAAhB,CAAwBC,QAAxB,EAAkCY,QAAlC;AACA;;AACJ,mBAAK;AAAA;AAAA,wCAAST,SAAT,CAAmBG,KAAxB;AACI,qBAAKpG,UAAL,CAAgB6F,OAAhB,CAAwBC,QAAxB,EAAkCY,QAAlC;AACA;AAfR;AAiBH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACII,QAAAA,WAAW,CAAChB,QAAD,EAAW;AAClB,iBAAO,KAAKzH,WAAL,CAAiB8E,GAAjB,CAAqB2C,QAArB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACItE,QAAAA,cAAc,CAACC,QAAD,EAAWsF,MAAM,GAAG,IAApB,EAA0BC,MAAM,GAAGrK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAArC,EAA6C;AACvD,cAAI,KAAKkC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiB+B,IAAjB,CAAsBmG,MAAM,IAAI,IAAhC,EAAqCvK,IAAI,CAACiF,QAAQ,CAAC,CAAD,CAAT,EAAaA,QAAQ,CAAC,CAAD,CAArB,CAAzC,EAAoEuF,MAApE;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAACF,MAAD,EAAS;AACrB,cAAI,KAAKlI,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBkI,MAAjB,GAA0BA,MAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,gBAAgB,CAACF,MAAD,EAAS;AACrB,cAAI,KAAKnI,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBmI,MAAjB,GAA0BA,MAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,eAAe,CAACC,UAAD,EAAaC,WAAb,EAA0B;AACrC,cAAI,KAAKxI,WAAL,IAAoB,KAAKhB,MAA7B,EAAqC;AACjC,iBAAKgB,WAAL,CAAiBrC,IAAjB,GAAwBA,IAAI,CACxB,KAAKqB,MAAL,CAAY4D,QAAZ,CAAqB,CAArB,IAA0B2F,UADF,EAExB,KAAKvJ,MAAL,CAAY4D,QAAZ,CAAqB,CAArB,IAA0B4F,WAFF,CAA5B;AAIH;AACJ;;AAEDC,QAAAA,SAAS,CAAC7F,QAAD,EAAW;AAChB,cAAI,KAAKsB,MAAL,IAAe,CAAC,KAAKJ,MAAzB,EAAiC;AAC7B,gBAAIlB,QAAQ,CAACsF,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,oBAAMrD,MAAM,GAAGjC,QAAQ,CAACsF,MAAT,CAAgBQ,SAAhB,CAA0B,IAA1B,CAAf;AACA;AAAA;AAAA,sCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8ChG,QAAQ,CAACsF,MAAT,CAAgBhG,IAAhB,CAAqB2G,WAArB,EAA9C,EAAiFhE,MAAjF;AACA,oBAAMiE,WAAW,GAAGC,IAAI,CAACC,GAAL,CAASnE,MAAM,GAAG,EAAlB,EAAsBA,MAAM,GAAG,KAAKvF,OAApC,CAApB;;AACA,kBAAI,KAAK2J,IAAL,CAAUH,WAAV,CAAJ,EAA4B,CAC3B;AACJ,aAND,MAMO,IAAIlG,QAAQ,CAACsF,MAAT;AAAA;AAAA,2CAAwC,KAAKxG,YAAL,KAAsB;AAAA;AAAA,sCAASxB,iBAAT,CAA2BC,IAAzF,IAAiG,KAAKC,YAA1G,EAAwH;AAC3H,mBAAK4F,GAAL,CAAS;AAAA;AAAA,wCAASC,gBAAT,CAA0BC,GAAnC;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACI+C,QAAAA,IAAI,CAACC,MAAD,EAAS;AACT,cAAI,CAAC,KAAKhF,MAAN,IAAgB,KAAKJ,MAArB,IAA+B,CAAC,KAAKuC,YAAL,EAApC,EAAyD;AACrD,mBAAO,KAAP;AACH;;AACD,eAAKR,QAAL,CAAc,CAACqD,MAAf;;AACA,eAAKC,cAAL,CAAoBD,MAApB;;AACA,eAAKE,MAAL;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACID,QAAAA,cAAc,CAACD,MAAD,EAAS;AACnB,cAAI,KAAKrI,QAAT,EAAmB;AACf,iBAAKwI,SAAL,CAAeH,MAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIG,QAAAA,SAAS,CAACH,MAAM,GAAG,CAAV,EAAa3C,QAAQ,GAAGxI,IAAI,CAACwH,IAA7B,EAAmC,CACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACI6D,QAAAA,MAAM,GAAG,CAAG;AAEZ;AACJ;AACA;AACA;;;AACIpD,QAAAA,GAAG,CAACsD,WAAD,EAAc;AACb,cAAI,KAAKxF,MAAT,EAAiB;AACb;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,eAAKnC,WAAL,GAAmB,KAAnB;;AAEA,cAAI,KAAKP,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa4E,GAAb;AACH;;AAED,eAAKuD,KAAL,CAAWD,WAAX;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,CAACD,WAAD,EAAc;AACf,cAAIA,WAAW,KAAK;AAAA;AAAA,oCAASrD,gBAAT,CAA0BC,GAA9C,EAAmD;AAE/C,gBAAI,KAAK7E,SAAT,EAAoB;AAChB,mBAAK,MAAMmI,MAAX,IAAqB,KAAKlI,OAA1B,EAAmC;AAC/BkI,gBAAAA,MAAM,CAACC,SAAP;AACH;AACJ;;AAED,gBAAI,CAAC,KAAKhI,MAAV,EAAkB,CACd;AACA;AACA;AACH;AACJ;;AAED,eAAKH,OAAL,GAAe,EAAf;;AAEA,kBAAQ,KAAKyB,IAAb;AACI,iBAAK;AAAA;AAAA,sCAAS2G,SAAT,CAAmBC,QAAxB;AACA,iBAAK;AAAA;AAAA,sCAASD,SAAT,CAAmBE,YAAxB;AACI;;AACJ;AACI,kBAAIrD,QAAQ,GAAG,KAAKrE,IAAL,CAAUqE,QAAzB;;AACA,kBAAI,KAAKD,UAAL,GAAkB,CAAtB,EAAyB;AACrB,oBACI,KAAKvD,IAAL,KAAc;AAAA;AAAA,0CAAS2G,SAAT,CAAmBG,MAAjC,IACA,KAAK9G,IAAL,KAAc;AAAA;AAAA,0CAAS2G,SAAT,CAAmBI,OADjC,IAEA,KAAK/G,IAAL,KAAc;AAAA;AAAA,0CAAS2G,SAAT,CAAmBK,YAHrC,EAIE;AACExD,kBAAAA,QAAQ,GAAG,KAAKrE,IAAL,CAAUC,YAAV,CAAuBtE,WAAvB,EAAoC2I,qBAApC,CAA0DxI,IAAI,CAACuH,IAA/D,CAAX;AACAgB,kBAAAA,QAAQ,GAAG;AAAA;AAAA,kDAAYE,EAAZ,CAAeC,eAAf,CAA+BvE,YAA/B,CAA4CtE,WAA5C,EAAyD8I,oBAAzD,CAA8EJ,QAA9E,CAAX;AACH;AACJ,eATD,MASO;AACH,sBAAMyD,aAAa,GAAG,KAAK9H,IAAL,CAAUC,YAAV,CAAuBtE,WAAvB,EAAoC2I,qBAApC,CAA0DxI,IAAI,CAACuH,IAA/D,CAAtB;AACAgB,gBAAAA,QAAQ,GAAG;AAAA;AAAA,gDAAYE,EAAZ,CAAeC,eAAf,CAA+BvE,YAA/B,CAA4CtE,WAA5C,EAAyD8I,oBAAzD,CAA8EqD,aAA9E,CAAX;AACH;;AACD;AAAA;AAAA,sCAAQC,YAAR,CAAqBC,iBAArB,CAAuC3D,QAAvC;AAnBR;AAqBH;AAED;AACJ;AACA;;;AACI4D,QAAAA,UAAU,GAAG,CAAG;AAEhB;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKhJ,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAagJ,cAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,cAAI,CAAC,KAAKrL,MAAV,EAAkB;AACd;AACH;;AAED,cAAI,KAAKA,MAAL,CAAYsL,UAAZ,GAAyB,CAA7B,EAAgC,CAC5B;AACH;;AAED,eAAKC,YAAL,CAAkB,MAAM;AACpB,iBAAK/H,mBAAL;AACA,iBAAKgI,YAAL;AACH,WAHD,EAGG,GAHH;AAIH;AAED;AACJ;AACA;;;AACIA,QAAAA,YAAY,GAAG,CAAG;AAElB;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACjB,MAAD,EAAS;AACd,cAAI,KAAKnI,SAAL,IAAkB,KAAKC,OAA3B,EAAoC;AAChC,iBAAKA,OAAL,CAAaqC,IAAb,CAAkB6F,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIkB,QAAAA,YAAY,CAAClB,MAAD,EAAS;AACjB,cAAI,KAAKnI,SAAL,IAAkB,KAAKC,OAA3B,EAAoC;AAChC,kBAAMqJ,KAAK,GAAG,KAAKrJ,OAAL,CAAasJ,OAAb,CAAqBpB,MAArB,CAAd;;AACA,gBAAImB,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKrJ,OAAL,CAAauJ,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAnxB8C,O", "sourcesContent": ["import { _decorator, size, UIOpacity, UITransform, v2, Vec2, Vec3 } from \"cc\";\r\nimport EnemyEntity from \"./EnemyEntity\";\r\nimport GameEnum from \"../../../const/GameEnum\";\r\nimport EnemyEffectComp from \"./EnemyEffectComp\";\r\nimport EnemyAttrComponent from \"./EnemyAttrComponent\";\r\nimport { Tools } from \"../../../utils/Tools\";\r\nimport BattleLayer from \"../../layer/BattleLayer\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport Bullet from \"../../bullet/Bullet\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { MainPlane } from \"../mainPlane/MainPlane\";\r\nimport FCollider, { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass\r\nexport default class EnemyBase extends EnemyEntity {\r\n\r\n    uiData = null;\r\n    scaleType = -1;\r\n    propertyRate = [];\r\n    _curHp = 0;\r\n    exp = 0;\r\n    maxHp = 0;\r\n    defence = 0;\r\n    resist = {};\r\n    hurtBuffMap = new Map();\r\n    _hurtBuffTime = new Map();\r\n    _fireDemage = 0;\r\n    _fireHurtCd = 0;\r\n    _fireHurtTime = 0;\r\n    _isFireCirt = false;\r\n    _buffCountArr = new Map();\r\n    collideComp:FBoxCollider = null;\r\n    _collideLevel = GameEnum.EnemyCollideLevel.Main;\r\n    bCollideDead = false;\r\n    damaged = false;\r\n    _isTracked = false;\r\n    _countTime = 0;\r\n    _bStandBy = false;\r\n    _standByTime = 0;\r\n    _standByEnd = false;\r\n    _lootArr = [];\r\n    _lootItemArr = [];\r\n    _curLoot = null;\r\n    _lootHp = 0;\r\n    _lootNeedHp = 0;\r\n    _lootHpUnit = 0;\r\n    _itemParent = null;\r\n    _isItem = false;\r\n    effectComp = null;\r\n    attrCom = null;\r\n    dieBullet = false;\r\n    bullets = [];\r\n\r\n    get itemParent() {\r\n        return this._itemParent;\r\n    }\r\n    set itemParent(value) {\r\n        this._itemParent = value;\r\n    }\r\n    get isItem() {\r\n        return this._isItem;\r\n    }\r\n    set isItem(value) {\r\n        this._isItem = value;\r\n    }\r\n    get collideLevel() {\r\n        return this._collideLevel;\r\n    }\r\n    set collideLevel(value) {\r\n        this._collideLevel = value;\r\n    }\r\n\r\n    /**\r\n     * 获取碰撞是否可用\r\n     * @returns {boolean} 是否可用\r\n     */\r\n    get collideAble() {\r\n        return this.collideComp && this.collideComp.isEnable;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞是否可用\r\n     * @param {boolean} value 是否可用\r\n     */\r\n    set collideAble(value) {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = value;\r\n        }\r\n    }\r\n    /**\r\n  * 预加载敌人组件\r\n  */\r\n    preLoad() {\r\n        // 添加碰撞组件并初始化\r\n        this.collideComp = this.addComponent(FBoxCollider);\r\n        this.collideComp.init(this);\r\n        this.collideComp.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n\r\n        // 获取敌人效果组件\r\n        this.effectComp = this.node.getComponent(EnemyEffectComp);\r\n\r\n        // 添加属性组件\r\n        this.attrCom = Tools.addScript(this.node, EnemyAttrComponent);\r\n    }\r\n\r\n    /**\r\n     * 重置敌人状态\r\n     */\r\n    reset() {\r\n        super.reset();\r\n        this.uiData = null;\r\n        this.scaleType = -1;\r\n        this._curHp = 0;\r\n        this.maxHp = 0;\r\n        this.exp = 0;\r\n        this.collideAtk = 0;\r\n        this.hurtBuffMap.clear();\r\n        this._hurtBuffTime.clear();\r\n        this.resist = {};\r\n        this._isTracked = false;\r\n        this._countTime = 0;\r\n        this._bStandBy = false;\r\n        this._standByTime = 0;\r\n        this._collideLevel = GameEnum.EnemyCollideLevel.Main;\r\n        this.damaged = false;\r\n        this._lootArr = [];\r\n        this._lootItemArr = [];\r\n        this._curLoot = null;\r\n        this._lootHp = 0;\r\n        this._lootNeedHp = 0;\r\n        this._lootHpUnit = 0;\r\n        this._itemParent = this;\r\n        this._isItem = false;\r\n        this.removeAllBuffEffect();\r\n        this.dieBullet = false;\r\n        this.bullets = [];\r\n    }\r\n\r\n    /**\r\n     * 设置 UI 数据\r\n     * @param {Object} data UI 数据\r\n     */\r\n    setUIData(data) {\r\n        this.uiData = data;\r\n        if (this.uiData) {\r\n            this.setCollideData(this.uiData.collider);\r\n            this.initComps();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置缩放类型\r\n     * @param {number} type 缩放类型\r\n     */\r\n    setScaleType(type) {\r\n        this.scaleType = type;\r\n    }\r\n\r\n    /**\r\n     * 获取缩放类型\r\n     * @returns {number} 缩放类型\r\n     */\r\n    getScaleType() {\r\n        return this.scaleType;\r\n    }\r\n\r\n    /**\r\n     * 初始化属性\r\n     * @param {string} attr 属性字符串\r\n     */\r\n    initAttr(attr) {\r\n        this.attrCom.init(this, attr || \"\");\r\n    }\r\n\r\n    /**\r\n     * 检查是否具有指定属性\r\n     * @param {string} attr 属性名称\r\n     * @returns {boolean} 是否具有该属性\r\n     */\r\n    hasAttribution(attr) {\r\n        return this.attrCom && this.attrCom.hasAttribution(attr);\r\n    }\r\n\r\n    /**\r\n     * 设置经验值\r\n     * @param {number} exp 经验值\r\n     */\r\n    setExp(exp) {\r\n        this.exp = exp;\r\n    }\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    initComps() {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this.collideAble = true;\r\n    }\r\n\r\n    /**\r\n     * 添加掉落物\r\n     * @param {Object} loot 掉落物\r\n     */\r\n    addLoot(loot) {\r\n        this._lootArr.push(loot);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        if (this.isDead || this.checkStandby(deltaTime)) {\r\n            return;\r\n        }\r\n\r\n        // 更新技能抗性\r\n        this.updateSkillResist(deltaTime);\r\n\r\n        // 更新属性组件\r\n        if (this.attrCom) {\r\n            this.attrCom.updateGameLogic(deltaTime);\r\n        }\r\n\r\n        this._isTracked = false;\r\n\r\n        // 更新所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 检查待机状态\r\n     * @param {number} deltaTime 帧间隔时间\r\n     * @returns {boolean} 是否处于待机状态\r\n     */\r\n    checkStandby(deltaTime) {\r\n        this._countTime += deltaTime;\r\n        if (this._bStandBy) {\r\n            if (this._countTime > this._standByTime) {\r\n                this.active = true;\r\n                this._bStandBy = false;\r\n                this._countTime = 0;\r\n                this._standByEnd = true;\r\n                this.node.getComponent(UIOpacity).opacity = 255;\r\n                this.startBattle();\r\n            }\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 更新技能抗性\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateSkillResist(deltaTime) {\r\n        this.hurtBuffMap.forEach((value, key) => {\r\n            const time = this._hurtBuffTime.get(key);\r\n            if (time !== null && time > 0) {\r\n                this._hurtBuffTime.set(key, time - deltaTime);\r\n            } else {\r\n                this.removeBuff(key);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化属性倍率\r\n     * @param {Array<number>} rates 属性倍率数组\r\n     */\r\n    initPropertyRate(rates) {\r\n        this.propertyRate = rates;\r\n        if (this.propertyRate.length > 2) {\r\n            this.curHp *= this.propertyRate[0];\r\n            this.maxHp = this.curHp;\r\n            this.attack *= this.propertyRate[1];\r\n            this.collideAtk *= this.propertyRate[2];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取 UI 数据\r\n     * @returns {Object} UI 数据\r\n     */\r\n    getUIData() {\r\n        return this.uiData;\r\n    }\r\n    /**\r\n * 设置待机时间\r\n * @param {number} time 待机时间\r\n */\r\n    setStandByTime(time) {\r\n        this._bStandBy = true;\r\n        this._standByTime = time;\r\n        this.node.getComponent(UIOpacity).opacity = 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否处于待机状态\r\n     * @returns {boolean} 是否待机\r\n     */\r\n    isStandBy() {\r\n        return this._bStandBy;\r\n    }\r\n\r\n    /**\r\n     * 设置敌人位置\r\n     * @param {number} x X 坐标\r\n     * @param {number} y Y 坐标\r\n     * @param {boolean} isTracked 是否被追踪\r\n     */\r\n    setPos(x, y, isTracked = false) {\r\n        this.node.setPosition(x, y);\r\n        this._isTracked = isTracked;\r\n    }\r\n\r\n    /**\r\n     * 获取是否被追踪\r\n     * @returns {boolean} 是否被追踪\r\n     */\r\n    get isTracked() {\r\n        return this._isTracked;\r\n    }\r\n\r\n    /**\r\n     * 获取方向\r\n     * @returns {Vec2} 方向向量\r\n     */\r\n    getDir() {\r\n        return Vec2.ZERO;\r\n    }\r\n\r\n    /**\r\n     * 获取角度\r\n     * @returns {number} 角度\r\n     */\r\n    getAngle() {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否满血\r\n     * @returns {boolean} 是否满血\r\n     */\r\n    isFullBlood() {\r\n        return this.curHp >= this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 获取最大血量\r\n     * @returns {number} 最大血量\r\n     */\r\n    getMaxHp() {\r\n        return this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 获取当前血量\r\n     * @returns {number} 当前血量\r\n     */\r\n    get curHp() {\r\n        return this._curHp;\r\n    }\r\n\r\n    /**\r\n     * 设置当前血量\r\n     * @param {number} hp 当前血量\r\n     */\r\n    set curHp(hp) {\r\n        this._curHp = hp;\r\n    }\r\n\r\n    /**\r\n     * 获取血量百分比\r\n     * @returns {number} 血量百分比\r\n     */\r\n    getHpPercent() {\r\n        return this.curHp / this.maxHp;\r\n    }\r\n\r\n    /**\r\n     * 改变血量\r\n     * @param {number} delta 血量变化值\r\n     */\r\n    changeHp(delta) {\r\n        this.curHp += delta;\r\n        if (this.curHp < 0) {\r\n            this.curHp = 0;\r\n        } else if (this.curHp > this.maxHp) {\r\n            this.curHp = this.maxHp;\r\n        }\r\n        this.checkHp();\r\n    }\r\n\r\n    /**\r\n     * 检查血量是否为 0\r\n     * @returns {boolean} 是否死亡\r\n     */\r\n    checkHp() {\r\n        if (this.curHp <= 0) {\r\n            this.die(GameEnum.EnemyDestroyType.Die);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 设置当前血量\r\n     * @param {number} hp 当前血量\r\n     */\r\n    setCurHp(hp) {\r\n        this.curHp = hp;\r\n    }\r\n\r\n    /**\r\n     * 获取当前血量\r\n     * @returns {number} 当前血量\r\n     */\r\n    getCurHp() {\r\n        return this.curHp;\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否可以被伤害\r\n     * @returns {boolean} 是否可以被伤害\r\n     */\r\n    isDamageable() {\r\n        if (this.sceneLayer < 0 && !this._bStandBy) {\r\n            if (this.damaged) {\r\n                return true;\r\n            }\r\n            let position = this.node.position;\r\n            if (this.itemParent !== this) {\r\n                position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);\r\n            }\r\n            return (\r\n                position.y < 0 &&\r\n                position.x > -GameConst.ViewCenter.x &&\r\n                position.x < GameConst.ViewCenter.x\r\n            );\r\n        }\r\n        return !this._bStandBy;\r\n    }\r\n\r\n    /**\r\n     * 设置敌人是否已被伤害\r\n     * @param {boolean} value 是否已被伤害\r\n     */\r\n    setDamaged(value) {\r\n        this.damaged = value;\r\n    }\r\n    /**\r\n     * 获取技能抗性\r\n     * @param {number} skillType 技能类型\r\n     * @returns {number} 技能抗性\r\n     */\r\n    getSkillResist(skillType) {\r\n        return this.resist[skillType] || 1;\r\n    }\r\n\r\n    /**\r\n     * 添加 Buff\r\n     * @param {number} buffType Buff 类型\r\n     * @param {Array<number>} params Buff 参数\r\n     * @param {boolean} isCritical 是否暴击\r\n     */\r\n    addBuff(buffType, params, isCritical) {\r\n        this.hurtBuffMap.set(buffType, true);\r\n        switch (buffType) {\r\n            case GameEnum.EnemyBuff.Ice:\r\n                this._hurtBuffTime.set(buffType, params[0]);\r\n                break;\r\n            case GameEnum.EnemyBuff.Fire:\r\n                this._isFireCirt = isCritical;\r\n                this._hurtBuffTime.set(buffType, params[0]);\r\n                this._fireDemage = params[1] === 0 ? this._fireDemage : params[1];\r\n                this._fireHurtCd = params[2];\r\n                break;\r\n            case GameEnum.EnemyBuff.Treat:\r\n                const count = this._buffCountArr.get(buffType) || 0;\r\n                this._buffCountArr.set(buffType, count + 1);\r\n                break;\r\n        }\r\n        this.onAddBuff(buffType);\r\n    }\r\n\r\n    /**\r\n     * 移除 Buff\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    removeBuff(buffType) {\r\n        this.hurtBuffMap.delete(buffType);\r\n        this._hurtBuffTime.delete(buffType);\r\n        let shouldRemove = true;\r\n        if (buffType === GameEnum.EnemyBuff.Treat) {\r\n            const count = this._buffCountArr.get(buffType);\r\n            if (count > 0) {\r\n                this._buffCountArr.set(buffType, count - 1);\r\n                if (count - 1 > 0) {\r\n                    shouldRemove = false;\r\n                }\r\n            }\r\n        }\r\n        if (shouldRemove) {\r\n            this.onRemoveBuff(buffType);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除所有 Buff 效果\r\n     */\r\n    removeAllBuffEffect() {\r\n        this._buffCountArr.clear();\r\n        if (this.effectComp) {\r\n            this.effectComp.removeAllBuffEffect();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 当 Buff 被移除时的回调\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onRemoveBuff(buffType) {\r\n        if (this.effectComp) {\r\n            this.effectComp.removeBuff(buffType);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 当 Buff 被添加时的回调\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onAddBuff(buffType) {\r\n        if (this.effectComp && this.uiData) {\r\n            const buffData = this.uiData.skillResistUIDict[buffType];\r\n            switch (buffType) {\r\n                case GameEnum.EnemyBuff.Ice:\r\n                    if (buffData && buffData.length > 0) {\r\n                        this.effectComp.addBuff(buffType, buffData);\r\n                    }\r\n                    break;\r\n                case GameEnum.EnemyBuff.Fire:\r\n                    const fireDamage = 0.4 * this.uiData.hpParam[3];\r\n                    if (!buffData && fireDamage) {\r\n                        buffData[0][0] = fireDamage;\r\n                    }\r\n                    this.effectComp.addBuff(buffType, buffData);\r\n                    break;\r\n                case GameEnum.EnemyBuff.Treat:\r\n                    this.effectComp.addBuff(buffType, buffData);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否具有指定的伤害 Buff\r\n     * @param {number} buffType Buff 类型\r\n     * @returns {boolean} 是否具有该 Buff\r\n     */\r\n    hasHurtBuff(buffType) {\r\n        return this.hurtBuffMap.get(buffType);\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞数据\r\n     * @param {Array<number>} collider 碰撞数据\r\n     * @param {number} scale 缩放比例\r\n     * @param {Vec2} offset 偏移量\r\n     */\r\n    setCollideData(collider, entity = null, offset = v2(0, 0)) {\r\n        if (this.collideComp) {\r\n            this.collideComp.init(entity || this,size(collider[3],collider[4]), offset);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞实体\r\n     * @param {Entity} entity 碰撞实体\r\n     */\r\n    setCollideEntity(entity) {\r\n        if (this.collideComp) {\r\n            this.collideComp.entity = entity;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞偏移\r\n     * @param {Vec2} offset 偏移量\r\n     */\r\n    setCollideOffset(offset) {\r\n        if (this.collideComp) {\r\n            this.collideComp.offset = offset;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞缩放\r\n     * @param {number} widthScale 宽度缩放\r\n     * @param {number} heightScale 高度缩放\r\n     */\r\n    setCollideScale(widthScale, heightScale) {\r\n        if (this.collideComp && this.uiData) {\r\n            this.collideComp.size = size(\r\n                this.uiData.collider[3] * widthScale,\r\n                this.uiData.collider[4] * heightScale\r\n            );\r\n        }\r\n    }\r\n\r\n    onCollide(collider) {\r\n        if (this.active && !this.isDead) {\r\n            if (collider.entity instanceof Bullet) {\r\n                const attack = collider.entity.getAttack(this);\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(),attack);\r\n                const finalDamage = Math.max(attack / 10, attack - this.defence);\r\n                if (this.hurt(finalDamage)) {\r\n                }\r\n            } else if (collider.entity instanceof MainPlane && this.collideLevel === GameEnum.EnemyCollideLevel.Main && this.bCollideDead) {\r\n                this.die(GameEnum.EnemyDestroyType.Die);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 处理敌人受到的伤害\r\n     * @param {number} damage 伤害值\r\n     * @returns {boolean} 是否成功处理伤害\r\n     */\r\n    hurt(damage) {\r\n        if (!this.active || this.isDead || !this.isDamageable()) {\r\n            return false;\r\n        }\r\n        this.changeHp(-damage);\r\n        this._checkHurtLoot(damage);\r\n        this.onHurt();\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 检查是否需要生成掉落物\r\n     * @param {number} damage 伤害值\r\n     */\r\n    _checkHurtLoot(damage) {\r\n        if (this._curLoot) {\r\n            this.checkLoot(damage);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查并生成掉落物\r\n     * @param {number} damage 伤害值\r\n     * @param {Vec2} position 掉落物生成位置\r\n     */\r\n    checkLoot(damage = 0, position = Vec2.ZERO) {\r\n        // if (this.isDead && this.scaleType !== GameEnum.EnemyScale.None) {\r\n        //     const lootType = GameIns.lootManager.checkLoot(this.scaleType);\r\n        //     GameIns.lootManager.addProp(\r\n        //         this.node.convertToWorldSpaceAR(position),\r\n        //         this.uiData.lootParam1,\r\n        //         lootType\r\n        //     );\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 当敌人受到伤害时的回调\r\n     */\r\n    onHurt() { }\r\n\r\n    /**\r\n     * 处理敌人死亡逻辑\r\n     * @param {number} destroyType 敌人销毁类型\r\n     */\r\n    die(destroyType) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.isDead = true;\r\n        this.collideAble = false;\r\n\r\n        if (this.attrCom) {\r\n            this.attrCom.die();\r\n        }\r\n\r\n        this.onDie(destroyType);\r\n    }\r\n\r\n    /**\r\n     * 敌人死亡时的回调\r\n     * @param {number} destroyType 敌人销毁类型\r\n     */\r\n    onDie(destroyType) {\r\n        if (destroyType === GameEnum.EnemyDestroyType.Die) {\r\n\r\n            if (this.dieBullet) {\r\n                for (const bullet of this.bullets) {\r\n                    bullet.dieRemove();\r\n                }\r\n            }\r\n\r\n            if (!this.isItem) {\r\n                // TaskManager.TaskMgr.taskNumberChange(TaskManager.TaskType.KillPlane, 1);\r\n                // TaskManager.TaskMgr.achievementNumberChange(TaskManager.AchievementType.KillEnemy, 1);\r\n                // GameData.GData.killEnemyNumber += 1;\r\n            }\r\n        }\r\n\r\n        this.bullets = [];\r\n\r\n        switch (this.type) {\r\n            case GameEnum.EnemyType.Ligature:\r\n            case GameEnum.EnemyType.LigatureLine:\r\n                break;\r\n            default:\r\n                let position = this.node.position;\r\n                if (this.sceneLayer < 0) {\r\n                    if (\r\n                        this.type === GameEnum.EnemyType.Turret ||\r\n                        this.type === GameEnum.EnemyType.GoldBox ||\r\n                        this.type === GameEnum.EnemyType.LigatureUnit\r\n                    ) {\r\n                        position = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                        position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(position);\r\n                    }\r\n                } else {\r\n                    const worldPosition = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);\r\n                    position = BattleLayer.me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(worldPosition);\r\n                }\r\n                GameIns.enemyManager.checkEnemyDieBomb(position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌人时的回调\r\n     */\r\n    willRemove() { }\r\n\r\n    /**\r\n     * 显示属性护盾\r\n     */\r\n    showAttrShield() {\r\n        if (this.attrCom) {\r\n            this.attrCom.showAttrShield();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放敌人死亡动画\r\n     */\r\n    playDieAnim() {\r\n        if (!this.uiData) {\r\n            return;\r\n        }\r\n\r\n        if (this.uiData.blastSound > 0) {\r\n            // GameIns.audioManager.playEffect(`blast${this.uiData.blastSound}`);\r\n        }\r\n\r\n        this.scheduleOnce(() => {\r\n            this.removeAllBuffEffect();\r\n            this.onDieAnimEnd();\r\n        }, 0.1);\r\n    }\r\n\r\n    /**\r\n     * 敌人死亡动画结束时的回调\r\n     */\r\n    onDieAnimEnd() { }\r\n\r\n    /**\r\n     * 添加子弹到敌人\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    addBullet(bullet) {\r\n        if (this.dieBullet && this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet) {\r\n        if (this.dieBullet && this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n}"]}