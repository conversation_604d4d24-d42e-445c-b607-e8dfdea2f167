{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EventHandler", "ccclass", "property", "executeInEditMode", "menu", "help", "inspector", "ButtonPlus", "tooltip", "type", "multiline", "formerlySerializedAs", "continuous", "_continuousTimer", "longPressFlag", "longPressTimer", "onEnable", "clickDefZoomScale", "transition", "zoomScale", "duration", "onDisable", "clearTimeout", "_onTouchBegan", "event", "interactable", "enabledInHierarchy", "openLongPress", "setTimeout", "node", "emit", "bind", "longPressTime", "propagationStopped", "_onTouchEnded", "openContinuous", "emitEvents", "clickEvents", "continuousTime", "_onTouchCancel", "addClick", "callback", "target", "off", "on", "addLongClick", "startFunc", "endFunc"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Y,OAAAA,Y;;;;;;;;;OAEvB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,IAAxC;AAA8CC,QAAAA,IAA9C;AAAoDC,QAAAA;AAApD,O,GAAkER,U;;4BAM3DS,U,WAERL,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAERN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE,MAAX;AAAmBC,QAAAA,IAAI,EAAE,EAAzB;AAA6BC,QAAAA,SAAS,EAAE,IAAxC;AAA8CC,QAAAA,oBAAoB,EAAE;AAApE,OAAD,C,UAERT,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAERN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAWRN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,EA3BZP,O,UAEAE,iB,qBAFD,MAKaI,UALb,SAKgCR,MALhC,CAKuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAYnC;AAZmC,eAanCa,UAbmC,GAab,KAba;AAcnC;AAdmC,eAenCC,gBAfmC,GAehB,IAfgB;;AAkBnC;AAlBmC;;AAqBnC;AArBmC;;AAAA,eAwBnCC,aAxBmC,GAwBnB,KAxBmB;AAAA,eA0B3BC,cA1B2B,GA0BV,IA1BU;AAAA;;AA4BnCC,QAAAA,QAAQ,GAAG;AACP,eAAKJ,UAAL,GAAkB,KAAlB;AACA,gBAAMI,QAAN,GAFO,CAGP;AACA;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB,iBAAKC,UAAL,GAAkB,CAAlB;AACA,iBAAKC,SAAL,GAAiB,IAAjB;AACA,iBAAKC,QAAL,GAAgB,GAAhB;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKR,gBAAT,EAA2B;AACvBS,YAAAA,YAAY,CAAC,KAAKT,gBAAN,CAAZ;AACA,iBAAKA,gBAAL,GAAwB,IAAxB;AACH;;AACD,cAAI,KAAKE,cAAT,EAAyB;AACrBO,YAAAA,YAAY,CAAC,KAAKP,cAAN,CAAZ;AACA,iBAAKA,cAAL,GAAsB,IAAtB;AACH;;AACD,gBAAMM,SAAN;AACH;AAED;;;AACUE,QAAAA,aAAa,CAACC,KAAD,EAAoB;AACvC,cAAI,CAAC,KAAKC,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AAEpD,cAAI,KAAKC,aAAL,IAAsB,CAAC,KAAKb,aAAhC,EAA+C;AAAK;AAChD,gBAAI,KAAKC,cAAT,EAAyBO,YAAY,CAAC,KAAKP,cAAN,CAAZ;AACzB,iBAAKA,cAAL,GAAsBa,UAAU,CAAC,YAAY;AACzC;AACA,kBAAI,KAAK,UAAL,CAAJ,EAAsB;AAClB,qBAAKC,IAAL,CAAUC,IAAV,CAAe,gBAAf,EAAiC,IAAjC;AACA,qBAAKhB,aAAL,GAAqB,IAArB;AACH;AACJ,aANgC,CAM/BiB,IAN+B,CAM1B,IAN0B,CAAD,EAMlB,KAAKC,aAAL,GAAqB,IANH,CAAhC;AAOH;;AAED,eAAK,UAAL,IAAmB,IAAnB;AACA,eAAK,cAAL,IAfuC,CAgBvC;;AACAR,UAAAA,KAAK,CAACS,kBAAN,GAA2B,IAA3B;AACH;;AACSC,QAAAA,aAAa,CAACV,KAAD,EAAoB;AACvC,cAAI,CAAC,KAAKC,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AACpD,cAAI,KAAK,UAAL,KAAoB,KAAKZ,aAA7B,EAA4C;AACxC,iBAAKe,IAAL,CAAUC,IAAV,CAAe,cAAf,EAA+B,IAA/B;AACA,iBAAKhB,aAAL,GAAqB,KAArB;AACH,WAHD,MAGO,IAAI,KAAK,UAAL,KAAoB,CAAC,KAAKF,UAA9B,EAA0C;AAC7C,iBAAKA,UAAL,GAAkB,KAAKuB,cAAL,GAAsB,IAAtB,GAA6B,KAA/C;AACAnC,YAAAA,YAAY,CAACoC,UAAb,CAAwB,KAAKC,WAA7B,EAA0Cb,KAA1C;AACA,iBAAKK,IAAL,CAAUC,IAAV,CAAe,OAAf,EAAwBN,KAAxB,EAH6C,CAI7C;;AACA,gBAAI,KAAKW,cAAT,EAAyB;AACrB,mBAAKtB,gBAAL,GAAwBe,UAAU,CAAC,YAAY;AAC3C,qBAAKhB,UAAL,GAAkB,KAAlB;AACH,eAFkC,CAEjCmB,IAFiC,CAE5B,IAF4B,CAAD,EAEpB,KAAKO,cAAL,GAAsB,IAFF,CAAlC;AAGH;AACJ;;AACD,eAAK,UAAL,IAAmB,KAAnB;AACA,eAAK,cAAL;AACAd,UAAAA,KAAK,CAACS,kBAAN,GAA2B,IAA3B,CAlBuC,CAmBvC;AACH;;AACSM,QAAAA,cAAc,GAAG;AACvB,cAAI,CAAC,KAAKd,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AACpD,cAAI,KAAK,UAAL,KAAoB,KAAKZ,aAA7B,EAA4C;AACxC,iBAAKe,IAAL,CAAUC,IAAV,CAAe,cAAf,EAA+B,IAA/B;AACA,iBAAKhB,aAAL,GAAqB,KAArB;AACH;;AACD,eAAK,UAAL,IAAmB,KAAnB;AACA,eAAK,cAAL;AACH;AACD;;;AACA0B,QAAAA,QAAQ,CAACC,QAAD,EAAqBC,MAArB,EAAqC;AACzC,eAAKb,IAAL,CAAUc,GAAV,CAAc,OAAd;AACA,eAAKd,IAAL,CAAUe,EAAV,CAAa,OAAb,EAAsBH,QAAtB,EAAgCC,MAAhC;AACH;AACD;;;AACAG,QAAAA,YAAY,CAACC,SAAD,EAAsBC,OAAtB,EAAyCL,MAAzC,EAAyD;AACjE,eAAKb,IAAL,CAAUc,GAAV,CAAc,gBAAd;AACA,eAAKd,IAAL,CAAUc,GAAV,CAAc,cAAd;AACA,eAAKd,IAAL,CAAUe,EAAV,CAAa,gBAAb,EAA+BE,SAA/B,EAA0CJ,MAA1C;AACA,eAAKb,IAAL,CAAUe,EAAV,CAAa,cAAb,EAA6BG,OAA7B,EAAsCL,MAAtC;AACH;;AAhHkC,O;;;;;iBAGf,I;;;;;;;iBAET,E;;;;;;;iBAEM,I;;;;;;;iBAEA,G;;;;;;;iBAWD,K;;;;;;;iBAGA,C", "sourcesContent": ["import { _decorator, <PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, EventTouch } from 'cc';\n\nconst { ccclass, property, executeInEditMode, menu, help, inspector } = _decorator;\n@ccclass\n//@menu('i18n:MAIN_MENU.component.ui/ButtonPlus')\n@executeInEditMode\n//@help('i18n:COMPONENT.help_url.button')\n//@inspector('packages://buttonplus/inspector.js')\nexport class ButtonPlus extends Button {\n\n    @property({ tooltip: \"点击放大默认倍数\" })\n    clickDefZoomScale = true;\n    @property({ tooltip: \"音效路径\", type: '', multiline: true, formerlySerializedAs: '_N$string' })\n    audioUrl = '';\n    @property({ tooltip: \"屏蔽连续点击\" })\n    openContinuous = true;\n    @property({ tooltip: \"屏蔽时间, 单位:秒\" })\n    continuousTime = 0.5;\n\n\n    // false表示可以点击\n    continuous: boolean = false;\n    // 定时器\n    _continuousTimer = null;\n\n\n    // 长按触发\n    @property({ tooltip: \"是否开启长按事件\" })\n    openLongPress = false;\n    // 触发时间\n    @property({ tooltip: \"长按时间\" })\n    longPressTime = 1;\n    longPressFlag = false;\n\n    private longPressTimer = null;\n\n    onEnable() {\n        this.continuous = false;\n        super.onEnable();\n        // if (!CC_EDITOR) {\n        // }\n        if (this.clickDefZoomScale) {\n            this.transition = 3\n            this.zoomScale = 1.05\n            this.duration = 0.1\n        }\n    }\n    onDisable() {\n        if (this._continuousTimer) {\n            clearTimeout(this._continuousTimer);\n            this._continuousTimer = null;\n        }\n        if (this.longPressTimer) {\n            clearTimeout(this.longPressTimer);\n            this.longPressTimer = null;\n        }\n        super.onDisable();\n    }\n\n    /** 重写 */\n    protected _onTouchBegan(event: EventTouch) {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n\n        if (this.openLongPress && !this.longPressFlag) {    // 开启长按\n            if (this.longPressTimer) clearTimeout(this.longPressTimer);\n            this.longPressTimer = setTimeout(function () {\n                // 还在触摸中 触发事件\n                if (this[\"_pressed\"]) {\n                    this.node.emit('longclickStart', this);\n                    this.longPressFlag = true;\n                }\n            }.bind(this), this.longPressTime * 1000);\n        }\n\n        this[\"_pressed\"] = true;\n        this[\"_updateState\"]();\n        //event.stopPropagation();\n        event.propagationStopped = true\n    }\n    protected _onTouchEnded(event: EventTouch) {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n        if (this[\"_pressed\"] && this.longPressFlag) {\n            this.node.emit('longclickEnd', this);\n            this.longPressFlag = false;\n        } else if (this[\"_pressed\"] && !this.continuous) {\n            this.continuous = this.openContinuous ? true : false;\n            EventHandler.emitEvents(this.clickEvents, event);\n            this.node.emit('click', event);\n            //SoundMgr.inst.playEffect(this.audioUrl)\n            if (this.openContinuous) {\n                this._continuousTimer = setTimeout(function () {\n                    this.continuous = false;\n                }.bind(this), this.continuousTime * 1000);\n            }\n        }\n        this[\"_pressed\"] = false;\n        this[\"_updateState\"]();\n        event.propagationStopped = true\n        //event.stopPropagation();\n    }\n    protected _onTouchCancel() {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n        if (this[\"_pressed\"] && this.longPressFlag) {\n            this.node.emit('longclickEnd', this);\n            this.longPressFlag = false;\n        }\n        this[\"_pressed\"] = false;\n        this[\"_updateState\"]();\n    }\n    /** 添加点击事件 */\n    addClick(callback: Function, target: Object) {\n        this.node.off('click');\n        this.node.on('click', callback, target);\n    }\n    /** 添加一个长按事件 */\n    addLongClick(startFunc: Function, endFunc: Function, target: Object) {\n        this.node.off('longclickStart');\n        this.node.off('longclickEnd');\n        this.node.on('longclickStart', startFunc, target);\n        this.node.on('longclickEnd', endFunc, target);\n    }\n}"]}