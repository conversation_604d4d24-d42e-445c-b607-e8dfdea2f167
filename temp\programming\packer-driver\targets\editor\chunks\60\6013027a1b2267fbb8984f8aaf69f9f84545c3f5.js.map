{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts"], "names": ["_decorator", "Component", "ccclass", "property", "EnemyEffectComp", "_buffNodePool", "_buffAnimNodePool", "_buffEffectMap", "Map", "_buffFrameAnimPool", "init", "removeAllBuffEffect", "removeBuff", "buffType", "addBuff", "params"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OAGf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,e,WADpBF,OAAO,CAAC,iBAAD,C,gBAAR,MACqBE,eADrB,SAC6CH,SAD7C,CACuD;AAAA;AAAA;AAAA,eAEnDI,aAFmD,GAEnC,EAFmC;AAE/B;AAF+B,eAGnDC,iBAHmD,GAG/B,EAH+B;AAG3B;AAH2B,eAInDC,cAJmD,GAIlC,IAAIC,GAAJ,EAJkC;AAIvB;AAJuB,eAKnDC,kBALmD,GAK9B,EAL8B;AAAA;;AAK1B;;AAGzB;AACJ;AACA;AACIC,QAAAA,IAAI,GAAG;AACH,eAAKC,mBAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,mBAAmB,GAAG,CAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,UAAU,CAACC,QAAD,EAAW,CACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACD,QAAD,EAAWE,MAAX,EAAmB,CACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAvKkD,O", "sourcesContent": ["import { _decorator, Component, instantiate, tween, v2 } from \"cc\";\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"EnemyEffectComp\")\r\nexport default class EnemyEffectComp extends Component {\r\n\r\n    _buffNodePool = []; // 冰冻效果节点池\r\n    _buffAnimNodePool = []; // 火焰效果节点池\r\n    _buffEffectMap = new Map(); // Buff 效果映射\r\n    _buffFrameAnimPool = []; // 治疗效果节点池\r\n\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    init() {\r\n        this.removeAllBuffEffect();\r\n    }\r\n\r\n    /**\r\n     * 移除所有 Buff 效果\r\n     */\r\n    removeAllBuffEffect() {\r\n        // this._buffEffectMap.forEach((effects, buffType) => {\r\n        //     if (effects) {\r\n        //         for (const effect of effects) {\r\n        //             switch (buffType) {\r\n        //                 case GameEnum.EnemyBuff.Ice:\r\n        //                     effect.active = false;\r\n        //                     this._buffNodePool.push(effect);\r\n        //                     break;\r\n        //                 case GameEnum.EnemyBuff.Fire:\r\n        //                     const fireComp = effect.getComponent(ImageSequence);\r\n        //                     if (fireComp) fireComp.stop();\r\n        //                     effect.active = false;\r\n        //                     this._buffAnimNodePool.push(effect);\r\n        //                     break;\r\n        //                 case GameEnum.EnemyBuff.Treat:\r\n        //                     const treatComp = effect.getComponent(PfFrameAnim);\r\n        //                     if (treatComp) treatComp.stop();\r\n        //                     effect.active = false;\r\n        //                     this._buffFrameAnimPool.push(effect);\r\n        //                     break;\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // });\r\n        // this._buffEffectMap.clear();\r\n    }\r\n\r\n    /**\r\n     * 移除指定 Buff 效果\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    removeBuff(buffType) {\r\n        // const effects = this._buffEffectMap.get(buffType);\r\n        // if (effects) {\r\n        //     for (const effect of effects) {\r\n        //         switch (buffType) {\r\n        //             case GameEnum.EnemyBuff.Fire:\r\n        //                 const fireComp = effect.getComponent(ImageSequence);\r\n        //                 if (fireComp) fireComp.stop();\r\n        //                 effect.active = false;\r\n        //                 this._buffAnimNodePool.push(effect);\r\n        //                 break;\r\n        //             case GameEnum.EnemyBuff.Ice:\r\n        //                 HurtEffectManager.me.playAHurtEffect('icebreak', this.node);\r\n        //                 tween(effect)\r\n        //                     .to(5 / 30, { opacity: 0 })\r\n        //                     .call(() => {\r\n        //                         effect.active = false;\r\n        //                         this._buffNodePool.push(effect);\r\n        //                     })\r\n        //                     .start();\r\n        //                 break;\r\n        //             case GameEnum.EnemyBuff.Treat:\r\n        //                 const treatComp = effect.getComponent(PfFrameAnim);\r\n        //                 if (treatComp) treatComp.stop();\r\n        //                 effect.active = false;\r\n        //                 this._buffFrameAnimPool.push(effect);\r\n        //                 break;\r\n        //         }\r\n        //     }\r\n        //     this._buffEffectMap.delete(buffType);\r\n        //     if (buffType === GameEnum.EnemyBuff.Ice) {\r\n        //         // GameIns.audioManager.playEffect('b_ice_over');\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 添加 Buff 效果\r\n     * @param {number} buffType Buff 类型\r\n     * @param {Array} params Buff 参数\r\n     */\r\n    addBuff(buffType, params) {\r\n        // if (!this._buffEffectMap.get(buffType)) {\r\n        //     try {\r\n        //         switch (buffType) {\r\n        //             case GameEnum.EnemyBuff.Ice:\r\n        //                 GameIns.audioManager.playEffect('b_ice_atk');\r\n        //                 const iceEffects = [];\r\n        //                 for (const param of params) {\r\n        //                     let iceNode = this._buffNodePool.pop();\r\n        //                     if (!iceNode) {\r\n        //                         iceNode = new Node();\r\n        //                         this.node.addChild(iceNode);\r\n        //                         iceNode.addComponent(Sprite);\r\n        //                         iceNode.zIndex = 10;\r\n        //                     }\r\n        //                     iceNode.active = true;\r\n        //                     iceNode.opacity = 255;\r\n        //                     const sprite = iceNode.getComponent(Sprite);\r\n        //                     EnemyManager.EnemyMgr.setPlaneFrame(sprite, `ice_${param[0]}`);\r\n        //                     iceNode.position = v2(param[1], param[2]);\r\n        //                     iceNode.angle = param[3];\r\n        //                     iceNode.scale = param[4];\r\n        //                     iceEffects.push(iceNode);\r\n        //                 }\r\n        //                 this._buffEffectMap.set(buffType, iceEffects);\r\n        //                 break;\r\n\r\n        //             case GameEnum.EnemyBuff.Fire:\r\n        //                 const fireEffects = [];\r\n        //                 const scale = params ? params[0][0] : 0.5;\r\n        //                 let fireNode = this._buffAnimNodePool.pop();\r\n        //                 if (!fireNode) {\r\n        //                     fireNode = instantiate(GameConst.hurtEffect);\r\n        //                     const fireComp = fireNode.getComponent(ImageSequence);\r\n        //                     const frames = [];\r\n        //                     for (let i = 0; i <= 8; i++) {\r\n        //                         frames[i] = GameIns.loadManager.getImage(\r\n        //                             `burn_${i}`,\r\n        //                             EnemyManager.EnemyMgr.enemyAtlas.name.split('.')[0]\r\n        //                         );\r\n        //                     }\r\n        //                     fireComp.setData(frames);\r\n        //                 }\r\n        //                 fireNode.parent = this.node;\r\n        //                 fireNode.position = v2(0, 0);\r\n        //                 fireNode.scale = scale;\r\n        //                 fireNode.active = true;\r\n        //                 fireNode.getComponent(ImageSequence).play(0);\r\n        //                 fireEffects.push(fireNode);\r\n        //                 this._buffEffectMap.set(buffType, fireEffects);\r\n        //                 break;\r\n\r\n        //             case GameEnum.EnemyBuff.Treat:\r\n        //                 const treatEffects = [];\r\n        //                 let treatNode = this._buffFrameAnimPool.pop();\r\n        //                 if (!treatNode) {\r\n        //                     treatNode = instantiate(GameConst.GConst.frameAnim);\r\n        //                     this.node.addChild(treatNode, 1);\r\n        //                     treatNode.getComponent(PfFrameAnim).init(\r\n        //                         EnemyManager.EnemyMgr.enemyAtlas,\r\n        //                         'a_doctor_',\r\n        //                         15,\r\n        //                         GameConst.ActionFrameTime\r\n        //                     );\r\n        //                 }\r\n        //                 treatNode.active = true;\r\n        //                 treatNode.position = v2(0, 0);\r\n        //                 treatNode.getComponent(PfFrameAnim).reset();\r\n        //                 treatEffects.push(treatNode);\r\n        //                 this._buffEffectMap.set(buffType, treatEffects);\r\n        //                 break;\r\n        //         }\r\n        //     } catch (error) {\r\n        //         Tools.log('enemy effect error:', buffType, error);\r\n        //     }\r\n        // }\r\n    }\r\n}"]}