import { _decorator, Component, Label, Node } from 'cc';
import { DataEvent } from 'db://assets/scripts/Data/DataEvent';
import { DataMgr } from 'db://assets/scripts/Data/DataManager';
import { MyApp } from 'db://assets/scripts/MyApp';
import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { logDebug, logError } from 'db://assets/scripts/Utils/Logger';
import { EventMgr } from 'db://assets/scripts/event/EventManager';
import { ButtonPlus } from '../../../../common/components/button/ButtonPlus';
import { DropDown } from '../../../../common/components/dropdown/DropDown';
import { PlaneUIEvent } from '../../PlaneEvent';
import { BagSortType, TabStatus } from '../../PlaneTypes';
const { ccclass, property } = _decorator;

@ccclass('SortTypeDropdown')
export class SortTypeDropdown extends Component {
    @property(DropDown)
    dropDown: DropDown = null;

    private _tabStatus: TabStatus = TabStatus.None;
    private _sortType: BagSortType = BagSortType.None;
    private _bagTabOptions: { key: BagSortType, label: string }[] = [
        { key: BagSortType.Quality, label: '按品质排序' },
        { key: BagSortType.Part, label: '按部位排序' },
    ];
    private _mergeTabOptions: { key: BagSortType, label: string }[] = [
        { key: BagSortType.Merge, label: '按合成排序' },
    ];

    onLoad() {
        this.getComponent(ButtonPlus).addClick(this.onDropDownOptionClick, this);
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChangeEvent, this);
        EventMgr.on(PlaneUIEvent.UpdateBagGrids, this.sortBag, this)
        EventMgr.on(DataEvent.ItemsRefresh, this.sortBag, this)
    }

    onTabChangeEvent(tabStatus: TabStatus) {
        if (tabStatus === this._tabStatus) {
            return;
        }
        this._tabStatus = tabStatus;
        let optionKeyList: string[]
        if (tabStatus === TabStatus.Bag) {
            optionKeyList = this._bagTabOptions.map(v => v.key)
        } else {
            optionKeyList = this._mergeTabOptions.map(v => v.key)
        }
        this.dropDown.init(optionKeyList, this.onOptionRender.bind(this), this.onDropDownOptionClick.bind(this))
        this.sortBag();
    }

    onOptionRender(optNode: Node, optKey: string) {
        const label = optNode.getComponentInChildren(Label);
        let opt: { label: string, key: BagSortType };
        switch (this._tabStatus) {
            case TabStatus.Bag:
                opt = this._bagTabOptions.find(v => v.key == optKey)
                label.string = opt.label;
                break;
            case TabStatus.Merge:
                opt = this._mergeTabOptions.find(v => v.key == optKey)
                label.string = opt.label;
                break;
            default:
                logError("PlaneUI", `onOptionRender error ${this._tabStatus}`)
                break;
        }
    }

    onDropDownOptionClick(optKey: string) {
        let opt: { label: string, key: BagSortType };
        switch (this._tabStatus) {
            case TabStatus.Bag:
                opt = this._bagTabOptions.find(v => v.key == optKey);
                break;
            case TabStatus.Merge:
                opt = this._mergeTabOptions.find(v => v.key == optKey);
                break;
            default:
                logError("PlaneUI", `Dropdown onClickDropDownOption error ${this._tabStatus}`)
                break;
        }
        this.sortBag()
    }

    private sortBag() {
        let items = []
        let equips = []
        switch (this._tabStatus) {
            case TabStatus.Bag:
                equips = this.sortEquipsInBagTabStatus();
                items = this.sortItems();
                break;
            case TabStatus.Merge:
                equips = this.sortEquipsInCombineTabStatus();
                break;
            default:
                logError("PlaneUI", `Dropdown onDisPlayRefresh error ${this._tabStatus}`)
                break;
        }
        EventMgr.emit(PlaneUIEvent.SortTypeChange, this._tabStatus, equips, items);
    }

    private sortItems() {
        const sorted = []
        DataMgr.bag.items.forEach(v => {
            const cfg = MyApp.lubanMgr.table.TbItem.get(v.item_id)
            if (cfg) {
                sorted.push(v)
            }
        })
        sorted.sort((a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {
            return b.add_time - a.add_time
        })
        return sorted
    }

    private sortEquipsInBagTabStatus() {
        const tbEquip = MyApp.lubanMgr.table.TbEquip;
        const equippedList = DataMgr.equip.eqSlots.getEquippedList().map(v => tbEquip.get(v));

        // 1. 找出所有空部位
        const emptySlots: csproto.cs.ICSEquipSlotInfo[] = DataMgr.equip.eqSlots.getEmptySlots();

        logDebug("PlaneUI", `sortEquipsInBagTabStatus item_total:${DataMgr.bag.items.length}, empty_slots:${emptySlots.join(',')}`);

        // 2. 将装备分为三部分：
        //    - emptySlotEquips: 对应空部位的装备（最高优先级）
        //    - unequippedEquips: 未装备的其他装备
        //    - equippedEquips: 已装备的装备（最低优先级）
        const emptySlotEquips: csproto.cs.ICSItem[] = [];
        const unequippedEquips: csproto.cs.ICSItem[] = [];
        const equippedEquips: csproto.cs.ICSItem[] = [];

        for (const item of DataMgr.bag.items) {
            const cfg = tbEquip.get(item.item_id);
            if (!cfg) continue;

            // 检查是否已装备
            const isEquipped = equippedList.some(equipped => equipped && equipped.equipClass === cfg.equipClass);

            if (isEquipped) {
                equippedEquips.push(item);
            } else {
                // 检查是否对应空部位
                const isEmptySlotEquip = emptySlots.some(slot => {
                    return slot.equip_class === cfg.equipClass;
                });
                if (isEmptySlotEquip) {
                    emptySlotEquips.push(item);
                } else {
                    unequippedEquips.push(item);
                }
            }
        }

        // 3. 排序函数
        const sortFn = (a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {
            const aCfg = tbEquip.get(a.item_id);
            const bCfg = tbEquip.get(b.item_id);

            if (this._sortType === BagSortType.Part) {
                // 按部位排序：先按部位类型，再按品质（从高到低）
                return aCfg.equipClass - bCfg.equipClass || bCfg.quality - aCfg.quality;
            } else {
                // 按品质排序：先按品质（从高到低），再按部位类型
                return bCfg.quality - aCfg.quality || aCfg.equipClass - bCfg.equipClass;
            }
        };

        // 4. 分别排序三部分
        const sortedEmptySlotEquips = emptySlotEquips.sort(sortFn);
        const sortedUnequippedEquips = unequippedEquips.sort(sortFn);
        const sortedEquippedEquips = equippedEquips.sort(sortFn);

        // 5. 合并结果：空部位装备 → 未装备装备 → 已装备装备
        const sorted = [...sortedEmptySlotEquips, ...sortedUnequippedEquips, ...sortedEquippedEquips];

        // 6. 调试输出
        sorted.forEach((e, index) => {
            const cfg = tbEquip.get(e.item_id);
            const equipType = sortedEmptySlotEquips.includes(e) ? "[空部位]" :
                sortedEquippedEquips.includes(e) ? "[已装备]" : "[未装备]";
            logDebug("PlaneUI", `${index + 1}. ${equipType} ID:${e.item_id} 部位:${cfg.equipClass} 品质:${cfg.quality} 数量:${e.count}`);
        });

        return sorted;
    }



    /**
     * 材料排序函数（支持主材料格子优先）
     * 功能：对背包中的材料进行智能排序
     * 排序规则：
     * 1. 如果主材料格子有放主材料，优先排该主材料相关的副材料
     * 2. 优先排可合成的主材料及其副材料
     * 3. 如果有多个可合成的材料，按合成后的品质优先级排序
     * 4. 剩余材料按当前材料品质排序
     * @returns 排序后的材料列表
     */
    sortEquipsInCombineTabStatus(): any[] {
        const tbEquip = MyApp.lubanTables.TbEquip;
        const bagItems = DataMgr.bag.items;

        // 获取主材料格子上的材料
        const combineMainMaterial = DataMgr.equip.eqCombine.getByPos(0)?.item;
        const mainMaterialInSlot = combineMainMaterial ? {
            id: combineMainMaterial.item_id,
            count: combineMainMaterial.count
        } : null;

        // 获取所有需要多个材料的装备
        const multiMaterialEquips = tbEquip.getDataList().filter(v => v.consumeItems.length > 1);

        // 存储每个材料的排序信息
        const materialInfos = new Map<number, {
            item: csproto.cs.ICSItem;
            isMainMaterial: boolean;
            canSynthesize: boolean;
            synthesisPriority: number; // 合成优先级（品质越高，数值越大）
            isRelatedToSlotMain: boolean;
            relatedEquipClass: number;
            materialQuality: number; // 材料自身的品质
        }>();

        // 初始化材料信息
        bagItems.forEach(item => {
            const equipCfg = tbEquip.get(item.item_id);
            if (!equipCfg) return;

            materialInfos.set(item.item_id, {
                item: item,
                isMainMaterial: false,
                canSynthesize: false,
                synthesisPriority: 0,
                isRelatedToSlotMain: false,
                relatedEquipClass: 0,
                materialQuality: equipCfg.quality || 0
            });
        });

        // 存储所有可合成的装备信息
        const synthesizableEquips: Array<{
            equipConfig: any;
            mainMaterialId: number;
            mainMaterialRequire: number;
            subMaterials: Array<{ id: number, num: number }>;
            priority: number; // 装备品质决定的优先级
        }> = [];

        // 第一步：找出所有可合成的装备
        multiMaterialEquips.forEach(equipConfig => {
            if (equipConfig.consumeItems.length < 2) return;

            const mainMaterial = equipConfig.consumeItems[0];
            const subMaterials = equipConfig.consumeItems.slice(1);

            // 检查主材料是否足够
            const mainMaterialInfo = materialInfos.get(mainMaterial.id);
            if (!mainMaterialInfo || mainMaterialInfo.item.count < mainMaterial.num) {
                return;
            }

            // 检查所有副材料是否足够
            let allSubMaterialsSufficient = true;
            for (const subMaterial of subMaterials) {
                const subMaterialInfo = materialInfos.get(subMaterial.id);
                if (!subMaterialInfo || subMaterialInfo.item.count < subMaterial.num) {
                    allSubMaterialsSufficient = false;
                    break;
                }
            }

            if (allSubMaterialsSufficient) {
                synthesizableEquips.push({
                    equipConfig,
                    mainMaterialId: mainMaterial.id,
                    mainMaterialRequire: mainMaterial.num,
                    subMaterials,
                    priority: equipConfig.quality || 0 // 使用装备品质作为优先级
                });
            }
        });

        // 第二步：按装备品质对可合成装备排序（品质高的优先）
        synthesizableEquips.sort((a, b) => b.priority - a.priority);

        // 第三步：标记材料信息
        synthesizableEquips.forEach(synthEquip => {
            const { equipConfig, mainMaterialId, subMaterials, priority } = synthEquip;

            // 标记主材料
            const mainMaterialInfo = materialInfos.get(mainMaterialId);
            if (mainMaterialInfo) {
                mainMaterialInfo.canSynthesize = true;
                mainMaterialInfo.isMainMaterial = true;
                mainMaterialInfo.synthesisPriority = Math.max(mainMaterialInfo.synthesisPriority, priority);
                mainMaterialInfo.relatedEquipClass = equipConfig.equipClass || 0;

                // 检查是否与主材料格子相关
                if (mainMaterialInSlot && mainMaterialId === mainMaterialInSlot.id) {
                    mainMaterialInfo.isRelatedToSlotMain = true;
                }
            }

            // 标记副材料
            subMaterials.forEach(subMaterial => {
                const subMaterialInfo = materialInfos.get(subMaterial.id);
                if (subMaterialInfo) {
                    subMaterialInfo.canSynthesize = true;
                    subMaterialInfo.synthesisPriority = Math.max(subMaterialInfo.synthesisPriority, priority);
                    subMaterialInfo.relatedEquipClass = equipConfig.equipClass || 0;

                    // 如果是与主材料格子相关的装备，标记副材料也相关
                    if (mainMaterialInSlot && mainMaterialId === mainMaterialInSlot.id) {
                        subMaterialInfo.isRelatedToSlotMain = true;
                    }
                }
            });
        });

        // 第四步：转换Map为数组并排序
        const materialsArray = Array.from(materialInfos.values());

        materialsArray.sort((a, b) => {
            // 1. 与主材料格子相关的材料最优先
            if (a.isRelatedToSlotMain && !b.isRelatedToSlotMain) return -1;
            if (!a.isRelatedToSlotMain && b.isRelatedToSlotMain) return 1;

            // 2. 可合成的材料优先于不可合成的
            if (a.canSynthesize && !b.canSynthesize) return -1;
            if (!a.canSynthesize && b.canSynthesize) return 1;

            // 3. 都可合成时，按合成优先级排序（品质高的优先）
            if (a.canSynthesize && b.canSynthesize) {
                if (a.synthesisPriority !== b.synthesisPriority) {
                    return b.synthesisPriority - a.synthesisPriority;
                }

                // 同一合成优先级时，主材料优先于副材料
                if (a.isMainMaterial && !b.isMainMaterial) return -1;
                if (!a.isMainMaterial && b.isMainMaterial) return 1;
            }

            // 4. 都不可合成或同一优先级时，按材料自身品质排序（品质高的优先）
            if (b.materialQuality !== a.materialQuality) {
                return b.materialQuality - a.materialQuality;
            }

            // 5. 最后按ID排序确保稳定性
            return a.item.item_id - b.item.item_id;
        });

        // 调试输出
        logDebug("PlaneUI", `材料排序结果 - 主材料格子: ${mainMaterialInSlot ? `ID ${mainMaterialInSlot.id}` : '空'}`);
        logDebug("PlaneUI", `可合成装备数量: ${synthesizableEquips.length}`);

        materialsArray.forEach((material, index) => {
            const type = material.isMainMaterial ? '主材料' : '副材料';
            const status = material.canSynthesize ? `可合成(品质${material.synthesisPriority})` : '不可合成';
            const related = material.isRelatedToSlotMain ? '[格子相关]' : '';
            const quality = `品质${material.materialQuality}`;
            logDebug("PlaneUI", `${index + 1}. ID:${material.item.item_id} 数量:${material.item.count} ${type} ${status} ${quality} ${related}`);
        });

        return materialsArray.map(v => v.item);
    }


}