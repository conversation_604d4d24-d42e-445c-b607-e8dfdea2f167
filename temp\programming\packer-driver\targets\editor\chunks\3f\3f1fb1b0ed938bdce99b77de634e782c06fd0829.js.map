{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "Node", "Sprite", "Animation", "Label", "tween", "UIOpacity", "sp", "UITransform", "Tween", "view", "v3", "size", "SpriteAtlas", "Plane", "GameConst", "GameIns", "GameEnum", "BattleLayer", "FireShells", "Bullet", "EnemyEntity", "BossUnit", "EffectLayer", "FBoxCollider", "ColliderGroupType", "MyApp", "GameResourceList", "ccclass", "property", "AnimState", "MainPlane", "Skeleton", "hpbarBack", "hpbarMid", "hpbarFont", "hpfont", "hpMidActin", "m_fireAnim", "m_screenDatas", "m_moveEnable", "m_config", "m_collideComp", "mainData", "m_fires", "m_fireState", "m_animState", "idle", "_hurtActTime", "_hurtActDuration", "onLoad", "mainPlaneManager", "_mainConfig", "hpBar", "getChildByName", "getComponent", "addComponent", "init", "groupType", "PLAYER", "start", "enemy", "gameDataManager", "battlePlaneActive", "isEnable", "m_comps", "for<PERSON>ach", "comp", "initPlane", "setFireEnable", "update", "dt", "GameAble", "gameRuleManager", "gameState", "GameState", "Battle", "fireEnable", "resMgr", "loadAsync", "atlas_mainPlane", "type", "addFireAnim", "getFireState", "changeScreenLv", "screenLv", "initPic", "attspe", "attspeAnim", "node", "atlas", "spriteFrame", "getSpriteFrame", "transSrc", "position", "transExt", "split", "setPosition", "Number", "scrAnim", "children", "child", "scr", "pos", "auxAnim", "aux", "level", "attack<PERSON>ey", "attackData", "i", "length", "screenData", "slice", "push", "data", "index", "createAttackPoint", "changeScreen", "zjdmtxzb", "x", "y", "fireNode", "parent", "skin", "opacity", "skeletonData", "spine_mainfire", "SkeletonData", "skeleton", "setAnimation", "initBattle", "active", "UpdateHp", "planeIn", "self", "frameTime", "ActionFrameTime", "me", "selfPlane<PERSON><PERSON><PERSON>", "setScale", "posY", "getVisibleSize", "height", "battleManager", "getRatio", "stopFire", "stopAllByTarget", "scheduleOnce", "targetY", "targetX", "to", "animSpeed", "call", "begine", "scale", "delay", "streak", "addStreak", "battleQuit", "die", "cutHp", "damage", "hurtTotal", "newHp", "hp", "Math", "max", "to<PERSON><PERSON>", "addHp", "heal", "min", "maxhp", "onCollide", "collision", "entity", "getAttack", "getColliderAtk", "_playHurtAnim", "showRedScreen", "onControl", "posX", "ViewHeight", "stop", "fill<PERSON><PERSON><PERSON>", "duration", "abs", "string", "toFixed", "onRelife", "reviveCount", "relife", "reviveType", "revive", "atkChallenge", "atkAddRatio", "attack", "attackLv", "hpAttackLv", "speedLv", "cirtLv", "catapultLv", "fireIntensify", "intensifyAtk", "_playDieAnim", "blast", "setCompleteListener", "_dieAnimEnd", "bind", "battleFail", "enable", "anim", "setMoveAble", "setColAble", "beginFire", "isContinue", "onPlaneIn", "fire", "setData", "removeAllFire", "PrefabName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAwBC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAC9JC,MAAAA,K;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AAEFC,MAAAA,Q;;AACAC,MAAAA,W;;AAGAC,MAAAA,U;;AACAC,MAAAA,M;;AACAC,MAAAA,W;;AACAC,MAAAA,Q;;AACAC,MAAAA,W;;AACAC,MAAAA,Y;;AACEC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,K,kBAAAA,K;;AACFC,MAAAA,gB;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwB7B,U;AAG9B;AACA;AACA;;AACK8B,MAAAA,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;QAAAA,S;;2BAOQC,S,WADZH,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAAC3B,MAAD,C,UAGR2B,QAAQ,CAAC5B,IAAD,C,UAER4B,QAAQ,CAAC5B,IAAD,C,UAGR4B,QAAQ,CAACtB,EAAE,CAACyB,QAAJ,C,UAGRH,QAAQ,CAAC1B,SAAD,C,UAGR0B,QAAQ,CAAC1B,SAAD,C,UAGR0B,QAAQ,CAAC1B,SAAD,C,sCAtBb,MACa4B,SADb;AAAA;AAAA,0BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAwBjC;AAxBiC,eAyBjCE,SAzBiC,GAyBrB,IAzBqB;AAyBf;AAzBe,eA0BjCC,QA1BiC,GA0BtB,IA1BsB;AA0BhB;AA1BgB,eA2BjCC,SA3BiC,GA2BrB,IA3BqB;AA2Bf;AA3Be,eA4BjCC,MA5BiC,GA4BxB,IA5BwB;AA4BlB;AA5BkB,eA8BjCC,UA9BiC,GA8BpB,IA9BoB;AA8Bd;AA9Bc,eAgCjCC,UAhCiC,GAgCZ,EAhCY;AAgCR;AAEzB;AAlCiC,eAmCjCC,aAnCiC,GAmCjB,EAnCiB;AAmCb;AAnCa,eAoCjCC,YApCiC,GAoClB,IApCkB;AAoCZ;AApCY,eAqCjCC,QArCiC;AAqCvB;AArCuB,eAsCjCC,aAtCiC,GAsCH,IAtCG;AAsCG;AAtCH,eAuCjCC,QAvCiC;AAAA,eAwCjCC,OAxCiC,GAwCvB,EAxCuB;AAwCnB;AAEd;AA1CiC,eA2CjCC,WA3CiC,GA2CnB,IA3CmB;AA2Cb;AA3Ca,eA6CjCC,WA7CiC,GA6CRhB,SAAS,CAACiB,IA7CF;AA6CQ;AA7CR,eA+CjCC,YA/CiC,GA+ClB,CA/CkB;AA+Cf;AA/Ce,eAgDjCC,gBAhDiC,GAgDd,GAhDc;AAAA;;AAgDT;;AACxB;AACJ;AACA;AACA;AACIC,QAAAA,MAAM,GAAG;AACL,eAAKT,QAAL,GAAgB;AAAA;AAAA,kCAAQU,gBAAR,CAAyBC,WAAzC,CADK,CACiD;;AACtD,eAAKT,QAAL,GAAgB;AAAA;AAAA,kCAAQQ,gBAAR,CAAyBR,QAAzC,CAFK,CAE8C;;AAGnD,eAAKV,SAAL,GAAiB,KAAKoB,KAAL,CAAWC,cAAX,CAA0B,WAA1B,EAAuCC,YAAvC,CAAoDrD,MAApD,CAAjB;AACA,eAAKgC,QAAL,GAAgB,KAAKmB,KAAL,CAAWC,cAAX,CAA0B,UAA1B,EAAsCC,YAAtC,CAAmDrD,MAAnD,CAAhB;AACA,eAAKiC,SAAL,GAAiB,KAAKkB,KAAL,CAAWC,cAAX,CAA0B,WAA1B,EAAuCC,YAAvC,CAAoDrD,MAApD,CAAjB;AACA,eAAKkC,MAAL,GAAc,KAAKiB,KAAL,CAAWC,cAAX,CAA0B,QAA1B,EAAoCC,YAApC,CAAiDnD,KAAjD,CAAd;AAEA,eAAKsC,aAAL,GAAqB,KAAKa,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAxD;AACA,eAAKd,aAAL,CAAmBe,IAAnB,CAAwB,IAAxB,EAA6B7C,IAAI,CAAC,EAAD,EAAI,EAAJ,CAAjC,EAXK,CAWsC;;AAC3C,eAAK8B,aAAL,CAAmBgB,SAAnB,GAA+B;AAAA;AAAA,sDAAkBC,MAAjD;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKC,KAAL,GAAa,KAAb;AACA;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,iBAAxB,GAA4C,IAA5C;AAEA,eAAKrB,aAAL,CAAmBsB,QAAnB,GAA8B,IAA9B,CAJI,CAOJ;;AACA,eAAKlB,WAAL,GAAmBhB,SAAS,CAACiB,IAA7B,CARI,CAUJ;;AACA,eAAKkB,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACV,IAAL,CAAU,IAAV;AACH,WAFD,EAXI,CAeJ;;AACA,eAAKW,SAAL,GAhBI,CAkBJ;;AACA,eAAKC,aAAL,CAAmB,KAAnB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAK;AACP,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB,OADlB,CAGP;;AACA,cAAID,EAAE,GAAG,GAAT,EAAcA,EAAE,GAAG,iBAAL,CAJP,CAMP;;AACA,cACI;AAAA;AAAA,kCAAQE,eAAR,CAAwBC,SAAxB,KAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAAzD,IACA;AAAA;AAAA,kCAAQzB,gBAAR,CAAyB0B,UAF7B,EAGE;AACE;AACA,iBAAKZ,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACG,MAAL,CAAYC,EAAZ;AACH,aAFD;AAGH,WAfM,CAgBP;;;AACA,eAAKvB,YAAL,IAAqBuB,EAArB;AACH;AAED;AACJ;AACA;;;AACmB,cAATH,SAAS,GAAG;AACd;AACA,gBAAM;AAAA;AAAA,8BAAMU,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,oDAAiBC,eAAjB,GAAmC,KAAKvC,QAAL,CAAcwC,IAAxE,EAA6EpE,WAA7E,CAAN,CAFc,CAId;;AACA,gBAAM,KAAKqE,WAAL,EAAN,CALc,CAOd;;AACA,eAAKC,YAAL,GARc,CAUd;;AACA,eAAKC,cAAL,CAAoB,KAAKzC,QAAL,CAAc0C,QAAlC,EAXc,CAad;;AACA,eAAKC,OAAL;AACH;AAED;AACJ;AACA;;;AACiB,cAAPA,OAAO,GAAG;AACZ,gBAAMC,MAAM,GAAG,KAAKC,UAAL,CAAgBC,IAAhB,CAAqBnC,cAArB,CAAoC,QAApC,CAAf;AACA,gBAAMoC,KAAK,GAAG,MAAM;AAAA;AAAA,8BAAMZ,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,oDAAiBC,eAAjB,GAAmC,KAAKvC,QAAL,CAAcwC,IAAxE,EAA8EpE,WAA9E,CAApB;AACA0E,UAAAA,MAAM,CAAChC,YAAP,CAAoBrD,MAApB,EAA4ByF,WAA5B,GAA0CD,KAAK,CAACE,cAAN,CAAsB,GAAE,KAAKnD,QAAL,CAAcoD,QAAd,CAAuB,CAAvB,CAA0B,GAAlD,CAA1C;AAGA,gBAAMC,QAAQ,GAAG,KAAKrD,QAAL,CAAcsD,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAjB;AACAT,UAAAA,MAAM,CAACU,WAAP,CAAmBC,MAAM,CAACJ,QAAQ,CAAC,CAAD,CAAT,CAAzB,EAAwCI,MAAM,CAACJ,QAAQ,CAAC,CAAD,CAAT,CAA9C;AAEA,eAAKK,OAAL,CAAaV,IAAb,CAAkBW,QAAlB,CAA2BlC,OAA3B,CAAmC,MAAOmC,KAAP,IAAiB;AAChD,kBAAMC,GAAG,GAAGD,KAAK,CAAC/C,cAAN,CAAqB,KAArB,CAAZ;AACAgD,YAAAA,GAAG,CAAC/C,YAAJ,CAAiBrD,MAAjB,EAAyByF,WAAzB,GAAuCD,KAAK,CAACE,cAAN,CAAsB,GAAE,KAAKnD,QAAL,CAAcoD,QAAd,CAAuB,CAAvB,CAA0B,GAAlD,CAAvC;AACA,kBAAMU,GAAG,GAAG,KAAK9D,QAAL,CAAcsD,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAZ;AACAK,YAAAA,KAAK,CAACJ,WAAN,CAAkBC,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxB,EAAkCL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxC;AACH,WALD;AAOA,eAAKC,OAAL,CAAaf,IAAb,CAAkBW,QAAlB,CAA2BlC,OAA3B,CAAmC,MAAOmC,KAAP,IAAiB;AAChD,kBAAMI,GAAG,GAAGJ,KAAK,CAAC/C,cAAN,CAAqB,KAArB,CAAZ;AACAmD,YAAAA,GAAG,CAAClD,YAAJ,CAAiBrD,MAAjB,EAAyByF,WAAzB,GAAuCD,KAAK,CAACE,cAAN,CAAsB,GAAE,KAAKnD,QAAL,CAAcoD,QAAd,CAAuB,CAAvB,CAA0B,GAAlD,CAAvC;AACA,kBAAMU,GAAG,GAAG,KAAK9D,QAAL,CAAcsD,QAAd,CAAuB,CAAvB,EAA0BC,KAA1B,CAAgC,GAAhC,CAAZ;AACAK,YAAAA,KAAK,CAACJ,WAAN,CAAkBC,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxB,EAAkCL,MAAM,CAACK,GAAG,CAAC,CAAD,CAAJ,CAAxC;AACH,WALD;AAMH;AAED;AACJ;AACA;AACA;;;AACInB,QAAAA,cAAc,CAACsB,KAAD,EAAQ;AAClB,cAAIA,KAAK,KAAK,CAAd,EAAiB;AAEjB,eAAKnE,aAAL,GAAqB,EAArB;AACA,gBAAMoE,SAAS,GAAG,aAAlB;AACA,gBAAMC,UAAU,GAAG,KAAKnE,QAAL,CAAe,GAAEkE,SAAU,GAAED,KAAM,EAAnC,CAAnB;;AAEA,eAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,UAAU,CAACE,MAA/B,EAAuCD,CAAC,IAAI,CAA5C,EAA+C;AAC3C,kBAAME,UAAU,GAAGH,UAAU,CAACI,KAAX,CAAiBH,CAAjB,EAAoBA,CAAC,GAAG,CAAxB,CAAnB;AACA,iBAAKtE,aAAL,CAAmB0E,IAAnB,CAAwBF,UAAxB;AACH;;AAGD,eAAKxE,aAAL,CAAmB2B,OAAnB,CAA2B,CAACgD,IAAD,EAAOC,KAAP,KAAiB;AACxC,gBAAI,KAAKvE,OAAL,CAAauE,KAAb,KAAuB,IAA3B,EAAiC;AAC7B,mBAAKC,iBAAL,CAAuBF,IAAvB;AACH,aAFD,MAEO;AACH,mBAAKG,YAAL,CAAkBF,KAAlB,EAAyBD,IAAzB;AACH;AACJ,WAND;;AAQA,eAAK,IAAIL,CAAC,GAAG,KAAKtE,aAAL,CAAmBuE,MAAhC,EAAwCD,CAAC,GAAG,KAAKjE,OAAL,CAAakE,MAAzD,EAAiED,CAAC,EAAlE,EAAsE;AAClE,iBAAKQ,YAAL,CAAkBR,CAAlB,EAAqB,IAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACqB,cAAX3B,WAAW,GAAG;AAChB,eAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpE,QAAL,CAAc6E,QAAd,CAAuBR,MAA3C,EAAmDD,CAAC,IAAI,CAAxD,EAA2D;AACvD,kBAAMU,CAAC,GAAG,KAAK9E,QAAL,CAAc6E,QAAd,CAAuBT,CAAvB,CAAV;AACA,kBAAMW,CAAC,GAAG,KAAK/E,QAAL,CAAc6E,QAAd,CAAuBT,CAAC,GAAG,CAA3B,CAAV;AAEA,kBAAMY,QAAQ,GAAG,IAAIxH,IAAJ,CAAS,UAAT,CAAjB;AACAwH,YAAAA,QAAQ,CAACjE,YAAT,CAAsBhD,WAAtB;AACAiH,YAAAA,QAAQ,CAACjE,YAAT,CAAsBlD,SAAtB;AACAmH,YAAAA,QAAQ,CAACxB,WAAT,CAAqBC,MAAM,CAACqB,CAAD,CAA3B,EAAgCrB,MAAM,CAACsB,CAAD,CAAtC;AACAC,YAAAA,QAAQ,CAACC,MAAT,GAAkB,KAAKC,IAAvB;AACA,iBAAKrF,UAAL,CAAgB2E,IAAhB,CAAqBQ,QAArB;AACAA,YAAAA,QAAQ,CAAClE,YAAT,CAAsBjD,SAAtB,EAAiCsH,OAAjC,GAA2C,CAA3C;AAEA,kBAAMC,YAAY,GAAG,MAAM;AAAA;AAAA,gCAAM/C,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,sDAAiB+C,cAAxC,EAAuDvH,EAAE,CAACwH,YAA1D,CAA3B;AACA,kBAAMC,QAAQ,GAAGP,QAAQ,CAACjE,YAAT,CAAsBjD,EAAE,CAACyB,QAAzB,CAAjB;AACAgG,YAAAA,QAAQ,CAACH,YAAT,GAAwBA,YAAxB;AACAG,YAAAA,QAAQ,CAACC,YAAT,CAAsB,CAAtB,EAAyB,MAAzB,EAAiC,IAAjC;AACH;AACJ;;AAEDC,QAAAA,UAAU,GAAG;AACT,eAAKzC,IAAL,CAAU0C,MAAV,GAAmB,IAAnB,CADS,CAET;;AACA,eAAKhD,YAAL;AAEA,eAAKzC,aAAL,CAAmBsB,QAAnB,GAA8B,KAA9B;AACA,eAAKoE,QAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,gBAAMC,IAAI,GAAG,IAAb;AAEA,gBAAMC,SAAS,GAAG;AAAA;AAAA,sCAAUC,eAA5B,CAHY,CAKZ;AACA;AAEA;;AACA,eAAK/C,IAAL,CAAUlC,YAAV,CAAuBjD,SAAvB,EAAkCsH,OAAlC,GAA4C,GAA5C;AACA,eAAKnC,IAAL,CAAUiC,MAAV,GAAmB;AAAA;AAAA,0CAAYe,EAAZ,CAAeC,cAAlC;AACA,eAAKf,IAAL,CAAUpE,YAAV,CAAuBjD,SAAvB,EAAkCsH,OAAlC,GAA4C,GAA5C;AACA,eAAKD,IAAL,CAAUgB,QAAV,CAAmB,CAAnB,EAAsB,CAAtB;AACA,cAAIC,IAAI,GAAG,CAAClI,IAAI,CAACmI,cAAL,GAAsBC,MAAvB,IAAiC,KAAKrG,QAAL,CAAcwC,IAAd,KAAuB,GAAvB,GAA6B,IAA7B,GAAoC,EAArE,CAAX;AACA,eAAKQ,IAAL,CAAUQ,WAAV,CAAsB,CAAtB,EAAyB2C,IAAzB;AACA,eAAKnD,IAAL,CAAUkD,QAAV,CAAmB;AAAA;AAAA,kCAAQI,aAAR,CAAsBC,QAAtB,EAAnB,EAAqD;AAAA;AAAA,kCAAQD,aAAR,CAAsBC,QAAtB,EAArD;AACA,eAAKC,QAAL;AACA,eAAKzG,YAAL,GAAoB,KAApB;AACA/B,UAAAA,KAAK,CAACyI,eAAN,CAAsB,KAAKzD,IAA3B,EAlBY,CAoBZ;AAEA;;AACA,eAAK0D,YAAL,CAAkB,MAAM;AACpB,kBAAMC,OAAO,GAAG,CAAC1I,IAAI,CAACmI,cAAL,GAAsBC,MAAvB,GAAgC,GAAhD;AACA,kBAAMO,OAAO,GAAG,KAAK5D,IAAL,CAAUK,QAAV,CAAmByB,CAAnC;AACAlH,YAAAA,KAAK,CAAC,KAAKoF,IAAN,CAAL,CACK6D,EADL,CACQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAD/C,EAC0D;AAAEzD,cAAAA,QAAQ,EAAEnF,EAAE,CAAC0I,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAD1D,EAEKE,EAFL,CAEQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAF/C,EAE0D;AAAEzD,cAAAA,QAAQ,EAAEnF,EAAE,CAAC0I,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAF1D,EAGKE,EAHL,CAGQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAH/C,EAG0D;AAAEzD,cAAAA,QAAQ,EAAEnF,EAAE,CAAC0I,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAH1D,EAIKE,EAJL,CAIQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAJ/C,EAI0D;AAAEzD,cAAAA,QAAQ,EAAEnF,EAAE,CAAC0I,OAAD,EAAUD,OAAV;AAAd,aAJ1D,EAKKI,IALL,CAKU,MAAM;AACRlB,cAAAA,IAAI,CAACmB,MAAL;AACH,aAPL,EAQK7F,KARL;AAUAvD,YAAAA,KAAK,CAAC,KAAKsH,IAAN,CAAL,CACK2B,EADL,CACQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAD/C,EAC0D;AAAEG,cAAAA,KAAK,EAAE/I,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aAD1D,EAEK2I,EAFL,CAEQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAF/C,EAE0D;AAAEG,cAAAA,KAAK,EAAE/I,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aAF1D,EAGK2I,EAHL,CAGQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAH/C,EAG0D;AAAEG,cAAAA,KAAK,EAAE/I,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAH1D,EAIK2I,EAJL,CAIQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SAJ/C,EAI0D;AAAEG,cAAAA,KAAK,EAAE/I,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAJ1D,EAKKiD,KALL;;AAOA,gBAAI,KAAKP,KAAT,EAAgB;AACZhD,cAAAA,KAAK,CAAC,KAAKgD,KAAL,CAAWE,YAAX,CAAwBjD,SAAxB,CAAD,CAAL,CACKgJ,EADL,CACQ,CADR,EACW;AAAE1B,gBAAAA,OAAO,EAAE;AAAX,eADX,EAEK+B,KAFL,CAEW,KAAKpB,SAAL,GAAiB;AAAA;AAAA,sCAAQQ,aAAR,CAAsBQ,SAFlD,EAGKD,EAHL,CAGQ,KAAKf,SAAL,GAAiB;AAAA;AAAA,sCAAQQ,aAAR,CAAsBQ,SAH/C,EAG0D;AAAE3B,gBAAAA,OAAO,EAAE;AAAX,eAH1D,EAIKhE,KAJL;AAKH;;AAED,iBAAKuF,YAAL,CAAkB,MAAM;AACpB9I,cAAAA,KAAK,CAAC,KAAKuJ,MAAL,CAAYnE,IAAb,CAAL,CACK6D,EADL,CACQ,IAAIf,SAAJ,GAAgB;AAAA;AAAA,sCAAQQ,aAAR,CAAsBQ,SAD9C,EACyD;AAAEG,gBAAAA,KAAK,EAAE/I,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eADzD,EAEK2I,EAFL,CAEQ,IAAIf,SAAJ,GAAgB;AAAA;AAAA,sCAAQQ,aAAR,CAAsBQ,SAF9C,EAEyD;AAAEG,gBAAAA,KAAK,EAAE/I,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAFzD,EAGK2I,EAHL,CAGQ,IAAIf,SAAJ,GAAgB;AAAA;AAAA,sCAAQQ,aAAR,CAAsBQ,SAH9C,EAGyD;AAAEG,gBAAAA,KAAK,EAAE/I,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAHzD,EAIK6I,IAJL,CAIU,MAAM;AACRlB,gBAAAA,IAAI,CAACuB,SAAL;AACH,eANL,EAOKjG,KAPL;AAQH,aATD,EASG,IAAI2E,SAAJ,GAAgB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBQ,SATzC;AAUH,WAtCD,EAsCG,IAAIhB,SAAJ,GAAgB;AAAA;AAAA,kCAAQQ,aAAR,CAAsBQ,SAtCzC;AAuCH;AAED;AACJ;AACA;;;AACIO,QAAAA,UAAU,GAAG;AACT,eAAKnH,QAAL,CAAcoH,GAAd,GAAoB,KAApB,CADS,CAET;AAEA;;AACA,eAAKpC,IAAL,CAAUpE,YAAV,CAAuBjD,SAAvB,EAAkCsH,OAAlC,GAA4C,GAA5C,CALS,CAMT;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACH;AAGD;AACJ;AACA;AACA;;;AACIoC,QAAAA,KAAK,CAACC,MAAD,EAAS;AACV;AAAA;AAAA,kCAAQ9G,gBAAR,CAAyB+G,SAAzB,IAAsCD,MAAtC;AACA,gBAAME,KAAK,GAAG;AAAA;AAAA,kCAAQhH,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuCH,MAArD;AACA;AAAA;AAAA,kCAAQ9G,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuCC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYH,KAAZ,CAAvC;;AAEA,cAAIA,KAAK,GAAG,CAAZ,EAAe;AACX;AAAA;AAAA,oCAAQhH,gBAAR,CAAyB+G,SAAzB,IAAsCC,KAAtC;AACH;;AAED,eAAK/B,QAAL;AAAgB;;AAEhB,cAAI;AAAA;AAAA,kCAAQjF,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,IAAwC,CAAxC,IAA6C,CAAC,KAAKzH,QAAL,CAAcoH,GAAhE,EAAqE;AACjE,iBAAKQ,KAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,KAAK,CAACC,IAAD,EAAO;AACR;AAAA;AAAA,kCAAQtH,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuCC,IAAI,CAACK,GAAL,CACnC;AAAA;AAAA,kCAAQvH,gBAAR,CAAyBR,QAAzB,CAAkCgI,KADC,EAEnC;AAAA;AAAA,kCAAQxH,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuCK,IAFJ,CAAvC;AAIA,eAAKrC,QAAL;AAAgB;AACnB;AAED;AACJ;AACA;AACA;;;AACIwC,QAAAA,SAAS,CAACC,SAAD,EAAY;AACjB;AAEA,cAAIZ,MAAM,GAAG,CAAb;;AACA,cAAIY,SAAS,CAACC,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpCb,YAAAA,MAAM,GAAGY,SAAS,CAACC,MAAV,CAAiBC,SAAjB,CAA2B,IAA3B,CAAT;AACH,WAFD,MAEO,IACHF,SAAS,CAACC,MAAV;AAAA;AAAA,6CACAD,SAAS,CAACC,MAAV;AAAA;AAAA,mCAFG,EAGL;AACEb,YAAAA,MAAM,GAAGY,SAAS,CAACC,MAAV,CAAiBE,cAAjB,EAAT;AACH;;AAED,cAAIf,MAAM,GAAG,CAAb,EAAgB;AACZ,iBAAKD,KAAL,CAAWC,MAAX;;AACA,iBAAKgB,aAAL;;AACA,gBAAI;AAAA;AAAA,oCAAQ9H,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,IAAwC,CAAxC,IAA6C,CAAC,KAAKzH,QAAL,CAAcoH,GAAhE,EAAqE;AACjE,mBAAKQ,KAAL;AACH;AACJ;AACJ;;AAEOU,QAAAA,aAAa,GAAG;AACpB,cAAI,KAAKjI,YAAL,GAAoB,KAAKC,gBAA7B,EAA+C;AAC3C,iBAAKD,YAAL,GAAoB,CAApB,CAD2C,CAE3C;;AACA;AAAA;AAAA,4CAAYyF,EAAZ,CAAeyC,aAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,IAAD,EAAOxC,IAAP,EAAa;AAClB,cAAI,CAAC,KAAKjG,QAAL,CAAcoH,GAAf,IAAsB,KAAKvH,YAA/B,EAA6C;AACzC;AACA4I,YAAAA,IAAI,GAAGf,IAAI,CAACK,GAAL,CAAS,GAAT,EAAcU,IAAd,CAAP;AACAA,YAAAA,IAAI,GAAGf,IAAI,CAACC,GAAL,CAAS,CAAC,GAAV,EAAec,IAAf,CAAP;AACAxC,YAAAA,IAAI,GAAGyB,IAAI,CAACK,GAAL,CAAS,CAAT,EAAY9B,IAAZ,CAAP;AACAA,YAAAA,IAAI,GAAGyB,IAAI,CAACC,GAAL,CAAS,CAAC;AAAA;AAAA,wCAAUe,UAApB,EAAgCzC,IAAhC,CAAP;AACA,iBAAKnD,IAAL,CAAUQ,WAAV,CAAsBmF,IAAtB,EAA4BxC,IAA5B;AACH;AACJ;AAED;AACJ;AACA;;;AACIR,QAAAA,QAAQ,GAAG;AACP,cAAI,KAAK/E,KAAL,IAAc,KAAKlB,SAAnB,IAAgC,KAAKD,QAAzC,EAAmD;AAC/C;AACA,gBAAI,KAAKG,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgBiJ,IAAhB;AACA,mBAAKjJ,UAAL,GAAkB,IAAlB;AACH,aAL8C,CAO/C;;;AACA,iBAAKF,SAAL,CAAeoJ,SAAf,GAA2B;AAAA;AAAA,oCAAQpI,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuC;AAAA;AAAA,oCAAQjH,gBAAR,CAAyBR,QAAzB,CAAkCgI,KAApG,CAR+C,CAU/C;;AACA,kBAAMa,QAAQ,GAAGnB,IAAI,CAACoB,GAAL,CAAS,KAAKvJ,QAAL,CAAcqJ,SAAd,GAA0B,KAAKpJ,SAAL,CAAeoJ,SAAlD,CAAjB,CAX+C,CAa/C;;AACA,iBAAKlJ,UAAL,GAAkBhC,KAAK,CAAC,KAAK6B,QAAN,CAAL,CACboH,EADa,CACVkC,QADU,EACA;AAAED,cAAAA,SAAS,EAAE,KAAKpJ,SAAL,CAAeoJ;AAA5B,aADA,EAEb/B,IAFa,CAER,MAAM;AACR,mBAAKnH,UAAL,GAAkB,IAAlB;AACH,aAJa,EAKbuB,KALa,EAAlB,CAd+C,CAqB/C;;AACA,iBAAKxB,MAAL,CAAYsJ,MAAZ,GAAqB;AAAA;AAAA,oCAAQvI,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,CAAqCuB,OAArC,CAA6C,CAA7C,CAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQ9H,eAAR,CAAwB+H,WAAxB,IAAuC,CAAvC,CADO,CACmC;;AAC1C,eAAKC,MAAL,CAAY,CAAZ,EAFO,CAES;AACnB;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,MAAM,CAACC,UAAD,EAAa;AAEf;AACA,eAAKpJ,QAAL,CAAcoH,GAAd,GAAoB,KAApB,CAHe,CAGY;;AAC3B,eAAKpH,QAAL,CAAcqJ,MAAd,GAAuB,IAAvB,CAJe,CAIc;;AAC7B,eAAK7C,YAAL,CAAkB,MAAM;AACpB,iBAAKxG,QAAL,CAAcqJ,MAAd,GAAuB,KAAvB;AACH,WAFD,EAEG,GAFH;AAIA;AAAA;AAAA,kCAAQlI,eAAR,CAAwBC,iBAAxB,GAA4C,IAA5C,CATe,CASmC;;AAClD;AAAA;AAAA,kCAAQZ,gBAAR,CAAyBR,QAAzB,CAAkCyH,EAAlC,GAAuC;AAAA;AAAA,kCAAQjH,gBAAR,CAAyBR,QAAzB,CAAkCgI,KAAzE,CAVe,CAUiE;;AAChF,eAAKvC,QAAL;AAAgB,WAXD,CAWG;AACrB;AACD;AACJ;AACA;;;AACIjD,QAAAA,YAAY,GAAG;AACX,gBAAM8G,YAAY,GAAG,KAAKtJ,QAAL,CAAcuJ,WAAnC;AAEA,eAAKrJ,WAAL,GAAmB;AACfsJ,YAAAA,MAAM,EAAE,KAAKxJ,QAAL,CAAcwJ,MADP;AAEfC,YAAAA,QAAQ,EAAE,CAFK;AAGfC,YAAAA,UAAU,EAAE,CAHG;AAIfC,YAAAA,OAAO,EAAE,CAJM;AAKfC,YAAAA,MAAM,EAAE,CALO;AAKL;AACVC,YAAAA,UAAU,EAAE,CANG;AAMD;AACdP,YAAAA,YAAY,EAAEA,YAPC;AAQfQ,YAAAA,aAAa,EAAE,KAAK9J,QAAL,CAAc+J;AARd,WAAnB;AAUH;AAED;AACJ;AACA;AACA;;;AACI3B,QAAAA,SAAS,GAAG;AACR,iBAAO,KAAKlI,WAAL,CAAiBsJ,MAAxB;AACH;;AAED5B,QAAAA,KAAK,GAAG;AAEJ;AACA,eAAK5H,QAAL,CAAcoH,GAAd,GAAoB,IAApB,CAHI,CAKJ;;AACA,eAAKrH,aAAL,CAAmBsB,QAAnB,GAA8B,KAA9B,CANI,CAQJ;;AACA,cAAI,KAAK9B,QAAT,EAAmB;AACf7B,YAAAA,KAAK,CAAC,KAAK6B,QAAN,CAAL,CAAqBoJ,IAArB;AACA,iBAAKpJ,QAAL,CAAcqJ,SAAd,GAA0B,CAA1B;AACH,WAZG,CAcJ;;;AACA,eAAKoB,YAAL,GAfI,CAiBJ;;;AACA;AAAA;AAAA,kCAAQ7I,eAAR,CAAwBC,iBAAxB,GAA4C,KAA5C;AACH;AAED;AACJ;AACA;;;AACI4I,QAAAA,YAAY,GAAG;AACX,eAAKC,KAAL,CAAWnH,IAAX,CAAgBlC,YAAhB,CAA6BjD,SAA7B,EAAwCsH,OAAxC,GAAkD,GAAlD,CADW,CAC4C;;AACvD,eAAKgF,KAAL,CAAWC,mBAAX,CAA+B,KAAKC,WAAL,CAAiBC,IAAjB,CAAsB,IAAtB,CAA/B,EAFW,CAEkD;;AAC7D,eAAKH,KAAL,CAAW3E,YAAX,CAAwB,CAAxB,EAA2B,MAA3B,EAAmC,KAAnC,EAHW,CAGgC;AAC9C;AAED;AACJ;AACA;;;AACI6E,QAAAA,WAAW,GAAS;AAChB;AACA,eAAKF,KAAL,CAAWnH,IAAX,CAAgBlC,YAAhB,CAA6BjD,SAA7B,EAAwCsH,OAAxC,GAAkD,CAAlD,CAFgB,CAIhB;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA,kCAAQmB,aAAR,CAAsBiE,UAAtB,GAvBgB,CAwBhB;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACI3I,QAAAA,aAAa,CAAC4I,MAAD,EAAS;AAClB,cAAI,KAAKxK,QAAL,IAAiB,KAAKA,QAAL,CAAcwC,IAAd,KAAuB,GAA5C,EAAiD;;AAEjD,cAAIgI,MAAJ,EAAY;AACR,iBAAK3K,UAAL,CAAgB4B,OAAhB,CAAyBgJ,IAAD,IAAU;AAC9BA,cAAAA,IAAI,CAAC3J,YAAL,CAAkBjD,SAAlB,EAA6BsH,OAA7B,GAAuC,GAAvC,CAD8B,CACc;AAC/C,aAFD;AAGH,WAJD,MAIO;AACH,iBAAKtF,UAAL,CAAgB4B,OAAhB,CAAyBgJ,IAAD,IAAU;AAC9BA,cAAAA,IAAI,CAAC3J,YAAL,CAAkBjD,SAAlB,EAA6BsH,OAA7B,GAAuC,CAAvC,CAD8B,CACY;AAC7C,aAFD;AAGH;AACJ;AAED;AACJ;AACA;AACA;;;AACIuF,QAAAA,WAAW,CAACF,MAAD,EAAS;AAChB,eAAKzK,YAAL,GAAoByK,MAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,UAAU,CAACH,MAAD,EAAS;AACf,eAAKvK,aAAL,CAAmBsB,QAAnB,GAA8BiJ,MAA9B;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQlK,gBAAR,CAAyB0B,UAAzB,GAAsC,IAAtC;AACH;AAED;AACJ;AACA;;;AACIoE,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQ9F,gBAAR,CAAyB0B,UAAzB,GAAsC,KAAtC;AACH;AAED;AACJ;AACA;AACA;;;AACI4E,QAAAA,MAAM,CAAC6D,UAAU,GAAG,KAAd,EAAqB;AACvB,cAAIA,UAAJ,EAAgB;AACZ,iBAAKD,SAAL;AACA,iBAAK7K,YAAL,GAAoB,IAApB;;AACA,gBAAI,KAAKE,aAAT,EAAwB;AACpB,mBAAKA,aAAL,CAAmBsB,QAAnB,GAA8B,IAA9B;AACH;AACJ,WAND,MAMO;AACH;AAAA;AAAA,oCAAQ+E,aAAR,CAAsBwE,SAAtB;AACH;AACJ;AAGD;AACJ;AACA;AACA;AACA;;;AACIlG,QAAAA,YAAY,CAACF,KAAD,EAAQD,IAAR,EAAc;AACtB,cAAIA,IAAI,IAAI,IAAZ,EAAkB;AACd,gBAAIC,KAAK,GAAG,KAAKvE,OAAL,CAAakE,MAAzB,EAAiC;AAC7B,oBAAM0G,IAAI,GAAG,KAAK5K,OAAL,CAAauE,KAAb,CAAb;AACAqG,cAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmB,KAAK5K,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AACA2K,cAAAA,IAAI,CAAC/H,IAAL,CAAU0C,MAAV,GAAmB,KAAnB;AACH;AACJ,WAND,MAMO;AACH,gBAAIhB,KAAK,GAAG,KAAKvE,OAAL,CAAakE,MAAzB,EAAiC;AAC7B,oBAAM0G,IAAI,GAAG,KAAK5K,OAAL,CAAauE,KAAb,CAAb;AACAqG,cAAAA,IAAI,CAAC/H,IAAL,CAAU0C,MAAV,GAAmB,IAAnB;AACAqF,cAAAA,IAAI,CAACC,OAAL,CAAavG,IAAb,EAAmB,KAAKrE,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AACH,aAJD,MAIO;AACH,mBAAKuE,iBAAL,CAAuBF,IAAvB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIwG,QAAAA,aAAa,GAAG;AACZ,eAAK9K,OAAL,CAAasB,OAAb,CAAsBsJ,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmB,KAAK5K,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACIuE,QAAAA,iBAAiB,CAACF,IAAD,EAAO;AACpB,gBAAMO,QAAQ,GAAG,IAAIxH,IAAJ,CAAS,MAAT,CAAjB;AACAwH,UAAAA,QAAQ,CAACC,MAAT,GAAkB,KAAKjC,IAAvB;AAEA,gBAAM+H,IAAI,GAAG/F,QAAQ,CAACjE,YAAT;AAAA;AAAA,uCAAb;AACAgK,UAAAA,IAAI,CAACC,OAAL,CAAavG,IAAb,EAAmB,KAAKrE,WAAxB,EAAqC,KAArC,EAA4C,IAA5C;AAEA,eAAKD,OAAL,CAAaqE,IAAb,CAAkBuG,IAAlB;AACA,iBAAOA,IAAP;AACH;AACD;AACJ;AACA;;;AACI3D,QAAAA,SAAS,GAAG,CACR;AACA;AACA;AACA;AACA;AAEA;AACH;;AAxpBgC,O,UAE1B8D,U,GAAa,W;;;;;iBAGH,I;;;;;;;iBAGJ,I;;;;;;;iBAEC,I;;;;;;;iBAGO,I;;;;;;;iBAGG,I;;;;;;;iBAGH,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, v2, SpriteFrame, size, SpriteAtlas } from \"cc\";\r\nimport Plane from \"../Plane\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { MainData } from \"../../../data/MainData\";\r\nimport GameEnum from \"../../../const/GameEnum\";\r\nimport BattleLayer from \"../../layer/BattleLayer\";\r\nimport GameMapRun from \"../../map/GameMapRun\";\r\nimport { GameFunc } from \"../../../GameFunc\";\r\nimport FireShells from \"./FireShells\";\r\nimport Bullet from \"../../bullet/Bullet\";\r\nimport EnemyEntity from \"../enemy/EnemyEntity\";\r\nimport BossUnit from \"../boss/BossUnit\";\r\nimport EffectLayer from \"../../layer/EffectLayer\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\nimport { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport { MyApp } from \"db://assets/scripts/MyApp\";\r\nimport GameResourceList from \"../../../const/GameResourceList\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n/**\r\n * 动画状态枚举\r\n */\r\nenum AnimState {\r\n    idle = 0,             // 空闲状态\r\n    crazy = 1,            // 疯狂状态\r\n    willCancelCrazy = 2   // 即将取消疯狂状态\r\n}\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends Plane {\r\n\r\n    static PrefabName = \"MainPlane\";\r\n\r\n    @property(Sprite)\r\n    streak: Sprite = null;\r\n\r\n    @property(Node)\r\n    skin: Node = null;\r\n    @property(Node)\r\n    hpBar: Node = null;\r\n\r\n    @property(sp.Skeleton)\r\n    blast: sp.Skeleton = null;\r\n\r\n    @property(Animation)\r\n    attspeAnim: Animation = null;\r\n\r\n    @property(Animation)\r\n    scrAnim: Animation = null;\r\n\r\n    @property(Animation)\r\n    auxAnim: Animation = null;\r\n\r\n    // 血条相关\r\n    hpbarBack = null; // 血条背景\r\n    hpbarMid = null; // 血条中间部分\r\n    hpbarFont = null; // 血条前景\r\n    hpfont = null; // 血量文字\r\n\r\n    hpMidActin = null; // 血条动画\r\n\r\n    m_fireAnim: Node[] = []; // 射击动画数组\r\n\r\n    // // 飞机状态\r\n    m_screenDatas = []; // 屏幕数据\r\n    m_moveEnable = true; // 是否允许移动\r\n    m_config; // 飞机配置\r\n    m_collideComp: FBoxCollider = null; // 碰撞组件\r\n    mainData: MainData;\r\n    m_fires = []; // 射击点数组\r\n\r\n    // // 飞机状态\r\n    m_fireState = null; // 射击状态\r\n\r\n    m_animState: AnimState = AnimState.idle; // 动画状态\r\n\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n    /**\r\n     * 生命周期方法：onLoad\r\n     * 初始化主飞机的配置和数据\r\n     */\r\n    onLoad() {\r\n        this.m_config = GameIns.mainPlaneManager._mainConfig; // 获取主飞机的配置记录\r\n        this.mainData = GameIns.mainPlaneManager.mainData; // 获取主飞机的数据\r\n\r\n\r\n        this.hpbarBack = this.hpBar.getChildByName(\"hpbarback\").getComponent(Sprite);\r\n        this.hpbarMid = this.hpBar.getChildByName(\"hpbarmid\").getComponent(Sprite);\r\n        this.hpbarFont = this.hpBar.getChildByName(\"hpbarfont\").getComponent(Sprite);\r\n        this.hpfont = this.hpBar.getChildByName(\"hpfont\").getComponent(Label);\r\n\r\n        this.m_collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.m_collideComp.init(this,size(40,40)); // 初始化碰撞组件\r\n        this.m_collideComp.groupType = ColliderGroupType.PLAYER;\r\n    }\r\n\r\n    /**\r\n     * 生命周期方法：start\r\n     * 初始化主飞机的组件和状态\r\n     */\r\n    start() {\r\n        this.enemy = false;\r\n        GameIns.gameDataManager.battlePlaneActive = true;\r\n\r\n        this.m_collideComp.isEnable = true;\r\n\r\n\r\n        // 初始化动画状态\r\n        this.m_animState = AnimState.idle;\r\n\r\n        // 初始化所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n\r\n        // 初始化飞机\r\n        this.initPlane();\r\n\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n    }\r\n\r\n    update(dt) {\r\n        if (!GameConst.GameAble) return;\r\n\r\n        // 限制帧率\r\n        if (dt > 0.2) dt = 0.016666666666667;\r\n\r\n        // 游戏状态为战斗时更新逻辑\r\n        if (\r\n            GameIns.gameRuleManager.gameState === GameEnum.GameState.Battle &&\r\n            GameIns.mainPlaneManager.fireEnable\r\n        ) {\r\n            // 更新所有组件\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(dt);\r\n            });\r\n        }\r\n        // 更新受伤动画时间\r\n        this._hurtActTime += dt;\r\n    }\r\n\r\n    /**\r\n     * 初始化主飞机\r\n     */\r\n    async initPlane() {\r\n        // 加载飞机资源\r\n        await MyApp.resMgr.loadAsync(GameResourceList.atlas_mainPlane + this.m_config.type,SpriteAtlas);\r\n\r\n        // 添加火焰动画\r\n        await this.addFireAnim();\r\n\r\n        // 获取火力状态\r\n        this.getFireState();\r\n\r\n        // 根据屏幕等级更改屏幕数据\r\n        this.changeScreenLv(this.mainData.screenLv);\r\n\r\n        // 初始化图片\r\n        this.initPic();\r\n    }\r\n\r\n    /**\r\n * 初始化变形图片\r\n */\r\n    async initPic() {\r\n        const attspe = this.attspeAnim.node.getChildByName(\"attspe\");\r\n        const atlas = await MyApp.resMgr.loadAsync(GameResourceList.atlas_mainPlane + this.m_config.type, SpriteAtlas);\r\n        attspe.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[1]}1`);\r\n\r\n\r\n        const position = this.m_config.transExt[0].split(\",\");\r\n        attspe.setPosition(Number(position[0]), Number(position[1]))\r\n\r\n        this.scrAnim.node.children.forEach(async (child) => {\r\n            const scr = child.getChildByName(\"scr\");\r\n            scr.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[2]}1`);\r\n            const pos = this.m_config.transExt[1].split(\",\");\r\n            child.setPosition(Number(pos[0]), Number(pos[1]))\r\n        });\r\n\r\n        this.auxAnim.node.children.forEach(async (child) => {\r\n            const aux = child.getChildByName(\"aux\");\r\n            aux.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[3]}1`);\r\n            const pos = this.m_config.transExt[2].split(\",\");\r\n            child.setPosition(Number(pos[0]), Number(pos[1]))\r\n        });\r\n    }\r\n\r\n    /**\r\n * 改变屏幕等级\r\n * @param {number} level 屏幕等级\r\n */\r\n    changeScreenLv(level) {\r\n        if (level === 0) return;\r\n\r\n        this.m_screenDatas = [];\r\n        const attackKey = \"shiftingatk\";\r\n        const attackData = this.m_config[`${attackKey}${level}`];\r\n\r\n        for (let i = 0; i < attackData.length; i += 8) {\r\n            const screenData = attackData.slice(i, i + 8);\r\n            this.m_screenDatas.push(screenData);\r\n        }\r\n\r\n\r\n        this.m_screenDatas.forEach((data, index) => {\r\n            if (this.m_fires[index] == null) {\r\n                this.createAttackPoint(data);\r\n            } else {\r\n                this.changeScreen(index, data);\r\n            }\r\n        });\r\n\r\n        for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {\r\n            this.changeScreen(i, null);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加发射火焰动画\r\n     */\r\n    async addFireAnim() {\r\n        for (let i = 0; i < this.m_config.zjdmtxzb.length; i += 2) {\r\n            const x = this.m_config.zjdmtxzb[i];\r\n            const y = this.m_config.zjdmtxzb[i + 1];\r\n\r\n            const fireNode = new Node(\"fireNode\");\r\n            fireNode.addComponent(UITransform);\r\n            fireNode.addComponent(UIOpacity)\r\n            fireNode.setPosition(Number(x), Number(y));\r\n            fireNode.parent = this.skin;\r\n            this.m_fireAnim.push(fireNode);\r\n            fireNode.getComponent(UIOpacity).opacity = 0;\r\n\r\n            const skeletonData = await MyApp.resMgr.loadAsync(GameResourceList.spine_mainfire,sp.SkeletonData);\r\n            const skeleton = fireNode.addComponent(sp.Skeleton);\r\n            skeleton.skeletonData = skeletonData;\r\n            skeleton.setAnimation(0, \"play\", true);\r\n        }\r\n    }\r\n\r\n    initBattle() {\r\n        this.node.active = true;\r\n        // 获取火力状态\r\n        this.getFireState();\r\n\r\n        this.m_collideComp.isEnable = false;\r\n        this.UpdateHp();\r\n    }\r\n\r\n    /**\r\n * 主飞机入场动画\r\n */\r\n    planeIn(): void {\r\n        const self = this;\r\n\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        // 播放入场音效\r\n        // frameWork.audioManager.playEffect(\"planeFristIn\");\r\n\r\n        // 设置初始位置和状态\r\n        this.node.getComponent(UIOpacity).opacity = 255;\r\n        this.node.parent = BattleLayer.me.selfPlaneLayer;\r\n        this.skin.getComponent(UIOpacity).opacity = 255;\r\n        this.skin.setScale(1, 1)\r\n        let posY = -view.getVisibleSize().height - (this.m_config.type === 711 ? 1000 : 80);\r\n        this.node.setPosition(0, posY)\r\n        this.node.setScale(GameIns.battleManager.getRatio(), GameIns.battleManager.getRatio());\r\n        this.stopFire();\r\n        this.m_moveEnable = false;\r\n        Tween.stopAllByTarget(this.node)\r\n\r\n        // 地图加速 TODO\r\n\r\n        // 飞机入场动画\r\n        this.scheduleOnce(() => {\r\n            const targetY = -view.getVisibleSize().height * 0.7;\r\n            const targetX = this.node.position.x;\r\n            tween(this.node)\r\n                .to(20 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY - 17) })\r\n                .to(11 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 57) })\r\n                .to(10 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 76) })\r\n                .to(27 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY) })\r\n                .call(() => {\r\n                    self.begine();\r\n                })\r\n                .start();\r\n\r\n            tween(this.skin)\r\n                .to(20 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.9, 1.9) })\r\n                .to(11 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.4, 1.4) })\r\n                .to(10 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                .to(27 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                .start();\r\n\r\n            if (this.hpBar) {\r\n                tween(this.hpBar.getComponent(UIOpacity))\r\n                    .to(0, { opacity: 0 })\r\n                    .delay(31 * frameTime / GameIns.battleManager.animSpeed)\r\n                    .to(10 * frameTime / GameIns.battleManager.animSpeed, { opacity: 255 })\r\n                    .start();\r\n            }\r\n\r\n            this.scheduleOnce(() => {\r\n                tween(this.streak.node)\r\n                    .to(9 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 4) })\r\n                    .to(7 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 2) })\r\n                    .to(5 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })\r\n                    .call(() => {\r\n                        self.addStreak();\r\n                    })\r\n                    .start();\r\n            }, 2 * frameTime / GameIns.battleManager.animSpeed);\r\n        }, 7 * frameTime / GameIns.battleManager.animSpeed);\r\n    }\r\n\r\n    /**\r\n     * 退出战斗\r\n     */\r\n    battleQuit() {\r\n        this.mainData.die = false;\r\n        // GameIns.gameDataManager.battlePlaneActive = true;\r\n\r\n        // 重置飞机状态\r\n        this.skin.getComponent(UIOpacity).opacity = 255;\r\n        // this.suitEffect.active = false;\r\n        // if (this.downSuitCall) this.downSuitCall();\r\n        // GameIns.mainPlaneManager.hideSkillNode();\r\n        // this.quitGameSetPic();\r\n        // this.clearMechaOverPic(true);\r\n\r\n        // 重置动画节点位置\r\n        // this.skinAnim.node.y = this.initPosSkinAnim;\r\n        // this.mechaAnimNode.y = this.initPosMechaAnim;\r\n        // this.suitAnimBot.y = this.initPosSuitBot;\r\n        // this.suitAnimTop.y = this.initPosSuitTop;\r\n    }\r\n\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage) {\r\n        GameIns.mainPlaneManager.hurtTotal += damage;\r\n        const newHp = GameIns.mainPlaneManager.mainData.hp - damage;\r\n        GameIns.mainPlaneManager.mainData.hp = Math.max(0, newHp);\r\n\r\n        if (newHp < 0) {\r\n            GameIns.mainPlaneManager.hurtTotal += newHp;\r\n        }\r\n\r\n        this.UpdateHp();;\r\n\r\n        if (GameIns.mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 增加血量\r\n     * @param {number} heal 恢复的血量值\r\n     */\r\n    addHp(heal) {\r\n        GameIns.mainPlaneManager.mainData.hp = Math.min(\r\n            GameIns.mainPlaneManager.mainData.maxhp,\r\n            GameIns.mainPlaneManager.mainData.hp + heal\r\n        );\r\n        this.UpdateHp();;\r\n    }\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision) {\r\n        // if (this.m_skill && this.m_skill.invincible) return;\r\n\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            damage = collision.entity.getAttack(this);\r\n        } else if (\r\n            collision.entity instanceof EnemyEntity ||\r\n            collision.entity instanceof BossUnit\r\n        ) {\r\n            damage = collision.entity.getColliderAtk();\r\n        }\r\n\r\n        if (damage > 0) {\r\n            this.cutHp(damage);\r\n            this._playHurtAnim();\r\n            if (GameIns.mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {\r\n                this.toDie();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _playHurtAnim() {\r\n        if (this._hurtActTime > this._hurtActDuration) {\r\n            this._hurtActTime = 0;\r\n            // 显示红屏效果\r\n            EffectLayer.me.showRedScreen();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} moveY 垂直方向的移动量\r\n     */\r\n    onControl(posX, posY) {\r\n        if (!this.mainData.die && this.m_moveEnable) {\r\n            // 限制飞机移动范围\r\n            posX = Math.min(360, posX);\r\n            posX = Math.max(-360, posX);\r\n            posY = Math.min(0, posY);\r\n            posY = Math.max(-GameConst.ViewHeight, posY);\r\n            this.node.setPosition(posX, posY);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    UpdateHp() {\r\n        if (this.hpBar && this.hpbarFont && this.hpbarMid) {\r\n            // 停止当前血条动画\r\n            if (this.hpMidActin) {\r\n                this.hpMidActin.stop();\r\n                this.hpMidActin = null;\r\n            }\r\n\r\n            // 更新血条前景的填充范围\r\n            this.hpbarFont.fillRange = GameIns.mainPlaneManager.mainData.hp / GameIns.mainPlaneManager.mainData.maxhp;\r\n\r\n            // 计算血条动画时间\r\n            const duration = Math.abs(this.hpbarMid.fillRange - this.hpbarFont.fillRange);\r\n\r\n            // 血条中间部分的动画\r\n            this.hpMidActin = tween(this.hpbarMid)\r\n                .to(duration, { fillRange: this.hpbarFont.fillRange })\r\n                .call(() => {\r\n                    this.hpMidActin = null;\r\n                })\r\n                .start();\r\n\r\n            // 更新血量文字\r\n            this.hpfont.string = GameIns.mainPlaneManager.mainData.hp.toFixed(0);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理复活逻辑\r\n     */\r\n    onRelife() {\r\n        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数\r\n        this.relife(1); // 调用复活方法\r\n    }\r\n\r\n    /**\r\n     * 执行复活\r\n     * @param {number} reviveType 复活类型\r\n     */\r\n    relife(reviveType) {\r\n\r\n        // this.playRelifeAim(); // 播放复活动画\r\n        this.mainData.die = false; // 设置飞机为非死亡状态\r\n        this.mainData.revive = true; // 设置复活状态\r\n        this.scheduleOnce(() => {\r\n            this.mainData.revive = false;\r\n        }, 0.5);\r\n\r\n        GameIns.gameDataManager.battlePlaneActive = true; // 激活主飞机\r\n        GameIns.mainPlaneManager.mainData.hp = GameIns.mainPlaneManager.mainData.maxhp; // 恢复满血\r\n        this.UpdateHp();; // 触发血量更新事件\r\n    }\r\n    /**\r\n     * 获取火力状态\r\n     */\r\n    getFireState() {\r\n        const atkChallenge = this.mainData.atkAddRatio;\r\n\r\n        this.m_fireState = {\r\n            attack: this.mainData.attack,\r\n            attackLv: 0,\r\n            hpAttackLv: 0,\r\n            speedLv: 0,\r\n            cirtLv: 0,//cirtLevel,\r\n            catapultLv: 0,//SkillManager.me.getLv(SkillType.catapult),\r\n            atkChallenge: atkChallenge,\r\n            fireIntensify: this.mainData.intensifyAtk,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 获取攻击力\r\n     * @returns {number} 当前攻击力\r\n     */\r\n    getAttack() {\r\n        return this.m_fireState.attack;\r\n    }\r\n\r\n    toDie() {\r\n\r\n        // 设置玩家状态为死亡\r\n        this.mainData.die = true;\r\n\r\n        // 禁用碰撞组件\r\n        this.m_collideComp.isEnable = false;\r\n\r\n        // 停止血条动画并设置血条为 0\r\n        if (this.hpbarMid) {\r\n            tween(this.hpbarMid).stop();\r\n            this.hpbarMid.fillRange = 0;\r\n        }\r\n\r\n        // 播放死亡动画\r\n        this._playDieAnim();\r\n\r\n        // 设置战斗状态为不可用\r\n        GameIns.gameDataManager.battlePlaneActive = false;\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    _playDieAnim() {\r\n        this.blast.node.getComponent(UIOpacity).opacity = 255; // 显示爆炸效果\r\n        this.blast.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调\r\n        this.blast.setAnimation(0, \"play\", false); // 播放爆炸动画\r\n    }\r\n\r\n    /**\r\n     * 主飞机死亡动画结束后的处理逻辑\r\n     */\r\n    _dieAnimEnd(): void {\r\n        // 隐藏爆炸动画节点\r\n        this.blast.node.getComponent(UIOpacity).opacity = 0;\r\n\r\n        // // 如果主飞机还有剩余生命次数\r\n        // if (this.mainData.lifeNum > 0) {\r\n        //     // 减少生命次数\r\n        //     this.mainData.lifeNum--;\r\n\r\n        //     // 更新剩余生命次数到全局数据\r\n        //     // GameIns.gameDataManager.setLifeNum(this.mainData.lifeNum);\r\n\r\n        //     // 触发复活逻辑\r\n        //     this.relife(0);\r\n        // } else {\r\n        // 如果没有剩余生命次数，检查是否可以复活\r\n        // const reviveCount = GameIns.gameDataManager.reviveCount;\r\n        // if (this.mainData.relifeNum - reviveCount <= 0) {\r\n        // // 如果是远征模式，结束当前章节\r\n        // if (ExpeditionManager.isExpedition) {\r\n        //     ExpeditionManager.sectionOver(false);\r\n        // } else {\r\n        //     // 否则触发战斗失败逻辑\r\n        GameIns.battleManager.battleFail();\r\n        // }\r\n        // } else {\r\n        //     // 如果可以复活，触发战斗死亡逻辑\r\n        //     GameIns.battleManager.battleDie();\r\n        // }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 启用或禁用火力\r\n     * @param {boolean} enable 是否启用火力\r\n     */\r\n    setFireEnable(enable) {\r\n        if (this.m_config && this.m_config.type === 710) return;\r\n\r\n        if (enable) {\r\n            this.m_fireAnim.forEach((anim) => {\r\n                anim.getComponent(UIOpacity).opacity = 255; // 显示火力动画\r\n            });\r\n        } else {\r\n            this.m_fireAnim.forEach((anim) => {\r\n                anim.getComponent(UIOpacity).opacity = 0; // 隐藏火力动画\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置飞机是否可移动\r\n     * @param {boolean} enable 是否可移动\r\n     */\r\n    setMoveAble(enable) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    /**\r\n     * 设置碰撞是否可用\r\n     * @param {boolean} enable 是否启用碰撞\r\n     */\r\n    setColAble(enable) {\r\n        this.m_collideComp.isEnable = enable;\r\n    }\r\n\r\n    /**\r\n     * 开始射击\r\n     */\r\n    beginFire() {\r\n        GameIns.mainPlaneManager.fireEnable = true;\r\n    }\r\n\r\n    /**\r\n     * 停止射击\r\n     */\r\n    stopFire() {\r\n        GameIns.mainPlaneManager.fireEnable = false;\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     * @param {boolean} isContinue 是否继续战斗\r\n     */\r\n    begine(isContinue = false) {\r\n        if (isContinue) {\r\n            this.beginFire();\r\n            this.m_moveEnable = true;\r\n            if (this.m_collideComp) {\r\n                this.m_collideComp.isEnable = true;\r\n            }\r\n        } else {\r\n            GameIns.battleManager.onPlaneIn();\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 改变屏幕上的火力点\r\n     * @param {number} index 火力点索引\r\n     * @param {Array|null} data 火力点数据\r\n     */\r\n    changeScreen(index, data) {\r\n        if (data == null) {\r\n            if (index < this.m_fires.length) {\r\n                const fire = this.m_fires[index];\r\n                fire.setData(null, this.m_fireState, false, this);\r\n                fire.node.active = false;\r\n            }\r\n        } else {\r\n            if (index < this.m_fires.length) {\r\n                const fire = this.m_fires[index];\r\n                fire.node.active = true;\r\n                fire.setData(data, this.m_fireState, false, this);\r\n            } else {\r\n                this.createAttackPoint(data);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除所有火力点\r\n     */\r\n    removeAllFire() {\r\n        this.m_fires.forEach((fire) => {\r\n            fire.setData(null, this.m_fireState, false, this);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 创建攻击点\r\n     * @param {Array} data 攻击点数据\r\n     * @returns {FireShells} 创建的攻击点\r\n     */\r\n    createAttackPoint(data) {\r\n        const fireNode = new Node(\"fire\");\r\n        fireNode.parent = this.node;\r\n\r\n        const fire = fireNode.addComponent(FireShells);\r\n        fire.setData(data, this.m_fireState, false, this);\r\n\r\n        this.m_fires.push(fire);\r\n        return fire;\r\n    }\r\n    /**\r\n     * 添加拖尾效果\r\n     */\r\n    addStreak() {\r\n        // Tween.stopAllByTarget(this.streak.node)\r\n        // const ani = new Tween(this.streak.node)\r\n        //     .to(1, { opacity: 255, scale: v3(1 })\r\n        //     .to(3, { opacity: 204, scale: 0.88 })\r\n        //     .to(6, { opacity: 255, scale: 1 });\r\n\r\n        // tween(this.streak.node).repeatForever(ani).start();\r\n    }\r\n}"]}