System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, UITransform, DataMgr, EventMgr, logDebug, List, UIMgr, PlaneEquipInfoUI, PlaneUIEvent, OpenEquipInfoUISource, TabStatus, BagItem, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, BagGrid;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/scripts/Data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/scripts/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../../../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneEquipInfoUI(extras) {
    _reporterNs.report("PlaneEquipInfoUI", "../../PlaneEquipInfoUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../PlaneEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfOpenEquipInfoUISource(extras) {
    _reporterNs.report("OpenEquipInfoUISource", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBagItem(extras) {
    _reporterNs.report("BagItem", "./BagItem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      logDebug = _unresolved_4.logDebug;
    }, function (_unresolved_5) {
      List = _unresolved_5.default;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      PlaneEquipInfoUI = _unresolved_7.PlaneEquipInfoUI;
    }, function (_unresolved_8) {
      PlaneUIEvent = _unresolved_8.PlaneUIEvent;
    }, function (_unresolved_9) {
      OpenEquipInfoUISource = _unresolved_9.OpenEquipInfoUISource;
      TabStatus = _unresolved_9.TabStatus;
    }, function (_unresolved_10) {
      BagItem = _unresolved_10.BagItem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1ce0bH/NfdBLL19SPqwUpSI", "BagGrid", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BagGrid", BagGrid = (_dec = ccclass('BagGrid'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class BagGrid extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bagList", _descriptor, this);

          _initializerDefineProperty(this, "separator", _descriptor2, this);

          _initializerDefineProperty(this, "mergeSelectMaskBg", _descriptor3, this);

          this._sortedItems = [];
          this._sortedPlaneParts = [];
          this._lineGridNum = 5;
          this._separatorRow = 0;
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
        }

        onLoad() {
          this.separator.removeFromParent();
          this.mergeSelectMaskBg.active = false;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).SortTypeChange, this.onSortTypeChange, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).BagItemClick, this.onBagItemClick, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onUpdateMergeEquipStatus() {
          this.bagList.updateAll();
        }
        /*暂时只有装备点击*/


        onBagItemClick(item) {
          switch (this._tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              this.mergeSelectMaskBg.active = false;
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && PlaneEquipInfoUI === void 0 ? (_reportPossibleCrUseOfPlaneEquipInfoUI({
                error: Error()
              }), PlaneEquipInfoUI) : PlaneEquipInfoUI, item, (_crd && OpenEquipInfoUISource === void 0 ? (_reportPossibleCrUseOfOpenEquipInfoUISource({
                error: Error()
              }), OpenEquipInfoUISource) : OpenEquipInfoUISource).BagGrid);
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              const isEmpty = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.size() == 0;

              if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isFull() && !(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.getByGuid(item.guid) && isEmpty && !(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isMainMat(item.item_id) && !isEmpty && (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isCanCombineWith(item)) {
                return;
              }

              this.onUpdateMergeEquipStatus();
              break;
          }
        }

        onSortTypeChange(tabStatus, equips, items) {
          this._tabStatus = tabStatus;
          this.mergeSelectMaskBg.active = false;
          this.separator.active = false;
          this.separator.removeFromParent();
          this.bagList._customSize = {};

          this.bagList._resizeContent();

          switch (tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              this._sortedItems = items;
              this._sortedPlaneParts = equips;
              this._separatorRow = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum);
              const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum);
              this.bagList.numItems = this._separatorRow + itemRowNum + 1;
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              this._separatorRow = -1;
              this._sortedPlaneParts = equips;
              this.bagList.numItems = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum);
              break;
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", `onSortTypeChange list num:${this.bagList.numItems} maxPlanePartRowNum:${this._separatorRow}`);
          this.bagList.scrollTo(0, 1);
        }

        onListRenderInBagStatus(listItem, row) {
          listItem.name = `${row}`;

          if (row == this._separatorRow) {
            const normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize;
            const itemUITrans = listItem.getComponent(UITransform);
            listItem.children.forEach(v => v.active = false);
            this.separator.removeFromParent();
            this.separator.active = true;
            listItem.addChild(this.separator);
            itemUITrans.setContentSize(normalSize.width, normalSize.height / 2);
            return;
          }

          if (listItem.children.length > 5) {
            this.separator.removeFromParent();
            this.separator.active = false;
          }

          const bagItems = listItem.getComponentsInChildren(_crd && BagItem === void 0 ? (_reportPossibleCrUseOfBagItem({
            error: Error()
          }), BagItem) : BagItem);

          if (row < this._separatorRow) {
            for (let index = 0; index < bagItems.length; index++) {
              const item = bagItems[index];
              const dataIndex = row * this._lineGridNum + index;

              if (dataIndex >= this._sortedPlaneParts.length) {
                item.node.active = false;
                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)("PlaneUI", `onListRender bagItem index:${index} dataIndex:${dataIndex} row:${row} sortedLen:${this._sortedPlaneParts.length}`);
                continue;
              }

              item.node.active = true;
              item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);
            }
          } else {
            for (let index = 0; index < bagItems.length; index++) {
              const item = bagItems[index];
              const dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + index;

              if (dataIndex >= this._sortedItems.length) {
                item.node.active = false;
                continue;
              }

              item.node.active = true;
              item.onRenderItem(this._sortedItems[dataIndex]);
            }
          }
        }

        onListRenderInMergeStatus(listItem, row) {
          const bagItems = listItem.getComponentsInChildren(_crd && BagItem === void 0 ? (_reportPossibleCrUseOfBagItem({
            error: Error()
          }), BagItem) : BagItem);

          for (let index = 0; index < bagItems.length; index++) {
            const item = bagItems[index];
            const dataIndex = row * this._lineGridNum + index;

            if (dataIndex >= this._sortedPlaneParts.length) {
              item.node.active = false;
              continue;
            }

            item.node.active = true;
            item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);
          }
        }

        onListRender(listItem, row) {
          listItem.name = `listItem${row}`;

          if (this._tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag) {
            this.onListRenderInBagStatus(listItem, row);
          } else {
            this.onListRenderInMergeStatus(listItem, row);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bagList", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "separator", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "mergeSelectMaskBg", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b25bebc8e31b603d5682b297bfda0c8fa52f943f.js.map