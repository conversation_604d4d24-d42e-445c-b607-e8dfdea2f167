{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EventGroupData.ts"], "names": ["_decorator", "Enum", "EmitterConditionData", "BulletConditionData", "EmitterActionData", "BulletActionData", "ccclass", "property", "eConditionGroupOp", "EmitterGroupData", "displayName", "type", "And", "BulletGroupData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqCC,MAAAA,I,OAAAA,I;;AACrCC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,mB,iBAAAA,mB;;AACtBC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACtB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;mCAElBQ,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;AAIZ;AACA;AACA;AACA;;;kCAEaC,gB,WADZH,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI;AAAA;AAAA,wDAAN;AAA8BD,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,UAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEV,IAAI,CAACO,iBAAD,CAAZ;AAAiCE,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI;AAAA;AAAA,wDAAN;AAA8BD,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,UAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAE;AAAA;AAAA,mDAAR;AAA6BD,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,2BAjBb,MACaD,gBADb,CAC8B;AAAA;AAAA;;AAAA;;AAAA;;AAQ2B;AAR3B;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,E;;;;;;;;;;;;iBAMcD,iBAAiB,CAACI,G;;;;;;;;;;;;iBAM3B,K;;;;;;;iBAGW,E;;;;iCAIvBC,e,YADZP,OAAO,CAAC,iBAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI;AAAA;AAAA,sDAAN;AAA6BD,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEV,IAAI,CAACO,iBAAD,CAAZ;AAAiCE,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,WAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI;AAAA;AAAA,sDAAN;AAA6BD,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAGRH,QAAQ,CAAC;AAAEG,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRH,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAE;AAAA;AAAA,iDAAR;AAA4BD,QAAAA,WAAW,EAAE;AAAzC,OAAD,C,6BAjBb,MACaG,eADb,CAC6B;AAAA;AAAA;;AAAA;;AAAA;;AAQ4B;AAR5B;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAET,E;;;;;;;;;;;;iBAMcL,iBAAiB,CAACI,G;;;;;;;;;;;;iBAM3B,K;;;;;;;iBAGU,E", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nimport { EmitterConditionData, BulletConditionData } from \"./EventConditionData\";\r\nimport { EmitterActionData, BulletActionData } from \"./EventActionData\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eConditionGroupOp {\r\n    And, Or\r\n}\r\n\r\n/**\r\n * 发射器事件数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"EmitterGroupData\")\r\nexport class EmitterGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    name : string = '';\r\n\r\n    @property({ type: EmitterConditionData, displayName: '条件A' })\r\n    conditionA : EmitterConditionData;\r\n\r\n    @property({ type: Enum(eConditionGroupOp), displayName: '条件组合操作' })\r\n    groupOp : eConditionGroupOp = eConditionGroupOp.And; // 条件组合操作: 与/或\r\n\r\n    @property({ type: EmitterConditionData, displayName: '条件B' })\r\n    conditionB : EmitterConditionData;\r\n\r\n    @property({ displayName: '是否重复触发' })\r\n    isRepeat : boolean = false;\r\n\r\n    @property({ type: [EmitterActionData], displayName: '行为列表' })\r\n    actions : EmitterActionData[] = [];\r\n}\r\n\r\n@ccclass(\"BulletGroupData\")\r\nexport class BulletGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    name : string = '';\r\n\r\n    @property({ type: BulletConditionData, displayName: '条件A' })\r\n    conditionA : BulletConditionData;\r\n\r\n    @property({ type: Enum(eConditionGroupOp), displayName: '条件组合操作' })\r\n    groupOp : eConditionGroupOp = eConditionGroupOp.And; // 条件组合操作: 与/或\r\n\r\n    @property({ type: BulletConditionData, displayName: '条件B' })\r\n    conditionB : BulletConditionData;\r\n\r\n    @property({ displayName: '是否重复触发' })\r\n    isRepeat : boolean = false;\r\n\r\n    @property({ type: [BulletActionData], displayName: '行为列表' })\r\n    actions : BulletActionData[] = [];\r\n}"]}