System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, EmitterActionMapping, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _class5, _class6, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _crd, ccclass, property, eEmitterActionType, eBulletActionType, eEasing, EmitterActionData, BulletActionData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  _export("EmitterActionMapping", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b4e34ymEeFIco+5AaUxrSlS", "EventActionData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);
      /**
       * ActionType对应要修改的属性
       * 以下是发射器的行为
       */

      _export("eEmitterActionType", eEmitterActionType = /*#__PURE__*/function (eEmitterActionType) {
        eEmitterActionType[eEmitterActionType["Emitter_Active"] = 1] = "Emitter_Active";
        eEmitterActionType[eEmitterActionType["Emitter_InitialDelay"] = 2] = "Emitter_InitialDelay";
        eEmitterActionType[eEmitterActionType["Emitter_Prewarm"] = 3] = "Emitter_Prewarm";
        eEmitterActionType[eEmitterActionType["Emitter_PrewarmDuration"] = 4] = "Emitter_PrewarmDuration";
        eEmitterActionType[eEmitterActionType["Emitter_Duration"] = 5] = "Emitter_Duration";
        eEmitterActionType[eEmitterActionType["Emitter_ElapsedTime"] = 6] = "Emitter_ElapsedTime";
        eEmitterActionType[eEmitterActionType["Emitter_Loop"] = 7] = "Emitter_Loop";
        eEmitterActionType[eEmitterActionType["Emitter_LoopInterval"] = 8] = "Emitter_LoopInterval";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitInterval"] = 9] = "Emitter_PerEmitInterval";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitCount"] = 10] = "Emitter_PerEmitCount";
        eEmitterActionType[eEmitterActionType["Emitter_PerEmitOffsetX"] = 11] = "Emitter_PerEmitOffsetX";
        eEmitterActionType[eEmitterActionType["Emitter_Angle"] = 12] = "Emitter_Angle";
        eEmitterActionType[eEmitterActionType["Emitter_Count"] = 13] = "Emitter_Count";
        eEmitterActionType[eEmitterActionType["Bullet_Duration"] = 14] = "Bullet_Duration";
        eEmitterActionType[eEmitterActionType["Bullet_ElapsedTime"] = 15] = "Bullet_ElapsedTime";
        eEmitterActionType[eEmitterActionType["Bullet_PosX"] = 16] = "Bullet_PosX";
        eEmitterActionType[eEmitterActionType["Bullet_PosY"] = 17] = "Bullet_PosY";
        eEmitterActionType[eEmitterActionType["Bullet_Damage"] = 18] = "Bullet_Damage";
        eEmitterActionType[eEmitterActionType["Bullet_Speed"] = 19] = "Bullet_Speed";
        eEmitterActionType[eEmitterActionType["Bullet_SpeedAngle"] = 20] = "Bullet_SpeedAngle";
        eEmitterActionType[eEmitterActionType["Bullet_Acceleration"] = 21] = "Bullet_Acceleration";
        eEmitterActionType[eEmitterActionType["Bullet_AccelerationAngle"] = 22] = "Bullet_AccelerationAngle";
        eEmitterActionType[eEmitterActionType["Bullet_Scale"] = 23] = "Bullet_Scale";
        eEmitterActionType[eEmitterActionType["Bullet_ColorR"] = 24] = "Bullet_ColorR";
        eEmitterActionType[eEmitterActionType["Bullet_ColorG"] = 25] = "Bullet_ColorG";
        eEmitterActionType[eEmitterActionType["Bullet_ColorB"] = 26] = "Bullet_ColorB";
        eEmitterActionType[eEmitterActionType["Bullet_ColorA"] = 27] = "Bullet_ColorA";
        eEmitterActionType[eEmitterActionType["Bullet_FaceMovingDir"] = 28] = "Bullet_FaceMovingDir";
        eEmitterActionType[eEmitterActionType["Bullet_TrackingTarget"] = 29] = "Bullet_TrackingTarget";
        eEmitterActionType[eEmitterActionType["Bullet_Destructive"] = 30] = "Bullet_Destructive";
        eEmitterActionType[eEmitterActionType["Bullet_DestructiveOnHit"] = 31] = "Bullet_DestructiveOnHit";
        eEmitterActionType[eEmitterActionType["Unit_Life"] = 32] = "Unit_Life";
        eEmitterActionType[eEmitterActionType["Unit_LifePercent"] = 33] = "Unit_LifePercent";
        eEmitterActionType[eEmitterActionType["Unit_PosX"] = 34] = "Unit_PosX";
        eEmitterActionType[eEmitterActionType["Unit_PosY"] = 35] = "Unit_PosY";
        eEmitterActionType[eEmitterActionType["Unit_Speed"] = 36] = "Unit_Speed";
        eEmitterActionType[eEmitterActionType["Unit_SpeedAngle"] = 37] = "Unit_SpeedAngle";
        eEmitterActionType[eEmitterActionType["Unit_Acceleration"] = 38] = "Unit_Acceleration";
        eEmitterActionType[eEmitterActionType["Unit_AccelerationAngle"] = 39] = "Unit_AccelerationAngle";
        return eEmitterActionType;
      }({})); // const map:[] = {
      //     {eEmitter_Active, "active", ValueType.Boolean },
      // }
      // class PropertyContainer {
      //     [key: string]: any;
      // }
      // class Emitter {
      //     container: PropertyContainer;
      //     init(): void {
      //         this.container = new PropertyContainer();
      //         this.container["active"] = true;
      //     }
      // }

      /**
       * ActionType对应要修改的属性
       * 以下是子弹的行为
       */


      _export("eBulletActionType", eBulletActionType = /*#__PURE__*/function (eBulletActionType) {
        eBulletActionType[eBulletActionType["Bullet_Duration"] = 100] = "Bullet_Duration";
        eBulletActionType[eBulletActionType["Bullet_ElapsedTime"] = 101] = "Bullet_ElapsedTime";
        eBulletActionType[eBulletActionType["Bullet_PosX"] = 102] = "Bullet_PosX";
        eBulletActionType[eBulletActionType["Bullet_PosY"] = 103] = "Bullet_PosY";
        eBulletActionType[eBulletActionType["Bullet_Damage"] = 104] = "Bullet_Damage";
        eBulletActionType[eBulletActionType["Bullet_Speed"] = 105] = "Bullet_Speed";
        eBulletActionType[eBulletActionType["Bullet_SpeedAngle"] = 106] = "Bullet_SpeedAngle";
        eBulletActionType[eBulletActionType["Bullet_Acceleration"] = 107] = "Bullet_Acceleration";
        eBulletActionType[eBulletActionType["Bullet_AccelerationAngle"] = 108] = "Bullet_AccelerationAngle";
        eBulletActionType[eBulletActionType["Bullet_Scale"] = 109] = "Bullet_Scale";
        eBulletActionType[eBulletActionType["Bullet_ColorR"] = 110] = "Bullet_ColorR";
        eBulletActionType[eBulletActionType["Bullet_ColorG"] = 111] = "Bullet_ColorG";
        eBulletActionType[eBulletActionType["Bullet_ColorB"] = 112] = "Bullet_ColorB";
        eBulletActionType[eBulletActionType["Bullet_ColorA"] = 113] = "Bullet_ColorA";
        eBulletActionType[eBulletActionType["Bullet_FaceMovingDir"] = 114] = "Bullet_FaceMovingDir";
        eBulletActionType[eBulletActionType["Bullet_TrackingTarget"] = 115] = "Bullet_TrackingTarget";
        eBulletActionType[eBulletActionType["Bullet_Destructive"] = 116] = "Bullet_Destructive";
        eBulletActionType[eBulletActionType["Bullet_DestructiveOnHit"] = 117] = "Bullet_DestructiveOnHit";
        return eBulletActionType;
      }({}));

      _export("eEasing", eEasing = /*#__PURE__*/function (eEasing) {
        eEasing[eEasing["Linear"] = 0] = "Linear";
        eEasing[eEasing["InSine"] = 1] = "InSine";
        eEasing[eEasing["OutSine"] = 2] = "OutSine";
        eEasing[eEasing["InOutSine"] = 3] = "InOutSine";
        eEasing[eEasing["InQuad"] = 4] = "InQuad";
        eEasing[eEasing["OutQuad"] = 5] = "OutQuad";
        eEasing[eEasing["InOutQuad"] = 6] = "InOutQuad";
        return eEasing;
      }({}));

      _export("EmitterActionMapping", EmitterActionMapping = class EmitterActionMapping {
        constructor() {
          this.type = void 0;
          this.name = void 0;
        }

      });
      /**
       * 发射器行为数据
       * 所有时间相关的，单位都是秒(s)
       */


      _export("EmitterActionData", EmitterActionData = (_dec = ccclass("EmitterActionData"), _dec2 = property({
        type: Enum(eEmitterActionType),
        displayName: '行为类型'
      }), _dec3 = property({
        displayName: '是否随机'
      }), _dec4 = property({
        displayName: '最小值'
      }), _dec5 = property({
        displayName: '最大值'
      }), _dec6 = property({
        displayName: 'bool值'
      }), _dec7 = property({
        displayName: '持续时间'
      }), _dec8 = property({
        type: Enum(eEasing),
        displayName: '缓动函数'
      }), _dec(_class2 = (_class3 = class EmitterActionData {
        constructor() {
          _initializerDefineProperty(this, "actionType", _descriptor, this);

          _initializerDefineProperty(this, "isRandom", _descriptor2, this);

          _initializerDefineProperty(this, "minValue", _descriptor3, this);

          _initializerDefineProperty(this, "maxValue", _descriptor4, this);

          _initializerDefineProperty(this, "boolValue", _descriptor5, this);

          _initializerDefineProperty(this, "duration", _descriptor6, this);

          // 持续时间: 0表示立即执行
          _initializerDefineProperty(this, "easing", _descriptor7, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "actionType", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "isRandom", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "minValue", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class3.prototype, "maxValue", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class3.prototype, "boolValue", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class3.prototype, "duration", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class3.prototype, "easing", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eEasing.Linear;
        }
      })), _class3)) || _class2));
      /**
       * 子弹行为数据
       * 所有时间相关的，单位都是秒(s)
       */


      _export("BulletActionData", BulletActionData = (_dec9 = ccclass("BulletActionData"), _dec10 = property({
        type: Enum(eBulletActionType),
        displayName: '行为类型'
      }), _dec11 = property({
        displayName: '是否随机'
      }), _dec12 = property({
        displayName: '最小值'
      }), _dec13 = property({
        displayName: '最大值'
      }), _dec14 = property({
        displayName: 'bool值'
      }), _dec15 = property({
        displayName: '持续时间'
      }), _dec16 = property({
        type: Enum(eEasing),
        displayName: '缓动函数'
      }), _dec9(_class5 = (_class6 = class BulletActionData {
        constructor() {
          _initializerDefineProperty(this, "actionType", _descriptor8, this);

          _initializerDefineProperty(this, "isRandom", _descriptor9, this);

          _initializerDefineProperty(this, "minValue", _descriptor10, this);

          _initializerDefineProperty(this, "maxValue", _descriptor11, this);

          _initializerDefineProperty(this, "boolValue", _descriptor12, this);

          _initializerDefineProperty(this, "duration", _descriptor13, this);

          // 持续时间: 0表示立即执行
          _initializerDefineProperty(this, "easing", _descriptor14, this);
        }

      }, (_descriptor8 = _applyDecoratedDescriptor(_class6.prototype, "actionType", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor9 = _applyDecoratedDescriptor(_class6.prototype, "isRandom", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class6.prototype, "minValue", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class6.prototype, "maxValue", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class6.prototype, "boolValue", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class6.prototype, "duration", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class6.prototype, "easing", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eEasing.Linear;
        }
      })), _class6)) || _class5));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=628363ca65ddf4b44954c8e0ba6c625a39047418.js.map