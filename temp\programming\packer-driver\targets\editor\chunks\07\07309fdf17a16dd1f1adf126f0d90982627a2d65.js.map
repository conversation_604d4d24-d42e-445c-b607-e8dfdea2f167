{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts"], "names": ["DataManager", "Bag", "BaseInfo", "Equip", "GameLevel", "GM", "constructor", "baseInfo", "bag", "equip", "gameLevel", "gm", "update", "init", "Instance", "DataMgr", "window"], "mappings": ";;;mEAeaA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfJC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,E,iBAAAA,E;;;;;;;AAIT;AAMA;6BACaL,W,GAAN,MAAMA,WAAN,CAAkB;AAerBM,QAAAA,WAAW,GAAG;AAXd;AAWc,eAVPC,QAUO,GAVc;AAAA;AAAA,qCAUd;AATd;AASc,eARPC,GAQO,GARI;AAAA;AAAA,2BAQJ;AAPd;AAOc,eANPC,KAMO,GANQ;AAAA;AAAA,+BAMR;AALd;AAKc,eAJPC,SAIO,GAJgB;AAAA;AAAA,uCAIhB;AAHd;AAGc,eAFPC,EAEO,GAFE;AAAA;AAAA,yBAEF;AAAG,SAfI,CAgBrB;;;AACOC,QAAAA,MAAM,GAAS;AAClB,eAAKL,QAAL,CAAcK,MAAd;AACA,eAAKJ,GAAL,CAASI,MAAT;AACA,eAAKH,KAAL,CAAWG,MAAX;AACA,eAAKF,SAAL,CAAeE,MAAf;AACH,SAtBoB,CAwBrB;;;AACOC,QAAAA,IAAI,GAAS;AAChB,eAAKN,QAAL,CAAcM,IAAd;AACA,eAAKF,EAAL,CAAQE,IAAR;AACA,eAAKL,GAAL,CAASK,IAAT;AACA,eAAKJ,KAAL,CAAWI,IAAX;AACA,eAAKH,SAAL,CAAeG,IAAf;AACH;;AA/BoB,O;;eAAZb,W;AACT;AADSA,MAAAA,W,CAEcc,Q,GAAwB,IAAId,MAAJ,E;;yBAgCtCe,O,GAAUf,WAAW,CAACc,Q;;AACnCE,MAAAA,MAAM,CAAC,SAAD,CAAN,GAAoBD,OAApB", "sourcesContent": ["import { Bag } from \"./bag/Bag\";\nimport { BaseInfo } from \"./BaseInfo\";\nimport { Equip } from \"./equip/Equip\";\nimport { GameLevel } from \"./GameLevel\";\nimport { GM } from \"./gm/GM\";\n\n\n\n//数据基础类\nexport interface IData {\n    init(): void;\n    update(): void;\n}\n\n// 数据管理类，负责游戏数据的管理和提供\nexport class DataManager {\n    // 单例实例\n    public static readonly Instance: DataManager = new DataManager();\n\n    // 基础信息\n    public baseInfo: BaseInfo = new BaseInfo();\n    // 背包数据\n    public bag: Bag = new Bag();\n    // 装备数据\n    public equip: Equip = new Equip()\n    // 游戏关卡数据\n    public gameLevel: GameLevel = new GameLevel();\n    // gm数据\n    public gm: GM = new GM();\n\n    constructor() { }\n    // 数据更新\n    public update(): void {\n        this.baseInfo.update();\n        this.bag.update();\n        this.equip.update();\n        this.gameLevel.update();\n    }\n\n    // 初始化数据\n    public init(): void {\n        this.baseInfo.init();\n        this.gm.init();\n        this.bag.init();\n        this.equip.init();\n        this.gameLevel.init();\n    }\n}\n\nexport const DataMgr = DataManager.Instance\nwindow[\"DataMgr\"] = DataMgr;"]}