import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
const { ccclass, property } = _decorator;

export enum eEmitterCondition {
    Level_Duration = 1,     // 已持续时间
    Level_Distance,         // 已飞行距离
    Level_InfLevel,         // 无尽模式当前关卡等级
    Level_ChallengeLevel,   // 闯关模式当前等级

    Player_ActLevel,        // 玩家账号等级
    Player_PosX,            // 玩家当前坐标X
    Player_PosY,            // 玩家当前坐标Y
    Player_LifePercent,     // 玩家当前生命百分比
    Player_GainBuff,        // 玩家获得buff

    Unit_Life,              // 单位当前生命值
    Unit_LifePercent,       // 单位当前生命百分比
    Unit_Duration,          // 单位当前持续时间
    Unit_PosX,              // 单位当前坐标X
    Unit_PosY,              // 单位当前坐标Y
    Unit_Speed,             // 单位当前速度
    Unit_SpeedAngle,        // 单位当前速度角度
    Unit_Acceleration,      // 单位当前加速度
    Unit_AccelerationAngle, // 单位当前加速度角度
    Unit_DistanceToPlayer,  // 单位与玩家的距离
    Unit_AngleToPlayer,     // 单位与玩家的角度

    Emitter_Active,         // 发射器是否启用
    Emitter_InitialDelay,   // 发射器当前的初始延迟
    Emitter_Prewarm,        // 发射器是否启用预热
    Emitter_PrewarmDuration, // 发射器预热的持续时间
    Emitter_Duration,       // 发射器配置的持续时间
    Emitter_ElapsedTime,    // 发射器已运行的时间
    Emitter_Loop,           // 发射器是否循环
    Emitter_LoopInterval,   // 发射器循环的间隔时间

    Emitter_EmitInterval,   // 发射器开火间隔
    Emitter_EmitCount,      // 发射器开火次数
    Emitter_EmitOffsetX,    // 发射器开火偏移

    Emitter_Angle,          // 发射器弹道角度
    Emitter_Count,          // 发射器弹道数量

    Bullet_Sprite,          // 外观,这个需要id或者路径
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_Duration,
    Bullet_ElapsedTime,
    Bullet_Speed,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_FacingMoveDir,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export enum eBulletCondition {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_FacingMoveDir,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export enum eEventConditionOp {
    Equal, // 等于
    NotEqual, // 不等于
    Greater, // 大于
    Less, // 小于
    GreaterEqual, // 大于等于
    LessEqual, // 小
}

/**
 * 发射器事件数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("EmitterConditionData")
export class EmitterConditionData {
    @property({ type: Enum(eEmitterCondition), displayName: '条件类型' })
    eventType : eEmitterCondition = eEmitterCondition.Level_Duration;

    @property({ type: Enum(eEventConditionOp), displayName: '条件操作' })
    eventOp : eEventConditionOp = eEventConditionOp.Equal;

    @property({ displayName: '条件值' })
    targetValue : number = 0; // 条件值: 例如持续时间、距离

    @property({ displayName: '条件值(string)'})
    targetValueStr : string = '';
}

/**
 * 子弹事件数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("BulletConditionData")
export class BulletConditionData {
    @property({ type: Enum(eBulletCondition), displayName: '条件类型' })
    eventType : eBulletCondition = eBulletCondition.Bullet_ElapsedTime;

    @property({ type: Enum(eEventConditionOp), displayName: '条件操作' })
    eventOp : eEventConditionOp = eEventConditionOp.Equal;

    @property({ displayName: '条件值' })
    targetValue : number = 0; // 条件值: 例如持续时间、距离

    @property({ displayName: '条件值(string)'})
    targetValueStr : string = '';
}
