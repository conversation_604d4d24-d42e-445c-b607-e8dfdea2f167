
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------


export namespace res { 
/**
 * 装备部位
 */
export enum EquipClass {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 火力核心
     */
    WEAPON = 1,
    /**
     * 副武器
     */
    SUB_WEAPON = 2,
    /**
     * 装甲核心
     */
    ARMOR = 3,
    /**
     * 科技核心
     */
    TECHNIC = 4,
}

} 
export namespace res { 
/**
 * GM命令页签
 */
export enum GMTabID {
    /**
     * 通用
     */
    COMMON = 0,
    /**
     * 战斗
     */
    BATTLE = 1,
}

} 
export namespace res { 
/**
 * 道具的使用效果
 */
export enum ItemEffectType {
    /**
     * 无效果
     */
    NONE = 0,
    /**
     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID
     */
    DROP = 1,
    /**
     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币
     */
    GEN_GOLD = 2,
    /**
     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石
     */
    GEN_DIAMOND = 3,
    /**
     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值
     */
    GEN_XP = 4,
    /**
     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值
     */
    GEN_ENERGY = 5,
    /**
     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具
     */
    GEN_ITEM = 6,
}

} 
export namespace res { 
/**
 * 道具的使用类型
 */
export enum ItemUseType {
    /**
     * 不可直接从背包来使用的道具
     */
    NONE = 0,
    /**
     * 先放入背包内，然后由玩家从背包手动选择后使用
     */
    MANUAL = 1,
    /**
     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子
     */
    AUTO = 2,
}

} 
export namespace res { 
/**
 * 模式类型
 */
export enum ModeType {
    /**
     * 无尽
     */
    ENDLESS = 0,
    /**
     * 剧情
     */
    STORY = 1,
    /**
     * 远征
     */
    EXPEDITION = 2,
    /**
     * 无尽PK
     */
    ENDLESSPK = 3,
    /**
     * 好友PK
     */
    FRIENDPK = 4,
}

} 
export namespace res { 
/**
 * 货币类型
 */
export enum MoneyType {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 金币
     */
    GOLD = 1,
    /**
     * 钻石
     */
    DIAMOND = 2,
    /**
     * 体力
     */
    POWER = 3,
    /**
     * 道具
     */
    ITEM = 4,
}

} 
export namespace res { 
/**
 * 模式类型
 */
export enum PlayCycle {
    /**
     * 每日
     */
    DAY = 0,
    /**
     * 每周
     */
    WEEK = 1,
}

} 
export namespace res { 
/**
 * 装备属性名称
 */
export enum PropName {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 攻击力
     */
    HURT = 1,
    /**
     * 生命值
     */
    HP = 2,
}

} 
export namespace res { 
/**
 * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)
 */
export enum QualityType {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 普通
     */
    COMMON = 1,
    /**
     * 精良
     */
    UNCOMMON = 2,
    /**
     * 稀有
     */
    RACE = 3,
    /**
     * 史诗
     */
    EPIC = 4,
    /**
     * 传说
     */
    LEGENDARY = 5,
    /**
     * 神话
     */
    MYTHIC = 6,
}

} 
 
export enum TargetScanStrategy {
    /**
     * 更新
     */
    Refresh = 0,
    /**
     * 保持
     */
    Keep = 1,
}

 





export class Boss {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.bId === undefined) { throw new Error() }
        this.bId = _json_.bId
        if (_json_.sId === undefined) { throw new Error() }
        this.sId = _json_.sId
        if (_json_.app === undefined) { throw new Error() }
        this.app = _json_.app
        if (_json_.ta === undefined) { throw new Error() }
        this.ta = _json_.ta
        if (_json_.ft === undefined) { throw new Error() }
        this.ft = _json_.ft
        if (_json_.leave === undefined) { throw new Error() }
        this.leave = _json_.leave
        if (_json_.exp === undefined) { throw new Error() }
        this.exp = _json_.exp
        if (_json_.rid === undefined) { throw new Error() }
        this.rid = _json_.rid
        if (_json_.sk === undefined) { throw new Error() }
        this.sk = _json_.sk
        if (_json_.blp === undefined) { throw new Error() }
        this.blp = _json_.blp
        if (_json_.us === undefined) { throw new Error() }
        this.us = _json_.us
        if (_json_.ua === undefined) { throw new Error() }
        this.ua = _json_.ua
        if (_json_.va === undefined) { throw new Error() }
        this.va = _json_.va
        if (_json_.sv === undefined) { throw new Error() }
        this.sv = _json_.sv
        if (_json_.fl === undefined) { throw new Error() }
        this.fl = _json_.fl
        if (_json_.loot === undefined) { throw new Error() }
        this.loot = _json_.loot
        if (_json_.adsorb === undefined) { throw new Error() }
        this.adsorb = _json_.adsorb
        if (_json_.lp0 === undefined) { throw new Error() }
        this.lp0 = _json_.lp0
        if (_json_.lp1 === undefined) { throw new Error() }
        this.lp1 = _json_.lp1
        if (_json_.dh === undefined) { throw new Error() }
        this.dh = _json_.dh
        if (_json_.atk === undefined) { throw new Error() }
        this.atk = _json_.atk
        if (_json_.col === undefined) { throw new Error() }
        this.col = _json_.col
        if (_json_.tway === undefined) { throw new Error() }
        this.tway = _json_.tway
        if (_json_.way === undefined) { throw new Error() }
        this.way = _json_.way
        if (_json_.wi === undefined) { throw new Error() }
        this.wi = _json_.wi
        if (_json_.sp === undefined) { throw new Error() }
        this.sp = _json_.sp
        if (_json_.ai === undefined) { throw new Error() }
        this.ai = _json_.ai
        if (_json_.ra === undefined) { throw new Error() }
        this.ra = _json_.ra
        if (_json_.a0 === undefined) { throw new Error() }
        this.a0 = _json_.a0
        if (_json_.a1 === undefined) { throw new Error() }
        this.a1 = _json_.a1
        if (_json_.a2 === undefined) { throw new Error() }
        this.a2 = _json_.a2
        if (_json_.a3 === undefined) { throw new Error() }
        this.a3 = _json_.a3
        if (_json_.a4 === undefined) { throw new Error() }
        this.a4 = _json_.a4
        if (_json_.a5 === undefined) { throw new Error() }
        this.a5 = _json_.a5
        if (_json_.a6 === undefined) { throw new Error() }
        this.a6 = _json_.a6
        if (_json_.a7 === undefined) { throw new Error() }
        this.a7 = _json_.a7
        if (_json_.a8 === undefined) { throw new Error() }
        this.a8 = _json_.a8
        if (_json_.a9 === undefined) { throw new Error() }
        this.a9 = _json_.a9
        if (_json_.a10 === undefined) { throw new Error() }
        this.a10 = _json_.a10
        if (_json_.a11 === undefined) { throw new Error() }
        this.a11 = _json_.a11
        if (_json_.a12 === undefined) { throw new Error() }
        this.a12 = _json_.a12
        if (_json_.a13 === undefined) { throw new Error() }
        this.a13 = _json_.a13
        if (_json_.a14 === undefined) { throw new Error() }
        this.a14 = _json_.a14
        if (_json_.a15 === undefined) { throw new Error() }
        this.a15 = _json_.a15
        if (_json_.a16 === undefined) { throw new Error() }
        this.a16 = _json_.a16
        if (_json_.a17 === undefined) { throw new Error() }
        this.a17 = _json_.a17
        if (_json_.a18 === undefined) { throw new Error() }
        this.a18 = _json_.a18
        if (_json_.a19 === undefined) { throw new Error() }
        this.a19 = _json_.a19
        if (_json_.a20 === undefined) { throw new Error() }
        this.a20 = _json_.a20
        if (_json_.a21 === undefined) { throw new Error() }
        this.a21 = _json_.a21
        if (_json_.a22 === undefined) { throw new Error() }
        this.a22 = _json_.a22
        if (_json_.a100 === undefined) { throw new Error() }
        this.a100 = _json_.a100
        if (_json_.a101 === undefined) { throw new Error() }
        this.a101 = _json_.a101
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 飞机id
     */
    readonly bId: number
    /**
     * 子类型
     */
    readonly sId: number
    /**
     * 出场参数
     */
    readonly app: string
    /**
     * inAudio
     */
    readonly ta: string
    /**
     * 死亡掉落延迟
     */
    readonly ft: number
    /**
     * leave
     */
    readonly leave: number
    /**
     * exp
     */
    readonly exp: number
    /**
     * rid
     */
    readonly rid: string
    /**
     * 爆炸震动参数
     */
    readonly sk: string
    /**
     * 爆炸参数
     */
    readonly blp: string
    /**
     * us
     */
    readonly us: string
    /**
     * ua
     */
    readonly ua: string
    /**
     * va
     */
    readonly va: string
    /**
     * sv
     */
    readonly sv: string
    /**
     * fl
     */
    readonly fl: string
    /**
     * loot
     */
    readonly loot: string
    /**
     * adsorb
     */
    readonly adsorb: string
    /**
     * lp0
     */
    readonly lp0: string
    /**
     * lp1
     */
    readonly lp1: string
    /**
     * dh
     */
    readonly dh: string
    /**
     * atk
     */
    readonly atk: number
    /**
     * col
     */
    readonly col: number
    /**
     * tway
     */
    readonly tway: string
    /**
     * way
     */
    readonly way: string
    /**
     * wi
     */
    readonly wi: string
    /**
     * sp
     */
    readonly sp: string
    /**
     * ai
     */
    readonly ai: string
    /**
     * ra
     */
    readonly ra: string
    /**
     * a0
     */
    readonly a0: string
    /**
     * a1
     */
    readonly a1: string
    /**
     * a2
     */
    readonly a2: string
    /**
     * a3
     */
    readonly a3: string
    /**
     * a4
     */
    readonly a4: string
    /**
     * a5
     */
    readonly a5: string
    /**
     * a6
     */
    readonly a6: string
    /**
     * a7
     */
    readonly a7: string
    /**
     * a8
     */
    readonly a8: string
    /**
     * a9
     */
    readonly a9: string
    /**
     * a10
     */
    readonly a10: string
    /**
     * a11
     */
    readonly a11: string
    /**
     * a12
     */
    readonly a12: string
    /**
     * a13
     */
    readonly a13: string
    /**
     * a14
     */
    readonly a14: string
    /**
     * a15
     */
    readonly a15: string
    /**
     * a16
     */
    readonly a16: string
    /**
     * a17
     */
    readonly a17: string
    /**
     * a18
     */
    readonly a18: string
    /**
     * a19
     */
    readonly a19: string
    /**
     * a20
     */
    readonly a20: string
    /**
     * a21
     */
    readonly a21: string
    /**
     * a22
     */
    readonly a22: string
    /**
     * a100
     */
    readonly a100: string
    /**
     * a101
     */
    readonly a101: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}




export namespace builtin {
export class ConParam {

    constructor(_json_: any) {
        if (_json_.con === undefined) { throw new Error() }
        this.con = _json_.con
        if (_json_.param === undefined) { throw new Error() }
        this.param = _json_.param
    }

    readonly con: number
    readonly param: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
/**
 * 随机策略
 */
export class randStrategy {

    constructor(_json_: any) {
        if (_json_.ID === undefined) { throw new Error() }
        this.ID = _json_.ID
        if (_json_.Weight === undefined) { throw new Error() }
        this.Weight = _json_.Weight
    }

    /**
     * 随机策略ID
     */
    readonly ID: number
    /**
     * ID的权重
     */
    readonly Weight: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
export class RatingParam {

    constructor(_json_: any) {
        if (_json_.rating === undefined) { throw new Error() }
        this.rating = _json_.rating
        if (_json_.param === undefined) { throw new Error() }
        this.param = _json_.param
    }

    readonly rating: number
    readonly param: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
export class vector2 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
    }

    readonly x: number
    readonly y: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
export class vector3 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
    }

    readonly x: number
    readonly y: number
    readonly z: number

    resolve(tables:Tables) {
        
        
        
    }
}

}


export namespace builtin {
export class vector4 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
        if (_json_.w === undefined) { throw new Error() }
        this.w = _json_.w
    }

    readonly x: number
    readonly y: number
    readonly z: number
    readonly w: number

    resolve(tables:Tables) {
        
        
        
        
    }
}

}



export class Bullet {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.am === undefined) { throw new Error() }
        this.am = _json_.am
        if (_json_.image === undefined) { throw new Error() }
        this.image = _json_.image
        if (_json_.bustyle === undefined) { throw new Error() }
        this.bustyle = _json_.bustyle
        if (_json_.angleSpeed === undefined) { throw new Error() }
        this.angleSpeed = _json_.angleSpeed
        if (_json_.waittime === undefined) { throw new Error() }
        { this.waittime = []; for(let _ele0 of _json_.waittime) { let _e0; _e0 = _ele0; this.waittime.push(_e0);}}
        if (_json_.initialve === undefined) { throw new Error() }
        this.initialve = _json_.initialve
        if (_json_.spdiff === undefined) { throw new Error() }
        this.spdiff = _json_.spdiff
        if (_json_.scale === undefined) { throw new Error() }
        this.scale = _json_.scale
        if (_json_.retrieve === undefined) { throw new Error() }
        this.retrieve = _json_.retrieve
        if (_json_.disappear === undefined) { throw new Error() }
        this.disappear = _json_.disappear
        if (_json_.shiftingbody === undefined) { throw new Error() }
        { this.shiftingbody = []; for(let _ele0 of _json_.shiftingbody) { let _e0; _e0 = _ele0; this.shiftingbody.push(_e0);}}
        if (_json_.body === undefined) { throw new Error() }
        this.body = _json_.body
        if (_json_.exstyle1 === undefined) { throw new Error() }
        this.exstyle1 = _json_.exstyle1
        if (_json_.exstyle2 === undefined) { throw new Error() }
        this.exstyle2 = _json_.exstyle2
        if (_json_.time === undefined) { throw new Error() }
        this.time = _json_.time
        if (_json_.accnumber === undefined) { throw new Error() }
        this.accnumber = _json_.accnumber
        if (_json_.acc === undefined) { throw new Error() }
        this.acc = _json_.acc
        if (_json_.offset === undefined) { throw new Error() }
        { this.offset = []; for(let _ele0 of _json_.offset) { let _e0; _e0 = _ele0; this.offset.push(_e0);}}
        if (_json_.para === undefined) { throw new Error() }
        { this.para = []; for(let _ele0 of _json_.para) { let _e0; _e0 = _ele0; this.para.push(_e0);}}
    }

    /**
     * id
     */
    readonly id: number
    /**
     * name
     */
    readonly name: string
    /**
     * am
     */
    readonly am: string
    /**
     * image
     */
    readonly image: string
    /**
     * 子弹类型
     */
    readonly bustyle: number
    /**
     * 旋转角度
     */
    readonly angleSpeed: number
    /**
     * 等待时间
     */
    readonly waittime: number[]
    /**
     * 速度
     */
    readonly initialve: number
    /**
     * 速度随机变量
     */
    readonly spdiff: number
    /**
     * 缩放
     */
    readonly scale: number
    /**
     * 子弹存活时间
     */
    readonly retrieve: number
    /**
     * 是否穿透
     */
    readonly disappear: number
    /**
     * 碰撞宽高
     */
    readonly shiftingbody: number[]
    /**
     * 碰撞
     */
    readonly body: number
    /**
     * 伤害粒子效果
     */
    readonly exstyle1: string
    /**
     * 伤害粒子缩放
     */
    readonly exstyle2: string
    /**
     * time
     */
    readonly time: number
    /**
     * accnumber
     */
    readonly accnumber: number
    /**
     * acc
     */
    readonly acc: number
    /**
     * offset
     */
    readonly offset: number[]
    /**
     * para
     */
    readonly para: number[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class Chapter {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.levelCount === undefined) { throw new Error() }
        this.levelCount = _json_.levelCount
        if (_json_.levelGroupCount === undefined) { throw new Error() }
        this.levelGroupCount = _json_.levelGroupCount
        if (_json_.strategy === undefined) { throw new Error() }
        this.strategy = _json_.strategy
        if (_json_.damageBonus === undefined) { throw new Error() }
        this.damageBonus = _json_.damageBonus
        if (_json_.lifeBounus === undefined) { throw new Error() }
        this.lifeBounus = _json_.lifeBounus
        if (_json_.strategyList === undefined) { throw new Error() }
        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.strategyList.push(_e0);}}
    }

    /**
     * 章节ID
     */
    readonly id: number
    /**
     * 章节关卡数量
     */
    readonly levelCount: number
    /**
     * 章节关卡组数量
     */
    readonly levelGroupCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly strategy: number
    /**
     * 章节伤害加成
     */
    readonly damageBonus: number
    /**
     * 章节生命加成
     */
    readonly lifeBounus: number
    readonly strategyList: builtin.randStrategy[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        for (let _e of this.strategyList) { _e?.resolve(tables); }
    }
}





/**
 * 消耗的材料
 */
export class ConsumeItem {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
    }

    readonly id: number
    readonly num: number

    resolve(tables:Tables) {
        
        
    }
}





export class ConsumeMoney {

    constructor(_json_: any) {
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
    }

    /**
     * 货币类型
     */
    readonly type: res.MoneyType
    /**
     * 货币数量
     */
    readonly num: number

    resolve(tables:Tables) {
        
        
    }
}





export class Enemy {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.uiId === undefined) { throw new Error() }
        this.uiId = _json_.uiId
        if (_json_.atk === undefined) { throw new Error() }
        this.atk = _json_.atk
        if (_json_.hp === undefined) { throw new Error() }
        this.hp = _json_.hp
        if (_json_.collideLevel === undefined) { throw new Error() }
        this.collideLevel = _json_.collideLevel
        if (_json_.turn === undefined) { throw new Error() }
        this.turn = _json_.turn
        if (_json_.hpShow === undefined) { throw new Error() }
        this.hpShow = _json_.hpShow
        if (_json_.collideAttack === undefined) { throw new Error() }
        this.collideAttack = _json_.collideAttack
        if (_json_.bCollideDead === undefined) { throw new Error() }
        this.bCollideDead = _json_.bCollideDead
        if (_json_.bMoveAttack === undefined) { throw new Error() }
        this.bMoveAttack = _json_.bMoveAttack
        if (_json_.bStayAttack === undefined) { throw new Error() }
        this.bStayAttack = _json_.bStayAttack
        if (_json_.attackInterval === undefined) { throw new Error() }
        this.attackInterval = _json_.attackInterval
        if (_json_.attackNum === undefined) { throw new Error() }
        this.attackNum = _json_.attackNum
        if (_json_.attackData === undefined) { throw new Error() }
        this.attackData = _json_.attackData
        if (_json_.param === undefined) { throw new Error() }
        this.param = _json_.param
        if (_json_.dieShoot === undefined) { throw new Error() }
        this.dieShoot = _json_.dieShoot
        if (_json_.dieBullet === undefined) { throw new Error() }
        this.dieBullet = _json_.dieBullet
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 敌机的显示id
     */
    readonly uiId: number
    /**
     * 攻击
     */
    readonly atk: number
    /**
     * 血量
     */
    readonly hp: number
    /**
     * 碰撞等级
     */
    readonly collideLevel: number
    /**
     * 是否改变方向
     */
    readonly turn: number
    /**
     * 是否显示血条
     */
    readonly hpShow: number
    /**
     * 碰撞伤害值
     */
    readonly collideAttack: number
    /**
     * 碰撞后是否死亡
     */
    readonly bCollideDead: number
    /**
     * 移动时是否攻击
     */
    readonly bMoveAttack: number
    /**
     * 静止时是否攻击
     */
    readonly bStayAttack: number
    /**
     * 攻击间隔时间
     */
    readonly attackInterval: number
    /**
     * 攻击次数
     */
    readonly attackNum: number
    /**
     * 攻击点位置数据(x,y;间隔,子弹id,子弹数量,子弹间隔,子弹攻击力百分比(100为1倍);)
     */
    readonly attackData: string
    /**
     * 自定义参数
     */
    readonly param: string
    /**
     * 死亡时发射的子弹数据
     */
    readonly dieShoot: string
    /**
     * 死亡时是否发射子弹
     */
    readonly dieBullet: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class EnemyUI {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.image === undefined) { throw new Error() }
        this.image = _json_.image
        if (_json_.isAm === undefined) { throw new Error() }
        this.isAm = _json_.isAm
        if (_json_.collider === undefined) { throw new Error() }
        this.collider = _json_.collider
        if (_json_.hpParam === undefined) { throw new Error() }
        this.hpParam = _json_.hpParam
        if (_json_.blastSound === undefined) { throw new Error() }
        this.blastSound = _json_.blastSound
        if (_json_.blp === undefined) { throw new Error() }
        this.blp = _json_.blp
        if (_json_.blastDurations === undefined) { throw new Error() }
        this.blastDurations = _json_.blastDurations
        if (_json_.blastShake === undefined) { throw new Error() }
        this.blastShake = _json_.blastShake
        if (_json_.damageParam === undefined) { throw new Error() }
        this.damageParam = _json_.damageParam
        if (_json_.extraParam0 === undefined) { throw new Error() }
        this.extraParam0 = _json_.extraParam0
        if (_json_.extraParam1 === undefined) { throw new Error() }
        this.extraParam1 = _json_.extraParam1
        if (_json_.skillResistUIDict === undefined) { throw new Error() }
        this.skillResistUIDict = _json_.skillResistUIDict
        if (_json_.lootParam0 === undefined) { throw new Error() }
        this.lootParam0 = _json_.lootParam0
        if (_json_.lootParam1 === undefined) { throw new Error() }
        this.lootParam1 = _json_.lootParam1
        if (_json_.showParam === undefined) { throw new Error() }
        this.showParam = _json_.showParam
        if (_json_.sneakAnim === undefined) { throw new Error() }
        this.sneakAnim = _json_.sneakAnim
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 图片
     */
    readonly image: string
    /**
     * 是否动画
     */
    readonly isAm: number
    /**
     * 碰撞器数据
     */
    readonly collider: string
    /**
     * 血量参数
     */
    readonly hpParam: string
    /**
     * 爆炸音效 ID
     */
    readonly blastSound: number
    /**
     * 爆炸次数和爆炸参数
     */
    readonly blp: string
    /**
     * 爆炸持续时间
     */
    readonly blastDurations: string
    /**
     * 爆炸震动参数
     */
    readonly blastShake: string
    /**
     * 伤害参数
     */
    readonly damageParam: string
    /**
     * 额外参数
     */
    readonly extraParam0: string
    /**
     * 额外参数 1
     */
    readonly extraParam1: string
    /**
     * 技能抗性字典
     */
    readonly skillResistUIDict: string
    /**
     * 掉落参数 0
     */
    readonly lootParam0: string
    /**
     * 掉落参数 1
     */
    readonly lootParam1: string
    /**
     * 显示参数
     */
    readonly showParam: string
    /**
     * 潜行动画
     */
    readonly sneakAnim: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





/**
 * 装备属性
 */
export class EquipProp {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.value === undefined) { throw new Error() }
        this.value = _json_.value
    }

    readonly id: res.PropName
    readonly value: number

    resolve(tables:Tables) {
        
        
    }
}





export class GameMap {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.level === undefined) { throw new Error() }
        this.level = _json_.level
        if (_json_.floor_res === undefined) { throw new Error() }
        { this.floorRes = []; for(let _ele0 of _json_.floor_res) { let _e0; _e0 = _ele0; this.floorRes.push(_e0);}}
        if (_json_.hide_img === undefined) { throw new Error() }
        { this.hideImg = []; for(let _ele0 of _json_.hide_img) { let _e0; _e0 = _ele0; this.hideImg.push(_e0);}}
        if (_json_.sky_res === undefined) { throw new Error() }
        { this.skyRes = []; for(let _ele0 of _json_.sky_res) { let _e0; _e0 = _ele0; this.skyRes.push(_e0);}}
        if (_json_.imageSque_res === undefined) { throw new Error() }
        { this.imageSqueRes = []; for(let _ele0 of _json_.imageSque_res) { let _e0; _e0 = _ele0; this.imageSqueRes.push(_e0);}}
        if (_json_.floor_speed === undefined) { throw new Error() }
        { this.floorSpeed = []; for(let _ele0 of _json_.floor_speed) { let _e0; _e0 = _ele0; this.floorSpeed.push(_e0);}}
        if (_json_.sky_speed === undefined) { throw new Error() }
        { this.skySpeed = []; for(let _ele0 of _json_.sky_speed) { let _e0; _e0 = _ele0; this.skySpeed.push(_e0);}}
        if (_json_.imageSque_speed === undefined) { throw new Error() }
        { this.imageSqueSpeed = []; for(let _ele0 of _json_.imageSque_speed) { let _e0; _e0 = _ele0; this.imageSqueSpeed.push(_e0);}}
        if (_json_.floor_layer === undefined) { throw new Error() }
        { this.floorLayer = []; for(let _ele0 of _json_.floor_layer) { let _e0; _e0 = _ele0; this.floorLayer.push(_e0);}}
        if (_json_.sky_layer === undefined) { throw new Error() }
        { this.skyLayer = []; for(let _ele0 of _json_.sky_layer) { let _e0; _e0 = _ele0; this.skyLayer.push(_e0);}}
        if (_json_.imageSque_layer === undefined) { throw new Error() }
        { this.imageSqueLayer = []; for(let _ele0 of _json_.imageSque_layer) { let _e0; _e0 = _ele0; this.imageSqueLayer.push(_e0);}}
        if (_json_.imageSqueNode_move === undefined) { throw new Error() }
        { this.imageSqueNodeMove = []; for(let _ele0 of _json_.imageSqueNode_move) { let _e0; _e0 = _ele0; this.imageSqueNodeMove.push(_e0);}}
        if (_json_.imageSque_pos === undefined) { throw new Error() }
        { this.imageSquePos = []; for(let _ele0 of _json_.imageSque_pos) { let _e0; _e0 = _ele0; this.imageSquePos.push(_e0);}}
        if (_json_.skyNode_move === undefined) { throw new Error() }
        { this.skyNodeMove = []; for(let _ele0 of _json_.skyNode_move) { let _e0; _e0 = _ele0; this.skyNodeMove.push(_e0);}}
        if (_json_.link_y_distance === undefined) { throw new Error() }
        { this.linkYDistance = []; for(let _ele0 of _json_.link_y_distance) { let _e0; _e0 = _ele0; this.linkYDistance.push(_e0);}}
        if (_json_.sky_angle === undefined) { throw new Error() }
        { this.skyAngle = []; for(let _ele0 of _json_.sky_angle) { let _e0; _e0 = _ele0; this.skyAngle.push(_e0);}}
        if (_json_.sky_layout === undefined) { throw new Error() }
        { this.skyLayout = []; for(let _ele0 of _json_.sky_layout) { let _e0; _e0 = _ele0; this.skyLayout.push(_e0);}}
        if (_json_.in_map_item === undefined) { throw new Error() }
        { this.inMapItem = []; for(let _ele0 of _json_.in_map_item) { let _e0; _e0 = _ele0; this.inMapItem.push(_e0);}}
        if (_json_.start_y === undefined) { throw new Error() }
        this.startY = _json_.start_y
        if (_json_.total_rules === undefined) { throw new Error() }
        this.totalRules = _json_.total_rules
    }

    /**
     * id
     */
    readonly id: number
    /**
     * level
     */
    readonly level: number
    /**
     * floor_res
     */
    readonly floorRes: string[]
    /**
     * hide_img
     */
    readonly hideImg: string[]
    /**
     * sky_res
     */
    readonly skyRes: string[]
    /**
     * imageSque_res
     */
    readonly imageSqueRes: string[]
    /**
     * floor_speed
     */
    readonly floorSpeed: number[]
    /**
     * sky_speed
     */
    readonly skySpeed: number[]
    /**
     * imageSque_speed
     */
    readonly imageSqueSpeed: number[]
    /**
     * floor_layer
     */
    readonly floorLayer: number[]
    /**
     * sky_layer
     */
    readonly skyLayer: number[]
    /**
     * imageSque_layer
     */
    readonly imageSqueLayer: number[]
    /**
     * imageSqueNode_move
     */
    readonly imageSqueNodeMove: number[]
    /**
     * imageSque_pos
     */
    readonly imageSquePos: number[]
    /**
     * skyNode_move
     */
    readonly skyNodeMove: number[]
    /**
     * link_y_distance
     */
    readonly linkYDistance: string[]
    /**
     * sky_angle
     */
    readonly skyAngle: number[]
    /**
     * sky_layout
     */
    readonly skyLayout: number[]
    /**
     * in_map_item
     */
    readonly inMapItem: number[]
    /**
     * start_y
     */
    readonly startY: number
    /**
     * total_rules
     */
    readonly totalRules: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class GameMode {

    constructor(_json_: any) {
        if (_json_.ID === undefined) { throw new Error() }
        this.ID = _json_.ID
        if (_json_.modeType === undefined) { throw new Error() }
        this.modeType = _json_.modeType
        if (_json_.chapterID === undefined) { throw new Error() }
        this.chapterID = _json_.chapterID
        if (_json_.order === undefined) { throw new Error() }
        this.order = _json_.order
        if (_json_.resourceID === undefined) { throw new Error() }
        this.resourceID = _json_.resourceID
        if (_json_.description === undefined) { throw new Error() }
        this.description = _json_.description
        if (_json_.conList === undefined) { throw new Error() }
        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new builtin.ConParam(_ele0); this.conList.push(_e0);}}
        if (_json_.cycle === undefined) { throw new Error() }
        this.cycle = _json_.cycle
        if (_json_.times === undefined) { throw new Error() }
        this.times = _json_.times
        if (_json_.monType === undefined) { throw new Error() }
        this.monType = _json_.monType
        if (_json_.costParam1 === undefined) { throw new Error() }
        this.costParam1 = _json_.costParam1
        if (_json_.costParam2 === undefined) { throw new Error() }
        this.costParam2 = _json_.costParam2
        if (_json_.rebirthTimes === undefined) { throw new Error() }
        this.rebirthTimes = _json_.rebirthTimes
        if (_json_.rebirthCost === undefined) { throw new Error() }
        this.rebirthCost = _json_.rebirthCost
        if (_json_.power === undefined) { throw new Error() }
        this.power = _json_.power
        if (_json_.rogueID === undefined) { throw new Error() }
        this.rogueID = _json_.rogueID
        if (_json_.LevelLimit === undefined) { throw new Error() }
        this.LevelLimit = _json_.LevelLimit
        if (_json_.rogueFirst === undefined) { throw new Error() }
        this.rogueFirst = _json_.rogueFirst
        if (_json_.sweepLimit === undefined) { throw new Error() }
        this.sweepLimit = _json_.sweepLimit
        if (_json_.rewardID1 === undefined) { throw new Error() }
        this.rewardID1 = _json_.rewardID1
        if (_json_.rewardID2 === undefined) { throw new Error() }
        this.rewardID2 = _json_.rewardID2
        if (_json_.ratingList === undefined) { throw new Error() }
        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new builtin.RatingParam(_ele0); this.ratingList.push(_e0);}}
    }

    /**
     * ID
     */
    readonly ID: number
    /**
     * 模式类型
     */
    readonly modeType: res.ModeType
    /**
     * 章节ID
     */
    readonly chapterID: number
    /**
     * 排序
     */
    readonly order: number
    /**
     * 入口资源
     */
    readonly resourceID: number
    /**
     * 文本介绍
     */
    readonly description: string
    readonly conList: builtin.ConParam[]
    /**
     * 进入周期
     */
    readonly cycle: res.PlayCycle
    /**
     * 进入次数
     */
    readonly times: number
    /**
     * 消耗类型
     */
    readonly monType: res.MoneyType
    /**
     * 消耗参数1
     */
    readonly costParam1: number
    /**
     * 消耗参数2
     */
    readonly costParam2: number
    /**
     * 复活次数
     */
    readonly rebirthTimes: number
    /**
     * 复活消耗
     */
    readonly rebirthCost: number
    /**
     * 战力评估
     */
    readonly power: number
    /**
     * 肉鸽组
     */
    readonly rogueID: number
    /**
     * 局内等级上限
     */
    readonly LevelLimit: number
    /**
     * 初始肉鸽选择
     */
    readonly rogueFirst: number
    /**
     * 扫荡次数
     */
    readonly sweepLimit: number
    /**
     * 奖励ID1
     */
    readonly rewardID1: number
    /**
     * 奖励ID2
     */
    readonly rewardID2: number
    readonly ratingList: builtin.RatingParam[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class GlobalAttr {

    constructor(_json_: any) {
        if (_json_.GoldProducion === undefined) { throw new Error() }
        this.GoldProducion = _json_.GoldProducion
        if (_json_.MaxEnergy === undefined) { throw new Error() }
        this.MaxEnergy = _json_.MaxEnergy
        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }
        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval
        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }
        this.EnergyRecoverValue = _json_.EnergyRecoverValue
    }

    /**
     * 每回合发放的金币
     */
    readonly GoldProducion: number
    /**
     * 体力上限值
     */
    readonly MaxEnergy: number
    /**
     * 体力恢复的间隔时间（秒）
     */
    readonly EnergyRecoverInterval: number
    /**
     * 体力恢复的值
     */
    readonly EnergyRecoverValue: number

    resolve(tables:Tables) {
        
        
        
        
    }
}





export class Level {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.prefab === undefined) { throw new Error() }
        this.prefab = _json_.prefab
        if (_json_.forbidFire === undefined) { throw new Error() }
        this.forbidFire = _json_.forbidFire
        if (_json_.forbidNBomb === undefined) { throw new Error() }
        this.forbidNBomb = _json_.forbidNBomb
        if (_json_.forbidActSkill === undefined) { throw new Error() }
        this.forbidActSkill = _json_.forbidActSkill
        if (_json_.planeCollisionScaling === undefined) { throw new Error() }
        this.planeCollisionScaling = _json_.planeCollisionScaling
        if (_json_.levelType === undefined) { throw new Error() }
        this.levelType = _json_.levelType
    }

    /**
     * 关卡id
     */
    readonly id: number
    /**
     * 关卡prefab
     */
    readonly prefab: string
    /**
     * 是、否（默认值）
     */
    readonly forbidFire: boolean
    /**
     * 是、否（默认值）
     */
    readonly forbidNBomb: boolean
    /**
     * 是、否（默认值）
     */
    readonly forbidActSkill: boolean
    /**
     * 0到1（1表示正常碰撞）
     */
    readonly planeCollisionScaling: number
    /**
     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关
     */
    readonly levelType: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
    }
}





export class LevelGroup {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.normLevelCount === undefined) { throw new Error() }
        this.normLevelCount = _json_.normLevelCount
        if (_json_.normLevelST === undefined) { throw new Error() }
        this.normLevelST = _json_.normLevelST
        if (_json_.normSTList === undefined) { throw new Error() }
        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.normSTList.push(_e0);}}
        if (_json_.bossLevelCount === undefined) { throw new Error() }
        this.bossLevelCount = _json_.bossLevelCount
        if (_json_.bossLevelST === undefined) { throw new Error() }
        this.bossLevelST = _json_.bossLevelST
        if (_json_.bossSTList === undefined) { throw new Error() }
        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.bossSTList.push(_e0);}}
    }

    /**
     * 关卡组ID
     */
    readonly id: number
    /**
     * 常规关卡数量
     */
    readonly normLevelCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly normLevelST: number
    readonly normSTList: builtin.randStrategy[]
    /**
     * BOSS关卡数量
     */
    readonly bossLevelCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly bossLevelST: number
    readonly bossSTList: builtin.randStrategy[]

    resolve(tables:Tables) {
        
        
        
        for (let _e of this.normSTList) { _e?.resolve(tables); }
        
        
        for (let _e of this.bossSTList) { _e?.resolve(tables); }
    }
}





export class MainPlane {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.body === undefined) { throw new Error() }
        { this.body = []; for(let _ele0 of _json_.body) { let _e0; _e0 = _ele0; this.body.push(_e0);}}
        if (_json_.transSrc === undefined) { throw new Error() }
        { this.transSrc = []; for(let _ele0 of _json_.transSrc) { let _e0; _e0 = _ele0; this.transSrc.push(_e0);}}
        if (_json_.transExt === undefined) { throw new Error() }
        { this.transExt = []; for(let _ele0 of _json_.transExt) { let _e0; _e0 = _ele0; this.transExt.push(_e0);}}
        if (_json_.zjdmtxzb === undefined) { throw new Error() }
        { this.zjdmtxzb = []; for(let _ele0 of _json_.zjdmtxzb) { let _e0; _e0 = _ele0; this.zjdmtxzb.push(_e0);}}
        if (_json_.transatk1 === undefined) { throw new Error() }
        { this.transatk1 = []; for(let _ele0 of _json_.transatk1) { let _e0; _e0 = _ele0; this.transatk1.push(_e0);}}
        if (_json_.shiftingatk1 === undefined) { throw new Error() }
        { this.shiftingatk1 = []; for(let _ele0 of _json_.shiftingatk1) { let _e0; _e0 = _ele0; this.shiftingatk1.push(_e0);}}
    }

    /**
     * id
     */
    readonly id: number
    /**
     * type
     */
    readonly type: number
    /**
     * 碰撞范围
     */
    readonly body: number[]
    /**
     * 变现
     */
    readonly transSrc: string[]
    /**
     * 变形参数
     */
    readonly transExt: string[]
    /**
     * 火焰位置
     */
    readonly zjdmtxzb: number[]
    /**
     * transatk1
     */
    readonly transatk1: number[]
    /**
     * shiftingatk1
     */
    readonly shiftingatk1: number[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
    }
}





export class MainPlaneLv {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.hp === undefined) { throw new Error() }
        this.hp = _json_.hp
        if (_json_.atk === undefined) { throw new Error() }
        this.atk = _json_.atk
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 血量
     */
    readonly hp: number
    /**
     * 攻击
     */
    readonly atk: number

    resolve(tables:Tables) {
        
        
        
    }
}





/**
 * 属性增幅
 */
export class PropInc {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.inc === undefined) { throw new Error() }
        this.inc = _json_.inc
    }

    readonly id: res.PropName
    /**
     * 万分比
     */
    readonly inc: number

    resolve(tables:Tables) {
        
        
    }
}





/**
 * 装备
 */
export class ResEquip {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.quality === undefined) { throw new Error() }
        this.quality = _json_.quality
        if (_json_.quality_sub === undefined) { throw new Error() }
        this.qualitySub = _json_.quality_sub
        if (_json_.equip_class === undefined) { throw new Error() }
        this.equipClass = _json_.equip_class
        if (_json_.props === undefined) { throw new Error() }
        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}
        if (_json_.consume_items === undefined) { throw new Error() }
        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}
    }

    readonly id: number
    readonly name: string
    readonly quality: res.QualityType
    readonly qualitySub: number
    readonly equipClass: res.EquipClass
    readonly props: EquipProp[]
    readonly consumeItems: ConsumeItem[]

    resolve(tables:Tables) {
        
        
        
        
        
        for (let _e of this.props) { _e?.resolve(tables); }
        for (let _e of this.consumeItems) { _e?.resolve(tables); }
    }
}





/**
 * 装备位升级
 */
export class ResEquipUpgrade {

    constructor(_json_: any) {
        if (_json_.equip_class === undefined) { throw new Error() }
        this.equipClass = _json_.equip_class
        if (_json_.level_from === undefined) { throw new Error() }
        this.levelFrom = _json_.level_from
        if (_json_.level_to === undefined) { throw new Error() }
        this.levelTo = _json_.level_to
        if (_json_.prop_inc === undefined) { throw new Error() }
        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}
        if (_json_.consume_money === undefined) { throw new Error() }
        this.consumeMoney = new ConsumeMoney(_json_.consume_money)
        if (_json_.consume_items === undefined) { throw new Error() }
        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}
    }

    /**
     * 装备槽位的类型
     */
    readonly equipClass: res.EquipClass
    /**
     * 等级下限
     */
    readonly levelFrom: number
    /**
     * 等级上限
     */
    readonly levelTo: number
    readonly propInc: PropInc[]
    readonly consumeMoney: ConsumeMoney
    readonly consumeItems: ConsumeItem[]

    resolve(tables:Tables) {
        
        
        
        for (let _e of this.propInc) { _e?.resolve(tables); }
        this.consumeMoney?.resolve(tables);
        for (let _e of this.consumeItems) { _e?.resolve(tables); }
    }
}





export class ResGM {

    constructor(_json_: any) {
        if (_json_.tabID === undefined) { throw new Error() }
        this.tabID = _json_.tabID
        if (_json_.tabName === undefined) { throw new Error() }
        this.tabName = _json_.tabName
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.cmd === undefined) { throw new Error() }
        this.cmd = _json_.cmd
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
    }

    readonly tabID: res.GMTabID
    readonly tabName: string
    readonly name: string
    readonly cmd: string
    readonly desc: string

    resolve(tables:Tables) {
        
        
        
        
        
    }
}





/**
 * 道具
 */
export class ResItem {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.quality === undefined) { throw new Error() }
        this.quality = _json_.quality
        if (_json_.quality_sub === undefined) { throw new Error() }
        this.qualitySub = _json_.quality_sub
        if (_json_.use_type === undefined) { throw new Error() }
        this.useType = _json_.use_type
        if (_json_.effect_id === undefined) { throw new Error() }
        this.effectId = _json_.effect_id
        if (_json_.effect_param1 === undefined) { throw new Error() }
        this.effectParam1 = _json_.effect_param1
        if (_json_.effect_param2 === undefined) { throw new Error() }
        this.effectParam2 = _json_.effect_param2
        if (_json_.max_stack_num === undefined) { throw new Error() }
        this.maxStackNum = _json_.max_stack_num
    }

    readonly id: number
    readonly name: string
    readonly quality: res.QualityType
    readonly qualitySub: number
    readonly useType: res.ItemUseType
    readonly effectId: res.ItemEffectType
    readonly effectParam1: number
    readonly effectParam2: number
    readonly maxStackNum: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
    }
}





/**
 * 卡牌
 */
export class ResWeapon {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
    }

    readonly id: number
    readonly name: string

    resolve(tables:Tables) {
        
        
    }
}





export class ResWhiteList {

    constructor(_json_: any) {
        if (_json_.openid === undefined) { throw new Error() }
        this.openid = _json_.openid
        if (_json_.password === undefined) { throw new Error() }
        this.password = _json_.password
        if (_json_.status === undefined) { throw new Error() }
        this.status = _json_.status
        if (_json_.privilege === undefined) { throw new Error() }
        this.privilege = _json_.privilege
    }

    /**
     * 第一行默认是主键
     */
    readonly openid: string
    /**
     * 密码的MD5
     */
    readonly password: string
    /**
     * account  status: normal/disable
     */
    readonly status: number
    /**
     * 可以访问的内容
     */
    readonly privilege: number

    resolve(tables:Tables) {
        
        
        
        
    }
}





export class Stage {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.mainStage === undefined) { throw new Error() }
        this.mainStage = _json_.mainStage
        if (_json_.subStage === undefined) { throw new Error() }
        this.subStage = _json_.subStage
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.enemyGroupID === undefined) { throw new Error() }
        this.enemyGroupID = _json_.enemyGroupID
        if (_json_.delay === undefined) { throw new Error() }
        this.delay = _json_.delay
        if (_json_.enemyNorRate === undefined) { throw new Error() }
        this.enemyNorRate = _json_.enemyNorRate
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 关卡
     */
    readonly mainStage: number
    /**
     * 阶段
     */
    readonly subStage: number
    /**
     * 类型0:普通敌机 100：boss
     */
    readonly type: number
    /**
     * 波次id
     */
    readonly enemyGroupID: string
    /**
     * 延迟时间
     */
    readonly delay: number
    /**
     * 属性倍率（血量，攻击，碰撞攻击）
     */
    readonly enemyNorRate: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
    }
}





export class Track {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.tpe === undefined) { throw new Error() }
        this.tpe = _json_.tpe
        if (_json_.value === undefined) { throw new Error() }
        this.value = _json_.value
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 类型
     */
    readonly tpe: number
    /**
     * 值(不同的轨迹类型，数据代表的信息不一样)
     */
    readonly value: string

    resolve(tables:Tables) {
        
        
        
    }
}





export class Unit {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.uId === undefined) { throw new Error() }
        this.uId = _json_.uId
        if (_json_.im === undefined) { throw new Error() }
        this.im = _json_.im
        if (_json_.imp === undefined) { throw new Error() }
        this.imp = _json_.imp
        if (_json_.am === undefined) { throw new Error() }
        this.am = _json_.am
        if (_json_.dam === undefined) { throw new Error() }
        this.dam = _json_.dam
        if (_json_.hp === undefined) { throw new Error() }
        this.hp = _json_.hp
        if (_json_.pos === undefined) { throw new Error() }
        this.pos = _json_.pos
        if (_json_.hpp === undefined) { throw new Error() }
        this.hpp = _json_.hpp
        if (_json_.col === undefined) { throw new Error() }
        this.col = _json_.col
        if (_json_.sco === undefined) { throw new Error() }
        this.sco = _json_.sco
        if (_json_.hc === undefined) { throw new Error() }
        this.hc = _json_.hc
        if (_json_.hs === undefined) { throw new Error() }
        this.hs = _json_.hs
        if (_json_.bla === undefined) { throw new Error() }
        this.bla = _json_.bla
        if (_json_.so === undefined) { throw new Error() }
        this.so = _json_.so
        if (_json_.sk === undefined) { throw new Error() }
        this.sk = _json_.sk
        if (_json_.act === undefined) { throw new Error() }
        this.act = _json_.act
        if (_json_.mix === undefined) { throw new Error() }
        this.mix = _json_.mix
        if (_json_.turn === undefined) { throw new Error() }
        this.turn = _json_.turn
    }

    /**
     * id
     */
    readonly id: number
    /**
     * uId
     */
    readonly uId: number
    /**
     * im
     */
    readonly im: string
    /**
     * imp
     */
    readonly imp: string
    /**
     * am
     */
    readonly am: string
    /**
     * dam
     */
    readonly dam: string
    /**
     * hp
     */
    readonly hp: number
    /**
     * pos
     */
    readonly pos: string
    /**
     * hpp
     */
    readonly hpp: string
    /**
     * col
     */
    readonly col: string
    /**
     * sco
     */
    readonly sco: number
    /**
     * hc
     */
    readonly hc: string
    /**
     * hs
     */
    readonly hs: string
    /**
     * bla
     */
    readonly bla: string
    /**
     * so
     */
    readonly so: string
    /**
     * sk
     */
    readonly sk: string
    /**
     * act
     */
    readonly act: string
    /**
     * mix
     */
    readonly mix: string
    /**
     * turn
     */
    readonly turn: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class Wave {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.enemyGroupID === undefined) { throw new Error() }
        this.enemyGroupID = _json_.enemyGroupID
        if (_json_.delay === undefined) { throw new Error() }
        this.delay = _json_.delay
        if (_json_.planeType === undefined) { throw new Error() }
        this.planeType = _json_.planeType
        if (_json_.planeId === undefined) { throw new Error() }
        this.planeId = _json_.planeId
        if (_json_.interval === undefined) { throw new Error() }
        this.interval = _json_.interval
        if (_json_.offsetPos === undefined) { throw new Error() }
        this.offsetPos = _json_.offsetPos
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
        if (_json_.pos === undefined) { throw new Error() }
        this.pos = _json_.pos
        if (_json_.track === undefined) { throw new Error() }
        this.track = _json_.track
        if (_json_.trackParams === undefined) { throw new Error() }
        this.trackParams = _json_.trackParams
        if (_json_.rotatioSpeed === undefined) { throw new Error() }
        this.rotatioSpeed = _json_.rotatioSpeed
        if (_json_.FirstShootDelay === undefined) { throw new Error() }
        this.FirstShootDelay = _json_.FirstShootDelay
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 波次 ID
     */
    readonly enemyGroupID: number
    /**
     * 延迟时间
     */
    readonly delay: number
    /**
     * 0 表示普通敌机
     */
    readonly planeType: number
    /**
     * 敌机id
     */
    readonly planeId: number
    /**
     * 生成间隔时间
     */
    readonly interval: number
    /**
     * 根据敌机数量设置偏移位置
     */
    readonly offsetPos: string
    /**
     * 生成的敌机数量
     */
    readonly num: number
    /**
     * 初始位置
     */
    readonly pos: string
    /**
     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)
     */
    readonly track: string
    /**
     * 轨迹参数
     */
    readonly trackParams: string
    /**
     * 旋转速度
     */
    readonly rotatioSpeed: number
    /**
     * 首次射击延迟
     */
    readonly FirstShootDelay: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}






/**
 * GM命令表
 */
export class TbGM {
    private _dataList: ResGM[]
    
    constructor(_json_: any) {
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResGM
            _v = new ResGM(_json2_)
            this._dataList.push(_v)
        }
    }

    getDataList(): ResGM[] { return this._dataList }

    get(index: number): ResGM | undefined { return this._dataList[index] }
    
    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




/**
 * 武器
 */
export class TbWeapon {
    private _dataMap: Map<number, ResWeapon>
    private _dataList: ResWeapon[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResWeapon>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResWeapon
            _v = new ResWeapon(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResWeapon> { return this._dataMap; }
    getDataList(): ResWeapon[] { return this._dataList; }

    get(key: number): ResWeapon | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




/**
 * 装备位升级
 */
export class TbEquipUpgrade {
    private _dataList: ResEquipUpgrade[]
    
    constructor(_json_: any) {
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEquipUpgrade
            _v = new ResEquipUpgrade(_json2_)
            this._dataList.push(_v)
        }
    }

    getDataList(): ResEquipUpgrade[] { return this._dataList }

    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }
    
    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




/**
 * 装备表
 */
export class TbEquip {
    private _dataMap: Map<number, ResEquip>
    private _dataList: ResEquip[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResEquip>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEquip
            _v = new ResEquip(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResEquip> { return this._dataMap; }
    getDataList(): ResEquip[] { return this._dataList; }

    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




/**
 * 道具表
 */
export class TbItem {
    private _dataMap: Map<number, ResItem>
    private _dataList: ResItem[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResItem>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResItem
            _v = new ResItem(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResItem> { return this._dataMap; }
    getDataList(): ResItem[] { return this._dataList; }

    get(key: number): ResItem | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbGlobalAttr {

    private _data: GlobalAttr
    constructor(_json_: any) {
        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')
        this._data = new GlobalAttr(_json_[0])
    }

    getData(): GlobalAttr { return this._data; }

    /**
     * 每回合发放的金币
     */
    get  GoldProducion(): number { return this._data.GoldProducion; }
    /**
     * 体力上限值
     */
    get  MaxEnergy(): number { return this._data.MaxEnergy; }
    /**
     * 体力恢复的间隔时间（秒）
     */
    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }
    /**
     * 体力恢复的值
     */
    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }

    resolve(tables:Tables)
    {
        this._data.resolve(tables)
    }
    
}




export class TbBoss {
    private _dataMap: Map<number, Boss>
    private _dataList: Boss[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Boss>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Boss
            _v = new Boss(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Boss> { return this._dataMap; }
    getDataList(): Boss[] { return this._dataList; }

    get(key: number): Boss | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbBullet {
    private _dataMap: Map<number, Bullet>
    private _dataList: Bullet[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Bullet>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Bullet
            _v = new Bullet(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Bullet> { return this._dataMap; }
    getDataList(): Bullet[] { return this._dataList; }

    get(key: number): Bullet | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbChapter {
    private _dataMap: Map<number, Chapter>
    private _dataList: Chapter[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Chapter>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Chapter
            _v = new Chapter(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Chapter> { return this._dataMap; }
    getDataList(): Chapter[] { return this._dataList; }

    get(key: number): Chapter | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbEnemy {
    private _dataMap: Map<number, Enemy>
    private _dataList: Enemy[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Enemy>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Enemy
            _v = new Enemy(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Enemy> { return this._dataMap; }
    getDataList(): Enemy[] { return this._dataList; }

    get(key: number): Enemy | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbEnemyUI {
    private _dataMap: Map<number, EnemyUI>
    private _dataList: EnemyUI[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, EnemyUI>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: EnemyUI
            _v = new EnemyUI(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, EnemyUI> { return this._dataMap; }
    getDataList(): EnemyUI[] { return this._dataList; }

    get(key: number): EnemyUI | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbGameMap {
    private _dataMap: Map<number, GameMap>
    private _dataList: GameMap[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, GameMap>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: GameMap
            _v = new GameMap(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, GameMap> { return this._dataMap; }
    getDataList(): GameMap[] { return this._dataList; }

    get(key: number): GameMap | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbGameMode {
    private _dataMap: Map<number, GameMode>
    private _dataList: GameMode[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, GameMode>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: GameMode
            _v = new GameMode(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.ID, _v)
        }
    }

    getDataMap(): Map<number, GameMode> { return this._dataMap; }
    getDataList(): GameMode[] { return this._dataList; }

    get(key: number): GameMode | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbLevel {
    private _dataMap: Map<number, Level>
    private _dataList: Level[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Level>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Level
            _v = new Level(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Level> { return this._dataMap; }
    getDataList(): Level[] { return this._dataList; }

    get(key: number): Level | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbLevelGroup {
    private _dataMap: Map<number, LevelGroup>
    private _dataList: LevelGroup[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, LevelGroup>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: LevelGroup
            _v = new LevelGroup(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, LevelGroup> { return this._dataMap; }
    getDataList(): LevelGroup[] { return this._dataList; }

    get(key: number): LevelGroup | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbMainPlane {
    private _dataMap: Map<number, MainPlane>
    private _dataList: MainPlane[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, MainPlane>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: MainPlane
            _v = new MainPlane(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, MainPlane> { return this._dataMap; }
    getDataList(): MainPlane[] { return this._dataList; }

    get(key: number): MainPlane | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbMainPlaneLv {
    private _dataMap: Map<number, MainPlaneLv>
    private _dataList: MainPlaneLv[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, MainPlaneLv>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: MainPlaneLv
            _v = new MainPlaneLv(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, MainPlaneLv> { return this._dataMap; }
    getDataList(): MainPlaneLv[] { return this._dataList; }

    get(key: number): MainPlaneLv | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbStage {
    private _dataMap: Map<number, Stage>
    private _dataList: Stage[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Stage>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Stage
            _v = new Stage(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Stage> { return this._dataMap; }
    getDataList(): Stage[] { return this._dataList; }

    get(key: number): Stage | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbTrack {
    private _dataMap: Map<number, Track>
    private _dataList: Track[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Track>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Track
            _v = new Track(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Track> { return this._dataMap; }
    getDataList(): Track[] { return this._dataList; }

    get(key: number): Track | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbUnit {
    private _dataMap: Map<number, Unit>
    private _dataList: Unit[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Unit>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Unit
            _v = new Unit(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Unit> { return this._dataMap; }
    getDataList(): Unit[] { return this._dataList; }

    get(key: number): Unit | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbWave {
    private _dataMap: Map<number, Wave>
    private _dataList: Wave[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, Wave>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: Wave
            _v = new Wave(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, Wave> { return this._dataMap; }
    getDataList(): Wave[] { return this._dataList; }

    get(key: number): Wave | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




type JsonLoader = (file: string) => any

export class Tables {
    private _TbGM: TbGM
    /**
     * GM命令表
     */
    get TbGM(): TbGM  { return this._TbGM;}
    private _TbWeapon: TbWeapon
    /**
     * 武器
     */
    get TbWeapon(): TbWeapon  { return this._TbWeapon;}
    private _TbEquipUpgrade: TbEquipUpgrade
    /**
     * 装备位升级
     */
    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}
    private _TbEquip: TbEquip
    /**
     * 装备表
     */
    get TbEquip(): TbEquip  { return this._TbEquip;}
    private _TbItem: TbItem
    /**
     * 道具表
     */
    get TbItem(): TbItem  { return this._TbItem;}
    private _TbGlobalAttr: TbGlobalAttr
    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}
    private _TbBoss: TbBoss
    get TbBoss(): TbBoss  { return this._TbBoss;}
    private _TbBullet: TbBullet
    get TbBullet(): TbBullet  { return this._TbBullet;}
    private _TbChapter: TbChapter
    get TbChapter(): TbChapter  { return this._TbChapter;}
    private _TbEnemy: TbEnemy
    get TbEnemy(): TbEnemy  { return this._TbEnemy;}
    private _TbEnemyUI: TbEnemyUI
    get TbEnemyUI(): TbEnemyUI  { return this._TbEnemyUI;}
    private _TbGameMap: TbGameMap
    get TbGameMap(): TbGameMap  { return this._TbGameMap;}
    private _TbGameMode: TbGameMode
    get TbGameMode(): TbGameMode  { return this._TbGameMode;}
    private _TbLevel: TbLevel
    get TbLevel(): TbLevel  { return this._TbLevel;}
    private _TbLevelGroup: TbLevelGroup
    get TbLevelGroup(): TbLevelGroup  { return this._TbLevelGroup;}
    private _TbMainPlane: TbMainPlane
    get TbMainPlane(): TbMainPlane  { return this._TbMainPlane;}
    private _TbMainPlaneLv: TbMainPlaneLv
    get TbMainPlaneLv(): TbMainPlaneLv  { return this._TbMainPlaneLv;}
    private _TbStage: TbStage
    get TbStage(): TbStage  { return this._TbStage;}
    private _TbTrack: TbTrack
    get TbTrack(): TbTrack  { return this._TbTrack;}
    private _TbUnit: TbUnit
    get TbUnit(): TbUnit  { return this._TbUnit;}
    private _TbWave: TbWave
    get TbWave(): TbWave  { return this._TbWave;}

    constructor(loader: JsonLoader) {
        this._TbGM = new TbGM(loader('tbgm'))
        this._TbWeapon = new TbWeapon(loader('tbweapon'))
        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))
        this._TbEquip = new TbEquip(loader('tbequip'))
        this._TbItem = new TbItem(loader('tbitem'))
        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))
        this._TbBoss = new TbBoss(loader('tbboss'))
        this._TbBullet = new TbBullet(loader('tbbullet'))
        this._TbChapter = new TbChapter(loader('tbchapter'))
        this._TbEnemy = new TbEnemy(loader('tbenemy'))
        this._TbEnemyUI = new TbEnemyUI(loader('tbenemyui'))
        this._TbGameMap = new TbGameMap(loader('tbgamemap'))
        this._TbGameMode = new TbGameMode(loader('tbgamemode'))
        this._TbLevel = new TbLevel(loader('tblevel'))
        this._TbLevelGroup = new TbLevelGroup(loader('tblevelgroup'))
        this._TbMainPlane = new TbMainPlane(loader('tbmainplane'))
        this._TbMainPlaneLv = new TbMainPlaneLv(loader('tbmainplanelv'))
        this._TbStage = new TbStage(loader('tbstage'))
        this._TbTrack = new TbTrack(loader('tbtrack'))
        this._TbUnit = new TbUnit(loader('tbunit'))
        this._TbWave = new TbWave(loader('tbwave'))

        this._TbGM.resolve(this)
        this._TbWeapon.resolve(this)
        this._TbEquipUpgrade.resolve(this)
        this._TbEquip.resolve(this)
        this._TbItem.resolve(this)
        this._TbGlobalAttr.resolve(this)
        this._TbBoss.resolve(this)
        this._TbBullet.resolve(this)
        this._TbChapter.resolve(this)
        this._TbEnemy.resolve(this)
        this._TbEnemyUI.resolve(this)
        this._TbGameMap.resolve(this)
        this._TbGameMode.resolve(this)
        this._TbLevel.resolve(this)
        this._TbLevelGroup.resolve(this)
        this._TbMainPlane.resolve(this)
        this._TbMainPlaneLv.resolve(this)
        this._TbStage.resolve(this)
        this._TbTrack.resolve(this)
        this._TbUnit.resolve(this)
        this._TbWave.resolve(this)
    }
}

