System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EditBox, Label, Node, csproto, DataMgr, MyApp, BaseUI, UILayer, UIMgr, ButtonPlus, DropDown, List, GmButtonUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, GmUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfres(extras) {
    _reporterNs.report("res", "../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../Data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDropDown(extras) {
    _reporterNs.report("DropDown", "../common/components/dropdown/DropDown", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGmButtonUI(extras) {
    _reporterNs.report("GmButtonUI", "./GmButtonUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      EditBox = _cc.EditBox;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      DataMgr = _unresolved_3.DataMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      BaseUI = _unresolved_5.BaseUI;
      UILayer = _unresolved_5.UILayer;
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      ButtonPlus = _unresolved_6.ButtonPlus;
    }, function (_unresolved_7) {
      DropDown = _unresolved_7.DropDown;
    }, function (_unresolved_8) {
      List = _unresolved_8.default;
    }, function (_unresolved_9) {
      GmButtonUI = _unresolved_9.GmButtonUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "019daPIf6xP/LD6RFKEsz1P", "GmUI", undefined);

      __checkObsolete__(['_decorator', 'EditBox', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GmUI", GmUI = (_dec = ccclass('GmUI'), _dec2 = property(_crd && DropDown === void 0 ? (_reportPossibleCrUseOfDropDown({
        error: Error()
      }), DropDown) : DropDown), _dec3 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(Node), _dec6 = property(Label), _dec7 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class GmUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "tabDropDown", _descriptor, this);

          _initializerDefineProperty(this, "cmdBtnList", _descriptor2, this);

          _initializerDefineProperty(this, "sendBtn", _descriptor3, this);

          _initializerDefineProperty(this, "inputParentNode", _descriptor4, this);

          _initializerDefineProperty(this, "logLabel", _descriptor5, this);

          _initializerDefineProperty(this, "clearBtn", _descriptor6, this);

          this._inputNodeList = [];
        }

        static getUrl() {
          return "ui/gm/GmUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getUIOption() {
          return {
            isClickBgHideUI: true
          };
        }

        onLoad() {
          this.inputParentNode.children.forEach(v => {
            this._inputNodeList.push(v.getComponentInChildren(EditBox));
          });
          this.sendBtn.addClick(this.onSendBtnClick, this);
          this.clearBtn.addClick(this.onClearBtnClick, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GM, this.onGmMsg.bind(this));
        }

        onClearBtnClick() {
          this.logLabel.string = "";
        }

        onGmMsg(res) {
          this.log(`接收 => ${res.body.gm.text}`);
        }

        onCmdBtnRender(item, idx) {
          const cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey)[idx];
          const label = item.getComponentInChildren(Label);
          label.string = cmdInfo.cfg.name;
        }

        onCmdBtnClick(item, idx) {
          const cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.name !== "")[idx];
          this._inputNodeList[0].string = cmdInfo.cfg.cmd;
          this._inputNodeList[1].placeholder = cmdInfo.cfg.desc;
        }

        onDropDownOptionRender(nd, optKey) {
          const cfg = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(optKey);
          nd.getComponentInChildren(Label).string = cfg[0].cfg.tabName;
        }

        onDropDownOptionClick(optKey) {
          const cfg = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(optKey).filter(v => v.cfg.name !== "");
          this.cmdBtnList.numItems = cfg.length;
        }

        log(msg) {
          this.logLabel.string += `[${new Date().toLocaleString()}] ${msg}\n`;
        }

        onSendBtnClick() {
          this._inputNodeList.forEach(v => {
            if (v.string == v.placeholder) {
              v.string = "";
            }
          });

          const cmd = this._inputNodeList[0].string;
          const args = this._inputNodeList[1].string;
          let target = this._inputNodeList[2].string;

          if (target !== "") {
            target = `[destuin ${target}]`;
          }

          const cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.cmd === cmd)[0];

          if (cmdInfo != null && cmdInfo.onSendClick) {
            const res = cmdInfo.onSendClick(args);
            this.log(`[${cmd}] 发送 => ${res}`);
          } else {
            const gmStr = cmd + " " + target + " " + args;
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD.CS_CMD_GM, {
              gm: {
                gm_str: gmStr
              }
            });
            this.log(`[${cmd}] 发送 => ${gmStr}`);
          }
        } // 显示 UI 的方法，需要子类实现


        onShow(...args) {
          const tabIDList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.tabIDList;
          this.tabDropDown.init(tabIDList, this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this));
          this.cmdBtnList.numItems = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).length;
          this.onClearBtnClick();
          return;
        }

        // 隐藏 UI 的方法，需要子类实现
        onHide(...args) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && GmButtonUI === void 0 ? (_reportPossibleCrUseOfGmButtonUI({
            error: Error()
          }), GmButtonUI) : GmButtonUI);
          return;
        }

        // 关闭 UI 的方法，需要子类实现
        onClose(...args) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && GmButtonUI === void 0 ? (_reportPossibleCrUseOfGmButtonUI({
            error: Error()
          }), GmButtonUI) : GmButtonUI);
          return;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "tabDropDown", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "cmdBtnList", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "sendBtn", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "inputParentNode", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "logLabel", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "clearBtn", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c6e8acb2d92afe8c7ad989aa6a8f474a5d773f60.js.map