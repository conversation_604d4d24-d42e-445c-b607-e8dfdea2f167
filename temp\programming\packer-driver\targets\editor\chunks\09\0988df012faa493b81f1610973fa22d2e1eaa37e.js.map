{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts"], "names": ["_decorator", "BossUnitBase", "ccclass", "property", "BossCollider", "create", "owner", "data", "setCollidePosition", "x", "y", "setCollideAble", "enabled", "collide<PERSON>omp", "updateGameLogic", "deltaTime", "m_comps", "for<PERSON>ach", "comp", "update"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,Y;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,Y,WADpBF,OAAO,CAAC,cAAD,C,gBAAR,MACqBE,YADrB;AAAA;AAAA,wCACuD;AAEnDC,QAAAA,MAAM,CAACC,KAAD,EAAaC,IAAb,EAA8B,CAChC;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACH;;AAEDC,QAAAA,kBAAkB,CAACC,CAAD,EAAYC,CAAZ,EAA6B,CAC3C;AACH;;AAEDC,QAAAA,cAAc,CAACC,OAAD,EAAyB;AACnC,cAAI,KAAKC,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBD,OAAjB,GAA2BA,OAA3B;AACH;AACJ;;AAEDE,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,eAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACC,MAAL,CAAYJ,SAAZ;AACH,WAFD;AAGH;;AA7BkD,O", "sourcesContent": ["import { _decorator, Component, v2, Vec3 } from 'cc';\r\nimport BossUnitBase from './BossUnitBase';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossCollider')\r\nexport default class BossCollider extends BossUnitBase {\r\n\r\n    create(owner: any, data: any): void {\r\n        // this.owner = owner;\r\n        // if (!this.collideComp) {\r\n        //     this.collideComp = this.addComp(ColliderComp, new ColliderComp());\r\n        // }\r\n        // this.collideComp.init(this);\r\n        // this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));\r\n\r\n        // this.m_comps.forEach((comp) => {\r\n        //     comp.init(this);\r\n        // });\r\n    }\r\n\r\n    setCollidePosition(x: number, y: number): void {\r\n        // this.collideComp?.setPos(x, y);\r\n    }\r\n\r\n    setCollideAble(enabled: boolean): void {\r\n        if (this.collideComp) {\r\n            this.collideComp.enabled = enabled;\r\n        }\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n    }\r\n}"]}