
import { LoginInfo } from '../Network/NetMgr';
import { logInfo } from '../Utils/Logger';
import { IPlatformSDK, PlatformSDKUserInfo } from './IPlatformSDK'
import csproto from '../AutoGen/PB/cs_proto.js';

export class WXLogin implements IPlatformSDK {
    private authButton = null;
    login(cb: (err: string, req: LoginInfo) => void) {
        logInfo("Login", "start wx login")
        // @ts-ignore
        wx.login({
            complete(res) {
                logInfo("Login", `complete wx login ${res.errMsg} ${res.code}`)
                cb(res.code ? null : res.errMsg, {
                    accountType: csproto.cs.ACCOUNT_TYPE.ACCOUNT_TYPE_WXMINIGAME,
                    code: res.code,
                    serverAddr: "wss://m2test.5600.online:9101/",
                })
            }
        })
    }

    showUserInfoButton() {
        if (this.authButton) {
            this.authButton.show();
        }
    }

    hideUserInfoButton() {
        if (this.authButton) {
            this.authButton.hide();
        }
    }

    getUserInfo(cb: (err: string, req: PlatformSDKUserInfo, hasTap) => void, param: any) {
        let THIS = this;
        // @ts-ignore
        wx.getSetting({
            success(res) {
                console.error('WXLogin get userinfo auth setting', res.authSetting)
                if (!res.authSetting['scope.userInfo']) {
                    console.error('WXLogin start authorize userinfo')
                    // @ts-ignore
                    let sysInfo = wx.getSystemInfoSync();
                    // @ts-ignore
                    var button = wx.createUserInfoButton({
                        type: 'text',
                        text: '',
                        //image : "SDK/WXGetUserInfo.png",
                        plain: true,
                        style: {
                            left: sysInfo.screenWidth * param.x,//0,//buttonPosition.x,
                            top: sysInfo.screenHeight * (1 - param.y) + (sysInfo.safeArea.top - 20),//1334-100,//buttonPosition.y+buttonPosition.height,
                            width: sysInfo.screenWidth * param.w,//750,//buttonPosition.width,
                            height: sysInfo.screenHeight * param.h,//100,//buttonPosition.height,
                            lineHeight: 40,
                            backgroundColor: '#ff0000',
                            color: '#ffffff',
                            textAlign: 'center',
                            fontSize: 16,
                            borderRadius: 4
                        }
                    })
                    button.show();
                    button.onTap((res) => {
                        if (res.userInfo) {
                            button.destroy();
                            THIS.authButton = null;
                            console.log('WXLogin get wx userinfo success', res.userInfo)
                            THIS.getUserInfo__(res.userInfo, cb, true)
                        }
                    })
                    THIS.authButton = button;
                }
                else {
                    THIS.getUserInfo_(cb, false);
                }
            }
        })
    }
    private getUserInfo__(userInfo: any, cb: (err: string, req: PlatformSDKUserInfo, hasTap: boolean) => void, hasTap: boolean) {
        cb(null, {
            name: userInfo.nickName,
            icon: userInfo.avatarUrl,
        }, true)
    }
    private getUserInfo_(cb: (err: string, req: PlatformSDKUserInfo, hasTap: boolean) => void, hasTap: boolean) {
        let THIS = this
        // @ts-ignore
        wx.getUserInfo({
            complete(res) {
                console.error("WXLogin getUserInfo complete")
                if (res.userInfo) {
                    THIS.getUserInfo__(res.userInfo, cb, hasTap)
                }
                else {
                    cb("get userinfo error", null, false)
                }
            }
        })
    }
}