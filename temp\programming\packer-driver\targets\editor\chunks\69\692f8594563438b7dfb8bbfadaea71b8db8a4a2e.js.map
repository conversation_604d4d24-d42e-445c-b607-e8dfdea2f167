{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts"], "names": ["Label", "ProgressBar", "_decorator", "tween", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "LoadingUI", "_loadTotal", "_loadCount", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onShow", "runSec", "startPercent", "bar", "progress", "percent", "string", "onHide", "hideSec", "lbPercent", "Promise", "resolve", "to", "start", "end", "current", "ratio", "value", "toFixed", "call", "setTimeout", "onClose", "args", "updateProgress"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AAChCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;2BAEjBM,S,WADZF,OAAO,CAAC,WAAD,C,UAIHC,QAAQ,CAACN,WAAD,C,UAERM,QAAQ,CAACP,KAAD,C,UAERO,QAAQ,CAACP,KAAD,C,2BARb,MACaQ,SADb;AAAA;AAAA,4BACsC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAUlC;AACA;AAXkC,eAalCC,UAbkC,GAarB,CAbqB;AAAA,eAclCC,UAdkC,GAcrB,CAdqB;AAAA;;AACd,eAANC,MAAM,GAAW;AAAE,iBAAO,cAAP;AAAuB;;AAClC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AAc5D;AACJ;AACA;AACA;AACA;AACgB,cAANC,MAAM,CAACC,MAAD,EAAiBC,YAAjB,EAAsD;AAC9D,eAAKC,GAAL,CAASC,QAAT,GAAoB,CAApB;AACA,eAAKC,OAAL,CAAaC,MAAb,GAAsB,OAAtB;AACH;;AAEW,cAANC,MAAM,CAACC,OAAD,EAAiC;AACzC,cAAIC,SAAgB,GAAG,KAAKJ,OAA5B,CADyC,CAEzC;AACA;;AACA,iBAAO,IAAIK,OAAJ,CAAmBC,OAAD,IAAa;AAClCtB,YAAAA,KAAK,CAAC,KAAKc,GAAN,CAAL,CAAgBS,EAAhB,CAAmBJ,OAAnB,EAA4B;AAAEJ,cAAAA,QAAQ,EAAE;AAAZ,aAA5B,EACI;AACIA,cAAAA,QAAQ,EAAE,CAACS,KAAD,EAAgBC,GAAhB,EAA6BC,OAA7B,EAA8CC,KAA9C,KAAwE;AAC9E,oBAAIC,KAAa,GAAGJ,KAAK,GAAG,CAACC,GAAG,GAAGD,KAAP,IAAgBG,KAA5C;AACAP,gBAAAA,SAAS,CAACH,MAAV,GAAmB,CAACW,KAAK,GAAG,GAAT,EAAcC,OAAd,CAAsB,CAAtB,IAA2B,GAA9C;AACA,uBAAOD,KAAP;AACH;AALL,aADJ,EAQEE,IARF,CAQO,MAAM;AACTC,cAAAA,UAAU,CAACT,OAAD,EAAU,GAAV,CAAV;AACA,mBAAKR,GAAL,CAASC,QAAT,GAAoB,CAApB;AACH,aAXD,EAWGS,KAXH;AAYH,WAbM,CAAP;AAcH;;AAEY,cAAPQ,OAAO,CAAC,GAAGC,IAAJ,EAAgC,CAAG;;AAEhDC,QAAAA,cAAc,CAAClB,OAAD,EAAkB;AAC5B,eAAKF,GAAL,CAASC,QAAT,GAAoBC,OAApB;AACA,eAAKA,OAAL,CAAaC,MAAb,GAAsB,CAAC,KAAKH,GAAL,CAASC,QAAT,GAAoB,GAArB,EAA0Bc,OAA1B,CAAkC,CAAlC,IAAuC,GAA7D;AACH,SAnDiC,CAqDlC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA9DkC,O;;;;;iBAIf,I;;;;;;;iBAEF,I;;;;;;;iBAEF,I", "sourcesContent": ["import { Label, ProgressBar, _decorator, tween } from \"cc\";\nimport { BaseUI, UILayer } from \"./UIMgr\";\n\nconst { ccclass, property } = _decorator;\n@ccclass(\"LoadingUI\")\nexport class LoadingUI extends BaseUI {\n    public static getUrl(): string { return \"ui/LoadingUI\" };\n    public static getLayer(): UILayer { return UILayer.Default }\n    @property(ProgressBar)\n    bar: ProgressBar = null;\n    @property(Label)\n    percent: Label = null;\n    @property(Label)\n    title: Label = null;\n\n    // runSec: number = 0;\n    // initPercent: number = 0;\n\n    _loadTotal = 0;\n    _loadCount = 0;\n\n    /**\n     *\n     * @param runSec 运行时间\n     * @param startPercent 开始的百分比数- 0.991=99.1%\n     */\n    async onShow(runSec: number, startPercent: number): Promise<void> {\n        this.bar.progress = 0\n        this.percent.string = \"00.0%\"\n    }\n\n    async onHide(hideSec: number): Promise<void> {\n        let lbPercent: Label = this.percent;\n        // this.runSec = 0;\n        // this.initPercent = 0;\n        return new Promise<void>((resolve) => {\n            tween(this.bar).to(hideSec, { progress: 1 },\n                {\n                    progress: (start: number, end: number, current: number, ratio: number): number => {\n                        let value: number = start + (end - start) * ratio;\n                        lbPercent.string = (value * 100).toFixed(1) + \"%\";\n                        return value;\n                    },\n                }\n            ).call(() => {\n                setTimeout(resolve, 500);\n                this.bar.progress = 1;\n            }).start();\n        });\n    }\n\n    async onClose(...args: any[]): Promise<void> { }\n\n    updateProgress(percent: number) {\n        this.bar.progress = percent;\n        this.percent.string = (this.bar.progress * 100).toFixed(1) + \"%\";\n    }\n\n    // update(dt: number) {\n    //     if (this.runSec == 0) {\n    //         return;\n    //     }\n    //     this.bar.progress += (dt * this.initPercent) / this.runSec;\n    //     if (this.bar.progress > this.initPercent) {\n    //         this.bar.progress = this.initPercent;\n    //     }\n    //     this.percent.string = (this.bar.progress * 100).toFixed(1) + \"%\";\n    // }\n}\n"]}