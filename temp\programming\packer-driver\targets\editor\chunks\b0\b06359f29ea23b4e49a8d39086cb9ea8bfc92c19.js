System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, LevelDataEventCondtionType, LevelDataEventCondtionDelayDistance, LevelDataEventCondtionDelayTime, LevelDataEventCondtionWave, _crd;

  function newCondition(obj) {
    switch (obj._type) {
      case (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
        error: Error()
      }), LevelDataEventCondtionType) : LevelDataEventCondtionType).DelayTime:
        return Object.assign(new (_crd && LevelDataEventCondtionDelayTime === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionDelayTime({
          error: Error()
        }), LevelDataEventCondtionDelayTime) : LevelDataEventCondtionDelayTime)(obj.comb), obj);

      case (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
        error: Error()
      }), LevelDataEventCondtionType) : LevelDataEventCondtionType).DelayDistance:
        return Object.assign(new (_crd && LevelDataEventCondtionDelayDistance === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionDelayDistance({
          error: Error()
        }), LevelDataEventCondtionDelayDistance) : LevelDataEventCondtionDelayDistance)(obj.comb), obj);

      case (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
        error: Error()
      }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave:
        return Object.assign(new (_crd && LevelDataEventCondtionWave === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionWave({
          error: Error()
        }), LevelDataEventCondtionWave) : LevelDataEventCondtionWave)(obj.comb), obj);
    }
  }

  function _reportPossibleCrUseOfLevelDataEventCondtion(extras) {
    _reporterNs.report("LevelDataEventCondtion", "./LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionComb(extras) {
    _reporterNs.report("LevelDataEventCondtionComb", "./LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "./LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionDelayDistance(extras) {
    _reporterNs.report("LevelDataEventCondtionDelayDistance", "./LevelDataEventCondtionDelayDistance", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionDelayTime(extras) {
    _reporterNs.report("LevelDataEventCondtionDelayTime", "./LevelDataEventCondtionDelayTime", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionWave(extras) {
    _reporterNs.report("LevelDataEventCondtionWave", "./LevelDataEventCondtionWave", _context.meta, extras);
  }

  _export("newCondition", newCondition);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      LevelDataEventCondtionType = _unresolved_2.LevelDataEventCondtionType;
    }, function (_unresolved_3) {
      LevelDataEventCondtionDelayDistance = _unresolved_3.LevelDataEventCondtionDelayDistance;
    }, function (_unresolved_4) {
      LevelDataEventCondtionDelayTime = _unresolved_4.LevelDataEventCondtionDelayTime;
    }, function (_unresolved_5) {
      LevelDataEventCondtionWave = _unresolved_5.LevelDataEventCondtionWave;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "45e21H2xyhJbLxBUyKP82R2", "newCondition", undefined);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b06359f29ea23b4e49a8d39086cb9ea8bfc92c19.js.map