{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "Component", "EDITOR", "Bullet", "EmitterData", "ObjectPool", "BulletSystem", "eEmitterActionType", "ccclass", "executeInEditMode", "property", "playOnFocus", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "Emitter", "type", "displayName", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "updateInEditor", "_isActive", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "_perEmitStartTime", "_perEmitBulletQueue", "_perEmitAccumulator", "isActive", "status", "isEmitting", "statusElapsedTime", "totalElapsedTime", "start", "onCreateEmitter", "resetProperties", "update", "dt", "tick", "tickBullets", "tickActionRunners", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "emitterData", "changeStatus", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "console", "log", "toFixed", "length", "i", "j", "push", "index", "perEmitIndex", "targetTime", "sort", "a", "b", "emitSingle", "processPerEmitQueue", "deltaTime", "nextBullet", "timeFromStart", "expectedTime", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "bulletPrefab", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "applyAction", "actType", "actValue", "Emitter_Active", "Emitter_InitialDelay", "Emitter_Prewarm", "Emitter_PrewarmDuration", "Emitter_Duration", "Emitter_ElapsedTime", "Emitter_Loop", "Emitter_LoopInterval", "Emitter_PerEmitInterval", "Emitter_PerEmitCount", "Emitter_PerEmitOffsetX", "Emitter_Angle", "Emitter_Count", "isInScreen", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDb,U;OACxD;AAAEc,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCd,I;;gCAEnCe,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZP,OAAO,CAAC,SAAD,C,UAOHE,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEhB,MAAP;AAAeiB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRP,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBC,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,gBATZR,iB,UACAE,W,gCAFD,MAGaI,OAHb,SAG6Bd,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAUnC;AAVmC,eAW5BiB,cAX4B,GAWD,IAXC;AAWO;AAXP,eAY5BC,SAZ4B,GAYN,KAZM;AAYO;AAZP,eAa5BC,MAb4B,GAaT,IAbS;AAaO;AAbP,eAe5BC,YAf4B,GAeJ,GAfI;AAeO;AAfP,eAgB5BC,eAhB4B,GAgBD,GAhBC;AAgBO;AAhBP,eAkB5BC,YAlB4B,GAkBJ,GAlBI;AAkBO;AAlBP,eAmB5BC,YAnB4B,GAmBJ,GAnBI;AAmBO;AAnBP,eAoB5BC,SApB4B,GAoBP,GApBO;AAoBO;AApBP,eAqB5BC,YArB4B,GAqBJ,GArBI;AAqBO;AArBP,eAuB5BC,YAvB4B,GAuBJ,CAvBI;AAuBO;AAvBP,eAwB5BC,eAxB4B,GAwBD,GAxBC;AAwBO;AAxBP,eAyB5BC,cAzB4B,GAyBF,GAzBE;AAyBO;AAzBP,eA2B5BC,KA3B4B,GA2BX,CAAC,EA3BU;AA2BO;AA3BP,eA4B5BC,KA5B4B,GA4BX,CA5BW;AA4BO;AA5BP,eA6B5BC,GA7B4B,GA6BX,EA7BW;AA6BO;AA7BP,eA8B5BC,MA9B4B,GA8BV,GA9BU;AA8BO;AA9BP,eAgC5BC,cAhC4B,GAgCD,KAhCC;AAgCM;AAhCN,eAiCzBC,SAjCyB,GAiCJ,KAjCI;AAAA,eAkCzBC,OAlCyB,GAkCCtB,cAAc,CAACuB,IAlChB;AAAA,eAmCzBC,kBAnCyB,GAmCI,CAnCJ;AAAA,eAoCzBC,iBApCyB,GAoCG,CApCH;AAAA,eAqCzBC,WArCyB,GAqCF,KArCE;AAAA,eAsCzBC,aAtCyB,GAsCD,CAtCC;AAwCnC;AAxCmC,eAyCzBC,iBAzCyB,GAyCG,CAzCH;AAAA,eA0CzBC,mBA1CyB,GA0C+D,EA1C/D;AAAA,eA2CzBC,mBA3CyB,GA2CK,CA3CL;AAAA;;AA2CQ;AAE/B,YAARC,QAAQ,GAAY;AACpB,iBAAO,KAAKV,SAAZ;AACH;;AAES,YAANW,MAAM,GAAmB;AACzB,iBAAO,KAAKV,OAAZ;AACH;;AAEa,YAAVW,UAAU,GAAY;AACtB,iBAAO,KAAKP,WAAZ;AACH;;AAEoB,YAAjBQ,iBAAiB,GAAW;AAC5B,iBAAO,KAAKV,kBAAZ;AACH;;AAEmB,YAAhBW,gBAAgB,GAAW;AAC3B,iBAAO,KAAKV,iBAAZ;AACH;;AAESW,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACA,eAAKhB,SAAL,GAAiB,IAAjB;AACA,eAAKiB,eAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChC,cAAIpD,MAAM,IAAI,KAAKgC,cAAnB,EAAmC;AAC/B,iBAAKqB,IAAL,CAAUD,EAAV;AACA;AAAA;AAAA,8CAAaE,WAAb,CAAyBF,EAAzB;AACA;AAAA;AAAA,8CAAaG,iBAAb,CAA+BH,EAA/B;AACH;AACJ;;AAEMI,QAAAA,aAAa,GAAG;AACnB,eAAKxB,cAAL,GAAsB,IAAtB;AACH;;AAEMyB,QAAAA,eAAe,GAAG;AACrB,eAAKzB,cAAL,GAAsB,IAAtB;AACA,eAAKkB,eAAL;AACH;;AAEMQ,QAAAA,mBAAmB,GAAG;AACzB,eAAK1B,cAAL,GAAsB,KAAtB;;AACA,cAAI;AAAA;AAAA,4CAAa2B,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ,SA7FkC,CA+FnC;;;AACUX,QAAAA,eAAe,GAAG;AACxB,eAAKlC,cAAL,GAAsB,KAAK8C,WAAL,CAAiB9C,cAAvC;AACA,eAAKC,SAAL,GAAiB,KAAK6C,WAAL,CAAiB7C,SAAlC;AACA,eAAKC,MAAL,GAAc,KAAK4C,WAAL,CAAiB5C,MAA/B;AACA,eAAKC,YAAL,GAAoB,KAAK2C,WAAL,CAAiB3C,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAK0C,WAAL,CAAiB1C,eAAxC;AACA,eAAKC,YAAL,GAAoB,KAAKyC,WAAL,CAAiBzC,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKwC,WAAL,CAAiBxC,YAArC;AACA,eAAKC,SAAL,GAAiB,KAAKuC,WAAL,CAAiBvC,SAAlC;AACA,eAAKC,YAAL,GAAoB,KAAKsC,WAAL,CAAiBtC,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKqC,WAAL,CAAiBrC,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAKoC,WAAL,CAAiBpC,eAAxC;AACA,eAAKC,cAAL,GAAsB,KAAKmC,WAAL,CAAiBnC,cAAvC;AACA,eAAKC,KAAL,GAAa,KAAKkC,WAAL,CAAiBlC,KAA9B;AACA,eAAKC,KAAL,GAAa,KAAKiC,WAAL,CAAiBjC,KAA9B;AACA,eAAKC,GAAL,GAAW,KAAKgC,WAAL,CAAiBhC,GAA5B;AACA,eAAKC,MAAL,GAAc,KAAK+B,WAAL,CAAiB/B,MAA/B;AACH;AACD;AACJ;AACA;;;AACIgC,QAAAA,YAAY,CAACnB,MAAD,EAAyB;AACjC,eAAKV,OAAL,GAAeU,MAAf;AACA,eAAKR,kBAAL,GAA0B,CAA1B,CAFiC,CAGjC;;AACA,eAAKK,mBAAL,GAA2B,EAA3B;AACH;;AAESuB,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKzB,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKd,YAApD;AACH;;AAES2C,QAAAA,aAAa,GAAG;AACtB,eAAK3B,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAES4B,QAAAA,YAAY,GAAG;AACrB,eAAK5B,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAK6B,sBAAL,GAHqB,CAIrB;;AACA,eAAK1B,mBAAL,GAA2B,EAA3B;AACH;;AAES2B,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,cAAI,KAAK3C,eAAL,GAAuB,CAA3B,EAA8B;AAC1B;AACA,iBAAKc,iBAAL,GAAyB,KAAKJ,kBAA9B;AACA,iBAAKM,mBAAL,GAA2B,CAA3B;AACA4B,YAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8B,KAAK/B,iBAAL,CAAuBgC,OAAvB,CAA+B,CAA/B,CAAkC,0BAAyB,KAAK/B,mBAAL,CAAyBgC,MAAO,EAAtI,EAJ0B,CAM1B;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7C,KAAzB,EAAgC6C,CAAC,EAAjC,EAAqC;AACjC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlD,YAAzB,EAAuCkD,CAAC,EAAxC,EAA4C;AACxC,qBAAKlC,mBAAL,CAAyBmC,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEH,CADmB;AAE1BI,kBAAAA,YAAY,EAAEH,CAFY;AAG1BI,kBAAAA,UAAU,EAAE,CAHc,CAGZ;;AAHY,iBAA9B;;AAKAT,gBAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgBG,CAAE,IAAGC,CAAE,qBAAoBA,CAAE,EAA1D;AACH;AACJ,aAhByB,CAkB1B;;;AACA,iBAAKlC,mBAAL,CAAyBuC,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACH,YAAF,GAAiBI,CAAC,CAACJ,YAA3D;;AACAR,YAAAA,OAAO,CAACC,GAAR,CAAa,uBAAsB,KAAK9B,mBAAL,CAAyBgC,MAAO,EAAnE;AACH,WArBD,MAsBK;AACD;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7C,KAAzB,EAAgC6C,CAAC,EAAjC,EAAqC;AACjC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlD,YAAzB,EAAuCkD,CAAC,EAAxC,EAA4C;AACxC,qBAAKQ,UAAL,CAAgBT,CAAhB,EAAmBC,CAAnB;AACH;AACJ;AACJ;AACJ;;AAESS,QAAAA,mBAAmB,CAACC,SAAD,EAA0B;AACnD,cAAI,KAAK5C,mBAAL,CAAyBgC,MAAzB,KAAoC,CAAxC,EAA2C;AACvC;AACH,WAHkD,CAKnD;;;AACA,eAAK/B,mBAAL,IAA4B2C,SAA5B,CANmD,CAQnD;;AACA,iBAAO,KAAK5C,mBAAL,CAAyBgC,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,kBAAMa,UAAU,GAAG,KAAK7C,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,kBAAM8C,aAAa,GAAG,KAAKnD,kBAAL,GAA0B,KAAKI,iBAArD;AACA,kBAAMgD,YAAY,GAAG,KAAK9D,eAAL,GAAuB4D,UAAU,CAACR,YAAvD;;AAEA,gBAAIS,aAAa,IAAIC,YAArB,EAAmC;AAC/B;AACA,mBAAK/C,mBAAL,CAAyBgD,KAAzB;;AAEAnB,cAAAA,OAAO,CAACC,GAAR,CAAa,mBAAkBe,UAAU,CAACR,YAAa,YAAW,KAAK1C,kBAAL,CAAwBoC,OAAxB,CAAgC,CAAhC,CAAmC,iBAAgB,CAAC,KAAKhC,iBAAL,GAAyBgD,YAA1B,EAAwChB,OAAxC,CAAgD,CAAhD,CAAmD,WAAU,CAACe,aAAa,GAAGC,YAAjB,EAA+BhB,OAA/B,CAAuC,CAAvC,CAA0C,EAA5N;AAEA,mBAAKW,UAAL,CAAgBG,UAAU,CAACT,KAA3B,EAAkCS,UAAU,CAACR,YAA7C;AACH,aAPD,MAOO;AACH;AACA;AACH;AACJ;AACJ;;AAESY,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKtB,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESc,QAAAA,UAAU,CAACN,KAAD,EAAeC,YAAf,EAAqC;AACrDR,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA,gBAAMoB,SAAS,GAAG,KAAKC,iBAAL,CAAuBf,KAAvB,CAAlB;AACA,gBAAMgB,QAAQ,GAAG,KAAKC,gBAAL,CAAsBjB,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKiB,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACf,KAAD,EAA0C;AACvD;AACA,gBAAMmB,WAAW,GAAG,KAAKnE,KAAL,GAAa,CAAb,GAAkB,KAAKC,GAAL,IAAY,KAAKD,KAAL,GAAa,CAAzB,CAAD,GAAgCgD,KAAhC,GAAwC,KAAK/C,GAAL,GAAW,CAApE,GAAwE,CAA5F;AACA,gBAAMmE,MAAM,GAAGvF,gBAAgB,CAAC,KAAKkB,KAAL,GAAaoE,WAAd,CAA/B;AACA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACjB,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,gBAAMnD,cAAc,GAAI,KAAKF,YAAL,GAAoB,CAApB,GAAyB,KAAKE,cAAL,IAAuB,KAAKF,YAAL,GAAoB,CAA3C,CAAD,GAAkDqD,YAAlD,GAAiE,KAAKnD,cAAL,GAAsB,CAA/G,GAAmH,CAA3I;;AACA,cAAI,KAAKI,MAAL,IAAe,CAAnB,EAAsB;AAClB,mBAAO;AAAEmE,cAAAA,CAAC,EAAEvE,cAAL;AAAqB0E,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBf,KAAvB,CAAlB;AACA,iBAAO;AACHqB,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKnE,MAAnB,GAA4BJ,cAD5B;AAEH0E,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKtE;AAFnB,WAAP;AAIH;;AAEDgE,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAKU,YAAV,EAAwB;AACpBjC,YAAAA,OAAO,CAACkC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,gBAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAa/C,YAAhC,EAA8C,KAAK4C,YAAnD,CAAnB;;AACA,cAAI,CAACE,UAAL,EAAiB;AACbnC,YAAAA,OAAO,CAACqC,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,gBAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACTtC,YAAAA,OAAO,CAACqC,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAI9G,MAAJ,EAAY;AACRyG,YAAAA,UAAU,CAACM,IAAX,GAAkBlG,OAAO,CAACmG,mBAA1B;AACH,WAtBiF,CAwBlF;;;AACA,gBAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAV,UAAAA,UAAU,CAACW,gBAAX,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4BV,MAA5B,EAhCkF,CAkClF;;AACAA,UAAAA,MAAM,CAACW,KAAP,CAAaC,UAAb,GAA0B7G,gBAAgB,CAACwF,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAU,UAAAA,MAAM,CAACW,KAAP,CAAaG,KAAb,IAAsB,KAAKnG,SAA3B,CApCkF,CAqClF;;AAEA,iBAAOkF,UAAP;AACH;;AAEMkB,QAAAA,WAAW,CAACC,OAAD,EAA8BC,QAA9B,EAAgD;AAC9D,kBAAQD,OAAR;AACI,iBAAK;AAAA;AAAA,0DAAmBE,cAAxB;AACI,mBAAK7F,SAAL,GAAiB4F,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBE,oBAAxB;AACI,mBAAK5G,YAAL,GAAoB0G,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBG,eAAxB;AACI,mBAAK/G,SAAL,GAAiB4G,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBI,uBAAxB;AACI,mBAAK7G,eAAL,GAAuByG,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBK,gBAAxB;AACI,mBAAK7G,YAAL,GAAoBwG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBM,mBAAxB;AACI,mBAAK/F,kBAAL,GAA0ByF,QAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBO,YAAxB;AACI,mBAAKlH,MAAL,GAAc2G,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAtC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBQ,oBAAxB;AACI,mBAAK7G,YAAL,GAAoBqG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBS,uBAAxB;AACI,mBAAK5G,eAAL,GAAuBmG,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBU,oBAAxB;AACI,mBAAK9G,YAAL,GAAoBoG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBW,sBAAxB;AACI,mBAAK7G,cAAL,GAAsBkG,QAAtB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBY,aAAxB;AACI,mBAAK7G,KAAL,GAAaiG,QAAb;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBa,aAAxB;AACI,mBAAK7G,KAAL,GAAagG,QAAb;AACA;AACJ;;AACA;AAAS;AAzCb;AA2CH;AAED;AACJ;AACA;;;AACcc,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMtF,QAAAA,IAAI,CAACgC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKpD,SAAV,EAAqB;AACjB;AACH;;AAED,eAAKG,kBAAL,IAA2BiD,SAA3B;AACA,eAAKhD,iBAAL,IAA0BgD,SAA1B;;AAEA,kBAAQ,KAAKnD,OAAb;AAEI,iBAAKtB,cAAc,CAACuB,IAApB;AACI,mBAAKyG,gBAAL;AACA;;AACJ,iBAAKhI,cAAc,CAACiI,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKlI,cAAc,CAACmI,QAApB;AACI,mBAAKC,oBAAL,CAA0B3D,SAA1B;AACA;;AACJ,iBAAKzE,cAAc,CAACqI,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKtI,cAAc,CAACuI,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKxG,kBAAL,IAA2B,KAAKjB,YAApC,EAAkD;AAC9C,iBAAK4C,YAAL,CAAkBnD,cAAc,CAACiI,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAK7H,SAAV,EACI,KAAK8C,YAAL,CAAkBnD,cAAc,CAACmI,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK3G,kBAAL,IAA2B,KAAKhB,eAApC,EAAqD;AACjD,mBAAK2C,YAAL,CAAkBnD,cAAc,CAACmI,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAAC3D,SAAD,EAAoB;AAC9C,cAAI,KAAKjD,kBAAL,GAA0B,KAAKf,YAAnC,EAAiD;AAC7C,iBAAK6C,YAAL;AACA,gBAAI,KAAKhD,MAAT,EACI,KAAK6C,YAAL,CAAkBnD,cAAc,CAACqI,cAAjC,EADJ,KAGI,KAAKlF,YAAL,CAAkBnD,cAAc,CAACuI,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAK7G,WAAV,EAAuB;AACnB,iBAAK2B,aAAL;AACH,WAFD,MAGK,IAAI,KAAK3B,WAAL,IAAoB,KAAKF,kBAAL,IAA2B,KAAKG,aAAxD,EAAuE;AACxE;AACA,iBAAKmD,OAAL;AACA,iBAAK1B,gBAAL;AACH,WAlB6C,CAoB9C;;;AACA,eAAKoB,mBAAL,CAAyBC,SAAzB;AACH;;AAES6D,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAK9G,kBAAL,IAA2B,KAAKZ,YAApC,EAAkD;AAC9C,iBAAKuC,YAAL,CAAkBnD,cAAc,CAACmI,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AAnbkC,O,UAE5BpC,mB,GAA6B,U;;;;;iBAGb,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/EmitterData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { eEmitterActionType } from '../data/EventActionData';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property({type: EmitterData, displayName: \"Emitter Data\"})\r\n    emitterData: EmitterData = null;\r\n\r\n    // 以下属性重新定义作为可修改的属性\r\n    public isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n    public isPreWarm : boolean = false;       // 是否预热\r\n    public isLoop : boolean = true;           // 是否循环\r\n\r\n    public initialDelay : number = 0.0;       // 初始延迟\r\n    public preWarmDuration : number = 0.0;    // 预热持续时长\r\n\r\n    public emitDuration : number = 1.0;       // 发射器持续时间\r\n    public emitInterval : number = 1.0;       // 发射间隔\r\n    public emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)\r\n    public loopInterval : number = 0.0;       // 循环间隔\r\n\r\n    public perEmitCount : number = 1;         // 单次发射数量\r\n    public perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔\r\n    public perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移\r\n\r\n    public angle : number = -90;              // 发射角度: -90朝下\r\n    public count : number = 1;                // 发射条数(弹道数量)\r\n    public arc   : number = 60;               // 发射范围(弧度范围)\r\n    public radius : number = 1.0;             // 发射半径\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n    protected _isActive: boolean = false;\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitStartTime: number = 0;\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n    protected _perEmitAccumulator: number = 0; // Accumulates time for fixed-step per-emit timing\r\n\r\n    get isActive(): boolean {\r\n        return this._isActive;\r\n    }\r\n\r\n    get status(): eEmitterStatus {\r\n        return this._status;\r\n    }\r\n\r\n    get isEmitting(): boolean {\r\n        return this._isEmitting;\r\n    }\r\n\r\n    get statusElapsedTime(): number {\r\n        return this._statusElapsedTime;\r\n    }\r\n\r\n    get totalElapsedTime(): number {\r\n        return this._totalElapsedTime;\r\n    }\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n        this._isActive = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        if (EDITOR && this.updateInEditor) {\r\n            this.tick(dt);\r\n            BulletSystem.tickBullets(dt);\r\n            BulletSystem.tickActionRunners(dt);\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        this.isOnlyInScreen = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm = this.emitterData.isPreWarm;\r\n        this.isLoop = this.emitterData.isLoop;\r\n        this.initialDelay = this.emitterData.initialDelay;\r\n        this.preWarmDuration = this.emitterData.preWarmDuration;\r\n        this.emitDuration = this.emitterData.emitDuration;\r\n        this.emitInterval = this.emitterData.emitInterval;\r\n        this.emitPower = this.emitterData.emitPower;\r\n        this.loopInterval = this.emitterData.loopInterval;\r\n        this.perEmitCount = this.emitterData.perEmitCount;\r\n        this.perEmitInterval = this.emitterData.perEmitInterval;\r\n        this.perEmitOffsetX = this.emitterData.perEmitOffsetX;\r\n        this.angle = this.emitterData.angle;\r\n        this.count = this.emitterData.count;\r\n        this.arc = this.emitterData.arc;\r\n        this.radius = this.emitterData.radius;\r\n    }\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.perEmitInterval > 0) {\r\n            // Set the start time for this emit cycle\r\n            this._perEmitStartTime = this._statusElapsedTime;\r\n            this._perEmitAccumulator = 0;\r\n            console.log(`Starting emit cycle at time ${this._perEmitStartTime.toFixed(3)}, queue length before: ${this._perEmitBulletQueue.length}`);\r\n\r\n            // Queue all bullets for this cycle\r\n            for (let i = 0; i < this.count; i++) {\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: 0 // Not used in new system\r\n                    });\r\n                    console.log(`Queued bullet ${i}-${j} for perEmitIndex ${j}`);\r\n                }\r\n            }\r\n\r\n            // Sort by perEmitIndex to ensure proper order\r\n            this._perEmitBulletQueue.sort((a, b) => a.perEmitIndex - b.perEmitIndex);\r\n            console.log(`Queue length after: ${this._perEmitBulletQueue.length}`);\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count; i++) {\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(deltaTime: number): void {\r\n        if (this._perEmitBulletQueue.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Use fixed-step timing for consistent per-emit intervals\r\n        this._perEmitAccumulator += deltaTime;\r\n\r\n        // Process bullets using fixed time steps\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if enough time has accumulated for the next bullet\r\n            const timeFromStart = this._statusElapsedTime - this._perEmitStartTime;\r\n            const expectedTime = this.perEmitInterval * nextBullet.perEmitIndex;\r\n\r\n            if (timeFromStart >= expectedTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n\r\n                console.log(`Emitting bullet ${nextBullet.perEmitIndex} at time ${this._statusElapsedTime.toFixed(3)}, expected at ${(this._perEmitStartTime + expectedTime).toFixed(3)}, diff: ${(timeFromStart - expectedTime).toFixed(3)}`);\r\n\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        console.log(\"emit a bullet\");\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;\r\n        const radian = degreesToRadians(this.angle + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.perEmitCount > 1 ? (this.perEmitOffsetX / (this.perEmitCount - 1)) * perEmitIndex - this.perEmitOffsetX / 2 : 0);\r\n        if (this.radius <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius + perEmitOffsetX,\r\n            y: direction.y * this.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitPower;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    public applyAction(actType: eEmitterActionType, actValue: number) {\r\n        switch (actType) {\r\n            case eEmitterActionType.Emitter_Active:\r\n                this._isActive = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_InitialDelay:\r\n                this.initialDelay = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Prewarm:\r\n                this.isPreWarm = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_PrewarmDuration:\r\n                this.preWarmDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Duration:\r\n                this.emitDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_ElapsedTime:\r\n                this._statusElapsedTime = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Loop:\r\n                this.isLoop = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_LoopInterval:\r\n                this.loopInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitInterval:\r\n                this.perEmitInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitCount:\r\n                this.perEmitCount = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitOffsetX:\r\n                this.perEmitOffsetX = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Angle:\r\n                this.angle = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Count:\r\n                this.count = actValue;\r\n                break;\r\n            // TODO: 补充更多的行为实现\r\n            default: break;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this._isActive) {\r\n            return;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this._statusElapsedTime > this.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue(deltaTime);\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}