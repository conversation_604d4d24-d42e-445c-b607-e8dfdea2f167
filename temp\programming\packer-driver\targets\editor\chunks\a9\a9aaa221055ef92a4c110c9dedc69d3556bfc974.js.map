{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts"], "names": ["_decorator", "v2", "BaseComp", "GameIns", "Tools", "EnemyBase", "BossBase", "BattleLayer", "ccclass", "property", "BaseScreen", "m_enemy", "m_mainEntity", "m_bulletState", "attack", "through", "cirt", "atkChallenge", "m_config", "m_count", "toFire", "count", "fire", "setData", "config", "enemy", "bulletManager", "getConfig", "setBulletState", "bulletState", "mainEntity", "entity", "key", "getBulletCirt", "getBulletAttack", "getAttackPoint", "scene<PERSON>os", "sceneManager", "getScenePos", "position", "m_entity", "node", "getFireBulletAngle", "getPositionByAngle", "x", "y", "getAttackAngle", "angle", "getAttackPointAngle", "fireAngle", "error", "getAimAngle", "attackPoint", "mainPlaneNode", "mainPlaneManager", "mainPlane", "getAngle", "planeManager", "enemyTarget", "enemyCollider", "createBullet", "bulletId", "id", "bullet", "getBullet", "me", "addBullet"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAoDC,MAAAA,E,OAAAA,E;;AACtDC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,S;;AACAC,MAAAA,Q;;AACAC,MAAAA,W;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGTU,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,gCACiD;AAAA;AAAA;AAAA,eACnCC,OADmC,GACpB,IADoB;AAAA,eAEnCC,YAFmC,GAEf,IAFe;AAAA,eAGnCC,aAHmC,GAGnB;AACtBC,YAAAA,MAAM,EAAE,CADc;AAEtBC,YAAAA,OAAO,EAAE,KAFa;AAGtBC,YAAAA,IAAI,EAAC,CAHiB;AAItBC,YAAAA,YAAY,EAAE;AAJQ,WAHmB;AAAA,eASnCC,QATmC;AAAA,eAUnCC,OAVmC,GAUjB,CAViB;AAAA;;AAY7C;AACJ;AACA;AACA;AACgB,cAANC,MAAM,CAACC,KAAD,EAA+B;AACvC,eAAKF,OAAL,GAAeE,KAAf;AACA,gBAAM,KAAKC,IAAL,EAAN;AACH;;AAES,cAAJA,IAAI,GAAE,CAEX;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACC,MAAD,EAAcC,KAAd,EAAgC;AACnC,eAAKP,QAAL,GAAgB;AAAA;AAAA,kCAAQQ,aAAR,CAAsBC,SAAtB,CAAgCH,MAAhC,CAAhB;AACA,eAAKb,OAAL,GAAec,KAAf;AACA,eAAKN,OAAL,GAAe,CAAf;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIS,QAAAA,cAAc,CAACC,WAAD,EAAmBC,UAAnB,EAA2C;AACrD,eAAKlB,YAAL,GAAoBkB,UAAU,IAAI,KAAKC,MAAvC;;AACA,eAAK,MAAMC,GAAX,IAAkBH,WAAlB,EAA+B;AAC3B,iBAAKhB,aAAL,CAAmBmB,GAAnB,IAA0BH,WAAW,CAACG,GAAD,CAArC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,aAAa,GAAW;AACpB,iBAAO,KAAKpB,aAAL,CAAmBG,IAAnB,GAA0B,KAAKH,aAAL,CAAmBG,IAAnB,CAAwB,CAAxB,CAA1B,GAAuD,CAA9D;AACH;AAED;AACJ;AACA;AACA;;;AACIkB,QAAAA,eAAe,GAAW;AACtB,iBAAO,KAAKrB,aAAL,CAAmBI,YAAnB,IAAmC,CAA1C;AACH;AAED;AACJ;AACA;AACA;;;AACIkB,QAAAA,cAAc,GAAO;AACjB,cAAI,CAAC,KAAKvB,YAAV,EAAwB;AACpB,mBAAOX,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAT;AACH;;AAED,gBAAMmC,QAAQ,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,WAArB,CAAiC,KAAK1B,YAAtC,CAAjB;AACA,cAAI2B,QAAQ,GAAG,KAAKC,QAAL,CAAcC,IAAd,CAAmBF,QAAlC;;AAEA,cACI,KAAK3B,YAAL,CAAkB8B,kBAAlB,IACA,KAAK9B,YAAL,CAAkB8B,kBAAlB,OAA2C,CAF/C,EAGE;AACEH,YAAAA,QAAQ,GAAG;AAAA;AAAA,gCAAMI,kBAAN,CACP,KAAKH,QAAL,CAAcC,IAAd,CAAmBF,QADZ,EAEP,KAAK3B,YAAL,CAAkB8B,kBAAlB,EAFO,CAAX;AAIH;;AAED,iBAAOzC,EAAE,CACLsC,QAAQ,CAACK,CAAT,GAAaR,QAAQ,CAACQ,CADjB,EAELL,QAAQ,CAACM,CAAT,GAAaT,QAAQ,CAACS,CAFjB,CAAT;AAIH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,cAAc,GAAW;AACrB,cAAIC,KAAK,GAAG,KAAKP,QAAL,CAAcC,IAAd,CAAmBM,KAA/B;;AAEA,cAAI,KAAKP,QAAL,CAAcQ,mBAAlB,EAAuC;AACnCD,YAAAA,KAAK,IAAI,KAAKP,QAAL,CAAcQ,mBAAd,EAAT;AACH;;AAED,cAAIC,SAAS,GAAG,CAAhB;;AACA,cAAI;AACA,gBACI,KAAKrC,YAAL,IACA,KAAKA,YAAL,CAAkB8B,kBAFtB,EAGE;AACEO,cAAAA,SAAS,GAAG,KAAKrC,YAAL,CAAkB8B,kBAAlB,EAAZ;AACH;AACJ,WAPD,CAOE,OAAOQ,KAAP,EAAc,CAAE;;AAElB,iBAAOH,KAAK,GAAGE,SAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,WAAW,GAAW;AAClB,cAAIJ,KAAK,GAAG,CAAZ;AACA,gBAAMK,WAAW,GAAG,KAAKjB,cAAL,EAApB;;AAEA,cAAI,KAAKxB,OAAT,EAAkB;AACd,kBAAM0C,aAAa,GAAG;AAAA;AAAA,oCAAQC,gBAAR,CAAyBC,SAAzB,CAAmCd,IAAzD;AACAM,YAAAA,KAAK,GAAGM,aAAa,GACf;AAAA;AAAA,gCAAMG,QAAN,CAAeJ,WAAf,EAA4BnD,EAAE,CAACoD,aAAa,CAACd,QAAd,CAAuBK,CAAxB,EAA2BS,aAAa,CAACd,QAAd,CAAuBM,CAAlD,CAA9B,CADe,GAEf,CAFN;AAGH,WALD,MAKO,IACH;AAAA;AAAA,kCAAQY,YAAR,CAAqBC,WAArB,IACA;AAAA;AAAA,kCAAQD,YAAR,CAAqBE,aAFlB,EAGL,CACE;AACA;AACA;AACA;AACA;AACA;AACH;;AAED,iBAAOZ,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACsB,cAAZa,YAAY,GAAiB;AAC/B,gBAAMC,QAAQ,GAAG,KAAK3C,QAAL,CAAc4C,EAA/B;AACA,gBAAMC,MAAM,GAAG,MAAM;AAAA;AAAA,kCAAQrC,aAAR,CAAsBsC,SAAtB,CAAgCH,QAAhC,EAA0C,KAAKlD,OAA/C,CAArB;;AAEA,cAAIoD,MAAJ,EAAY;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AAAA;AAAA,4CAAYE,EAAZ,CAAeC,SAAf,CAAyBH,MAAzB,EAXI,CAYR;;AAEA,gBACI,KAAKpD,OAAL,KACC,KAAKC,YAAL;AAAA;AAAA,2CACG,KAAKA,YAAL;AAAA;AAAA,qCAFJ,CADJ,EAIE;AACE,mBAAKA,YAAL,CAAkBsD,SAAlB,CAA4BH,MAA5B;AACH;AACJ;;AAED,iBAAOA,MAAP;AACH;;AAhL4C,O", "sourcesContent": ["import { _decorator, Component, Tween,Node, Animation, Vec2, v2 } from 'cc';\r\nimport BaseComp from '../base/BaseComp';\r\nimport { GameIns } from '../../GameIns';\r\nimport { Tools } from '../../utils/Tools';\r\nimport EnemyBase from '../plane/enemy/EnemyBase';\r\nimport BossBase from '../plane/boss/BossBase';\r\nimport BattleLayer from '../layer/BattleLayer';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BaseScreen')\r\nexport default class BaseScreen extends BaseComp {\r\n    protected m_enemy: any = true;\r\n    protected m_mainEntity: any = null;\r\n    protected m_bulletState = {\r\n        attack: 0,\r\n        through: false,\r\n        cirt:0,\r\n        atkChallenge: 0,\r\n    };\r\n    protected m_config: any;\r\n    protected m_count: number = 0;\r\n\r\n    /**\r\n     * 开始射击\r\n     * @param count 射击次数\r\n     */\r\n    async toFire(count: number): Promise<void> {\r\n        this.m_count = count;\r\n        await this.fire();\r\n    }\r\n\r\n    async fire(){\r\n        \r\n    }\r\n\r\n    /**\r\n     * 设置数据\r\n     * @param config 配置数据\r\n     * @param enemy 敌人实体\r\n     */\r\n    setData(config: any, enemy: any): void {\r\n        this.m_config = GameIns.bulletManager.getConfig(config);\r\n        this.m_enemy = enemy;\r\n        this.m_count = 0;\r\n    }\r\n\r\n    /**\r\n     * 设置子弹状态\r\n     * @param bulletState 子弹状态\r\n     * @param mainEntity 主实体\r\n     */\r\n    setBulletState(bulletState: any, mainEntity?: any): void {\r\n        this.m_mainEntity = mainEntity || this.entity;\r\n        for (const key in bulletState) {\r\n            this.m_bulletState[key] = bulletState[key];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹暴击率\r\n     * @returns 暴击率\r\n     */\r\n    getBulletCirt(): number {\r\n        return this.m_bulletState.cirt ? this.m_bulletState.cirt[0] : 0;\r\n    }\r\n\r\n    /**\r\n     * 获取子弹攻击力\r\n     * @returns 攻击力\r\n     */\r\n    getBulletAttack(): number {\r\n        return this.m_bulletState.atkChallenge || 0;\r\n    }\r\n\r\n    /**\r\n     * 获取攻击点\r\n     * @returns 攻击点的坐标\r\n     */\r\n    getAttackPoint():Vec2{\r\n        if (!this.m_mainEntity) {\r\n            return v2(0, 0);\r\n        }\r\n\r\n        const scenePos = GameIns.sceneManager.getScenePos(this.m_mainEntity);\r\n        let position = this.m_entity.node.position;\r\n\r\n        if (\r\n            this.m_mainEntity.getFireBulletAngle &&\r\n            this.m_mainEntity.getFireBulletAngle() !== 0\r\n        ) {\r\n            position = Tools.getPositionByAngle(\r\n                this.m_entity.node.position,\r\n                this.m_mainEntity.getFireBulletAngle()\r\n            );\r\n        }\r\n\r\n        return v2(\r\n            position.x + scenePos.x,\r\n            position.y + scenePos.y,\r\n        );\r\n    }\r\n\r\n    /**\r\n     * 获取攻击角度\r\n     * @returns 攻击角度\r\n     */\r\n    getAttackAngle(): number {\r\n        let angle = this.m_entity.node.angle;\r\n\r\n        if (this.m_entity.getAttackPointAngle) {\r\n            angle -= this.m_entity.getAttackPointAngle();\r\n        }\r\n\r\n        let fireAngle = 0;\r\n        try {\r\n            if (\r\n                this.m_mainEntity &&\r\n                this.m_mainEntity.getFireBulletAngle\r\n            ) {\r\n                fireAngle = this.m_mainEntity.getFireBulletAngle();\r\n            }\r\n        } catch (error) {}\r\n\r\n        return angle - fireAngle;\r\n    }\r\n\r\n    /**\r\n     * 获取瞄准角度\r\n     * @returns 瞄准角度\r\n     */\r\n    getAimAngle(): number {\r\n        let angle = 0;\r\n        const attackPoint = this.getAttackPoint();\r\n\r\n        if (this.m_enemy) {\r\n            const mainPlaneNode = GameIns.mainPlaneManager.mainPlane.node;\r\n            angle = mainPlaneNode\r\n                ? Tools.getAngle(attackPoint, v2(mainPlaneNode.position.x, mainPlaneNode.position.y))\r\n                : 0;\r\n        } else if (\r\n            GameIns.planeManager.enemyTarget &&\r\n            GameIns.planeManager.enemyCollider\r\n        ) {\r\n            // const enemyTarget = GameIns.planeManager.enemyTarget;\r\n            // const enemyColliderPos =\r\n            //     GameIns.planeManager.enemyCollider.getScreenPos();\r\n            // angle = enemyTarget\r\n            //     ? Tools.getAngle(attackPoint, enemyColliderPos)\r\n            //     : 0;\r\n        }\r\n\r\n        return angle;\r\n    }\r\n\r\n    /**\r\n     * 创建子弹\r\n     * @returns 创建的子弹\r\n     */\r\n    async createBullet(): Promise<any> {\r\n        const bulletId = this.m_config.id;\r\n        const bullet = await GameIns.bulletManager.getBullet(bulletId, this.m_enemy);\r\n\r\n        if (bullet) {\r\n            // if (GameIns.bulletManager.fireShellUINode) {\r\n            //     bullet.node.parent = GameIns.bulletManager.fireShellUINode;\r\n            // }\r\n            //  else if (MainGameIns.planeManager.isShow) {\r\n            //     const mainPlaneUI = frameWork.uiManager.getDialogByName(\r\n            //         \"MainPlaneUI\"\r\n            //     );\r\n            //     bullet.node.parent = mainPlaneUI.fireNode;\r\n            // } \r\n            // else {\r\n                BattleLayer.me.addBullet(bullet);\r\n            // }\r\n\r\n            if (\r\n                this.m_enemy &&\r\n                (this.m_mainEntity instanceof EnemyBase ||\r\n                    this.m_mainEntity instanceof BossBase)\r\n            ) {\r\n                this.m_mainEntity.addBullet(bullet);\r\n            }\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n}"]}