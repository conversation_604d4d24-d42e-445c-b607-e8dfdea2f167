import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
import { EmitterConditionData, BulletConditionData } from "./EventConditionData";
import { EmitterActionData, BulletActionData } from "./EventActionData";
const { ccclass, property } = _decorator;

export enum eConditionGroupOp {
    And, Or
}

/**
 * 发射器事件数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("EmitterGroupData")
export class EmitterGroupData {
    @property({ displayName: '事件组名称' })
    name : string = '';

    @property({ type: EmitterConditionData, displayName: '条件A' })
    conditionA : EmitterConditionData;

    @property({ type: Enum(eConditionGroupOp), displayName: '条件组合操作' })
    groupOp : eConditionGroupOp = eConditionGroupOp.And; // 条件组合操作: 与/或

    @property({ type: EmitterConditionData, displayName: '条件B' })
    conditionB : EmitterConditionData;

    @property({ displayName: '是否重复触发' })
    isRepeat : boolean = false;

    @property({ type: [EmitterActionData], displayName: '行为列表' })
    actions : EmitterActionData[] = [];
}

@ccclass("BulletGroupData")
export class BulletGroupData {
    @property({ displayName: '事件组名称' })
    name : string = '';

    @property({ type: BulletConditionData, displayName: '条件A' })
    conditionA : BulletConditionData;

    @property({ type: Enum(eConditionGroupOp), displayName: '条件组合操作' })
    groupOp : eConditionGroupOp = eConditionGroupOp.And; // 条件组合操作: 与/或

    @property({ type: BulletConditionData, displayName: '条件B' })
    conditionB : BulletConditionData;

    @property({ displayName: '是否重复触发' })
    isRepeat : boolean = false;

    @property({ type: [BulletActionData], displayName: '行为列表' })
    actions : BulletActionData[] = [];
}