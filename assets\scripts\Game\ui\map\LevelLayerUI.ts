import { _decorator, assetManager, Component, instantiate, Node, Prefab, Vec2 } from "cc";
import { LevelDataEvent, LevelDataLayer, LevelDataWave } from "../../../leveldata/leveldata";
import { LevelWaveUI } from "./LevelWaveUI";
import { LevelEventUI } from "./LevelEventUI";
import WaveManager from "../../manager/WaveManager";
import { MyApp } from "../../../MyApp";
import { GameIns } from "../../GameIns";
import { Wave } from "../../wave/Wave";
import { LevelDataEventCondtionType } from "../../../leveldata/condition/LevelDataEventCondtion";
import { LevelDataEventTriggerType } from "../../../leveldata/trigger/LevelDataEventTrigger";
import { LevelDataEventTriggerLog } from "../../../leveldata/trigger/LevelDataEventTriggerLog";
import { LevelDataEventTriggerWave } from "../../../leveldata/trigger/LevelDataEventTriggerWave";

const { ccclass } = _decorator;

const TerrainsNodeName = "terrains";
const DynamicNodeName = "dynamic";
const WaveNodeName = "waves";
const EventNodeName = "events"

@ccclass('LevelLayerUI')
export class LevelLayerUI extends Component {
    public backgrounds: Prefab[] = [];
    private _offSetY: number = 0; // 当前关卡的偏移量

    private terrainsNode: Node|null = null;
    private dynamicNode: Node|null = null;
    private waves: LevelDataWave[] = [];
    private events: LevelDataEvent[] = [];
    private enableEvents: LevelDataEvent[] = [];

    onLoad(): void {
        
    }

    public initByLevelData(data: LevelDataLayer, offSetY: number):void {
        this._offSetY = offSetY;
        this.node.setPosition(0, offSetY, 0);
        
        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);
        
        console.log('LevelLayerUI'," initByLevelData");
        this.backgrounds = [];

        data.terrains?.forEach((terrain) => {
            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                if (err) {
                    console.error('LevelLayerUI'," initByLevelData load terrain prefab err", err);
                    return;
                } 
                
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode.addChild(terrainNode);                
            });
        });
        this.waves = [...data.waves]
        this.waves.sort((a, b) => a.position.y - b.position.y);
        this.events = [...data.events]
        this.events.sort((a, b) => a.position.y - b.position.y);
    }

    public tick(deltaTime: number, speed:number):void {
        const prePosY = this.node.getPosition().y;
        this.node.setPosition(0, prePosY - deltaTime * speed, 0);

        while(this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {
            const wave = this.waves[0];
            this.waves.shift();
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)
            MyApp.resMgr.load(path, (err, prefab:Prefab) => {
                if (err) {
                    console.error('LevelLayerUI'," tick load wave prefab err", err);
                    return;
                } 
                const waveComp = instantiate(prefab).getComponent(Wave)
                GameIns.waveManager.addWaveByLevel(waveComp, wave.position);
            });
        }
        while(this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {
            const event = this.events[0];
            this.events.shift();
            this.enableEvents.push(event);
        }
        for (let i = this.enableEvents.length-1; i >= 0; i--) {
            const event = this.enableEvents[i];
            let condResult = true
            for (let cond of event.conditions) {
            }
            if (condResult) {
                this.enableEvents.splice(i, 1);
                for (let trigger of event.triggers) {
                    switch(trigger._type) {
                        case LevelDataEventTriggerType.Log:
                            console.log("LevelLayerUI", "trigger log", (trigger as LevelDataEventTriggerLog).message);
                            break;
                        case LevelDataEventTriggerType.Audio:
                            break;
                        case LevelDataEventTriggerType.Wave:
                            const waveTriger = trigger as LevelDataEventTriggerWave;
                            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveTriger.waveUUID)
                            MyApp.resMgr.load(path, (err, prefab:Prefab) => {
                                if (err) {
                                    console.error('LevelLayerUI'," tick load wave prefab err", err);
                                    return;
                                } 
                                const waveComp = instantiate(prefab).getComponent(Wave)
                                GameIns.waveManager.addWaveByLevel(waveComp, new Vec2(event.position.x, this.node.position.y));
                            });
                            break;
                    }
                }
            }
        }
    }

    private _getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }
}