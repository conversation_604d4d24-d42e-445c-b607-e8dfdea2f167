{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/event/EventManager.ts"], "names": ["EventManager", "_eventCacheMap", "Map", "emit", "eventName", "param", "eventCacheArray", "get", "sortedEvents", "sort", "a", "b", "priority", "eventCache", "i", "length", "callback", "apply", "target", "once", "index", "findIndex", "e", "splice", "delete", "on", "_on", "push", "set", "updatePriority", "newPriority", "off", "targetOff", "for<PERSON>ach", "Instance", "EventMgr", "window"], "mappings": ";;;iBAGqBA,Y;;;;;;;;;;;;;AAHrB;AACA;AACA;yBACqBA,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAE9B;AACJ;AACA;AACA;AALkC,eAMtBC,cANsB,GAMsB,IAAIC,GAAJ,EANtB;AAAA;;AAQ9B;AACJ;AACA;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,SAAD,EAAoB,GAAGC,KAAvB,EAA2C;AAC3C,cAAIC,eAAe,GAAG,KAAKL,cAAL,CAAoBM,GAApB,CAAwBH,SAAxB,CAAtB;;AACA,cAAIE,eAAJ,EAAqB;AACjB;AACA,kBAAME,YAAY,GAAG,CAAC,GAAGF,eAAJ,EAAqBG,IAArB,CAA0B,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACC,QAAF,GAAaF,CAAC,CAACE,QAAnD,CAArB;AAEA,gBAAIC,UAAsB,GAAG,IAA7B;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,YAAY,CAACO,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;AAC1CD,cAAAA,UAAU,GAAGL,YAAY,CAACM,CAAD,CAAzB;AACAD,cAAAA,UAAU,CAACG,QAAX,CAAoBC,KAApB,CAA0BJ,UAAU,CAACK,MAArC,EAA6Cb,KAA7C,EAF0C,CAI1C;;AACA,kBAAIQ,UAAU,CAACM,IAAf,EAAqB;AACjB,sBAAMC,KAAK,GAAGd,eAAe,CAACe,SAAhB,CAA0BC,CAAC,IACrCA,CAAC,CAACJ,MAAF,KAAaL,UAAU,CAACK,MAAxB,IACAI,CAAC,CAACN,QAAF,KAAeH,UAAU,CAACG,QAFhB,CAAd;;AAIA,oBAAII,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdd,kBAAAA,eAAe,CAACiB,MAAhB,CAAuBH,KAAvB,EAA8B,CAA9B;AACH;AACJ;AACJ,aAnBgB,CAqBjB;;;AACA,gBAAId,eAAe,CAACS,MAAhB,KAA2B,CAA/B,EAAkC;AAC9B,mBAAKd,cAAL,CAAoBuB,MAApB,CAA2BpB,SAA3B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIqB,QAAAA,EAAE,CAACrB,SAAD,EAAoBY,QAApB,EAAwCE,MAAxC,EAAsDN,QAAgB,GAAG,CAAzE,EAAkF;AAChF,eAAKc,GAAL,CAAStB,SAAT,EAAoBY,QAApB,EAA8BE,MAA9B,EAAsC,KAAtC,EAA6CN,QAA7C;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIO,QAAAA,IAAI,CAACf,SAAD,EAAoBY,QAApB,EAAwCE,MAAxC,EAAsDN,QAAgB,GAAG,CAAzE,EAAkF;AAClF,eAAKc,GAAL,CAAStB,SAAT,EAAoBY,QAApB,EAA8BE,MAA9B,EAAsC,IAAtC,EAA4CN,QAA5C;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYc,QAAAA,GAAG,CAACtB,SAAD,EAAoBY,QAApB,EAAwCE,MAAxC,EAAqDC,IAArD,EAAoEP,QAApE,EAA4F;AACnG,cAAIN,eAAe,GAAG,KAAKL,cAAL,CAAoBM,GAApB,CAAwBH,SAAxB,CAAtB;;AACA,cAAI,CAACE,eAAL,EAAsB;AAClBA,YAAAA,eAAe,GAAG,EAAlB;AACH;;AACD,cAAIc,KAAK,GAAGd,eAAe,CAACe,SAAhB,CAA2BR,UAAD,IAAgB;AAClD,mBAAOA,UAAU,CAACK,MAAX,KAAsBA,MAAtB,IAAgCL,UAAU,CAACG,QAAX,KAAwBA,QAA/D;AACH,WAFW,CAAZ;;AAIA,cAAII,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdd,YAAAA,eAAe,CAACqB,IAAhB,CAAqB;AACjBT,cAAAA,MAAM,EAAEA,MADS;AAEjBF,cAAAA,QAAQ,EAAEA,QAFO;AAGjBG,cAAAA,IAAI,EAAEA,IAHW;AAIjBP,cAAAA,QAAQ,EAAEA;AAJO,aAArB;;AAMA,iBAAKX,cAAL,CAAoB2B,GAApB,CAAwBxB,SAAxB,EAAmCE,eAAnC;AACH,WARD,MAQO;AACH;AACAA,YAAAA,eAAe,CAACc,KAAD,CAAf,CAAuBR,QAAvB,GAAkCA,QAAlC;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIiB,QAAAA,cAAc,CAACzB,SAAD,EAAoBY,QAApB,EAAwCE,MAAxC,EAAqDY,WAArD,EAAgF;AAC1F,cAAIxB,eAAe,GAAG,KAAKL,cAAL,CAAoBM,GAApB,CAAwBH,SAAxB,CAAtB;;AACA,cAAIE,eAAJ,EAAqB;AACjB,kBAAMc,KAAK,GAAGd,eAAe,CAACe,SAAhB,CAA2BR,UAAD,IAAgB;AACpD,qBAAOA,UAAU,CAACK,MAAX,KAAsBA,MAAtB,IAAgCL,UAAU,CAACG,QAAX,KAAwBA,QAA/D;AACH,aAFa,CAAd;;AAGA,gBAAII,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdd,cAAAA,eAAe,CAACc,KAAD,CAAf,CAAuBR,QAAvB,GAAkCkB,WAAlC;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,GAAG,CAAC3B,SAAD,EAAoBY,QAApB,EAAyCE,MAAzC,EAA6D;AAC5D,cAAIZ,eAAe,GAAG,KAAKL,cAAL,CAAoBM,GAApB,CAAwBH,SAAxB,CAAtB;;AACA,cAAIE,eAAJ,EAAqB;AACjB,gBAAIU,QAAQ,IAAIE,MAAhB,EAAwB;AACpB,kBAAIE,KAAK,GAAGd,eAAe,CAACe,SAAhB,CAA2BR,UAAD,IAAgB;AAClD,uBAAOA,UAAU,CAACK,MAAX,KAAsBA,MAAtB,IAAgCL,UAAU,CAACG,QAAX,KAAwBA,QAA/D;AACH,eAFW,CAAZ;;AAGA,kBAAII,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdd,gBAAAA,eAAe,CAACiB,MAAhB,CAAuBH,KAAvB,EAA8B,CAA9B;;AACA,oBAAId,eAAe,CAACS,MAAhB,KAA2B,CAA/B,EAAkC;AAC9B,uBAAKd,cAAL,CAAoBuB,MAApB,CAA2BpB,SAA3B;AACH,iBAFD,MAEO;AACH,uBAAKH,cAAL,CAAoB2B,GAApB,CAAwBxB,SAAxB,EAAmCE,eAAnC;AACH;AACJ;AACJ,aAZD,MAYO;AACH,mBAAKL,cAAL,CAAoBuB,MAApB,CAA2BpB,SAA3B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACI4B,QAAAA,SAAS,CAACd,MAAD,EAAoB;AACzB,eAAKjB,cAAL,CAAoBgC,OAApB,CAA4B,CAAC3B,eAAD,EAAkBF,SAAlB,KAAgC;AACxD,gBAAIE,eAAJ,EAAqB;AACjB,mBAAK,IAAIQ,CAAC,GAAGR,eAAe,CAACS,MAAhB,GAAyB,CAAtC,EAAyCD,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,oBAAIR,eAAe,CAACQ,CAAD,CAAf,CAAmBI,MAAnB,KAA8BA,MAAlC,EAA0C;AACtCZ,kBAAAA,eAAe,CAACiB,MAAhB,CAAuBT,CAAvB,EAA0B,CAA1B;AACH;AACJ;;AACD,kBAAIR,eAAe,CAACS,MAAhB,KAA2B,CAA/B,EAAkC;AAC9B,qBAAKd,cAAL,CAAoBuB,MAApB,CAA2BpB,SAA3B;AACH;AACJ;AACJ,WAXD;AAYH;;AAtK6B,O;AAyKlC;AACA;AACA;;;eA3KqBJ,Y;AAAAA,MAAAA,Y,CACMkC,Q,GAAyB,IAAIlC,MAAJ,E;;0BAiMvCmC,Q,GAAWnC,YAAY,CAACkC,Q;;AACrCE,MAAAA,MAAM,CAAC,UAAD,CAAN,GAAqBD,QAArB", "sourcesContent": ["/**\n* 事件管理器（带优先级处理）\n*/\nexport default class EventManager {\n    public static readonly Instance: EventManager = new EventManager();\n    /**\n     * key: 事件名\n     * value: 缓存事件\n     */\n    private _eventCacheMap: Map<string, EventCache[]> = new Map();\n\n    /**\n     * 广播事件\n     *\n     * @param eventName 事件名\n     * @param param 传递的剩余不定参数\n     */\n    emit(eventName: string, ...param: any[]): void {\n        let eventCacheArray = this._eventCacheMap.get(eventName);\n        if (eventCacheArray) {\n            // 先按照优先级排序（优先级高的先执行）\n            const sortedEvents = [...eventCacheArray].sort((a, b) => b.priority - a.priority);\n\n            let eventCache: EventCache = null;\n            for (let i = 0; i < sortedEvents.length; i++) {\n                eventCache = sortedEvents[i];\n                eventCache.callback.apply(eventCache.target, param);\n\n                // 只接受一次回调的事件，在触发之后就移除掉该缓存事件\n                if (eventCache.once) {\n                    const index = eventCacheArray.findIndex(e =>\n                        e.target === eventCache.target &&\n                        e.callback === eventCache.callback\n                    );\n                    if (index !== -1) {\n                        eventCacheArray.splice(index, 1);\n                    }\n                }\n            }\n\n            // 更新缓存（移除已经执行的一次性事件）\n            if (eventCacheArray.length === 0) {\n                this._eventCacheMap.delete(eventName);\n            }\n        }\n    }\n\n    /**\n     * 注册事件\n     *\n     * @param eventName 事件名\n     * @param callback 事件处理函数\n     * @param target 事件处理函数的执行对象\n     * @param priority 优先级（数字越大优先级越高，默认为0）\n     */\n    on(eventName: string, callback: Function, target?: any, priority: number = 0): void {\n        this._on(eventName, callback, target, false, priority);\n    }\n\n    /**\n     * 注册事件（接受函数执行一次后会自动销毁，不用主动off）\n     *\n     * @param eventName 事件名\n     * @param callback 事件处理函数\n     * @param target 事件处理函数的执行对象\n     * @param priority 优先级（数字越大优先级越高，默认为0）\n     */\n    once(eventName: string, callback: Function, target?: any, priority: number = 0): void {\n        this._on(eventName, callback, target, true, priority);\n    }\n\n    /**\n     * 注册事件\n     *\n     * @param eventName 事件名\n     * @param callback 事件处理函数\n     * @param target 事件处理函数的执行对象\n     * @param once 是否只回调一次\n     * @param priority 优先级（数字越大优先级越高）\n     */\n    private _on(eventName: string, callback: Function, target: any, once: boolean, priority: number): void {\n        let eventCacheArray = this._eventCacheMap.get(eventName);\n        if (!eventCacheArray) {\n            eventCacheArray = [];\n        }\n        let index = eventCacheArray.findIndex((eventCache) => {\n            return eventCache.target === target && eventCache.callback === callback;\n        });\n\n        if (index === -1) {\n            eventCacheArray.push({\n                target: target,\n                callback: callback,\n                once: once,\n                priority: priority\n            });\n            this._eventCacheMap.set(eventName, eventCacheArray);\n        } else {\n            // 如果已存在，更新优先级\n            eventCacheArray[index].priority = priority;\n        }\n    }\n\n    /**\n     * 更新已注册事件的优先级\n     *\n     * @param eventName 事件名\n     * @param callback 事件处理函数\n     * @param target 事件处理函数的执行对象\n     * @param newPriority 新的优先级\n     */\n    updatePriority(eventName: string, callback: Function, target: any, newPriority: number): void {\n        let eventCacheArray = this._eventCacheMap.get(eventName);\n        if (eventCacheArray) {\n            const index = eventCacheArray.findIndex((eventCache) => {\n                return eventCache.target === target && eventCache.callback === callback;\n            });\n            if (index !== -1) {\n                eventCacheArray[index].priority = newPriority;\n            }\n        }\n    }\n\n    /**\n     * 注销事件\n     *\n     * @param eventName 事件名\n     * @param callback 事件处理函数\n     * @param target 事件处理函数的执行对象\n     */\n    off(eventName: string, callback?: Function, target?: any): void {\n        let eventCacheArray = this._eventCacheMap.get(eventName);\n        if (eventCacheArray) {\n            if (callback && target) {\n                let index = eventCacheArray.findIndex((eventCache) => {\n                    return eventCache.target === target && eventCache.callback === callback;\n                });\n                if (index !== -1) {\n                    eventCacheArray.splice(index, 1);\n                    if (eventCacheArray.length === 0) {\n                        this._eventCacheMap.delete(eventName);\n                    } else {\n                        this._eventCacheMap.set(eventName, eventCacheArray);\n                    }\n                }\n            } else {\n                this._eventCacheMap.delete(eventName);\n            }\n        }\n    }\n\n    /**\n     * 注销某个已经注册的对象的所有事件\n     *\n     * @param target 事件函数处理的执行对象\n     */\n    targetOff(target: any): void {\n        this._eventCacheMap.forEach((eventCacheArray, eventName) => {\n            if (eventCacheArray) {\n                for (let i = eventCacheArray.length - 1; i >= 0; i--) {\n                    if (eventCacheArray[i].target === target) {\n                        eventCacheArray.splice(i, 1);\n                    }\n                }\n                if (eventCacheArray.length === 0) {\n                    this._eventCacheMap.delete(eventName);\n                }\n            }\n        });\n    }\n}\n\n/**\n * 缓存事件\n */\ninterface EventCache {\n    /**\n     * 回调函数执行者\n     */\n    target: any;\n\n    /**\n     * 回调函数\n     */\n    callback: Function;\n\n    /**\n     * 是否只回调一次\n     */\n    once: boolean;\n\n    /**\n     * 事件触发优先级（数字越大优先级越高）\n     */\n    priority: number;\n}\n\nexport const EventMgr = EventManager.Instance;\nwindow[\"EventMgr\"] = EventMgr;"]}