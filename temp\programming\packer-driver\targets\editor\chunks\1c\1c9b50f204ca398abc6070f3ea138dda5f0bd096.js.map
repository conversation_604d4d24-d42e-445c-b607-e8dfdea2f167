{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts"], "names": ["_decorator", "Node", "Sprite", "tween", "Enum", "Vec2", "sp", "v3", "UITransform", "Tween", "size", "Bullet", "MainPlane", "GameEnum", "BossHurt", "Tools", "GameIns", "FBoxCollider", "ColliderGroupType", "ccclass", "property", "BossUnit", "_data", "_boss<PERSON><PERSON>", "_collideComp", "_unitType", "_curHp", "_maxHp", "_hpBar", "_hpWhite", "_hpWhiteTween", "defence", "_hpStage", "_hpStageIndex", "_action", "BossAction", "Normal", "_damaged", "_damageable", "_bSmoking", "_whiteNode", "_winkCount", "_bW<PERSON><PERSON><PERSON>e", "_winkAct", "_skel", "_curAnim", "_skelCallMap", "Map", "_skelEventCallMap", "_smokeSkelPool", "_smokePosArr", "_smokeSkelArr", "_smokeBoneArr", "onLoad", "init", "data", "boss<PERSON><PERSON>", "reset", "type", "EnemyType", "initData", "_initUI", "_refreshHpBar", "<PERSON><PERSON><PERSON>", "isDead", "_initCollide", "_checkHpStage", "removeSmoke", "hp", "colliderData", "collider", "getComponent", "addComponent", "width", "height", "groupType", "ENEMY_NORMAL", "isEnable", "node", "setPosition", "pos", "x", "y", "skelNode", "<PERSON><PERSON><PERSON><PERSON>", "Skeleton", "skeletonData", "boss<PERSON><PERSON><PERSON>", "skelDataMap", "get", "anim", "premultipliedAlpha", "addSpine", "mixArr", "for<PERSON>ach", "mix", "setMix", "error", "log", "setCompleteListener", "trackEntry", "animationName", "animation", "name", "callback", "key", "setEventListener", "event", "eventName", "setSiblingIndex", "UnitZIndex", "Skel", "hpParam", "hpNode", "getChildByName", "uId", "parent", "setBossFrame", "whiteNode", "Type", "FILLED", "fillType", "FillType", "HORIZONTAL", "fill<PERSON><PERSON><PERSON>", "barNode", "posX", "position", "posY", "scaleX", "setScale", "getScale", "active", "updateGameLogic", "deltaTime", "smoke", "index", "boneName", "bone", "findBone", "add", "worldX", "worldY", "m_comps", "comp", "update", "onCollide", "damageable", "entity", "damage", "getAttack", "hurtEffectManager", "createHurtNumByType", "getPosition", "console", "Math", "max", "hurt", "hpChange", "_checkHp", "_wink<PERSON><PERSON>e", "amount", "change", "newHp", "_playStageAnim", "_die", "hpStage", "length", "stop", "_playDieAnim", "unitDestroyed", "fillDifference", "abs", "to", "call", "start", "playSkel", "loop", "set", "setAnimation", "setEventCallback", "skinIndex", "formIndex", "setAction", "action", "_playShakeAnim", "actionFrameTime", "angle", "hideSmoke", "opacity", "push", "removeSpine", "id", "unitId", "isBody", "getUnitType", "setPropertyRate", "rates", "attack", "_collideAtk", "value", "boss<PERSON><PERSON><PERSON>", "curHp", "maxHp", "stageIndex", "_onStageAnimEnd", "unitDestroyAnimEnd", "_getSmokeAnim", "smokeAnim", "pop", "smokeNode", "smokeSkelData", "setCollideAble", "isEnabled", "getCollideAble", "SmokeBottom", "SmokeTop"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACjGC,MAAAA,M;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACAC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AAGFC,MAAAA,Y;;AACaC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OACd;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;yBAGTqB,Q,WADpBF,OAAO,CAAC,UAAD,C,2BAAR,MACqBE,QADrB;AAAA;AAAA,gCAC+C;AAAA;AAAA;AAAA,eAO3CC,KAP2C,GAO9B,IAP8B;AAAA,eAQ3CC,UAR2C,GAQzB,IARyB;AAAA,eAS3CC,YAT2C;AAAA,eAU3CC,SAV2C,GAUvB,CAVuB;AAAA,eAW3CC,MAX2C,GAW1B,CAX0B;AAAA,eAY3CC,MAZ2C,GAY1B,CAZ0B;AAAA,eAa3CC,MAb2C,GAanB,IAbmB;AAAA,eAc3CC,QAd2C,GAcjB,IAdiB;AAAA,eAe3CC,aAf2C,GAetB,IAfsB;AAAA,eAgB3CC,OAhB2C,GAgBzB,CAhByB;AAAA,eAiB3CC,QAjB2C,GAiBxB,CAjBwB;AAAA,eAkB3CC,aAlB2C,GAkBnB,CAlBmB;AAAA,eAmB3CC,OAnB2C,GAmBzB;AAAA;AAAA,oCAASC,UAAT,CAAoBC,MAnBK;AAAA,eAoB3CC,QApB2C,GAoBvB,KApBuB;AAAA,eAqB3CC,WArB2C,GAqBpB,KArBoB;AAAA,eAsB3CC,SAtB2C,GAsBtB,KAtBsB;AAAA,eAuB3CC,UAvB2C,GAuBjB,IAvBiB;AAAA,eAwB3CC,UAxB2C,GAwBtB,CAxBsB;AAAA,eAyB3CC,WAzB2C,GAyBpB,KAzBoB;AAAA,eA0B3CC,QA1B2C,GA0B3B,IA1B2B;AAAA,eA2B3CC,KA3B2C,GA2B9B,IA3B8B;AAAA,eA4B3CC,QA5B2C,GA4BxB,EA5BwB;AAAA,eA6B3CC,YA7B2C,GA6BL,IAAIC,GAAJ,EA7BK;AAAA,eA8B3CC,iBA9B2C,GA8BA,IAAID,GAAJ,EA9BA;AAAA,eA+B3CE,cA/B2C,GA+BnB,EA/BmB;AAAA,eAgC3CC,YAhC2C,GAgCpB,EAhCoB;AAAA,eAiC3CC,aAjC2C,GAiCpB,EAjCoB;AAAA,eAkC3CC,aAlC2C,GAkCjB,EAlCiB;AAAA;;AAoC3CC,QAAAA,MAAM,GAAS,CACX;AACH;;AAEDC,QAAAA,IAAI,CAACC,IAAD,EAAYC,SAAZ,EAAkC;AAClC,eAAKC,KAAL;AACA,eAAKC,IAAL,GAAY;AAAA;AAAA,oCAASC,SAAT,CAAmBtC,QAA/B;AACA,eAAKC,KAAL,GAAaiC,IAAb;AACA,eAAKhC,UAAL,GAAkBiC,SAAlB;AAEA,eAAKI,QAAL;;AACA,eAAKC,OAAL;;AACA,eAAKC,aAAL;;AACA,eAAKC,OAAL,CAAa,CAAb;;AAEA,cAAI,KAAKrC,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKD,SAAL,GAAiB,CAAjB;AACA,iBAAKuC,MAAL,GAAc,KAAd;;AACA,iBAAKC,YAAL;;AACA,iBAAKC,aAAL;AACH,WALD,MAKO;AACH,iBAAKzC,SAAL,GAAiB,CAAjB;AACA,iBAAKuC,MAAL,GAAc,IAAd;AACH;AACJ;;AAEDP,QAAAA,KAAK,GAAS;AACV,eAAKZ,QAAL,GAAgB,EAAhB;AACA,eAAKb,QAAL,GAAgB,CAAhB;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,oCAASC,UAAT,CAAoBC,MAAnC;AACA,eAAK+B,WAAL;AACH;;AAEDP,QAAAA,QAAQ,GAAS;AACb,eAAKlC,MAAL,GAAc,KAAKJ,KAAL,CAAW8C,EAAzB;AACA,eAAKzC,MAAL,GAAc,KAAKD,MAAnB;AACH;;AAEDuC,QAAAA,YAAY,GAAS;AAEjB,gBAAMI,YAAY,GAAG,KAAK/C,KAAL,CAAWgD,QAAhC;AACA,eAAK9C,YAAL,GAAoB,KAAK+C,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAvD;;AACA,eAAKhD,YAAL,CAAkB8B,IAAlB,CAAuB,IAAvB,EAA4B5C,IAAI,CAAC2D,YAAY,CAACI,KAAd,EAAqBJ,YAAY,CAACK,MAAlC,CAAhC,EAJiB,CAI2D;;;AAC5E,eAAKlD,YAAL,CAAkBmD,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKpD,YAAL,CAAkBqD,QAAlB,GAA6B,KAA7B;AACH;;AAEDhB,QAAAA,OAAO,GAAS;AACZ,eAAKiB,IAAL,CAAUC,WAAV,CAAsB,KAAKzD,KAAL,CAAW0D,GAAX,CAAeC,CAArC,EAAwC,KAAK3D,KAAL,CAAW0D,GAAX,CAAeE,CAAvD;;AAEA,kBAAQ,KAAK5D,KAAL,CAAWoC,IAAnB;AACI,iBAAK,CAAL;AACI,kBAAI,CAAC,KAAKd,KAAV,EAAiB;AACb,sBAAMuC,QAAQ,GAAG,IAAIlF,IAAJ,CAAS,UAAT,CAAjB;AACA,qBAAK6E,IAAL,CAAUM,QAAV,CAAmBD,QAAnB,EAFa,CAGb;;AACA,qBAAKvC,KAAL,GAAauC,QAAQ,CAACX,YAAT,CAAsBlE,EAAE,CAAC+E,QAAzB,CAAb;AACA,qBAAKzC,KAAL,CAAW0C,YAAX,GAA0B;AAAA;AAAA,wCAAQC,WAAR,CAAoBC,WAApB,CAAgCC,GAAhC,CAAoC,KAAKnE,KAAL,CAAWoE,IAA/C,CAA1B;AACA,qBAAK9C,KAAL,CAAW+C,kBAAX,GAAgC,KAAhC;;AACA,qBAAKpE,UAAL,CAAgBqE,QAAhB,CAAyB,KAAKhD,KAA9B;;AAEA,qBAAKtB,KAAL,CAAWuE,MAAX,CAAkBC,OAAlB,CAA2BC,GAAD,IAA2B;AACjD,sBAAI;AACA,yBAAKnD,KAAL,CAAWoD,MAAX,CAAkBD,GAAG,CAAC,CAAD,CAArB,EAA0BA,GAAG,CAAC,CAAD,CAA7B,EAAkC,GAAlC;AACH,mBAFD,CAEE,OAAOE,KAAP,EAAc;AACZ;AAAA;AAAA,wCAAMC,GAAN,CAAU,sBAAV,EAAkCH,GAAlC;AACH;AACJ,iBAND;;AAQA,qBAAKnD,KAAL,CAAWuD,mBAAX,CAAgCC,UAAD,IAAqB;AAChD,wBAAMC,aAAa,GAAGD,UAAU,CAACE,SAAX,GAAuBF,UAAU,CAACE,SAAX,CAAqBC,IAA5C,GAAmD,EAAzE;;AACA,uBAAKzD,YAAL,CAAkBgD,OAAlB,CAA0B,CAACU,QAAD,EAAWC,GAAX,KAAmB;AACzC,wBAAIJ,aAAa,KAAKI,GAAlB,IAAyBD,QAA7B,EAAuC;AACnCA,sBAAAA,QAAQ;AACX;AACJ,mBAJD;AAKH,iBAPD;;AASA,qBAAK5D,KAAL,CAAW8D,gBAAX,CAA4B,CAACN,UAAD,EAAkBO,KAAlB,KAAiC;AACzD,wBAAMC,SAAS,GAAGD,KAAK,CAACpD,IAAN,CAAWgD,IAA7B;;AACA,uBAAKvD,iBAAL,CAAuB8C,OAAvB,CAA+B,CAACU,QAAD,EAAWC,GAAX,KAAmB;AAC9C,wBAAIG,SAAS,KAAKH,GAAd,IAAqBD,QAAzB,EAAmC;AAC/BA,sBAAAA,QAAQ;AACX;AACJ,mBAJD;AAKH,iBAPD;AAQH,eAnCL,CAqCI;AACA;AACA;;;AACA;;AAEJ,iBAAK,CAAL;AACI,oBAAMrB,QAAQ,GAAG,IAAIlF,IAAJ,EAAjB;AACAkF,cAAAA,QAAQ,CAACX,YAAT,CAAsBhE,WAAtB;AACA,mBAAKsE,IAAL,CAAUM,QAAV,CAAmBD,QAAnB;AACA,mBAAKL,IAAL,CAAU+B,eAAV,CAA0BxF,QAAQ,CAACyF,UAAT,CAAoBC,IAA9C,EAJJ,CAKI;AACA;AACA;;AACA;AAnDR;;AAsDA,cAAI,KAAKzF,KAAL,CAAW0F,OAAX,CAAmB,CAAnB,MAA0B,CAA9B,EAAiC;AAC7B,gBAAIC,MAAW,GAAG,KAAK1F,UAAL,CAAgBuD,IAAhB,CAAqBoC,cAArB,CAAqC,QAAO,KAAK5F,KAAL,CAAW6F,GAAI,EAA3D,CAAlB;;AACA,gBAAI,CAACF,MAAL,EAAa;AACTA,cAAAA,MAAM,GAAG,IAAIhH,IAAJ,EAAT;AACAgH,cAAAA,MAAM,CAACzC,YAAP,CAAoBhE,WAApB;AACAyG,cAAAA,MAAM,CAACV,IAAP,GAAe,QAAO,KAAKjF,KAAL,CAAW6F,GAAI,EAArC;AACAF,cAAAA,MAAM,CAACG,MAAP,GAAgB,KAAK7F,UAAL,CAAgBuD,IAAhC;AACAmC,cAAAA,MAAM,CAACJ,eAAP,CAAuB,EAAvB;AAEA;AAAA;AAAA,sCAAQtB,WAAR,CAAoB8B,YAApB,CAAiCJ,MAAM,CAACzC,YAAP,CAAoBtE,MAApB,CAAjC,EAA8D,MAA9D;AAEA,oBAAMoH,SAAS,GAAG,IAAIrH,IAAJ,CAAS,WAAT,CAAlB;AACAqH,cAAAA,SAAS,CAAC9C,YAAV,CAAuBhE,WAAvB;AACAyG,cAAAA,MAAM,CAAC7B,QAAP,CAAgBkC,SAAhB;AACA,mBAAKzF,QAAL,GAAgByF,SAAS,CAAC9C,YAAV,CAAuBtE,MAAvB,CAAhB;AACA;AAAA;AAAA,sCAAQqF,WAAR,CAAoB8B,YAApB,CAAiC,KAAKxF,QAAtC,EAAgD,MAAhD;AACA,mBAAKA,QAAL,CAAc6B,IAAd,GAAqBxD,MAAM,CAACqH,IAAP,CAAYC,MAAjC;AACA,mBAAK3F,QAAL,CAAc4F,QAAd,GAAyBvH,MAAM,CAACwH,QAAP,CAAgBC,UAAzC;AACA,mBAAK9F,QAAL,CAAc+F,SAAd,GAA0B,CAA1B;AAEA,oBAAMC,OAAO,GAAG,IAAI5H,IAAJ,CAAS,SAAT,CAAhB;AACA4H,cAAAA,OAAO,CAACrD,YAAR,CAAqBhE,WAArB;AACAyG,cAAAA,MAAM,CAAC7B,QAAP,CAAgByC,OAAhB;AACA,mBAAKjG,MAAL,GAAciG,OAAO,CAACrD,YAAR,CAAqBtE,MAArB,CAAd;AACA;AAAA;AAAA,sCAAQqF,WAAR,CAAoB8B,YAApB,CAAiC,KAAKzF,MAAtC,EAA8C,MAA9C;AACA,mBAAKA,MAAL,CAAY8B,IAAZ,GAAmBxD,MAAM,CAACqH,IAAP,CAAYC,MAA/B;AACA,mBAAK5F,MAAL,CAAY6F,QAAZ,GAAuBvH,MAAM,CAACwH,QAAP,CAAgBC,UAAvC;AACA,mBAAK/F,MAAL,CAAYgG,SAAZ,GAAwB,CAAxB;AACH;;AAED,gBAAIE,IAAI,GAAG,KAAKxG,KAAL,CAAW0F,OAAX,CAAmB,CAAnB,IAAwB,KAAKlC,IAAL,CAAUiD,QAAV,CAAmB9C,CAAtD;AACA,gBAAI+C,IAAI,GAAI,KAAK1G,KAAL,CAAW0F,OAAX,CAAmB,CAAnB,IAAwB,KAAKlC,IAAL,CAAUiD,QAAV,CAAmB7C,CAAvD;AACA,gBAAI+C,MAAM,GAAG,KAAK3G,KAAL,CAAW0F,OAAX,CAAmB,CAAnB,CAAb;AACAC,YAAAA,MAAM,CAAClC,WAAP,CAAmB+C,IAAnB,EAAyBE,IAAzB;AACAf,YAAAA,MAAM,CAACiB,QAAP,CAAgBD,MAAM,GAAChB,MAAM,CAACkB,QAAP,GAAkBlD,CAAzC,EAA2CgC,MAAM,CAACkB,QAAP,GAAkBjD,CAA7D;AACA+B,YAAAA,MAAM,CAACmB,MAAP,GAAgB,KAAhB;AACH;AACJ;;AAEDC,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,eAAKnF,aAAL,CAAmB2C,OAAnB,CAA2B,CAACyC,KAAD,EAAQC,KAAR,KAAkB;AACzC,kBAAMC,QAAQ,GAAG,KAAKrF,aAAL,CAAmBoF,KAAnB,CAAjB;;AACA,kBAAME,IAAI,GAAG,KAAK9F,KAAL,CAAW+F,QAAX,CAAoBF,QAApB,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AACN,kBAAI1D,GAAG,GAAG,KAAK9B,YAAL,CAAkBsF,KAAlB,EAAyBI,GAAzB,CAA6B,IAAIvI,IAAJ,CAASqI,IAAI,CAACG,MAAd,EAAsBH,IAAI,CAACI,MAA3B,CAA7B,CAAV;;AACAP,cAAAA,KAAK,CAACzD,IAAN,CAAWC,WAAX,CAAuBC,GAAG,CAACC,CAA3B,EAA8BD,GAAG,CAACE,CAAlC;AACH;AACJ,WAPD;;AASA,cAAI,CAAC,KAAKlB,MAAV,EAAkB;AACd,gBAAI,KAAKtB,WAAT,EAAsB;AAClB,mBAAKD,UAAL;;AACA,kBAAI,KAAKA,UAAL,GAAkB,CAAtB,EAAyB;AACrB,qBAAKA,UAAL,GAAkB,CAAlB;AACA,qBAAKC,WAAL,GAAmB,KAAnB;AACH;AACJ;;AAED,iBAAKqG,OAAL,CAAajD,OAAb,CAAsBkD,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACC,MAAL,CAAYX,SAAZ;AACH,aAFD;AAGH;AACJ;;AACDY,QAAAA,SAAS,CAAC5E,QAAD,EAA4B;AACjC,cAAI,CAAC,KAAKN,MAAN,IAAgB,KAAKmF,UAAzB,EAAqC;AACjC,gBAAI7E,QAAQ,CAAC8E,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,kBAAIC,MAAM,GAAG/E,QAAQ,CAAC8E,MAAT,CAAgBE,SAAhB,CAA0B,IAA1B,CAAb;;AACA,kBAAI;AACA;AAAA;AAAA,wCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8ClF,QAAQ,CAAC8E,MAAT,CAAgBtE,IAAhB,CAAqB2E,WAArB,EAA9C,EAAiFJ,MAAjF;AACH,eAFD,CAEE,OAAOpD,KAAP,EAAc;AACZyD,gBAAAA,OAAO,CAACzD,KAAR,CAAcA,KAAd;AACH;;AACDoD,cAAAA,MAAM,GAAGM,IAAI,CAACC,GAAL,CAASP,MAAM,GAAG,EAAlB,EAAsBA,MAAM,GAAG,KAAKtH,OAApC,CAAT;AACA,mBAAK8H,IAAL,CAAUR,MAAV;AACH,aATD,MASO,IAAI/E,QAAQ,CAAC8E,MAAT;AAAA;AAAA,uCAAJ,EAA0C,CAC7C;AACH;AACJ;AACJ;;AAEDS,QAAAA,IAAI,CAACR,MAAD,EAA0B;AAC1B,cAAI,KAAKrF,MAAL,IAAe,CAAC,KAAKmF,UAAzB,EAAqC;AACjC,mBAAO,KAAP;AACH;;AACD,eAAKW,QAAL,CAAc,CAACT,MAAf;;AACA,eAAKU,QAAL;;AACA,cAAI,CAAC,KAAK/F,MAAV,EAAkB;AACd,iBAAKgG,UAAL;AACH;;AACD,iBAAO,IAAP;AACH;;AAEDF,QAAAA,QAAQ,CAACG,MAAD,EAAuB;AAC3B,cAAIC,MAAM,GAAGD,MAAb;AACA,cAAIE,KAAK,GAAG,KAAKzI,MAAL,GAAcuI,MAA1B;;AAEA,cAAIE,KAAK,GAAG,CAAZ,EAAe;AACXD,YAAAA,MAAM,GAAG,CAAC,KAAKxI,MAAf;AACH;;AAED,eAAKA,MAAL,GAAcyI,KAAd;;AACA,cAAI,KAAKzI,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKA,MAAL,GAAc,CAAd;AACH;;AAED,cAAI,KAAKH,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBuI,QAAhB,CAAyBI,MAAzB;AACH;;AAED,eAAKpG,aAAL;AACH;;AAEDiG,QAAAA,QAAQ,GAAS;AACb,cAAI,KAAKrI,MAAL,IAAe,KAAKM,QAAxB,EAAkC;AAC9B,iBAAKK,QAAL,GAAgB,IAAhB;;AACA,iBAAK+H,cAAL,CAAoB,KAAKnI,aAAL,GAAqB,CAAzC;;AACA,iBAAKA,aAAL;;AACA,iBAAKiC,aAAL;;AAEA,gBAAI,KAAKlC,QAAL,GAAgB,CAApB,EAAuB;AACnB,mBAAKqI,IAAL;AACH;AACJ;AACJ;;AAEDnG,QAAAA,aAAa,GAAS;AAClB,cAAI,KAAKjC,aAAL,GAAqB,KAAKX,KAAL,CAAWgJ,OAAX,CAAmBC,MAA5C,EAAoD;AAChD,iBAAKvI,QAAL,GAAgB,KAAKV,KAAL,CAAWgJ,OAAX,CAAmB,KAAKrI,aAAxB,CAAhB;AACH,WAFD,MAEO;AACH,iBAAKD,QAAL,GAAgB,CAAC,CAAjB;AACH;AACJ;;AAEDqI,QAAAA,IAAI,GAAS;AACT,eAAKrG,MAAL,GAAc,IAAd;;AACA,cAAI;AACA,gBAAI,KAAKpC,MAAT,EAAiB;AACb,mBAAKA,MAAL,CAAYkD,IAAZ,CAAiBsC,MAAjB,CAAyBgB,MAAzB,GAAkC,KAAlC;AACH;AACJ,WAJD,CAIE,OAAOnC,KAAP,EAAc;AACZyD,YAAAA,OAAO,CAACzD,KAAR,CAAcA,KAAd;AACH;;AAED,cAAI,KAAKnE,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB0I,IAAnB;;AACA,iBAAK1I,aAAL,GAAqB,IAArB;AACH;;AAED,cAAI,KAAKN,YAAT,EAAuB;AACnB,iBAAKA,YAAL,CAAkBqD,QAAlB,GAA6B,KAA7B;AACH;;AAED,eAAK4F,YAAL;;AACA,cAAI,KAAKlJ,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBmJ,aAAhB,CAA8B,IAA9B;AACH;AACJ;;AAED5G,QAAAA,aAAa,GAAS;AAClB,cAAI,KAAKlC,MAAT,EAAiB;AACb,gBAAI,KAAKE,aAAT,EAAwB;AACpB,mBAAKA,aAAL,CAAmB0I,IAAnB;;AACA,mBAAK1I,aAAL,GAAqB,IAArB;AACH;;AAED,iBAAKF,MAAL,CAAYgG,SAAZ,GAAwB,KAAKlG,MAAL,GAAc,KAAKC,MAA3C;AAEA,kBAAMgJ,cAAc,GAAGhB,IAAI,CAACiB,GAAL,CAAS,KAAK/I,QAAL,CAAe+F,SAAf,GAA2B,KAAKhG,MAAL,CAAYgG,SAAhD,CAAvB;AACA,iBAAK9F,aAAL,GAAqB,IAAIrB,KAAJ,CAAU,KAAKoB,QAAf,EAChBgJ,EADgB,CACbF,cADa,EACG;AAAE/C,cAAAA,SAAS,EAAE,KAAKhG,MAAL,CAAYgG;AAAzB,aADH,EAEhBkD,IAFgB,CAEX,MAAM;AACR,mBAAKhJ,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhBiJ,KALgB,EAArB;AAMH;AACJ;;AAEDC,QAAAA,QAAQ,CAAC3E,aAAD,EAAwB4E,IAAxB,EAAuCzE,QAAiB,GAAG,IAA3D,EAA0E;AAC9E,cAAI,CAAC,KAAK5D,KAAN,IAAe,KAAKC,QAAL,KAAkBwD,aAArC,EAAoD;AAChD,mBAAO,KAAP;AACH;;AACD,eAAKxD,QAAL,GAAgBwD,aAAhB;;AACA,eAAKvD,YAAL,CAAkBoI,GAAlB,CAAsB7E,aAAtB,EAAqCG,QAArC;;AACA,eAAK5D,KAAL,CAAWuI,YAAX,CAAwB,CAAxB,EAA2B9E,aAA3B,EAA0C4E,IAA1C;;AACA,iBAAO,IAAP;AACH;;AAEDG,QAAAA,gBAAgB,CAACxE,SAAD,EAAoBJ,QAApB,EAA8C;AAC1D,eAAKxD,iBAAL,CAAuBkI,GAAvB,CAA2BtE,SAA3B,EAAsCJ,QAAtC;AACH;;AAEDzC,QAAAA,OAAO,CAACsH,SAAD,EAA0B;AAC7B,cAAI,KAAKzI,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWmB,OAAX,CAAoB,IAAG,KAAKxC,UAAL,CAAgB+J,SAAhB,GAA4B,CAAE,IAAGD,SAAU,EAAlE;AACH;AACJ;;AAEDE,QAAAA,SAAS,CAACC,MAAD,EAAuB;AAC5B,cAAI,CAAC,KAAKxH,MAAV,EAAkB;AACd,iBAAK9B,OAAL,GAAesJ,MAAf;AACH;AACJ;;AAEDxB,QAAAA,UAAU,GAAS,CACf;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDyB,QAAAA,cAAc,GAAS;AACnB,gBAAMC,eAAe,GAAG,KAAxB,CADmB,CACY;;AAC/BvL,UAAAA,KAAK,CAAC,KAAK2E,IAAN,CAAL,CACK+F,EADL,CACQa,eADR,EACyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyBoL,YAAAA,KAAK,EAAE,CAAC;AAAjC,WADzB,EAEKd,EAFL,CAEQ,IAAIa,eAFZ,EAE6B;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAd;AAAsBoL,YAAAA,KAAK,EAAE;AAA7B,WAF7B,EAGKd,EAHL,CAGQ,IAAIa,eAHZ,EAG6B;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyBoL,YAAAA,KAAK,EAAE;AAAhC,WAH7B,EAIKd,EAJL,CAIQ,IAAIa,eAJZ,EAI6B;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WAJ7B,EAKKsK,EALL,CAKQ,IAAIa,eALZ,EAK6B;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN;AAAd,WAL7B,EAMKsK,EANL,CAMQa,eANR,EAMyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WANzB,EAOKsK,EAPL,CAOQa,eAPR,EAOyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL;AAAd,WAPzB,EAQKsK,EARL,CAQQa,eARR,EAQyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WARzB,EASKsK,EATL,CASQa,eATR,EASyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WATzB,EAUKsK,EAVL,CAUQa,eAVR,EAUyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAVzB,EAWKsK,EAXL,CAWQa,eAXR,EAWyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAXzB,EAYKsK,EAZL,CAYQa,eAZR,EAYyB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAZzB,EAaKsK,EAbL,CAaQa,eAbR,EAayB;AAAE3D,YAAAA,QAAQ,EAAExH,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAbzB,EAcKwK,KAdL;AAeH;;AAEDa,QAAAA,SAAS,GAAS;AACd,eAAK,MAAMrD,KAAX,IAAoB,KAAKpF,aAAzB,EAAwC;AACpChD,YAAAA,KAAK,CAACoI,KAAK,CAACzD,IAAP,CAAL,CACK+F,EADL,CACQ,IAAI,KADZ,EACmB;AAAEgB,cAAAA,OAAO,EAAE;AAAX,aADnB,EACmC;AADnC,aAEKd,KAFL;AAGH;AACJ;;AAED5G,QAAAA,WAAW,GAAS;AAChB,eAAK,MAAMoE,KAAX,IAAoB,KAAKpF,aAAzB,EAAwC;AACpCoF,YAAAA,KAAK,CAACzD,IAAN,CAAWsD,MAAX,GAAoB,KAApB;;AACA,iBAAKnF,cAAL,CAAoB6I,IAApB,CAAyBvD,KAAzB;;AACA,iBAAKhH,UAAL,CAAgBwK,WAAhB,CAA4BxD,KAA5B;AACH;;AACD,eAAKpF,aAAL,CAAmBoH,MAAnB,GAA4B,CAA5B;AACA,eAAKrH,YAAL,CAAkBqH,MAAlB,GAA2B,CAA3B;AACA,eAAKnH,aAAL,CAAmBmH,MAAnB,GAA4B,CAA5B;AACH;;AAEK,YAAFyB,EAAE,GAAW;AACb,iBAAO,KAAK1K,KAAL,CAAW0K,EAAlB;AACH;;AAES,YAANC,MAAM,GAAW;AACjB,iBAAO,KAAK3K,KAAL,CAAW6F,GAAlB;AACH;;AAED+E,QAAAA,MAAM,GAAY;AACd,iBAAO,KAAKzK,SAAL,KAAmB,CAA1B;AACH;;AAED0K,QAAAA,WAAW,GAAW;AAClB,iBAAO,KAAK7K,KAAL,CAAWoC,IAAlB;AACH;;AAED0I,QAAAA,eAAe,CAACC,KAAD,EAAwB;AACnC,cAAIA,KAAK,CAAC9B,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAK7I,MAAL,IAAe2K,KAAK,CAAC,CAAD,CAApB;AACA,iBAAK1K,MAAL,GAAc,KAAKD,MAAnB;AACA,iBAAK4K,MAAL,IAAeD,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKE,WAAL,IAAoBF,KAAK,CAAC,CAAD,CAAzB;AACH;AACJ;;AAEa,YAAVlD,UAAU,GAAY;AACtB,iBAAO,KAAK7G,WAAZ;AACH;;AAEa,YAAV6G,UAAU,CAACqD,KAAD,EAAiB;AAC3B,eAAKlK,WAAL,GAAmBkK,KAAnB;AACH;;AAEa,YAAVC,UAAU,GAAQ;AAClB,iBAAO,KAAKlL,UAAZ;AACH;;AAEQ,YAALmL,KAAK,GAAW;AAChB,iBAAO,KAAKhL,MAAZ;AACH;;AAEQ,YAALiL,KAAK,GAAW;AAChB,iBAAO,KAAKhL,MAAZ;AACH;;AACDyI,QAAAA,cAAc,CAACwC,UAAD,EAA2B;AACrC,eAAKC,eAAL;;AACA,eAAK9I,OAAL,CAAa6I,UAAb;AACH;;AAEDC,QAAAA,eAAe,GAAS;AACpB,cAAI;AACA,gBAAI,KAAKtL,UAAT,EAAqB;AACjB,mBAAKA,UAAL,CAAgBuL,kBAAhB,CAAmC,IAAnC;AACH;;AACD,gBAAI,KAAK9I,MAAT,EAAiB;AACb,mBAAKgH,QAAL,CAAe,OAAM,KAAKzJ,UAAL,CAAgB+J,SAAhB,GAA4B,CAAE,EAAnD,EAAsD,IAAtD;AACH;AACJ,WAPD,CAOE,OAAOrF,KAAP,EAAc;AACZyD,YAAAA,OAAO,CAACzD,KAAR,CAAcA,KAAd;AACH;AACJ;;AAED8G,QAAAA,aAAa,GAAQ;AACjB,cAAIC,SAAS,GAAG,KAAK/J,cAAL,CAAoBgK,GAApB,EAAhB;;AACA,cAAI,CAACD,SAAL,EAAgB;AACZ,kBAAME,SAAS,GAAG,IAAIjN,IAAJ,EAAlB;AACAiN,YAAAA,SAAS,CAAC1I,YAAV,CAAuBhE,WAAvB;AACA,iBAAKsE,IAAL,CAAUM,QAAV,CAAmB8H,SAAnB;AACAF,YAAAA,SAAS,GAAGE,SAAS,CAAC1I,YAAV,CAAuBlE,EAAE,CAAC+E,QAA1B,CAAZ;AACA2H,YAAAA,SAAS,CAAC1H,YAAV,GAAyB;AAAA;AAAA,oCAAQC,WAAR,CAAoB4H,aAA7C;AACAH,YAAAA,SAAS,CAACrH,kBAAV,GAA+B,KAA/B;;AACA,iBAAK1C,cAAL,CAAoB6I,IAApB,CAAyBkB,SAAzB;AACH;;AACDA,UAAAA,SAAS,CAAClI,IAAV,CAAesD,MAAf,GAAwB,IAAxB;AACA,iBAAO4E,SAAP;AACH;;AAEDvC,QAAAA,YAAY,GAAS;AACjB,cAAI,KAAKnJ,KAAL,CAAW0K,EAAX,IAAiB,GAAjB,IAAwB,KAAK1K,KAAL,CAAW0K,EAAX,GAAgB,GAA5C,EAAiD;AAC7C;AACH;;AACD,eAAKpJ,KAAL,CAAWuI,YAAX,CAAwB,CAAxB,EAA4B,QAAO,KAAK5J,UAAL,CAAgB+J,SAAhB,GAA4B,CAAE,EAAjE,EAAoE,KAApE;AACH;;AAED8B,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,cAAI,CAAC,KAAKnB,MAAL,EAAD,IAAkB,KAAK1K,YAA3B,EAAyC;AACrC,iBAAK4G,MAAL,GAAciF,SAAd;AACA,iBAAK7L,YAAL,CAAkBqD,QAAlB,GAA6BwI,SAA7B;;AAEA,gBAAI;AACA,kBAAI,KAAKzL,MAAT,EAAiB;AACb,qBAAKA,MAAL,CAAYkD,IAAZ,CAAiBsC,MAAjB,CAAwBgB,MAAxB,GAAiCiF,SAAjC;AACH;AACJ,aAJD,CAIE,OAAOpH,KAAP,EAAc;AACZyD,cAAAA,OAAO,CAACzD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACH;AACJ;AACJ;;AAEDqH,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAK9L,YAAL,GAAoB,KAAKA,YAAL,CAAkBqD,QAAtC,GAAiD,KAAxD;AACH;;AA5e0C,O,UACpCiC,U,GAAa1G,IAAI,CAAC;AACrBmN,QAAAA,WAAW,EAAE,CAAC,EADO;AAErBxG,QAAAA,IAAI,EAAE,CAAC,CAFc;AAGrByG,QAAAA,QAAQ,EAAE,CAAC;AAHU,OAAD,C", "sourcesContent": ["import { _decorator, Component, Node, Sprite, tween, Color, Enum, Vec2, sp, v2, v3, UITransform, Tween, size } from 'cc';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport { MainPlane } from '../mainPlane/MainPlane';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport BossHurt from './BossHurt';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport EnemyEffectLayer from '../../layer/EnemyEffectLayer';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossUnit')\r\nexport default class BossUnit extends BossHurt {\r\n    static UnitZIndex = Enum({\r\n        SmokeBottom: -10,\r\n        Skel: -9,\r\n        SmokeTop: -8,\r\n    });\r\n\r\n    _data: any = null;\r\n    _bossPlane: any = null;\r\n    _collideComp: FBoxCollider;\r\n    _unitType: number = 0;\r\n    _curHp: number = 0;\r\n    _maxHp: number = 0;\r\n    _hpBar: Sprite | null = null;\r\n    _hpWhite: Sprite | null = null;\r\n    _hpWhiteTween: any = null;\r\n    defence: number = 0;\r\n    _hpStage: number = 0;\r\n    _hpStageIndex: number = 0;\r\n    _action: number = GameEnum.BossAction.Normal;\r\n    _damaged: boolean = false;\r\n    _damageable: boolean = false;\r\n    _bSmoking: boolean = false;\r\n    _whiteNode: Node | null = null;\r\n    _winkCount: number = 0;\r\n    _bWinkWhite: boolean = false;\r\n    _winkAct: any = null;\r\n    _skel: any = null;\r\n    _curAnim: string = '';\r\n    _skelCallMap: Map<string, Function> = new Map();\r\n    _skelEventCallMap: Map<string, Function> = new Map();\r\n    _smokeSkelPool: any[] = [];\r\n    _smokePosArr: Vec2[] = [];\r\n    _smokeSkelArr: any[] = [];\r\n    _smokeBoneArr: string[] = [];\r\n\r\n    onLoad(): void {\r\n        // 初始化逻辑\r\n    }\r\n\r\n    init(data: any, bossPlane: any): void {\r\n        this.reset();\r\n        this.type = GameEnum.EnemyType.BossUnit;\r\n        this._data = data;\r\n        this._bossPlane = bossPlane;\r\n\r\n        this.initData();\r\n        this._initUI();\r\n        this._refreshHpBar();\r\n        this.setSkin(0);\r\n\r\n        if (this._curHp > 0) {\r\n            this._unitType = 1;\r\n            this.isDead = false;\r\n            this._initCollide();\r\n            this._checkHpStage();\r\n        } else {\r\n            this._unitType = 0;\r\n            this.isDead = true;\r\n        }\r\n    }\r\n\r\n    reset(): void {\r\n        this._curAnim = '';\r\n        this._hpStage = 0;\r\n        this._hpStageIndex = 0;\r\n        this._action = GameEnum.BossAction.Normal;\r\n        this.removeSmoke();\r\n    }\r\n\r\n    initData(): void {\r\n        this._curHp = this._data.hp;\r\n        this._maxHp = this._curHp;\r\n    }\r\n\r\n    _initCollide(): void {\r\n        \r\n        const colliderData = this._data.collider;\r\n        this._collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this._collideComp.init(this,size(colliderData.width, colliderData.height)); // 初始化碰撞组件\r\n        this._collideComp.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this._collideComp.isEnable = false;\r\n    }\r\n\r\n    _initUI(): void {\r\n        this.node.setPosition(this._data.pos.x, this._data.pos.y);\r\n\r\n        switch (this._data.type) {\r\n            case 0:\r\n                if (!this._skel) {\r\n                    const skelNode = new Node(\"skelNode\");\r\n                    this.node.addChild(skelNode);\r\n                    // this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)\r\n                    this._skel = skelNode.addComponent(sp.Skeleton);\r\n                    this._skel.skeletonData = GameIns.bossManager.skelDataMap.get(this._data.anim);\r\n                    this._skel.premultipliedAlpha = false;\r\n                    this._bossPlane.addSpine(this._skel);\r\n\r\n                    this._data.mixArr.forEach((mix: [string, string]) => {\r\n                        try {\r\n                            this._skel.setMix(mix[0], mix[1], 0.5);\r\n                        } catch (error) {\r\n                            Tools.log('Boss unit mix error:', mix);\r\n                        }\r\n                    });\r\n\r\n                    this._skel.setCompleteListener((trackEntry: any) => {\r\n                        const animationName = trackEntry.animation ? trackEntry.animation.name : '';\r\n                        this._skelCallMap.forEach((callback, key) => {\r\n                            if (animationName === key && callback) {\r\n                                callback();\r\n                            }\r\n                        });\r\n                    });\r\n\r\n                    this._skel.setEventListener((trackEntry: any, event: any) => {\r\n                        const eventName = event.data.name;\r\n                        this._skelEventCallMap.forEach((callback, key) => {\r\n                            if (eventName === key && callback) {\r\n                                callback();\r\n                            }\r\n                        });\r\n                    });\r\n                }\r\n\r\n                // this._winkAct = new Tween()\r\n                //     .to(0, { color: new Color(this._data.hurtColor.getR(), this._data.hurtColor.getG(), this._data.hurtColor.getB()) })\r\n                //     .to(0.1, { color: Color.WHITE });\r\n                break;\r\n\r\n            case 1:\r\n                const skelNode = new Node();\r\n                skelNode.addComponent(UITransform);\r\n                this.node.addChild(skelNode);\r\n                this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)\r\n                // this._winkAct = tween()\r\n                //     .to(0, { opacity: 180 })\r\n                //     .to(3 * GameConst.ActionFrameTime, { opacity: 0 });\r\n                break;\r\n        }\r\n\r\n        if (this._data.hpParam[0] !== 0) {\r\n            let hpNode:Node = this._bossPlane.node.getChildByName(`blood${this._data.uId}`);\r\n            if (!hpNode) {\r\n                hpNode = new Node();\r\n                hpNode.addComponent(UITransform);\r\n                hpNode.name = `blood${this._data.uId}`;\r\n                hpNode.parent = this._bossPlane.node;\r\n                hpNode.setSiblingIndex(10);\r\n\r\n                GameIns.bossManager.setBossFrame(hpNode.addComponent(Sprite), 'hp_0');\r\n\r\n                const whiteNode = new Node(\"whiteNode\");\r\n                whiteNode.addComponent(UITransform);\r\n                hpNode.addChild(whiteNode);\r\n                this._hpWhite = whiteNode.addComponent(Sprite);\r\n                GameIns.bossManager.setBossFrame(this._hpWhite, 'hp_2');\r\n                this._hpWhite.type = Sprite.Type.FILLED;\r\n                this._hpWhite.fillType = Sprite.FillType.HORIZONTAL;\r\n                this._hpWhite.fillRange = 1;\r\n\r\n                const barNode = new Node(\"barNode\");\r\n                barNode.addComponent(UITransform);\r\n                hpNode.addChild(barNode);\r\n                this._hpBar = barNode.addComponent(Sprite);\r\n                GameIns.bossManager.setBossFrame(this._hpBar, 'hp_1');\r\n                this._hpBar.type = Sprite.Type.FILLED;\r\n                this._hpBar.fillType = Sprite.FillType.HORIZONTAL;\r\n                this._hpBar.fillRange = 1;\r\n            }\r\n\r\n            let posX = this._data.hpParam[1] + this.node.position.x;\r\n            let posY  = this._data.hpParam[2] + this.node.position.y;\r\n            let scaleX = this._data.hpParam[3];\r\n            hpNode.setPosition(posX, posY);\r\n            hpNode.setScale(scaleX*hpNode.getScale().x,hpNode.getScale().y)\r\n            hpNode.active = false;\r\n        }\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        this._smokeSkelArr.forEach((smoke, index) => {\r\n            const boneName = this._smokeBoneArr[index];\r\n            const bone = this._skel.findBone(boneName);\r\n            if (bone) {\r\n                let pos = this._smokePosArr[index].add(new Vec2(bone.worldX, bone.worldY));\r\n                smoke.node.setPosition(pos.x, pos.y);\r\n            }\r\n        });\r\n\r\n        if (!this.isDead) {\r\n            if (this._bWinkWhite) {\r\n                this._winkCount++;\r\n                if (this._winkCount > 8) {\r\n                    this._winkCount = 0;\r\n                    this._bWinkWhite = false;\r\n                }\r\n            }\r\n\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n        }\r\n    }\r\n    onCollide(collider: FCollider): void {\r\n        if (!this.isDead && this.damageable) {\r\n            if (collider.entity instanceof Bullet) {\r\n                let damage = collider.entity.getAttack(this);\r\n                try {\r\n                    GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(),damage);\r\n                } catch (error) {\r\n                    console.error(error);\r\n                }\r\n                damage = Math.max(damage / 10, damage - this.defence);\r\n                this.hurt(damage);\r\n            } else if (collider.entity instanceof MainPlane) {\r\n                // Handle collision with the main plane\r\n            }\r\n        }\r\n    }\r\n\r\n    hurt(damage: number): boolean {\r\n        if (this.isDead || !this.damageable) {\r\n            return false;\r\n        }\r\n        this.hpChange(-damage);\r\n        this._checkHp();\r\n        if (!this.isDead) {\r\n            this._winkWhite();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    hpChange(amount: number): void {\r\n        let change = amount;\r\n        let newHp = this._curHp + amount;\r\n\r\n        if (newHp < 0) {\r\n            change = -this._curHp;\r\n        }\r\n\r\n        this._curHp = newHp;\r\n        if (this._curHp < 0) {\r\n            this._curHp = 0;\r\n        }\r\n\r\n        if (this._bossPlane) {\r\n            this._bossPlane.hpChange(change);\r\n        }\r\n\r\n        this._refreshHpBar();\r\n    }\r\n\r\n    _checkHp(): void {\r\n        if (this._curHp <= this._hpStage) {\r\n            this._damaged = true;\r\n            this._playStageAnim(this._hpStageIndex + 1);\r\n            this._hpStageIndex++;\r\n            this._checkHpStage();\r\n\r\n            if (this._hpStage < 0) {\r\n                this._die();\r\n            }\r\n        }\r\n    }\r\n\r\n    _checkHpStage(): void {\r\n        if (this._hpStageIndex < this._data.hpStage.length) {\r\n            this._hpStage = this._data.hpStage[this._hpStageIndex];\r\n        } else {\r\n            this._hpStage = -1;\r\n        }\r\n    }\r\n\r\n    _die(): void {\r\n        this.isDead = true;\r\n        try {\r\n            if (this._hpBar) {\r\n                this._hpBar.node.parent!.active = false;\r\n            }\r\n        } catch (error) {\r\n            console.error(error);\r\n        }\r\n\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        if (this._collideComp) {\r\n            this._collideComp.isEnable = false;\r\n        }\r\n\r\n        this._playDieAnim();\r\n        if (this._bossPlane) {\r\n            this._bossPlane.unitDestroyed(this);\r\n        }\r\n    }\r\n\r\n    _refreshHpBar(): void {\r\n        if (this._hpBar) {\r\n            if (this._hpWhiteTween) {\r\n                this._hpWhiteTween.stop();\r\n                this._hpWhiteTween = null;\r\n            }\r\n\r\n            this._hpBar.fillRange = this._curHp / this._maxHp;\r\n\r\n            const fillDifference = Math.abs(this._hpWhite!.fillRange - this._hpBar.fillRange);\r\n            this._hpWhiteTween = new Tween(this._hpWhite)\r\n                .to(fillDifference, { fillRange: this._hpBar.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        }\r\n    }\r\n\r\n    playSkel(animationName: string, loop: boolean, callback:Function = null): boolean {\r\n        if (!this._skel || this._curAnim === animationName) {\r\n            return false;\r\n        }\r\n        this._curAnim = animationName;\r\n        this._skelCallMap.set(animationName, callback);\r\n        this._skel.setAnimation(0, animationName, loop);\r\n        return true;\r\n    }\r\n\r\n    setEventCallback(eventName: string, callback: Function): void {\r\n        this._skelEventCallMap.set(eventName, callback);\r\n    }\r\n\r\n    setSkin(skinIndex: number): void {\r\n        if (this._skel) {\r\n            this._skel.setSkin(`s${this._bossPlane.formIndex + 1}_${skinIndex}`);\r\n        }\r\n    }\r\n\r\n    setAction(action: number): void {\r\n        if (!this.isDead) {\r\n            this._action = action;\r\n        }\r\n    }\r\n\r\n    _winkWhite(): void {\r\n        // if (!this._bWinkWhite && this._action < 2) { // Assuming 2 is the threshold for attack actions\r\n        //     this._bWinkWhite = true;\r\n        //     if (this._skel && this._skel.node) {\r\n        //         this._winkAct.clone(this._skel.node).start();\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    _playShakeAnim(): void {\r\n        const actionFrameTime = 0.016; // Assuming 60 FPS\r\n        tween(this.node)\r\n            .to(actionFrameTime, { position: v3(11, -16), angle: -1 })\r\n            .to(2 * actionFrameTime, { position: v3(7, 2), angle: 1 })\r\n            .to(2 * actionFrameTime, { position: v3(20, -11), angle: 0 })\r\n            .to(2 * actionFrameTime, { position: v3(28, 5) })\r\n            .to(2 * actionFrameTime, { position: v3(13, -7) })\r\n            .to(actionFrameTime, { position: v3(17, 1) })\r\n            .to(actionFrameTime, { position: v3(4, -8) })\r\n            .to(actionFrameTime, { position: v3(14, 2) })\r\n            .to(actionFrameTime, { position: v3(-1, -6) })\r\n            .to(actionFrameTime, { position: v3(5, 4) })\r\n            .to(actionFrameTime, { position: v3(-3, -7) })\r\n            .to(actionFrameTime, { position: v3(2, 1) })\r\n            .to(actionFrameTime, { position: v3(0, 0) })\r\n            .start();\r\n    }\r\n\r\n    hideSmoke(): void {\r\n        for (const smoke of this._smokeSkelArr) {\r\n            tween(smoke.node)\r\n                .to(5 * 0.016, { opacity: 0 }) // Assuming 60 FPS\r\n                .start();\r\n        }\r\n    }\r\n\r\n    removeSmoke(): void {\r\n        for (const smoke of this._smokeSkelArr) {\r\n            smoke.node.active = false;\r\n            this._smokeSkelPool.push(smoke);\r\n            this._bossPlane.removeSpine(smoke);\r\n        }\r\n        this._smokeSkelArr.length = 0;\r\n        this._smokePosArr.length = 0;\r\n        this._smokeBoneArr.length = 0;\r\n    }\r\n\r\n    get id(): number {\r\n        return this._data.id;\r\n    }\r\n\r\n    get unitId(): number {\r\n        return this._data.uId;\r\n    }\r\n\r\n    isBody(): boolean {\r\n        return this._unitType === 0;\r\n    }\r\n\r\n    getUnitType(): number {\r\n        return this._data.type;\r\n    }\r\n\r\n    setPropertyRate(rates: number[]): void {\r\n        if (rates.length > 2) {\r\n            this._curHp *= rates[0];\r\n            this._maxHp = this._curHp;\r\n            this.attack *= rates[1];\r\n            this._collideAtk *= rates[2];\r\n        }\r\n    }\r\n\r\n    get damageable(): boolean {\r\n        return this._damageable;\r\n    }\r\n\r\n    set damageable(value: boolean) {\r\n        this._damageable = value;\r\n    }\r\n\r\n    get bossEntity(): any {\r\n        return this._bossPlane;\r\n    }\r\n\r\n    get curHp(): number {\r\n        return this._curHp;\r\n    }\r\n\r\n    get maxHp(): number {\r\n        return this._maxHp;\r\n    }\r\n    _playStageAnim(stageIndex: number): void {\r\n        this._onStageAnimEnd()\r\n        this.setSkin(stageIndex);\r\n    }\r\n\r\n    _onStageAnimEnd(): void {\r\n        try {\r\n            if (this._bossPlane) {\r\n                this._bossPlane.unitDestroyAnimEnd(this);\r\n            }\r\n            if (this.isDead) {\r\n                this.playSkel(`idle${this._bossPlane.formIndex + 1}`, true);\r\n            }\r\n        } catch (error) {\r\n            console.error(error);\r\n        }\r\n    }\r\n\r\n    _getSmokeAnim(): any {\r\n        let smokeAnim = this._smokeSkelPool.pop();\r\n        if (!smokeAnim) {\r\n            const smokeNode = new Node();\r\n            smokeNode.addComponent(UITransform);\r\n            this.node.addChild(smokeNode);\r\n            smokeAnim = smokeNode.addComponent(sp.Skeleton);\r\n            smokeAnim.skeletonData = GameIns.bossManager.smokeSkelData;\r\n            smokeAnim.premultipliedAlpha = false;\r\n            this._smokeSkelPool.push(smokeAnim);\r\n        }\r\n        smokeAnim.node.active = true;\r\n        return smokeAnim;\r\n    }\r\n\r\n    _playDieAnim(): void {\r\n        if (this._data.id >= 200 && this._data.id < 250) {\r\n            return;\r\n        }\r\n        this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);\r\n    }\r\n\r\n    setCollideAble(isEnabled: boolean) {\r\n        if (!this.isBody() && this._collideComp) {\r\n            this.active = isEnabled;\r\n            this._collideComp.isEnable = isEnabled;\r\n    \r\n            try {\r\n                if (this._hpBar) {\r\n                    this._hpBar.node.parent.active = isEnabled;\r\n                }\r\n            } catch (error) {\r\n                console.error(\"Error setting collide ability:\", error);\r\n            }\r\n        }\r\n    }\r\n    \r\n    getCollideAble(): boolean {\r\n        return this._collideComp ? this._collideComp.isEnable : false;\r\n    }\r\n}"]}