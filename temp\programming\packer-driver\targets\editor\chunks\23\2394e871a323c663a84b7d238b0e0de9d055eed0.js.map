{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts"], "names": ["LevelDataEventTriggerLog", "LevelDataEventTrigger", "LevelDataEventTriggerType", "constructor", "Log", "message"], "mappings": ";;;gFAEaA,wB;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,yB,iBAAAA,yB;;;;;;;0CAEnBF,wB,GAAN,MAAMA,wBAAN;AAAA;AAAA,0DAA6D;AAEhEG,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sEAA0BC,GAAhC;AADU,eADPC,OACO,GADW,EACX;AAEb;;AAJ+D,O", "sourcesContent": ["import { LevelDataEventTrigger, LevelDataEventTriggerType } from \"./LevelDataEventTrigger\";\r\n\r\nexport class LevelDataEventTriggerLog extends LevelDataEventTrigger {\r\n    public message: string = \"\";\r\n    constructor() {\r\n        super(LevelDataEventTriggerType.Log);\r\n    }\r\n}\r\n"]}