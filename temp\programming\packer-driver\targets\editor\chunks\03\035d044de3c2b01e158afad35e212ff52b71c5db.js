System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, EventManager, _class, _crd, EventMgr;

  _export("default", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9c640NICl9D0ogZhZ8MenVa", "EventManager", undefined);

      /**
      * 事件管理器（带优先级处理）
      */
      _export("default", EventManager = class EventManager {
        constructor() {
          /**
           * key: 事件名
           * value: 缓存事件
           */
          this._eventCacheMap = new Map();
        }

        /**
         * 广播事件
         *
         * @param eventName 事件名
         * @param param 传递的剩余不定参数
         */
        emit(eventName, ...param) {
          let eventCacheArray = this._eventCacheMap.get(eventName);

          if (eventCacheArray) {
            // 先按照优先级排序（优先级高的先执行）
            const sortedEvents = [...eventCacheArray].sort((a, b) => b.priority - a.priority);
            let eventCache = null;

            for (let i = 0; i < sortedEvents.length; i++) {
              eventCache = sortedEvents[i];
              eventCache.callback.apply(eventCache.target, param); // 只接受一次回调的事件，在触发之后就移除掉该缓存事件

              if (eventCache.once) {
                const index = eventCacheArray.findIndex(e => e.target === eventCache.target && e.callback === eventCache.callback);

                if (index !== -1) {
                  eventCacheArray.splice(index, 1);
                }
              }
            } // 更新缓存（移除已经执行的一次性事件）


            if (eventCacheArray.length === 0) {
              this._eventCacheMap.delete(eventName);
            }
          }
        }
        /**
         * 注册事件
         *
         * @param eventName 事件名
         * @param callback 事件处理函数
         * @param target 事件处理函数的执行对象
         * @param priority 优先级（数字越大优先级越高，默认为0）
         */


        on(eventName, callback, target, priority = 0) {
          this._on(eventName, callback, target, false, priority);
        }
        /**
         * 注册事件（接受函数执行一次后会自动销毁，不用主动off）
         *
         * @param eventName 事件名
         * @param callback 事件处理函数
         * @param target 事件处理函数的执行对象
         * @param priority 优先级（数字越大优先级越高，默认为0）
         */


        once(eventName, callback, target, priority = 0) {
          this._on(eventName, callback, target, true, priority);
        }
        /**
         * 注册事件
         *
         * @param eventName 事件名
         * @param callback 事件处理函数
         * @param target 事件处理函数的执行对象
         * @param once 是否只回调一次
         * @param priority 优先级（数字越大优先级越高）
         */


        _on(eventName, callback, target, once, priority) {
          let eventCacheArray = this._eventCacheMap.get(eventName);

          if (!eventCacheArray) {
            eventCacheArray = [];
          }

          let index = eventCacheArray.findIndex(eventCache => {
            return eventCache.target === target && eventCache.callback === callback;
          });

          if (index === -1) {
            eventCacheArray.push({
              target: target,
              callback: callback,
              once: once,
              priority: priority
            });

            this._eventCacheMap.set(eventName, eventCacheArray);
          } else {
            // 如果已存在，更新优先级
            eventCacheArray[index].priority = priority;
          }
        }
        /**
         * 更新已注册事件的优先级
         *
         * @param eventName 事件名
         * @param callback 事件处理函数
         * @param target 事件处理函数的执行对象
         * @param newPriority 新的优先级
         */


        updatePriority(eventName, callback, target, newPriority) {
          let eventCacheArray = this._eventCacheMap.get(eventName);

          if (eventCacheArray) {
            const index = eventCacheArray.findIndex(eventCache => {
              return eventCache.target === target && eventCache.callback === callback;
            });

            if (index !== -1) {
              eventCacheArray[index].priority = newPriority;
            }
          }
        }
        /**
         * 注销事件
         *
         * @param eventName 事件名
         * @param callback 事件处理函数
         * @param target 事件处理函数的执行对象
         */


        off(eventName, callback, target) {
          let eventCacheArray = this._eventCacheMap.get(eventName);

          if (eventCacheArray) {
            if (callback && target) {
              let index = eventCacheArray.findIndex(eventCache => {
                return eventCache.target === target && eventCache.callback === callback;
              });

              if (index !== -1) {
                eventCacheArray.splice(index, 1);

                if (eventCacheArray.length === 0) {
                  this._eventCacheMap.delete(eventName);
                } else {
                  this._eventCacheMap.set(eventName, eventCacheArray);
                }
              }
            } else {
              this._eventCacheMap.delete(eventName);
            }
          }
        }
        /**
         * 注销某个已经注册的对象的所有事件
         *
         * @param target 事件函数处理的执行对象
         */


        targetOff(target) {
          this._eventCacheMap.forEach((eventCacheArray, eventName) => {
            if (eventCacheArray) {
              for (let i = eventCacheArray.length - 1; i >= 0; i--) {
                if (eventCacheArray[i].target === target) {
                  eventCacheArray.splice(i, 1);
                }
              }

              if (eventCacheArray.length === 0) {
                this._eventCacheMap.delete(eventName);
              }
            }
          });
        }

      });
      /**
       * 缓存事件
       */


      _class = EventManager;
      EventManager.Instance = new _class();

      _export("EventMgr", EventMgr = EventManager.Instance);

      window["EventMgr"] = EventMgr;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=035d044de3c2b01e158afad35e212ff52b71c5db.js.map