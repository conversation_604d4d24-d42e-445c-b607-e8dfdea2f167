System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Enum, rect, v2, Vec2, GameIns, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _class3, _crd, ccclass, property, menu, ColliderType, ColliderGroupType, FCollider;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Enum = _cc.Enum;
      rect = _cc.rect;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3110dd1lBNOhIbn/QdLrJ3h", "FCollider", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Enum', 'Rect', 'rect', 'v2', 'Vec2']);

      ({
        ccclass,
        property,
        menu
      } = _decorator);

      _export("ColliderType", ColliderType = /*#__PURE__*/function (ColliderType) {
        ColliderType[ColliderType["Circle"] = 1] = "Circle";
        ColliderType[ColliderType["Box"] = 2] = "Box";
        ColliderType[ColliderType["Polygon"] = 4] = "Polygon";
        return ColliderType;
      }({})); //碰撞分组


      _export("ColliderGroupType", ColliderGroupType = Enum({
        DEFAULT: 1,
        PLAYER: 2,
        BULLET_SELF: 3,
        ENEMY_NORMAL: 4,
        BULLET_ENEMY: 5,
        ENEMY_BUILE: 6,
        PROP: 7 //道具

      }));

      _export("default", FCollider = (_dec = property(Vec2), _dec2 = property(Vec2), _dec3 = property({
        type: ColliderGroupType,
        displayName: '碰撞分组'
      }), ccclass(_class = (_class2 = (_class3 = class FCollider extends Component {
        constructor(...args) {
          super(...args);
          this.isConvex = true;
          this.isEnable = true;
          //是否启用碰撞检测
          this.isImmunityBullet = false;
          //是否免疫子弹碰撞（不碰撞，直接穿透）
          this.isImmunityBulletHurt = false;
          //是否免疫子弹碰撞伤害（会碰撞，没伤害）
          this.isImmunityCollider = false;
          //无视撞击(免疫撞击也免疫子弹)
          this.isImmunityColliderHurt = false;
          //是否免疫碰撞伤害(免疫撞击也免疫子弹)
          this.entity = void 0;
          //AABB
          this.aabb = rect();
          this.colliderId = 0;

          _initializerDefineProperty(this, "_offset", _descriptor, this);

          _initializerDefineProperty(this, "groupType", _descriptor2, this);
        }

        get type() {
          return ColliderType.Box;
        }

        get x() {
          return this.aabb.x;
        }

        get y() {
          return this.aabb.y;
        }

        get width() {
          return this.aabb.width;
        }

        get height() {
          return this.aabb.height;
        }

        initCollider() {
          this.colliderId = FCollider._baseId++;

          if (FCollider._baseId > 5e10) {
            //防止id太大，做个轮回
            FCollider._baseId = 1;
          }
        }

        get offset() {
          return this._offset;
        }

        set offset(value) {
          this._offset = value;
        }

        onEnable() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.addCollider(this);
          this.draw();
        }

        draw() {}

        onDisable() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.removeCollider(this);
        }

        initBaseData(entity, offset = v2(0, 0)) {
          this.entity = entity;
          this.offset = offset;
        }

      }, _class3._baseId = 1, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "_offset", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return v2();
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "offset", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "offset"), _class2.prototype), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "groupType", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return ColliderGroupType.ENEMY_NORMAL;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0c7e21d2a3c3ae02a9eae71fa3edcf66afecb263.js.map