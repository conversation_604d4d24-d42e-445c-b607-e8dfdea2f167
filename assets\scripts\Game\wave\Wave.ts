import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('WaveTrack')
export class WaveTrack {
    @property(CCInteger)
    public id = 0;
    @property(CCFloat)
    public speed = 0;
    @property(CCFloat)
    public accelerate = 0;
    @property(CCFloat)
    public Interval = 0;
}

@ccclass('WaveTrackGroup')
export class WaveTrackGroup {
    @property(CCInteger)
    public type = 0;
    @property(CCInteger)
    public loopNum = 0;
    @property(CCInteger)
    public formIndex = 0;
    @property([WaveTrack])
    public tracks: WaveTrack[] = [];
}

@ccclass('Wave')
@executeInEditMode()
export class Wave extends Component {
    @property(CCBoolean)
    public set play(value: boolean) {
    }
    public get play():boolean {
        return false;
    }

    @property(CCInteger)
    public enemyGroupID = 0;
    @property(CCFloat)
    public delay = 0;

    @property(CCInteger)
    public planeID = 0;
    @property(CCInteger)
    public planeType = 0;
    @property(CCFloat)
    public interval: number = 0;
    @property(CCInteger)
    public num: number = 0;
    @property(CCFloat)
    public rotateSpeed: number = 0;
    @property (Vec2)
    public startPos: Vec2 = new Vec2();

    @property([WaveTrackGroup])
    public trackGroups: WaveTrackGroup[] = [];

    @property([CCFloat])
    public firstShootDelay: number[] = [];
}