import Long from 'long';
import csproto from '../../AutoGen/PB/cs_proto.js';
import { EventMgr } from '../../event/EventManager';
import { MyApp } from '../../MyApp';
import { logError } from '../../Utils/Logger';
import { DataEvent } from '../DataEvent';
export class EquipSlots {
    slots: csproto.cs.ICSEquipSlotInfo[] = []
    init() {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, this.onGetEquipSlotInfoMsg.bind(this))
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, this.onEquipSlotInstallMsg.bind(this))
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, this.onEquipSlotUnInstallMsg.bind(this))
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, { get_equip_slot_info: {} })
    }

    private onGetEquipSlotInfoMsg(msg: csproto.cs.IS2CMsg): void {
        this.slots = msg.body.get_equip_slot_info.slots || []
        EventMgr.emit(DataEvent.EquipSlotRefresh)
    }

    private onEquipSlotInstallMsg(msg: csproto.cs.IS2CMsg): void {
        const m = msg.body.equip_slot_install
        const slot = this.slots.find(slot => slot.slot_id == m.slot_id)
        if (slot) {
            slot.equip_id = m.equip_id
            slot.guid = m.guid
        }
        EventMgr.emit(DataEvent.EquipSlotRefresh)
    }

    private onEquipSlotUnInstallMsg(msg: csproto.cs.IS2CMsg): void {
        const m = msg.body.equip_slot_uninstall
        const slot = this.slots.find(slot => slot.slot_id == m.slot_id)
        if (slot) {
            slot.equip_id = 0
            slot.guid = Long.ZERO
        }
        EventMgr.emit(DataEvent.EquipSlotRefresh)
    }

    getEmptySlots(): csproto.cs.ICSEquipSlotInfo[] {
        return this.slots.filter(slot => slot.equip_id == 0)
    }

    getEquippedList(): number[] {
        return this.slots.filter(slot => slot.equip_id != 0).map(slot => slot.equip_id)
    }

    getEquippedByClass(equipClass: number): csproto.cs.ICSEquipSlotInfo {
        return this.slots.find(slot => slot.equip_class == equipClass && slot.guid.gt(0))
    }

    getEquipSlotInfo(slotID: number): csproto.cs.ICSEquipSlotInfo {
        return this.slots.find(slot => slot.slot_id == slotID)
    }

    equip(item: csproto.cs.ICSItem) {
        const equipCfg = MyApp.lubanMgr.table.TbEquip.get(item.item_id)
        const slot = this.slots.find(slot => slot.equip_class == equipCfg?.equipClass)
        if (!slot) {
            logError("PlaneUI", ` equip fail item:${item.item_id} ${item.guid} equipClass:${equipCfg?.equipClass}`)
            return
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, {
            equip_slot_install: {
                slot_id: slot.slot_id,
                guid: item.guid,
            }
        })
    }

    unequip(guid: Long) {
        const slot = this.slots.find(slot => slot.guid.eq(guid))
        if (!slot) {
            logError("PlaneUI", `unequip fail guid:${guid}`)
            return
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, {
            equip_slot_uninstall: {
                slot_id: slot.slot_id,
            }
        })
    }
}