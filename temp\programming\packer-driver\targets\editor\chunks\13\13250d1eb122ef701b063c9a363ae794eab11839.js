System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, LevelDataEventCondtion, _crd, LevelDataEventCondtionComb, LevelDataEventCondtionType;

  _export("LevelDataEventCondtion", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "00cdeG2RqtL24uUB5LF5JDd", "LevelDataEventCondtion", undefined);

      _export("LevelDataEventCondtionComb", LevelDataEventCondtionComb = /*#__PURE__*/function (LevelDataEventCondtionComb) {
        LevelDataEventCondtionComb[LevelDataEventCondtionComb["And"] = 0] = "And";
        LevelDataEventCondtionComb[LevelDataEventCondtionComb["Or"] = 1] = "Or";
        return LevelDataEventCondtionComb;
      }({}));

      _export("LevelDataEventCondtionType", LevelDataEventCondtionType = /*#__PURE__*/function (LevelDataEventCondtionType) {
        LevelDataEventCondtionType[LevelDataEventCondtionType["DelayTime"] = 0] = "DelayTime";
        LevelDataEventCondtionType[LevelDataEventCondtionType["DelayDistance"] = 1] = "DelayDistance";
        LevelDataEventCondtionType[LevelDataEventCondtionType["Wave"] = 2] = "Wave";
        return LevelDataEventCondtionType;
      }({}));

      _export("LevelDataEventCondtion", LevelDataEventCondtion = class LevelDataEventCondtion {
        constructor(comb, type) {
          this.comb = LevelDataEventCondtionComb.And;
          this._type = LevelDataEventCondtionType.DelayTime;
          this._type = type, this.comb = comb;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=13250d1eb122ef701b063c9a363ae794eab11839.js.map