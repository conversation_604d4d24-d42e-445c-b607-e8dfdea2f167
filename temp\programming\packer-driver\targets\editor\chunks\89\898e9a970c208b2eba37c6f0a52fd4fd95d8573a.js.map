{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts"], "names": ["FColliderManager", "obbApplyMatrix", "rect", "mat4", "out_bl", "out_tl", "out_tr", "out_br", "x", "y", "width", "height", "mat4m", "Array", "Mat4", "toArray", "m00", "m01", "m04", "m05", "m12", "m13", "tx", "ty", "xa", "xb", "yc", "yd", "v2", "Vec2", "Graphics", "director", "Node", "view", "Color", "ColliderGroupType", "ColliderType", "Intersection", "QuadTree", "tempVec2", "tempMat4", "tempArr", "collisionState", "ColliderTriggerFuncs", "Circle", "self", "other", "selfCircle", "otherCircle", "circleCircle", "Box", "otherBox", "polygonCircle", "worldPoints", "Polygon", "otherPolygon", "selfBox", "node", "angle", "rectRect", "aabb", "satPolygonPolygon", "worldEdge", "isConvex", "polygonPolygon", "CollisionType", "CollisionMatrix", "BULLET_SELF", "ENEMY_NORMAL", "BULLET_ENEMY", "PLAYER", "instance", "_instance", "constructor", "_onCollisionEnter", "_onCollisionStay", "_onCollisionExit", "_tree", "_frameId", "_treeDirty", "_maxDepth", "_max<PERSON><PERSON><PERSON>n", "_treeRect", "getVisibleSize", "_enable", "collisionPairs", "Map", "_colliders", "_enableDebugDraw", "_enableQuadTreeDraw", "_debugDrawer", "max<PERSON><PERSON><PERSON>", "value", "max<PERSON><PERSON><PERSON><PERSON>", "treeRect", "equals", "set", "enable", "addCollider", "collider", "colliders", "initCollider", "push", "removeCollider", "i", "length", "c", "colliderId", "splice", "setGlobalColliderEnterCall", "func", "setGlobalColliderStayCall", "setGlobalColliderExitCall", "updateCollider", "getWorldMatrix", "type", "size", "offset", "wps", "wp0", "wp1", "wp2", "wp3", "minx", "Math", "min", "miny", "maxx", "max", "maxy", "l", "subtract", "transformMat4", "worldPosition", "mm", "tempx", "tempy", "fromArray", "radius", "d", "sqrt", "worldRadius", "points", "Number", "MAX_SAFE_INTEGER", "shouldCollide", "c1", "c2", "groupType", "DEFAULT", "isOutOfScreen", "visibleSize", "screenLeft", "screenRight", "screenTop", "screenBottom", "update", "dt", "oneTest", "timeNow", "Date", "now", "clear", "<PERSON><PERSON><PERSON><PERSON>", "isEnable", "entity", "onOutScreen", "insert", "getAllNeedTestColliders", "k", "klen", "len", "icollider", "j", "jcollider", "id", "aid", "bid", "pairs", "data", "get", "undefined", "frameId", "state", "onStay", "colliderA", "colliderB", "onEnter", "_doCollide", "endTrigger", "drawColliders", "drawQuadTree", "curFrameId", "entries", "values", "waitToDelete", "next", "onExit", "delete", "collider1", "collider2", "comps1", "_components", "comp", "enableDebugDraw", "_checkDebugDrawValid", "active", "enableQuadTreeDraw", "addPersistRootNode", "addComponent", "lineWidth", "debugDrawer", "strokeColor", "RED", "ps", "moveTo", "lineTo", "close", "stroke", "circle", "render"], "mappings": ";;;2MA4GqBA,gB;;AAlDrB,WAASC,cAAT,CAAwBC,IAAxB,EAAoCC,IAApC,EAAgDC,MAAhD,EAA8DC,MAA9D,EAA4EC,MAA5E,EAA0FC,MAA1F,EAAwG;AACpG,QAAIC,CAAC,GAAGN,IAAI,CAACM,CAAb;AACA,QAAIC,CAAC,GAAGP,IAAI,CAACO,CAAb;AACA,QAAIC,KAAK,GAAGR,IAAI,CAACQ,KAAjB;AACA,QAAIC,MAAM,GAAGT,IAAI,CAACS,MAAlB,CAJoG,CAMpG;;AACA,QAAIC,KAAK,GAAG,IAAIC,KAAJ,CAAU,EAAV,CAAZ,CAPoG,CAQpG;AAEA;;AACAC,IAAAA,IAAI,CAACC,OAAL,CAAaH,KAAb,EAAoBT,IAApB;AACA,QAAIa,GAAG,GAAGJ,KAAK,CAAC,CAAD,CAAf;AAAA,QAAoBK,GAAG,GAAGL,KAAK,CAAC,CAAD,CAA/B;AAAA,QAAoCM,GAAG,GAAGN,KAAK,CAAC,CAAD,CAA/C;AAAA,QAAoDO,GAAG,GAAGP,KAAK,CAAC,CAAD,CAA/D;AACA,QAAIQ,GAAG,GAAGR,KAAK,CAAC,EAAD,CAAf;AAAA,QAAqBS,GAAG,GAAGT,KAAK,CAAC,EAAD,CAAhC;AAEA,QAAIU,EAAE,GAAGN,GAAG,GAAGR,CAAN,GAAUU,GAAG,GAAGT,CAAhB,GAAoBW,GAA7B;AACA,QAAIG,EAAE,GAAGN,GAAG,GAAGT,CAAN,GAAUW,GAAG,GAAGV,CAAhB,GAAoBY,GAA7B;AACA,QAAIG,EAAE,GAAGR,GAAG,GAAGN,KAAf;AACA,QAAIe,EAAE,GAAGR,GAAG,GAAGP,KAAf;AACA,QAAIgB,EAAE,GAAGR,GAAG,GAAGP,MAAf;AACA,QAAIgB,EAAE,GAAGR,GAAG,GAAGR,MAAf;AAEAN,IAAAA,MAAM,CAACG,CAAP,GAAWc,EAAX;AACAjB,IAAAA,MAAM,CAACI,CAAP,GAAWc,EAAX;AACAjB,IAAAA,MAAM,CAACE,CAAP,GAAWgB,EAAE,GAAGF,EAAhB;AACAhB,IAAAA,MAAM,CAACG,CAAP,GAAWgB,EAAE,GAAGF,EAAhB;AACAnB,IAAAA,MAAM,CAACI,CAAP,GAAWkB,EAAE,GAAGJ,EAAhB;AACAlB,IAAAA,MAAM,CAACK,CAAP,GAAWkB,EAAE,GAAGJ,EAAhB;AACAhB,IAAAA,MAAM,CAACC,CAAP,GAAWgB,EAAE,GAAGE,EAAL,GAAUJ,EAArB;AACAf,IAAAA,MAAM,CAACE,CAAP,GAAWgB,EAAE,GAAGE,EAAL,GAAUJ,EAArB;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxFoBK,MAAAA,E,OAAAA,E;AAAIzB,MAAAA,I,OAAAA,I;AAAYW,MAAAA,I,OAAAA,I;AAAMe,MAAAA,I,OAAAA,I;AAAM3B,MAAAA,I,OAAAA,I;AAAM4B,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Q,OAAAA,Q;AAA2BC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAGpFC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,Y,iBAAAA,Y;;AAE9BC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;AACLC,MAAAA,Q,GAAWX,EAAE,E;AACbY,MAAAA,Q,GAAWrC,IAAI,E;AACfsC,MAAAA,O,GAAiB,E;AACjBC,MAAAA,c,GAAyB,C;;sCAEhBC,oB,GAAuB,E;;AACpCA,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaC,MAAb,GAAsB;AAAA;AAAA,wCAAaA,MAApC,CAApB,GAAkE,CAACC,IAAD,EAAkBC,KAAlB,KAAuC;AACrG,YAAIC,UAAU,GAAGF,IAAjB;AACA,YAAIG,WAAW,GAAGF,KAAlB;AACA,eAAO;AAAA;AAAA,0CAAaG,YAAb,CAA0BF,UAA1B,EAAsCC,WAAtC,CAAP;AACH,OAJD;;AAKAL,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaC,MAAb,GAAsB;AAAA;AAAA,wCAAaM,GAApC,CAApB,GAA+D,CAACL,IAAD,EAAkBC,KAAlB,KAAuC;AAClG,YAAIC,UAAU,GAAGF,IAAjB;AACA,YAAIM,QAAQ,GAAGL,KAAf;AACA,eAAO;AAAA;AAAA,0CAAaM,aAAb,CAA2BD,QAAQ,CAACE,WAApC,EAAiDN,UAAjD,CAAP;AACH,OAJD;;AAKAJ,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaC,MAAb,GAAsB;AAAA;AAAA,wCAAaU,OAApC,CAApB,GAAmE,CAACT,IAAD,EAAkBC,KAAlB,KAAuC;AACtG,YAAIC,UAAU,GAAGF,IAAjB;AACA,YAAIU,YAAY,GAAGT,KAAnB;AACA,eAAO;AAAA;AAAA,0CAAaM,aAAb,CAA2BG,YAAY,CAACF,WAAxC,EAAqDN,UAArD,CAAP;AACH,OAJD;;AAKAJ,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaO,GAAb,GAAmB;AAAA;AAAA,wCAAaA,GAAjC,CAApB,GAA4D,CAACL,IAAD,EAAkBC,KAAlB,KAAuC;AAC/F,YAAIU,OAAO,GAAGX,IAAd;AACA,YAAIM,QAAQ,GAAGL,KAAf;;AACA,YAAIU,OAAO,CAACC,IAAR,CAAaC,KAAb,KAAuB,CAAvB,IAA4BP,QAAQ,CAACM,IAAT,CAAcC,KAAd,IAAuB,CAAvD,EAA0D;AACtD,iBAAO;AAAA;AAAA,4CAAaC,QAAb,CAAsBH,OAAO,CAACI,IAA9B,EAAoCT,QAAQ,CAACS,IAA7C,CAAP;AACH,SAFD,MAEO;AACH,iBAAO;AAAA;AAAA,4CAAaC,iBAAb,CAA+BL,OAAO,CAACH,WAAvC,EAAoDF,QAAQ,CAACE,WAA7D,EAA0EG,OAAO,CAACM,SAAlF,EAA6FX,QAAQ,CAACW,SAAtG,CAAP;AACH;AACJ,OARD;;AASAnB,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaO,GAAb,GAAmB;AAAA;AAAA,wCAAaI,OAAjC,CAApB,GAAgE,CAACT,IAAD,EAAkBC,KAAlB,KAAuC;AACnG,YAAIU,OAAO,GAAGX,IAAd;AACA,YAAIM,QAAQ,GAAGL,KAAf;;AACA,YAAI,CAACK,QAAQ,CAACY,QAAd,EAAwB;AACpB,iBAAO;AAAA;AAAA,4CAAaC,cAAb,CAA4BR,OAAO,CAACH,WAApC,EAAiDF,QAAQ,CAACE,WAA1D,CAAP;AACH,SAFD,MAGK;AACD,iBAAO;AAAA;AAAA,4CAAaQ,iBAAb,CAA+BL,OAAO,CAACH,WAAvC,EAAoDF,QAAQ,CAACE,WAA7D,EAA0EG,OAAO,CAACM,SAAlF,EAA6FX,QAAQ,CAACW,SAAtG,CAAP;AACH;AACJ,OATD;;AAUAnB,MAAAA,oBAAoB,CAAC;AAAA;AAAA,wCAAaW,OAAb,GAAuB;AAAA;AAAA,wCAAaA,OAArC,CAApB,GAAoE,CAACT,IAAD,EAAkBC,KAAlB,KAAuC;AACvG,YAAIU,OAAO,GAAGX,IAAd;AACA,YAAIM,QAAQ,GAAGL,KAAf;;AACA,YAAI,CAACK,QAAQ,CAACY,QAAV,IAAsB,CAACP,OAAO,CAACO,QAAnC,EAA6C;AACzC,iBAAO;AAAA;AAAA,4CAAaC,cAAb,CAA4BR,OAAO,CAACH,WAApC,EAAiDF,QAAQ,CAACE,WAA1D,CAAP;AACH,SAFD,MAGK;AACD,iBAAO;AAAA;AAAA,4CAAaQ,iBAAb,CAA+BL,OAAO,CAACH,WAAvC,EAAoDF,QAAQ,CAACE,WAA7D,EAA0EG,OAAO,CAACM,SAAlF,EAA6FX,QAAQ,CAACW,SAAtG,CAAP;AACH;AACJ,OATD;;AA2CKG,MAAAA,a,0BAAAA,a;AAAAA,QAAAA,a;AAAAA,QAAAA,a;AAAAA,QAAAA,a;eAAAA,a;QAAAA,a;;AAaDC,MAAAA,e,GAAkB;AAClB,SAAE,GAAE;AAAA;AAAA,oDAAkBC,WAAY,IAAG;AAAA;AAAA,oDAAkBC,YAAa,EAApE,GAAwE,IADtD;AAElB,SAAE,GAAE;AAAA;AAAA,oDAAkBC,YAAa,IAAG;AAAA;AAAA,oDAAkBC,MAAO,EAA/D,GAAmE;AAFjD,O;;yBAKDtE,gB,GAAN,MAAMA,gBAAN,CAAuB;AAGR,mBAARuE,QAAQ,GAAG;AACzB,cAAI,CAAC,KAAKC,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,IAAIxE,gBAAJ,EAAjB;AACH;;AACD,iBAAO,KAAKwE,SAAZ;AACH;;AAQOC,QAAAA,WAAW,GAAG;AAAA,eANtBC,iBAMsB,GANQ,IAMR;AAAA,eALtBC,gBAKsB,GALO,IAKP;AAAA,eAJtBC,gBAIsB,GAJO,IAIP;AAAA,eAFdC,KAEc,GAFe,IAEf;AAAA,eADdC,QACc,GADK,CACL;AAItB;AAJsB,eAKdC,UALc,GAKQ,IALR;AAAA,eAOdC,SAPc,GAOM,CAPN;AAAA,eAkBdC,YAlBc,GAkBS,EAlBT;AAAA,eA6BdC,SA7Bc,GA6BIhF,IAAI,CAAC,CAAD,EAAI,CAAJ,EAAO+B,IAAI,CAACkD,cAAL,GAAsBzE,KAA7B,EAAoCuB,IAAI,CAACkD,cAAL,GAAsBxE,MAA1D,CA7BR;AAAA,eA0CdyE,OA1Cc,GA0CK,KA1CL;AAAA,eAuDdC,cAvDc,GAuD+B,IAAIC,GAAJ,EAvD/B;AAAA,eAwDdC,UAxDc,GAwDY,EAxDZ;AAAA,eAiYdC,gBAjYc,GAiYc,KAjYd;AAAA,eAiZdC,mBAjZc,GAiZiB,KAjZjB;AAAA,eAiatBC,YAjasB,GAiaG,IAjaH;AAClB,eAAKZ,QAAL,GAAgB,CAAhB;AACA,eAAKD,KAAL,GAAa;AAAA;AAAA,oCAAwB,KAAKK,SAA7B,EAAwC,CAAxC,EAA2C,KAAKF,SAAhD,EAA2D,KAAKC,YAAhE,CAAb;AACH;;AAKkB,YAARU,QAAQ,GAAW;AAC1B,iBAAO,KAAKX,SAAZ;AACH;;AACkB,YAARW,QAAQ,CAACC,KAAD,EAAgB;AAC/B,cAAIA,KAAK,IAAI,KAAKZ,SAAlB,EAA6B;AACzB,iBAAKA,SAAL,GAAiBY,KAAjB;AACA,iBAAKb,UAAL,GAAkB,IAAlB;AACH;AACJ;;AAGqB,YAAXc,WAAW,GAAW;AAC7B,iBAAO,KAAKZ,YAAZ;AACH;;AACqB,YAAXY,WAAW,CAACD,KAAD,EAAgB;AAClC,cAAI,KAAKX,YAAL,IAAqBW,KAAzB,EAAgC;AAC5B,iBAAKX,YAAL,GAAoBW,KAApB;AACA,iBAAKb,UAAL,GAAkB,IAAlB;AACH;AACJ;;AAGkB,YAARe,QAAQ,GAAS;AACxB,iBAAO,KAAKZ,SAAZ;AACH,SAhDiC,CAiDlC;;;AACmB,YAARY,QAAQ,CAACF,KAAD,EAAc;AAC7B,cAAI,KAAKV,SAAT,EAAoB;AAChB,gBAAI,KAAKA,SAAL,CAAea,MAAf,CAAsBH,KAAtB,CAAJ,EAAkC;AACrC;;AACD,eAAKV,SAAL,CAAec,GAAf,CAAmBJ,KAAnB;;AACA,eAAKb,UAAL,GAAkB,KAAlB;AACH;;AAGgB,YAANkB,MAAM,GAAY;AACzB,iBAAO,KAAKb,OAAZ;AACH;;AACgB,YAANa,MAAM,CAACL,KAAD,EAAiB;AAC9B,eAAKR,OAAL,GAAeQ,KAAf;;AACA,cAAIA,KAAJ,EAAW,CACP;AACA;AACA;AACH;AACJ;;AAIMM,QAAAA,WAAW,CAACC,QAAD,EAAsB;AACpC,cAAIC,SAAS,GAAG,KAAKb,UAArB;AACA,eAAKc,YAAL,CAAkBF,QAAlB;AACAC,UAAAA,SAAS,CAACE,IAAV,CAAeH,QAAf;AACH;;AACMI,QAAAA,cAAc,CAACJ,QAAD,EAAsB;AACvC,eAAK,IAAIK,CAAC,GAAG,KAAKjB,UAAL,CAAgBkB,MAAhB,GAAyB,CAAtC,EAAyCD,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,gBAAIE,CAAC,GAAG,KAAKnB,UAAL,CAAgBiB,CAAhB,CAAR;;AACA,gBAAIL,QAAQ,CAACQ,UAAT,KAAwBD,CAAC,CAACC,UAA9B,EAA0C;AACtC,mBAAKpB,UAAL,CAAgBqB,MAAhB,CAAuBJ,CAAvB,EAA0B,CAA1B;AACH;AACJ;AACJ;;AAEMH,QAAAA,YAAY,CAACF,QAAD,EAAsB;AACrCA,UAAAA,QAAQ,CAACE,YAAT;AACH;;AAEDQ,QAAAA,0BAA0B,CAACC,IAAD,EAAgB;AACtC,eAAKpC,iBAAL,GAAyBoC,IAAzB;AACH;;AACDC,QAAAA,yBAAyB,CAACD,IAAD,EAAgB;AACrC,eAAKnC,gBAAL,GAAwBmC,IAAxB;AACH;;AACDE,QAAAA,yBAAyB,CAACF,IAAD,EAAgB;AACrC,eAAKlC,gBAAL,GAAwBkC,IAAxB;AACH;;AAEMG,QAAAA,cAAc,CAACP,CAAD,EAAe;AAChCA,UAAAA,CAAC,CAACjD,IAAF,CAAOyD,cAAP,CAAsB1E,QAAtB;;AACA,cAAIkE,CAAC,CAACS,IAAF,KAAW;AAAA;AAAA,4CAAajE,GAA5B,EAAiC;AAC7B,gBAAIiD,QAAQ,GAAGO,CAAf;AACA,gBAAIU,IAAI,GAAGjB,QAAQ,CAACiB,IAApB;AACAjB,YAAAA,QAAQ,CAACvC,IAAT,CAAcpD,CAAd,GAAkB2F,QAAQ,CAACkB,MAAT,CAAgB7G,CAAhB,GAAoB4G,IAAI,CAAC1G,KAAL,GAAa,CAAnD;AACAyF,YAAAA,QAAQ,CAACvC,IAAT,CAAcnD,CAAd,GAAkB0F,QAAQ,CAACkB,MAAT,CAAgB5G,CAAhB,GAAoB2G,IAAI,CAACzG,MAAL,GAAc,CAApD;AACAwF,YAAAA,QAAQ,CAACvC,IAAT,CAAclD,KAAd,GAAsB0G,IAAI,CAAC1G,KAA3B;AACAyF,YAAAA,QAAQ,CAACvC,IAAT,CAAcjD,MAAd,GAAuByG,IAAI,CAACzG,MAA5B;AACA,gBAAI2G,GAAG,GAAGnB,QAAQ,CAAC9C,WAAnB;AACA,gBAAIkE,GAAG,GAAGD,GAAG,CAAC,CAAD,CAAb;AAAA,gBAAkBE,GAAG,GAAGF,GAAG,CAAC,CAAD,CAA3B;AAAA,gBACIG,GAAG,GAAGH,GAAG,CAAC,CAAD,CADb;AAAA,gBACkBI,GAAG,GAAGJ,GAAG,CAAC,CAAD,CAD3B;AAEArH,YAAAA,cAAc,CAACkG,QAAQ,CAACvC,IAAV,EAAgBpB,QAAhB,EAA0B+E,GAA1B,EAA+BC,GAA/B,EAAoCC,GAApC,EAAyCC,GAAzC,CAAd;AACA,gBAAIC,IAAI,GAAGC,IAAI,CAACC,GAAL,CAASN,GAAG,CAAC/G,CAAb,EAAgBgH,GAAG,CAAChH,CAApB,EAAuBiH,GAAG,CAACjH,CAA3B,EAA8BkH,GAAG,CAAClH,CAAlC,CAAX;AACA,gBAAIsH,IAAI,GAAGF,IAAI,CAACC,GAAL,CAASN,GAAG,CAAC9G,CAAb,EAAgB+G,GAAG,CAAC/G,CAApB,EAAuBgH,GAAG,CAAChH,CAA3B,EAA8BiH,GAAG,CAACjH,CAAlC,CAAX;AACA,gBAAIsH,IAAI,GAAGH,IAAI,CAACI,GAAL,CAAST,GAAG,CAAC/G,CAAb,EAAgBgH,GAAG,CAAChH,CAApB,EAAuBiH,GAAG,CAACjH,CAA3B,EAA8BkH,GAAG,CAAClH,CAAlC,CAAX;AACA,gBAAIyH,IAAI,GAAGL,IAAI,CAACI,GAAL,CAAST,GAAG,CAAC9G,CAAb,EAAgB+G,GAAG,CAAC/G,CAApB,EAAuBgH,GAAG,CAAChH,CAA3B,EAA8BiH,GAAG,CAACjH,CAAlC,CAAX;AACA,gBAAIqD,SAAS,GAAGqC,QAAQ,CAACrC,SAAzB;;AACA,iBAAK,IAAI0C,CAAC,GAAG,CAAR,EAAW0B,CAAC,GAAGZ,GAAG,CAACb,MAAxB,EAAgCD,CAAC,GAAG0B,CAApC,EAAuC1B,CAAC,EAAxC,EAA4C;AACxC,kBAAI,CAAC1C,SAAS,CAAC0C,CAAD,CAAd,EAAmB1C,SAAS,CAAC0C,CAAD,CAAT,GAAe5E,EAAE,EAAjB;AACnBC,cAAAA,IAAI,CAACsG,QAAL,CAAcrE,SAAS,CAAC0C,CAAD,CAAvB,EAA4Bc,GAAG,CAAC,CAACd,CAAC,GAAG,CAAL,IAAU0B,CAAX,CAA/B,EAA8CZ,GAAG,CAACd,CAAD,CAAjD;AACH;;AACDL,YAAAA,QAAQ,CAACvC,IAAT,CAAcpD,CAAd,GAAkBmH,IAAlB;AACAxB,YAAAA,QAAQ,CAACvC,IAAT,CAAcnD,CAAd,GAAkBqH,IAAlB;AACA3B,YAAAA,QAAQ,CAACvC,IAAT,CAAclD,KAAd,GAAsBqH,IAAI,GAAGJ,IAA7B;AACAxB,YAAAA,QAAQ,CAACvC,IAAT,CAAcjD,MAAd,GAAuBsH,IAAI,GAAGH,IAA9B;AACH,WAxBD,MAwBO,IAAIpB,CAAC,CAACS,IAAF,IAAU;AAAA;AAAA,4CAAavE,MAA3B,EAAmC;AACtC,gBAAIuD,QAAQ,GAAGO,CAAf;AACA7E,YAAAA,IAAI,CAACuG,aAAL,CAAmB7F,QAAnB,EAA6B4D,QAAQ,CAACkB,MAAtC,EAA8C7E,QAA9C;AACA2D,YAAAA,QAAQ,CAACkC,aAAT,CAAuB7H,CAAvB,GAA2B+B,QAAQ,CAAC/B,CAApC;AACA2F,YAAAA,QAAQ,CAACkC,aAAT,CAAuB5H,CAAvB,GAA2B8B,QAAQ,CAAC9B,CAApC;AAEA,gBAAI6H,EAAE,GAAG,IAAIzH,KAAJ,CAAU,EAAV,CAAT,CANsC,CAMd;;AACxBC,YAAAA,IAAI,CAACC,OAAL,CAAauH,EAAb,EAAiB9F,QAAjB,EAPsC,CAOV;;AAE5B,gBAAI+F,KAAK,GAAGD,EAAE,CAAC,EAAD,CAAd;AAAA,gBAAoBE,KAAK,GAAGF,EAAE,CAAC,EAAD,CAA9B;AACAA,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASA,EAAE,CAAC,EAAD,CAAF,GAAS,CAAlB;AAEAxH,YAAAA,IAAI,CAAC2H,SAAL,CAAejG,QAAf,EAAyB8F,EAAzB;AAGA/F,YAAAA,QAAQ,CAAC/B,CAAT,GAAa2F,QAAQ,CAACuC,MAAtB;AACAnG,YAAAA,QAAQ,CAAC9B,CAAT,GAAa,CAAb;AAEAoB,YAAAA,IAAI,CAACuG,aAAL,CAAmB7F,QAAnB,EAA6BA,QAA7B,EAAuCC,QAAvC;AACA,gBAAImG,CAAC,GAAGf,IAAI,CAACgB,IAAL,CAAUrG,QAAQ,CAAC/B,CAAT,GAAa+B,QAAQ,CAAC/B,CAAtB,GAA0B+B,QAAQ,CAAC9B,CAAT,GAAa8B,QAAQ,CAAC9B,CAA1D,CAAR;AAEA0F,YAAAA,QAAQ,CAAC0C,WAAT,GAAuBF,CAAvB;AAEAxC,YAAAA,QAAQ,CAACvC,IAAT,CAAcpD,CAAd,GAAkB2F,QAAQ,CAACkC,aAAT,CAAuB7H,CAAvB,GAA2BmI,CAA7C;AACAxC,YAAAA,QAAQ,CAACvC,IAAT,CAAcnD,CAAd,GAAkB0F,QAAQ,CAACkC,aAAT,CAAuB5H,CAAvB,GAA2BkI,CAA7C;AACAxC,YAAAA,QAAQ,CAACvC,IAAT,CAAclD,KAAd,GAAsBiI,CAAC,GAAG,CAA1B;AACAxC,YAAAA,QAAQ,CAACvC,IAAT,CAAcjD,MAAd,GAAuBgI,CAAC,GAAG,CAA3B;AAEAL,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASC,KAAT;AACAD,YAAAA,EAAE,CAAC,EAAD,CAAF,GAASE,KAAT;AACH,WA9BM,MA8BA,IAAI9B,CAAC,CAACS,IAAF,IAAU;AAAA;AAAA,4CAAa7D,OAA3B,EAAoC;AACvC,gBAAI6C,QAAQ,GAAGO,CAAf;AACA,gBAAIoC,MAAM,GAAG3C,QAAQ,CAAC2C,MAAtB;AACA,gBAAIzF,WAAW,GAAG8C,QAAQ,CAAC9C,WAA3B;AACA,gBAAIS,SAAS,GAAGqC,QAAQ,CAACrC,SAAzB;AACAT,YAAAA,WAAW,CAACoD,MAAZ,GAAqBqC,MAAM,CAACrC,MAA5B;AAEA,gBAAIkB,IAAI,GAAGoB,MAAM,CAACC,gBAAlB;AAAA,gBAAoClB,IAAI,GAAGiB,MAAM,CAACC,gBAAlD;AAAA,gBAAoEjB,IAAI,GAAG,CAACgB,MAAM,CAACC,gBAAnF;AAAA,gBAAqGf,IAAI,GAAG,CAACc,MAAM,CAACC,gBAApH;;AACA,iBAAK,IAAIxC,CAAC,GAAG,CAAR,EAAW0B,CAAC,GAAGY,MAAM,CAACrC,MAA3B,EAAmCD,CAAC,GAAG0B,CAAvC,EAA0C1B,CAAC,EAA3C,EAA+C;AAC3C,kBAAI,CAACnD,WAAW,CAACmD,CAAD,CAAhB,EAAqB;AACjBnD,gBAAAA,WAAW,CAACmD,CAAD,CAAX,GAAiB5E,EAAE,EAAnB;AACH;;AACDW,cAAAA,QAAQ,CAAC/B,CAAT,GAAasI,MAAM,CAACtC,CAAD,CAAN,CAAUhG,CAAV,GAAc2F,QAAQ,CAACkB,MAAT,CAAgB7G,CAA3C;AACA+B,cAAAA,QAAQ,CAAC9B,CAAT,GAAaqI,MAAM,CAACtC,CAAD,CAAN,CAAU/F,CAAV,GAAc0F,QAAQ,CAACkB,MAAT,CAAgB5G,CAA3C;AACAoB,cAAAA,IAAI,CAACuG,aAAL,CAAmB7F,QAAnB,EAA6BA,QAA7B,EAAuCC,QAAvC;AAEA,kBAAIhC,CAAC,GAAG+B,QAAQ,CAAC/B,CAAjB;AACA,kBAAIC,CAAC,GAAG8B,QAAQ,CAAC9B,CAAjB;AAEA4C,cAAAA,WAAW,CAACmD,CAAD,CAAX,CAAeR,GAAf,CAAmBzD,QAAnB;AAEA,kBAAI/B,CAAC,GAAGuH,IAAR,EAAcA,IAAI,GAAGvH,CAAP;AACd,kBAAIA,CAAC,GAAGmH,IAAR,EAAcA,IAAI,GAAGnH,CAAP;AACd,kBAAIC,CAAC,GAAGwH,IAAR,EAAcA,IAAI,GAAGxH,CAAP;AACd,kBAAIA,CAAC,GAAGqH,IAAR,EAAcA,IAAI,GAAGrH,CAAP;AACjB;;AACD,gBAAIiG,CAAC,CAAC3C,QAAN,EAAgB;AACZ,mBAAK,IAAIyC,CAAC,GAAG,CAAR,EAAW0B,CAAC,GAAG7E,WAAW,CAACoD,MAAhC,EAAwCD,CAAC,GAAG0B,CAA5C,EAA+C1B,CAAC,EAAhD,EAAoD;AAChD,oBAAI,CAAC1C,SAAS,CAAC0C,CAAD,CAAd,EAAmB1C,SAAS,CAAC0C,CAAD,CAAT,GAAe5E,EAAE,EAAjB;AACnBC,gBAAAA,IAAI,CAACsG,QAAL,CAAcrE,SAAS,CAAC0C,CAAD,CAAvB,EAA4BnD,WAAW,CAAC,CAACmD,CAAC,GAAG,CAAL,IAAU0B,CAAX,CAAvC,EAAsD7E,WAAW,CAACmD,CAAD,CAAjE;AACH;AACJ;;AAEDL,YAAAA,QAAQ,CAACvC,IAAT,CAAcpD,CAAd,GAAkBmH,IAAlB;AACAxB,YAAAA,QAAQ,CAACvC,IAAT,CAAcnD,CAAd,GAAkBqH,IAAlB;AACA3B,YAAAA,QAAQ,CAACvC,IAAT,CAAclD,KAAd,GAAsBqH,IAAI,GAAGJ,IAA7B;AACAxB,YAAAA,QAAQ,CAACvC,IAAT,CAAcjD,MAAd,GAAuBsH,IAAI,GAAGH,IAA9B;AACH;AACJ;;AAEMmB,QAAAA,aAAa,CAACC,EAAD,EAAgBC,EAAhB,EAA+B;AAC/C,cAAID,EAAE,CAACE,SAAH,KAAiB;AAAA;AAAA,sDAAkBC,OAAnC,IAA8CF,EAAE,CAACC,SAAH,KAAiB;AAAA;AAAA,sDAAkBC,OAArF,EAA8F;AAC1F,mBAAO,IAAP;AACH;;AAED,iBAAOnF,eAAe,CAAE,GAAEgF,EAAE,CAACE,SAAU,IAAGD,EAAE,CAACC,SAAU,EAAjC,CAAf,IAAsDlF,eAAe,CAAE,GAAEiF,EAAE,CAACC,SAAU,IAAGF,EAAE,CAACE,SAAU,EAAjC,CAA5E;AACH;;AAEDE,QAAAA,aAAa,CAAC1F,IAAD,EAAsB;AAC/B,gBAAM2F,WAAW,GAAGtH,IAAI,CAACkD,cAAL,EAApB;AACA,gBAAMqE,UAAU,GAAG,CAAC,GAApB;AACA,gBAAMC,WAAW,GAAGF,WAAW,CAAC7I,KAAZ,GAAoB,GAAxC;AACA,gBAAMgJ,SAAS,GAAGH,WAAW,CAAC5I,MAAZ,GAAqB,GAAvC;AACA,gBAAMgJ,YAAY,GAAG,CAAC,GAAtB,CAL+B,CAO/B;;AACA,iBACI/F,IAAI,CAACpD,CAAL,GAASoD,IAAI,CAAClD,KAAd,GAAsB8I,UAAtB,IAAoC;AACpC5F,UAAAA,IAAI,CAACpD,CAAL,GAASiJ,WADT,IACmC;AACnC7F,UAAAA,IAAI,CAACnD,CAAL,GAASmD,IAAI,CAACjD,MAAd,GAAuBgJ,YAFvB,IAEuC;AACvC/F,UAAAA,IAAI,CAACnD,CAAL,GAASiJ,SAJb,CAIuC;AAJvC;AAMH;;AAEME,QAAAA,MAAM,CAACC,EAAD,EAAa;AACtB,cAAI,CAAC,KAAK5D,MAAV,EAAkB,OADI,CAEtB;;AACA,eAAK6D,OAAL,CAAaD,EAAb,EAHsB,CAItB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACWC,QAAAA,OAAO,CAACD,EAAD,EAAa;AACvB,cAAIE,OAAO,GAAGC,IAAI,CAACC,GAAL,EAAd;AACA,eAAKnF,QAAL;;AAEA,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKF,KAAL,GAAa;AAAA;AAAA,sCAAwB,KAAKK,SAA7B,EAAwC,CAAxC,EAA2C,KAAKF,SAAhD,EAA2D,KAAKC,YAAhE,CAAb;AACA,iBAAKF,UAAL,GAAkB,KAAlB;AACH;;AACD,eAAKF,KAAL,CAAWqF,KAAX;;AACA,eAAK,IAAI1D,CAAC,GAAG,KAAKjB,UAAL,CAAgBkB,MAAhB,GAAyB,CAAtC,EAAyCD,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,gBAAIL,QAAQ,GAAG,KAAKZ,UAAL,CAAgBiB,CAAhB,CAAf;;AACA,gBAAI,CAACL,QAAD,IAAa,CAACA,QAAQ,CAACgE,OAA3B,EAAoC;AAChC,mBAAK5E,UAAL,CAAgBqB,MAAhB,CAAuBJ,CAAvB,EAA0B,CAA1B;;AACA;AACH;;AACD,gBAAI,CAACL,QAAQ,CAACiE,QAAd,EAAuB;AACnB;AACH;;AACD,iBAAKnD,cAAL,CAAoB,KAAK1B,UAAL,CAAgBiB,CAAhB,CAApB;;AACA,gBAAI,KAAK8C,aAAL,CAAmBnD,QAAQ,CAACvC,IAA5B,CAAJ,EAAsC;AAAA;;AAClC,kCAAAuC,QAAQ,CAACkE,MAAT,8BAAiBC,WAAjB,6BAAiBA,WAAjB;AACA;AACH;;AAED,iBAAKzF,KAAL,CAAW0F,MAAX,CAAkB,KAAKhF,UAAL,CAAgBiB,CAAhB,CAAlB;AACH;;AAED/D,UAAAA,OAAO,CAACgE,MAAR,GAAiB,CAAjB;;AACA,eAAK5B,KAAL,CAAW2F,uBAAX,CAAmC/H,OAAnC;;AACA,eAAK,IAAIgI,CAAC,GAAG,CAAR,EAAWC,IAAI,GAAGjI,OAAO,CAACgE,MAA/B,EAAuCgE,CAAC,GAAGC,IAA3C,EAAiDD,CAAC,EAAlD,EAAsD;AAClD,gBAAIlF,UAAU,GAAG9C,OAAO,CAACgI,CAAD,CAAxB;;AACA,iBAAK,IAAIjE,CAAC,GAAG,CAAR,EAAWmE,GAAG,GAAGpF,UAAU,CAACkB,MAAjC,EAAyCD,CAAC,GAAGmE,GAA7C,EAAkDnE,CAAC,EAAnD,EAAuD;AACnD,kBAAIoE,SAAS,GAAGrF,UAAU,CAACiB,CAAD,CAA1B;;AACA,mBAAK,IAAIqE,CAAC,GAAGrE,CAAC,GAAG,CAAjB,EAAoBqE,CAAC,GAAGF,GAAxB,EAA6BE,CAAC,EAA9B,EAAkC;AAC9B,oBAAIC,SAAS,GAAGvF,UAAU,CAACsF,CAAD,CAA1B;AACA,oBAAI,CAAC,KAAK5B,aAAL,CAAmB2B,SAAnB,EAA8BE,SAA9B,CAAL,EAA+C;;AAC/C,oBAAIF,SAAS,CAACzD,IAAV,GAAiB2D,SAAS,CAAC3D,IAA/B,EAAqC;AACjCzE,kBAAAA,cAAc,GAAGC,oBAAoB,CAACiI,SAAS,CAACzD,IAAV,GAAiB2D,SAAS,CAAC3D,IAA5B,CAApB,CAAsDyD,SAAtD,EAAiEE,SAAjE,CAAjB;AACH,iBAFD,MAEO;AACHpI,kBAAAA,cAAc,GAAGC,oBAAoB,CAACmI,SAAS,CAAC3D,IAAV,GAAiByD,SAAS,CAACzD,IAA5B,CAApB,CAAsD2D,SAAtD,EAAiEF,SAAjE,CAAjB;AACH;AACD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,oBAAIlI,cAAJ,EAAoB;AAChB,sBAAIqI,EAAE,GAAG,CAAT;AAAA,sBAAYC,GAAG,GAAGJ,SAAS,CAACjE,UAA5B;AAAA,sBAAwCsE,GAAG,GAAGH,SAAS,CAACnE,UAAxD;;AACA,sBAAIqE,GAAG,GAAGC,GAAV,EAAe;AAAEF,oBAAAA,EAAE,GAAGC,GAAL;AAAUA,oBAAAA,GAAG,GAAGC,GAAN;AAAWA,oBAAAA,GAAG,GAAGF,EAAN;AAAW;;AACjDA,kBAAAA,EAAE,GAAG,CAACC,GAAG,IAAIA,GAAG,GAAG,CAAV,CAAH,IAAmB,CAApB,IAAyBC,GAAzB,GAA+B,CAApC,CAHgB,CAGsB;;AACtC,sBAAIC,KAAK,GAAG,KAAK7F,cAAjB;AACA,sBAAI8F,IAAI,GAAGD,KAAK,CAACE,GAAN,CAAUL,EAAV,CAAX;;AACA,sBAAII,IAAI,KAAKE,SAAb,EAAwB;AACpBF,oBAAAA,IAAI,CAACG,OAAL,GAAe,KAAKxG,QAApB;AACAqG,oBAAAA,IAAI,CAACI,KAAL,GAAatH,aAAa,CAACuH,MAA3B;AACH,mBAHD,MAGO;AACHL,oBAAAA,IAAI,GAAG;AACHJ,sBAAAA,EAAE,EAAEA,EADD;AAEHU,sBAAAA,SAAS,EAAEb,SAFR;AAGHc,sBAAAA,SAAS,EAAEZ,SAHR;AAIHQ,sBAAAA,OAAO,EAAE,KAAKxG,QAJX;AAKHyG,sBAAAA,KAAK,EAAEtH,aAAa,CAAC0H;AALlB,qBAAP;AAOAT,oBAAAA,KAAK,CAAClF,GAAN,CAAU+E,EAAV,EAAcI,IAAd;AACH;;AACD,uBAAKS,UAAL,CAAgBhB,SAAhB,EAA2BE,SAA3B,EAAsCK,IAAI,CAACI,KAA3C,EAnBgB,CAoBhB;;AACH;AACJ;AACJ;AACJ;;AACD,eAAKM,UAAL;AACA,eAAKC,aAAL;AACA,eAAKC,YAAL,GA1GuB,CA2GvB;AACH;;AAEOF,QAAAA,UAAU,GAAG;AACjB,gBAAMG,UAAU,GAAG,KAAKlH,QAAxB;AACA,cAAI6F,GAAG,GAAG,KAAKtF,cAAL,CAAoB+B,IAA9B;AACA,cAAI6E,OAAO,GAAG,KAAK5G,cAAL,CAAoB6G,MAApB,EAAd;AACA,cAAIC,YAAY,GAAG,EAAnB;;AACA,eAAK,IAAI3F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmE,GAApB,EAAyBnE,CAAC,EAA1B,EAA8B;AAC1B,kBAAMZ,KAAK,GAAGqG,OAAO,CAACG,IAAR,GAAexG,KAA7B;AACA,gBAAIgF,SAAS,GAAGhF,KAAK,CAAC6F,SAAtB;AACA,gBAAIX,SAAS,GAAGlF,KAAK,CAAC8F,SAAtB;;AACA,gBAAI9F,KAAK,CAAC0F,OAAN,KAAkBU,UAAlB,IAAgC,CAAClB,SAAS,CAACrH,IAA3C,IAAmD,CAACmH,SAAS,CAACnH,IAA9D,IAAsE,CAACqH,SAAS,CAACrH,IAAV,CAAe0G,OAAtF,IAAiG,CAACS,SAAS,CAACnH,IAAV,CAAe0G,OAArH,EAA8H;AAC1H,mBAAKyB,UAAL,CAAgBhB,SAAhB,EAA2BE,SAA3B,EAAsC7G,aAAa,CAACoI,MAApD,EAD0H,CAE1H;;;AACAF,cAAAA,YAAY,CAAC7F,IAAb,CAAkBV,KAAK,CAACmF,EAAxB;AACH;AACJ;;AACDJ,UAAAA,GAAG,GAAGwB,YAAY,CAAC1F,MAAb,GAAsB,CAA5B;;AACA,iBAAOkE,GAAG,IAAI,CAAd,EAAiB;AACb,iBAAKtF,cAAL,CAAoBiH,MAApB,CAA2BH,YAAY,CAACxB,GAAD,CAAvC;AACAA,YAAAA,GAAG;AACN;;AACDwB,UAAAA,YAAY,CAAC1F,MAAb,GAAsB,CAAtB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEYmF,QAAAA,UAAU,CAACW,SAAD,EAAuBC,SAAvB,EAA6CrF,IAA7C,EAAkE;AAChF,cAAI,CAACoF,SAAD,IAAc,CAACA,SAAS,CAAC9I,IAA7B,EAAmC,OAD6C,CAEhF;;AACA,cAAIgJ,MAAM,GAAGF,SAAS,CAAC9I,IAAV,CAAeiJ,WAA5B;AACA,cAAIC,IAAJ;;AACA,eAAK,IAAInG,CAAC,GAAG,CAAR,EAAW0B,CAAC,GAAGuE,MAAM,CAAChG,MAA3B,EAAmCD,CAAC,GAAG0B,CAAvC,EAA0C1B,CAAC,EAA3C,EAA+C;AAC3CmG,YAAAA,IAAI,GAAGF,MAAM,CAACjG,CAAD,CAAb;;AACA,gBAAImG,IAAI,CAACxF,IAAD,CAAR,EAAgB;AACZwF,cAAAA,IAAI,CAACxF,IAAD,CAAJ,CAAWqF,SAAX,EAAsBD,SAAtB;AACH;AACJ;;AACD,cAAI,KAAK7H,iBAAL,IAA0ByC,IAAI,KAAKlD,aAAa,CAAC0H,OAArD,EAA8D;AAAA;;AAC1D,0CAAKjH,iBAAL,8CAAyB6H,SAAzB,EAAoCC,SAApC;AACH,WAFD,MAGK,IAAI,KAAK7H,gBAAL,IAAyBwC,IAAI,KAAKlD,aAAa,CAACuH,MAApD,EAA4D;AAAA;;AAC7D,0CAAK7G,gBAAL,8CAAwB4H,SAAxB,EAAmCC,SAAnC;AACH,WAFI,MAGA,IAAI,KAAK5H,gBAAL,IAAyBuC,IAAI,KAAKlD,aAAa,CAACoI,MAApD,EAA4D;AAAA;;AAC7D,0CAAKzH,gBAAL,8CAAwB2H,SAAxB,EAAmCC,SAAnC;AACH;AACJ;;AAGyB,YAAfI,eAAe,GAAY;AAClC,iBAAO,KAAKpH,gBAAZ;AACH;;AACyB,YAAfoH,eAAe,CAAChH,KAAD,EAAiB;AACvC,cAAIA,KAAK,IAAI,CAAC,KAAKJ,gBAAnB,EAAqC;AACjC,iBAAKqH,oBAAL;;AACA,iBAAKnH,YAAL,CAAkBjC,IAAlB,CAAuBqJ,MAAvB,GAAgC,IAAhC;AACH,WAHD,MAIK,IAAI,CAAClH,KAAD,IAAU,KAAKJ,gBAAnB,EAAqC;AACtC,iBAAKE,YAAL,CAAkBwE,KAAlB;;AACA,iBAAKxE,YAAL,CAAkBjC,IAAlB,CAAuBqJ,MAAvB,GAAgC,KAAhC;AACH;;AACD,eAAKtH,gBAAL,GAAwBI,KAAxB;AACH;;AAG4B,YAAlBmH,kBAAkB,GAAY;AACrC,iBAAO,KAAKtH,mBAAZ;AACH;;AAC4B,YAAlBsH,kBAAkB,CAACnH,KAAD,EAAiB;AAC1C,cAAIA,KAAK,IAAI,CAAC,KAAKH,mBAAnB,EAAwC;AACpC,iBAAKoH,oBAAL;;AACA,iBAAKnH,YAAL,CAAkBjC,IAAlB,CAAuBqJ,MAAvB,GAAgC,IAAhC;AACH,WAHD,MAIK,IAAI,CAAClH,KAAD,IAAU,KAAKH,mBAAnB,EAAwC;AACzC,iBAAKC,YAAL,CAAkBwE,KAAlB;;AACA,iBAAKxE,YAAL,CAAkBjC,IAAlB,CAAuBqJ,MAAvB,GAAgC,KAAhC;AACH;;AACD,eAAKrH,mBAAL,GAA2BG,KAA3B;AACH;;AAGOiH,QAAAA,oBAAoB,GAAG;AAC3B,cAAI,CAAC,KAAKnH,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkByE,OAA7C,EAAsD;AAClD,gBAAI1G,IAAI,GAAG,IAAIzB,IAAJ,CAAS,+BAAT,CAAX,CADkD,CAElD;;AACAD,YAAAA,QAAQ,CAACiL,kBAAT,CAA4BvJ,IAA5B;AACA,iBAAKiC,YAAL,GAAoBjC,IAAI,CAACwJ,YAAL,CAAkBnL,QAAlB,CAApB;AACA,iBAAK4D,YAAL,CAAkBwH,SAAlB,GAA8B,CAA9B;AACH;AACJ;;AACDpB,QAAAA,aAAa,GAAG;AACZ,cAAI,CAAC,KAAKtG,gBAAV,EAA4B;AACxB;AACH;;AACD,eAAKqH,oBAAL;;AAEA,cAAIM,WAAW,GAAG,KAAKzH,YAAvB;AACAyH,UAAAA,WAAW,CAACjD,KAAZ;AAEA,cAAI9D,SAAS,GAAG,KAAKb,UAArB;;AAEA,eAAK,IAAIiB,CAAC,GAAG,CAAR,EAAW0B,CAAC,GAAG9B,SAAS,CAACK,MAA9B,EAAsCD,CAAC,GAAG0B,CAA1C,EAA6C1B,CAAC,EAA9C,EAAkD;AAC9C,gBAAIL,QAAQ,GAAGC,SAAS,CAACI,CAAD,CAAxB;AACA2G,YAAAA,WAAW,CAACC,WAAZ,GAA0BlL,KAAK,CAACmL,GAAhC;;AACA,gBAAIlH,QAAQ,CAACgB,IAAT,KAAkB;AAAA;AAAA,8CAAajE,GAA/B,IAAsCiD,QAAQ,CAACgB,IAAT,KAAkB;AAAA;AAAA,8CAAa7D,OAAzE,EAAkF;AAC9E;AACA,kBAAIgK,EAAE,GAAGnH,QAAQ,CAAC9C,WAAlB;;AACA,kBAAIiK,EAAE,CAAC7G,MAAH,GAAY,CAAhB,EAAmB;AACf5E,gBAAAA,IAAI,CAACmE,GAAL,CAASzD,QAAT,EAAmB+K,EAAE,CAAC,CAAD,CAAF,CAAM9M,CAAzB,EAA4B8M,EAAE,CAAC,CAAD,CAAF,CAAM7M,CAAlC;AACA0M,gBAAAA,WAAW,CAACI,MAAZ,CAAmBhL,QAAQ,CAAC/B,CAA5B,EAA+B+B,QAAQ,CAAC9B,CAAxC;;AACA,qBAAK,IAAIoK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyC,EAAE,CAAC7G,MAAvB,EAA+BoE,CAAC,EAAhC,EAAoC;AAChChJ,kBAAAA,IAAI,CAACmE,GAAL,CAASzD,QAAT,EAAmB+K,EAAE,CAACzC,CAAD,CAAF,CAAMrK,CAAzB,EAA4B8M,EAAE,CAACzC,CAAD,CAAF,CAAMpK,CAAlC;AACA0M,kBAAAA,WAAW,CAACK,MAAZ,CAAmBjL,QAAQ,CAAC/B,CAA5B,EAA+B+B,QAAQ,CAAC9B,CAAxC;AACH;;AACD0M,gBAAAA,WAAW,CAACM,KAAZ;AACAN,gBAAAA,WAAW,CAACO,MAAZ;AACH;AACJ,aAbD,MAcK,IAAIvH,QAAQ,CAACgB,IAAT,KAAkB;AAAA;AAAA,8CAAavE,MAAnC,EAA2C;AAC5C;AACAuK,cAAAA,WAAW,CAACQ,MAAZ,CAAmBxH,QAAQ,CAACkC,aAAT,CAAuB7H,CAA1C,EAA6C2F,QAAQ,CAACkC,aAAT,CAAuB5H,CAApE,EAAuE0F,QAAQ,CAAC0C,WAAhF,EAF4C,CAG5C;;AACAsE,cAAAA,WAAW,CAACO,MAAZ;AACH;AACJ;AACJ;;AAED3B,QAAAA,YAAY,GAAG;AACX,cAAI,CAAC,KAAKtG,mBAAV,EAA+B;AAC3B;AACH;;AACD,eAAKoH,oBAAL;;AAEA,cAAIM,WAAW,GAAG,KAAKzH,YAAvB;AACA,cAAI,CAAC,KAAKF,gBAAV,EAA4B2H,WAAW,CAACjD,KAAZ;;AAC5B,eAAKrF,KAAL,CAAW+I,MAAX,CAAkBT,WAAlB;AACH;;AAzeiC,O;;AAAjBnN,MAAAA,gB,CACFwE,S,GAA8B,I", "sourcesContent": ["import { _decorator, v2, mat4, Rect, Mat4, Vec2, rect, Graphics, director, Scheduler, game, Node, view, Color, Director, log} from 'cc';\nimport FBoxCollider from \"./FBoxCollider\";\nimport FCircleCollider from \"./FCircleCollider\";\nimport FCollider, { ColliderGroupType, ColliderType } from \"./FCollider\";\nimport FPolygonCollider from \"./FPolygonCollider\";\nimport { Intersection } from \"./Intersection\";\nimport { QuadTree } from \"./QuadTree\";\nlet tempVec2 = v2();\nlet tempMat4 = mat4();\nlet tempArr: any[] = [];\nlet collisionState: number = 0;\n\nexport const ColliderTriggerFuncs = [];\nColliderTriggerFuncs[ColliderType.Circle | ColliderType.Circle] = (self: FCollider, other: FCollider) => {\n    let selfCircle = self as FCircleCollider;\n    let otherCircle = other as FCircleCollider;\n    return Intersection.circleCircle(selfCircle, otherCircle);\n}\nColliderTriggerFuncs[ColliderType.Circle | ColliderType.Box] = (self: FCollider, other: FCollider) => {\n    let selfCircle = self as FCircleCollider;\n    let otherBox = other as FBoxCollider;\n    return Intersection.polygonCircle(otherBox.worldPoints, selfCircle);\n}\nColliderTriggerFuncs[ColliderType.Circle | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {\n    let selfCircle = self as FCircleCollider;\n    let otherPolygon = other as FPolygonCollider;\n    return Intersection.polygonCircle(otherPolygon.worldPoints, selfCircle);\n}\nColliderTriggerFuncs[ColliderType.Box | ColliderType.Box] = (self: FCollider, other: FCollider) => {\n    let selfBox = self as FBoxCollider;\n    let otherBox = other as FBoxCollider;\n    if (selfBox.node.angle === 0 && otherBox.node.angle == 0) {\n        return Intersection.rectRect(selfBox.aabb, otherBox.aabb);\n    } else {\n        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);\n    }\n}\nColliderTriggerFuncs[ColliderType.Box | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {\n    let selfBox = self as FBoxCollider;\n    let otherBox = other as FPolygonCollider;\n    if (!otherBox.isConvex) {\n        return Intersection.polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);\n    }\n    else {\n        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);\n    }\n}\nColliderTriggerFuncs[ColliderType.Polygon | ColliderType.Polygon] = (self: FCollider, other: FCollider) => {\n    let selfBox = self as FPolygonCollider;\n    let otherBox = other as FPolygonCollider;\n    if (!otherBox.isConvex || !selfBox.isConvex) {\n        return Intersection.polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);\n    }\n    else {\n        return Intersection.satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);\n    }\n}\n\nfunction obbApplyMatrix(rect: Rect, mat4: Mat4, out_bl: Vec2, out_tl: Vec2, out_tr: Vec2, out_br: Vec2) {\n    let x = rect.x;\n    let y = rect.y;\n    let width = rect.width;\n    let height = rect.height;\n\n    // 初始化一个长度为16的数组来存储矩阵元素\n    let mat4m = new Array(16);\n    // 或者使用 Float32Array(16) 如果你需要TypedArray\n    \n    // 将矩阵数据提取到数组中\n    Mat4.toArray(mat4m, mat4);\n    let m00 = mat4m[0], m01 = mat4m[1], m04 = mat4m[4], m05 = mat4m[5];\n    let m12 = mat4m[12], m13 = mat4m[13];\n\n    let tx = m00 * x + m04 * y + m12;\n    let ty = m01 * x + m05 * y + m13;\n    let xa = m00 * width;\n    let xb = m01 * width;\n    let yc = m04 * height;\n    let yd = m05 * height;\n\n    out_tl.x = tx;\n    out_tl.y = ty;\n    out_tr.x = xa + tx;\n    out_tr.y = xb + ty;\n    out_bl.x = yc + tx;\n    out_bl.y = yd + ty;\n    out_br.x = xa + yc + tx;\n    out_br.y = xb + yd + ty;\n}\n\nenum CollisionType {\n    onEnter = \"onCollisionEnter\",\n    onStay = \"onCollisionStay\",\n    onExit = \"onCollisionExit\"\n}\nexport interface IColliderPair {\n    id: number,\n    colliderA: FCollider,\n    colliderB: FCollider,\n    frameId: number,\n    state: CollisionType,\n}\n\nlet CollisionMatrix = {\n    [`${ColliderGroupType.BULLET_SELF}_${ColliderGroupType.ENEMY_NORMAL}`]: true,\n    [`${ColliderGroupType.BULLET_ENEMY}_${ColliderGroupType.PLAYER}`]: true,\n}\n\nexport default class FColliderManager {\n    private static _instance: FColliderManager = null;\n\n    public static get instance() {\n        if (!this._instance) {\n            this._instance = new FColliderManager();\n        }\n        return this._instance;\n    };\n\n    _onCollisionEnter: Function = null;\n    _onCollisionStay: Function = null;\n    _onCollisionExit: Function = null;\n\n    private _tree: QuadTree<FCollider> = null;\n    private _frameId: number = 0;\n    private constructor() {\n        this._frameId = 0;\n        this._tree = new QuadTree<FCollider>(this._treeRect, 0, this._maxDepth, this._maxChildren);\n    }\n    //是否要重新建树\n    private _treeDirty: boolean = true;\n\n    private _maxDepth: number = 6;\n    public get maxDepth(): number {\n        return this._maxDepth;\n    }\n    public set maxDepth(value: number) {\n        if (value != this._maxDepth) {\n            this._maxDepth = value;\n            this._treeDirty = true;\n        }\n    }\n\n    private _maxChildren: number = 12;\n    public get maxChildren(): number {\n        return this._maxChildren;\n    }\n    public set maxChildren(value: number) {\n        if (this._maxChildren != value) {\n            this._maxChildren = value;\n            this._treeDirty = true;\n        }\n    }\n\n    private _treeRect: Rect = rect(0, 0, view.getVisibleSize().width, view.getVisibleSize().height);\n    public get treeRect(): Rect {\n        return this._treeRect;\n    }\n    //设置四叉树大小\n    public set treeRect(value: Rect) {\n        if (this._treeRect) {\n            if (this._treeRect.equals(value)) return;\n        }\n        this._treeRect.set(value);\n        this._treeDirty = false;\n    }\n\n    private _enable: boolean = false;\n    public get enable(): boolean {\n        return this._enable;\n    }\n    public set enable(value: boolean) {\n        this._enable = value;\n        if (value) {\n            // director.getScheduler().enableForTarget(this);\n            // director.getScheduler().scheduleUpdate(this, Scheduler.PRIORITY_NON_SYSTEM, false);\n            // director.on(Director.EVENT_BEFORE_UPDATE, this.update, this);\n        }\n    }\n\n    private collisionPairs: Map<number, IColliderPair> = new Map();\n    private _colliders: FCollider[] = [];\n    public addCollider(collider: FCollider) {\n        let colliders = this._colliders;\n        this.initCollider(collider);\n        colliders.push(collider);\n    }\n    public removeCollider(collider: FCollider) {\n        for (let i = this._colliders.length - 1; i >= 0; i--) {\n            let c = this._colliders[i];\n            if (collider.colliderId === c.colliderId) {\n                this._colliders.splice(i, 1);\n            }\n        }\n    }\n\n    public initCollider(collider: FCollider) {\n        collider.initCollider();\n    }\n\n    setGlobalColliderEnterCall(func:Function) {\n        this._onCollisionEnter = func;\n    }\n    setGlobalColliderStayCall(func:Function) {\n        this._onCollisionStay = func;\n    }\n    setGlobalColliderExitCall(func:Function) {\n        this._onCollisionExit = func;\n    }\n\n    public updateCollider(c: FCollider) {\n        c.node.getWorldMatrix(tempMat4);\n        if (c.type === ColliderType.Box) {\n            let collider = c as FBoxCollider;\n            let size = collider.size;\n            collider.aabb.x = collider.offset.x - size.width / 2;\n            collider.aabb.y = collider.offset.y - size.height / 2;\n            collider.aabb.width = size.width;\n            collider.aabb.height = size.height;\n            let wps = collider.worldPoints;\n            let wp0 = wps[0], wp1 = wps[1],\n                wp2 = wps[2], wp3 = wps[3];\n            obbApplyMatrix(collider.aabb, tempMat4, wp0, wp1, wp2, wp3);\n            let minx = Math.min(wp0.x, wp1.x, wp2.x, wp3.x);\n            let miny = Math.min(wp0.y, wp1.y, wp2.y, wp3.y);\n            let maxx = Math.max(wp0.x, wp1.x, wp2.x, wp3.x);\n            let maxy = Math.max(wp0.y, wp1.y, wp2.y, wp3.y);\n            let worldEdge = collider.worldEdge;\n            for (let i = 0, l = wps.length; i < l; i++) {\n                if (!worldEdge[i]) worldEdge[i] = v2();\n                Vec2.subtract(worldEdge[i], wps[(i + 1) % l], wps[i]);\n            }\n            collider.aabb.x = minx;\n            collider.aabb.y = miny;\n            collider.aabb.width = maxx - minx;\n            collider.aabb.height = maxy - miny;\n        } else if (c.type == ColliderType.Circle) {\n            let collider = c as FCircleCollider;\n            Vec2.transformMat4(tempVec2, collider.offset, tempMat4);\n            collider.worldPosition.x = tempVec2.x;\n            collider.worldPosition.y = tempVec2.y;\n\n            let mm = new Array(16); // Create an array to hold the matrix data\n            Mat4.toArray(mm, tempMat4); // Extract the matrix data into the array\n\n            let tempx = mm[12], tempy = mm[13];\n            mm[12] = mm[13] = 0;\n\n            Mat4.fromArray(tempMat4, mm); \n\n\n            tempVec2.x = collider.radius;\n            tempVec2.y = 0;\n\n            Vec2.transformMat4(tempVec2, tempVec2, tempMat4);\n            let d = Math.sqrt(tempVec2.x * tempVec2.x + tempVec2.y * tempVec2.y);\n\n            collider.worldRadius = d;\n\n            collider.aabb.x = collider.worldPosition.x - d;\n            collider.aabb.y = collider.worldPosition.y - d;\n            collider.aabb.width = d * 2;\n            collider.aabb.height = d * 2;\n\n            mm[12] = tempx;\n            mm[13] = tempy;\n        } else if (c.type == ColliderType.Polygon) {\n            let collider = c as FPolygonCollider;\n            let points = collider.points;\n            let worldPoints = collider.worldPoints;\n            let worldEdge = collider.worldEdge;\n            worldPoints.length = points.length;\n\n            let minx = Number.MAX_SAFE_INTEGER, miny = Number.MAX_SAFE_INTEGER, maxx = -Number.MAX_SAFE_INTEGER, maxy = -Number.MAX_SAFE_INTEGER;\n            for (let i = 0, l = points.length; i < l; i++) {\n                if (!worldPoints[i]) {\n                    worldPoints[i] = v2();\n                }\n                tempVec2.x = points[i].x + collider.offset.x;\n                tempVec2.y = points[i].y + collider.offset.y;\n                Vec2.transformMat4(tempVec2, tempVec2, tempMat4);\n\n                let x = tempVec2.x;\n                let y = tempVec2.y;\n\n                worldPoints[i].set(tempVec2);\n\n                if (x > maxx) maxx = x;\n                if (x < minx) minx = x;\n                if (y > maxy) maxy = y;\n                if (y < miny) miny = y;\n            }\n            if (c.isConvex) {\n                for (let i = 0, l = worldPoints.length; i < l; i++) {\n                    if (!worldEdge[i]) worldEdge[i] = v2();\n                    Vec2.subtract(worldEdge[i], worldPoints[(i + 1) % l], worldPoints[i]);\n                }\n            }\n\n            collider.aabb.x = minx;\n            collider.aabb.y = miny;\n            collider.aabb.width = maxx - minx;\n            collider.aabb.height = maxy - miny;\n        }\n    }\n\n    public shouldCollide(c1: FCollider, c2: FCollider) {\n        if (c1.groupType === ColliderGroupType.DEFAULT || c2.groupType === ColliderGroupType.DEFAULT) {\n            return true;\n        }\n\n        return CollisionMatrix[`${c1.groupType}_${c2.groupType}`] || CollisionMatrix[`${c2.groupType}_${c1.groupType}`];\n    }\n\n    isOutOfScreen(aabb: Rect): boolean {\n        const visibleSize = view.getVisibleSize();\n        const screenLeft = -200;\n        const screenRight = visibleSize.width + 200;\n        const screenTop = visibleSize.height + 200;\n        const screenBottom = -200;\n    \n        // 判断是否超出屏幕边界\n        return (\n            aabb.x + aabb.width < screenLeft || // 超出屏幕左边\n            aabb.x > screenRight ||            // 超出屏幕右边\n            aabb.y + aabb.height < screenBottom || // 超出屏幕下边\n            aabb.y > screenTop                 // 超出屏幕上边\n        );\n    }\n\n    public update(dt: number) {\n        if (!this.enable) return;\n        // console.time(\"碰撞\");\n        this.oneTest(dt);\n        // console.timeEnd(\"碰撞\");\n    }\n\n    /**\n     * 1.清空四叉树\n     * 2.更新Collider\n     * 3.插入四叉树\n     * 4.筛选重复碰撞组(暂无)\n     * 5.碰撞检测\n     * @param dt \n     * @returns \n     */\n    public oneTest(dt: number) {\n        let timeNow = Date.now();\n        this._frameId++;\n\n        if (this._treeDirty) {\n            this._tree = new QuadTree<FCollider>(this._treeRect, 0, this._maxDepth, this._maxChildren);\n            this._treeDirty = false;\n        }\n        this._tree.clear();\n        for (let i = this._colliders.length - 1; i >= 0; i--) {\n            let collider = this._colliders[i];\n            if (!collider || !collider.isValid) {\n                this._colliders.splice(i, 1);\n                continue;\n            }\n            if (!collider.isEnable){\n                continue;\n            }\n            this.updateCollider(this._colliders[i]);\n            if (this.isOutOfScreen(collider.aabb)){\n                collider.entity?.onOutScreen?.();\n                continue;\n            }\n\n            this._tree.insert(this._colliders[i]);\n        }\n\n        tempArr.length = 0;\n        this._tree.getAllNeedTestColliders(tempArr);\n        for (let k = 0, klen = tempArr.length; k < klen; k++) {\n            let _colliders = tempArr[k];\n            for (let i = 0, len = _colliders.length; i < len; i++) {\n                let icollider = _colliders[i] as FCollider;\n                for (let j = i + 1; j < len; j++) {\n                    let jcollider = _colliders[j] as FCollider;\n                    if (!this.shouldCollide(icollider, jcollider)) continue;\n                    if (icollider.type < jcollider.type) {\n                        collisionState = ColliderTriggerFuncs[icollider.type | jcollider.type](icollider, jcollider);\n                    } else {\n                        collisionState = ColliderTriggerFuncs[jcollider.type | icollider.type](jcollider, icollider);\n                    }\n                    /*\n                    switch (icollider.type) {\n                        case ColliderType.Circle:\n                            if (jcollider.type === ColliderType.Circle) {\n                                collisionState = Intersection.circleCircle(icollider as FCircleCollider, jcollider as FCircleCollider) ? 1 : 0;\n                            } else if (jcollider.type === ColliderType.Box || jcollider.type === ColliderType.Polygon) {\n                                collisionState = Intersection.polygonCircle((jcollider as FPolygonCollider).worldPoints, icollider as FCircleCollider) ? 1 : 0;\n                            }\n                            break;\n                        case ColliderType.Box:\n                            if (jcollider.type === ColliderType.Circle) {\n                                collisionState = Intersection.polygonCircle((icollider as FPolygonCollider).worldPoints, jcollider as FCircleCollider) ? 1 : 0;\n                            } else if (jcollider.type === ColliderType.Box) {\n                                if (icollider.node.angle === 0 && jcollider.node.angle === 0) {\n                                    collisionState = Intersection.rectRect(icollider.aabb, jcollider.aabb) ? 1 : 0;\n                                } else {\n                                    collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;\n                                }\n                            }\n                            else if (jcollider.type === ColliderType.Polygon) {\n                                if (!jcollider.isConvex) {\n                                    collisionState = Intersection.polygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints) ? 1 : 0;\n                                }\n                                else {\n                                    collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;\n                                }\n                            }\n                            break;\n                        case ColliderType.Polygon:\n                            if (jcollider.type === ColliderType.Circle) {\n                                collisionState = Intersection.polygonCircle((icollider as FPolygonCollider).worldPoints, jcollider as FCircleCollider) ? 1 : 0;\n                            } else if (icollider.isConvex && jcollider.isConvex) {\n                                collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;\n                            } else {\n                                collisionState = Intersection.polygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints) ? 1 : 0;\n                            }\n                            break;\n                    }*/\n                    if (collisionState) {\n                        let id = 0, aid = icollider.colliderId, bid = jcollider.colliderId;\n                        if (aid < bid) { id = aid; aid = bid; bid = id; }\n                        id = (aid * (aid + 1) >> 1) + bid - 1;//NOTE: 康拓配对函数\n                        let pairs = this.collisionPairs;\n                        let data = pairs.get(id);\n                        if (data !== undefined) {\n                            data.frameId = this._frameId;\n                            data.state = CollisionType.onStay;\n                        } else {\n                            data = {\n                                id: id,\n                                colliderA: icollider,\n                                colliderB: jcollider,\n                                frameId: this._frameId,\n                                state: CollisionType.onEnter,\n                            }\n                            pairs.set(id, data);\n                        }\n                        this._doCollide(icollider, jcollider, data.state);\n                        // this._doCollide(jcollider, icollider, data.state);\n                    }\n                }\n            }\n        }\n        this.endTrigger();\n        this.drawColliders();\n        this.drawQuadTree();\n        // log(\"每帧碰撞检测耗时:\",Date.now() - timeNow + \"毫秒\");\n    }\n\n    private endTrigger() {\n        const curFrameId = this._frameId;\n        let len = this.collisionPairs.size;\n        let entries = this.collisionPairs.values();\n        let waitToDelete = [];\n        for (let i = 0; i < len; i++) {\n            const value = entries.next().value as IColliderPair;\n            let icollider = value.colliderA;\n            let jcollider = value.colliderB;\n            if (value.frameId !== curFrameId || !jcollider.node || !icollider.node || !jcollider.node.isValid || !icollider.node.isValid) {\n                this._doCollide(icollider, jcollider, CollisionType.onExit);\n                // this._doCollide(jcollider, icollider, CollisionType.onExit);\n                waitToDelete.push(value.id);\n            }\n        }\n        len = waitToDelete.length - 1;\n        while (len >= 0) {\n            this.collisionPairs.delete(waitToDelete[len]);\n            len--;\n        }\n        waitToDelete.length = 0;\n    }\n\n    /**\n     *更新两个碰撞体之前的碰撞信息,延迟到发生碰撞才创建\n     *\n     * @private\n     * @param {*} collider1\n     * @param {*} collider2\n     * @memberof _ColliderManager\n     */\n\n    private _doCollide(collider1: FCollider, collider2: FCollider, type: CollisionType) {\n        if (!collider1 || !collider1.node) return;\n        //@ts-ignore\n        let comps1 = collider1.node._components;\n        let comp;\n        for (let i = 0, l = comps1.length; i < l; i++) {\n            comp = comps1[i];\n            if (comp[type]) {\n                comp[type](collider2, collider1);\n            }\n        }\n        if (this._onCollisionEnter && type === CollisionType.onEnter) {\n            this._onCollisionEnter?.(collider1, collider2);\n        }\n        else if (this._onCollisionStay && type === CollisionType.onStay) {\n            this._onCollisionStay?.(collider1, collider2);\n        }\n        else if (this._onCollisionExit && type === CollisionType.onExit) {\n            this._onCollisionExit?.(collider1, collider2);\n        }\n    }\n\n    private _enableDebugDraw: boolean = false;\n    public get enableDebugDraw(): boolean {\n        return this._enableDebugDraw;\n    }\n    public set enableDebugDraw(value: boolean) {\n        if (value && !this._enableDebugDraw) {\n            this._checkDebugDrawValid();\n            this._debugDrawer.node.active = true;\n        }\n        else if (!value && this._enableDebugDraw) {\n            this._debugDrawer.clear();\n            this._debugDrawer.node.active = false;\n        }\n        this._enableDebugDraw = value;\n    }\n\n    private _enableQuadTreeDraw: boolean = false;\n    public get enableQuadTreeDraw(): boolean {\n        return this._enableQuadTreeDraw;\n    }\n    public set enableQuadTreeDraw(value: boolean) {\n        if (value && !this._enableQuadTreeDraw) {\n            this._checkDebugDrawValid();\n            this._debugDrawer.node.active = true;\n        }\n        else if (!value && this._enableQuadTreeDraw) {\n            this._debugDrawer.clear();\n            this._debugDrawer.node.active = false;\n        }\n        this._enableQuadTreeDraw = value;\n    }\n\n    _debugDrawer: Graphics = null;\n    private _checkDebugDrawValid() {\n        if (!this._debugDrawer || !this._debugDrawer.isValid) {\n            let node = new Node('FCOLLISION_MANAGER_DEBUG_DRAW');\n            // node.setSli;\n            director.addPersistRootNode(node);\n            this._debugDrawer = node.addComponent(Graphics);\n            this._debugDrawer.lineWidth = 5;\n        }\n    }\n    drawColliders() {\n        if (!this._enableDebugDraw) {\n            return;\n        }\n        this._checkDebugDrawValid();\n\n        let debugDrawer = this._debugDrawer;\n        debugDrawer.clear();\n\n        let colliders = this._colliders;\n\n        for (let i = 0, l = colliders.length; i < l; i++) {\n            let collider = colliders[i];\n            debugDrawer.strokeColor = Color.RED;\n            if (collider.type === ColliderType.Box || collider.type === ColliderType.Polygon) {\n                //@ts-ignore\n                let ps = collider.worldPoints;\n                if (ps.length > 0) {\n                    Vec2.set(tempVec2, ps[0].x, ps[0].y);\n                    debugDrawer.moveTo(tempVec2.x, tempVec2.y);\n                    for (let j = 1; j < ps.length; j++) {\n                        Vec2.set(tempVec2, ps[j].x, ps[j].y);\n                        debugDrawer.lineTo(tempVec2.x, tempVec2.y);\n                    }\n                    debugDrawer.close();\n                    debugDrawer.stroke();\n                }\n            }\n            else if (collider.type === ColliderType.Circle) {\n                //@ts-ignore\n                debugDrawer.circle(collider.worldPosition.x, collider.worldPosition.y, collider.worldRadius);\n                // console.log(collider.worldPosition.toString(),collider.worldRadius)\n                debugDrawer.stroke();\n            }\n        }\n    }\n\n    drawQuadTree() {\n        if (!this._enableQuadTreeDraw) {\n            return;\n        }\n        this._checkDebugDrawValid();\n\n        let debugDrawer = this._debugDrawer;\n        if (!this._enableDebugDraw) debugDrawer.clear();\n        this._tree.render(debugDrawer);\n    }\n}\n"]}