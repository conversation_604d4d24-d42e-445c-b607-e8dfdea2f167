{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts"], "names": ["SceneManager", "SingletonBase", "GameEnum", "GameMapRun", "EnemyBase", "getScenePos", "entity", "sceneLayer", "mapToBattleScene", "node", "x", "y", "position", "parentName", "type", "EnemyType", "Missile", "parent", "scaleX", "scaleY", "lastParent", "name", "scale", "getLayerSpeed", "getMapSpeed", "layer", "instance", "MapSpeed", "ViewTop", "getSceneEntity", "result"], "mappings": ";;;8EAOaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,Q;;AAEAC,MAAAA,U;;AACAC,MAAAA,S;;;;;;;8BAGMJ,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAE1D;AACJ;AACA;AACA;AACA;AACIK,QAAAA,WAAW,CAACC,MAAD,EAAS;AAEhB,cAAIA,MAAM;AAAA;AAAA,qCAAV,EAAiC;AAC7B,kBAAMC,UAAU,GAAGD,MAAM,CAACC,UAA1B;;AACA,gBAAIA,UAAU,IAAI,CAAlB,EAAqB;AACjB,qBAAO,KAAKC,gBAAL,CAAsBF,MAAM,CAACG,IAAP,CAAYC,CAAlC,EAAqCJ,MAAM,CAACG,IAAP,CAAYE,CAAjD,EAAoDJ,UAApD,CAAP;AACH;;AAED,gBAAIG,CAAC,GAAGJ,MAAM,CAACG,IAAP,CAAYG,QAAZ,CAAqBF,CAA7B;AACA,gBAAIC,CAAC,GAAGL,MAAM,CAACG,IAAP,CAAYG,QAAZ,CAAqBD,CAA7B;AACA,gBAAIE,UAAU,GAAG,YAAjB;;AACA,gBAAIP,MAAM,CAACQ,IAAP,KAAgB;AAAA;AAAA,sCAASC,SAAT,CAAmBC,OAAvC,EAAgD;AAC5CH,cAAAA,UAAU,GAAG,aAAb;AACH;;AAED,gBAAII,MAAM,GAAGX,MAAM,CAACG,IAAP,CAAYQ,MAAzB;AACA,gBAAIC,MAAM,GAAG,CAAb;AACA,gBAAIC,MAAM,GAAG,CAAb;AACA,gBAAIC,UAAU,GAAG,IAAjB;;AAEA,mBAAOH,MAAM,IAAIA,MAAM,CAACI,IAAP,KAAgBR,UAAjC,EAA6C;AACzCK,cAAAA,MAAM,IAAID,MAAM,CAACK,KAAP,CAAaZ,CAAvB;AACAS,cAAAA,MAAM,IAAIF,MAAM,CAACK,KAAP,CAAaX,CAAvB;AACAD,cAAAA,CAAC,IAAIO,MAAM,CAACP,CAAZ;AACAC,cAAAA,CAAC,IAAIM,MAAM,CAACN,CAAZ;AACAS,cAAAA,UAAU,GAAGH,MAAb;AACAA,cAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AAED,gBAAIG,UAAJ,EAAgB;AACZV,cAAAA,CAAC,IAAIU,UAAU,CAACV,CAAhB;AACAC,cAAAA,CAAC,IAAIS,UAAU,CAACT,CAAhB;AACAD,cAAAA,CAAC,IAAIQ,MAAL;AACAP,cAAAA,CAAC,IAAIQ,MAAL;AACAT,cAAAA,CAAC,IAAIU,UAAU,CAACV,CAAhB;AACAC,cAAAA,CAAC,IAAIS,UAAU,CAACT,CAAhB;AACH,aAPD,MAOO;AACHD,cAAAA,CAAC,IAAIQ,MAAL;AACAP,cAAAA,CAAC,IAAIQ,MAAL;AACH;;AAED,mBAAO;AAAET,cAAAA,CAAF;AAAKC,cAAAA;AAAL,aAAP;AACH,WA1Ce,CA4ChB;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAEA,iBAAO;AAAED,YAAAA,CAAC,EAAEJ,MAAM,CAACG,IAAP,CAAYC,CAAjB;AAAoBC,YAAAA,CAAC,EAAEL,MAAM,CAACG,IAAP,CAAYE;AAAnC,WAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIY,QAAAA,aAAa,CAACjB,MAAD,EAAS;AAClB;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIkB,QAAAA,WAAW,CAACC,KAAD,EAAQ;AACf,iBAAO;AAAA;AAAA,wCAAWC,QAAX,CAAoBC,QAA3B;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACInB,QAAAA,gBAAgB,CAACE,CAAD,EAAIC,CAAJ,EAAOc,KAAP,EAAc;AAC1B,iBAAO;AACHf,YAAAA,CADG;AAEHC,YAAAA,CAAC,EAAEA,CAAC,GAAG;AAAA;AAAA,0CAAWe,QAAX,CAAoBE;AAFxB,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACvB,MAAD,EAAS;AACnB,cAAIwB,MAAM,GAAGxB,MAAb,CADmB,CAGnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAOwB,MAAP;AACH;;AA3HyD,O", "sourcesContent": ["import { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport EnemyBase from \"../ui/plane/enemy/EnemyBase\";\r\n\r\n\r\nexport class SceneManager extends SingletonBase<SceneManager> {\r\n\r\n    /**\r\n     * 获取场景中的位置\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {Object} 包含 x 和 y 的位置对象\r\n     */\r\n    getScenePos(entity) {\r\n\r\n        if (entity instanceof EnemyBase) {\r\n            const sceneLayer = entity.sceneLayer;\r\n            if (sceneLayer >= 0) {\r\n                return this.mapToBattleScene(entity.node.x, entity.node.y, sceneLayer);\r\n            }\r\n\r\n            let x = entity.node.position.x;\r\n            let y = entity.node.position.y;\r\n            let parentName = \"enemyPlane\";\r\n            if (entity.type === GameEnum.EnemyType.Missile) {\r\n                parentName = \"enemyBullet\";\r\n            }\r\n\r\n            let parent = entity.node.parent;\r\n            let scaleX = 1;\r\n            let scaleY = 1;\r\n            let lastParent = null;\r\n\r\n            while (parent && parent.name !== parentName) {\r\n                scaleX *= parent.scale.x;\r\n                scaleY *= parent.scale.y;\r\n                x += parent.x;\r\n                y += parent.y;\r\n                lastParent = parent;\r\n                parent = parent.parent;\r\n            }\r\n\r\n            if (lastParent) {\r\n                x -= lastParent.x;\r\n                y -= lastParent.y;\r\n                x *= scaleX;\r\n                y *= scaleY;\r\n                x += lastParent.x;\r\n                y += lastParent.y;\r\n            } else {\r\n                x *= scaleX;\r\n                y *= scaleY;\r\n            }\r\n\r\n            return { x, y };\r\n        }\r\n\r\n        // if (entity instanceof BossUnitBase) {\r\n        //     const scenePos = entity.getScenePos();\r\n        //     return { x: scenePos.x, y: scenePos.y };\r\n        // }\r\n\r\n        // if (entity instanceof WinePlane) {\r\n        //     return entity.scenePos;\r\n        // }\r\n\r\n        return { x: entity.node.x, y: entity.node.y };\r\n    }\r\n\r\n    /**\r\n     * 获取场景层的速度\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {number} 场景层的速度\r\n     */\r\n    getLayerSpeed(entity) {\r\n        // if (entity instanceof EnemyBuild) {\r\n        //     const sceneLayer = entity.sceneLayer;\r\n        //     return this.getMapSpeed(sceneLayer);\r\n        // }\r\n\r\n        // if (entity instanceof EnemyTurret && entity.sceneLayer >= 0) {\r\n        //     return this.getMapSpeed(entity.sceneLayer);\r\n        // }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 获取地图层的速度\r\n     * @param {number} layer 地图层索引\r\n     * @returns {number} 地图层的速度\r\n     */\r\n    getMapSpeed(layer) {\r\n        return GameMapRun.instance.MapSpeed;\r\n    }\r\n\r\n    /**\r\n     * 将地图坐标转换为战斗场景坐标\r\n     * @param {number} x 地图坐标 x\r\n     * @param {number} y 地图坐标 y\r\n     * @param {number} layer 地图层索引\r\n     * @returns {Object} 包含 x 和 y 的战斗场景坐标\r\n     */\r\n    mapToBattleScene(x, y, layer) {\r\n        return {\r\n            x,\r\n            y: y - GameMapRun.instance.ViewTop,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 获取场景中的实体\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {Entity} 实体对象\r\n     */\r\n    getSceneEntity(entity) {\r\n        let result = entity;\r\n\r\n        // if (entity instanceof BossUnit) {\r\n        //     result = entity.bossEntity;\r\n        // } else if (entity instanceof AttackPoint) {\r\n        //     result = entity.getOwnEntity();\r\n        // } else if (entity instanceof EnemyLine) {\r\n        //     result = entity.owner;\r\n        // } else if (entity instanceof FireShells) {\r\n        //     result = entity.sceneEntity;\r\n        // }\r\n\r\n        return result;\r\n    }\r\n}"]}