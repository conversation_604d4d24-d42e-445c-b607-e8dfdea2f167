{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/PopupUI.ts"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "PopupUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "message", "label", "string", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGjBM,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACJ,KAAD,C,2BAHb,MACaK,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;AAAA;;AAKZ,eAANC,MAAM,GAAW;AAAE,iBAAO,iBAAP;AAA2B;;AACtC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC/B,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CACxB;;AAEW,cAANC,MAAM,CAACC,OAAD,EAAiC;AACzC,eAAKC,KAAL,CAAWC,MAAX,GAAoBF,OAApB;AACH;;AACW,cAANG,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AApB+B,O;;;;;iBAGjB,I", "sourcesContent": ["import { _decorator, Label } from 'cc';\nimport { BaseUI, UILayer, UIOpt } from '../UIMgr';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PopupUI')\nexport class PopupUI extends BaseUI {\n\n    @property(Label)\n    label: Label = null;\n\n    public static getUrl(): string { return \"ui/main/PopupUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n\n    protected onLoad(): void {\n    }\n\n    async onShow(message: string): Promise<void> {\n        this.label.string = message;\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n    }\n\n}\n"]}