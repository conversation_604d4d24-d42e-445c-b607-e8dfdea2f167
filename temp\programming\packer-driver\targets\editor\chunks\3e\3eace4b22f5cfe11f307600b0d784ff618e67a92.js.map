{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts"], "names": ["_decorator", "Component", "Node", "NodePool", "Color", "instantiate", "Sprite", "view", "GameIns", "ColliderType", "ccclass", "property", "ColliderTest", "nodeCount", "pool", "width", "height", "halfWidth", "halfHeight", "_list", "start", "getVisibleSize", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "setChangeColor", "RED", "setGlobalColliderExitCall", "WHITE", "i", "index", "Math", "floor", "createNode", "collider", "color", "type", "Polygon", "nodes", "node", "children", "length", "getComponent", "random", "min", "max", "get", "testPrefabs", "parent", "parentNode", "active", "x", "y", "setPosition", "push", "update", "dt", "position"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAmBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;;AAC3EC,MAAAA,O,iBAAAA,O;;AACWC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACd;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAITY,Y,WADpBF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAAC,CAACT,IAAD,CAAD,C,UAERS,QAAQ,CAACT,IAAD,C,2BALb,MACqBU,YADrB,SAC0CX,SAD1C,CACoD;AAAA;AAAA;AAAA,eACxCY,SADwC,GACpB,IADoB;;AAAA;;AAAA;;AAAA,eAOxCC,IAPwC;AAAA,eAQxCC,KARwC;AAAA,eASxCC,MATwC;AAAA,eAUxCC,SAVwC;AAAA,eAWxCC,UAXwC;AAAA,eAmDxCC,KAnDwC,GAmDxB,EAnDwB;AAAA;;AAahDC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,MAAL,GAAcT,IAAI,CAACc,cAAL,GAAsBL,MAApC;AACA,eAAKD,KAAL,GAAaR,IAAI,CAACc,cAAL,GAAsBN,KAAnC;AACA,eAAKE,SAAL,GAAiB,KAAKF,KAAL,GAAa,GAA9B;AACA,eAAKG,UAAL,GAAkB,KAAKF,MAAL,GAAc,GAAhC;AACA;AAAA;AAAA,kCAAQM,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAAgD;AAChG,iBAAKC,cAAL,CAAoBF,SAApB,EAA8BrB,KAAK,CAACwB,GAApC;AACA,iBAAKD,cAAL,CAAoBD,SAApB,EAA8BtB,KAAK,CAACwB,GAApC;AAEH,WAJD;AAKA;AAAA;AAAA,kCAAQN,gBAAR,CAAyBO,yBAAzB,CAAmD,CAACJ,SAAD,EAAuBC,SAAvB,KAAgD;AAC/F,iBAAKC,cAAL,CAAoBF,SAApB,EAA8BrB,KAAK,CAAC0B,KAApC;AACA,iBAAKH,cAAL,CAAoBD,SAApB,EAA8BtB,KAAK,CAAC0B,KAApC;AACH,WAHD;AAKA,eAAKhB,IAAL,GAAY,IAAIX,QAAJ,EAAZ;;AACA,eAAK,IAAI4B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlB,SAAzB,EAAoCkB,CAAC,EAArC,EAAyC;AACrC,gBAAIC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWH,CAAC,GAAG,CAAf,CAAZ;AACA,iBAAKI,UAAL;AACH;AACJ;;AAEDR,QAAAA,cAAc,CAACS,QAAD,EAAoBC,KAApB,EAAgC;AAC1C,cAAID,QAAQ,CAACE,IAAT,IAAiB;AAAA;AAAA,4CAAaC,OAAlC,EAA0C;AACtC,gBAAKC,KAAK,GAAGJ,QAAQ,CAACK,IAAT,CAAcC,QAA3B;;AACA,iBAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,KAAK,CAACG,MAA1B,EAAkCZ,CAAC,EAAnC,EAAuC;AACnCS,cAAAA,KAAK,CAACT,CAAD,CAAL,CAASa,YAAT,CAAsBtC,MAAtB,EAA+B+B,KAA/B,GAAuCA,KAAvC;AACH;AACJ,WALD,MAKK;AACDD,YAAAA,QAAQ,CAACK,IAAT,CAAcG,YAAd,CAA2BtC,MAA3B,EAAoC+B,KAApC,GAA4CA,KAA5C;AACH;AACJ;;AAEDQ,QAAAA,MAAM,CAACC,GAAD,EAAcC,GAAd,EAA2B;AAC7B,iBAAOd,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACY,MAAL,MAAiBE,GAAG,GAAGD,GAAvB,CAAX,IAA0CA,GAAjD;AACH;;AAGDX,QAAAA,UAAU,GAAG;AACT,cAAIM,IAAI,GAAG,KAAK3B,IAAL,CAAUkC,GAAV,EAAX;AACA,cAAIhB,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACY,MAAL,KAAgB,KAAKI,WAAL,CAAiBN,MAA5C,CAAZ;AACA,cAAI,CAACF,IAAL,EAAWA,IAAI,GAAGpC,WAAW,CAAC,KAAK4C,WAAL,CAAiBjB,KAAjB,CAAD,CAAlB;AACXS,UAAAA,IAAI,CAACS,MAAL,GAAc,KAAKC,UAAnB;AACAV,UAAAA,IAAI,CAACW,MAAL,GAAc,IAAd;AACA,cAAIC,CAAC,GAAG,CAACpB,IAAI,CAACY,MAAL,KAAgB,GAAjB,IAAwB,KAAK9B,KAArC;AACA,cAAIuC,CAAC,GAAG,CAACrB,IAAI,CAACY,MAAL,KAAgB,GAAjB,IAAwB,KAAK7B,MAArC;AACAyB,UAAAA,IAAI,CAACc,WAAL,CAAiBF,CAAjB,EAAoBC,CAApB;;AACA,eAAKnC,KAAL,CAAWqC,IAAX,CAAgBf,IAAhB;;AACAA,UAAAA,IAAI,CAAC,IAAD,CAAJ,GAAaR,IAAI,CAACY,MAAL,KAAgB,CAAhB,GAAoB,GAAjC;AACAJ,UAAAA,IAAI,CAAC,IAAD,CAAJ,GAAaR,IAAI,CAACY,MAAL,KAAgB,CAAhB,GAAoB,GAAjC;AACH;;AACSY,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/B,eAAK,IAAI3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKZ,KAAL,CAAWwB,MAA/B,EAAuCZ,CAAC,EAAxC,EAA4C;AACxC,gBAAIU,IAAI,GAAG,KAAKtB,KAAL,CAAWY,CAAX,CAAX;AACA,gBAAIsB,CAAC,GAAGZ,IAAI,CAACkB,QAAL,CAAcN,CAAd,GAAkBZ,IAAI,CAAC,IAAD,CAA9B;AACA,gBAAIa,CAAC,GAAGb,IAAI,CAACkB,QAAL,CAAcL,CAAd,GAAkBb,IAAI,CAAC,IAAD,CAA9B;AACAA,YAAAA,IAAI,CAACc,WAAL,CAAiBF,CAAjB,EAAoBC,CAApB;;AACA,gBAAIb,IAAI,CAACkB,QAAL,CAAcN,CAAd,GAAkB,KAAKpC,SAAvB,IAAoCwB,IAAI,CAACkB,QAAL,CAAcN,CAAd,GAAkB,CAAC,KAAKpC,SAAhE,EAA2E;AACvEwB,cAAAA,IAAI,CAAC,IAAD,CAAJ,IAAc,CAAC,CAAf;AACH;;AACD,gBAAIA,IAAI,CAACkB,QAAL,CAAcL,CAAd,GAAkB,KAAKpC,UAAvB,IAAqCuB,IAAI,CAACkB,QAAL,CAAcL,CAAd,GAAkB,CAAC,KAAKpC,UAAjE,EAA6E;AACzEuB,cAAAA,IAAI,CAAC,IAAD,CAAJ,IAAc,CAAC,CAAf;AACH;AACJ;;AACD;AAAA;AAAA,kCAAQnB,gBAAR,CAAyBmC,MAAzB,CAAgCC,EAAhC;AACH;;AA/E+C,O;;;;;iBAG1B,E;;;;;;;iBAEI,I", "sourcesContent": ["\r\n\r\n\r\nimport { _decorator, Component, Prefab, Node, NodePool, Color, instantiate, Sprite, view, v3, Vec3, Graphics } from 'cc';\r\nimport { GameIns } from './Game/GameIns';\r\nimport FCollider, { ColliderType } from './Game/collider-system/FCollider';\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('ColliderTest')\r\nexport default class ColliderTest extends Component {\r\n    private nodeCount: number = 1000;\r\n    @property([Node])\r\n    testPrefabs: Node[] = [];\r\n    @property(Node)\r\n    parentNode: Node | null = null;\r\n\r\n    private pool: NodePool;\r\n    private width: number;\r\n    private height: number;\r\n    private halfWidth: number;\r\n    private halfHeight: number;\r\n\r\n    start() {\r\n        this.height = view.getVisibleSize().height;\r\n        this.width = view.getVisibleSize().width;\r\n        this.halfWidth = this.width * 0.5;\r\n        this.halfHeight = this.height * 0.5;\r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            this.setChangeColor(colliderA,Color.RED);\r\n            this.setChangeColor(colliderB,Color.RED);\r\n\r\n        });\r\n        GameIns.fColliderManager.setGlobalColliderExitCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            this.setChangeColor(colliderA,Color.WHITE);\r\n            this.setChangeColor(colliderB,Color.WHITE);\r\n        });\r\n\r\n        this.pool = new NodePool();\r\n        for (let i = 0; i < this.nodeCount; i++) {\r\n            let index = Math.floor(i / 2);\r\n            this.createNode();\r\n        }\r\n    }\r\n\r\n    setChangeColor(collider:FCollider,color:Color){\r\n        if (collider.type == ColliderType.Polygon){\r\n            let  nodes = collider.node.children;\r\n            for (let i = 0; i < nodes.length; i++) {\r\n                nodes[i].getComponent(Sprite)!.color = color;\r\n            }\r\n        }else{\r\n            collider.node.getComponent(Sprite)!.color = color;\r\n        }\r\n    }\r\n\r\n    random(min: number, max: number) {\r\n        return Math.floor(Math.random() * (max - min)) + min;\r\n    }\r\n\r\n    private _list: Node[] = [];\r\n    createNode() {\r\n        let node = this.pool.get();\r\n        let index = Math.floor(Math.random() * this.testPrefabs.length);\r\n        if (!node) node = instantiate(this.testPrefabs[index]);\r\n        node.parent = this.parentNode;\r\n        node.active = true;\r\n        let x = (Math.random() - 0.5) * this.width;\r\n        let y = (Math.random() - 0.5) * this.height;\r\n        node.setPosition(x, y)\r\n        this._list.push(node);\r\n        node[\"vx\"] = Math.random() * 3 - 1.5;\r\n        node[\"vy\"] = Math.random() * 3 - 1.5;\r\n    }\r\n    protected update(dt: number): void {\r\n        for (let i = 0; i < this._list.length; i++) {\r\n            let node = this._list[i];\r\n            let x = node.position.x + node[\"vx\"];\r\n            let y = node.position.y + node[\"vy\"];\r\n            node.setPosition(x, y)\r\n            if (node.position.x > this.halfWidth || node.position.x < -this.halfWidth) {\r\n                node[\"vx\"] *= -1;\r\n            }\r\n            if (node.position.y > this.halfHeight || node.position.y < -this.halfHeight) {\r\n                node[\"vy\"] *= -1;\r\n            }\r\n        }\r\n        GameIns.fColliderManager.update(dt);\r\n    }\r\n}"]}