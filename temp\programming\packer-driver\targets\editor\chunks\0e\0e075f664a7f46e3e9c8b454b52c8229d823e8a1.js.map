{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts"], "names": ["_decorator", "Node", "Sprite", "Vec2", "tween", "v2", "misc", "instantiate", "UIOpacity", "UITransform", "v3", "EnemyBase", "Tools", "GameIns", "GameEnum", "EnemyShootComponent", "EnemyShootData", "TrackComponent", "GameConst", "EnemyPlaneRole", "PfFrameAnim", "ccclass", "property", "EnemyPlane", "_data", "_trackCom", "_shootCom", "_curAction", "_removeCount", "_removeTime", "_curAngle", "_destAngle", "_curDir", "ZERO", "_destDir", "_rotateSpeed", "_leaveAct", "_roleIndex", "_curFormIndex", "_curTrackType", "_dieAnimEnd", "_initAngle", "_hpWhiteTween", "_bDieShoot", "m_outTime", "dir", "preLoad", "addScript", "node", "preLoadUI", "data", "role", "init", "_reset", "dieBullet", "setUIData", "enemyManager", "getEnemyUIData", "uiId", "_refreshProperty", "_refreshUI", "_refreshColliders", "setDir", "setAngle", "initAttr", "attr", "reset", "EnemyAction", "Track", "initComps", "isSpecialEnemy", "id", "setAtkStartCall", "setAction", "AttackPrepare", "setAtkOverCall", "AttackOver", "initTrack", "trackData", "trackParams", "offsetX", "offsetY", "rotateSpeed", "setTrackGroupStartCall", "track", "groupType", "groupIndex", "setTrackGroupOverCall", "uiData", "isAm", "Transform", "setTrackOverCall", "die", "EnemyDestroyType", "TrackOver", "setTrackLeaveCall", "Leave", "setTrackStartCall", "trackType", "setTrackType", "setFirstShoot<PERSON>elay", "delay", "startBattle", "active", "_refreshHpBar", "setTrackAble", "startTrack", "updateGameLogic", "deltaTime", "isDead", "_checkRemoveAble", "isStandBy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enemy<PERSON>uff", "Ice", "_updateAction", "setNextAble", "<PERSON><PERSON><PERSON>", "_updateCurDir", "collideAble", "isMoving", "bMoveAttack", "bStayAttack", "AttackIng", "TimeOver", "removeAble", "bTurnDir", "getDegreeForDir", "getDir", "setDestDir", "angle", "setDestAngle", "delta", "Math", "abs", "checkLiveAble", "setFormIndex", "index", "bAttackAbles", "shootData", "attackInterval", "attackNum", "attackPointArr", "attackArrNum", "length", "setShootData", "frameTime", "ActionFrameTime", "type", "tail", "getChildByName", "<PERSON><PERSON><PERSON><PERSON>", "setPlaneFrame", "addComponent", "getComponent", "anchorY", "setScale", "to", "scale", "call", "start", "scheduleOnce", "_playTailEffects", "position", "endX", "trackOffsetX", "endY", "trackOffsetY", "setPos", "trackFinish", "appearNode", "_createAppearEffect", "tailEffects", "tailFrames", "scales", "scaleX", "scaleY", "for<PERSON>ach", "name", "gameResManager", "frameAnim", "enemyAtlas", "stop", "showParam", "setPosition", "action", "setIsShooting", "hpBg", "opacity", "stopShoot", "playAnim", "setNextShootAtOnce", "playAtkAnim", "startAttack", "startShoot", "attackOver", "direction", "angleChange", "newAngle", "rotatedDir", "x", "y", "rotate", "degreesToRadians", "curHp", "hp", "maxHp", "defence", "attack", "collideLevel", "collideAtk", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "isRespawn", "param", "hpParam", "hpSpr", "hpWhite", "error", "colliderData", "collider", "setCollideData", "hpRatio", "isDecreasing", "fill<PERSON><PERSON><PERSON>", "duration", "onHurt", "checkHp", "onRemoveBuff", "buffType", "onAddBuff", "onDie", "destroyType", "will<PERSON><PERSON><PERSON>", "Die", "playDieAnim", "_checkDieShoot", "removeChildByName", "checkLoot", "onDieAnimEnd", "value", "setRoleOpacity", "getFireBulletAngle", "checkInScreen", "itemParent", "isPlaneOutScreen", "PrefabName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAsBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAA6BC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AACrIC,MAAAA,S;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACAC,MAAAA,mB;;AAC6BC,MAAAA,c,iBAAAA,c;;AAC7BC,MAAAA,c;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;;;;;;;;OAGD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBtB,U;;yBAGTuB,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ;AAAA;AAAA,2C,UAGRA,QAAQ,CAACpB,MAAD,C,UAERoB,QAAQ,CAACpB,MAAD,C,UAERoB,QAAQ,CAACpB,MAAD,C,sCATb,MACqBqB,UADrB;AAAA;AAAA,kCACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAW9CC,KAX8C,GAWtB,IAXsB;AAAA,eAY9CC,SAZ8C,GAYlB,IAZkB;AAAA,eAa9CC,SAb8C,GAab,IAba;AAAA,eAc9CC,UAd8C,GAczB,CAdyB;AAAA,eAe9CC,YAf8C,GAevB,CAfuB;AAAA,eAgB9CC,WAhB8C,GAgBxB,CAhBwB;AAAA,eAmB9CC,SAnB8C,GAmB1B,CAnB0B;AAAA,eAoB9CC,UApB8C,GAoBzB,IApByB;AAAA,eAqB9CC,OArB8C,GAqB9B7B,IAAI,CAAC8B,IArByB;AAAA,eAsB9CC,QAtB8C,GAsB7B/B,IAAI,CAAC8B,IAtBwB;AAAA,eAuB9CE,YAvB8C,GAuBvB,CAvBuB;AAAA,eAwB9CC,SAxB8C,GAwB1B,CAAC,CAxByB;AAAA,eAyB9CC,UAzB8C,GAyBzB,CAzByB;AAAA,eA0B9CC,aA1B8C,GA0BtB,CA1BsB;AAAA,eA2B9CC,aA3B8C,GA2BtB,CAAC,CA3BqB;AAAA,eA4B9CC,WA5B8C,GA4BvB,KA5BuB;AAAA,eA+B9CC,UA/B8C,GA+BxB,IA/BwB;AAAA,eAgC9CC,aAhC8C,GAgCzB,IAhCyB;AAAA,eAiC9CC,UAjC8C,GAiCxB,KAjCwB;AAAA,eAkC9CC,SAlC8C,GAkC1B,CAlC0B;AAAA,eAqC9CC,GArC8C;AAAA;;AAuC9CC,QAAAA,OAAO,GAAG;AACN,gBAAMA,OAAN;AACA,eAAKrB,SAAL,GAAiB;AAAA;AAAA,8BAAMsB,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;AACA,eAAKtB,SAAL,GAAiB;AAAA;AAAA,8BAAMqB,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,yDAAjB;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAoB;AACzB,eAAKC,IAAL,CAAUF,SAAV,CAAoBC,IAApB;AACH;;AAES,cAAJE,IAAI,CAACF,IAAD,EAAuB;AAC7B,eAAKG,MAAL;;AACA,eAAK7B,KAAL,GAAa0B,IAAb;AACA,eAAKI,SAAL,GAAiBJ,IAAI,CAACI,SAAtB;AACA,eAAKC,SAAL,CAAe;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,cAArB,CAAoC,KAAKjC,KAAL,CAAWkC,IAA/C,CAAf;;AACA,eAAKC,gBAAL;;AACA,gBAAM,KAAKC,UAAL,EAAN;;AACA,eAAKC,iBAAL;;AACA,eAAKC,MAAL,CAAYzD,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAAd;AACA,eAAK0D,QAAL,CAAc,CAAd;AACA,eAAKC,QAAL,CAAcd,IAAI,CAACe,IAAnB,EAV6B,CAY7B;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDZ,QAAAA,MAAM,GAAG;AACL,gBAAMa,KAAN;AACA,eAAKvC,UAAL,GAAkB;AAAA;AAAA,oCAASwC,WAAT,CAAqBC,KAAvC;AACA,eAAKvC,WAAL,GAAmB,CAAnB;AACA,eAAKD,YAAL,GAAoB,CAApB;AACA,eAAKY,WAAL,GAAmB,KAAnB;AACA,eAAKG,UAAL,GAAkB,KAAlB;AACA,eAAKL,aAAL,GAAqB,CAArB;AACA,eAAKC,aAAL,GAAqB,CAAC,CAAtB;AACA,eAAKT,SAAL,GAAiB,CAAjB;AACA,eAAKC,UAAL,GAAkB,IAAlB;AACA,eAAKC,OAAL,GAAe7B,IAAI,CAAC8B,IAApB;AACA,eAAKC,QAAL,GAAgB/B,IAAI,CAAC8B,IAArB;AACA,eAAKE,YAAL,GAAoB,CAApB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB;AACA,eAAKK,UAAL,GAAkB,IAAlB;AACA,eAAKG,SAAL,GAAiB,CAAjB;AACH;AAGD;AACJ;AACA;;;AACIyB,QAAAA,SAAS,GAAS;AACd;AACA,gBAAMA,SAAN,GAFc,CAId;;AACA,gBAAMC,cAAc,GAAG,KAAK9C,KAAL,CAAW+C,EAAX,IAAiB,KAAjB,IAA0B,KAAK/C,KAAL,CAAW+C,EAAX,GAAgB,KAAjE,CALc,CAOd;;AACA,eAAK7C,SAAL,CAAe0B,IAAf,CAAoB,IAApB,EAA0BkB,cAAc,GAAG,KAAKnB,IAAL,CAAUA,IAAV,CAAeH,IAAlB,GAAyB,KAAKA,IAAtE,EAA4E,IAA5E,EAAkFsB,cAAlF,EARc,CAUd;;;AACA,eAAK5C,SAAL,CAAe8C,eAAf,CAA+B,MAAM;AACjC,iBAAKC,SAAL,CAAe;AAAA;AAAA,sCAASN,WAAT,CAAqBO,aAApC;AACH,WAFD,EAXc,CAed;;;AACA,eAAKhD,SAAL,CAAeiD,cAAf,CAA8B,MAAM;AAChC,iBAAKF,SAAL,CAAe;AAAA;AAAA,sCAASN,WAAT,CAAqBS,UAApC;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAA0BC,WAA1B,EAAiDC,OAAjD,EAAkEC,OAAlE,EAAmFC,WAAW,GAAG,CAAjG,EAAoG;AACzG,eAAK/C,YAAL,GAAoB+C,WAApB;;AACA,eAAKzD,SAAL,CAAe2B,IAAf,CAAoB,IAApB,EAA0B0B,SAA1B,EAAqCC,WAArC,EAAkDC,OAAlD,EAA2DC,OAA3D,EAFyG,CAIzG;;;AACA,eAAKxD,SAAL,CAAe0D,sBAAf,CAAsC,CAACC,KAAD,EAAQC,SAAR,EAAmBC,UAAnB,KAAkC,CAEvE,CAFD;;AAIA,eAAK7D,SAAL,CAAe8D,qBAAf,CAAsCF,SAAD,IAAe;AAChD,gBAAI,KAAKG,MAAL,CAAYC,IAAZ,IAAoBJ,SAAS,KAAK,CAAtC,EAAyC;AACrC,mBAAKZ,SAAL,CAAe;AAAA;AAAA,wCAASN,WAAT,CAAqBuB,SAApC;AACH;AACJ,WAJD;;AAMA,eAAKjE,SAAL,CAAekE,gBAAf,CAAgC,MAAM;AAClC,iBAAKC,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,SAAnC;AACH,WAFD;;AAIA,eAAKrE,SAAL,CAAesE,iBAAf,CAAiC,MAAM;AACnC,iBAAKH,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BG,KAAnC;AACH,WAFD;;AAIA,eAAKvE,SAAL,CAAewE,iBAAf,CAAkCC,SAAD,IAAe;AAC5C,iBAAKC,YAAL,CAAkBD,SAAlB;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,kBAAkB,CAACC,KAAD,EAAQ;AACtB,eAAK3E,SAAL,CAAe0E,kBAAf,CAAkCC,KAAlC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,gBAAMA,WAAN;AACA,eAAKC,MAAL,GAAc,IAAd;;AACA,eAAKC,aAAL;;AACA,eAAK/E,SAAL,CAAegF,YAAf,CAA4B,IAA5B;;AACA,eAAKhF,SAAL,CAAeiF,UAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,gBAAMD,eAAN,CAAsBC,SAAtB;;AAEA,cAAI,KAAKC,MAAT,EAAiB;AACb,gBAAI,KAAKlE,UAAT,EAAqB;AACjB,mBAAKjB,SAAL,CAAeiF,eAAf,CAA+BC,SAA/B;AACH,aAFD,MAEO;AACH,mBAAKE,gBAAL,CAAsBF,SAAtB;AACH;AACJ,WAND,MAMO,IAAI,CAAC,KAAKG,SAAL,EAAL,EAAuB;AAC1B,iBAAK5D,IAAL,CAAUwD,eAAV,CAA0BC,SAA1B;;AACA,gBAAI,CAAC,KAAKI,WAAL,CAAiB;AAAA;AAAA,sCAASC,SAAT,CAAmBC,GAApC,CAAL,EAA+C;AAC3C,mBAAKzF,SAAL,CAAekF,eAAf,CAA+BC,SAA/B;;AACA,mBAAKlF,SAAL,CAAeiF,eAAf,CAA+BC,SAA/B;;AACA,mBAAKO,aAAL,CAAmBP,SAAnB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,aAAa,CAACP,SAAD,EAAY;AACrB,eAAKlF,SAAL,CAAe0F,WAAf,CAA2B,KAA3B;;AAEA,kBAAQ,KAAKzF,UAAb;AACI,iBAAK;AAAA;AAAA,sCAASwC,WAAT,CAAqBkD,KAA1B;AACI,mBAAKC,aAAL,CAAmBV,SAAnB;;AACA,mBAAKW,WAAL,GAAmB,KAAnB;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASpD,WAAT,CAAqBC,KAA1B;AACI,mBAAKkD,aAAL,CAAmBV,SAAnB;;AACA,mBAAKlF,SAAL,CAAe0F,WAAf,CACK,KAAK3F,SAAL,CAAe+F,QAAf,IAA2B,KAAKhG,KAAL,CAAWiG,WAAvC,IACC,CAAC,KAAKhG,SAAL,CAAe+F,QAAhB,IAA4B,KAAKhG,KAAL,CAAWkG,WAF5C;;AAIA;;AAEJ,iBAAK;AAAA;AAAA,sCAASvD,WAAT,CAAqBuB,SAA1B;AACI;;AAEJ,iBAAK;AAAA;AAAA,sCAASvB,WAAT,CAAqBO,aAA1B;AACA,iBAAK;AAAA;AAAA,sCAASP,WAAT,CAAqBwD,SAA1B;AACI,mBAAKL,aAAL,CAAmBV,SAAnB;;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASzC,WAAT,CAAqB6B,KAA1B;AACI,kBAAI,KAAK5D,SAAL,KAAmB,CAAvB,EAA0B;AACtB,qBAAKwD,GAAL,CAAS;AAAA;AAAA,0CAASC,gBAAT,CAA0B+B,QAAnC;AACH,eAFD,MAEO,IAAI,KAAKxF,SAAL,GAAiB,CAArB,EAAwB;AAC3B,qBAAKkF,aAAL,CAAmBV,SAAnB;AACH;;AACD;AA5BR;AA8BH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,gBAAgB,CAACF,SAAD,EAAY;AACxB,cAAI,KAAKpE,WAAT,EAAsB;AAClB,iBAAKZ,YAAL,IAAqBgF,SAArB;;AACA,gBAAI,KAAKhF,YAAL,GAAoB,KAAKC,WAA7B,EAA0C;AACtC,mBAAKgG,UAAL,GAAkB,IAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACI/D,QAAAA,MAAM,CAACjB,GAAD,EAAM;AACR,eAAKA,GAAL,GAAWA,GAAX;;AACA,cAAI,KAAKrB,KAAL,CAAWsG,QAAf,EAAyB;AACrB,iBAAK/D,QAAL,CAAc,CAAC;AAAA;AAAA,gCAAMgE,eAAN,CAAsBlF,GAAtB,CAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACImF,QAAAA,MAAM,GAAG;AACL,iBAAO,KAAKhG,OAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIiG,QAAAA,UAAU,CAACpF,GAAD,EAAM;AACZ,eAAKX,QAAL,GAAgBW,GAAhB;;AACA,cAAI,KAAKJ,UAAT,EAAqB;AACjB,iBAAKA,UAAL,GAAkB,KAAlB;AACA,iBAAKqB,MAAL,CAAYjB,GAAZ;AACH,WAHD,MAGO,IAAI,KAAKV,YAAL,IAAqB,CAAzB,EAA4B;AAC/B,iBAAK2B,MAAL,CAAYjB,GAAZ;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIkB,QAAAA,QAAQ,CAACmE,KAAD,EAAQ;AACZ,cAAIA,KAAK,GAAG,GAAZ,EAAiB;AACbA,YAAAA,KAAK,IAAI,GAAT;AACH,WAFD,MAEO,IAAIA,KAAK,GAAG,CAAC,GAAb,EAAkB;AACrBA,YAAAA,KAAK,IAAI,GAAT;AACH;;AACD,eAAKpG,SAAL,GAAiBoG,KAAjB;AACA,eAAK/E,IAAL,CAAUH,IAAV,CAAekF,KAAf,GAAuBA,KAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACD,KAAD,EAAQ;AAChB,cAAI,KAAK/F,YAAL,GAAoB,CAAxB,EAA2B;AACvB,gBAAIiG,KAAK,GAAGF,KAAK,GAAG,KAAKpG,SAAzB;;AACA,mBAAOuG,IAAI,CAACC,GAAL,CAASF,KAAT,IAAkB,GAAzB,EAA8B;AAC1BF,cAAAA,KAAK,IAAIA,KAAK,GAAG,KAAKpG,SAAb,GAAyB,CAAC,GAA1B,GAAgC,GAAzC;AACAsG,cAAAA,KAAK,GAAGF,KAAK,GAAG,KAAKpG,SAArB;AACH;;AACD,gBAAIuG,IAAI,CAACC,GAAL,CAASF,KAAT,IAAkB,GAAtB,EAA2B;AACvBF,cAAAA,KAAK,IAAIA,KAAK,GAAG,KAAKpG,SAAb,GAAyB,CAAC,GAA1B,GAAgC,GAAzC;AACH;;AACD,iBAAKC,UAAL,GAAkBmG,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAK5G,UAAL,KAAoB;AAAA;AAAA,oCAASwC,WAAT,CAAqBC,KAA7C,EAAoD;AAChD,iBAAKK,SAAL,CAAe;AAAA;AAAA,sCAASN,WAAT,CAAqB6B,KAApC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIwC,QAAAA,YAAY,CAACC,KAAD,EAAQ;AAChB,eAAKnG,aAAL,GAAqBmG,KAArB;;AACA,cAAI,KAAKnG,aAAL,IAAsB,CAAtB,IAA2B,KAAKd,KAAL,CAAWkH,YAAX,CAAwB,KAAKpG,aAA7B,CAA/B,EAA4E;AACxE,kBAAMqG,SAAS,GAAG;AAAA;AAAA,mDAAlB;AACAA,YAAAA,SAAS,CAACC,cAAV,GAA2B,KAAKpH,KAAL,CAAWoH,cAAtC;AACAD,YAAAA,SAAS,CAACE,SAAV,GAAsB,KAAKrH,KAAL,CAAWqH,SAAjC;AACAF,YAAAA,SAAS,CAACG,cAAV,GAA2B,KAAKtH,KAAL,CAAWsH,cAAX,CAA0B,KAAKxG,aAA/B,CAA3B;AACAqG,YAAAA,SAAS,CAACI,YAAV,GAAyBJ,SAAS,CAACG,cAAV,CAAyBE,MAAlD;;AACA,iBAAKtH,SAAL,CAAeuH,YAAf,CAA4BN,SAA5B;AACH,WAPD,MAOO;AACH,iBAAKjH,SAAL,CAAeuH,YAAf,CAA4B,IAA5B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI9C,QAAAA,YAAY,CAACrB,SAAD,EAAuB;AAC/B,cAAI,CAACA,SAAL,EAAgB;AACZ;AACH;;AAED,gBAAMoE,SAAS,GAAG;AAAA;AAAA,sCAAUC,eAA5B;;AAEA,kBAAQrE,SAAS,CAACsE,IAAlB;AACI,iBAAK,CAAL;AAAQ;AACJ,mBAAK3H,SAAL,CAAegF,YAAf,CAA4B,KAA5B;;AACA,mBAAKc,WAAL,GAAmB,KAAnB;AAEA,kBAAI8B,IAAI,GAAG,KAAKrG,IAAL,CAAUsG,cAAV,CAAyB,MAAzB,CAAX;;AACA,kBAAI,CAACD,IAAL,EAAW;AACPA,gBAAAA,IAAI,GAAG,IAAIpJ,IAAJ,EAAP;AACA,qBAAK+C,IAAL,CAAUuG,QAAV,CAAmBF,IAAnB;AACA;AAAA;AAAA,wCAAQ7F,YAAR,CAAqBgG,aAArB,CAAmCH,IAAI,CAACI,YAAL,CAAkBvJ,MAAlB,CAAnC,EAA8D,SAA9D;AACAmJ,gBAAAA,IAAI,CAACK,YAAL,CAAkBjJ,WAAlB,EAA+BkJ,OAA/B,GAAyC,CAAzC;AACH;;AACDN,cAAAA,IAAI,CAAC9C,MAAL,GAAc,IAAd;AACA8C,cAAAA,IAAI,CAACO,QAAL,CAAc,CAAd,EAAiB,GAAjB;AAEAxJ,cAAAA,KAAK,CAACiJ,IAAD,CAAL,CACKhD,KADL,CACW,IADX,EAEKwD,EAFL,CAEQ,IAAIX,SAFZ,EAEuB;AAAEY,gBAAAA,KAAK,EAAEpJ,EAAE,CAAC,CAAD,EAAI,GAAJ;AAAX,eAFvB,EAGKmJ,EAHL,CAGQ,IAAIX,SAHZ,EAGuB;AAAEY,gBAAAA,KAAK,EAAEpJ,EAAE,CAAC,CAAD,EAAI,GAAJ;AAAX,eAHvB,EAIKmJ,EAJL,CAIQX,SAJR,EAImB;AAAEY,gBAAAA,KAAK,EAAEpJ,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,eAJnB,EAKKqJ,IALL,CAKU,MAAM;AACRV,gBAAAA,IAAI,CAAC9C,MAAL,GAAc,KAAd;AACH,eAPL,EAQKyD,KARL;AAUA,mBAAKC,YAAL,CAAkB,MAAM;AACpB,qBAAKC,gBAAL,CAAsBpF,SAAtB,EAAiCoE,SAAjC;AACH,eAFD,EAEG,IAAIA,SAFP;AAIA9I,cAAAA,KAAK,CAAC,KAAK4C,IAAN,CAAL,CACK6G,EADL,CACQ,IAAIX,SADZ,EACuB;AACfiB,gBAAAA,QAAQ,EAAEzJ,EAAE,CACRoE,SAAS,CAACsF,IAAV,GAAiB,KAAK3I,SAAL,CAAe4I,YADxB,EAERvF,SAAS,CAACwF,IAAV,GAAiB,KAAK7I,SAAL,CAAe8I,YAFxB;AADG,eADvB,EAOKR,IAPL,CAOU,MAAM;AACR,qBAAKS,MAAL,CACI1F,SAAS,CAACsF,IAAV,GAAiB,KAAK3I,SAAL,CAAe4I,YADpC,EAEIvF,SAAS,CAACwF,IAAV,GAAiB,KAAK7I,SAAL,CAAe8I,YAFpC;AAIA,qBAAKhD,WAAL,GAAmB,IAAnB;AACH,eAbL,EAcKlB,KAdL,CAcW,GAdX,EAeK0D,IAfL,CAeU,MAAM;AACR,qBAAKtI,SAAL,CAAegJ,WAAf,GAA6B,IAA7B;;AACA,qBAAKhJ,SAAL,CAAegF,YAAf,CAA4B,IAA5B;AACH,eAlBL,EAmBKuD,KAnBL;AAoBA;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,mBAAKvI,SAAL,CAAegF,YAAf,CAA4B,KAA5B;;AACA,mBAAKc,WAAL,GAAmB,KAAnB;AACA,mBAAKiD,MAAL,CACI1F,SAAS,CAACsF,IAAV,GAAiB,KAAK3I,SAAL,CAAe4I,YADpC,EAEIvF,SAAS,CAACwF,IAAV,GAAiB,KAAK7I,SAAL,CAAe8I,YAFpC;;AAKA,oBAAMG,UAAU,GAAG,KAAKC,mBAAL,CAAyBzB,SAAzB,CAAnB;;AACA,mBAAKe,YAAL,CAAkB,MAAM,CACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACH,eARD,EAQG,IAAIf,SARP;AASA;;AAEJ,iBAAK,CAAL;AACA,iBAAK,CAAL;AAAQ;AACJ,kBAAI,KAAK3G,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,qBAAKd,SAAL,CAAegF,YAAf,CAA4B,KAA5B;;AACA,qBAAK/E,SAAL,CAAe6E,MAAf,GAAwB,KAAxB;AACA,qBAAKgB,WAAL,GAAmB,KAAnB,CAHsD,CAItD;AACA;AACA;AACA;AACH,eARD,MAQO;AACH,qBAAK7F,SAAL,CAAe6E,MAAf,GAAwB,KAAxB;;AACA,qBAAK9E,SAAL,CAAegF,YAAf,CAA4B,KAA5B,EAFG,CAGH;AACA;AACA;AACA;AACA;AACA;;AACH;;AACD;;AAEJ;AACI,kBAAI,KAAKlE,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,qBAAKb,SAAL,CAAe6E,MAAf,GAAwB,KAAxB;;AACA,qBAAK9E,SAAL,CAAegF,YAAf,CAA4B,KAA5B,EAFsD,CAGtD;AACA;AACA;AACA;AACA;AACA;;AACH;;AACD;AAxGR;;AA2GA,eAAKlE,aAAL,GAAqBuC,SAAS,CAACsE,IAA/B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYc,QAAAA,gBAAgB,CAACpF,SAAD,EAAiBoE,SAAjB,EAA0C;AAC9D,gBAAM0B,WAAW,GAAG,CAAC,OAAD,EAAU,OAAV,EAAmB,OAAnB,CAApB;AACA,gBAAMC,UAAU,GAAG,CAAC,SAAD,EAAY,SAAZ,EAAuB,SAAvB,CAAnB;AACA,gBAAMC,MAAM,GAAG,CAAC,GAAD,EAAM,IAAN,EAAY;AAAEC,YAAAA,MAAM,EAAE,GAAV;AAAeC,YAAAA,MAAM,EAAE;AAAvB,WAAZ,CAAf;AAEAJ,UAAAA,WAAW,CAACK,OAAZ,CAAoB,CAACC,IAAD,EAAOzC,KAAP,KAAiB;AACjC,gBAAIY,IAAI,GAAG,KAAKrG,IAAL,CAAUsG,cAAV,CAAyB4B,IAAzB,CAAX;;AACA,gBAAI,CAAC7B,IAAL,EAAW;AACPA,cAAAA,IAAI,GAAG,IAAIpJ,IAAJ,EAAP;AACAoJ,cAAAA,IAAI,CAAC6B,IAAL,GAAYA,IAAZ;AACA,mBAAKlI,IAAL,CAAUuG,QAAV,CAAmBF,IAAnB;AACA;AAAA;AAAA,sCAAQ7F,YAAR,CAAqBgG,aAArB,CAAmCH,IAAI,CAACI,YAAL,CAAkBvJ,MAAlB,CAAnC,EAA8D2K,UAAU,CAACpC,KAAD,CAAxE;AACH;;AACDY,YAAAA,IAAI,CAAC9C,MAAL,GAAc,IAAd,CARiC,CASjC;;AACA8C,YAAAA,IAAI,CAACO,QAAL,CAAc,CAAd,EAAiB,CAAjB;AAEA,gBAAIE,KAAK,GAAG,OAAOgB,MAAM,CAACrC,KAAD,CAAb,KAAyB,QAAzB,GAAoC/H,EAAE,CAACoK,MAAM,CAACrC,KAAD,CAAN,CAAcsC,MAAf,EAAuBD,MAAM,CAACrC,KAAD,CAAN,CAAcuC,MAArC,CAAtC,GAAqFtK,EAAE,CAACoK,MAAM,CAACrC,KAAD,CAAP,EAAgBqC,MAAM,CAACrC,KAAD,CAAtB,CAAnG;AACA,mBAAOqB,KAAP;AACA1J,YAAAA,KAAK,CAACiJ,IAAD,CAAL,CACKQ,EADL,CACQ,IAAIX,SADZ,EACuB;AAAEY,cAAAA,KAAK,EAAEA;AAAT,aADvB,EAEI;AAFJ,aAGKC,IAHL,CAGU,MAAM;AACRV,cAAAA,IAAI,CAAC9C,MAAL,GAAc,KAAd;AACH,aALL,EAMKyD,KANL;AAOH,WArBD;AAsBH;AAED;AACJ;AACA;AACA;AACA;;;AACYW,QAAAA,mBAAmB,CAACzB,SAAD,EAA0B;AACjD,cAAIwB,UAAU,GAAG,KAAK1H,IAAL,CAAUsG,cAAV,CAAyB,QAAzB,CAAjB;;AACA,cAAI,CAACoB,UAAL,EAAiB;AACbA,YAAAA,UAAU,GAAGnK,WAAW,CAAC;AAAA;AAAA,oCAAQ4K,cAAR,CAAuBC,SAAxB,CAAxB;AACAV,YAAAA,UAAU,CAACQ,IAAX,GAAkB,QAAlB;AACA,iBAAKlI,IAAL,CAAUuG,QAAV,CAAmBmB,UAAnB;AAEA,kBAAMU,SAAS,GAAGV,UAAU,CAAChB,YAAX;AAAA;AAAA,2CAAlB;AACA0B,YAAAA,SAAS,CAAChI,IAAV,CAAe;AAAA;AAAA,oCAAQI,YAAR,CAAqB6H,UAApC,EAAgD,KAAhD,EAAuD,EAAvD,EAA2DnC,SAA3D,EAAsE,MAAM;AACxEkC,cAAAA,SAAS,CAACE,IAAV;AACAZ,cAAAA,UAAU,CAACnE,MAAX,GAAoB,KAApB;AACH,aAHD;AAIH;;AAEDmE,UAAAA,UAAU,CAACnE,MAAX,GAAoB,IAApB;;AACA,cAAI,KAAKf,MAAL,CAAY+F,SAAZ,CAAsBvC,MAAtB,GAA+B,CAAnC,EAAsC;AAClC0B,YAAAA,UAAU,CAACd,QAAX,CAAoB,KAAKpE,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAApB,EAA8C,KAAK/F,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAA9C;AACAb,YAAAA,UAAU,CAACc,WAAX,CAAuB,KAAKhG,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAAvB,EAAiD,KAAK/F,MAAL,CAAY+F,SAAZ,CAAsB,CAAtB,CAAjD;AACH,WAlBgD,CAoBjD;;;AACAb,UAAAA,UAAU,CAAChB,YAAX;AAAA;AAAA,0CAAqCxF,KAArC;AAEA,iBAAOwG,UAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACIjG,QAAAA,SAAS,CAACgH,MAAD,EAAS;AACd,cAAI,KAAK9J,UAAL,KAAoB8J,MAAxB,EAAgC;AAC5B,iBAAK9J,UAAL,GAAkB8J,MAAlB,CAD4B,CAG5B;;AACA,iBAAK/J,SAAL,CAAegK,aAAf,CAA6B,KAA7B;;AACA,iBAAKjK,SAAL,CAAegF,YAAf,CAA4B,IAA5B;;AAEA,oBAAQ,KAAK9E,UAAb;AACI,mBAAK;AAAA;AAAA,wCAASwC,WAAT,CAAqBkD,KAA1B;AACI;AACA,qBAAKsE,IAAL,CAAU3I,IAAV,CAAe0G,YAAf,CAA4BlJ,SAA5B,EAAuCoL,OAAvC,GAAiD,CAAjD,CAFJ,CAGI;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASzH,WAAT,CAAqBC,KAA1B;AACI;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqBuB,SAA1B;AACI;AACA,qBAAKjE,SAAL,CAAegF,YAAf,CAA4B,KAA5B;;AACA,qBAAK/E,SAAL,CAAemK,SAAf;;AACA,qBAAKxJ,UAAL;AACA,qBAAKc,IAAL,CAAU2I,QAAV,CAAmB,WAAnB,EAAgC,MAAM;AAClC,uBAAK3I,IAAL,CAAU2I,QAAV,CAAmB,SAAS,KAAKzJ,UAAjC,EAA6C,IAA7C;AACA,uBAAKoC,SAAL,CAAe;AAAA;AAAA,4CAASN,WAAT,CAAqBC,KAApC;;AACA,uBAAK1C,SAAL,CAAeqK,kBAAf;AACH,iBAJD,MAIO,KAAKtH,SAAL,CAAe;AAAA;AAAA,0CAASN,WAAT,CAAqBC,KAApC,GAA4C,KAAK1C,SAAL,CAAeqK,kBAAf,EAJnD;AAKA;;AAEJ,mBAAK;AAAA;AAAA,wCAAS5H,WAAT,CAAqBO,aAA1B;AACI;AACA,qBAAKsH,WAAL;AACA,qBAAKvH,SAAL,CAAe;AAAA;AAAA,0CAASN,WAAT,CAAqBwD,SAApC;AACA,qBAAKxE,IAAL,CAAU8I,WAAV;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAAS9H,WAAT,CAAqBwD,SAA1B;AACI;AACA,qBAAKjG,SAAL,CAAewK,UAAf;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAAS/H,WAAT,CAAqBS,UAA1B;AACI;AACA,qBAAKzB,IAAL,CAAUgJ,UAAV;AACA,qBAAK1H,SAAL,CAAe;AAAA;AAAA,0CAASN,WAAT,CAAqBC,KAApC;AACA;;AAEJ;AACI;AA1CR;AA4CH;AACJ;AAED;AACJ;AACA;AACA;;;AACIkD,QAAAA,aAAa,CAACV,SAAD,EAAY;AACrB,cAAI,KAAKpF,KAAL,CAAWsG,QAAX,IAAuB,KAAK/F,UAAL,KAAoB,IAA3C,IAAmD,KAAKA,UAAL,KAAoB,KAAKD,SAAhF,EAA2F;AACvF,kBAAMsK,SAAS,GAAG,KAAKrK,UAAL,GAAkB,KAAKD,SAAvB,GAAmC,CAAnC,GAAuC,CAAC,CAA1D;AACA,kBAAMuK,WAAW,GAAGzF,SAAS,GAAG,KAAKzE,YAAjB,GAAgCiK,SAApD;AACA,gBAAIE,QAAQ,GAAG,KAAKxK,SAAL,GAAiBuK,WAAhC;;AAEA,gBAAID,SAAS,GAAG,CAAhB,EAAmB;AACf,kBAAIE,QAAQ,GAAG,KAAKvK,UAApB,EAAgCuK,QAAQ,GAAG,KAAKvK,UAAhB;AACnC,aAFD,MAEO;AACH,kBAAIuK,QAAQ,GAAG,KAAKvK,UAApB,EAAgCuK,QAAQ,GAAG,KAAKvK,UAAhB;AACnC;;AAED,kBAAMwK,UAAU,GAAGlM,EAAE,CAAC,KAAK2B,OAAL,CAAawK,CAAd,EAAiB,KAAKxK,OAAL,CAAayK,CAA9B,CAAF,CAAmCC,MAAnC,CAA0CpM,IAAI,CAACqM,gBAAL,CAAsBN,WAAtB,CAA1C,CAAnB;AACA,iBAAKxJ,GAAL,GAAW0J,UAAX,CAZuF,CAYhE;;AACvB,iBAAKxI,QAAL,CAAcuI,QAAd;AACH;AACJ;AAED;AACJ;AACA;;;AACI3I,QAAAA,gBAAgB,GAAG;AACf,eAAKiJ,KAAL,GAAa,KAAKpL,KAAL,CAAWqL,EAAxB;AACA,eAAKC,KAAL,GAAa,KAAKtL,KAAL,CAAWqL,EAAxB;AACA,eAAKE,OAAL,GAAe,KAAKvL,KAAL,CAAWuL,OAA1B;AACA,eAAKC,MAAL,GAAc,KAAKxL,KAAL,CAAWwL,MAAzB;AACA,eAAKC,YAAL,GAAoB,KAAKzL,KAAL,CAAWyL,YAA/B;AACA,eAAKC,UAAL,GAAkB,KAAK1L,KAAL,CAAW2L,aAA7B;AACA,eAAKC,YAAL,GAAoB,KAAK5L,KAAL,CAAW4L,YAA/B;AACH;AAED;AACJ;AACA;AACA;;;AACoB,cAAVxJ,UAAU,CAACyJ,SAAS,GAAG,KAAb,EAAoB;AAChC,gBAAM,KAAKlK,IAAL,CAAUC,IAAV,CAAe,KAAKoC,MAApB,EAA4B,IAA5B,EAAkC,KAAKhE,KAAL,CAAW8L,KAA7C,CAAN;;AAEA,cAAI,CAACD,SAAL,EAAgB;AACZ,gBAAI;AACA,mBAAK1B,IAAL,CAAU3I,IAAV,CAAe0G,YAAf,CAA4BlJ,SAA5B,EAAuCoL,OAAvC,GAAiD,GAAjD;AACA,mBAAKD,IAAL,CAAU3I,IAAV,CAAewI,WAAf,CAA2B,KAAKhG,MAAL,CAAY+H,OAAZ,CAAoB,CAApB,CAA3B,EAAmD,KAAK/H,MAAL,CAAY+H,OAAZ,CAAoB,CAApB,CAAnD;AACA;AAAA;AAAA,sCAAQ/J,YAAR,CAAqBgG,aAArB,CAAmC,KAAKmC,IAAxC,EAA+C,KAAI,KAAKnG,MAAL,CAAY+H,OAAZ,CAAoB,CAApB,CAAuB,IAA1E;AACA;AAAA;AAAA,sCAAQ/J,YAAR,CAAqBgG,aAArB,CAAmC,KAAKgE,KAAxC,EAAgD,KAAI,KAAKhI,MAAL,CAAY+H,OAAZ,CAAoB,CAApB,CAAuB,IAA3E;AACA;AAAA;AAAA,sCAAQ/J,YAAR,CAAqBgG,aAArB,CAAmC,KAAKiE,OAAxC,EAAkD,KAAI,KAAKjI,MAAL,CAAY+H,OAAZ,CAAoB,CAApB,CAAuB,IAA7E;AACH,aAND,CAME,OAAOG,KAAP,EAAc,CACZ;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACI7J,QAAAA,iBAAiB,GAAG;AAChB,cAAIiG,KAAK,GAAG,KAAK3G,IAAL,CAAUH,IAAV,CAAe8G,KAAf,CAAqB0C,CAAjC;AACA,gBAAMmB,YAAY,GAAG,KAAKnI,MAAL,CAAYoI,QAAjC;AACA,eAAKC,cAAL,CAAoB,CAChBF,YAAY,CAAC,CAAD,CADI,EAEhBA,YAAY,CAAC,CAAD,CAAZ,GAAiB7D,KAFD,EAGhB6D,YAAY,CAAC,CAAD,CAAZ,GAAiB7D,KAHD,EAIhB6D,YAAY,CAAC,CAAD,CAAZ,GAAiB7D,KAJD,EAKhB6D,YAAY,CAAC,CAAD,CAAZ,GAAiB7D,KALD,CAApB;AAOH;AACD;AACJ;AACA;;;AACItD,QAAAA,aAAa,GAAG;AACZ,gBAAMsH,OAAO,GAAG,KAAKlB,KAAL,GAAa,KAAKE,KAAlC;AACA,gBAAMiB,YAAY,GAAGD,OAAO,GAAG,KAAKN,KAAL,CAAWQ,SAA1C,CAFY,CAIZ;;AACA,eAAKR,KAAL,CAAWQ,SAAX,GAAuBF,OAAvB,CALY,CAOZ;;AACA,cAAI,KAAKpL,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB4I,IAAnB;;AACA,iBAAK5I,aAAL,GAAqB,IAArB;AACH,WAXW,CAaZ;;;AACA,cAAIqL,YAAJ,EAAkB;AACd,kBAAME,QAAQ,GAAG5F,IAAI,CAACC,GAAL,CAAS,KAAKmF,OAAL,CAAaO,SAAb,GAAyB,KAAKR,KAAL,CAAWQ,SAA7C,CAAjB;AACA,iBAAKtL,aAAL,GAAqBtC,KAAK,CAAC,KAAKqN,OAAN,CAAL,CAChB5D,EADgB,CACboE,QADa,EACH;AAAED,cAAAA,SAAS,EAAE,KAAKR,KAAL,CAAWQ;AAAxB,aADG,EAEhBjE,IAFgB,CAEX,MAAM;AACR,mBAAKrH,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhBsH,KALgB,EAArB;AAMH,WARD,MAQO;AACH,iBAAKyD,OAAL,CAAaO,SAAb,GAAyBF,OAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,MAAM,GAAG;AACL,eAAK1H,aAAL;;AACA,eAAK2H,OAAL;;AACA,cAAI,CAAC,KAAKtH,MAAV,EAAkB,CACd;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIsH,QAAAA,OAAO,GAAG;AACN,cAAI,MAAMA,OAAN,EAAJ,EAAqB;AACjB,mBAAO,IAAP;AACH;;AAED,iBAAO,KAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,QAAD,EAAW;AACnB,gBAAMD,YAAN,CAAmBC,QAAnB,EADmB,CAEnB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACD,QAAD,EAAW;AAChB,gBAAMC,SAAN,CAAgBD,QAAhB,EADgB,CAEhB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,KAAK,CAACC,WAAD,EAAc;AACf,gBAAMD,KAAN,CAAYC,WAAZ;AACA,eAAK7C,IAAL,CAAU3I,IAAV,CAAe0G,YAAf,CAA4BlJ,SAA5B,EAAuCoL,OAAvC,GAAiD,CAAjD;AACA,eAAK6C,UAAL;;AAEA,kBAAQD,WAAR;AACI,iBAAK;AAAA;AAAA,sCAAS3I,gBAAT,CAA0B6I,GAA/B;AACI,mBAAKC,WAAL;;AACA,mBAAKC,cAAL;;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAAS/I,gBAAT,CAA0BG,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASH,gBAAT,CAA0BC,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0B+B,QAA/B;AACI,mBAAKpF,WAAL,GAAmB,IAAnB;AACA;AAVR;AAYH;AAED;AACJ;AACA;;;AACIiM,QAAAA,UAAU,GAAG;AACT;AACA;AACA;AAEA,cAAI,KAAK/L,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB4I,IAAnB;;AACA,iBAAK5I,aAAL,GAAqB,IAArB;AACH;;AAED,eAAK+K,OAAL,CAAaO,SAAb,GAAyB,CAAzB;AACH;AAED;AACJ;AACA;;;AACIY,QAAAA,cAAc,GAAG,CACb;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACI5C,QAAAA,WAAW,GAAG;AACV,eAAK7I,IAAL,CAAU2I,QAAV,CAAoB,MAAK,KAAKzJ,UAAW,EAAzC,EAA4C,MAAM;AAC9C,iBAAKc,IAAL,CAAU2I,QAAV,CAAoB,OAAM,KAAKzJ,UAAW,EAA1C,EAA6C,IAA7C;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACIsM,QAAAA,WAAW,GAAG;AACV,gBAAMA,WAAN;AAEA,eAAK1E,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,gCAAM4E,iBAAN,CAAwB,KAAK7L,IAA7B,EAAmC,MAAnC;AACA,iBAAK8L,SAAL,GAFoB,CAGpB;AACH,WAJD,EAIG,GAJH;AAKH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAG;AACX,eAAKvM,WAAL,GAAmB,IAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAAL0F,KAAK,GAAG;AACR,iBAAO,KAAK/E,IAAL,GAAY,KAAKA,IAAL,CAAUH,IAAV,CAAekF,KAA3B,GAAmC,CAA1C;AACH;AAED;AACJ;AACA;AACA;;;AACa,YAALA,KAAK,CAAC8G,KAAD,EAAQ;AACb,cAAI,KAAK7L,IAAT,EAAe;AACX,iBAAKA,IAAL,CAAUH,IAAV,CAAekF,KAAf,GAAuB8G,KAAvB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACrD,OAAD,EAAU,CACpB;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIsD,QAAAA,kBAAkB,GAAG;AACjB;AACA;AACA;AACA,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,aAAa,CAACvI,SAAD,EAAY;AACrB,cAAI,KAAKwI,UAAL,KAAoB,IAAxB,EAA8B;AAC1B,gBAAI;AAAA;AAAA,gCAAMC,gBAAN,CAAuBhP,EAAE,CAAC,KAAK2C,IAAL,CAAUmH,QAAV,CAAmBqC,CAApB,EAAuB,KAAKxJ,IAAL,CAAUmH,QAAV,CAAmBsC,CAA1C,CAAzB,CAAJ,EAA4E;AACxE,mBAAK7J,SAAL,IAAkBgE,SAAlB;;AACA,kBAAI,KAAKhE,SAAL,GAAiB,EAArB,EAAyB;AACrB,qBAAKiE,MAAL,GAAc,IAAd;AACA,qBAAKgB,UAAL,GAAkB,IAAlB;AACH;AACJ,aAND,MAMO;AACH,mBAAKjF,SAAL,GAAiB,CAAjB;AACH;AACJ;AACJ;;AA12B6C,O,UAoCvC0M,U,GAAa,Y", "sourcesContent": ["import { _decorator, Component, Node, Sprite, Vec2, ParticleSystem, tween, v2, misc, instantiate, UIOpacity, ParticleSystem2D, UITransform, v3 } from 'cc';\r\nimport EnemyBase from './EnemyBase';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport EnemyShootComponent from './EnemyShootComponent';\r\nimport { EnemyData, EnemyPlaneData, EnemyShootData, EnemyUIData } from '../../../data/EnemyData';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport EnemyPlaneRole from './EnemyPlaneRole';\r\nimport PfFrameAnim from '../../base/PfFrameAnim';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlane')\r\nexport default class EnemyPlane extends EnemyBase {\r\n    @property(EnemyPlaneRole)\r\n    role: EnemyPlaneRole;\r\n\r\n    @property(Sprite)\r\n    hpBg: Sprite;\r\n    @property(Sprite)\r\n    hpSpr: Sprite;\r\n    @property(Sprite)\r\n    hpWhite: Sprite;\r\n\r\n    _data: EnemyPlaneData = null;\r\n    _trackCom: TrackComponent = null;\r\n    _shootCom: EnemyShootComponent = null;\r\n    _curAction: number = 0;\r\n    _removeCount: number = 0;\r\n    _removeTime: number = 0;\r\n\r\n\r\n    _curAngle: number = 0;\r\n    _destAngle: number = null;\r\n    _curDir: Vec2 = Vec2.ZERO;\r\n    _destDir: Vec2 = Vec2.ZERO;\r\n    _rotateSpeed: number = 0;\r\n    _leaveAct: number = -1;\r\n    _roleIndex: number = 1;\r\n    _curFormIndex: number = 0;\r\n    _curTrackType: number = -1;\r\n    _dieAnimEnd: boolean = false;\r\n\r\n\r\n    _initAngle: boolean = true;\r\n    _hpWhiteTween: any = null;\r\n    _bDieShoot: boolean = false;\r\n    m_outTime: number = 0;\r\n\r\n    static PrefabName = 'EnemyPlane';\r\n    dir: any;\r\n\r\n    preLoad() {\r\n        super.preLoad();\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._shootCom = Tools.addScript(this.node, EnemyShootComponent);\r\n    }\r\n\r\n    preLoadUI(data: EnemyUIData) {\r\n        this.role.preLoadUI(data);\r\n    }\r\n\r\n    async init(data: EnemyPlaneData) {\r\n        this._reset();\r\n        this._data = data;\r\n        this.dieBullet = data.dieBullet;\r\n        this.setUIData(GameIns.enemyManager.getEnemyUIData(this._data.uiId));\r\n        this._refreshProperty();\r\n        await this._refreshUI();\r\n        this._refreshColliders();\r\n        this.setDir(v2(0, -1));\r\n        this.setAngle(0);\r\n        this.initAttr(data.attr);\r\n\r\n        // if (this.hasAttribution(GameEnum.EnemyAttr.Shield)) {\r\n        //     this.role.setEventCallback('shield', () => {\r\n        //         this.showAttrShield();\r\n        //     });\r\n        // } else {\r\n        //     this.role.setEventCallback('shield', null);\r\n        // }\r\n    }\r\n\r\n    _reset() {\r\n        super.reset();\r\n        this._curAction = GameEnum.EnemyAction.Track;\r\n        this._removeTime = 0;\r\n        this._removeCount = 0;\r\n        this._dieAnimEnd = false;\r\n        this._bDieShoot = false;\r\n        this._curFormIndex = 0;\r\n        this._curTrackType = -1;\r\n        this._curAngle = 0;\r\n        this._destAngle = null;\r\n        this._curDir = Vec2.ZERO;\r\n        this._destDir = Vec2.ZERO;\r\n        this._rotateSpeed = 0;\r\n        this._leaveAct = -1;\r\n        this._initAngle = true;\r\n        this.m_outTime = 0;\r\n    }\r\n\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    initComps(): void {\r\n        // 调用父类的组件初始化方法\r\n        super.initComps();\r\n\r\n        // 判断是否为特殊敌机（ID范围在50000到60000之间）\r\n        const isSpecialEnemy = this._data.id >= 50000 && this._data.id < 60000;\r\n\r\n        // 初始化射击组件\r\n        this._shootCom.init(this, isSpecialEnemy ? this.role.role.node : this.node, null, isSpecialEnemy);\r\n\r\n        // 设置攻击开始的回调\r\n        this._shootCom.setAtkStartCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackPrepare);\r\n        });\r\n\r\n        // 设置攻击结束的回调\r\n        this._shootCom.setAtkOverCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackOver);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化轨迹\r\n     * @param {TrackData} trackData 轨迹数据\r\n     * @param {number} offsetX X 轴偏移\r\n     * @param {number} offsetY Y 轴偏移\r\n     * @param {boolean} isLoop 是否循环\r\n     * @param {number} rotateSpeed 旋转速度\r\n     */\r\n    initTrack(trackData: TrackGroup[], trackParams: number[], offsetX: number, offsetY: number, rotateSpeed = 0) {\r\n        this._rotateSpeed = rotateSpeed;\r\n        this._trackCom.init(this, trackData, trackParams, offsetX, offsetY);\r\n\r\n        // 设置轨迹的各种回调\r\n        this._trackCom.setTrackGroupStartCall((track, groupType, groupIndex) => {\r\n            \r\n        });\r\n\r\n        this._trackCom.setTrackGroupOverCall((groupType) => {\r\n            if (this.uiData.isAm && groupType === 0) {\r\n                this.setAction(GameEnum.EnemyAction.Transform);\r\n            }\r\n        });\r\n\r\n        this._trackCom.setTrackOverCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.TrackOver);\r\n        });\r\n\r\n        this._trackCom.setTrackLeaveCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.Leave);\r\n        });\r\n\r\n        this._trackCom.setTrackStartCall((trackType) => {\r\n            this.setTrackType(trackType);\r\n        });\r\n    }\r\n\r\n    /**\r\n * 设置首次射击的延迟时间\r\n * @param {number} delay 延迟时间\r\n */\r\n    setFirstShootDelay(delay) {\r\n        this._shootCom.setFirstShootDelay(delay);\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        super.startBattle();\r\n        this.active = true;\r\n        this._refreshHpBar();\r\n        this._trackCom.setTrackAble(true);\r\n        this._trackCom.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        super.updateGameLogic(deltaTime);\r\n\r\n        if (this.isDead) {\r\n            if (this._bDieShoot) {\r\n                this._shootCom.updateGameLogic(deltaTime);\r\n            } else {\r\n                this._checkRemoveAble(deltaTime);\r\n            }\r\n        } else if (!this.isStandBy()) {\r\n            this.role.updateGameLogic(deltaTime);\r\n            if (!this.hasHurtBuff(GameEnum.EnemyBuff.Ice)) {\r\n                this._trackCom.updateGameLogic(deltaTime);\r\n                this._shootCom.updateGameLogic(deltaTime);\r\n                this._updateAction(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新当前行为\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _updateAction(deltaTime) {\r\n        this._shootCom.setNextAble(false);\r\n\r\n        switch (this._curAction) {\r\n            case GameEnum.EnemyAction.Sneak:\r\n                this._updateCurDir(deltaTime);\r\n                this.collideAble = false;\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Track:\r\n                this._updateCurDir(deltaTime);\r\n                this._shootCom.setNextAble(\r\n                    (this._trackCom.isMoving && this._data.bMoveAttack) ||\r\n                    (!this._trackCom.isMoving && this._data.bStayAttack)\r\n                );\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Transform:\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.AttackPrepare:\r\n            case GameEnum.EnemyAction.AttackIng:\r\n                this._updateCurDir(deltaTime);\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Leave:\r\n                if (this._leaveAct === 0) {\r\n                    this.die(GameEnum.EnemyDestroyType.TimeOver);\r\n                } else if (this._leaveAct > 0) {\r\n                    this._updateCurDir(deltaTime);\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以移除\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _checkRemoveAble(deltaTime) {\r\n        if (this._dieAnimEnd) {\r\n            this._removeCount += deltaTime;\r\n            if (this._removeCount > this._removeTime) {\r\n                this.removeAble = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置方向\r\n     * @param {Vec2} dir 方向向量\r\n     */\r\n    setDir(dir) {\r\n        this.dir = dir;\r\n        if (this._data.bTurnDir) {\r\n            this.setAngle(-Tools.getDegreeForDir(dir));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取当前方向\r\n     * @returns {Vec2} 当前方向向量\r\n     */\r\n    getDir() {\r\n        return this._curDir;\r\n    }\r\n\r\n    /**\r\n     * 设置目标方向\r\n     * @param {Vec2} dir 目标方向向量\r\n     */\r\n    setDestDir(dir) {\r\n        this._destDir = dir;\r\n        if (this._initAngle) {\r\n            this._initAngle = false;\r\n            this.setDir(dir);\r\n        } else if (this._rotateSpeed <= 0) {\r\n            this.setDir(dir);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置当前角度\r\n     * @param {number} angle 当前角度\r\n     */\r\n    setAngle(angle) {\r\n        if (angle > 360) {\r\n            angle -= 360;\r\n        } else if (angle < -360) {\r\n            angle += 360;\r\n        }\r\n        this._curAngle = angle;\r\n        this.role.node.angle = angle;\r\n    }\r\n\r\n    /**\r\n     * 设置目标角度\r\n     * @param {number} angle 目标角度\r\n     */\r\n    setDestAngle(angle) {\r\n        if (this._rotateSpeed > 0) {\r\n            let delta = angle - this._curAngle;\r\n            while (Math.abs(delta) > 360) {\r\n                angle += angle > this._curAngle ? -360 : 360;\r\n                delta = angle - this._curAngle;\r\n            }\r\n            if (Math.abs(delta) > 180) {\r\n                angle += angle > this._curAngle ? -360 : 360;\r\n            }\r\n            this._destAngle = angle;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以存活\r\n     * @returns {boolean} 是否可以存活\r\n     */\r\n    checkLiveAble() {\r\n        if (this._curAction === GameEnum.EnemyAction.Track) {\r\n            this.setAction(GameEnum.EnemyAction.Leave);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param {number} index 形态索引\r\n     */\r\n    setFormIndex(index) {\r\n        this._curFormIndex = index;\r\n        if (this._curFormIndex >= 0 && this._data.bAttackAbles[this._curFormIndex]) {\r\n            const shootData = new EnemyShootData();\r\n            shootData.attackInterval = this._data.attackInterval;\r\n            shootData.attackNum = this._data.attackNum;\r\n            shootData.attackPointArr = this._data.attackPointArr[this._curFormIndex];\r\n            shootData.attackArrNum = shootData.attackPointArr.length;\r\n            this._shootCom.setShootData(shootData);\r\n        } else {\r\n            this._shootCom.setShootData(null);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置敌机的轨迹类型\r\n     * @param trackData 轨迹数据\r\n     */\r\n    setTrackType(trackData: any): void {\r\n        if (!trackData) {\r\n            return;\r\n        }\r\n\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        switch (trackData.type) {\r\n            case 2: // 渐变出现\r\n                this._trackCom.setTrackAble(false);\r\n                this.collideAble = false;\r\n\r\n                let tail = this.node.getChildByName(\"tail\");\r\n                if (!tail) {\r\n                    tail = new Node();\r\n                    this.node.addChild(tail);\r\n                    GameIns.enemyManager.setPlaneFrame(tail.addComponent(Sprite), \"ashow_0\");\r\n                    tail.getComponent(UITransform).anchorY = 0;\r\n                }\r\n                tail.active = true;\r\n                tail.setScale(1, 2.2);\r\n\r\n                tween(tail)\r\n                    .delay(0.02)\r\n                    .to(5 * frameTime, { scale: v3(1, 2.8) })\r\n                    .to(2 * frameTime, { scale: v3(1, 0.5) })\r\n                    .to(frameTime, { scale: v3(1, 0) })\r\n                    .call(() => {\r\n                        tail.active = false;\r\n                    })\r\n                    .start();\r\n\r\n                this.scheduleOnce(() => {\r\n                    this._playTailEffects(trackData, frameTime);\r\n                }, 7 * frameTime);\r\n\r\n                tween(this.node)\r\n                    .to(7 * frameTime, {\r\n                        position: v3(\r\n                            trackData.endX + this._trackCom.trackOffsetX,\r\n                            trackData.endY + this._trackCom.trackOffsetY\r\n                        ),\r\n                    })\r\n                    .call(() => {\r\n                        this.setPos(\r\n                            trackData.endX + this._trackCom.trackOffsetX,\r\n                            trackData.endY + this._trackCom.trackOffsetY\r\n                        );\r\n                        this.collideAble = true;\r\n                    })\r\n                    .delay(0.2)\r\n                    .call(() => {\r\n                        this._trackCom.trackFinish = true;\r\n                        this._trackCom.setTrackAble(true);\r\n                    })\r\n                    .start();\r\n                break;\r\n\r\n            case 3: // 瞬间出现\r\n                this._trackCom.setTrackAble(false);\r\n                this.collideAble = false;\r\n                this.setPos(\r\n                    trackData.endX + this._trackCom.trackOffsetX,\r\n                    trackData.endY + this._trackCom.trackOffsetY\r\n                );\r\n\r\n                const appearNode = this._createAppearEffect(frameTime);\r\n                this.scheduleOnce(() => {\r\n                    // this.role.opacity = 255;\r\n                    // this.role.blueShow(() => {\r\n                    //     this._trackCom.trackFinish = true;\r\n                    //     this.collideAble = true;\r\n                    //     this._trackCom.setTrackAble(true);\r\n                    //     // this.hpBg.node.opacity = this._data.hpParam === 1 ? 255 : 0;\r\n                    // });\r\n                }, 2 * frameTime);\r\n                break;\r\n\r\n            case 4:\r\n            case 5: // 隐身或显现\r\n                if (this._curTrackType !== 4 && this._curTrackType !== 5) {\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._shootCom.active = false;\r\n                    this.collideAble = false;\r\n                    // this.hpBg.node.opacity = 0;\r\n                    // this.role.playCloakeHideAnim(() => {\r\n                    //     this._trackCom.setTrackAble(true);\r\n                    // });\r\n                } else {\r\n                    this._shootCom.active = false;\r\n                    this._trackCom.setTrackAble(false);\r\n                    // this.role.playCloakeShowAnim(() => {\r\n                    //     this._shootCom.active = true;\r\n                    //     this._trackCom.setTrackAble(true);\r\n                    //     this.collideAble = true;\r\n                    //     // this.hpBg.node.opacity = 255;\r\n                    // });\r\n                }\r\n                break;\r\n\r\n            default:\r\n                if (this._curTrackType === 4 || this._curTrackType === 5) {\r\n                    this._shootCom.active = false;\r\n                    this._trackCom.setTrackAble(false);\r\n                    // this.role.playCloakeShowAnim(() => {\r\n                    //     this._shootCom.active = true;\r\n                    //     this._trackCom.setTrackAble(true);\r\n                    //     this.collideAble = true;\r\n                    //     // this.hpBg.node.opacity = 255;\r\n                    // });\r\n                }\r\n                break;\r\n        }\r\n\r\n        this._curTrackType = trackData.type;\r\n    }\r\n\r\n    /**\r\n     * 播放尾部特效\r\n     * @param trackData 轨迹数据\r\n     * @param frameTime 每帧时间\r\n     */\r\n    private _playTailEffects(trackData: any, frameTime: number): void {\r\n        const tailEffects = [\"tail2\", \"tail1\", \"tail0\"];\r\n        const tailFrames = [\"ashow_1\", \"ashow_2\", \"ashow_3\"];\r\n        const scales = [1.8, 1.55, { scaleX: 2.2, scaleY: 0.5 }];\r\n\r\n        tailEffects.forEach((name, index) => {\r\n            let tail = this.node.getChildByName(name);\r\n            if (!tail) {\r\n                tail = new Node();\r\n                tail.name = name;\r\n                this.node.addChild(tail);\r\n                GameIns.enemyManager.setPlaneFrame(tail.addComponent(Sprite), tailFrames[index]);\r\n            }\r\n            tail.active = true;\r\n            // tail.opacity = 255;\r\n            tail.setScale(1, 1);\r\n\r\n            let scale = typeof scales[index] === \"object\" ? v3(scales[index].scaleX, scales[index].scaleY) : v3(scales[index], scales[index]);\r\n            typeof scale\r\n            tween(tail)\r\n                .to(5 * frameTime, { scale: scale })\r\n                // .to(5 * frameTime, { opacity: 0 })\r\n                .call(() => {\r\n                    tail.active = false;\r\n                })\r\n                .start();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 创建瞬间出现的特效\r\n     * @param frameTime 每帧时间\r\n     * @returns 创建的特效节点\r\n     */\r\n    private _createAppearEffect(frameTime: number): Node {\r\n        let appearNode = this.node.getChildByName(\"appear\");\r\n        if (!appearNode) {\r\n            appearNode = instantiate(GameIns.gameResManager.frameAnim);\r\n            appearNode.name = \"appear\";\r\n            this.node.addChild(appearNode);\r\n\r\n            const frameAnim = appearNode.getComponent(PfFrameAnim);\r\n            frameAnim.init(GameIns.enemyManager.enemyAtlas, \"as_\", 13, frameTime, () => {\r\n                frameAnim.stop();\r\n                appearNode.active = false;\r\n            });\r\n        }\r\n\r\n        appearNode.active = true;\r\n        if (this.uiData.showParam.length > 3) {\r\n            appearNode.setScale(this.uiData.showParam[0], this.uiData.showParam[1]);\r\n            appearNode.setPosition(this.uiData.showParam[2], this.uiData.showParam[3]);\r\n        }\r\n\r\n        // BattleManager.audioManager.playEffect(\"e_appear_3\");\r\n        appearNode.getComponent(PfFrameAnim).reset();\r\n\r\n        return appearNode;\r\n    }\r\n\r\n\r\n    /**\r\n     * 设置敌机的当前行为\r\n     * @param {number} action 行为类型（枚举值）\r\n     */\r\n    setAction(action) {\r\n        if (this._curAction !== action) {\r\n            this._curAction = action;\r\n\r\n            // 停止射击并启用轨迹\r\n            this._shootCom.setIsShooting(false);\r\n            this._trackCom.setTrackAble(true);\r\n\r\n            switch (this._curAction) {\r\n                case GameEnum.EnemyAction.Sneak:\r\n                    // 潜行行为\r\n                    this.hpBg.node.getComponent(UIOpacity).opacity = 0;\r\n                    // this.role.playSneakAnim();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Track:\r\n                    // 跟踪行为\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Transform:\r\n                    // 变形行为\r\n                    this._trackCom.setTrackAble(false);\r\n                    this._shootCom.stopShoot();\r\n                    this._roleIndex++;\r\n                    this.role.playAnim(\"transform\", () => {\r\n                        this.role.playAnim(\"idle\" + this._roleIndex, null);\r\n                        this.setAction(GameEnum.EnemyAction.Track);\r\n                        this._shootCom.setNextShootAtOnce();\r\n                    }) || (this.setAction(GameEnum.EnemyAction.Track), this._shootCom.setNextShootAtOnce());\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackPrepare:\r\n                    // 准备攻击行为\r\n                    this.playAtkAnim();\r\n                    this.setAction(GameEnum.EnemyAction.AttackIng);\r\n                    this.role.startAttack();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackIng:\r\n                    // 攻击中行为\r\n                    this._shootCom.startShoot();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackOver:\r\n                    // 攻击结束行为\r\n                    this.role.attackOver();\r\n                    this.setAction(GameEnum.EnemyAction.Track);\r\n                    break;\r\n\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新当前方向\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _updateCurDir(deltaTime) {\r\n        if (this._data.bTurnDir && this._destAngle !== null && this._destAngle !== this._curAngle) {\r\n            const direction = this._destAngle > this._curAngle ? 1 : -1;\r\n            const angleChange = deltaTime * this._rotateSpeed * direction;\r\n            let newAngle = this._curAngle + angleChange;\r\n\r\n            if (direction > 0) {\r\n                if (newAngle > this._destAngle) newAngle = this._destAngle;\r\n            } else {\r\n                if (newAngle < this._destAngle) newAngle = this._destAngle;\r\n            }\r\n\r\n            const rotatedDir = v2(this._curDir.x, this._curDir.y).rotate(misc.degreesToRadians(angleChange));\r\n            this.dir = rotatedDir; // 使用旋转后的新向量\r\n            this.setAngle(newAngle);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的属性\r\n     */\r\n    _refreshProperty() {\r\n        this.curHp = this._data.hp;\r\n        this.maxHp = this._data.hp;\r\n        this.defence = this._data.defence;\r\n        this.attack = this._data.attack;\r\n        this.collideLevel = this._data.collideLevel;\r\n        this.collideAtk = this._data.collideAttack;\r\n        this.bCollideDead = this._data.bCollideDead;\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的 UI\r\n     * @param {boolean} isRespawn 是否为重生\r\n     */\r\n    async _refreshUI(isRespawn = false) {\r\n        await this.role.init(this.uiData, this, this._data.param);\r\n\r\n        if (!isRespawn) {\r\n            try {\r\n                this.hpBg.node.getComponent(UIOpacity).opacity = 255;\r\n                this.hpBg.node.setPosition(this.uiData.hpParam[0], this.uiData.hpParam[1]);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpBg, `hp${this.uiData.hpParam[2]}_0`);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpSpr, `hp${this.uiData.hpParam[2]}_1`);\r\n                GameIns.enemyManager.setPlaneFrame(this.hpWhite, `hp${this.uiData.hpParam[2]}_2`);\r\n            } catch (error) {\r\n                // this.hpBg.node.y = this.icon.node.height >> 1;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新敌机的碰撞器\r\n     */\r\n    _refreshColliders() {\r\n        let scale = this.role.node.scale.x;\r\n        const colliderData = this.uiData.collider;\r\n        this.setCollideData([\r\n            colliderData[0],\r\n            colliderData[1] *scale,\r\n            colliderData[2] *scale,\r\n            colliderData[3] *scale,\r\n            colliderData[4] *scale,\r\n        ]);\r\n    }\r\n    /**\r\n * 刷新血条\r\n */\r\n    _refreshHpBar() {\r\n        const hpRatio = this.curHp / this.maxHp;\r\n        const isDecreasing = hpRatio < this.hpSpr.fillRange;\r\n\r\n        // 更新血条显示\r\n        this.hpSpr.fillRange = hpRatio;\r\n\r\n        // 停止之前的血条动画\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        // 如果血量减少，播放白色血条的动画\r\n        if (isDecreasing) {\r\n            const duration = Math.abs(this.hpWhite.fillRange - this.hpSpr.fillRange);\r\n            this._hpWhiteTween = tween(this.hpWhite)\r\n                .to(duration, { fillRange: this.hpSpr.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        } else {\r\n            this.hpWhite.fillRange = hpRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 受到伤害时的处理\r\n     */\r\n    onHurt() {\r\n        this._refreshHpBar();\r\n        this.checkHp();\r\n        if (!this.isDead) {\r\n            // this.role.winkWhite();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查血量\r\n     * @returns {boolean} 是否死亡\r\n     */\r\n    checkHp() {\r\n        if (super.checkHp()) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 移除 Buff 时的处理\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onRemoveBuff(buffType) {\r\n        super.onRemoveBuff(buffType);\r\n        // if (buffType === GameEnum.EnemyBuff.Ice) {\r\n        //     this.role.resumeAnim();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 添加 Buff 时的处理\r\n     * @param {number} buffType Buff 类型\r\n     */\r\n    onAddBuff(buffType) {\r\n        super.onAddBuff(buffType);\r\n        // if (buffType === GameEnum.EnemyBuff.Ice) {\r\n        //     this.role.pauseAnim();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 敌机死亡时的处理\r\n     * @param {number} destroyType 销毁类型\r\n     */\r\n    onDie(destroyType) {\r\n        super.onDie(destroyType);\r\n        this.hpBg.node.getComponent(UIOpacity).opacity = 0;\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                this.playDieAnim();\r\n                this._checkDieShoot();\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                this._dieAnimEnd = true;\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n        // if (this.role) {\r\n        //     this.role.stopAnim();\r\n        // }\r\n\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        this.hpWhite.fillRange = 0;\r\n    }\r\n\r\n    /**\r\n     * 检查死亡时是否需要射击\r\n     */\r\n    _checkDieShoot() {\r\n        // if (this._data.dieShoot.length > 0 && !Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n        //     this._bDieShoot = true;\r\n\r\n        //     const shootData = new EnemyShootData();\r\n        //     shootData.attackInterval = this._data.attackInterval;\r\n        //     shootData.attackNum = 1;\r\n        //     shootData.attackArrNum = 1;\r\n        //     shootData.attackPointArr = [this._data.dieShoot];\r\n\r\n        //     this._shootCom.setShootData(shootData);\r\n        //     this._shootCom.setAtkOverCall(() => {\r\n        //         this._bDieShoot = false;\r\n        //         this.role.getComponent(UIOpacity).opacity = 0;\r\n        //     });\r\n        //     this._shootCom.setNextShoot();\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAtkAnim() {\r\n        this.role.playAnim(`atk${this._roleIndex}`, () => {\r\n            this.role.playAnim(`idle${this._roleIndex}`, null);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    playDieAnim() {\r\n        super.playDieAnim();\r\n\r\n        this.scheduleOnce(() => {\r\n            Tools.removeChildByName(this.node, \"fire\");\r\n            this.checkLoot();\r\n            // this.role.getComponent(UIOpacity).opacity = 0;\r\n        }, 0.1);\r\n    }\r\n\r\n    /**\r\n     * 死亡动画结束时的处理\r\n     */\r\n    onDieAnimEnd() {\r\n        this._dieAnimEnd = true;\r\n    }\r\n\r\n    /**\r\n     * 获取当前角度\r\n     * @returns {number} 当前角度\r\n     */\r\n    get angle() {\r\n        return this.role ? this.role.node.angle : 0;\r\n    }\r\n\r\n    /**\r\n     * 设置当前角度\r\n     * @param {number} value 角度值\r\n     */\r\n    set angle(value) {\r\n        if (this.role) {\r\n            this.role.node.angle = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置角色透明度\r\n     * @param {number} opacity 透明度值\r\n     */\r\n    setRoleOpacity(opacity) {\r\n        // if (this.role) {\r\n        //     this.role.opacity = opacity;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹发射的角度\r\n     * @returns {number} 子弹发射角度\r\n     */\r\n    getFireBulletAngle() {\r\n        // if (this._data.id >= 50000 && this._data.id < 60000) {\r\n        //     return this.role.role.node.angle;\r\n        // }\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 检查敌机是否在屏幕内\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    checkInScreen(deltaTime) {\r\n        if (this.itemParent === this) {\r\n            if (Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n                this.m_outTime += deltaTime;\r\n                if (this.m_outTime > 10) {\r\n                    this.isDead = true;\r\n                    this.removeAble = true;\r\n                }\r\n            } else {\r\n                this.m_outTime = 0;\r\n            }\r\n        }\r\n    }\r\n}"]}