System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, PlaneUIEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bbd61JregdCaYzCLdEd6Tnx", "PlaneEvent", undefined);

      _export("PlaneUIEvent", PlaneUIEvent = {
        TabChange: 'PlaneUIEvent_TabChange',
        SortTypeChange: 'PlaneUIEvent_SortTypeChange',
        BagItemClick: 'PlaneUIEvent_BagItemClick',
        UpdateBagGrids: 'PlaneUIEvent_UpdateBagGrids',
        UpdateMergeEquipStatus: 'PlaneUIEvent_UpdateEquipMergeStatus'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b19421e904b7289f238b1c8b1c9d9b81fb8fca6e.js.map