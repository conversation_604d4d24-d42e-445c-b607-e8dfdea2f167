import { _decorator, Component, Tween,Node, Animation, Vec2, v2 } from 'cc';
import BaseComp from '../base/BaseComp';
import { GameIns } from '../../GameIns';
import { Tools } from '../../utils/Tools';
import EnemyBase from '../plane/enemy/EnemyBase';
import BossBase from '../plane/boss/BossBase';
import BattleLayer from '../layer/BattleLayer';
const { ccclass, property } = _decorator;

@ccclass('BaseScreen')
export default class BaseScreen extends BaseComp {
    protected m_enemy: any = true;
    protected m_mainEntity: any = null;
    protected m_bulletState = {
        attack: 0,
        through: false,
        cirt:0,
        atkChallenge: 0,
    };
    protected m_config: any;
    protected m_count: number = 0;

    /**
     * 开始射击
     * @param count 射击次数
     */
    async toFire(count: number): Promise<void> {
        this.m_count = count;
        await this.fire();
    }

    async fire(){
        
    }

    /**
     * 设置数据
     * @param config 配置数据
     * @param enemy 敌人实体
     */
    setData(config: any, enemy: any): void {
        this.m_config = GameIns.bulletManager.getConfig(config);
        this.m_enemy = enemy;
        this.m_count = 0;
    }

    /**
     * 设置子弹状态
     * @param bulletState 子弹状态
     * @param mainEntity 主实体
     */
    setBulletState(bulletState: any, mainEntity?: any): void {
        this.m_mainEntity = mainEntity || this.entity;
        for (const key in bulletState) {
            this.m_bulletState[key] = bulletState[key];
        }
    }

    /**
     * 获取子弹暴击率
     * @returns 暴击率
     */
    getBulletCirt(): number {
        return this.m_bulletState.cirt ? this.m_bulletState.cirt[0] : 0;
    }

    /**
     * 获取子弹攻击力
     * @returns 攻击力
     */
    getBulletAttack(): number {
        return this.m_bulletState.atkChallenge || 0;
    }

    /**
     * 获取攻击点
     * @returns 攻击点的坐标
     */
    getAttackPoint():Vec2{
        if (!this.m_mainEntity) {
            return v2(0, 0);
        }

        const scenePos = GameIns.sceneManager.getScenePos(this.m_mainEntity);
        let position = this.m_entity.node.position;

        if (
            this.m_mainEntity.getFireBulletAngle &&
            this.m_mainEntity.getFireBulletAngle() !== 0
        ) {
            position = Tools.getPositionByAngle(
                this.m_entity.node.position,
                this.m_mainEntity.getFireBulletAngle()
            );
        }

        return v2(
            position.x + scenePos.x,
            position.y + scenePos.y,
        );
    }

    /**
     * 获取攻击角度
     * @returns 攻击角度
     */
    getAttackAngle(): number {
        let angle = this.m_entity.node.angle;

        if (this.m_entity.getAttackPointAngle) {
            angle -= this.m_entity.getAttackPointAngle();
        }

        let fireAngle = 0;
        try {
            if (
                this.m_mainEntity &&
                this.m_mainEntity.getFireBulletAngle
            ) {
                fireAngle = this.m_mainEntity.getFireBulletAngle();
            }
        } catch (error) {}

        return angle - fireAngle;
    }

    /**
     * 获取瞄准角度
     * @returns 瞄准角度
     */
    getAimAngle(): number {
        let angle = 0;
        const attackPoint = this.getAttackPoint();

        if (this.m_enemy) {
            const mainPlaneNode = GameIns.mainPlaneManager.mainPlane.node;
            angle = mainPlaneNode
                ? Tools.getAngle(attackPoint, v2(mainPlaneNode.position.x, mainPlaneNode.position.y))
                : 0;
        } else if (
            GameIns.planeManager.enemyTarget &&
            GameIns.planeManager.enemyCollider
        ) {
            // const enemyTarget = GameIns.planeManager.enemyTarget;
            // const enemyColliderPos =
            //     GameIns.planeManager.enemyCollider.getScreenPos();
            // angle = enemyTarget
            //     ? Tools.getAngle(attackPoint, enemyColliderPos)
            //     : 0;
        }

        return angle;
    }

    /**
     * 创建子弹
     * @returns 创建的子弹
     */
    async createBullet(): Promise<any> {
        const bulletId = this.m_config.id;
        const bullet = await GameIns.bulletManager.getBullet(bulletId, this.m_enemy);

        if (bullet) {
            // if (GameIns.bulletManager.fireShellUINode) {
            //     bullet.node.parent = GameIns.bulletManager.fireShellUINode;
            // }
            //  else if (MainGameIns.planeManager.isShow) {
            //     const mainPlaneUI = frameWork.uiManager.getDialogByName(
            //         "MainPlaneUI"
            //     );
            //     bullet.node.parent = mainPlaneUI.fireNode;
            // } 
            // else {
                BattleLayer.me.addBullet(bullet);
            // }

            if (
                this.m_enemy &&
                (this.m_mainEntity instanceof EnemyBase ||
                    this.m_mainEntity instanceof BossBase)
            ) {
                this.m_mainEntity.addBullet(bullet);
            }
        }

        return bullet;
    }
}