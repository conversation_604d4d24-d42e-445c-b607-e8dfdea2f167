System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseScreen, _dec, _class, _crd, ccclass, property, LoftScreen;

  function _reportPossibleCrUseOfBaseScreen(extras) {
    _reporterNs.report("BaseScreen", "./BaseScreen", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseScreen = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "32da3JAuUJMF4HPiLi5zjgf", "LoftScreen", undefined);

      __checkObsolete__(['_decorator', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", LoftScreen = (_dec = ccclass("LoftScreen"), _dec(_class = class LoftScreen extends (_crd && BaseScreen === void 0 ? (_reportPossibleCrUseOfBaseScreen({
        error: Error()
      }), BaseScreen) : BaseScreen) {
        constructor(config, mainEntity) {
          super();
          this.m_isPlus = false;
          this.props = void 0;
          this.setData(config, mainEntity);
          const para = this.m_config.para;
          const offset = this.m_config.offset;
          this.props = {
            bulletNum: para[0],
            beginAngle: para[1],
            endAngle: para[2],
            angleSpeed: para[3],
            posOffset: offset
          };
          this.m_isPlus = para[1] < para[2];
        }

        onInit() {
          this.m_count = 0;
        }

        async fire() {
          const attackPoint = this.getAttackPoint();
          const x = attackPoint.x;
          const y = attackPoint.y;

          if (this.props.bulletNum === 1) {
            const bullet = await this.createBullet();
            const angle = this.props.beginAngle + this.props.angleSpeed * this.m_count;

            if (bullet) {
              bullet.init(this.m_enemy, {
                x,
                y,
                angle
              }, this.m_bulletState, this.m_mainEntity);
            }
          } else if (this.props.bulletNum > 0) {
            const angleStep = (this.props.endAngle - this.props.beginAngle) / (this.props.bulletNum - 1);

            for (let i = 0; i < this.props.bulletNum; i++) {
              const bullet = await this.createBullet();
              const angle = this.props.beginAngle + angleStep * i + this.props.angleSpeed * this.m_count - 90;

              if (bullet) {
                bullet.init(this.m_enemy, {
                  x,
                  y,
                  angle
                }, this.m_bulletState, this.m_mainEntity);
              }
            }
          }
        }

        update(deltaTime) {// Update logic can be added here if needed
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c1e5585d33ae29c5cb09d682182ced926c7e25ca.js.map