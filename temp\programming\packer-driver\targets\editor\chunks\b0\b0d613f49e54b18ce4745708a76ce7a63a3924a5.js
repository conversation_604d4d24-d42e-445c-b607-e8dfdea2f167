System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseScreen, GameConst, _dec, _class, _crd, ccclass, property, AimSingleLineScreen;

  function _reportPossibleCrUseOfBaseScreen(extras) {
    _reporterNs.report("BaseScreen", "./BaseScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseScreen = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "afb57iowBdGe4U6hjn2sPT4", "AimSingleLineScreen", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Tween', 'Node', 'Animation', 'Vec2', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", AimSingleLineScreen = (_dec = ccclass('AimSingleLineScreen'), _dec(_class = class AimSingleLineScreen extends (_crd && BaseScreen === void 0 ? (_reportPossibleCrUseOfBaseScreen({
        error: Error()
      }), BaseScreen) : BaseScreen) {
        constructor(config, mainEntity) {
          super();
          this.m_angle = 0;
          this.props = void 0;
          this.m_target = void 0;
          this.setData(config, mainEntity);
          this.props = {
            sameDir: 0,
            posOffset: []
          };

          if (this.m_config.para && this.m_config.para.length > 0) {
            this.props.sameDir = this.m_config.para[0];
          } else {
            this.props.sameDir = 0;
          }

          this.props.posOffset = this.m_config.offset;
        }

        onInit() {
          this.m_count = 0;
        }

        async fire() {
          if (this.m_enemy !== 0 || this.m_target !== null) {
            const attackPoint = this.getAttackPoint();

            if (this.props.sameDir === 0 || this.m_count === 0) {
              this.m_angle = this.getAimAngle();
            }

            for (let i = 0; i < 1; i++) {
              const bullet = await this.createBullet();
              const x = attackPoint.x;
              const y = attackPoint.y;
              const angle = this.m_angle;

              if (bullet) {
                bullet.init(this.m_enemy, {
                  x,
                  y,
                  angle
                }, this.m_bulletState, this.m_mainEntity);
              }
            }
          }
        }

        update(deltaTime) {
          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {// Update logic can be added here if needed
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b0d613f49e54b18ce4745708a76ce7a63a3924a5.js.map