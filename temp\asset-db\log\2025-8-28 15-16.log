2025-8-28 15:16:06-debug: start **** info
2025-8-28 15:16:06-log: Cannot access game frame or container.
2025-8-28 15:16:06-debug: asset-db:require-engine-code (391ms)
2025-8-28 15:16:06-log: meshopt wasm decoder initialized
2025-8-28 15:16:06-log: [bullet]:bullet wasm lib loaded.
2025-8-28 15:16:06-log: [box2d]:box2d wasm lib loaded.
2025-8-28 15:16:06-log: Cocos Creator v3.8.6
2025-8-28 15:16:06-log: Using legacy pipeline
2025-8-28 15:16:06-log: Forward render pipeline initialized.
2025-8-28 15:16:06-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.03MB, end 84.08MB, increase: 3.05MB
2025-8-28 15:16:06-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.92MB, end 80.18MB, increase: 49.26MB
2025-8-28 15:16:07-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.89MB, end 274.02MB, increase: 193.14MB
2025-8-28 15:16:07-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.12MB, end 272.60MB, increase: 188.48MB
2025-8-28 15:16:07-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.20MB, end 274.05MB, increase: 193.85MB
2025-8-28 15:16:07-debug: run package(google-play) handler(enable) start
2025-8-28 15:16:07-debug: run package(google-play) handler(enable) success!
2025-8-28 15:16:07-debug: run package(harmonyos-next) handler(enable) success!
2025-8-28 15:16:07-debug: run package(honor-mini-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(harmonyos-next) handler(enable) start
2025-8-28 15:16:07-debug: run package(honor-mini-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(huawei-agc) handler(enable) success!
2025-8-28 15:16:07-debug: run package(huawei-quick-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(huawei-quick-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(ios) handler(enable) start
2025-8-28 15:16:07-debug: run package(ios) handler(enable) success!
2025-8-28 15:16:07-debug: run package(linux) handler(enable) success!
2025-8-28 15:16:07-debug: run package(linux) handler(enable) start
2025-8-28 15:16:07-debug: run package(huawei-agc) handler(enable) start
2025-8-28 15:16:07-debug: run package(migu-mini-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(mac) handler(enable) start
2025-8-28 15:16:07-debug: run package(mac) handler(enable) success!
2025-8-28 15:16:07-debug: run package(migu-mini-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(native) handler(enable) start
2025-8-28 15:16:07-debug: run package(native) handler(enable) success!
2025-8-28 15:16:07-debug: run package(ohos) handler(enable) success!
2025-8-28 15:16:07-debug: run package(oppo-mini-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(ohos) handler(enable) start
2025-8-28 15:16:07-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-28 15:16:07-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-28 15:16:07-debug: run package(vivo-mini-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(taobao-mini-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(web-desktop) handler(enable) success!
2025-8-28 15:16:07-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(web-mobile) handler(enable) success!
2025-8-28 15:16:07-debug: run package(web-desktop) handler(enable) start
2025-8-28 15:16:07-debug: run package(web-mobile) handler(enable) start
2025-8-28 15:16:07-debug: run package(wechatgame) handler(enable) success!
2025-8-28 15:16:07-debug: run package(wechatprogram) handler(enable) start
2025-8-28 15:16:07-debug: run package(wechatprogram) handler(enable) success!
2025-8-28 15:16:07-debug: run package(wechatgame) handler(enable) start
2025-8-28 15:16:07-debug: run package(windows) handler(enable) success!
2025-8-28 15:16:07-debug: run package(windows) handler(enable) start
2025-8-28 15:16:07-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-28 15:16:07-debug: run package(cocos-service) handler(enable) success!
2025-8-28 15:16:07-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-28 15:16:07-debug: run package(cocos-service) handler(enable) start
2025-8-28 15:16:07-debug: run package(im-plugin) handler(enable) success!
2025-8-28 15:16:07-debug: run package(im-plugin) handler(enable) start
2025-8-28 15:16:07-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-28 15:16:07-debug: asset-db:worker-init: initPlugin (1006ms)
2025-8-28 15:16:07-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-28 15:16:07-debug: run package(level-editor) handler(enable) start
2025-8-28 15:16:07-debug: run package(level-editor) handler(enable) success!
2025-8-28 15:16:07-debug: [Assets Memory track]: asset-db:worker-init start:30.91MB, end 276.27MB, increase: 245.36MB
2025-8-28 15:16:07-debug: Run asset db hook programming:beforePreStart ...
2025-8-28 15:16:07-debug: Run asset db hook programming:beforePreStart success!
2025-8-28 15:16:07-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-28 15:16:07-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-28 15:16:07-debug: run package(level-editor-extension) handler(enable) start
2025-8-28 15:16:07-debug: run package(level-editor-extension) handler(enable) success!
2025-8-28 15:16:07-debug: run package(localization-editor) handler(enable) start
2025-8-28 15:16:07-debug: run package(localization-editor) handler(enable) success!
2025-8-28 15:16:07-debug: asset-db-hook-programming-beforePreStart (139ms)
2025-8-28 15:16:07-debug: asset-db:worker-init (1608ms)
2025-8-28 15:16:07-debug: asset-db-hook-engine-extends-beforePreStart (139ms)
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:07-debug: Preimport db internal success
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: run package(placeholder) handler(enable) start
2025-8-28 15:16:08-debug: run package(placeholder) handler(enable) success!
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AAA
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\event
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\PlatformSDK
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Network
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\common
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\Luban
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\bag
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\equip
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\gm
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\level
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\gm
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\wave
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\img
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\fight
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\plane
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\layer
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\GameOld\world\base
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\button
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\dropdown
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\list
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ColliderTest.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\MainUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\MyApp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorElemUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorWaveUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEdtiorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\DataEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\DataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\GameFunc.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\event\EventManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\GameIns.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Network\NetMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\PlatformSDK\DevLogin.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\audioManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\PlatformSDK\WXLogin.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\UIMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\LoadingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB\cs_proto.d.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\Luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\ResManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\bag\Bag.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\equip\Equip.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\equip\EquipCombine.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\equip\EquipSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Data\gm\GM.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\EventRunner.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FCircleCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FBoxCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FColliderManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\FPolygonCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\Intersection.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\collider-system\QuadTree.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const\GameConst.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const\GameEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\const\GameResourceList.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\BossData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EmitterData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\BulletEventData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EnemyWave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EventActionData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EventConditionData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\GameMapData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\MainData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\TrackData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\StageData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\level\LevelItem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\level\LevelItemEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BulletManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GameDataManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GameResManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\HurtEffectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\GameRuleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\PlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\PrefabManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\StageManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\SceneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils\Tools.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition\LevelDataEventCondtion.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition\LevelDataEventCondtionDelayDistance.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition\LevelDataEventCondtionDelayTime.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition\LevelDataEventCondtionWave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\condition\newCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTrigger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTriggerAudio.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTriggerLog.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger\LevelDataEventTriggerWave.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\trigger\newTrigger.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\TopBlockInputUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\gm\GmUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\gm\GmButtonUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BattleUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\BuildingInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\PlaneShowUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\MapModeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\MainEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\BaseComp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\AttackPoint.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\ExchangeMap.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\NodeMove.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\PfFrameAnim.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\TrackComponent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\base\UIAnimMethods.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\AimCircleScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\BulletFly.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\CircleZoomFly.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\AimSingleLineScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\BaseScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\LoftScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\CircleScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bulletDanmu\CircleZoomScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\layer\BattleLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\layer\EffectLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\layer\EnemyEffectLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelElemUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelWaveUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\map\LevelEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\GameOld\world\base\SystemContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\dialogue\DialogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueSelectIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendAddUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\fight\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendListUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendStrangerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneTypes.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\PlaneUI.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnit.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\boss\BossUnitBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAnim.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyAttrDoctorCom.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyBase.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyEffectComp.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlaneRole.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyShootComponent.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\mainPlane\FireShells.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\dropdown\DropDown.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\common\components\list\List.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\BagGrid.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\BagItem.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\back_pack\SortTypeDropdown.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display\EquipDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\main\plane\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:08-debug: Run asset db hook programming:afterPreStart ...
2025-8-28 15:16:08-debug: Preimport db assets success
2025-8-28 15:16:08-debug: starting packer-driver...
2025-8-28 15:16:23-debug: initialize scripting environment...
2025-8-28 15:16:23-debug: [[Executor]] prepare before lock
2025-8-28 15:16:23-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-28 15:16:23-debug: [[Executor]] prepare after unlock
2025-8-28 15:16:23-debug: Run asset db hook programming:afterPreStart success!
2025-8-28 15:16:23-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-28 15:16:23-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-28 15:16:23-debug: [Assets Memory track]: asset-db:worker-init: preStart start:276.29MB, end 287.31MB, increase: 11.03MB
2025-8-28 15:16:23-debug: Start up the 'internal' database...
2025-8-28 15:16:24-debug: asset-db:worker-effect-data-processing (229ms)
2025-8-28 15:16:24-debug: asset-db-hook-programming-afterPreStart (16099ms)
2025-8-28 15:16:24-debug: asset-db-hook-engine-extends-afterPreStart (230ms)
2025-8-28 15:16:24-debug: Start up the 'assets' database...
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scenes\ColliderTest.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scenes\DevLogin.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: asset-db:worker-startup-database[internal] (16527ms)
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Main.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbboss.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbchapter.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbenemy.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbenemyui.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbbullet.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbequip.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbequipupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbgamemap.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbglobalattr.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbgm.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbitem.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tblevel.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tblevelgroup.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbmainplane.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbmainplanelv.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbstage.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbtrack.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbunit.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Luban\tbwave.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\tempbg.jpg
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AAA\init_cs_proto.js
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\WheelSpinner.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\3.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\4.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\5.json
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\tempbg.jpg@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\WheelSpinner.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\Bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\WheelSpinner.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\tempbg.jpg@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\Bullet_New.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\MainPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\common\TopBlockInputUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\GmButtonUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\GmUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\Building.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\BattleUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\BuildingInfoUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\ItemIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\MapModeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\PlaneShowUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\PopupUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\WheelSpinnerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\AutoGen\PB\cs_proto.js
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\ss1_1_4.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\1_2.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\1_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\ss1_1_5.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\ss1_1_6.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\ss1_1_7.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\ss1_1_8.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\yy1_1_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\yy1_1_2.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\yy1_1_3.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\background\yy1_1_4.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\wave\line.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\DialogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueAbilityIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueSelectIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\gm\img\gm_dw_mark.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\girl.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\dialogue\boy.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\fight\RogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendAddUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendListUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendStrangerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneCombineResultUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\plane\BagItem.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneEquipInfoUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\icon.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\plane\PlaneUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\friend\leidian.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@17072
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@32196
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@42631
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@49216
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@49723
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@54923
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@57784
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@58624
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@70691
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@74653
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@90878
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@53fb5
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@8b4eb
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@42c20
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f669c
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@58cb7
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@4c659
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f2072
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@322f0
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@3a0d4
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@2f354
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@fe1b1
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@47fc3
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@fcbe3
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@e74d2
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@b6e78
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@58afc
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f1723
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@1b9cb
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@0c7d9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@74c7b
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@1b009
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@0b997
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@241e5
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@b67cd
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@a87fe
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@6bee8
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@b78da
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@656aa
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@a1de9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@1a8ac
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d0647
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@6f387
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@436c9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@2d992
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@9339d
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@845f0
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@ef373
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@c16d3
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@a9ed9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@72be1
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@9b2ac
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@2882b
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f1031
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@c22bb
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@cff9d
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f7a8b
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@c050f
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@0c474
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@802e6
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d3232
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d59a1
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@314ad
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@6e37d
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@afb86
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@890a8
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@37ff3
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@8d079
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@279de
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@c78b5
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@f3f44
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@552c9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@77b7b
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@e0b26
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@5ef15
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@41b24
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d1c49
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@97a80
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@cbc51
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@352d3
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@eb6b4
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@5f06b
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d04d0
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@50f14
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@e3960
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@2837d
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@b3a44
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@2dc7e
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@a0aa9
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@921f4
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@c6925
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\texture\common\package_uiCommon.plist@d0de1
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:16:24-debug: lazy register asset handler *
2025-8-28 15:16:24-debug: lazy register asset handler directory
2025-8-28 15:16:24-debug: lazy register asset handler spine-data
2025-8-28 15:16:24-debug: lazy register asset handler dragonbones
2025-8-28 15:16:24-debug: lazy register asset handler json
2025-8-28 15:16:24-debug: lazy register asset handler dragonbones-atlas
2025-8-28 15:16:24-debug: lazy register asset handler text
2025-8-28 15:16:24-debug: lazy register asset handler terrain
2025-8-28 15:16:24-debug: lazy register asset handler javascript
2025-8-28 15:16:24-debug: lazy register asset handler prefab
2025-8-28 15:16:24-debug: lazy register asset handler typescript
2025-8-28 15:16:24-debug: lazy register asset handler scene
2025-8-28 15:16:24-debug: lazy register asset handler sprite-frame
2025-8-28 15:16:24-debug: lazy register asset handler tiled-map
2025-8-28 15:16:24-debug: lazy register asset handler image
2025-8-28 15:16:24-debug: lazy register asset handler alpha-image
2025-8-28 15:16:24-debug: lazy register asset handler texture
2025-8-28 15:16:24-debug: lazy register asset handler buffer
2025-8-28 15:16:24-debug: lazy register asset handler sign-image
2025-8-28 15:16:24-debug: lazy register asset handler erp-texture-cube
2025-8-28 15:16:24-debug: lazy register asset handler texture-cube-face
2025-8-28 15:16:24-debug: lazy register asset handler render-texture
2025-8-28 15:16:24-debug: lazy register asset handler rt-sprite-frame
2025-8-28 15:16:24-debug: lazy register asset handler gltf
2025-8-28 15:16:24-debug: lazy register asset handler texture-cube
2025-8-28 15:16:24-debug: lazy register asset handler gltf-animation
2025-8-28 15:16:24-debug: lazy register asset handler gltf-mesh
2025-8-28 15:16:24-debug: lazy register asset handler gltf-skeleton
2025-8-28 15:16:24-debug: lazy register asset handler gltf-embeded-image
2025-8-28 15:16:24-debug: lazy register asset handler gltf-material
2025-8-28 15:16:24-debug: lazy register asset handler material
2025-8-28 15:16:24-debug: lazy register asset handler effect
2025-8-28 15:16:24-debug: lazy register asset handler gltf-scene
2025-8-28 15:16:24-debug: lazy register asset handler physics-material
2025-8-28 15:16:24-debug: lazy register asset handler fbx
2025-8-28 15:16:24-debug: lazy register asset handler effect-header
2025-8-28 15:16:24-debug: lazy register asset handler animation-clip
2025-8-28 15:16:24-debug: lazy register asset handler animation-graph
2025-8-28 15:16:24-debug: lazy register asset handler animation-mask
2025-8-28 15:16:24-debug: lazy register asset handler animation-graph-variant
2025-8-28 15:16:24-debug: lazy register asset handler ttf-font
2025-8-28 15:16:24-debug: lazy register asset handler audio-clip
2025-8-28 15:16:24-debug: lazy register asset handler bitmap-font
2025-8-28 15:16:24-debug: lazy register asset handler particle
2025-8-28 15:16:24-debug: lazy register asset handler auto-atlas
2025-8-28 15:16:24-debug: lazy register asset handler render-pipeline
2025-8-28 15:16:24-debug: lazy register asset handler sprite-atlas
2025-8-28 15:16:24-debug: lazy register asset handler render-stage
2025-8-28 15:16:24-debug: lazy register asset handler instantiation-material
2025-8-28 15:16:24-debug: lazy register asset handler instantiation-mesh
2025-8-28 15:16:24-debug: lazy register asset handler label-atlas
2025-8-28 15:16:24-debug: lazy register asset handler instantiation-skeleton
2025-8-28 15:16:24-debug: lazy register asset handler video-clip
2025-8-28 15:16:24-debug: lazy register asset handler instantiation-animation
2025-8-28 15:16:24-debug: lazy register asset handler render-flow
2025-8-28 15:16:24-debug: asset-db:worker-startup-database[assets] (16700ms)
2025-8-28 15:16:24-debug: asset-db:start-database (16855ms)
2025-8-28 15:16:24-debug: asset-db:ready (19874ms)
2025-8-28 15:16:24-debug: init worker message success
2025-8-28 15:16:24-debug: fix the bug of updateDefaultUserData
2025-8-28 15:16:24-debug: programming:execute-script (3ms)
2025-8-28 15:16:24-debug: [Build Memory track]: builder:worker-init start:198.31MB, end 211.54MB, increase: 13.23MB
2025-8-28 15:16:24-debug: builder:worker-init (301ms)
2025-8-28 15:16:44-debug: refresh db internal success
2025-8-28 15:16:44-debug: refresh db assets success
2025-8-28 15:16:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:16:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:16:44-debug: asset-db:refresh-all-database (109ms)
2025-8-28 15:16:50-debug: Query all assets info in project
2025-8-28 15:16:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:16:50-debug: Skip compress image, progress: 0%
2025-8-28 15:16:50-debug: Init all bundles start..., progress: 0%
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:16:50-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:16:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:16:50-debug:   Number of other assets: 2032
2025-8-28 15:16:50-debug:   Number of all scenes: 8
2025-8-28 15:16:50-debug:   Number of all scripts: 260
2025-8-28 15:16:50-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:16:50-log: run build task Query asset bundle success in 21 ms√, progress: 5%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (21ms)
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:206.03MB, end 208.25MB, increase: 2.22MB
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 5%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:208.28MB, end 207.07MB, increase: -1236.39KB
2025-8-28 15:16:50-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:16:50-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:16:50-debug: [Build Memory track]: Sort some build options to settings.json start:207.10MB, end 207.13MB, increase: 25.83KB
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 15:16:50-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 15:16:50-debug: [Build Memory track]: Fill script data to settings start:207.15MB, end 207.18MB, increase: 28.46KB
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (3ms)
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 3 ms√, progress: 15%
2025-8-28 15:16:50-debug: [Build Memory track]: Sort some build options to settings.json start:207.21MB, end 207.50MB, increase: 297.20KB
2025-8-28 15:16:50-debug: Query all assets info in project
2025-8-28 15:16:50-debug: Query all assets info in project
2025-8-28 15:16:50-debug: Query all assets info in project
2025-8-28 15:16:50-debug: Query all assets info in project
2025-8-28 15:16:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:16:50-debug: Skip compress image, progress: 0%
2025-8-28 15:16:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:16:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:16:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:16:50-debug: Skip compress image, progress: 0%
2025-8-28 15:16:50-debug: Skip compress image, progress: 0%
2025-8-28 15:16:50-debug: Skip compress image, progress: 0%
2025-8-28 15:16:50-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:16:50-debug: Init all bundles start..., progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 0%
2025-8-28 15:16:50-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:16:50-debug: Init all bundles start..., progress: 0%
2025-8-28 15:16:50-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Init all bundles start..., progress: 0%
2025-8-28 15:16:50-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:16:50-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:16:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:16:50-debug:   Number of all scenes: 8
2025-8-28 15:16:50-debug:   Number of all scripts: 260
2025-8-28 15:16:50-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:16:50-debug:   Number of other assets: 2032
2025-8-28 15:16:50-debug: Init all bundles start..., progress: 0%
2025-8-28 15:16:50-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 0%
2025-8-28 15:16:50-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (19ms)
2025-8-28 15:16:50-log: run build task Query asset bundle success in 19 ms√, progress: 5%
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:209.72MB, end 209.76MB, increase: 38.22KB
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 5%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:16:50-debug:   Number of all scripts: 260
2025-8-28 15:16:50-debug:   Number of other assets: 2032
2025-8-28 15:16:50-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:16:50-debug:   Number of all scenes: 8
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:209.81MB, end 210.39MB, increase: 591.06KB
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (17ms)
2025-8-28 15:16:50-log: run build task Query asset bundle success in 17 ms√, progress: 5%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 5%
2025-8-28 15:16:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:16:50-debug:   Number of all scenes: 8
2025-8-28 15:16:50-debug:   Number of other assets: 2032
2025-8-28 15:16:50-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:16:50-debug:   Number of all scripts: 260
2025-8-28 15:16:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:16:50-debug:   Number of all scenes: 8
2025-8-28 15:16:50-debug:   Number of other assets: 2032
2025-8-28 15:16:50-debug:   Number of all scripts: 260
2025-8-28 15:16:50-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (33ms)
2025-8-28 15:16:50-log: run build task Query asset bundle success in 33 ms√, progress: 10%
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:210.41MB, end 211.27MB, increase: 881.98KB
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 5%
2025-8-28 15:16:50-log: run build task Query asset bundle success in √, progress: 5%
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-debug: Query asset bundle start, progress: 5%
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:211.34MB, end 211.36MB, increase: 12.24KB
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ----
2025-8-28 15:16:50-log: run build task Query asset bundle success in √, progress: 5%
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 15:16:50-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:16:50-debug: [Build Memory track]: Sort some build options to settings.json start:211.31MB, end 211.65MB, increase: 345.19KB
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:16:50-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 15:16:50-debug: [Build Memory track]: Fill script data to settings start:211.67MB, end 212.17MB, increase: 507.30KB
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Query asset bundle ---- (3ms)
2025-8-28 15:16:50-debug: [Build Memory track]: Query asset bundle start:211.38MB, end 212.23MB, increase: 869.13KB
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:16:50-log: run build task Query asset bundle success in 3 ms√, progress: 10%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-log: run build task Query asset bundle success in √, progress: 10%
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:16:50-log: run build task Query asset bundle success in √, progress: 10%
2025-8-28 15:16:50-debug: [Build Memory track]: Sort some build options to settings.json start:212.33MB, end 212.36MB, increase: 22.21KB
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:16:50-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:16:50-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:16:50-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:16:50-debug: [Build Memory track]: Fill script data to settings start:212.48MB, end 212.50MB, increase: 21.49KB
2025-8-28 15:16:50-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 15:16:50-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 15:16:50-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:16:50-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:16:50-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 15:16:50-debug: [Build Memory track]: Sort some build options to settings.json start:212.61MB, end 213.59MB, increase: 1001.37KB
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 15:16:50-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 15:19:17-debug: refresh db internal success
2025-8-28 15:19:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:19:17-debug: refresh db assets success
2025-8-28 15:19:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:19:17-debug: asset-db:refresh-all-database (138ms)
2025-8-28 15:19:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:19:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:19:30-debug: refresh db internal success
2025-8-28 15:19:30-debug: refresh db assets success
2025-8-28 15:19:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:19:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:19:30-debug: asset-db:refresh-all-database (105ms)
2025-8-28 15:19:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:20:09-debug: refresh db internal success
2025-8-28 15:20:09-debug: refresh db assets success
2025-8-28 15:20:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:20:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:20:09-debug: asset-db:refresh-all-database (132ms)
2025-8-28 15:20:09-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-28 15:20:09-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:20:13-debug: %cImport%c: E:\M2Game\Client\assets\scenes\DevLogin.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:20:13-debug: asset-db:reimport-asset4e0f763c-2bca-4fe4-a6f5-7ede697e4d9d (5ms)
2025-8-28 15:25:22-debug: refresh db internal success
2025-8-28 15:25:22-debug: refresh db assets success
2025-8-28 15:25:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:25:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:25:22-debug: asset-db:refresh-all-database (133ms)
2025-8-28 15:25:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:25:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:25:31-debug: Query all assets info in project
2025-8-28 15:25:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:25:31-debug: Skip compress image, progress: 0%
2025-8-28 15:25:31-debug: Init all bundles start..., progress: 0%
2025-8-28 15:25:31-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:25:31-debug: Query asset bundle start, progress: 0%
2025-8-28 15:25:31-debug: // ---- build task Query asset bundle ----
2025-8-28 15:25:31-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:25:31-debug:   Number of all scenes: 8
2025-8-28 15:25:31-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:25:31-debug:   Number of all scripts: 260
2025-8-28 15:25:31-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:25:31-debug:   Number of other assets: 2032
2025-8-28 15:25:31-debug: // ---- build task Query asset bundle ---- (18ms)
2025-8-28 15:25:31-log: run build task Query asset bundle success in 18 ms√, progress: 5%
2025-8-28 15:25:31-debug: [Build Memory track]: Query asset bundle start:208.53MB, end 208.31MB, increase: -226.84KB
2025-8-28 15:25:31-debug: Query asset bundle start, progress: 5%
2025-8-28 15:25:31-debug: // ---- build task Query asset bundle ----
2025-8-28 15:25:31-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 15:25:31-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:25:31-debug: [Build Memory track]: Query asset bundle start:208.33MB, end 208.60MB, increase: 274.25KB
2025-8-28 15:25:31-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 15:25:31-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:25:31-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:25:31-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:25:31-debug: [Build Memory track]: Sort some build options to settings.json start:208.63MB, end 208.65MB, increase: 25.95KB
2025-8-28 15:25:31-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:25:31-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:25:31-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 15:25:31-debug: [Build Memory track]: Fill script data to settings start:208.68MB, end 208.70MB, increase: 17.40KB
2025-8-28 15:25:31-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:25:31-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:25:31-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 15:25:31-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 15:25:31-debug: [Build Memory track]: Sort some build options to settings.json start:208.72MB, end 209.01MB, increase: 295.54KB
2025-8-28 15:26:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:24-debug: asset-db:reimport-assetd2915f5a-214b-497d-a3fb-649ce969d848 (3ms)
2025-8-28 15:26:55-debug: refresh db internal success
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\LoadingUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\BattleUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\BottomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\ShopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\SkyIslandUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\TalentUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\main\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:26:55-debug: refresh db assets success
2025-8-28 15:26:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:26:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:26:55-debug: asset-db:refresh-all-database (168ms)
2025-8-28 15:26:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:26:55-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:27:05-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:27:05-debug: asset-db:reimport-asset6b7dd519-4aaa-4330-9182-eb16a8339fe5 (2ms)
2025-8-28 15:27:12-debug: refresh db internal success
2025-8-28 15:27:13-debug: refresh db assets success
2025-8-28 15:27:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:27:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:27:13-debug: asset-db:refresh-all-database (103ms)
2025-8-28 15:27:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:27:38-debug: refresh db internal success
2025-8-28 15:27:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:27:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:27:38-debug: refresh db assets success
2025-8-28 15:27:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:27:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:27:38-debug: asset-db:refresh-all-database (138ms)
2025-8-28 15:27:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:27:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:27:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\ui\DevLoginUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-28 15:27:56-debug: asset-db:reimport-asset6b7dd519-4aaa-4330-9182-eb16a8339fe5 (3ms)
2025-8-28 15:28:11-debug: refresh db internal success
2025-8-28 15:28:11-debug: refresh db assets success
2025-8-28 15:28:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:28:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:28:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:28:11-debug: asset-db:refresh-all-database (130ms)
2025-8-28 15:28:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:28:16-debug: refresh db internal success
2025-8-28 15:28:16-debug: refresh db assets success
2025-8-28 15:28:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:28:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:28:16-debug: asset-db:refresh-all-database (108ms)
2025-8-28 15:28:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:28:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:28:38-debug: refresh db internal success
2025-8-28 15:28:38-debug: refresh db assets success
2025-8-28 15:28:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:28:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:28:38-debug: asset-db:refresh-all-database (126ms)
2025-8-28 15:28:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:28:38-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:28:46-debug: Query all assets info in project
2025-8-28 15:28:46-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 15:28:46-debug: Skip compress image, progress: 0%
2025-8-28 15:28:46-debug: Init all bundles start..., progress: 0%
2025-8-28 15:28:46-debug: Query asset bundle start, progress: 0%
2025-8-28 15:28:46-debug: // ---- build task Query asset bundle ----
2025-8-28 15:28:46-debug: Num of bundles: 3..., progress: 0%
2025-8-28 15:28:46-debug: Init bundle root assets start..., progress: 0%
2025-8-28 15:28:46-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 15:28:46-debug:   Number of all scripts: 260
2025-8-28 15:28:46-debug:   Number of other assets: 2032
2025-8-28 15:28:46-debug:   Number of all scenes: 8
2025-8-28 15:28:46-debug: Init bundle root assets success..., progress: 0%
2025-8-28 15:28:46-debug: // ---- build task Query asset bundle ---- (20ms)
2025-8-28 15:28:46-debug: [Build Memory track]: Query asset bundle start:207.36MB, end 208.39MB, increase: 1.03MB
2025-8-28 15:28:46-log: run build task Query asset bundle success in 20 ms√, progress: 5%
2025-8-28 15:28:46-debug: Query asset bundle start, progress: 5%
2025-8-28 15:28:46-debug: // ---- build task Query asset bundle ----
2025-8-28 15:28:46-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 15:28:46-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 15:28:46-debug: [Build Memory track]: Query asset bundle start:208.42MB, end 208.69MB, increase: 275.84KB
2025-8-28 15:28:46-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 15:28:46-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:28:46-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 15:28:46-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 15:28:46-debug: [Build Memory track]: Sort some build options to settings.json start:208.71MB, end 208.74MB, increase: 25.09KB
2025-8-28 15:28:46-debug: Fill script data to settings start, progress: 12%
2025-8-28 15:28:46-debug: // ---- build task Fill script data to settings ----
2025-8-28 15:28:46-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 15:28:46-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 15:28:46-debug: [Build Memory track]: Fill script data to settings start:208.76MB, end 208.79MB, increase: 25.35KB
2025-8-28 15:28:46-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 15:28:46-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 15:28:46-debug: // ---- build task Sort some build options to settings.json ---- (3ms)
2025-8-28 15:28:46-debug: [Build Memory track]: Sort some build options to settings.json start:208.81MB, end 209.10MB, increase: 296.08KB
2025-8-28 15:28:46-log: run build task Sort some build options to settings.json success in 3 ms√, progress: 15%
2025-8-28 15:29:26-debug: refresh db internal success
2025-8-28 15:29:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:29:26-debug: refresh db assets success
2025-8-28 15:29:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:29:26-debug: asset-db:refresh-all-database (122ms)
2025-8-28 15:29:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:29:26-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 15:30:27-debug: refresh db internal success
2025-8-28 15:30:27-debug: refresh db assets success
2025-8-28 15:30:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:30:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:30:27-debug: asset-db:refresh-all-database (102ms)
2025-8-28 15:30:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:30:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 15:30:57-debug: refresh db internal success
2025-8-28 15:30:57-debug: refresh db assets success
2025-8-28 15:30:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 15:30:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 15:30:57-debug: asset-db:refresh-all-database (137ms)
2025-8-28 15:30:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 15:30:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 16:41:05-debug: refresh db internal success
2025-8-28 16:41:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 16:41:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet
background: #aaff85; color: #000;
color: #000;
2025-8-28 16:41:05-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EventActionData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 16:41:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 16:41:05-debug: refresh db assets success
2025-8-28 16:41:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 16:41:05-debug: asset-db:refresh-all-database (109ms)
2025-8-28 16:41:07-debug: refresh db internal success
2025-8-28 16:41:07-debug: refresh db assets success
2025-8-28 16:41:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 16:41:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 16:41:07-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-28 16:41:07-debug: asset-db:refresh-all-database (96ms)
2025-8-28 16:41:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 16:41:09-debug: refresh db internal success
2025-8-28 16:41:09-debug: refresh db assets success
2025-8-28 16:41:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 16:41:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 16:41:09-debug: asset-db:refresh-all-database (94ms)
2025-8-28 16:41:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 16:41:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 16:41:11-debug: refresh db internal success
2025-8-28 16:41:11-debug: refresh db assets success
2025-8-28 16:41:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 16:41:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 16:41:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 16:41:11-debug: asset-db:refresh-all-database (95ms)
2025-8-28 16:41:11-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 16:41:13-debug: refresh db internal success
2025-8-28 16:41:13-debug: refresh db assets success
2025-8-28 16:41:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 16:41:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 16:41:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-28 16:41:13-debug: asset-db:refresh-all-database (104ms)
2025-8-28 16:41:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 17:01:24-debug: refresh db internal success
2025-8-28 17:01:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\EventActionData.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:01:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:01:24-debug: refresh db assets success
2025-8-28 17:01:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:01:24-debug: asset-db:refresh-all-database (142ms)
2025-8-28 17:01:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 17:01:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:01:30-debug: Query all assets info in project
2025-8-28 17:01:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:01:30-debug: Skip compress image, progress: 0%
2025-8-28 17:01:30-debug: Init all bundles start..., progress: 0%
2025-8-28 17:01:30-debug: Query asset bundle start, progress: 0%
2025-8-28 17:01:30-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:01:30-debug: // ---- build task Query asset bundle ----
2025-8-28 17:01:30-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:01:30-debug:   Number of all scenes: 8
2025-8-28 17:01:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:01:30-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:01:30-debug:   Number of other assets: 2032
2025-8-28 17:01:30-debug:   Number of all scripts: 261
2025-8-28 17:01:30-debug: // ---- build task Query asset bundle ---- (20ms)
2025-8-28 17:01:30-debug: [Build Memory track]: Query asset bundle start:207.86MB, end 208.87MB, increase: 1.01MB
2025-8-28 17:01:30-log: run build task Query asset bundle success in 20 ms√, progress: 5%
2025-8-28 17:01:30-debug: Query asset bundle start, progress: 5%
2025-8-28 17:01:30-debug: // ---- build task Query asset bundle ----
2025-8-28 17:01:30-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:01:30-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:01:30-debug: [Build Memory track]: Query asset bundle start:208.90MB, end 209.17MB, increase: 280.21KB
2025-8-28 17:01:30-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:01:30-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:01:30-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:01:30-debug: [Build Memory track]: Sort some build options to settings.json start:209.20MB, end 209.21MB, increase: 16.31KB
2025-8-28 17:01:30-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:01:30-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:01:30-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:01:30-debug: [Build Memory track]: Fill script data to settings start:209.24MB, end 209.26MB, increase: 16.95KB
2025-8-28 17:01:30-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:01:30-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:01:30-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:01:30-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:01:30-debug: [Build Memory track]: Sort some build options to settings.json start:209.28MB, end 209.57MB, increase: 294.95KB
2025-8-28 17:01:51-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:01:51-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-8-28 17:01:52-debug: Query all assets info in project
2025-8-28 17:01:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:01:52-debug: Skip compress image, progress: 0%
2025-8-28 17:01:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:01:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:01:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:01:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:01:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:01:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:01:52-debug:   Number of all scenes: 8
2025-8-28 17:01:52-debug:   Number of other assets: 2032
2025-8-28 17:01:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:01:52-debug:   Number of all scripts: 261
2025-8-28 17:01:52-debug: // ---- build task Query asset bundle ---- (20ms)
2025-8-28 17:01:52-log: run build task Query asset bundle success in 20 ms√, progress: 5%
2025-8-28 17:01:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:01:52-debug: [Build Memory track]: Query asset bundle start:211.56MB, end 211.31MB, increase: -255.84KB
2025-8-28 17:01:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:01:52-debug: // ---- build task Query asset bundle ---- (1ms)
2025-8-28 17:01:52-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-8-28 17:01:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:01:52-debug: [Build Memory track]: Query asset bundle start:211.34MB, end 211.61MB, increase: 279.38KB
2025-8-28 17:01:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:01:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:01:52-debug: [Build Memory track]: Sort some build options to settings.json start:211.64MB, end 211.65MB, increase: 16.84KB
2025-8-28 17:01:52-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:01:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:01:52-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:01:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:01:52-debug: [Build Memory track]: Fill script data to settings start:211.68MB, end 211.70MB, increase: 16.00KB
2025-8-28 17:01:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:01:52-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:01:52-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:01:52-debug: [Build Memory track]: Sort some build options to settings.json start:211.72MB, end 212.02MB, increase: 303.78KB
2025-8-28 17:02:01-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:02:01-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (1ms)
2025-8-28 17:02:01-debug: Query all assets info in project
2025-8-28 17:02:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:02:01-debug: Skip compress image, progress: 0%
2025-8-28 17:02:01-debug: Init all bundles start..., progress: 0%
2025-8-28 17:02:01-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:01-debug: Query asset bundle start, progress: 0%
2025-8-28 17:02:01-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:02:01-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:02:01-debug:   Number of all scenes: 8
2025-8-28 17:02:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:02:01-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:02:01-debug:   Number of other assets: 2032
2025-8-28 17:02:01-debug:   Number of all scripts: 261
2025-8-28 17:02:01-debug: // ---- build task Query asset bundle ---- (16ms)
2025-8-28 17:02:01-debug: [Build Memory track]: Query asset bundle start:213.03MB, end 214.39MB, increase: 1.36MB
2025-8-28 17:02:01-log: run build task Query asset bundle success in 16 ms√, progress: 5%
2025-8-28 17:02:01-debug: Query asset bundle start, progress: 5%
2025-8-28 17:02:01-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:01-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-8-28 17:02:01-debug: // ---- build task Query asset bundle ---- (1ms)
2025-8-28 17:02:01-debug: [Build Memory track]: Query asset bundle start:214.42MB, end 214.68MB, increase: 271.70KB
2025-8-28 17:02:01-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:01-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:02:01-debug: // ---- build task Sort some build options to settings.json ---- (-1ms)
2025-8-28 17:02:01-log: run build task Sort some build options to settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-8-28 17:02:01-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:02:01-debug: [Build Memory track]: Sort some build options to settings.json start:214.71MB, end 214.74MB, increase: 25.27KB
2025-8-28 17:02:01-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:02:01-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:02:01-debug: [Build Memory track]: Fill script data to settings start:214.76MB, end 213.30MB, increase: -1493.25KB
2025-8-28 17:02:01-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:02:01-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:01-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:02:01-debug: [Build Memory track]: Sort some build options to settings.json start:213.33MB, end 213.62MB, increase: 295.00KB
2025-8-28 17:02:01-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:02:11-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:02:11-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (1ms)
2025-8-28 17:02:12-debug: Query all assets info in project
2025-8-28 17:02:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:02:12-debug: Skip compress image, progress: 0%
2025-8-28 17:02:12-debug: Init all bundles start..., progress: 0%
2025-8-28 17:02:12-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:02:12-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:12-debug: Query asset bundle start, progress: 0%
2025-8-28 17:02:12-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:02:12-debug:   Number of all scenes: 8
2025-8-28 17:02:12-debug:   Number of all scripts: 261
2025-8-28 17:02:12-debug:   Number of other assets: 2032
2025-8-28 17:02:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:02:12-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:02:12-debug: // ---- build task Query asset bundle ---- (16ms)
2025-8-28 17:02:12-debug: Query asset bundle start, progress: 5%
2025-8-28 17:02:12-log: run build task Query asset bundle success in 16 ms√, progress: 5%
2025-8-28 17:02:12-debug: [Build Memory track]: Query asset bundle start:215.12MB, end 215.74MB, increase: 634.58KB
2025-8-28 17:02:12-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:12-debug: // ---- build task Query asset bundle ---- (1ms)
2025-8-28 17:02:12-debug: [Build Memory track]: Query asset bundle start:215.77MB, end 216.04MB, increase: 271.77KB
2025-8-28 17:02:12-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-8-28 17:02:12-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:02:12-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:12-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:02:12-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:02:12-debug: [Build Memory track]: Sort some build options to settings.json start:216.06MB, end 216.08MB, increase: 16.59KB
2025-8-28 17:02:12-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:02:12-debug: // ---- build task Fill script data to settings ---- (-1ms)
2025-8-28 17:02:12-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:02:12-log: run build task Fill script data to settings success in -1 h -1 min -1 s√, progress: 13%
2025-8-28 17:02:12-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:12-debug: [Build Memory track]: Fill script data to settings start:216.12MB, end 216.14MB, increase: 25.04KB
2025-8-28 17:02:12-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 17:02:12-debug: [Build Memory track]: Sort some build options to settings.json start:216.17MB, end 215.68MB, increase: -505.83KB
2025-8-28 17:02:27-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:02:27-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-8-28 17:02:28-debug: Query all assets info in project
2025-8-28 17:02:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:02:28-debug: Skip compress image, progress: 0%
2025-8-28 17:02:28-debug: Init all bundles start..., progress: 0%
2025-8-28 17:02:28-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:02:28-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:28-debug: Query asset bundle start, progress: 0%
2025-8-28 17:02:28-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:02:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:02:28-debug:   Number of all scripts: 261
2025-8-28 17:02:28-debug:   Number of all scenes: 8
2025-8-28 17:02:28-debug:   Number of other assets: 2032
2025-8-28 17:02:28-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:02:28-debug: // ---- build task Query asset bundle ---- (16ms)
2025-8-28 17:02:28-log: run build task Query asset bundle success in 16 ms√, progress: 5%
2025-8-28 17:02:28-debug: [Build Memory track]: Query asset bundle start:217.25MB, end 217.67MB, increase: 432.97KB
2025-8-28 17:02:28-debug: Query asset bundle start, progress: 5%
2025-8-28 17:02:28-debug: // ---- build task Query asset bundle ----
2025-8-28 17:02:28-debug: // ---- build task Query asset bundle ---- (1ms)
2025-8-28 17:02:28-debug: [Build Memory track]: Query asset bundle start:217.74MB, end 218.00MB, increase: 270.71KB
2025-8-28 17:02:28-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:02:28-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-8-28 17:02:28-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:28-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:02:28-debug: [Build Memory track]: Sort some build options to settings.json start:218.03MB, end 218.05MB, increase: 16.52KB
2025-8-28 17:02:28-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:02:28-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:02:28-debug: // ---- build task Fill script data to settings ---- (-1ms)
2025-8-28 17:02:28-log: run build task Fill script data to settings success in -1 h -1 min -1 s√, progress: 13%
2025-8-28 17:02:28-debug: [Build Memory track]: Fill script data to settings start:218.07MB, end 218.10MB, increase: 25.08KB
2025-8-28 17:02:28-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:02:28-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:02:28-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 17:02:28-debug: [Build Memory track]: Sort some build options to settings.json start:218.12MB, end 218.40MB, increase: 282.52KB
2025-8-28 17:02:44-debug: refresh db internal success
2025-8-28 17:02:45-debug: refresh db assets success
2025-8-28 17:02:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:02:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:02:45-debug: asset-db:refresh-all-database (130ms)
2025-8-28 17:03:12-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:03:12-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-8-28 17:03:13-debug: Query all assets info in project
2025-8-28 17:03:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:03:13-debug: Skip compress image, progress: 0%
2025-8-28 17:03:13-debug: Init all bundles start..., progress: 0%
2025-8-28 17:03:13-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:03:13-debug: Query asset bundle start, progress: 0%
2025-8-28 17:03:13-debug: // ---- build task Query asset bundle ----
2025-8-28 17:03:13-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:03:13-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:03:13-debug:   Number of all scenes: 8
2025-8-28 17:03:13-debug:   Number of other assets: 2032
2025-8-28 17:03:13-debug:   Number of all scripts: 261
2025-8-28 17:03:13-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:03:13-debug: // ---- build task Query asset bundle ---- (17ms)
2025-8-28 17:03:13-log: run build task Query asset bundle success in 17 ms√, progress: 5%
2025-8-28 17:03:13-debug: [Build Memory track]: Query asset bundle start:222.14MB, end 221.86MB, increase: -289.55KB
2025-8-28 17:03:13-debug: Query asset bundle start, progress: 5%
2025-8-28 17:03:13-debug: // ---- build task Query asset bundle ----
2025-8-28 17:03:13-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:03:13-debug: [Build Memory track]: Query asset bundle start:221.89MB, end 222.15MB, increase: 271.04KB
2025-8-28 17:03:13-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:03:13-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:03:13-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:03:13-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:03:13-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:03:13-debug: [Build Memory track]: Sort some build options to settings.json start:222.18MB, end 222.19MB, increase: 16.79KB
2025-8-28 17:03:13-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:03:13-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:03:13-debug: [Build Memory track]: Fill script data to settings start:222.22MB, end 222.24MB, increase: 16.15KB
2025-8-28 17:03:13-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:03:13-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:03:13-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:03:13-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:03:13-debug: [Build Memory track]: Sort some build options to settings.json start:222.26MB, end 222.55MB, increase: 290.38KB
2025-8-28 17:03:22-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:03:22-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-8-28 17:03:24-debug: Query all assets info in project
2025-8-28 17:03:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:03:24-debug: Skip compress image, progress: 0%
2025-8-28 17:03:24-debug: Init all bundles start..., progress: 0%
2025-8-28 17:03:24-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:03:24-debug: // ---- build task Query asset bundle ----
2025-8-28 17:03:24-debug: Query asset bundle start, progress: 0%
2025-8-28 17:03:24-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:03:24-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:03:24-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:03:24-debug:   Number of other assets: 2032
2025-8-28 17:03:24-debug:   Number of all scripts: 261
2025-8-28 17:03:24-debug:   Number of all scenes: 8
2025-8-28 17:03:24-debug: // ---- build task Query asset bundle ---- (16ms)
2025-8-28 17:03:24-debug: [Build Memory track]: Query asset bundle start:223.59MB, end 224.97MB, increase: 1.39MB
2025-8-28 17:03:24-log: run build task Query asset bundle success in 16 ms√, progress: 5%
2025-8-28 17:03:24-debug: Query asset bundle start, progress: 5%
2025-8-28 17:03:24-debug: // ---- build task Query asset bundle ----
2025-8-28 17:03:24-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:03:24-debug: [Build Memory track]: Query asset bundle start:225.00MB, end 225.26MB, increase: 271.20KB
2025-8-28 17:03:24-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:03:24-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:03:24-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:03:24-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 17:03:24-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 17:03:24-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:03:24-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:03:24-debug: [Build Memory track]: Sort some build options to settings.json start:225.29MB, end 225.32MB, increase: 24.99KB
2025-8-28 17:03:24-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:03:24-debug: [Build Memory track]: Fill script data to settings start:225.34MB, end 223.85MB, increase: -1531.77KB
2025-8-28 17:03:24-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:03:24-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:03:24-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:03:24-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:03:24-debug: [Build Memory track]: Sort some build options to settings.json start:223.87MB, end 224.22MB, increase: 355.64KB
2025-8-28 17:08:26-debug: refresh db internal success
2025-8-28 17:08:26-debug: refresh db assets success
2025-8-28 17:08:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:08:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:08:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 17:08:26-debug: asset-db:refresh-all-database (146ms)
2025-8-28 17:08:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:13:58-debug: refresh db internal success
2025-8-28 17:13:58-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:13:58-debug: refresh db assets success
2025-8-28 17:13:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:13:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:13:58-debug: asset-db:refresh-all-database (149ms)
2025-8-28 17:13:58-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-28 17:13:58-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 17:14:06-debug: Query all assets info in project
2025-8-28 17:14:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:14:06-debug: Skip compress image, progress: 0%
2025-8-28 17:14:06-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:14:06-debug: Query asset bundle start, progress: 0%
2025-8-28 17:14:06-debug: Init all bundles start..., progress: 0%
2025-8-28 17:14:06-debug: // ---- build task Query asset bundle ----
2025-8-28 17:14:06-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:14:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:14:06-debug:   Number of all scripts: 261
2025-8-28 17:14:06-debug:   Number of other assets: 2032
2025-8-28 17:14:06-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:14:06-debug:   Number of all scenes: 8
2025-8-28 17:14:06-log: run build task Query asset bundle success in 18 ms√, progress: 5%
2025-8-28 17:14:06-debug: // ---- build task Query asset bundle ---- (18ms)
2025-8-28 17:14:06-debug: [Build Memory track]: Query asset bundle start:208.41MB, end 210.22MB, increase: 1.82MB
2025-8-28 17:14:06-debug: // ---- build task Query asset bundle ----
2025-8-28 17:14:06-debug: Query asset bundle start, progress: 5%
2025-8-28 17:14:06-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:14:06-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:14:06-debug: [Build Memory track]: Query asset bundle start:210.25MB, end 210.52MB, increase: 271.25KB
2025-8-28 17:14:06-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:14:06-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:14:06-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 17:14:06-debug: [Build Memory track]: Sort some build options to settings.json start:210.54MB, end 210.57MB, increase: 25.20KB
2025-8-28 17:14:06-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:14:06-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 17:14:06-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:14:06-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 17:14:06-debug: [Build Memory track]: Fill script data to settings start:210.59MB, end 210.62MB, increase: 25.44KB
2025-8-28 17:14:06-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 17:14:06-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:14:06-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:14:06-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:14:06-debug: [Build Memory track]: Sort some build options to settings.json start:210.64MB, end 210.93MB, increase: 296.07KB
2025-8-28 17:14:06-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:14:33-debug: refresh db internal success
2025-8-28 17:14:33-debug: refresh db assets success
2025-8-28 17:14:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:14:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:14:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-8-28 17:14:33-debug: asset-db:refresh-all-database (102ms)
2025-8-28 17:14:33-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-28 17:17:21-debug: refresh db internal success
2025-8-28 17:17:21-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 17:17:21-debug: refresh db assets success
2025-8-28 17:17:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:17:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:17:21-debug: asset-db:refresh-all-database (136ms)
2025-8-28 17:17:24-debug: Query all assets info in project
2025-8-28 17:17:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:17:24-debug: Skip compress image, progress: 0%
2025-8-28 17:17:24-debug: Init all bundles start..., progress: 0%
2025-8-28 17:17:24-debug: // ---- build task Query asset bundle ----
2025-8-28 17:17:24-debug: Query asset bundle start, progress: 0%
2025-8-28 17:17:24-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:17:24-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:17:24-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:17:24-debug:   Number of all scenes: 8
2025-8-28 17:17:24-debug:   Number of other assets: 2032
2025-8-28 17:17:24-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:17:24-debug:   Number of all scripts: 261
2025-8-28 17:17:24-log: run build task Query asset bundle success in 18 ms√, progress: 5%
2025-8-28 17:17:24-debug: // ---- build task Query asset bundle ---- (18ms)
2025-8-28 17:17:24-debug: [Build Memory track]: Query asset bundle start:214.05MB, end 216.38MB, increase: 2.33MB
2025-8-28 17:17:24-debug: // ---- build task Query asset bundle ----
2025-8-28 17:17:24-debug: Query asset bundle start, progress: 5%
2025-8-28 17:17:24-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:17:24-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:17:24-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:17:24-debug: [Build Memory track]: Query asset bundle start:216.40MB, end 216.67MB, increase: 271.29KB
2025-8-28 17:17:24-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:17:24-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:17:24-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:17:24-debug: [Build Memory track]: Sort some build options to settings.json start:216.69MB, end 216.71MB, increase: 16.64KB
2025-8-28 17:17:24-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:17:24-debug: [Build Memory track]: Fill script data to settings start:216.74MB, end 216.75MB, increase: 16.05KB
2025-8-28 17:17:24-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:17:24-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:17:24-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:17:24-debug: // ---- build task Sort some build options to settings.json ---- (2ms)
2025-8-28 17:17:24-log: run build task Sort some build options to settings.json success in 2 ms√, progress: 15%
2025-8-28 17:17:24-debug: [Build Memory track]: Sort some build options to settings.json start:216.78MB, end 217.06MB, increase: 290.95KB
2025-8-28 17:17:48-debug: refresh db internal success
2025-8-28 17:17:48-debug: refresh db assets success
2025-8-28 17:17:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:17:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:17:48-debug: asset-db:refresh-all-database (97ms)
2025-8-28 17:17:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 17:17:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
