import { _decorator, Component, EventTouch, Label, Node } from 'cc';
import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { DataMgr } from 'db://assets/scripts/Data/DataManager';
import { EventMgr } from 'db://assets/scripts/event/EventManager';
import { MyApp } from 'db://assets/scripts/MyApp';
import { logError } from 'db://assets/scripts/Utils/Logger';
import { ButtonPlus } from '../../../../common/components/button/ButtonPlus';
import { TopBlockInputUI } from '../../../../common/TopBlockInputUI';
import { UIMgr } from '../../../../UIMgr';
import { PlaneCombineResultUI } from '../../PlaneCombineResultUI';
import { PlaneUIEvent } from '../../PlaneEvent';
import { TabStatus } from '../../PlaneTypes';

const { ccclass, property } = _decorator;

@ccclass('CombineDisplay')
export class CombineDisplay extends Component {
    @property(Node)
    topGrid: Node = null
    @property(Node)
    bottomGrid: Node = null
    @property(Label)
    tip: Label = null
    @property(ButtonPlus)
    combineOnceBtn: ButtonPlus = null
    @property(ButtonPlus)
    combineAllBtn: ButtonPlus = null
    private _tips: string[] = [
        "选择你想合成的装备",
        "还需要2件相同装备",
        "还需要1件相同装备",
        "一切准备就绪!",
    ]

    onLoad(): void {
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)
        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this, 1)
        this.bottomGrid.getComponentsInChildren(ButtonPlus).forEach(btn => {
            btn.addClick(this.onBottomBtnClick, this)
        })
        this.combineOnceBtn.addClick(this.onCombineOnceClick, this)
        this.combineAllBtn.addClick(this.onCombineAllClick, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg.bind(this))
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }

    private onTabChange(tabStatus: TabStatus) {
        if (tabStatus == TabStatus.Bag) {
            this.node.active = false;
            return
        }
        this.node.active = true;
        DataMgr.equip.eqCombine.clear();
        DataMgr.equip.eqCombine.prepareCombineAll();
        this.refreshDisplay();
    }

    private refreshDisplay() {
        const mergeLen = DataMgr.equip.eqCombine.size()
        this.bottomGrid.getComponentsInChildren(Label).forEach((label, index) => {
            const info = DataMgr.equip.eqCombine.getByPos(index)
            if (info) {
                label.string = MyApp.lubanTables.TbEquip.get(info.item.item_id)?.name
            } else {
                label.string = "合成材料"
            }
        });
        this.tip.string = this._tips[mergeLen]
        if (DataMgr.equip.eqCombine.isFull()) {
            const nextLev = DataMgr.equip.eqCombine.getCombineResult()
            if (nextLev) {
                this.topGrid.getComponentInChildren(Label).string = nextLev.name
            } else {
                logError("PlaneUI", `cant get merge result no pos1 equip info`)
            }
        } else {
            this.topGrid.getComponentInChildren(Label).string = "合成结果"
        }
        this.combineOnceBtn.node.active = mergeLen == 3
        this.combineAllBtn.node.active = mergeLen != 3
    }

    private onBagItemClick(item: csproto.cs.ICSItem) {
        if (!this.node.active) return
        const info = DataMgr.equip.eqCombine.getByGuid(item.guid)
        if (info) {
            DataMgr.equip.eqCombine.deleteByGuid(item.guid)
        } else {
            if (!DataMgr.equip.eqCombine.add(item)) return
        }
        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)
        this.refreshDisplay()
    }

    private onBottomBtnClick(event: EventTouch) {
        const nd = event.target as Node
        const pos = parseInt(nd.name)
        if (!DataMgr.equip.eqCombine.getByPos(pos)) return
        DataMgr.equip.eqCombine.deleteByPos(pos)
        this.refreshDisplay()
        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)
    }

    private onCombineResultMsg(msg: csproto.cs.IS2CEquipCombine) {
        DataMgr.bag.refreshItems();
        DataMgr.equip.eqCombine.clear();
        this.refreshDisplay()
        UIMgr.openUI(PlaneCombineResultUI, msg.results)
        UIMgr.hideUI(TopBlockInputUI)
    }

    private onCombineOnceClick() {
        if (!DataMgr.equip.eqCombine.isFull()) return
        UIMgr.openUI(TopBlockInputUI)
        DataMgr.equip.eqCombine.combine();
    }

    private onCombineAllClick() {
        if (DataMgr.equip.eqCombine.isFull()) return
        UIMgr.openUI(TopBlockInputUI)
        DataMgr.equip.eqCombine.combineAll()
    }
}

