{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/TrackData.ts"], "names": ["TrackData", "Tools", "trackID", "type", "startX", "startY", "control1X", "control1Y", "control2X", "control2Y", "endX", "endY", "loadJson", "data", "hasOwnProperty", "parseInt", "id", "tpe", "variable", "value", "points", "split", "start", "stringToPoint", "x", "y", "end", "control1", "control2", "error", "console"], "mappings": ";;;qCAEaA,S;;;;;;;;;;;;;;AAFJC,MAAAA,K,iBAAAA,K;;;;;;;2BAEID,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACnBE,OADmB,GACT,CADS;AACN;AADM,eAEnBC,IAFmB,GAEZ,CAFY;AAET;AAFS,eAGnBC,MAHmB,GAGV,CAHU;AAGP;AAHO,eAInBC,MAJmB,GAIV,CAJU;AAIP;AAJO,eAKnBC,SALmB,GAKP,CALO;AAKJ;AALI,eAMnBC,SANmB,GAMP,CANO;AAMJ;AANI,eAOnBC,SAPmB,GAOP,CAPO;AAOJ;AAPI,eAQnBC,SARmB,GAQP,CARO;AAQJ;AARI,eASnBC,IATmB,GASZ,CATY;AAST;AATS,eAUnBC,IAVmB,GAUZ,CAVY;AAAA;;AAUT;;AAEV;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,IAAD,EAAO;AACX,cAAIA,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B;AAC3B,iBAAKZ,OAAL,GAAea,QAAQ,CAACF,IAAI,CAACG,EAAN,CAAvB;AACH;;AACD,cAAIH,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,iBAAKX,IAAL,GAAYY,QAAQ,CAACF,IAAI,CAACI,GAAN,CAApB;AACH;;AACD,cAAIJ,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC;AAC9B,kBAAMI,QAAQ,GAAGL,IAAI,CAACM,KAAtB;;AACA,gBAAI;AACA,sBAAQ,KAAKhB,IAAb;AACI,qBAAK,CAAL;AACA,qBAAK,CAAL;AAAQ;AACJ,0BAAMiB,MAAM,GAAGF,QAAQ,CAACG,KAAT,CAAe,GAAf,CAAf;AACA,0BAAMC,KAAK,GAAG;AAAA;AAAA,wCAAMC,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAd;AACA,yBAAKhB,MAAL,GAAckB,KAAK,CAACE,CAApB;AACA,yBAAKnB,MAAL,GAAciB,KAAK,CAACG,CAApB;AACA,0BAAMC,GAAG,GAAG;AAAA;AAAA,wCAAMH,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAZ;AACA,yBAAKV,IAAL,GAAYgB,GAAG,CAACF,CAAhB;AACA,yBAAKb,IAAL,GAAYe,GAAG,CAACD,CAAhB;AACA;AACH;;AACD,qBAAK,EAAL;AAAS;AACL,0BAAMC,GAAG,GAAG;AAAA;AAAA,wCAAMH,aAAN,CAAoBL,QAApB,EAA8B,GAA9B,CAAZ;AACA,yBAAKR,IAAL,GAAYgB,GAAG,CAACF,CAAhB;AACA,yBAAKb,IAAL,GAAYe,GAAG,CAACD,CAAhB;AACA;AACH;;AACD;AAAS;AACL,0BAAML,MAAM,GAAGF,QAAQ,CAACG,KAAT,CAAe,GAAf,CAAf;AACA,0BAAMC,KAAK,GAAG;AAAA;AAAA,wCAAMC,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAd;AACA,yBAAKhB,MAAL,GAAckB,KAAK,CAACE,CAApB;AACA,yBAAKnB,MAAL,GAAciB,KAAK,CAACG,CAApB;;AAEA,wBAAI,KAAKtB,IAAL,KAAc,CAAd,IAAmB,KAAKA,IAAL,KAAc,CAArC,EAAwC;AACpC,4BAAMwB,QAAQ,GAAG;AAAA;AAAA,0CAAMJ,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAjB;AACA,2BAAKd,SAAL,GAAiBqB,QAAQ,CAACH,CAA1B;AACA,2BAAKjB,SAAL,GAAiBoB,QAAQ,CAACF,CAA1B;AAEA,4BAAMG,QAAQ,GAAG;AAAA;AAAA,0CAAML,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAjB;AACA,2BAAKZ,SAAL,GAAiBoB,QAAQ,CAACJ,CAA1B;AACA,2BAAKf,SAAL,GAAiBmB,QAAQ,CAACH,CAA1B;AAEA,4BAAMC,GAAG,GAAG;AAAA;AAAA,0CAAMH,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAZ;AACA,2BAAKV,IAAL,GAAYgB,GAAG,CAACF,CAAhB;AACA,2BAAKb,IAAL,GAAYe,GAAG,CAACD,CAAhB;AACH,qBAZD,MAYO,IAAI,KAAKtB,IAAL,KAAc,CAAd,IAAmB,KAAKA,IAAL,KAAc,CAArC,EAAwC;AAC3C,4BAAMuB,GAAG,GAAG;AAAA;AAAA,0CAAMH,aAAN,CAAoBH,MAAM,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAZ;AACA,2BAAKV,IAAL,GAAYgB,GAAG,CAACF,CAAhB;AACA,2BAAKb,IAAL,GAAYe,GAAG,CAACD,CAAhB;AACH;;AACD;AACH;AA1CL;AA4CH,aA7CD,CA6CE,OAAOI,KAAP,EAAc;AACZC,cAAAA,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;AACH;AACJ;AACJ;;AA1EkB,O", "sourcesContent": ["import { Tools } from \"../utils/Tools\";\r\n\r\nexport class TrackData {\r\n    trackID = 0; // 轨迹 ID\r\n    type = 0; // 轨迹类型\r\n    startX = 0; // 起点 X 坐标\r\n    startY = 0; // 起点 Y 坐标\r\n    control1X = 0; // 控制点 1 的 X 坐标\r\n    control1Y = 0; // 控制点 1 的 Y 坐标\r\n    control2X = 0; // 控制点 2 的 X 坐标\r\n    control2Y = 0; // 控制点 2 的 Y 坐标\r\n    endX = 0; // 终点 X 坐标\r\n    endY = 0; // 终点 Y 坐标\r\n\r\n    /**\r\n     * 加载 JSON 数据并解析轨迹信息\r\n     * @param {Object} data JSON 数据\r\n     */\r\n    loadJson(data) {\r\n        if (data.hasOwnProperty('id')) {\r\n            this.trackID = parseInt(data.id);\r\n        }\r\n        if (data.hasOwnProperty('tpe')) {\r\n            this.type = parseInt(data.tpe);\r\n        }\r\n        if (data.hasOwnProperty('value')) {\r\n            const variable = data.value;\r\n            try {\r\n                switch (this.type) {\r\n                    case 2:\r\n                    case 3: {\r\n                        const points = variable.split(';');\r\n                        const start = Tools.stringToPoint(points[0], ',');\r\n                        this.startX = start.x;\r\n                        this.startY = start.y;\r\n                        const end = Tools.stringToPoint(points[1], ',');\r\n                        this.endX = end.x;\r\n                        this.endY = end.y;\r\n                        break;\r\n                    }\r\n                    case 11: {\r\n                        const end = Tools.stringToPoint(variable, ',');\r\n                        this.endX = end.x;\r\n                        this.endY = end.y;\r\n                        break;\r\n                    }\r\n                    default: {\r\n                        const points = variable.split(';');\r\n                        const start = Tools.stringToPoint(points[0], ',');\r\n                        this.startX = start.x;\r\n                        this.startY = start.y;\r\n\r\n                        if (this.type === 0 || this.type === 4) {\r\n                            const control1 = Tools.stringToPoint(points[1], ',');\r\n                            this.control1X = control1.x;\r\n                            this.control1Y = control1.y;\r\n\r\n                            const control2 = Tools.stringToPoint(points[2], ',');\r\n                            this.control2X = control2.x;\r\n                            this.control2Y = control2.y;\r\n\r\n                            const end = Tools.stringToPoint(points[3], ',');\r\n                            this.endX = end.x;\r\n                            this.endY = end.y;\r\n                        } else if (this.type === 1 || this.type === 5) {\r\n                            const end = Tools.stringToPoint(points[1], ',');\r\n                            this.endX = end.x;\r\n                            this.endY = end.y;\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                console.error('Error parsing track data:', error);\r\n            }\r\n        }\r\n    }\r\n}"]}