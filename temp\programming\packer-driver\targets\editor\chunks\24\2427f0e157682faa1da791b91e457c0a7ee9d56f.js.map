{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts"], "names": ["_decorator", "ccclass", "property", "BulletEventData"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;AACA;;iCAEaG,e,WADZF,OAAO,CAAC,iBAAD,C,gBAAR,MACaE,eADb,CAC6B,E", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * 子弹事件数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"BulletEventData\")\r\nexport class BulletEventData {\r\n\r\n}"]}