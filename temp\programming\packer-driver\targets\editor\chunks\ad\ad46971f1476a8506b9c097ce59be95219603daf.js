System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, CCString, assetManager, CCInteger, AudioClip, LevelDataEventTriggerLog, LevelDataEventTriggerType, newTrigger, LevelWaveParam, LevelElemUI, LevelCondition, LevelDataEventCondtionType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _dec7, _dec8, _dec9, _class4, _class5, _descriptor, _descriptor2, _crd, ccclass, property, LevelEventTrigger, LevelEventUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "../../../leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "../../../leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "../../../leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewTrigger(extras) {
    _reporterNs.report("newTrigger", "../../../leveldata/trigger/newTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerAudio(extras) {
    _reporterNs.report("LevelDataEventTriggerAudio", "../../../leveldata/trigger/LevelDataEventTriggerAudio", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "../../../leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelWaveParam(extras) {
    _reporterNs.report("LevelWaveParam", "./LevelWaveUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelElemUI(extras) {
    _reporterNs.report("LevelElemUI", "./LevelElemUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelCondition(extras) {
    _reporterNs.report("LevelCondition", "./LevelCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "../../../leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionWave(extras) {
    _reporterNs.report("LevelDataEventCondtionWave", "../../../leveldata/condition/LevelDataEventCondtionWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../../leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      CCInteger = _cc.CCInteger;
      AudioClip = _cc.AudioClip;
    }, function (_unresolved_2) {
      LevelDataEventTriggerLog = _unresolved_2.LevelDataEventTriggerLog;
    }, function (_unresolved_3) {
      LevelDataEventTriggerType = _unresolved_3.LevelDataEventTriggerType;
    }, function (_unresolved_4) {
      newTrigger = _unresolved_4.newTrigger;
    }, function (_unresolved_5) {
      LevelWaveParam = _unresolved_5.LevelWaveParam;
    }, function (_unresolved_6) {
      LevelElemUI = _unresolved_6.LevelElemUI;
    }, function (_unresolved_7) {
      LevelCondition = _unresolved_7.LevelCondition;
    }, function (_unresolved_8) {
      LevelDataEventCondtionType = _unresolved_8.LevelDataEventCondtionType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e51d2kF0wFItYdlKYuYxX23", "LevelEventUI", undefined);

      __checkObsolete__(['_decorator', 'Prefab', 'CCString', 'assetManager', 'CCInteger', 'AudioClip']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelEventTrigger", LevelEventTrigger = (_dec = ccclass('LevelEventTrigger'), _dec2 = property({
        type: CCString,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log;
        }

      }), _dec3 = property({
        type: AudioClip,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio;
        }

      }), _dec4 = property({
        type: Prefab,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec5 = property({
        type: CCInteger,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec6 = property({
        type: [_crd && LevelWaveParam === void 0 ? (_reportPossibleCrUseOfLevelWaveParam({
          error: Error()
        }), LevelWaveParam) : LevelWaveParam],

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec(_class = (_class2 = class LevelEventTrigger {
        constructor() {
          this._index = 0;
          this.data = new (_crd && LevelDataEventTriggerLog === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerLog({
            error: Error()
          }), LevelDataEventTriggerLog) : LevelDataEventTriggerLog)();
          this._audio = null;
          this._wave = null;
          this._params = [];
        }

        get type() {
          return this.data._type;
        }

        set type(value) {
          if (this.data._type != value) {
            this.data = (_crd && newTrigger === void 0 ? (_reportPossibleCrUseOfnewTrigger({
              error: Error()
            }), newTrigger) : newTrigger)({
              _type: value
            });
          }
        }

        get message() {
          return this.data.message;
        }

        set message(value) {
          this.data.message = value;
        }

        get audio() {
          return this._audio;
        }

        set audio(value) {
          this._audio = value;

          if (value) {
            this.data.audioUUID = value.uuid;
          } else {
            this.data.audioUUID = "";
          }
        }

        get wave() {
          return this._wave;
        }

        set wave(value) {
          this._wave = value;

          if (value) {
            this.data.waveUUID = value.uuid;
          } else {
            this.data.waveUUID = "";
          }
        }

        get planeID() {
          return this.data.planeID;
        }

        set planeID(value) {
          this.data.planeID = value;
        }

        get params() {
          return this._params;
        }

        set params(value) {
          this._params = value;
          let waveTrigger = this.data;
          waveTrigger.params = {};

          for (let p of this._params) {
            waveTrigger.params[p.name] = p.value;
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "message", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "message"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "audio", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "audio"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "wave", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "wave"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "planeID", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "planeID"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "params", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "params"), _class2.prototype)), _class2)) || _class));

      _export("LevelEventUI", LevelEventUI = (_dec7 = ccclass('LevelEventUI'), _dec8 = property([_crd && LevelCondition === void 0 ? (_reportPossibleCrUseOfLevelCondition({
        error: Error()
      }), LevelCondition) : LevelCondition]), _dec9 = property([LevelEventTrigger]), _dec7(_class4 = (_class5 = class LevelEventUI extends (_crd && LevelElemUI === void 0 ? (_reportPossibleCrUseOfLevelElemUI({
        error: Error()
      }), LevelElemUI) : LevelElemUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "conditions", _descriptor, this);

          _initializerDefineProperty(this, "triggers", _descriptor2, this);
        }

        update(dt) {
          for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;

            if (cond.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
              error: Error()
            }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave && cond.data.targetElemID != "" && cond._targetElem == null) {
              const elems = this.node.scene.getComponentsInChildren(_crd && LevelElemUI === void 0 ? (_reportPossibleCrUseOfLevelElemUI({
                error: Error()
              }), LevelElemUI) : LevelElemUI);

              for (let elem of elems) {
                if (elem.elemID == cond.data.targetElemID) {
                  cond._targetElem = elem;
                  break;
                }
              }
            }
          }
        }

        initByLevelData(data) {
          super.initByLevelData(data);

          if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
              const condition = new (_crd && LevelCondition === void 0 ? (_reportPossibleCrUseOfLevelCondition({
                error: Error()
              }), LevelCondition) : LevelCondition)();
              condition._index = i;
              condition.data = data.conditions[i];
              this.conditions.push(condition);
            }
          }

          if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
              const trigger = new LevelEventTrigger();
              trigger._index = i;
              trigger.data = data.triggers[i];
              this.triggers.push(trigger);

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio) {
                let uuid = trigger.data.audioUUID;
                assetManager.loadAny({
                  uuid: uuid
                }, (err, audio) => {
                  if (err) {
                    console.error("LevelEventUI initByLevelData load audio err", err);
                    return;
                  }

                  trigger._audio = audio;
                });
              }

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave) {
                let waveTrigger = trigger.data;
                let uuid = waveTrigger.waveUUID;
                assetManager.loadAny({
                  uuid: uuid
                }, (err, prefab) => {
                  if (err) {
                    console.error("LevelEventUI initByLevelData load wave prefab err", err);
                    return;
                  }

                  trigger._wave = prefab;
                });
                trigger._params = [];

                for (let k in waveTrigger.params) {
                  let param = new (_crd && LevelWaveParam === void 0 ? (_reportPossibleCrUseOfLevelWaveParam({
                    error: Error()
                  }), LevelWaveParam) : LevelWaveParam)();
                  param.name = k;
                  param.value = waveTrigger.params[k];

                  trigger._params.push(param);
                }
              }
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class5.prototype, "conditions", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class5.prototype, "triggers", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ad46971f1476a8506b9c097ce59be95219603daf.js.map