System.register(["__unresolved_0", "long", "protobufjs/minimal.js"], function (_export, _context) {
  "use strict";

  var _cjsLoader, _req, _req0, _cjsExports, __cjsMetaURL;

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _cjsLoader = _unresolved_.default;
    }, function (_long) {
      _req = _long.__cjsMetaURL;
    }, function (_protobufjsMinimalJs) {
      _req0 = _protobufjsMinimalJs.__cjsMetaURL;
    }],
    execute: function () {
      _export("__cjsMetaURL", __cjsMetaURL = _context.meta.url);

      _cjsLoader.define(__cjsMetaURL, function (exports, require, module, __filename, __dirname) {
        // #region ORIGINAL CODE
        "use strict";

        var Long = require("long");

        var $protobuf = require("protobufjs/minimal.js");

        var $util = $protobuf.util;
        $util.Long = Long;
        $protobuf.configure(); // #endregion ORIGINAL CODE

        _export("default", _cjsExports = module.exports);
      }, () => ({
        'long': _req,
        'protobufjs/minimal.js': _req0
      }));
    }
  };
});
//# sourceMappingURL=4c66674e3bc8bb075d68c882061879b9bbfde183.js.map