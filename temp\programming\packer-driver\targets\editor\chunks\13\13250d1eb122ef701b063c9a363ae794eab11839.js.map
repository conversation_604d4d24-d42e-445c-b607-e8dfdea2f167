{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts"], "names": ["LevelDataEventCondtion", "LevelDataEventCondtionComb", "LevelDataEventCondtionType", "constructor", "comb", "type", "And", "_type", "DelayTime"], "mappings": ";;;iBAcaA,sB;;;;;;;;;;;;;4CAXDC,0B,0BAAAA,0B;AAAAA,QAAAA,0B,CAAAA,0B;AAAAA,QAAAA,0B,CAAAA,0B;eAAAA,0B;;;4CAKAC,0B,0BAAAA,0B;AAAAA,QAAAA,0B,CAAAA,0B;AAAAA,QAAAA,0B,CAAAA,0B;AAAAA,QAAAA,0B,CAAAA,0B;eAAAA,0B;;;wCAMCF,sB,GAAN,MAAMA,sBAAN,CAA6B;AAGhCG,QAAAA,WAAW,CAACC,IAAD,EAAmCC,IAAnC,EAAsE;AAAA,eAF1ED,IAE0E,GAFvCH,0BAA0B,CAACK,GAEY;AAAA,eAD1EC,KAC0E,GADtCL,0BAA0B,CAACM,SACW;AAC7E,eAAKD,KAAL,GAAaF,IAAb,EACA,KAAKD,IAAL,GAAYA,IADZ;AAEH;;AAN+B,O", "sourcesContent": ["import { LevelDataEventCondtionDelayDistance } from \"./LevelDataEventCondtionDelayDistance\";\r\nimport { LevelDataEventCondtionDelayTime } from \"./LevelDataEventCondtionDelayTime\";\r\n\r\nexport enum LevelDataEventCondtionComb {\r\n    And = 0,\r\n    Or = 1,\r\n}\r\n\r\nexport enum LevelDataEventCondtionType {\r\n    DelayTime = 0, \r\n    DelayDistance = 1,\r\n    Wave = 2,\r\n}\r\n\r\nexport class LevelDataEventCondtion {\r\n    public comb: LevelDataEventCondtionComb = LevelDataEventCondtionComb.And;\r\n    public _type: LevelDataEventCondtionType = LevelDataEventCondtionType.DelayTime;\r\n    constructor(comb: LevelDataEventCondtionComb, type : LevelDataEventCondtionType) {\r\n        this._type = type,\r\n        this.comb = comb;\r\n    }\r\n\r\n}\r\n"]}