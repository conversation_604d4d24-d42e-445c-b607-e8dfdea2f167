{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts"], "names": ["_decorator", "Component", "Label", "Node", "DataMgr", "EventMgr", "MyApp", "ButtonPlus", "PlaneUIEvent", "TabStatus", "ccclass", "property", "BagItem", "_planeEquipInfo", "_tabStatus", "None", "onLoad", "getComponent", "addClick", "onClick", "onDestroy", "targetOff", "<PERSON><PERSON>", "mask", "active", "selectedIcon", "equip", "eqCombine", "isFull", "emit", "BagItemClick", "updateMergeStatus", "size", "info", "getByGuid", "guid", "isCanCombineWith", "renderPlanePart", "planeEquipInfo", "tabStatus", "itemCfg", "lubanTables", "TbEquip", "get", "item_id", "getComponentInChildren", "string", "name", "quality", "onRenderItem", "item", "TbItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAE9BC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGjBY,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ,CAACR,IAAD,C,2BAJb,MACaS,OADb,SAC6BX,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAM3BY,eAN2B,GAMW,IANX;AAAA,eAO3BC,UAP2B,GAOH;AAAA;AAAA,sCAAUC,IAPP;AAAA;;AASzBC,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA8BC,QAA9B,CAAuC,KAAKC,OAA5C,EAAqD,IAArD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,OAAO,GAAG;AACd,cAAI,CAAC,KAAKN,eAAV,EAA2B;;AAC3B,cAAI,KAAKC,UAAL,IAAmB;AAAA;AAAA,sCAAUQ,KAAjC,EAAwC;AACpC,gBAAI,KAAKC,IAAL,CAAUC,MAAV,IAAoB,CAAC,KAAKC,YAA9B,EAA4C;AACxC;AACH;;AACD,gBAAI,CAAC,KAAKF,IAAL,CAAUC,MAAX,IAAqB;AAAA;AAAA,oCAAQE,KAAR,CAAcC,SAAd,CAAwBC,MAAxB,EAAzB,EAA2D;AACvD;AACH;AACJ;;AACD;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,4CAAaC,YAA3B,EAAyC,KAAKjB,eAA9C;AACH;;AAEOkB,QAAAA,iBAAiB,GAAG;AACxB,cAAI;AAAA;AAAA,kCAAQL,KAAR,CAAcC,SAAd,CAAwBK,IAAxB,KAAiC,CAArC,EAAwC;AACpC,kBAAMC,IAAI,GAAG;AAAA;AAAA,oCAAQP,KAAR,CAAcC,SAAd,CAAwBO,SAAxB,CAAkC,KAAKrB,eAAL,CAAqBsB,IAAvD,CAAb;;AACA,gBAAIF,IAAJ,EAAU;AACN,mBAAKR,YAAL,CAAkBD,MAAlB,GAA2B,IAA3B;AACA,mBAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACH,aAHD,MAGO;AACH,mBAAKC,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,mBAAKD,IAAL,CAAUC,MAAV,GAAmB,CAAC;AAAA;AAAA,sCAAQE,KAAR,CAAcC,SAAd,CAAwBS,gBAAxB,CAAyC,KAAKvB,eAA9C,CAApB;AACH;AACJ,WATD,MASO;AACH,iBAAKY,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,iBAAKD,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;AACJ;;AAEDa,QAAAA,eAAe,CAACC,cAAD,EAAqCC,SAArC,EAA2D;AACtE,eAAKzB,UAAL,GAAkByB,SAAlB;AACA,eAAK1B,eAAL,GAAuByB,cAAvB;AACA,eAAKb,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,eAAKD,IAAL,CAAUC,MAAV,GAAmB,KAAnB;;AACA,cAAIe,SAAS,IAAI;AAAA;AAAA,sCAAUjB,KAA3B,EAAkC;AAC9B,iBAAKS,iBAAL;AACH;;AACD,gBAAMS,OAAO,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8B,KAAK9B,eAAL,CAAqB+B,OAAnD,CAAhB;AACA,eAAKC,sBAAL,CAA4B3C,KAA5B,EAAmC4C,MAAnC,GAA4C,CAAAN,OAAO,QAAP,YAAAA,OAAO,CAAEO,IAAT,IAAiB,OAAMP,OAAP,oBAAOA,OAAO,CAAEQ,OAAQ,GAApF;AAEH;;AAEDC,QAAAA,YAAY,CAACC,IAAD,EAA2B;AAAA;;AACnC,eAAKzB,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,eAAKD,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA,eAAKqB,sBAAL,CAA4B3C,KAA5B,EAAmC4C,MAAnC,4BAA4C;AAAA;AAAA,8BAAML,WAAN,CAAkBU,MAAlB,CAAyBR,GAAzB,CAA6BO,IAAI,CAACN,OAAlC,CAA5C,qBAA4C,sBAA4CG,IAAxF;AACA,eAAKlC,eAAL,GAAuB,IAAvB;AACH;;AAhEkC,O;;;;;iBAEd,I;;;;;;;iBAER,I", "sourcesContent": ["import { _decorator, Component, Label, Node } from \"cc\";\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { DataMgr } from \"db://assets/scripts/Data/DataManager\";\nimport { EventMgr } from \"db://assets/scripts/event/EventManager\";\nimport { MyApp } from \"db://assets/scripts/MyApp\";\nimport { ButtonPlus } from \"../../../../common/components/button/ButtonPlus\";\nimport { PlaneUIEvent } from \"../../PlaneEvent\";\nimport { TabStatus } from \"../../PlaneTypes\";\nconst { ccclass, property } = _decorator;\n\n@ccclass('BagItem')\nexport class BagItem extends Component {\n    @property(Node)\n    selectedIcon: Node = null;\n    @property(Node)\n    mask: Node = null;\n\n    private _planeEquipInfo: csproto.cs.ICSItem = null;\n    private _tabStatus: TabStatus = TabStatus.None;\n\n    protected onLoad(): void {\n        this.getComponent(ButtonPlus).addClick(this.onClick, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onClick() {\n        if (!this._planeEquipInfo) return\n        if (this._tabStatus == TabStatus.Merge) {\n            if (this.mask.active && !this.selectedIcon) {\n                return\n            }\n            if (!this.mask.active && DataMgr.equip.eqCombine.isFull()) {\n                return\n            }\n        }\n        EventMgr.emit(PlaneUIEvent.BagItemClick, this._planeEquipInfo)\n    }\n\n    private updateMergeStatus() {\n        if (DataMgr.equip.eqCombine.size() > 0) {\n            const info = DataMgr.equip.eqCombine.getByGuid(this._planeEquipInfo.guid)\n            if (info) {\n                this.selectedIcon.active = true;\n                this.mask.active = true;\n            } else {\n                this.selectedIcon.active = false;\n                this.mask.active = !DataMgr.equip.eqCombine.isCanCombineWith(this._planeEquipInfo)\n            }\n        } else {\n            this.selectedIcon.active = false;\n            this.mask.active = false;\n        }\n    }\n\n    renderPlanePart(planeEquipInfo: csproto.cs.ICSItem, tabStatus: TabStatus) {\n        this._tabStatus = tabStatus;\n        this._planeEquipInfo = planeEquipInfo;\n        this.selectedIcon.active = false;\n        this.mask.active = false;\n        if (tabStatus == TabStatus.Merge) {\n            this.updateMergeStatus()\n        }\n        const itemCfg = MyApp.lubanTables.TbEquip.get(this._planeEquipInfo.item_id)\n        this.getComponentInChildren(Label).string = itemCfg?.name + `(品质:${itemCfg?.quality})`\n\n    }\n\n    onRenderItem(item: csproto.cs.ICSItem) {\n        this.selectedIcon.active = false;\n        this.mask.active = false;\n        this.getComponentInChildren(Label).string = MyApp.lubanTables.TbItem.get(item.item_id)?.name;\n        this._planeEquipInfo = null;\n    }\n}"]}