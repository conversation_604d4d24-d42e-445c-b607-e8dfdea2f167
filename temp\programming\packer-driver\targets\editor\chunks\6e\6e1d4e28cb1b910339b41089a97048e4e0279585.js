System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, csproto, MyApp, DataMgr, EquipCombine, _crd;

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResEquip(extras) {
    _reporterNs.report("ResEquip", "../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../DataManager", _context.meta, extras);
  }

  _export("EquipCombine", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      DataMgr = _unresolved_4.DataMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d8311fct6JJDIH+3bgjyKWA", "EquipCombine", undefined);

      _export("EquipCombine", EquipCombine = class EquipCombine {
        constructor() {
          this._combineItems = [];
          this._combineEquipConfig = null;
          this._combineAllInfo = [];
        }

        clear(num = 3) {
          this._combineEquipConfig = null;
          this._combineItems = [];

          this._combineItems.fill({}, 0, num);
        }

        isMainMat(itemid) {
          const cfgs = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEquip.getDataList().filter(v => v.consumeItems.length > 0 && v.consumeItems[0].id == itemid);
          return cfgs.length > 0;
        }

        isFull() {
          return this._combineItems.findIndex(v => v.guid.eq(0)) == -1 && this._combineItems.length > 0;
        }

        add(item, pos) {
          if (this.isFull()) return false;

          if (!pos) {
            pos = this._combineItems.findIndex(v => v.guid.eq(0));
          }

          if (pos < 0) {
            const tbEquip = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbEquip;
            const equipCfgList = tbEquip.getDataList().filter(v => {
              var _v$consumeItems$, _v$consumeItems$2;

              return ((_v$consumeItems$ = v.consumeItems[0]) == null ? void 0 : _v$consumeItems$.id) == item.item_id && ((_v$consumeItems$2 = v.consumeItems[0]) == null ? void 0 : _v$consumeItems$2.num) == item.count;
            });

            if (equipCfgList.length == 0) {
              return false;
            }

            this._combineEquipConfig = equipCfgList[0];
            this.clear(this._combineEquipConfig.consumeItems.length);
          } else if (!this.isCanCombineWith(item)) {
            return false;
          }

          this._combineItems[pos] = item;
          return true;
        }

        size() {
          return this._combineItems.filter(v => v.guid.gt(0)).length;
        }

        deleteByGuid(guid) {
          const info = this.getByGuid(guid);

          if (info) {
            if (info.pos == 0) {
              this.clear();
            } else {
              this._combineItems[info.pos] = {};
            }
          }
        }

        deleteByPos(pos) {
          if (pos == 0) {
            this.clear();
          } else {
            this._combineItems[pos] = {};
          }
        }

        isCanCombineWith(eqItem) {
          if (this._combineEquipConfig == null) {
            return false;
          }

          const consumeItems = this._combineEquipConfig.consumeItems.slice(1);

          return consumeItems.findIndex(v => v.id == eqItem.item_id && v.num == eqItem.count) >= 0;
        }

        getByGuid(guid) {
          const index = this._combineItems.findIndex(v => v.guid.eq(guid));

          if (index < 0) {
            return null;
          }

          return {
            pos: index,
            item: this._combineItems[index]
          };
        }

        getByPos(pos) {
          const info = this._combineItems[pos];

          if (!info || info.guid.eq(0)) {
            return null;
          }

          return {
            pos,
            item: info
          };
        }

        getCombineResult() {
          return this._combineEquipConfig;
        }

        combine() {
          const newEquip = this.getCombineResult();

          if (!newEquip) {
            return;
          }

          const materials = [];

          this._combineItems.forEach((v, index) => {
            materials.push({
              id: v.item_id,
              num: newEquip.consumeItems[index].num,
              guid: v.guid
            });
          });

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {
            equip_combine: {
              combines: [{
                equip_id: newEquip.id,
                num: 1,
                materials: materials
              }]
            }
          });
        }
        /**
         * 装备批量合成预处理方法
         * 功能：检查背包材料是否足够合成多件相同装备，收集合成信息
         * 规则：
         * 1. 主材料不存在不记录缺失，只跳过该装备
         * 2. 副材料不够要记录缺失
         * 3. 不同主材料的装备共享副材料，如果副材料不够分配，整个批量合成失败
         */


        prepareCombineAll() {
          // 初始化批量合成信息数组
          this._combineAllInfo = []; // 获取装备配置表

          const tbEquip = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEquip; // 筛选需要多个材料的装备（排除单材料装备）

          const newEquipConfigList = tbEquip.getDataList().filter(v => v.consumeItems.length > 1); // 创建背包物品映射（使用深拷贝避免修改原始数据）

          const originalBagItemMap = new Map((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.items.map(v => [v.item_id, {
            id: v.item_id,
            count: v.count,
            guid: v.guid
          }])); // 创建可修改的背包副本用于计算

          const bagItemMap = new Map(Array.from(originalBagItemMap.entries()).map(([key, value]) => [key, { ...value
          } // 深拷贝每个物品对象
          ])); // 存储缺少的副材料信息（材料ID => 需要数量）

          const lackSubMaterialMap = new Map(); // 存储所有可合成的装备信息（第一轮初步计算）

          const preliminaryCombines = []; // 第一轮：计算每个装备基于自身材料的最大可合成数量

          newEquipConfigList.forEach(equipConfig => {
            // 获取主材料（第一个材料）和副材料
            const mainMaterial = equipConfig.consumeItems[0];
            const subMaterials = equipConfig.consumeItems.slice(1); // 检查主材料是否存在 - 不存在直接跳过，不记录缺失

            const mainBagItem = bagItemMap.get(mainMaterial.id);

            if (!mainBagItem || mainBagItem.count === 0) {
              return; // 主材料不存在或数量为0，跳过该装备（不记录缺失）
            } // 计算基于主材料最多可以合成多少件


            const maxByMainMaterial = Math.floor(mainBagItem.count / mainMaterial.num);

            if (maxByMainMaterial === 0) {
              return; // 主材料数量不足合成1件，跳过该装备（不记录缺失）
            } // 计算基于副材料最多可以合成多少件


            let maxBySubMaterials = maxByMainMaterial;
            let hasLackSubMaterial = false;

            for (const subMaterial of subMaterials) {
              const subBagItem = bagItemMap.get(subMaterial.id);

              if (!subBagItem) {
                // 副材料不存在，记录缺失
                lackSubMaterialMap.set(subMaterial.id, subMaterial.num);
                hasLackSubMaterial = true;
                maxBySubMaterials = 0;
                break;
              }

              const maxForThisMaterial = Math.floor(subBagItem.count / subMaterial.num);

              if (maxForThisMaterial === 0) {
                // 副材料数量不足，记录缺失
                lackSubMaterialMap.set(subMaterial.id, subMaterial.num);
                hasLackSubMaterial = true;
                maxBySubMaterials = 0;
                break;
              }

              maxBySubMaterials = Math.min(maxBySubMaterials, maxForThisMaterial);
            } // 如果有副材料缺失，跳过该装备


            if (hasLackSubMaterial) {
              return;
            } // 添加到初步合成列表


            preliminaryCombines.push({
              equip_id: equipConfig.id,
              maxCount: maxBySubMaterials,
              mainMaterial: mainMaterial,
              subMaterials: subMaterials
            });
          }); // 如果有副材料缺失，整个批量合成失败

          if (lackSubMaterialMap.size > 0) {
            console.log('副材料不足，批量合成失败');
            return;
          } // 第二轮：处理副材料在不同装备间的分配冲突


          const finalCombines = []; // 创建一个副材料使用量统计表

          const subMaterialUsage = new Map(); // 初步确定每个装备的合成数量（先按最大可能计算）

          for (const combine of preliminaryCombines) {
            // 虚拟扣除材料
            const mainBagItem = bagItemMap.get(combine.mainMaterial.id);
            mainBagItem.count -= combine.mainMaterial.num * combine.maxCount; // 统计副材料使用量

            for (const subMaterial of combine.subMaterials) {
              const currentUsage = subMaterialUsage.get(subMaterial.id) || 0;
              subMaterialUsage.set(subMaterial.id, currentUsage + subMaterial.num * combine.maxCount);
              const subBagItem = bagItemMap.get(subMaterial.id);
              subBagItem.count -= subMaterial.num * combine.maxCount;
            } // 收集最终合成信息


            const materials = []; // 主材料信息

            materials.push({
              id: combine.mainMaterial.id,
              num: combine.mainMaterial.num * combine.maxCount,
              guid: bagItemMap.get(combine.mainMaterial.id).guid
            }); // 副材料信息

            for (const subMaterial of combine.subMaterials) {
              materials.push({
                id: subMaterial.id,
                num: subMaterial.num * combine.maxCount,
                guid: bagItemMap.get(subMaterial.id).guid
              });
            }

            finalCombines.push({
              equip_id: combine.equip_id,
              num: combine.maxCount,
              materials: materials
            });
          } // 检查副材料是否真的足够（考虑不同装备间的共享）


          let hasSubMaterialConflict = false;

          for (const [materialId, usage] of subMaterialUsage) {
            var _originalBagItemMap$g;

            const originalCount = ((_originalBagItemMap$g = originalBagItemMap.get(materialId)) == null ? void 0 : _originalBagItemMap$g.count) || 0;

            if (usage > originalCount) {
              hasSubMaterialConflict = true;
              console.log(`副材料 ${materialId} 不足: 需要${usage}, 仅有${originalCount}`);
              break;
            }
          } // 如果有副材料分配冲突，整个批量合成失败


          if (hasSubMaterialConflict) {
            console.log('副材料分配冲突，批量合成失败');
            return;
          } // 最终检查：确保所有材料都足够


          let canCombineAll = true;

          for (const combineInfo of finalCombines) {
            for (const material of combineInfo.materials) {
              const originalItem = originalBagItemMap.get(material.id);

              if (!originalItem || originalItem.count < material.num) {
                canCombineAll = false;
                break;
              }
            }

            if (!canCombineAll) break;
          } // 如果检查失败或没有可合成的装备，直接返回


          if (!canCombineAll || finalCombines.length === 0) {
            console.log('材料检查失败或无可合成装备');
            return;
          } // 设置批量合成信息，供后续合成操作使用


          this._combineAllInfo = finalCombines; // 输出合成信息

          let totalEquips = 0;
          finalCombines.forEach(combine => {
            totalEquips += combine.num;
            console.log(`装备ID: ${combine.equip_id}, 可合成: ${combine.num}件`);
          });
          console.log(`总计可合成 ${totalEquips} 件装备`);
        }

        combineAll() {
          if (this._combineAllInfo.length == 0) {
            return;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_COMBINE, {
            equip_combine: {
              combines: this._combineAllInfo
            }
          });
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6e1d4e28cb1b910339b41089a97048e4e0279585.js.map