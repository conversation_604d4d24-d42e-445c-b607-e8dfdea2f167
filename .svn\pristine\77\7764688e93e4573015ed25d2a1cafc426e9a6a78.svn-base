import { Enum, Vec2 } from "cc"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的
import { LevelDataEventCondtion } from "./condition/LevelDataEventCondtion";
import { LevelDataEventTrigger } from "./trigger/LevelDataEventTrigger";
import { newCondition } from "./condition/newCondition";
import { newTrigger } from "./trigger/newTrigger";

export class LevelDataTerrain {
    public uuid: string = "";
    public position: Vec2 = new Vec2();
    public scale: Vec2 = new Vec2();
    public rotation: number = 0;
}

export class LevelDataElem {
    public elemID: string = "";
    public position: Vec2 = new Vec2();
    public name: string = "default";
}

export class LevelDataWave extends LevelDataElem {
    public waveUUID: string = "";
    public planeID: number = 0;
    public params = {};

    static fromJSON(json: any): LevelDataWave {
        const wave = new LevelDataWave();
        if (!json) return wave;
        Object.assign(wave, json);
        return wave;
    }
}

export class LevelDataEvent extends LevelDataElem {
    public conditions: LevelDataEventCondtion[] = [];
    public triggers: LevelDataEventTrigger[] = [];

    static fromJSON(json: any): LevelDataEvent {
        const event = new LevelDataEvent();
        if (!json) return event;
        
        Object.assign(event, json);
        event.conditions = json.conditions?.map((condition: any) => {
            return newCondition(condition);
        }) || [];
        event.triggers = json.triggers?.map((trigger: any) => {
            return newTrigger(trigger);
        }) || [];
        
        return event;
    }
}

export class LevelDataLayer {
    public speed: number = 200;
    public terrains: LevelDataTerrain[] = [];
    public waves: LevelDataWave[] = [];
    public events: LevelDataEvent[] = [];

    protected assign(json: any):void {
        Object.assign(this, json);

        this.terrains = json.terrains?.map((terrain: any) => 
            Object.assign(new LevelDataTerrain(), terrain)) || [];
        this.waves = json.waves?.map((wave: any) => 
            LevelDataWave.fromJSON(wave)) || [];
        this.events = json.events?.map((event: any) => 
            LevelDataEvent.fromJSON(event)) || [];
    }

    static fromJSON(json: any): LevelDataLayer {
        const layer = new LevelDataLayer();
        if (!json) return layer;

        layer.assign(json);
        
        return layer;
    }
}

export class LevelDataBackgroundLayer extends LevelDataLayer {
    public backgrounds: string[] = [];
    
    static fromJSON(json: any): LevelDataBackgroundLayer {
        const layer = new LevelDataBackgroundLayer();
        if (!json) return layer;
       
        layer.assign(json);
        
        return layer;
    }
}

export class LevelData {
    public name: string = "";
    public totalTime: number = 59;
    public backgroundLayer: LevelDataBackgroundLayer = null;
    public floorLayers: LevelDataLayer[] = [];
    public skyLayers: LevelDataLayer[] = [];

    static fromJSON(json: any): LevelData {
        const levelData = new LevelData();
        if (!json) return levelData;
        
        Object.assign(levelData, json);
        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);
        levelData.floorLayers = json.floorLayers?.map((layer: any) =>
            LevelDataLayer.fromJSON(layer)) || [];
        levelData.skyLayers = json.skyLayers?.map((layer: any) => 
            LevelDataLayer.fromJSON(layer)) || [];
        
        return levelData;
    }
}
