{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts"], "names": ["_decorator", "BaseScreen", "ccclass", "property", "LoftScreen", "constructor", "config", "mainEntity", "m_isPlus", "props", "setData", "para", "m_config", "offset", "bulletNum", "beginAngle", "endAngle", "angleSpeed", "posOffset", "onInit", "m_count", "fire", "attackPoint", "getAttackPoint", "x", "y", "bullet", "createBullet", "angle", "init", "m_enemy", "m_bulletState", "m_mainEntity", "angleStep", "i", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,U;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;yBAGTI,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,oCACmD;AAW/CC,QAAAA,WAAW,CAACC,MAAD,EAAcC,UAAd,EAA+B;AACtC;AADsC,eATlCC,QASkC,GATd,KASc;AAAA,eARlCC,KAQkC;AAEtC,eAAKC,OAAL,CAAaJ,MAAb,EAAqBC,UAArB;AAEA,gBAAMI,IAAI,GAAG,KAAKC,QAAL,CAAcD,IAA3B;AACA,gBAAME,MAAM,GAAG,KAAKD,QAAL,CAAcC,MAA7B;AAEA,eAAKJ,KAAL,GAAa;AACTK,YAAAA,SAAS,EAAEH,IAAI,CAAC,CAAD,CADN;AAETI,YAAAA,UAAU,EAAEJ,IAAI,CAAC,CAAD,CAFP;AAGTK,YAAAA,QAAQ,EAAEL,IAAI,CAAC,CAAD,CAHL;AAITM,YAAAA,UAAU,EAAEN,IAAI,CAAC,CAAD,CAJP;AAKTO,YAAAA,SAAS,EAAEL;AALF,WAAb;AAQA,eAAKL,QAAL,GAAgBG,IAAI,CAAC,CAAD,CAAJ,GAAUA,IAAI,CAAC,CAAD,CAA9B;AACH;;AAEDQ,QAAAA,MAAM,GAAG;AACL,eAAKC,OAAL,GAAe,CAAf;AACH;;AAES,cAAJC,IAAI,GAAG;AACT,gBAAMC,WAAW,GAAG,KAAKC,cAAL,EAApB;AACA,gBAAMC,CAAC,GAAGF,WAAW,CAACE,CAAtB;AACA,gBAAMC,CAAC,GAAGH,WAAW,CAACG,CAAtB;;AAEA,cAAI,KAAKhB,KAAL,CAAWK,SAAX,KAAyB,CAA7B,EAAgC;AAC5B,kBAAMY,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;AACA,kBAAMC,KAAK,GAAG,KAAKnB,KAAL,CAAWM,UAAX,GAAwB,KAAKN,KAAL,CAAWQ,UAAX,GAAwB,KAAKG,OAAnE;;AAEA,gBAAIM,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACG,IAAP,CAAY,KAAKC,OAAjB,EAA0B;AAAEN,gBAAAA,CAAF;AAAKC,gBAAAA,CAAL;AAAQG,gBAAAA;AAAR,eAA1B,EAA2C,KAAKG,aAAhD,EAA+D,KAAKC,YAApE;AACH;AACJ,WAPD,MAOO,IAAI,KAAKvB,KAAL,CAAWK,SAAX,GAAuB,CAA3B,EAA8B;AACjC,kBAAMmB,SAAS,GAAG,CAAC,KAAKxB,KAAL,CAAWO,QAAX,GAAsB,KAAKP,KAAL,CAAWM,UAAlC,KAAiD,KAAKN,KAAL,CAAWK,SAAX,GAAuB,CAAxE,CAAlB;;AAEA,iBAAK,IAAIoB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzB,KAAL,CAAWK,SAA/B,EAA0CoB,CAAC,EAA3C,EAA+C;AAC3C,oBAAMR,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;AACA,oBAAMC,KAAK,GAAG,KAAKnB,KAAL,CAAWM,UAAX,GAAwBkB,SAAS,GAAGC,CAApC,GAAwC,KAAKzB,KAAL,CAAWQ,UAAX,GAAwB,KAAKG,OAArE,GAA+E,EAA7F;;AAEA,kBAAIM,MAAJ,EAAY;AACRA,gBAAAA,MAAM,CAACG,IAAP,CAAY,KAAKC,OAAjB,EAA0B;AAAEN,kBAAAA,CAAF;AAAKC,kBAAAA,CAAL;AAAQG,kBAAAA;AAAR,iBAA1B,EAA2C,KAAKG,aAAhD,EAA+D,KAAKC,YAApE;AACH;AACJ;AACJ;AACJ;;AAEDG,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACtB;AACH;;AA7D8C,O", "sourcesContent": ["import { _decorator, Prefab } from \"cc\";\r\nimport BaseScreen from \"./BaseScreen\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"LoftScreen\")\r\nexport default class LoftScreen extends BaseScreen {\r\n\r\n    private m_isPlus: boolean = false;\r\n    private props: {\r\n        bulletNum: number;\r\n        beginAngle: number;\r\n        endAngle: number;\r\n        angleSpeed: number;\r\n        posOffset: any;\r\n    };\r\n\r\n    constructor(config: any, mainEntity: any) {\r\n        super();\r\n        this.setData(config, mainEntity);\r\n\r\n        const para = this.m_config.para;\r\n        const offset = this.m_config.offset;\r\n\r\n        this.props = {\r\n            bulletNum: para[0],\r\n            beginAngle: para[1],\r\n            endAngle: para[2],\r\n            angleSpeed: para[3],\r\n            posOffset: offset,\r\n        };\r\n\r\n        this.m_isPlus = para[1] < para[2];\r\n    }\r\n\r\n    onInit() {\r\n        this.m_count = 0;\r\n    }\r\n\r\n    async fire() {\r\n        const attackPoint = this.getAttackPoint();\r\n        const x = attackPoint.x;\r\n        const y = attackPoint.y;\r\n\r\n        if (this.props.bulletNum === 1) {\r\n            const bullet = await this.createBullet();\r\n            const angle = this.props.beginAngle + this.props.angleSpeed * this.m_count;\r\n\r\n            if (bullet) {\r\n                bullet.init(this.m_enemy, { x, y, angle }, this.m_bulletState, this.m_mainEntity);\r\n            }\r\n        } else if (this.props.bulletNum > 0) {\r\n            const angleStep = (this.props.endAngle - this.props.beginAngle) / (this.props.bulletNum - 1);\r\n\r\n            for (let i = 0; i < this.props.bulletNum; i++) {\r\n                const bullet = await this.createBullet();\r\n                const angle = this.props.beginAngle + angleStep * i + this.props.angleSpeed * this.m_count - 90;\r\n\r\n                if (bullet) {\r\n                    bullet.init(this.m_enemy, { x, y, angle }, this.m_bulletState, this.m_mainEntity);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        // Update logic can be added here if needed\r\n    }\r\n}"]}