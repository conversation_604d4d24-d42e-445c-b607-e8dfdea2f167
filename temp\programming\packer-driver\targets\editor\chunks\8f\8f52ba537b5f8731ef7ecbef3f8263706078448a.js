System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Sprite, tween, v2, Node, NodePool, UITransform, EnemyAttrBaseCom, Tools, GameEnum, GameIns, GameConst, _dec, _class, _crd, ccclass, property, EnemyAttrDoctorCom;

  function _reportPossibleCrUseOfEnemyAttrBaseCom(extras) {
    _reporterNs.report("EnemyAttrBaseCom", "./EnemyAttrBaseCom", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      v2 = _cc.v2;
      Node = _cc.Node;
      NodePool = _cc.NodePool;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      EnemyAttrBaseCom = _unresolved_2.default;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      GameConst = _unresolved_6.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2b7a0T1aT9JM5kiT4GOEfle", "EnemyAttrDoctorCom", undefined);

      __checkObsolete__(['_decorator', 'Component', 'misc', 'Sprite', 'tween', 'v2', 'Node', 'NodePool', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyAttrDoctorCom = (_dec = ccclass('EnemyAttrDoctorCom'), _dec(_class = class EnemyAttrDoctorCom extends (_crd && EnemyAttrBaseCom === void 0 ? (_reportPossibleCrUseOfEnemyAttrBaseCom({
        error: Error()
      }), EnemyAttrBaseCom) : EnemyAttrBaseCom) {
        constructor(...args) {
          super(...args);
          this.radius = 0;
          // 医生技能的作用半径
          this.count = 0;
          // 同时治疗的敌人数量
          this.bloodTime = 0;
          // 治疗持续时间
          this.freeTime = 0;
          // 空闲时间
          this.blood = 0;
          // 每秒治疗的血量百分比
          this.time = 0;
          // 当前状态持续时间
          this.state = 0;
          // 当前状态
          this.enemyArr = [];
          // 当前正在治疗的敌人数组
          this.lightNode = null;
          // 医生技能的光圈节点
          this.lightPool = null;
          // 光线节点池
          this.lightList = [];
          // 当前光线节点数组
          this.actLightList = [];
          // 当前正在激活的光线节点数组
          this.attrData = null;
        }

        // 属性数据

        /**
         * 初始化医生属性组件
         * @param {Object} attrData 属性数据
         */
        init(attrData) {
          this.attrData = attrData;
          const params = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(attrData.param, ',');
          this.radius = params[0];
          this.count = params[1];
          this.bloodTime = params[2];
          this.freeTime = params[3];
          this.blood = params[4] / 100;
          this.reset();
          this.initUI();
        }
        /**
         * 重置医生技能状态
         */


        reset() {
          this._resetSkill();

          this.setState(0);
        }
        /**
         * 设置医生技能的状态
         * @param {number} state 状态值
         */


        setState(state) {
          this.state = state;
          this.time = 0;

          if (state === 1) {
            if (this.lightNode) {
              this.lightNode.stopAllActions();
              this.lightNode.active = true;
              tween(this.lightNode).to(0, {
                scale: 1.15
              }).to(4 * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).ActionFrameTime, {
                scale: 1
              }).to(6 * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).ActionFrameTime, {
                scale: 1.15
              }).union().repeatForever().start();
            }
          } else if (this.lightNode && this.lightNode.active) {
            this.lightNode.stopAllActions();
            this.lightNode.active = false;
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          switch (this.state) {
            case 0:
              // 空闲状态
              this.time += deltaTime;

              if (this.time > this.freeTime) {
                this.setState(2);
              }

              break;

            case 1:
              // 治疗状态
              if (!this.lightNode.active) {
                this.lightNode.active = true;
              }

              this.time += deltaTime; // 检查治疗中的敌人

              for (let i = 0; i < this.enemyArr.length; i++) {
                const enemy = this.enemyArr[i];
                const distance = enemy.node.position.sub(this.node.position);

                if (Math.abs(distance.x) > this.radius || Math.abs(distance.y) > this.radius || enemy.isDead || enemy.isFullBlood()) {
                  enemy.removeBuff((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                    error: Error()
                  }), GameEnum) : GameEnum).EnemyBuff.Treat);
                  this.enemyArr.splice(i, 1);
                  const light = this.lightList[i];
                  (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).arrRemove(this.actLightList, light);
                  this.lightPool.put(light);
                  this.lightList.splice(i, 1);
                  i--;
                }
              } // 检查是否需要治疗新的敌人


              if (this.enemyArr.length < this.count) {
                this.checkEnemy();
              } // 更新光线和治疗效果


              for (let i = 0; i < this.enemyArr.length; i++) {
                const enemy = this.enemyArr[i];
                const light = this.lightList[i];

                if (enemy && light) {
                  light.opacity = 255;
                  const direction = enemy.node.position.sub(this.node.position);
                  const angle = misc.radiansToDegrees(v2(0, -1).signAngle(direction));
                  const distance = direction.mag();
                  light.angle = angle;
                  const scaleY = distance / light.height;

                  if ((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).arrContain(this.actLightList, light)) {
                    light.scaleY += 0.4;

                    if (light.scaleY > scaleY) {
                      light.scaleY = scaleY;
                      (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).arrRemove(this.actLightList, light);
                    }
                  } else {
                    light.scaleY = scaleY;
                    enemy.changeHp(deltaTime * this.blood * enemy.getMaxHp());
                  }
                }
              } // 检查治疗时间是否结束


              if (this.time > this.bloodTime) {
                for (const enemy of this.enemyArr) {
                  enemy.removeBuff((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                    error: Error()
                  }), GameEnum) : GameEnum).EnemyBuff.Treat);
                }

                this.enemyArr = [];

                for (const light of this.lightList) {
                  this.lightPool.put(light);
                }

                this.actLightList = [];
                this.lightList = [];
                this.setState(0);
              }

              break;

            case 2:
              // 检查敌人状态
              this.checkEnemy();

              if (this.enemyArr.length > 0) {
                this.setState(1);
              }

              break;
          }
        }
        /**
         * 检查范围内的敌人并开始治疗
         */


        checkEnemy() {// const enemies = GameFunc.getRangeEnemys(this.node.position, this.radius);
          // for (const enemy of enemies) {
          //     if (
          //         enemy.node !== this.node &&
          //         !enemy.isDead &&
          //         !enemy.isFullBlood() &&
          //         this.enemyArr.length < this.count &&
          //         !Tools.arrContain(this.enemyArr, enemy)
          //     ) {
          //         enemy.addBuff(GameEnum.EnemyBuff.Treat, [this.bloodTime]);
          //         this.enemyArr.push(enemy);
          //         const light = this.lightPool.get();
          //         this.node.addChild(light);
          //         light.opacity = 0;
          //         light.scaleY = 0;
          //         this.lightList.push(light);
          //         this.actLightList.push(light);
          //     }
          // }
        }
        /**
         * 初始化 UI
         */


        initUI() {
          this.node.setSiblingIndex(0);

          if (!this.lightNode) {
            this.lightNode = new Node();
            this.lightNode.addComponent(UITransform);
            this.node.addChild(this.lightNode);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.lightNode.addComponent(Sprite), 'a_doctor_circle');
            this.lightNode.active = false;
          }

          if (!this.lightPool) {
            this.lightPool = new NodePool();
          }

          for (let i = this.lightPool.size(); i < this.count; i++) {
            const light = new Node();
            light.addComponent(UITransform); // light.anchorY = 1;

            light.setSiblingIndex(0); // light.opacity = 0;

            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.setPlaneFrame(light.addComponent(Sprite), 'a_doctor_line');
            this.lightPool.put(light);
          }
        }
        /**
         * 重置技能状态
         */


        _resetSkill() {
          if (this.lightNode) {
            this.lightNode.stopAllActions();
            this.lightNode.active = false;
          }

          for (const light of this.lightList) {
            light.stopAllActions();
            this.lightPool.put(light);
          }

          this.actLightList = [];
          this.lightList = [];
          this.enemyArr = [];
        }
        /**
         * 处理医生死亡逻辑
         */


        die() {
          for (const enemy of this.enemyArr) {
            enemy.removeBuff((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyBuff.Treat);
          }

          this._resetSkill();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8f52ba537b5f8731ef7ecbef3f8263706078448a.js.map