{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts"], "names": ["_decorator", "Prefab", "CCString", "assetManager", "CCInteger", "AudioClip", "LevelDataEventTriggerLog", "LevelDataEventTriggerType", "newTrigger", "LevelWaveParam", "LevelElemUI", "LevelCondition", "LevelDataEventCondtionType", "ccclass", "property", "LevelEventTrigger", "type", "visible", "Log", "Audio", "Wave", "_index", "data", "_audio", "_wave", "_params", "_type", "value", "message", "audio", "audioUUID", "uuid", "wave", "waveUUID", "planeID", "params", "waveTrigger", "p", "name", "LevelEventUI", "update", "dt", "i", "conditions", "length", "cond", "targetElemID", "_targetElem", "elems", "node", "scene", "getComponentsInChildren", "elem", "elemID", "initByLevelData", "condition", "push", "triggers", "trigger", "loadAny", "err", "console", "error", "prefab", "k", "param"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AACvDC,MAAAA,wB,iBAAAA,wB;;AACuBC,MAAAA,yB,iBAAAA,yB;;AACvBC,MAAAA,U,iBAAAA,U;;AAGAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;mCAGjBe,iB,WADZF,OAAO,CAAC,mBAAD,C,UAcHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEd,QADA;;AAENe,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BE,GAA9C;AACH;;AAJK,OAAD,C,UAcRJ,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,SADA;;AAENY,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BG,KAA9C;AACH;;AAJK,OAAD,C,UAmBRL,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEf,MADA;;AAENgB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,UAkBRN,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEZ,SADA;;AAENa,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,UAcRN,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAE;AAAA;AAAA,6CADA;;AAENC,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,2BA/Eb,MACaL,iBADb,CAC8B;AAAA;AAAA,eACnBM,MADmB,GACV,CADU;AAAA,eAEnBC,IAFmB,GAEY;AAAA;AAAA,qEAFZ;AAAA,eA0BnBC,MA1BmB,GA0BC,IA1BD;AAAA,eA6CnBC,KA7CmB,GA6CH,IA7CG;AAAA,eA6EnBC,OA7EmB,GA6ES,EA7ET;AAAA;;AAIX,YAAJT,IAAI,GAA6B;AACxC,iBAAO,KAAKM,IAAL,CAAUI,KAAjB;AACH;;AACc,YAAJV,IAAI,CAACW,KAAD,EAAmC;AAC9C,cAAI,KAAKL,IAAL,CAAUI,KAAV,IAAmBC,KAAvB,EAA8B;AAC1B,iBAAKL,IAAL,GAAY;AAAA;AAAA,0CAAW;AAACI,cAAAA,KAAK,EAAEC;AAAR,aAAX,CAAZ;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKN,IAAN,CAAwCM,OAA/C;AACH;;AACiB,YAAPA,OAAO,CAACD,KAAD,EAAgB;AAC7B,eAAKL,IAAN,CAAwCM,OAAxC,GAAkDD,KAAlD;AACH;;AASe,YAALE,KAAK,GAAc;AAC1B,iBAAO,KAAKN,MAAZ;AACH;;AACe,YAALM,KAAK,CAACF,KAAD,EAAmB;AAC/B,eAAKJ,MAAL,GAAcI,KAAd;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKL,IAAN,CAA0CQ,SAA1C,GAAsDH,KAAK,CAACI,IAA5D;AACH,WAFD,MAEO;AACF,iBAAKT,IAAN,CAA0CQ,SAA1C,GAAsD,EAAtD;AACH;AACJ;;AASc,YAAJE,IAAI,GAAW;AACtB,iBAAO,KAAKR,KAAZ;AACH;;AACc,YAAJQ,IAAI,CAACL,KAAD,EAAgB;AAC3B,eAAKH,KAAL,GAAaG,KAAb;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKL,IAAN,CAAyCW,QAAzC,GAAoDN,KAAK,CAACI,IAA1D;AACH,WAFD,MAEO;AACF,iBAAKT,IAAN,CAAyCW,QAAzC,GAAoD,EAApD;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKZ,IAAN,CAAyCY,OAAhD;AACH;;AACiB,YAAPA,OAAO,CAACP,KAAD,EAAgB;AAC7B,eAAKL,IAAN,CAAyCY,OAAzC,GAAmDP,KAAnD;AACH;;AASgB,YAANQ,MAAM,GAAqB;AAClC,iBAAO,KAAKV,OAAZ;AACH;;AACgB,YAANU,MAAM,CAACR,KAAD,EAA0B;AACvC,eAAKF,OAAL,GAAeE,KAAf;AACA,cAAIS,WAAW,GAAG,KAAKd,IAAvB;AACAc,UAAAA,WAAW,CAACD,MAAZ,GAAqB,EAArB;;AACA,eAAK,IAAIE,CAAT,IAAc,KAAKZ,OAAnB,EAA4B;AACxBW,YAAAA,WAAW,CAACD,MAAZ,CAAmBE,CAAC,CAACC,IAArB,IAA6BD,CAAC,CAACV,KAA/B;AACH;AACJ;;AA9FyB,O;;8BAmGjBY,Y,YADZ1B,OAAO,CAAC,cAAD,C,UAEHC,QAAQ,CAAC;AAAA;AAAA,2CAAD,C,UAERA,QAAQ,CAAC,CAACC,iBAAD,CAAD,C,6BAJb,MACawB,YADb;AAAA;AAAA,sCAC8C;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAMnCC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,kBAAMG,IAAI,GAAG,KAAKF,UAAL,CAAgBD,CAAhB,CAAb;AACAG,YAAAA,IAAI,CAACxB,MAAL,GAAcqB,CAAd;;AACA,gBAAIG,IAAI,CAAC7B,IAAL,IAAa;AAAA;AAAA,0EAA2BI,IAAxC,IACIyB,IAAI,CAACvB,IAAN,CAA0CwB,YAA1C,IAA0D,EAD7D,IAEGD,IAAI,CAACE,WAAL,IAAoB,IAF3B,EAEiC;AAC7B,oBAAMC,KAAK,GAAG,KAAKC,IAAL,CAAUC,KAAV,CAAgBC,uBAAhB;AAAA;AAAA,6CAAd;;AACA,mBAAK,IAAIC,IAAT,IAAiBJ,KAAjB,EAAwB;AACpB,oBAAII,IAAI,CAACC,MAAL,IAAgBR,IAAI,CAACvB,IAAN,CAA0CwB,YAA7D,EAA2E;AACvED,kBAAAA,IAAI,CAACE,WAAL,GAAmBK,IAAnB;AACA;AACH;AACJ;AACJ;AACJ;AACJ;;AAEME,QAAAA,eAAe,CAAChC,IAAD,EAAuB;AACzC,gBAAMgC,eAAN,CAAsBhC,IAAtB;;AACA,cAAIA,IAAI,CAACqB,UAAT,EAAqB;AACjB,iBAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,IAAI,CAACqB,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,oBAAMa,SAAS,GAAG;AAAA;AAAA,qDAAlB;AACAA,cAAAA,SAAS,CAAClC,MAAV,GAAmBqB,CAAnB;AACAa,cAAAA,SAAS,CAACjC,IAAV,GAAiBA,IAAI,CAACqB,UAAL,CAAgBD,CAAhB,CAAjB;AACA,mBAAKC,UAAL,CAAgBa,IAAhB,CAAqBD,SAArB;AACH;AACJ;;AACD,cAAIjC,IAAI,CAACmC,QAAT,EAAmB;AACf,iBAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,IAAI,CAACmC,QAAL,CAAcb,MAAlC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,oBAAMgB,OAAO,GAAG,IAAI3C,iBAAJ,EAAhB;AACA2C,cAAAA,OAAO,CAACrC,MAAR,GAAiBqB,CAAjB;AACAgB,cAAAA,OAAO,CAACpC,IAAR,GAAeA,IAAI,CAACmC,QAAL,CAAcf,CAAd,CAAf;AACA,mBAAKe,QAAL,CAAcD,IAAd,CAAmBE,OAAnB;;AACA,kBAAIA,OAAO,CAACpC,IAAR,CAAaI,KAAb,IAAsB;AAAA;AAAA,0EAA0BP,KAApD,EAA2D;AACvD,oBAAIY,IAAI,GAAI2B,OAAO,CAACpC,IAAT,CAA6CQ,SAAxD;AACA3B,gBAAAA,YAAY,CAACwD,OAAb,CAAqB;AAAC5B,kBAAAA,IAAI,EAACA;AAAN,iBAArB,EAAkC,CAAC6B,GAAD,EAAM/B,KAAN,KAA0B;AACxD,sBAAI+B,GAAJ,EAAS;AACLC,oBAAAA,OAAO,CAACC,KAAR,CAAc,6CAAd,EAA6DF,GAA7D;AACA;AACH;;AACDF,kBAAAA,OAAO,CAACnC,MAAR,GAAiBM,KAAjB;AACH,iBAND;AAOH;;AACD,kBAAI6B,OAAO,CAACpC,IAAR,CAAaI,KAAb,IAAsB;AAAA;AAAA,0EAA0BN,IAApD,EAA0D;AACtD,oBAAIgB,WAAW,GAAGsB,OAAO,CAACpC,IAA1B;AACA,oBAAIS,IAAI,GAAGK,WAAW,CAACH,QAAvB;AACA9B,gBAAAA,YAAY,CAACwD,OAAb,CAAqB;AAAC5B,kBAAAA,IAAI,EAACA;AAAN,iBAArB,EAAkC,CAAC6B,GAAD,EAAMG,MAAN,KAAwB;AACtD,sBAAIH,GAAJ,EAAS;AACLC,oBAAAA,OAAO,CAACC,KAAR,CAAc,mDAAd,EAAmEF,GAAnE;AACA;AACH;;AACDF,kBAAAA,OAAO,CAAClC,KAAR,GAAgBuC,MAAhB;AACH,iBAND;AAOAL,gBAAAA,OAAO,CAACjC,OAAR,GAAkB,EAAlB;;AACA,qBAAK,IAAIuC,CAAT,IAAc5B,WAAW,CAACD,MAA1B,EAAkC;AAC9B,sBAAI8B,KAAK,GAAG;AAAA;AAAA,yDAAZ;AACAA,kBAAAA,KAAK,CAAC3B,IAAN,GAAa0B,CAAb;AACAC,kBAAAA,KAAK,CAACtC,KAAN,GAAcS,WAAW,CAACD,MAAZ,CAAmB6B,CAAnB,CAAd;;AACAN,kBAAAA,OAAO,CAACjC,OAAR,CAAgB+B,IAAhB,CAAqBS,KAArB;AACH;AACJ;AACJ;AACJ;AACJ;;AAtEyC,O;;;;;iBAEJ,E;;;;;;;iBAEC,E", "sourcesContent": ["import { _decorator, Prefab, CCString, assetManager, CCInteger, AudioClip } from 'cc';\r\nimport { LevelDataEventTriggerLog } from '../../../leveldata/trigger/LevelDataEventTriggerLog';\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from '../../../leveldata/trigger/LevelDataEventTrigger';\r\nimport { newTrigger } from '../../../leveldata/trigger/newTrigger';\r\nimport { LevelDataEventTriggerAudio } from '../../../leveldata/trigger/LevelDataEventTriggerAudio';\r\nimport { LevelDataEventTriggerWave } from '../../../leveldata/trigger/LevelDataEventTriggerWave';\r\nimport { LevelWaveParam } from './LevelWaveUI';\r\nimport { LevelElemUI } from './LevelElemUI';\r\nimport { LevelCondition } from './LevelCondition';\r\nimport { LevelDataEventCondtionType } from '../../../leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionWave } from '../../../leveldata/condition/LevelDataEventCondtionWave';\r\nimport { LevelDataEvent } from '../../../leveldata/leveldata';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelEventTrigger')\r\nexport class LevelEventTrigger{\r\n    public _index = 0;\r\n    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();\r\n\r\n    public get type(): LevelDataEventTriggerType{\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventTriggerType) {\r\n        if (this.data._type != value) {\r\n            this.data = newTrigger({_type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCString,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Log ;\r\n        }\r\n    })\r\n    public get message(): string {\r\n        return (this.data as LevelDataEventTriggerLog).message;\r\n    }\r\n    public set message(value: string) {\r\n        (this.data as LevelDataEventTriggerLog).message = value;\r\n    }\r\n\r\n    public _audio: AudioClip = null;\r\n    @property({\r\n        type :AudioClip,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Audio;\r\n        }\r\n    })\r\n    public get audio(): AudioClip {\r\n        return this._audio;\r\n    }\r\n    public set audio(value: AudioClip) {\r\n        this._audio = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = \"\";\r\n        }\r\n    }\r\n\r\n    public _wave: Prefab = null;\r\n    @property({\r\n        type: Prefab,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get wave(): Prefab {\r\n        return this._wave;\r\n    }\r\n    public set wave(value: Prefab) {\r\n        this._wave = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerWave).waveUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerWave).waveUUID = \"\";\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCInteger,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get planeID(): number {\r\n        return (this.data as LevelDataEventTriggerWave).planeID;\r\n    }\r\n    public set planeID(value: number) {\r\n        (this.data as LevelDataEventTriggerWave).planeID = value;\r\n    }\r\n\r\n    public _params: LevelWaveParam[] = [];\r\n    @property({\r\n        type: [LevelWaveParam],\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get params(): LevelWaveParam[] {\r\n        return this._params;\r\n    }\r\n    public set params(value: LevelWaveParam[]) {\r\n        this._params = value;\r\n        let waveTrigger = this.data as LevelDataEventTriggerWave;\r\n        waveTrigger.params = {};\r\n        for (let p of this._params) {\r\n            waveTrigger.params[p.name] = p.value;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n@ccclass('LevelEventUI')\r\nexport class LevelEventUI extends LevelElemUI {\r\n    @property([LevelCondition])\r\n    public conditions: LevelCondition[] = [];\r\n    @property([LevelEventTrigger])\r\n    public triggers: LevelEventTrigger[] = [];\r\n\r\n    public update(dt: number): void {\r\n        for (let i = 0; i < this.conditions.length; i++) {\r\n            const cond = this.conditions[i];\r\n            cond._index = i;\r\n            if (cond.type == LevelDataEventCondtionType.Wave \r\n                && (cond.data as LevelDataEventCondtionWave).targetElemID != \"\" \r\n                && cond._targetElem == null) {\r\n                const elems = this.node.scene.getComponentsInChildren(LevelElemUI);\r\n                for (let elem of elems) {\r\n                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {\r\n                        cond._targetElem = elem;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataEvent) {\r\n        super.initByLevelData(data)\r\n        if (data.conditions) {\r\n            for (let i = 0; i < data.conditions.length; i++) {\r\n                const condition = new LevelCondition();\r\n                condition._index = i;\r\n                condition.data = data.conditions[i];\r\n                this.conditions.push(condition);\r\n            }\r\n        }\r\n        if (data.triggers) {\r\n            for (let i = 0; i < data.triggers.length; i++) {\r\n                const trigger = new LevelEventTrigger();\r\n                trigger._index = i;\r\n                trigger.data = data.triggers[i];\r\n                this.triggers.push(trigger);\r\n                if (trigger.data._type == LevelDataEventTriggerType.Audio) {\r\n                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;\r\n                    assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {\r\n                        if (err) {\r\n                            console.error(\"LevelEventUI initByLevelData load audio err\", err);\r\n                            return;\r\n                        }\r\n                        trigger._audio = audio;\r\n                    });\r\n                }\r\n                if (trigger.data._type == LevelDataEventTriggerType.Wave) {\r\n                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;\r\n                    let uuid = waveTrigger.waveUUID;\r\n                    assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {\r\n                        if (err) {\r\n                            console.error(\"LevelEventUI initByLevelData load wave prefab err\", err);\r\n                            return;\r\n                        }\r\n                        trigger._wave = prefab;\r\n                    });\r\n                    trigger._params = []\r\n                    for (let k in waveTrigger.params) {\r\n                        let param = new LevelWaveParam();\r\n                        param.name = k;\r\n                        param.value = waveTrigger.params[k];\r\n                        trigger._params.push(param);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}"]}