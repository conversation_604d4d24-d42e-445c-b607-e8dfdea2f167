{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts"], "names": ["_decorator", "Enum", "CCFloat", "LevelDataEventCondtionComb", "LevelDataEventCondtionType", "LevelDataEventCondtionDelayTime", "newCondition", "LevelEditorElemUI", "ccclass", "property", "executeInEditMode", "LevelEditorCondition", "type", "visible", "_index", "DelayTime", "DelayDistance", "Wave", "data", "And", "_targetElem", "comb", "value", "_type", "delayTime", "time", "delayDistance", "distance", "targetElem", "targetElemID", "elemID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;;AAEMC,MAAAA,0B,iBAAAA,0B;AAA4BC,MAAAA,0B,iBAAAA,0B;;AACpDC,MAAAA,+B,iBAAAA,+B;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CV,U;;sCASpCW,oB,WADZH,OAAO,CAAC,sBAAD,C,UAKHC,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACX,IAAI;AAAA;AAAA,qEADH;;AAENY,QAAAA,OAAO,GAAG;AACN,iBAAO,KAAKC,MAAL,IAAe,CAAtB;AACH;;AAJK,OAAD,C,UAaRL,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACX,IAAI;AAAA;AAAA;AADH,OAAD,C,UAYRQ,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEV,OADA;;AAENW,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BG,SAA/C;AACH;;AAJK,OAAD,C,UAaRN,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEV,OADA;;AAENW,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BI,aAA/C;AACH;;AAJK,OAAD,C,UAcRP,QAAQ,CAAC;AACNG,QAAAA,IAAI;AAAA;AAAA,kDADE;;AAENC,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,wEAA2BK,IAA/C;AACH;;AAJK,OAAD,C,2BAzDb,MACaN,oBADb,CACkC;AAAA;AAAA,eACvBG,MADuB,GACd,CADc;AAAA,eAEvBI,IAFuB,GAES;AAAA;AAAA,kFAAoC;AAAA;AAAA,wEAA2BC,GAA/D,CAFT;AAAA,eAuDvBC,WAvDuB,GAuDiB,IAvDjB;AAAA;;AAUf,YAAJC,IAAI,GAA+B;AAC1C,iBAAO,KAAKH,IAAL,CAAUG,IAAjB;AACH;;AACc,YAAJA,IAAI,CAACC,KAAD,EAAoC;AAC/C,eAAKJ,IAAL,CAAUG,IAAV,GAAiBC,KAAjB;AACH;;AAKc,YAAJV,IAAI,GAA+B;AAC1C,iBAAO,KAAKM,IAAL,CAAUK,KAAjB;AACH;;AACc,YAAJX,IAAI,CAACU,KAAD,EAAoC;AAC/C,cAAI,KAAKJ,IAAL,CAAUK,KAAV,IAAmBD,KAAvB,EAA8B;AAC1B,iBAAKJ,IAAL,GAAY;AAAA;AAAA,8CAAa;AAACG,cAAAA,IAAI,EAAE,KAAKH,IAAL,CAAUG,IAAjB;AAAuBE,cAAAA,KAAK,EAAED;AAA9B,aAAb,CAAZ;AACH;AACJ;;AAQmB,YAATE,SAAS,GAAW;AAC3B,iBAAQ,KAAKN,IAAN,CAA+CO,IAAtD;AACH;;AACmB,YAATD,SAAS,CAACF,KAAD,EAAgB;AAC/B,eAAKJ,IAAN,CAA+CO,IAA/C,GAAsDH,KAAtD;AACH;;AAQuB,YAAbI,aAAa,GAAW;AAC/B,iBAAQ,KAAKR,IAAN,CAAmDS,QAA1D;AACH;;AACuB,YAAbD,aAAa,CAACJ,KAAD,EAAgB;AACnC,eAAKJ,IAAN,CAAmDS,QAAnD,GAA8DL,KAA9D;AACH;;AASoB,YAAVM,UAAU,GAA6B;AAC7C,iBAAO,KAAKR,WAAZ;AACJ;;AACoB,YAAVQ,UAAU,CAACN,KAAD,EAAkC;AAAA;;AACnD,eAAKF,WAAL,GAAmBE,KAAnB;AACC,eAAKJ,IAAN,CAA0CW,YAA1C,oBAAyDP,KAAzD,oBAAyDA,KAAK,CAAEQ,MAAhE,4BAA0E,EAA1E;AACH;;AApE6B,O", "sourcesContent": ["import { _decorator, Enum, CCFloat } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from '../../scripts/leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionDelayTime } from '../../scripts/leveldata/condition/LevelDataEventCondtionDelayTime';\r\nimport { LevelDataEventCondtionDelayDistance } from '../../scripts/leveldata/condition/LevelDataEventCondtionDelayDistance';\r\nimport { LevelDataEventCondtionWave } from '../../scripts/leveldata/condition/LevelDataEventCondtionWave';\r\nimport { newCondition } from '../../scripts/leveldata/condition/newCondition';\r\nimport { LevelEditorElemUI } from './LevelEditorElemUI';\r\n\r\n@ccclass('LevelEditorCondition')\r\nexport class LevelEditorCondition {\r\n    public _index = 0;\r\n    public data : LevelDataEventCondtion = new LevelDataEventCondtionDelayTime(LevelDataEventCondtionComb.And);\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventCondtionComb),\r\n        visible() {\r\n            return this._index != 0;\r\n        }\r\n    })\r\n    public get comb(): LevelDataEventCondtionComb {\r\n        return this.data.comb;\r\n    }\r\n    public set comb(value: LevelDataEventCondtionComb) {\r\n        this.data.comb = value;\r\n    }\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventCondtionType),\r\n    })\r\n    public get type(): LevelDataEventCondtionType {\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventCondtionType) {\r\n        if (this.data._type != value) {\r\n            this.data = newCondition({comb: this.data.comb, _type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCFloat,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.DelayTime ;\r\n        }\r\n    })\r\n    public get delayTime(): number {\r\n        return (this.data as LevelDataEventCondtionDelayTime).time;\r\n    }\r\n    public set delayTime(value: number) {\r\n        (this.data as LevelDataEventCondtionDelayTime).time = value;\r\n    }\r\n\r\n    @property({\r\n        type :CCFloat,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.DelayDistance;\r\n        }\r\n    })\r\n    public get delayDistance(): number {\r\n        return (this.data as LevelDataEventCondtionDelayDistance).distance;\r\n    }\r\n    public set delayDistance(value: number) {\r\n        (this.data as LevelDataEventCondtionDelayDistance).distance = value;\r\n    }\r\n\r\n    public _targetElem: LevelEditorElemUI | null = null;\r\n    @property({\r\n        type: LevelEditorElemUI,\r\n        visible () {\r\n            return this.type == LevelDataEventCondtionType.Wave;\r\n        }\r\n    })\r\n    public get targetElem(): LevelEditorElemUI | null {\r\n         return this._targetElem;\r\n    }\r\n    public set targetElem(value: LevelEditorElemUI | null) { \r\n        this._targetElem = value;\r\n        (this.data as LevelDataEventCondtionWave).targetElemID = value?.elemID ?? \"\";\r\n    }\r\n}"]}