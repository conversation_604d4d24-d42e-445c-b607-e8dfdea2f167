System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, game, size, UITransform, GameIns, Tools, Entity, AngleComp, BulletFly, GameFunc, MainPlane, GameConst, CircleZoomFly, FBoxCollider, ColliderGroupType, _dec, _dec2, _class, _class2, _descriptor, _class3, _crd, ccclass, property, Bullet;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAngleComp(extras) {
    _reporterNs.report("AngleComp", "../base/AngleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletFly(extras) {
    _reporterNs.report("BulletFly", "./BulletFly", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../plane/mainPlane/MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCircleZoomFly(extras) {
    _reporterNs.report("CircleZoomFly", "./CircleZoomFly", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../collider-system/FCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
      game = _cc.game;
      size = _cc.size;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      Entity = _unresolved_4.default;
    }, function (_unresolved_5) {
      AngleComp = _unresolved_5.default;
    }, function (_unresolved_6) {
      BulletFly = _unresolved_6.default;
    }, function (_unresolved_7) {
      GameFunc = _unresolved_7.GameFunc;
    }, function (_unresolved_8) {
      MainPlane = _unresolved_8.MainPlane;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      CircleZoomFly = _unresolved_10.default;
    }, function (_unresolved_11) {
      FBoxCollider = _unresolved_11.default;
    }, function (_unresolved_12) {
      ColliderGroupType = _unresolved_12.ColliderGroupType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "771e3z4lZJCFI7xLAXh7DiF", "Bullet", undefined);

      __checkObsolete__(['_decorator', 'Sprite', 'ParticleSystem', 'Node', 'tween', 'v2', 'misc', 'director', 'game', 'ParticleSystem2D', 'Tween', 'v3', 'size', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", Bullet = (_dec = ccclass('Bullet'), _dec2 = property(Sprite), _dec(_class = (_class2 = (_class3 = class Bullet extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "skinImg", _descriptor, this);

          this.enemy = false;
          this.bulletID = 1;
          this.m_collideComp = void 0;
          this.isCirt = false;
          this.m_createTime = 0;
          this.m_lifeTime = 0;
          this.aliveTime = 0;
          this.m_fireTween = null;
          this._catapultCount = 0;
          this._catapultAtkRatio = 1;
          this._collideEntity = null;
          this._catapultTargets = [];
          this.m_throughArr = [];
          this.m_config = void 0;
          this.m_mainEntity = void 0;
          this.bulletState = void 0;
        }

        /**
         * 初始化子弹
         * @param {number} bulletID 子弹的唯一标识符
         */
        create(bulletID) {
          this.bulletID = bulletID;
          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.getConfig(this.bulletID);
          this.removeAllComp(); // 添加子弹飞行组件（如旋转角度）

          if (this.m_config.angleSpeed && this.m_config.angleSpeed !== 0) {
            this.addComp(_crd && AngleComp === void 0 ? (_reportPossibleCrUseOfAngleComp({
              error: Error()
            }), AngleComp) : AngleComp, new (_crd && AngleComp === void 0 ? (_reportPossibleCrUseOfAngleComp({
              error: Error()
            }), AngleComp) : AngleComp)({
              angleSpeed: this.m_config.angleSpeed
            }, this.m_config.bustyle));
          }

          switch (this.m_config.bustyle) {
            case 25:
              this.addComp(_crd && CircleZoomFly === void 0 ? (_reportPossibleCrUseOfCircleZoomFly({
                error: Error()
              }), CircleZoomFly) : CircleZoomFly, new (_crd && CircleZoomFly === void 0 ? (_reportPossibleCrUseOfCircleZoomFly({
                error: Error()
              }), CircleZoomFly) : CircleZoomFly)(this.m_config));
              break;

            default:
              this.addComp(_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
                error: Error()
              }), BulletFly) : BulletFly, new (_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
                error: Error()
              }), BulletFly) : BulletFly)(this.m_config));
          } // 添加碰撞组件


          this.m_collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.m_collideComp.init(this, size(40, 40)); // 初始化碰撞组件

          this.m_collideComp.groupType = this.enemy ? (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).BULLET_ENEMY : (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).BULLET_SELF;
          this.m_collideComp.isEnable = true; // 设置子弹皮肤

          this.setSkin(); // 设置子弹缩放

          this.node.setScale(this.m_config.scale, this.m_config.scale);
        }
        /**
        * 设置子弹的皮肤
        */


        async setSkin() {
          // 移除旧的子弹特效节点
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).removeChildByName(this.skinImg.node.parent, 'fist');
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).removeChildByName(this.skinImg.node.parent, 'gatlin');
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).removeChildByName(this.skinImg.node.parent, 'tail'); // 重置子弹透明度

          this.skinImg.node.opacity = 255; // 设置子弹的默认皮肤

          this.skinImg.spriteFrame = null;

          if (this.bulletID > 1000) {
            // 敌方子弹
            (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
              error: Error()
            }), GameFunc) : GameFunc).setImage(this.skinImg, this.m_config.image, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.enemyBulletAtlas);
          } else {
            // 主角子弹
            (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
              error: Error()
            }), GameFunc) : GameFunc).setImage(this.skinImg, this.m_config.image, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bulletManager.mainBulletAtlas);
          } // 检查图片是否加载成功


          if (!this.skinImg.spriteFrame) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error('Bullet image error:', this.m_config.image);
          }
        } //     /**
        //  * 初始化跟踪子弹
        //  * @param {boolean} isEnemy 是否为敌方子弹
        //  * @param {Object} position 子弹的初始位置
        //  * @param {number} attack 子弹的攻击力
        //  */
        //     initFollow(isEnemy, position, attack) {
        //         this.init(isEnemy, position, {
        //             attack: attack,
        //             through: false,
        //         });
        //     }

        /**
         * 初始化子弹
         * @param {boolean} isEnemy 是否为敌方子弹
         * @param {Object} position 子弹的初始位置
         * @param {Object} state 子弹的状态
         * @param {Entity} mainEntity 子弹的发射实体
         */


        init(isEnemy, position, state = null, mainEntity = null) {
          this.m_mainEntity = mainEntity;
          this.node.setPosition(position.x, position.y);
          this.skinImg.node.angle = 0;
          this.m_lifeTime = this.m_config.retrieve;

          if (this.m_lifeTime > 0) {
            this.m_createTime = game.totalTime;
          }

          this.node.angle = isEnemy ? 180 - position.angle : -position.angle;
          this.enemy = isEnemy;

          if (!state.clone) {
            state.through = this.m_config.disappear === 1;
          }

          this.bulletState = state; // 初始化子弹的所有组件

          this.m_comps.forEach(comp => {
            comp.init(this);
          }); // 设置子弹飞行组件

          const bulletFlyComp = this.getComp(_crd && BulletFly === void 0 ? (_reportPossibleCrUseOfBulletFly({
            error: Error()
          }), BulletFly) : BulletFly);

          if (bulletFlyComp) {
            bulletFlyComp.setData(-position.angle, this.m_mainEntity, this.enemy);
          }

          this.m_collideComp.init(this, size(this.m_config.body, this.m_config.body));
        }
        /**
         * 获取子弹的基础攻击力
         * @returns {number} 子弹的攻击力
         */


        _getAttack() {
          const randomValue = Math.random();
          const critChance = this.bulletState.cirt ? this.bulletState.cirt[0] : 0;
          const atkChallenge = this.bulletState.atkChallenge || 0;
          let attack = this.bulletState.attack; // 特殊处理主机类型子弹

          if (this.m_mainEntity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
            error: Error()
          }), MainPlane) : MainPlane)) {
            const config = this.m_mainEntity.m_config;

            if (config && config.type === 702) {
              if ([27, 28, 29, 37].indexOf(this.getType()) != -1) {
                attack /= 5;
              }
            }
          } // 判断是否暴击


          if (randomValue <= critChance) {
            const critMultiplier = this.bulletState.cirt ? this.bulletState.cirt[1] : 1;
            this.isCirt = true;
            return (attack + atkChallenge) * critMultiplier;
          } else {
            this.isCirt = false;
            return attack + atkChallenge;
          }
        }
        /**
         * 获取子弹对目标的实际攻击力
         * @param {Entity} target 目标实体
         * @returns {number} 实际攻击力
         */


        getAttack(target = null) {
          let attack = this._getAttack();

          return attack;
        }
        /**
         * 获取子弹的类型
         * @returns {number} 子弹的类型
         */


        getType() {
          return this.m_config.bustyle;
        }
        /**
         * 播放子弹命中音效
         */


        playHurtAudio() {// if (this.m_config.hit.length > 0) {
          //     Bullet.playAudio('hit2');
          // }
        }

        onCollide(collider) {
          // if (this.getComp(ResistBulletComp)) {
          //     this.getComp(ResistBulletComp).onCollide(target);
          // }
          // if (this.getComp(OnceCollideComp)) {
          //     if (this.getComp(OnceCollideComp).onCollide(target)) {
          //         this.m_throughArr.push(target);
          //     }
          // } else 
          this.remove(true);

          if (this.m_config.exstyle1) {
            let worldPos = collider.entity.node.parent.getComponent(UITransform).convertToWorldSpaceAR(collider.entity.node.position);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.createHurtEffect(worldPos, this.m_config.exstyle1, this.m_config.exstyle2);
          }
        }
        /**
         * 子弹超出屏幕处理
         */


        onOutScreen() {
          if (this.m_lifeTime > 0) {
            const currentTime = game.totalTime;
            this.aliveTime = (currentTime - this.m_createTime) / 1000;
          }

          if (this.aliveTime >= this.m_lifeTime) {
            this.remove();
          }
        }
        /**
         * 移除子弹
         * @param {boolean} force 是否强制移除
         */


        remove(force = false) {
          this.willRemove();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.removeBullet(this);
          this.m_mainEntity = null;
        }
        /**
         * 子弹死亡移除
         */


        dieRemove() {
          this.willRemove();

          try {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).hurtEffectManager.playBulletDieAnim(this.node.position);
          } catch (error) {
            console.error('Error during dieRemove:', error);
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.removeBullet(this, false);
          this.m_mainEntity = null;
        }
        /**
         * 子弹移除前的清理操作
         */


        willRemove() {
          if (this.m_collideComp) {
            this.m_collideComp.isEnable = false;
          }

          if (this.skinImg) {
            this.skinImg.spriteFrame = null;
          }

          this.m_throughArr.splice(0);
        }
        /**
         * 更新子弹逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          }

          this.m_comps.forEach(comp => {
            comp.update(deltaTime);
          });

          if (this.m_config.bustyle === 55 && this.node.y < -2000) {
            this.remove();
          }
        }
        /**
         * 更新子弹的组件逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateComp(deltaTime) {
          this.m_comps.forEach(comp => {
            if (comp.updateComp) {
              comp.updateComp(deltaTime);
            }
          });
        }
        /**
         * 刷新子弹状态
         */


        refresh() {
          this._catapultCount = 0;
          this._catapultAtkRatio = 1;
          this._collideEntity = null;

          this._catapultTargets.splice(0);
        }
        /**
         * 检查子弹是否可以与目标碰撞
         * @param {Entity} target 碰撞目标
         * @returns {boolean} 是否可以碰撞
         */


        canCollideEntity(target) {
          return this._catapultTargets.length === 0 || this._catapultTargets.indexOf(target) < 0;
        }
        /**
         * 子弹音效的最大播放数量
         */
        //     /**
        //      * 子弹音效的播放状态
        //      */
        //     static audios = new Map();
        //     /**
        //      * 播放子弹音效
        //      * @param {string} audioName 音效名称
        //      */
        //     static playAudio(audioName) {
        //         const maxNum = Bullet.audioMaxNum[audioName];
        //         let currentNum = Bullet.audios.get(audioName) || 0;
        //         if (currentNum < maxNum) {
        //             Bullet.audios.set(audioName, ++currentNum);
        //             // const audioId = GameIns.audioManager.playEffectSync(audioName);
        //             // audioEngine.setFinishCallback(audioId, () => {
        //             //     Bullet.onAudioFinish(audioName);
        //             // });
        //         }
        //     }
        //     /**
        //      * 子弹音效播放完成的回调
        //      * @param {string} audioName 音效名称
        //      */
        //     static onAudioFinish(audioName) {
        //         const currentNum = Bullet.audios.get(audioName);
        //         Bullet.audios.set(audioName, Math.max(0, currentNum - 1));
        //     }


      }, _class3.PrefabName = "Bullet", _class3.audioMaxNum = {
        hit1: 5,
        hit2: 3,
        hit3: 2,
        hit4: 5,
        hit5: 5,
        hit6: 5,
        swordHit: 5
      }, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "skinImg", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0327660c6417e6b430ccc7d906c2d0662723ff6f.js.map