{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts"], "names": ["LevelDataEventTriggerWave", "LevelDataEventTrigger", "LevelDataEventTriggerType", "constructor", "Wave", "waveUUID", "planeID", "params"], "mappings": ";;;gFAEaA,yB;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,yB,iBAAAA,yB;;;;;;;2CAEnBF,yB,GAAN,MAAMA,yBAAN;AAAA;AAAA,0DAA8D;AAIjEG,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sEAA0BC,IAAhC;AADU,eAHPC,QAGO,GAHY,EAGZ;AAAA,eAFPC,OAEO,GAFW,CAAC,CAEZ;AAAA,eADPC,MACO,GADE,EACF;AAEb;;AANgE,O", "sourcesContent": ["import { LevelDataEventTrigger, LevelDataEventTriggerType } from \"./LevelDataEventTrigger\";\r\n\r\nexport class LevelDataEventTriggerWave extends LevelDataEventTrigger {\r\n    public waveUUID: string = \"\";\r\n    public planeID: number = -1;\r\n    public params = {};\r\n    constructor() {\r\n        super(LevelDataEventTriggerType.Wave);\r\n    }\r\n}\r\n\r\n"]}