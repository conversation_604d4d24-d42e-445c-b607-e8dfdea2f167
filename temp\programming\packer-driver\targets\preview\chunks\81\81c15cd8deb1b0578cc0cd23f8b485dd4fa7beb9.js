System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Prefab, Component, EDITOR, Bullet, EmitterData, ObjectPool, BulletSystem, eEmitterActionType, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _class3, _crd, ccclass, executeInEditMode, property, playOnFocus, degreesToRadians, radiansToDegrees, eEmitterStatus, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterData(extras) {
    _reporterNs.report("EmitterData", "../data/EmitterData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterActionType(extras) {
    _reporterNs.report("eEmitterActionType", "../data/EventActionData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Prefab = _cc.Prefab;
      Component = _cc.Component;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Bullet = _unresolved_2.Bullet;
    }, function (_unresolved_3) {
      EmitterData = _unresolved_3.EmitterData;
    }, function (_unresolved_4) {
      ObjectPool = _unresolved_4.ObjectPool;
    }, function (_unresolved_5) {
      BulletSystem = _unresolved_5.BulletSystem;
    }, function (_unresolved_6) {
      eEmitterActionType = _unresolved_6.eEmitterActionType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'misc', 'instantiate', 'Node', 'Prefab', 'Component']);

      ({
        ccclass,
        executeInEditMode,
        property,
        playOnFocus
      } = _decorator);
      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);

      _export("eEmitterStatus", eEmitterStatus = /*#__PURE__*/function (eEmitterStatus) {
        eEmitterStatus[eEmitterStatus["None"] = 0] = "None";
        eEmitterStatus[eEmitterStatus["Prewarm"] = 1] = "Prewarm";
        eEmitterStatus[eEmitterStatus["Emitting"] = 2] = "Emitting";
        eEmitterStatus[eEmitterStatus["LoopEndReached"] = 3] = "LoopEndReached";
        eEmitterStatus[eEmitterStatus["Completed"] = 4] = "Completed";
        return eEmitterStatus;
      }({}));

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = property({
        type: Prefab,
        displayName: "Bullet Prefab"
      }), _dec3 = property({
        type: _crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
          error: Error()
        }), EmitterData) : EmitterData,
        displayName: "Emitter Data"
      }), _dec(_class = executeInEditMode(_class = playOnFocus(_class = (_class2 = (_class3 = class Emitter extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor, this);

          _initializerDefineProperty(this, "emitterData", _descriptor2, this);

          // 以下属性重新定义作为可修改的属性
          this.isOnlyInScreen = true;
          // 仅在屏幕内才发射
          this.isPreWarm = false;
          // 是否预热
          this.isLoop = true;
          // 是否循环
          this.initialDelay = 0.0;
          // 初始延迟
          this.preWarmDuration = 0.0;
          // 预热持续时长
          this.emitDuration = 1.0;
          // 发射器持续时间
          this.emitInterval = 1.0;
          // 发射间隔
          this.emitPower = 1.0;
          // 用来修改子弹初始速度的乘数(备用)
          this.loopInterval = 0.0;
          // 循环间隔
          this.perEmitCount = 1;
          // 单次发射数量
          this.perEmitInterval = 0.0;
          // 单次发射多个子弹时的间隔
          this.perEmitOffsetX = 0.0;
          // 单次发射多个子弹时的x偏移
          this.angle = -90;
          // 发射角度: -90朝下
          this.count = 1;
          // 发射条数(弹道数量)
          this.arc = 60;
          // 发射范围(弧度范围)
          this.radius = 1.0;
          // 发射半径
          this.updateInEditor = false;
          // 是否在编辑器中更新
          this._isActive = false;
          this._status = eEmitterStatus.None;
          this._statusElapsedTime = 0;
          this._totalElapsedTime = 0;
          this._isEmitting = false;
          this._nextEmitTime = 0;
          // Per-emit timing tracking
          this._perEmitStartTime = 0;
          this._perEmitBulletQueue = [];
        }

        get isActive() {
          return this._isActive;
        }

        get status() {
          return this._status;
        }

        get isEmitting() {
          return this._isEmitting;
        }

        get statusElapsedTime() {
          return this._statusElapsedTime;
        }

        get totalElapsedTime() {
          return this._totalElapsedTime;
        }

        start() {
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateEmitter(this);
          this._isActive = true;
          this.resetProperties();
        }

        update(dt) {
          if (EDITOR && this.updateInEditor) {
            this.tick(dt);
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tickBullets(dt);
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tickActionRunners(dt);
          }
        }

        resetInEditor() {
          this.updateInEditor = true;
        }

        onFocusInEditor() {
          this.updateInEditor = true;
          this.resetProperties();
        }

        onLostFocusInEditor() {
          this.updateInEditor = false;

          if ((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent && (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent.isValid) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).destroyAllBullets();
          }
        } // reset properties from emitterData


        resetProperties() {
          this.isOnlyInScreen = this.emitterData.isOnlyInScreen;
          this.isPreWarm = this.emitterData.isPreWarm;
          this.isLoop = this.emitterData.isLoop;
          this.initialDelay = this.emitterData.initialDelay;
          this.preWarmDuration = this.emitterData.preWarmDuration;
          this.emitDuration = this.emitterData.emitDuration;
          this.emitInterval = this.emitterData.emitInterval;
          this.emitPower = this.emitterData.emitPower;
          this.loopInterval = this.emitterData.loopInterval;
          this.perEmitCount = this.emitterData.perEmitCount;
          this.perEmitInterval = this.emitterData.perEmitInterval;
          this.perEmitOffsetX = this.emitterData.perEmitOffsetX;
          this.angle = this.emitterData.angle;
          this.count = this.emitterData.count;
          this.arc = this.emitterData.arc;
          this.radius = this.emitterData.radius;
        }
        /**
         * public apis
         */


        changeStatus(status) {
          this._status = status;
          this._statusElapsedTime = 0; // Clear per-emit queue when changing status

          this._perEmitBulletQueue = [];
        }

        scheduleNextEmit() {
          // Schedule the next emit after emitInterval
          this._nextEmitTime = this._statusElapsedTime + this.emitInterval;
        }

        startEmitting() {
          this._isEmitting = true; // 下一次update时触发发射
          // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
        }

        stopEmitting() {
          this._isEmitting = false; // Clear any scheduled per-emit bullets

          this.unscheduleAllCallbacks(); // Clear the per-emit bullet queue

          this._perEmitBulletQueue = [];
        }

        canEmit() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }

        emit() {
          if (this.perEmitInterval > 0) {
            // Calculate the base time for this emit cycle
            var currentEmitBaseTime = this._statusElapsedTime;
            console.log("Starting emit cycle at time " + currentEmitBaseTime.toFixed(3) + ", queue length before: " + this._perEmitBulletQueue.length); // Queue all bullets with their target emission times for this cycle

            for (var i = 0; i < this.count; i++) {
              for (var j = 0; j < this.perEmitCount; j++) {
                var targetTime = currentEmitBaseTime + this.perEmitInterval * j;

                this._perEmitBulletQueue.push({
                  index: i,
                  perEmitIndex: j,
                  targetTime: targetTime
                });

                console.log("Queued bullet " + i + "-" + j + " for time " + targetTime.toFixed(3));
              }
            } // Sort by target time to ensure proper order


            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);

            console.log("Queue length after: " + this._perEmitBulletQueue.length);
          } else {
            // Immediate emission - no timing needed
            for (var _i = 0; _i < this.count; _i++) {
              for (var _j = 0; _j < this.perEmitCount; _j++) {
                this.emitSingle(_i, _j);
              }
            }
          }
        }

        processPerEmitQueue() {
          // Process bullets that should be emitted based on current time
          // Use a small tolerance to handle floating point precision issues
          var timeTolerance = 0.001; // 1ms tolerance

          while (this._perEmitBulletQueue.length > 0) {
            var nextBullet = this._perEmitBulletQueue[0]; // Check if it's time to emit this bullet (with tolerance)

            if (this._statusElapsedTime >= nextBullet.targetTime - timeTolerance) {
              // Remove from queue and emit
              this._perEmitBulletQueue.shift();

              console.log("Emitting bullet at time " + this._statusElapsedTime.toFixed(3) + ", target was " + nextBullet.targetTime.toFixed(3) + ", diff: " + (this._statusElapsedTime - nextBullet.targetTime).toFixed(3));
              this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
              // No more bullets ready to emit yet
              break;
            }
          }
        }

        tryEmit() {
          if (this.canEmit()) {
            this.emit();
            return true;
          }

          return false;
        }

        emitSingle(index, perEmitIndex) {
          console.log("emit a bullet");
          var direction = this.getSpawnDirection(index);
          var position = this.getSpawnPosition(index, perEmitIndex);
          this.createBullet(direction, position);
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 计算发射方向
          var angleOffset = this.count > 1 ? this.arc / (this.count - 1) * index - this.arc / 2 : 0;
          var radian = degreesToRadians(this.angle + angleOffset);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index, perEmitIndex) {
          // add perEmitOffsetX by perEmitIndex
          var perEmitOffsetX = this.perEmitCount > 1 ? this.perEmitOffsetX / (this.perEmitCount - 1) * perEmitIndex - this.perEmitOffsetX / 2 : 0;

          if (this.radius <= 0) {
            return {
              x: perEmitOffsetX,
              y: 0
            };
          }

          var direction = this.getSpawnDirection(index);
          return {
            x: direction.x * this.radius + perEmitOffsetX,
            y: direction.y * this.radius
          };
        }

        createBullet(direction, position) {
          if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
          }

          var bulletNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent, this.bulletPrefab);

          if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component


          var bullet = bulletNode.getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);

          if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
          }

          if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
          } // Set bullet position relative to emitter


          var emitterPos = this.node.getWorldPosition();
          bulletNode.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateBullet(bullet); // Post set bullet properties

          bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));
          bullet.mover.speed *= this.emitPower; // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));

          return bulletNode;
        }

        applyAction(actType, actValue) {
          switch (actType) {
            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Active:
              this._isActive = actValue === 1 ? true : false;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay:
              this.initialDelay = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Prewarm:
              this.isPreWarm = actValue === 1 ? true : false;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PrewarmDuration:
              this.preWarmDuration = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Duration:
              this.emitDuration = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_ElapsedTime:
              this._statusElapsedTime = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Loop:
              this.isLoop = actValue === 1 ? true : false;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_LoopInterval:
              this.loopInterval = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval:
              this.perEmitInterval = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitCount:
              this.perEmitCount = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitOffsetX:
              this.perEmitOffsetX = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Angle:
              this.angle = actValue;
              break;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Count:
              this.count = actValue;
              break;
            // TODO: 补充更多的行为实现

            default:
              break;
          }
        }
        /**
         * Return true if this.node is in screen
         */


        isInScreen() {
          // TODO: Get mainCamera.containsNode(this.node)
          return true;
        }

        tick(deltaTime) {
          if (!this._isActive) {
            return;
          }

          this._statusElapsedTime += deltaTime;
          this._totalElapsedTime += deltaTime;

          switch (this._status) {
            case eEmitterStatus.None:
              this.updateStatusNone();
              break;

            case eEmitterStatus.Prewarm:
              this.updateStatusPrewarm();
              break;

            case eEmitterStatus.Emitting:
              this.updateStatusEmitting(deltaTime);
              break;

            case eEmitterStatus.LoopEndReached:
              this.updateStatusLoopEndReached();
              break;

            case eEmitterStatus.Completed:
              this.updateStatusCompleted();
              break;

            default:
              break;
          }
        }

        updateStatusNone() {
          if (this._statusElapsedTime >= this.initialDelay) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusPrewarm() {
          if (!this.isPreWarm) this.changeStatus(eEmitterStatus.Emitting);else {
            if (this._statusElapsedTime >= this.preWarmDuration) {
              this.changeStatus(eEmitterStatus.Emitting);
            }
          }
        }

        updateStatusEmitting(deltaTime) {
          if (this._statusElapsedTime > this.emitDuration) {
            this.stopEmitting();
            if (this.isLoop) this.changeStatus(eEmitterStatus.LoopEndReached);else this.changeStatus(eEmitterStatus.Completed);
            return;
          } // Start emitting if not already started


          if (!this._isEmitting) {
            this.startEmitting();
          } else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {
            // Check if it's time for the next emit
            this.tryEmit();
            this.scheduleNextEmit();
          } // Process per-emit bullet queue based on precise timing


          this.processPerEmitQueue();
        }

        updateStatusLoopEndReached() {
          if (this._statusElapsedTime >= this.loopInterval) {
            this.changeStatus(eEmitterStatus.Emitting);
          }
        }

        updateStatusCompleted() {// Do nothing or cleanup if needed
        }

      }, _class3.kBulletNameInEditor = "_bullet_", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "emitterData", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=81c15cd8deb1b0578cc0cd23f8b485dd4fa7beb9.js.map