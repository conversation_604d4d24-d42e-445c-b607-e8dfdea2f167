{"version": 3, "sources": ["file:///E:/M2Game/Client/node_modules/long/index.js"], "names": ["<PERSON>", "low", "high", "unsigned", "isLong", "obj", "ctz32", "value", "c", "Math", "clz32", "fromInt", "cachedObj", "cache", "UINT_CACHE", "fromBits", "INT_CACHE", "fromNumber", "isNaN", "UZERO", "ZERO", "TWO_PWR_64_DBL", "MAX_UNSIGNED_VALUE", "TWO_PWR_63_DBL", "MIN_VALUE", "MAX_VALUE", "neg", "TWO_PWR_32_DBL", "lowBits", "highBits", "fromString", "str", "radix", "length", "Error", "RangeError", "p", "indexOf", "substring", "radixToPower", "pow_dbl", "result", "i", "size", "min", "parseInt", "power", "mul", "add", "fromValue", "val", "wasm", "WebAssembly", "Instance", "<PERSON><PERSON><PERSON>", "Uint8Array", "exports", "prototype", "__isLong__", "Object", "defineProperty", "pow", "TWO_PWR_16_DBL", "TWO_PWR_24_DBL", "TWO_PWR_24", "ONE", "UONE", "NEG_ONE", "LongPrototype", "toInt", "toNumber", "toString", "isZero", "isNegative", "eq", "radixLong", "div", "rem1", "sub", "rem", "remDiv", "intval", "digits", "getHighBits", "getHighBitsUnsigned", "getLowBits", "getLowBitsUnsigned", "getNumBitsAbs", "bit", "isSafeInteger", "top11Bits", "eqz", "isPositive", "isOdd", "isEven", "equals", "other", "notEquals", "neq", "ne", "lessThan", "comp", "lt", "lessThanOrEqual", "lte", "le", "greaterThan", "gt", "greaterThanOrEqual", "gte", "ge", "compare", "thisNeg", "otherNeg", "negate", "not", "addend", "a48", "a32", "a16", "a00", "b48", "b32", "b16", "b00", "c48", "c32", "c16", "c00", "subtract", "subtrahend", "multiply", "multiplier", "divide", "divisor", "approx", "res", "halfThis", "shr", "shl", "toUnsigned", "shru", "max", "floor", "log2", "ceil", "log", "LN2", "delta", "approxRes", "approxRem", "modulo", "mod", "countLeadingZeros", "clz", "countTrailingZeros", "ctz", "and", "or", "xor", "shiftLeft", "numBits", "shiftRight", "shiftRightUnsigned", "shr_u", "rotateLeft", "b", "rotl", "rotateRight", "rotr", "toSigned", "toBytes", "toBytesLE", "toBytesBE", "hi", "lo", "fromBytes", "bytes", "fromBytesLE", "fromBytesBE", "BigInt", "fromBigInt", "Number", "asIntN", "fromValueWithBigInt", "toBigInt", "lowBigInt", "highBigInt"], "mappings": ";;;;;AAyGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAASA,IAAT,CAAcC,GAAd,EAAmBC,IAAnB,EAAyBC,QAAzB,EAAmC;AACjC;AACF;AACA;AACA;AACE,SAAKF,GAAL,GAAWA,GAAG,GAAG,CAAjB;AAEA;AACF;AACA;AACA;;AACE,SAAKC,IAAL,GAAYA,IAAI,GAAG,CAAnB;AAEA;AACF;AACA;AACA;;AACE,SAAKC,QAAL,GAAgB,CAAC,CAACA,QAAlB;AACD,G,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA,WAASC,MAAT,CAAgBC,GAAhB,EAAqB;AACnB,WAAO,CAACA,GAAG,IAAIA,GAAG,CAAC,YAAD,CAAX,MAA+B,IAAtC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASC,KAAT,CAAeC,KAAf,EAAsB;AACpB,QAAIC,CAAC,GAAGC,IAAI,CAACC,KAAL,CAAWH,KAAK,GAAG,CAACA,KAApB,CAAR;AACA,WAAOA,KAAK,GAAG,KAAKC,CAAR,GAAYA,CAAxB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA,WAASG,OAAT,CAAiBJ,KAAjB,EAAwBJ,QAAxB,EAAkC;AAChC,QAAIE,GAAJ,EAASO,SAAT,EAAoBC,KAApB;;AACA,QAAIV,QAAJ,EAAc;AACZI,MAAAA,KAAK,MAAM,CAAX;;AACA,UAAKM,KAAK,GAAG,KAAKN,KAAL,IAAcA,KAAK,GAAG,GAAnC,EAAyC;AACvCK,QAAAA,SAAS,GAAGE,UAAU,CAACP,KAAD,CAAtB;AACA,YAAIK,SAAJ,EAAe,OAAOA,SAAP;AAChB;;AACDP,MAAAA,GAAG,GAAGU,QAAQ,CAACR,KAAD,EAAQ,CAAR,EAAW,IAAX,CAAd;AACA,UAAIM,KAAJ,EAAWC,UAAU,CAACP,KAAD,CAAV,GAAoBF,GAApB;AACX,aAAOA,GAAP;AACD,KATD,MASO;AACLE,MAAAA,KAAK,IAAI,CAAT;;AACA,UAAKM,KAAK,GAAG,CAAC,GAAD,IAAQN,KAAR,IAAiBA,KAAK,GAAG,GAAtC,EAA4C;AAC1CK,QAAAA,SAAS,GAAGI,SAAS,CAACT,KAAD,CAArB;AACA,YAAIK,SAAJ,EAAe,OAAOA,SAAP;AAChB;;AACDP,MAAAA,GAAG,GAAGU,QAAQ,CAACR,KAAD,EAAQA,KAAK,GAAG,CAAR,GAAY,CAAC,CAAb,GAAiB,CAAzB,EAA4B,KAA5B,CAAd;AACA,UAAIM,KAAJ,EAAWG,SAAS,CAACT,KAAD,CAAT,GAAmBF,GAAnB;AACX,aAAOA,GAAP;AACD;AACF;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,WAASY,UAAT,CAAoBV,KAApB,EAA2BJ,QAA3B,EAAqC;AACnC,QAAIe,KAAK,CAACX,KAAD,CAAT,EAAkB,OAAOJ,QAAQ,GAAGgB,KAAH,GAAWC,IAA1B;;AAClB,QAAIjB,QAAJ,EAAc;AACZ,UAAII,KAAK,GAAG,CAAZ,EAAe,OAAOY,KAAP;AACf,UAAIZ,KAAK,IAAIc,cAAb,EAA6B,OAAOC,kBAAP;AAC9B,KAHD,MAGO;AACL,UAAIf,KAAK,IAAI,CAACgB,cAAd,EAA8B,OAAOC,SAAP;AAC9B,UAAIjB,KAAK,GAAG,CAAR,IAAagB,cAAjB,EAAiC,OAAOE,SAAP;AAClC;;AACD,QAAIlB,KAAK,GAAG,CAAZ,EAAe,OAAOU,UAAU,CAAC,CAACV,KAAF,EAASJ,QAAT,CAAV,CAA6BuB,GAA7B,EAAP;AACf,WAAOX,QAAQ,CACbR,KAAK,GAAGoB,cAAR,GAAyB,CADZ,EAEZpB,KAAK,GAAGoB,cAAT,GAA2B,CAFd,EAGbxB,QAHa,CAAf;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAASY,QAAT,CAAkBa,OAAlB,EAA2BC,QAA3B,EAAqC1B,QAArC,EAA+C;AAC7C,WAAO,IAAIH,IAAJ,CAAS4B,OAAT,EAAkBC,QAAlB,EAA4B1B,QAA5B,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAUwB;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAS2B,UAAT,CAAoBC,GAApB,EAAyB5B,QAAzB,EAAmC6B,KAAnC,EAA0C;AACxC,QAAID,GAAG,CAACE,MAAJ,KAAe,CAAnB,EAAsB,MAAMC,KAAK,CAAC,cAAD,CAAX;;AACtB,QAAI,OAAO/B,QAAP,KAAoB,QAAxB,EAAkC;AAChC;AACA6B,MAAAA,KAAK,GAAG7B,QAAR;AACAA,MAAAA,QAAQ,GAAG,KAAX;AACD,KAJD,MAIO;AACLA,MAAAA,QAAQ,GAAG,CAAC,CAACA,QAAb;AACD;;AACD,QACE4B,GAAG,KAAK,KAAR,IACAA,GAAG,KAAK,UADR,IAEAA,GAAG,KAAK,WAFR,IAGAA,GAAG,KAAK,WAJV,EAME,OAAO5B,QAAQ,GAAGgB,KAAH,GAAWC,IAA1B;AACFY,IAAAA,KAAK,GAAGA,KAAK,IAAI,EAAjB;AACA,QAAIA,KAAK,GAAG,CAAR,IAAa,KAAKA,KAAtB,EAA6B,MAAMG,UAAU,CAAC,OAAD,CAAhB;AAE7B,QAAIC,CAAJ;AACA,QAAI,CAACA,CAAC,GAAGL,GAAG,CAACM,OAAJ,CAAY,GAAZ,CAAL,IAAyB,CAA7B,EAAgC,MAAMH,KAAK,CAAC,iBAAD,CAAX,CAAhC,KACK,IAAIE,CAAC,KAAK,CAAV,EAAa;AAChB,aAAON,UAAU,CAACC,GAAG,CAACO,SAAJ,CAAc,CAAd,CAAD,EAAmBnC,QAAnB,EAA6B6B,KAA7B,CAAV,CAA8CN,GAA9C,EAAP;AACD,KAvBuC,CAyBxC;AACA;;AACA,QAAIa,YAAY,GAAGtB,UAAU,CAACuB,OAAO,CAACR,KAAD,EAAQ,CAAR,CAAR,CAA7B;AAEA,QAAIS,MAAM,GAAGrB,IAAb;;AACA,SAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGX,GAAG,CAACE,MAAxB,EAAgCS,CAAC,IAAI,CAArC,EAAwC;AACtC,UAAIC,IAAI,GAAGlC,IAAI,CAACmC,GAAL,CAAS,CAAT,EAAYb,GAAG,CAACE,MAAJ,GAAaS,CAAzB,CAAX;AAAA,UACEnC,KAAK,GAAGsC,QAAQ,CAACd,GAAG,CAACO,SAAJ,CAAcI,CAAd,EAAiBA,CAAC,GAAGC,IAArB,CAAD,EAA6BX,KAA7B,CADlB;;AAEA,UAAIW,IAAI,GAAG,CAAX,EAAc;AACZ,YAAIG,KAAK,GAAG7B,UAAU,CAACuB,OAAO,CAACR,KAAD,EAAQW,IAAR,CAAR,CAAtB;AACAF,QAAAA,MAAM,GAAGA,MAAM,CAACM,GAAP,CAAWD,KAAX,EAAkBE,GAAlB,CAAsB/B,UAAU,CAACV,KAAD,CAAhC,CAAT;AACD,OAHD,MAGO;AACLkC,QAAAA,MAAM,GAAGA,MAAM,CAACM,GAAP,CAAWR,YAAX,CAAT;AACAE,QAAAA,MAAM,GAAGA,MAAM,CAACO,GAAP,CAAW/B,UAAU,CAACV,KAAD,CAArB,CAAT;AACD;AACF;;AACDkC,IAAAA,MAAM,CAACtC,QAAP,GAAkBA,QAAlB;AACA,WAAOsC,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAASQ,SAAT,CAAmBC,GAAnB,EAAwB/C,QAAxB,EAAkC;AAChC,QAAI,OAAO+C,GAAP,KAAe,QAAnB,EAA6B,OAAOjC,UAAU,CAACiC,GAAD,EAAM/C,QAAN,CAAjB;AAC7B,QAAI,OAAO+C,GAAP,KAAe,QAAnB,EAA6B,OAAOpB,UAAU,CAACoB,GAAD,EAAM/C,QAAN,CAAjB,CAFG,CAGhC;;AACA,WAAOY,QAAQ,CACbmC,GAAG,CAACjD,GADS,EAEbiD,GAAG,CAAChD,IAFS,EAGb,OAAOC,QAAP,KAAoB,SAApB,GAAgCA,QAAhC,GAA2C+C,GAAG,CAAC/C,QAHlC,CAAf;AAKD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAxYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACIgD,MAAAA,I,GAAO,I;;AACX,UAAI;AACFA,QAAAA,IAAI,GAAG,IAAIC,WAAW,CAACC,QAAhB,CACL,IAAID,WAAW,CAACE,MAAhB,CACE,IAAIC,UAAJ,CAAe,CACb;AACA,SAFa,EAEV,EAFU,EAEN,GAFM,EAED,GAFC,EAGb;AACA,SAJa,EAIV,CAJU,EAIP,CAJO,EAIJ,CAJI,EAMb;AACA,SAPa,EAOV,EAPU,EAON,CAPM,EAQb;AACA,UATa,EAST,CATS,EASN,CATM,EASH,GATG,EAUb;AACA,UAXa,EAWT,CAXS,EAWN,GAXM,EAWD,GAXC,EAWI,GAXJ,EAWS,GAXT,EAWc,CAXd,EAWiB,GAXjB,EAab;AACA,SAda,EAcV,CAdU,EAcP,CAdO,EAeb;AACA,SAhBa,EAiBb;AACA,SAlBa,EAmBb;AACA,SApBa,EAqBb;AACA,SAtBa,EAuBb;AACA,SAxBa,EAyBb;AACA,SA1Ba,EA4Bb;AACA,SA7Ba,EA6BV,CA7BU,EA6BP,CA7BO,EA8Bb;AACA,WA/Ba,EA+BR,CA/BQ,EA+BL,EA/BK,EA+BD,CA/BC,EA+BE,EA/BF,EAiCb;AACA,SAlCa,EAkCV,EAlCU,EAkCN,CAlCM,EAmCb;AACA,SApCa,EAoCV,GApCU,EAoCL,GApCK,EAoCA,GApCA,EAoCK,CApCL,EAoCQ,CApCR,EAqCb;AACA,SAtCa,EAsCV,GAtCU,EAsCL,GAtCK,EAsCA,GAtCA,EAsCK,EAtCL,EAsCS,GAtCT,EAsCc,CAtCd,EAsCiB,CAtCjB,EAuCb;AACA,SAxCa,EAwCV,GAxCU,EAwCL,GAxCK,EAwCA,GAxCA,EAwCK,EAxCL,EAwCS,GAxCT,EAwCc,CAxCd,EAwCiB,CAxCjB,EAyCb;AACA,SA1Ca,EA0CV,GA1CU,EA0CL,GA1CK,EA0CA,GA1CA,EA0CK,EA1CL,EA0CS,GA1CT,EA0Cc,CA1Cd,EA0CiB,CA1CjB,EA2Cb;AACA,SA5Ca,EA4CV,GA5CU,EA4CL,GA5CK,EA4CA,GA5CA,EA4CK,EA5CL,EA4CS,GA5CT,EA4Cc,CA5Cd,EA4CiB,CA5CjB,EA6Cb;AACA,SA9Ca,EA8CV,GA9CU,EA8CL,GA9CK,EA8CA,GA9CA,EA8CK,EA9CL,EA8CS,GA9CT,EA8Cc,GA9Cd,EA8CmB,GA9CnB,EA8CwB,GA9CxB,EA8C6B,CA9C7B,EA8CgC,CA9ChC,EAgDb;AACA,UAjDa,EAiDT,GAjDS,EAiDJ,CAjDI,EAiDD,CAjDC,EAkDb;AACA,SAnDa,EAmDV,CAnDU,EAmDP,EAnDO,EAmDH,CAnDG,EAmDA,EAnDA,EAoDb;AACA,UArDa,EAqDT,CArDS,EAqDN,CArDM,EAqDH,GArDG,EAqDE,EArDF,EAqDM,CArDN,EAqDS,GArDT,EAqDc,EArDd,EAqDkB,CArDlB,EAqDqB,GArDrB,EAqD0B,EArD1B,EAqD8B,EArD9B,EAqDkC,GArDlC,EAqDuC,GArDvC,EAqD4C,EArD5C,EAqDgD,CArDhD,EAqDmD,GArDnD,EAqDwD,EArDxD,EAsDb,CAtDa,EAsDV,GAtDU,EAsDL,EAtDK,EAsDD,EAtDC,EAsDG,GAtDH,EAsDQ,GAtDR,EAsDa,GAtDb,EAsDkB,EAtDlB,EAsDsB,CAtDtB,EAsDyB,EAtDzB,EAsD6B,EAtD7B,EAsDiC,GAtDjC,EAsDsC,GAtDtC,EAsD2C,EAtD3C,EAsD+C,CAtD/C,EAsDkD,EAtDlD,EAsDsD,CAtDtD,EAuDb,GAvDa,EAuDR,EAvDQ,EAwDb;AACA,UAzDa,EAyDT,CAzDS,EAyDN,CAzDM,EAyDH,GAzDG,EAyDE,EAzDF,EAyDM,CAzDN,EAyDS,GAzDT,EAyDc,EAzDd,EAyDkB,CAzDlB,EAyDqB,GAzDrB,EAyD0B,EAzD1B,EAyD8B,EAzD9B,EAyDkC,GAzDlC,EAyDuC,GAzDvC,EAyD4C,EAzD5C,EAyDgD,CAzDhD,EAyDmD,GAzDnD,EAyDwD,EAzDxD,EA0Db,CA1Da,EA0DV,GA1DU,EA0DL,EA1DK,EA0DD,EA1DC,EA0DG,GA1DH,EA0DQ,GA1DR,EA0Da,GA1Db,EA0DkB,EA1DlB,EA0DsB,CA1DtB,EA0DyB,EA1DzB,EA0D6B,EA1D7B,EA0DiC,GA1DjC,EA0DsC,GA1DtC,EA0D2C,EA1D3C,EA0D+C,CA1D/C,EA0DkD,EA1DlD,EA0DsD,CA1DtD,EA2Db,GA3Da,EA2DR,EA3DQ,EA4Db;AACA,UA7Da,EA6DT,CA7DS,EA6DN,CA7DM,EA6DH,GA7DG,EA6DE,EA7DF,EA6DM,CA7DN,EA6DS,GA7DT,EA6Dc,EA7Dd,EA6DkB,CA7DlB,EA6DqB,GA7DrB,EA6D0B,EA7D1B,EA6D8B,EA7D9B,EA6DkC,GA7DlC,EA6DuC,GA7DvC,EA6D4C,EA7D5C,EA6DgD,CA7DhD,EA6DmD,GA7DnD,EA6DwD,EA7DxD,EA8Db,CA9Da,EA8DV,GA9DU,EA8DL,EA9DK,EA8DD,EA9DC,EA8DG,GA9DH,EA8DQ,GA9DR,EA8Da,GA9Db,EA8DkB,EA9DlB,EA8DsB,CA9DtB,EA8DyB,EA9DzB,EA8D6B,EA9D7B,EA8DiC,GA9DjC,EA8DsC,GA9DtC,EA8D2C,EA9D3C,EA8D+C,CA9D/C,EA8DkD,EA9DlD,EA8DsD,CA9DtD,EA+Db,GA/Da,EA+DR,EA/DQ,EAgEb;AACA,UAjEa,EAiET,CAjES,EAiEN,CAjEM,EAiEH,GAjEG,EAiEE,EAjEF,EAiEM,CAjEN,EAiES,GAjET,EAiEc,EAjEd,EAiEkB,CAjElB,EAiEqB,GAjErB,EAiE0B,EAjE1B,EAiE8B,EAjE9B,EAiEkC,GAjElC,EAiEuC,GAjEvC,EAiE4C,EAjE5C,EAiEgD,CAjEhD,EAiEmD,GAjEnD,EAiEwD,EAjExD,EAkEb,CAlEa,EAkEV,GAlEU,EAkEL,EAlEK,EAkED,EAlEC,EAkEG,GAlEH,EAkEQ,GAlER,EAkEa,GAlEb,EAkEkB,EAlElB,EAkEsB,CAlEtB,EAkEyB,EAlEzB,EAkE6B,EAlE7B,EAkEiC,GAlEjC,EAkEsC,GAlEtC,EAkE2C,EAlE3C,EAkE+C,CAlE/C,EAkEkD,EAlElD,EAkEsD,CAlEtD,EAmEb,GAnEa,EAmER,EAnEQ,EAoEb;AACA,UArEa,EAqET,CArES,EAqEN,CArEM,EAqEH,GArEG,EAqEE,EArEF,EAqEM,CArEN,EAqES,GArET,EAqEc,EArEd,EAqEkB,CArElB,EAqEqB,GArErB,EAqE0B,EArE1B,EAqE8B,EArE9B,EAqEkC,GArElC,EAqEuC,GArEvC,EAqE4C,EArE5C,EAqEgD,CArEhD,EAqEmD,GArEnD,EAqEwD,EArExD,EAsEb,CAtEa,EAsEV,GAtEU,EAsEL,EAtEK,EAsED,EAtEC,EAsEG,GAtEH,EAsEQ,GAtER,EAsEa,GAtEb,EAsEkB,EAtElB,EAsEsB,CAtEtB,EAsEyB,EAtEzB,EAsE6B,EAtE7B,EAsEiC,GAtEjC,EAsEsC,GAtEtC,EAsE2C,EAtE3C,EAsE+C,CAtE/C,EAsEkD,EAtElD,EAsEsD,CAtEtD,EAuEb,GAvEa,EAuER,EAvEQ,CAAf,CADF,CADK,EA4EL,EA5EK,EA6ELC,OA7EF;AA8ED,OA/ED,CA+EE,MAAM,CACN;AACD;;AAuDDxD,MAAAA,IAAI,CAACyD,SAAL,CAAeC,UAAf;AAEAC,MAAAA,MAAM,CAACC,cAAP,CAAsB5D,IAAI,CAACyD,SAA3B,EAAsC,YAAtC,EAAoD;AAAElD,QAAAA,KAAK,EAAE;AAAT,OAApD;AA6BAP,MAAAA,IAAI,CAACI,MAAL,GAAcA,MAAd;AAEA;AACA;AACA;AACA;AACA;;AACIY,MAAAA,S,GAAY,E;AAEhB;AACA;AACA;AACA;AACA;;AACIF,MAAAA,U,GAAa,E;AAsCjBd,MAAAA,IAAI,CAACW,OAAL,GAAeA,OAAf;AAgCAX,MAAAA,IAAI,CAACiB,UAAL,GAAkBA,UAAlB;AAsBAjB,MAAAA,IAAI,CAACe,QAAL,GAAgBA,QAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACIyB,MAAAA,O,GAAU/B,IAAI,CAACoD,G;AA8DnB7D,MAAAA,IAAI,CAAC8B,UAAL,GAAkBA,UAAlB;AA2BA9B,MAAAA,IAAI,CAACiD,SAAL,GAAiBA,SAAjB,C,CAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACIa,MAAAA,c,GAAiB,KAAK,E;AAE1B;AACA;AACA;AACA;AACA;;AACIC,MAAAA,c,GAAiB,KAAK,E;AAE1B;AACA;AACA;AACA;AACA;;AACIpC,MAAAA,c,GAAiBmC,cAAc,GAAGA,c;AAEtC;AACA;AACA;AACA;AACA;;AACIzC,MAAAA,c,GAAiBM,cAAc,GAAGA,c;AAEtC;AACA;AACA;AACA;AACA;;AACIJ,MAAAA,c,GAAiBF,cAAc,GAAG,C;AAEtC;AACA;AACA;AACA;AACA;;AACI2C,MAAAA,U,GAAarD,OAAO,CAACoD,cAAD,C;AAExB;AACA;AACA;AACA;;AACI3C,MAAAA,I,GAAOT,OAAO,CAAC,CAAD,C;AAElB;AACA;AACA;AACA;;AACAX,MAAAA,IAAI,CAACoB,IAAL,GAAYA,IAAZ;AAEA;AACA;AACA;AACA;;AACID,MAAAA,K,GAAQR,OAAO,CAAC,CAAD,EAAI,IAAJ,C;AAEnB;AACA;AACA;AACA;;AACAX,MAAAA,IAAI,CAACmB,KAAL,GAAaA,KAAb;AAEA;AACA;AACA;AACA;;AACI8C,MAAAA,G,GAAMtD,OAAO,CAAC,CAAD,C;AAEjB;AACA;AACA;AACA;;AACAX,MAAAA,IAAI,CAACiE,GAAL,GAAWA,GAAX;AAEA;AACA;AACA;AACA;;AACIC,MAAAA,I,GAAOvD,OAAO,CAAC,CAAD,EAAI,IAAJ,C;AAElB;AACA;AACA;AACA;;AACAX,MAAAA,IAAI,CAACkE,IAAL,GAAYA,IAAZ;AAEA;AACA;AACA;AACA;;AACIC,MAAAA,O,GAAUxD,OAAO,CAAC,CAAC,CAAF,C;AAErB;AACA;AACA;AACA;;AACAX,MAAAA,IAAI,CAACmE,OAAL,GAAeA,OAAf;AAEA;AACA;AACA;AACA;;AACI1C,MAAAA,S,GAAYV,QAAQ,CAAC,aAAa,CAAd,EAAiB,aAAa,CAA9B,EAAiC,KAAjC,C;AAExB;AACA;AACA;AACA;;AACAf,MAAAA,IAAI,CAACyB,SAAL,GAAiBA,SAAjB;AAEA;AACA;AACA;AACA;;AACIH,MAAAA,kB,GAAqBP,QAAQ,CAAC,aAAa,CAAd,EAAiB,aAAa,CAA9B,EAAiC,IAAjC,C;AAEjC;AACA;AACA;AACA;;AACAf,MAAAA,IAAI,CAACsB,kBAAL,GAA0BA,kBAA1B;AAEA;AACA;AACA;AACA;;AACIE,MAAAA,S,GAAYT,QAAQ,CAAC,CAAD,EAAI,aAAa,CAAjB,EAAoB,KAApB,C;AAExB;AACA;AACA;AACA;;AACAf,MAAAA,IAAI,CAACwB,SAAL,GAAiBA,SAAjB;AAEA;AACA;AACA;AACA;;AACI4C,MAAAA,a,GAAgBpE,IAAI,CAACyD,S;AAEzB;AACA;AACA;AACA;AACA;;AACAW,MAAAA,aAAa,CAACC,KAAd,GAAsB,SAASA,KAAT,GAAiB;AACrC,eAAO,KAAKlE,QAAL,GAAgB,KAAKF,GAAL,KAAa,CAA7B,GAAiC,KAAKA,GAA7C;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACE,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,YAAI,KAAKnE,QAAT,EACE,OAAO,CAAC,KAAKD,IAAL,KAAc,CAAf,IAAoByB,cAApB,IAAsC,KAAK1B,GAAL,KAAa,CAAnD,CAAP;AACF,eAAO,KAAKC,IAAL,GAAYyB,cAAZ,IAA8B,KAAK1B,GAAL,KAAa,CAA3C,CAAP;AACD,OAJD;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACG,QAAd,GAAyB,SAASA,QAAT,CAAkBvC,KAAlB,EAAyB;AAChDA,QAAAA,KAAK,GAAGA,KAAK,IAAI,EAAjB;AACA,YAAIA,KAAK,GAAG,CAAR,IAAa,KAAKA,KAAtB,EAA6B,MAAMG,UAAU,CAAC,OAAD,CAAhB;AAC7B,YAAI,KAAKqC,MAAL,EAAJ,EAAmB,OAAO,GAAP;;AACnB,YAAI,KAAKC,UAAL,EAAJ,EAAuB;AACrB;AACA,cAAI,KAAKC,EAAL,CAAQlD,SAAR,CAAJ,EAAwB;AACtB;AACA;AACA,gBAAImD,SAAS,GAAG1D,UAAU,CAACe,KAAD,CAA1B;AAAA,gBACE4C,GAAG,GAAG,KAAKA,GAAL,CAASD,SAAT,CADR;AAAA,gBAEEE,IAAI,GAAGD,GAAG,CAAC7B,GAAJ,CAAQ4B,SAAR,EAAmBG,GAAnB,CAAuB,IAAvB,CAFT;AAGA,mBAAOF,GAAG,CAACL,QAAJ,CAAavC,KAAb,IAAsB6C,IAAI,CAACR,KAAL,GAAaE,QAAb,CAAsBvC,KAAtB,CAA7B;AACD,WAPD,MAOO,OAAO,MAAM,KAAKN,GAAL,GAAW6C,QAAX,CAAoBvC,KAApB,CAAb;AACR,SAd+C,CAgBhD;AACA;;;AACA,YAAIO,YAAY,GAAGtB,UAAU,CAACuB,OAAO,CAACR,KAAD,EAAQ,CAAR,CAAR,EAAoB,KAAK7B,QAAzB,CAA7B;AAAA,YACE4E,GAAG,GAAG,IADR;AAEA,YAAItC,MAAM,GAAG,EAAb;;AACA,eAAO,IAAP,EAAa;AACX,cAAIuC,MAAM,GAAGD,GAAG,CAACH,GAAJ,CAAQrC,YAAR,CAAb;AAAA,cACE0C,MAAM,GAAGF,GAAG,CAACD,GAAJ,CAAQE,MAAM,CAACjC,GAAP,CAAWR,YAAX,CAAR,EAAkC8B,KAAlC,OAA8C,CADzD;AAAA,cAEEa,MAAM,GAAGD,MAAM,CAACV,QAAP,CAAgBvC,KAAhB,CAFX;AAGA+C,UAAAA,GAAG,GAAGC,MAAN;AACA,cAAID,GAAG,CAACP,MAAJ,EAAJ,EAAkB,OAAOU,MAAM,GAAGzC,MAAhB,CAAlB,KACK;AACH,mBAAOyC,MAAM,CAACjD,MAAP,GAAgB,CAAvB,EAA0BiD,MAAM,GAAG,MAAMA,MAAf;;AAC1BzC,YAAAA,MAAM,GAAG,KAAKyC,MAAL,GAAczC,MAAvB;AACD;AACF;AACF,OAhCD;AAkCA;AACA;AACA;AACA;AACA;;;AACA2B,MAAAA,aAAa,CAACe,WAAd,GAA4B,SAASA,WAAT,GAAuB;AACjD,eAAO,KAAKjF,IAAZ;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACgB,mBAAd,GAAoC,SAASA,mBAAT,GAA+B;AACjE,eAAO,KAAKlF,IAAL,KAAc,CAArB;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACiB,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,eAAO,KAAKpF,GAAZ;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACkB,kBAAd,GAAmC,SAASA,kBAAT,GAA8B;AAC/D,eAAO,KAAKrF,GAAL,KAAa,CAApB;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACmB,aAAd,GAA8B,SAASA,aAAT,GAAyB;AACrD,YAAI,KAAKd,UAAL,EAAJ,EACE;AACA,iBAAO,KAAKC,EAAL,CAAQlD,SAAR,IAAqB,EAArB,GAA0B,KAAKE,GAAL,GAAW6D,aAAX,EAAjC;AACF,YAAIrC,GAAG,GAAG,KAAKhD,IAAL,IAAa,CAAb,GAAiB,KAAKA,IAAtB,GAA6B,KAAKD,GAA5C;;AACA,aAAK,IAAIuF,GAAG,GAAG,EAAf,EAAmBA,GAAG,GAAG,CAAzB,EAA4BA,GAAG,EAA/B,EAAmC,IAAI,CAACtC,GAAG,GAAI,KAAKsC,GAAb,KAAsB,CAA1B,EAA6B;;AAChE,eAAO,KAAKtF,IAAL,IAAa,CAAb,GAAiBsF,GAAG,GAAG,EAAvB,GAA4BA,GAAG,GAAG,CAAzC;AACD,OAPD;AASA;AACA;AACA;AACA;AACA;;;AACApB,MAAAA,aAAa,CAACqB,aAAd,GAA8B,SAASA,aAAT,GAAyB;AACrD;AACA,YAAIC,SAAS,GAAG,KAAKxF,IAAL,IAAa,EAA7B,CAFqD,CAGrD;;AACA,YAAI,CAACwF,SAAL,EAAgB,OAAO,IAAP,CAJqC,CAKrD;;AACA,YAAI,KAAKvF,QAAT,EAAmB,OAAO,KAAP,CANkC,CAOrD;;AACA,eAAOuF,SAAS,KAAK,CAAC,CAAf,IAAoB,EAAE,KAAKzF,GAAL,KAAa,CAAb,IAAkB,KAAKC,IAAL,KAAc,CAAC,QAAnC,CAA3B;AACD,OATD;AAWA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACI,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,eAAO,KAAKtE,IAAL,KAAc,CAAd,IAAmB,KAAKD,GAAL,KAAa,CAAvC;AACD,OAFD;AAIA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACuB,GAAd,GAAoBvB,aAAa,CAACI,MAAlC;AAEA;AACA;AACA;AACA;AACA;;AACAJ,MAAAA,aAAa,CAACK,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,eAAO,CAAC,KAAKtE,QAAN,IAAkB,KAAKD,IAAL,GAAY,CAArC;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACwB,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,eAAO,KAAKzF,QAAL,IAAiB,KAAKD,IAAL,IAAa,CAArC;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACyB,KAAd,GAAsB,SAASA,KAAT,GAAiB;AACrC,eAAO,CAAC,KAAK5F,GAAL,GAAW,CAAZ,MAAmB,CAA1B;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAAC0B,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,eAAO,CAAC,KAAK7F,GAAL,GAAW,CAAZ,MAAmB,CAA1B;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAAC2B,MAAd,GAAuB,SAASA,MAAT,CAAgBC,KAAhB,EAAuB;AAC5C,YAAI,CAAC5F,MAAM,CAAC4F,KAAD,CAAX,EAAoBA,KAAK,GAAG/C,SAAS,CAAC+C,KAAD,CAAjB;AACpB,YACE,KAAK7F,QAAL,KAAkB6F,KAAK,CAAC7F,QAAxB,IACA,KAAKD,IAAL,KAAc,EAAd,KAAqB,CADrB,IAEA8F,KAAK,CAAC9F,IAAN,KAAe,EAAf,KAAsB,CAHxB,EAKE,OAAO,KAAP;AACF,eAAO,KAAKA,IAAL,KAAc8F,KAAK,CAAC9F,IAApB,IAA4B,KAAKD,GAAL,KAAa+F,KAAK,CAAC/F,GAAtD;AACD,OATD;AAWA;AACA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACM,EAAd,GAAmBN,aAAa,CAAC2B,MAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA3B,MAAAA,aAAa,CAAC6B,SAAd,GAA0B,SAASA,SAAT,CAAmBD,KAAnB,EAA0B;AAClD,eAAO,CAAC,KAAKtB,EAAL;AAAQ;AAAgBsB,QAAAA,KAAxB,CAAR;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACA5B,MAAAA,aAAa,CAAC8B,GAAd,GAAoB9B,aAAa,CAAC6B,SAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA7B,MAAAA,aAAa,CAAC+B,EAAd,GAAmB/B,aAAa,CAAC6B,SAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA7B,MAAAA,aAAa,CAACgC,QAAd,GAAyB,SAASA,QAAT,CAAkBJ,KAAlB,EAAyB;AAChD,eAAO,KAAKK,IAAL;AAAU;AAAgBL,QAAAA,KAA1B,IAAmC,CAA1C;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACA5B,MAAAA,aAAa,CAACkC,EAAd,GAAmBlC,aAAa,CAACgC,QAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAhC,MAAAA,aAAa,CAACmC,eAAd,GAAgC,SAASA,eAAT,CAAyBP,KAAzB,EAAgC;AAC9D,eAAO,KAAKK,IAAL;AAAU;AAAgBL,QAAAA,KAA1B,KAAoC,CAA3C;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACA5B,MAAAA,aAAa,CAACoC,GAAd,GAAoBpC,aAAa,CAACmC,eAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAnC,MAAAA,aAAa,CAACqC,EAAd,GAAmBrC,aAAa,CAACmC,eAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAnC,MAAAA,aAAa,CAACsC,WAAd,GAA4B,SAASA,WAAT,CAAqBV,KAArB,EAA4B;AACtD,eAAO,KAAKK,IAAL;AAAU;AAAgBL,QAAAA,KAA1B,IAAmC,CAA1C;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACA5B,MAAAA,aAAa,CAACuC,EAAd,GAAmBvC,aAAa,CAACsC,WAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAtC,MAAAA,aAAa,CAACwC,kBAAd,GAAmC,SAASA,kBAAT,CAA4BZ,KAA5B,EAAmC;AACpE,eAAO,KAAKK,IAAL;AAAU;AAAgBL,QAAAA,KAA1B,KAAoC,CAA3C;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACA5B,MAAAA,aAAa,CAACyC,GAAd,GAAoBzC,aAAa,CAACwC,kBAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAxC,MAAAA,aAAa,CAAC0C,EAAd,GAAmB1C,aAAa,CAACwC,kBAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAxC,MAAAA,aAAa,CAAC2C,OAAd,GAAwB,SAASA,OAAT,CAAiBf,KAAjB,EAAwB;AAC9C,YAAI,CAAC5F,MAAM,CAAC4F,KAAD,CAAX,EAAoBA,KAAK,GAAG/C,SAAS,CAAC+C,KAAD,CAAjB;AACpB,YAAI,KAAKtB,EAAL,CAAQsB,KAAR,CAAJ,EAAoB,OAAO,CAAP;AACpB,YAAIgB,OAAO,GAAG,KAAKvC,UAAL,EAAd;AAAA,YACEwC,QAAQ,GAAGjB,KAAK,CAACvB,UAAN,EADb;AAEA,YAAIuC,OAAO,IAAI,CAACC,QAAhB,EAA0B,OAAO,CAAC,CAAR;AAC1B,YAAI,CAACD,OAAD,IAAYC,QAAhB,EAA0B,OAAO,CAAP,CANoB,CAO9C;;AACA,YAAI,CAAC,KAAK9G,QAAV,EAAoB,OAAO,KAAK2E,GAAL,CAASkB,KAAT,EAAgBvB,UAAhB,KAA+B,CAAC,CAAhC,GAAoC,CAA3C,CAR0B,CAS9C;;AACA,eAAOuB,KAAK,CAAC9F,IAAN,KAAe,CAAf,GAAmB,KAAKA,IAAL,KAAc,CAAjC,IACJ8F,KAAK,CAAC9F,IAAN,KAAe,KAAKA,IAApB,IAA4B8F,KAAK,CAAC/F,GAAN,KAAc,CAAd,GAAkB,KAAKA,GAAL,KAAa,CADvD,GAEH,CAAC,CAFE,GAGH,CAHJ;AAID,OAdD;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACiC,IAAd,GAAqBjC,aAAa,CAAC2C,OAAnC;AAEA;AACA;AACA;AACA;AACA;;AACA3C,MAAAA,aAAa,CAAC8C,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,YAAI,CAAC,KAAK/G,QAAN,IAAkB,KAAKuE,EAAL,CAAQlD,SAAR,CAAtB,EAA0C,OAAOA,SAAP;AAC1C,eAAO,KAAK2F,GAAL,GAAWnE,GAAX,CAAeiB,GAAf,CAAP;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;;;AACAG,MAAAA,aAAa,CAAC1C,GAAd,GAAoB0C,aAAa,CAAC8C,MAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA9C,MAAAA,aAAa,CAACpB,GAAd,GAAoB,SAASA,GAAT,CAAaoE,MAAb,EAAqB;AACvC,YAAI,CAAChH,MAAM,CAACgH,MAAD,CAAX,EAAqBA,MAAM,GAAGnE,SAAS,CAACmE,MAAD,CAAlB,CADkB,CAGvC;;AAEA,YAAIC,GAAG,GAAG,KAAKnH,IAAL,KAAc,EAAxB;AACA,YAAIoH,GAAG,GAAG,KAAKpH,IAAL,GAAY,MAAtB;AACA,YAAIqH,GAAG,GAAG,KAAKtH,GAAL,KAAa,EAAvB;AACA,YAAIuH,GAAG,GAAG,KAAKvH,GAAL,GAAW,MAArB;AAEA,YAAIwH,GAAG,GAAGL,MAAM,CAAClH,IAAP,KAAgB,EAA1B;AACA,YAAIwH,GAAG,GAAGN,MAAM,CAAClH,IAAP,GAAc,MAAxB;AACA,YAAIyH,GAAG,GAAGP,MAAM,CAACnH,GAAP,KAAe,EAAzB;AACA,YAAI2H,GAAG,GAAGR,MAAM,CAACnH,GAAP,GAAa,MAAvB;AAEA,YAAI4H,GAAG,GAAG,CAAV;AAAA,YACEC,GAAG,GAAG,CADR;AAAA,YAEEC,GAAG,GAAG,CAFR;AAAA,YAGEC,GAAG,GAAG,CAHR;AAIAA,QAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAI,QAAAA,GAAG,IAAI,MAAP;AACA,eAAO9G,QAAQ,CAAEgH,GAAG,IAAI,EAAR,GAAcC,GAAf,EAAqBH,GAAG,IAAI,EAAR,GAAcC,GAAlC,EAAuC,KAAK3H,QAA5C,CAAf;AACD,OA/BD;AAiCA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAAC6D,QAAd,GAAyB,SAASA,QAAT,CAAkBC,UAAlB,EAA8B;AACrD,YAAI,CAAC9H,MAAM,CAAC8H,UAAD,CAAX,EAAyBA,UAAU,GAAGjF,SAAS,CAACiF,UAAD,CAAtB;AACzB,eAAO,KAAKlF,GAAL,CAASkF,UAAU,CAACxG,GAAX,EAAT,CAAP;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACA0C,MAAAA,aAAa,CAACU,GAAd,GAAoBV,aAAa,CAAC6D,QAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA7D,MAAAA,aAAa,CAAC+D,QAAd,GAAyB,SAASA,QAAT,CAAkBC,UAAlB,EAA8B;AACrD,YAAI,KAAK5D,MAAL,EAAJ,EAAmB,OAAO,IAAP;AACnB,YAAI,CAACpE,MAAM,CAACgI,UAAD,CAAX,EAAyBA,UAAU,GAAGnF,SAAS,CAACmF,UAAD,CAAtB,CAF4B,CAIrD;;AACA,YAAIjF,IAAJ,EAAU;AACR,cAAIlD,GAAG,GAAGkD,IAAI,CAAC,KAAD,CAAJ,CAAY,KAAKlD,GAAjB,EAAsB,KAAKC,IAA3B,EAAiCkI,UAAU,CAACnI,GAA5C,EAAiDmI,UAAU,CAAClI,IAA5D,CAAV;AACA,iBAAOa,QAAQ,CAACd,GAAD,EAAMkD,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKhD,QAA/B,CAAf;AACD;;AAED,YAAIiI,UAAU,CAAC5D,MAAX,EAAJ,EAAyB,OAAO,KAAKrE,QAAL,GAAgBgB,KAAhB,GAAwBC,IAA/B;AACzB,YAAI,KAAKsD,EAAL,CAAQlD,SAAR,CAAJ,EAAwB,OAAO4G,UAAU,CAACvC,KAAX,KAAqBrE,SAArB,GAAiCJ,IAAxC;AACxB,YAAIgH,UAAU,CAAC1D,EAAX,CAAclD,SAAd,CAAJ,EAA8B,OAAO,KAAKqE,KAAL,KAAerE,SAAf,GAA2BJ,IAAlC;;AAE9B,YAAI,KAAKqD,UAAL,EAAJ,EAAuB;AACrB,cAAI2D,UAAU,CAAC3D,UAAX,EAAJ,EAA6B,OAAO,KAAK/C,GAAL,GAAWqB,GAAX,CAAeqF,UAAU,CAAC1G,GAAX,EAAf,CAAP,CAA7B,KACK,OAAO,KAAKA,GAAL,GAAWqB,GAAX,CAAeqF,UAAf,EAA2B1G,GAA3B,EAAP;AACN,SAHD,MAGO,IAAI0G,UAAU,CAAC3D,UAAX,EAAJ,EAA6B,OAAO,KAAK1B,GAAL,CAASqF,UAAU,CAAC1G,GAAX,EAAT,EAA2BA,GAA3B,EAAP,CAjBiB,CAmBrD;;;AACA,YAAI,KAAK4E,EAAL,CAAQtC,UAAR,KAAuBoE,UAAU,CAAC9B,EAAX,CAActC,UAAd,CAA3B,EACE,OAAO/C,UAAU,CAAC,KAAKqD,QAAL,KAAkB8D,UAAU,CAAC9D,QAAX,EAAnB,EAA0C,KAAKnE,QAA/C,CAAjB,CArBmD,CAuBrD;AACA;;AAEA,YAAIkH,GAAG,GAAG,KAAKnH,IAAL,KAAc,EAAxB;AACA,YAAIoH,GAAG,GAAG,KAAKpH,IAAL,GAAY,MAAtB;AACA,YAAIqH,GAAG,GAAG,KAAKtH,GAAL,KAAa,EAAvB;AACA,YAAIuH,GAAG,GAAG,KAAKvH,GAAL,GAAW,MAArB;AAEA,YAAIwH,GAAG,GAAGW,UAAU,CAAClI,IAAX,KAAoB,EAA9B;AACA,YAAIwH,GAAG,GAAGU,UAAU,CAAClI,IAAX,GAAkB,MAA5B;AACA,YAAIyH,GAAG,GAAGS,UAAU,CAACnI,GAAX,KAAmB,EAA7B;AACA,YAAI2H,GAAG,GAAGQ,UAAU,CAACnI,GAAX,GAAiB,MAA3B;AAEA,YAAI4H,GAAG,GAAG,CAAV;AAAA,YACEC,GAAG,GAAG,CADR;AAAA,YAEEC,GAAG,GAAG,CAFR;AAAA,YAGEC,GAAG,GAAG,CAHR;AAIAA,QAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGK,GAAb;AACAE,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAA,QAAAA,GAAG,IAAIP,GAAG,GAAGG,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGM,GAAb;AACAC,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAA,QAAAA,GAAG,IAAIP,GAAG,GAAGI,GAAb;AACAE,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAA,QAAAA,GAAG,IAAIN,GAAG,GAAGE,GAAb;AACAG,QAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,QAAAA,GAAG,IAAI,MAAP;AACAD,QAAAA,GAAG,IAAIR,GAAG,GAAGO,GAAN,GAAYN,GAAG,GAAGK,GAAlB,GAAwBJ,GAAG,GAAGG,GAA9B,GAAoCF,GAAG,GAAGC,GAAjD;AACAI,QAAAA,GAAG,IAAI,MAAP;AACA,eAAO9G,QAAQ,CAAEgH,GAAG,IAAI,EAAR,GAAcC,GAAf,EAAqBH,GAAG,IAAI,EAAR,GAAcC,GAAlC,EAAuC,KAAK3H,QAA5C,CAAf;AACD,OA7DD;AA+DA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACrB,GAAd,GAAoBqB,aAAa,CAAC+D,QAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA/D,MAAAA,aAAa,CAACiE,MAAd,GAAuB,SAASA,MAAT,CAAgBC,OAAhB,EAAyB;AAC9C,YAAI,CAAClI,MAAM,CAACkI,OAAD,CAAX,EAAsBA,OAAO,GAAGrF,SAAS,CAACqF,OAAD,CAAnB;AACtB,YAAIA,OAAO,CAAC9D,MAAR,EAAJ,EAAsB,MAAMtC,KAAK,CAAC,kBAAD,CAAX,CAFwB,CAI9C;;AACA,YAAIiB,IAAJ,EAAU;AACR;AACA;AACA;AACA,cACE,CAAC,KAAKhD,QAAN,IACA,KAAKD,IAAL,KAAc,CAAC,UADf,IAEAoI,OAAO,CAACrI,GAAR,KAAgB,CAAC,CAFjB,IAGAqI,OAAO,CAACpI,IAAR,KAAiB,CAAC,CAJpB,EAKE;AACA;AACA,mBAAO,IAAP;AACD;;AACD,cAAID,GAAG,GAAG,CAAC,KAAKE,QAAL,GAAgBgD,IAAI,CAAC,OAAD,CAApB,GAAgCA,IAAI,CAAC,OAAD,CAArC,EACR,KAAKlD,GADG,EAER,KAAKC,IAFG,EAGRoI,OAAO,CAACrI,GAHA,EAIRqI,OAAO,CAACpI,IAJA,CAAV;AAMA,iBAAOa,QAAQ,CAACd,GAAD,EAAMkD,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKhD,QAA/B,CAAf;AACD;;AAED,YAAI,KAAKqE,MAAL,EAAJ,EAAmB,OAAO,KAAKrE,QAAL,GAAgBgB,KAAhB,GAAwBC,IAA/B;AACnB,YAAImH,MAAJ,EAAYxD,GAAZ,EAAiByD,GAAjB;;AACA,YAAI,CAAC,KAAKrI,QAAV,EAAoB;AAClB;AACA;AACA,cAAI,KAAKuE,EAAL,CAAQlD,SAAR,CAAJ,EAAwB;AACtB,gBAAI8G,OAAO,CAAC5D,EAAR,CAAWT,GAAX,KAAmBqE,OAAO,CAAC5D,EAAR,CAAWP,OAAX,CAAvB,EACE,OAAO3C,SAAP,CADF,CACoB;AADpB,iBAEK,IAAI8G,OAAO,CAAC5D,EAAR,CAAWlD,SAAX,CAAJ,EAA2B,OAAOyC,GAAP,CAA3B,KACA;AACH;AACA,kBAAIwE,QAAQ,GAAG,KAAKC,GAAL,CAAS,CAAT,CAAf;AACAH,cAAAA,MAAM,GAAGE,QAAQ,CAAC7D,GAAT,CAAa0D,OAAb,EAAsBK,GAAtB,CAA0B,CAA1B,CAAT;;AACA,kBAAIJ,MAAM,CAAC7D,EAAP,CAAUtD,IAAV,CAAJ,EAAqB;AACnB,uBAAOkH,OAAO,CAAC7D,UAAR,KAAuBR,GAAvB,GAA6BE,OAApC;AACD,eAFD,MAEO;AACLY,gBAAAA,GAAG,GAAG,KAAKD,GAAL,CAASwD,OAAO,CAACvF,GAAR,CAAYwF,MAAZ,CAAT,CAAN;AACAC,gBAAAA,GAAG,GAAGD,MAAM,CAACvF,GAAP,CAAW+B,GAAG,CAACH,GAAJ,CAAQ0D,OAAR,CAAX,CAAN;AACA,uBAAOE,GAAP;AACD;AACF;AACF,WAhBD,MAgBO,IAAIF,OAAO,CAAC5D,EAAR,CAAWlD,SAAX,CAAJ,EAA2B,OAAO,KAAKrB,QAAL,GAAgBgB,KAAhB,GAAwBC,IAA/B;;AAClC,cAAI,KAAKqD,UAAL,EAAJ,EAAuB;AACrB,gBAAI6D,OAAO,CAAC7D,UAAR,EAAJ,EAA0B,OAAO,KAAK/C,GAAL,GAAWkD,GAAX,CAAe0D,OAAO,CAAC5G,GAAR,EAAf,CAAP;AAC1B,mBAAO,KAAKA,GAAL,GAAWkD,GAAX,CAAe0D,OAAf,EAAwB5G,GAAxB,EAAP;AACD,WAHD,MAGO,IAAI4G,OAAO,CAAC7D,UAAR,EAAJ,EAA0B,OAAO,KAAKG,GAAL,CAAS0D,OAAO,CAAC5G,GAAR,EAAT,EAAwBA,GAAxB,EAAP;;AACjC8G,UAAAA,GAAG,GAAGpH,IAAN;AACD,SAzBD,MAyBO;AACL;AACA;AACA,cAAI,CAACkH,OAAO,CAACnI,QAAb,EAAuBmI,OAAO,GAAGA,OAAO,CAACM,UAAR,EAAV;AACvB,cAAIN,OAAO,CAAC3B,EAAR,CAAW,IAAX,CAAJ,EAAsB,OAAOxF,KAAP;AACtB,cAAImH,OAAO,CAAC3B,EAAR,CAAW,KAAKkC,IAAL,CAAU,CAAV,CAAX,CAAJ,EACE;AACA,mBAAO3E,IAAP;AACFsE,UAAAA,GAAG,GAAGrH,KAAN;AACD,SA/D6C,CAiE9C;AACA;AACA;AACA;AACA;;;AACA4D,QAAAA,GAAG,GAAG,IAAN;;AACA,eAAOA,GAAG,CAAC8B,GAAJ,CAAQyB,OAAR,CAAP,EAAyB;AACvB;AACA;AACAC,UAAAA,MAAM,GAAG9H,IAAI,CAACqI,GAAL,CAAS,CAAT,EAAYrI,IAAI,CAACsI,KAAL,CAAWhE,GAAG,CAACT,QAAJ,KAAiBgE,OAAO,CAAChE,QAAR,EAA5B,CAAZ,CAAT,CAHuB,CAKvB;AACA;;AACA,cAAI0E,IAAI,GAAGvI,IAAI,CAACwI,IAAL,CAAUxI,IAAI,CAACyI,GAAL,CAASX,MAAT,IAAmB9H,IAAI,CAAC0I,GAAlC,CAAX;AAAA,cACEC,KAAK,GAAGJ,IAAI,IAAI,EAAR,GAAa,CAAb,GAAiBxG,OAAO,CAAC,CAAD,EAAIwG,IAAI,GAAG,EAAX,CADlC;AAAA,cAEE;AACA;AACAK,UAAAA,SAAS,GAAGpI,UAAU,CAACsH,MAAD,CAJxB;AAAA,cAKEe,SAAS,GAAGD,SAAS,CAACtG,GAAV,CAAcuF,OAAd,CALd;;AAMA,iBAAOgB,SAAS,CAAC7E,UAAV,MAA0B6E,SAAS,CAAC3C,EAAV,CAAa5B,GAAb,CAAjC,EAAoD;AAClDwD,YAAAA,MAAM,IAAIa,KAAV;AACAC,YAAAA,SAAS,GAAGpI,UAAU,CAACsH,MAAD,EAAS,KAAKpI,QAAd,CAAtB;AACAmJ,YAAAA,SAAS,GAAGD,SAAS,CAACtG,GAAV,CAAcuF,OAAd,CAAZ;AACD,WAjBsB,CAmBvB;AACA;;;AACA,cAAIe,SAAS,CAAC7E,MAAV,EAAJ,EAAwB6E,SAAS,GAAGpF,GAAZ;AAExBuE,UAAAA,GAAG,GAAGA,GAAG,CAACxF,GAAJ,CAAQqG,SAAR,CAAN;AACAtE,UAAAA,GAAG,GAAGA,GAAG,CAACD,GAAJ,CAAQwE,SAAR,CAAN;AACD;;AACD,eAAOd,GAAP;AACD,OAlGD;AAoGA;AACA;AACA;AACA;AACA;AACA;;;AACApE,MAAAA,aAAa,CAACQ,GAAd,GAAoBR,aAAa,CAACiE,MAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAjE,MAAAA,aAAa,CAACmF,MAAd,GAAuB,SAASA,MAAT,CAAgBjB,OAAhB,EAAyB;AAC9C,YAAI,CAAClI,MAAM,CAACkI,OAAD,CAAX,EAAsBA,OAAO,GAAGrF,SAAS,CAACqF,OAAD,CAAnB,CADwB,CAG9C;;AACA,YAAInF,IAAJ,EAAU;AACR,cAAIlD,GAAG,GAAG,CAAC,KAAKE,QAAL,GAAgBgD,IAAI,CAAC,OAAD,CAApB,GAAgCA,IAAI,CAAC,OAAD,CAArC,EACR,KAAKlD,GADG,EAER,KAAKC,IAFG,EAGRoI,OAAO,CAACrI,GAHA,EAIRqI,OAAO,CAACpI,IAJA,CAAV;AAMA,iBAAOa,QAAQ,CAACd,GAAD,EAAMkD,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKhD,QAA/B,CAAf;AACD;;AAED,eAAO,KAAK2E,GAAL,CAAS,KAAKF,GAAL,CAAS0D,OAAT,EAAkBvF,GAAlB,CAAsBuF,OAAtB,CAAT,CAAP;AACD,OAfD;AAiBA;AACA;AACA;AACA;AACA;AACA;;;AACAlE,MAAAA,aAAa,CAACoF,GAAd,GAAoBpF,aAAa,CAACmF,MAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAnF,MAAAA,aAAa,CAACW,GAAd,GAAoBX,aAAa,CAACmF,MAAlC;AAEA;AACA;AACA;AACA;AACA;;AACAnF,MAAAA,aAAa,CAAC+C,GAAd,GAAoB,SAASA,GAAT,GAAe;AACjC,eAAOpG,QAAQ,CAAC,CAAC,KAAKd,GAAP,EAAY,CAAC,KAAKC,IAAlB,EAAwB,KAAKC,QAA7B,CAAf;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACqF,iBAAd,GAAkC,SAASA,iBAAT,GAA6B;AAC7D,eAAO,KAAKvJ,IAAL,GAAYO,IAAI,CAACC,KAAL,CAAW,KAAKR,IAAhB,CAAZ,GAAoCO,IAAI,CAACC,KAAL,CAAW,KAAKT,GAAhB,IAAuB,EAAlE;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACAmE,MAAAA,aAAa,CAACsF,GAAd,GAAoBtF,aAAa,CAACqF,iBAAlC;AAEA;AACA;AACA;AACA;AACA;;AACArF,MAAAA,aAAa,CAACuF,kBAAd,GAAmC,SAASA,kBAAT,GAA8B;AAC/D,eAAO,KAAK1J,GAAL,GAAWK,KAAK,CAAC,KAAKL,GAAN,CAAhB,GAA6BK,KAAK,CAAC,KAAKJ,IAAN,CAAL,GAAmB,EAAvD;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACwF,GAAd,GAAoBxF,aAAa,CAACuF,kBAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAvF,MAAAA,aAAa,CAACyF,GAAd,GAAoB,SAASA,GAAT,CAAa7D,KAAb,EAAoB;AACtC,YAAI,CAAC5F,MAAM,CAAC4F,KAAD,CAAX,EAAoBA,KAAK,GAAG/C,SAAS,CAAC+C,KAAD,CAAjB;AACpB,eAAOjF,QAAQ,CAAC,KAAKd,GAAL,GAAW+F,KAAK,CAAC/F,GAAlB,EAAuB,KAAKC,IAAL,GAAY8F,KAAK,CAAC9F,IAAzC,EAA+C,KAAKC,QAApD,CAAf;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAAC0F,EAAd,GAAmB,SAASA,EAAT,CAAY9D,KAAZ,EAAmB;AACpC,YAAI,CAAC5F,MAAM,CAAC4F,KAAD,CAAX,EAAoBA,KAAK,GAAG/C,SAAS,CAAC+C,KAAD,CAAjB;AACpB,eAAOjF,QAAQ,CAAC,KAAKd,GAAL,GAAW+F,KAAK,CAAC/F,GAAlB,EAAuB,KAAKC,IAAL,GAAY8F,KAAK,CAAC9F,IAAzC,EAA+C,KAAKC,QAApD,CAAf;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAAC2F,GAAd,GAAoB,SAASA,GAAT,CAAa/D,KAAb,EAAoB;AACtC,YAAI,CAAC5F,MAAM,CAAC4F,KAAD,CAAX,EAAoBA,KAAK,GAAG/C,SAAS,CAAC+C,KAAD,CAAjB;AACpB,eAAOjF,QAAQ,CAAC,KAAKd,GAAL,GAAW+F,KAAK,CAAC/F,GAAlB,EAAuB,KAAKC,IAAL,GAAY8F,KAAK,CAAC9F,IAAzC,EAA+C,KAAKC,QAApD,CAAf;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAAC4F,SAAd,GAA0B,SAASA,SAAT,CAAmBC,OAAnB,EAA4B;AACpD,YAAI7J,MAAM,CAAC6J,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,YAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP,CAA3B,KACK,IAAIA,OAAO,GAAG,EAAd,EACH,OAAOlJ,QAAQ,CACb,KAAKd,GAAL,IAAYgK,OADC,EAEZ,KAAK/J,IAAL,IAAa+J,OAAd,GAA0B,KAAKhK,GAAL,KAAc,KAAKgK,OAFhC,EAGb,KAAK9J,QAHQ,CAAf,CADG,KAMA,OAAOY,QAAQ,CAAC,CAAD,EAAI,KAAKd,GAAL,IAAagK,OAAO,GAAG,EAA3B,EAAgC,KAAK9J,QAArC,CAAf;AACN,OAVD;AAYA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACuE,GAAd,GAAoBvE,aAAa,CAAC4F,SAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA5F,MAAAA,aAAa,CAAC8F,UAAd,GAA2B,SAASA,UAAT,CAAoBD,OAApB,EAA6B;AACtD,YAAI7J,MAAM,CAAC6J,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,YAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP,CAA3B,KACK,IAAIA,OAAO,GAAG,EAAd,EACH,OAAOlJ,QAAQ,CACZ,KAAKd,GAAL,KAAagK,OAAd,GAA0B,KAAK/J,IAAL,IAAc,KAAK+J,OADhC,EAEb,KAAK/J,IAAL,IAAa+J,OAFA,EAGb,KAAK9J,QAHQ,CAAf,CADG,KAOH,OAAOY,QAAQ,CACb,KAAKb,IAAL,IAAc+J,OAAO,GAAG,EADX,EAEb,KAAK/J,IAAL,IAAa,CAAb,GAAiB,CAAjB,GAAqB,CAAC,CAFT,EAGb,KAAKC,QAHQ,CAAf;AAKH,OAfD;AAiBA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACsE,GAAd,GAAoBtE,aAAa,CAAC8F,UAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA9F,MAAAA,aAAa,CAAC+F,kBAAd,GAAmC,SAASA,kBAAT,CAA4BF,OAA5B,EAAqC;AACtE,YAAI7J,MAAM,CAAC6J,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,YAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,YAAIA,OAAO,GAAG,EAAd,EACE,OAAOlJ,QAAQ,CACZ,KAAKd,GAAL,KAAagK,OAAd,GAA0B,KAAK/J,IAAL,IAAc,KAAK+J,OADhC,EAEb,KAAK/J,IAAL,KAAc+J,OAFD,EAGb,KAAK9J,QAHQ,CAAf;AAKF,YAAI8J,OAAO,KAAK,EAAhB,EAAoB,OAAOlJ,QAAQ,CAAC,KAAKb,IAAN,EAAY,CAAZ,EAAe,KAAKC,QAApB,CAAf;AACpB,eAAOY,QAAQ,CAAC,KAAKb,IAAL,KAAe+J,OAAO,GAAG,EAA1B,EAA+B,CAA/B,EAAkC,KAAK9J,QAAvC,CAAf;AACD,OAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACyE,IAAd,GAAqBzE,aAAa,CAAC+F,kBAAnC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA/F,MAAAA,aAAa,CAACgG,KAAd,GAAsBhG,aAAa,CAAC+F,kBAApC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA/F,MAAAA,aAAa,CAACiG,UAAd,GAA2B,SAASA,UAAT,CAAoBJ,OAApB,EAA6B;AACtD,YAAIK,CAAJ;AACA,YAAIlK,MAAM,CAAC6J,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,YAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,YAAIA,OAAO,KAAK,EAAhB,EAAoB,OAAOlJ,QAAQ,CAAC,KAAKb,IAAN,EAAY,KAAKD,GAAjB,EAAsB,KAAKE,QAA3B,CAAf;;AACpB,YAAI8J,OAAO,GAAG,EAAd,EAAkB;AAChBK,UAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,iBAAOlJ,QAAQ,CACZ,KAAKd,GAAL,IAAYgK,OAAb,GAAyB,KAAK/J,IAAL,KAAcoK,CAD1B,EAEZ,KAAKpK,IAAL,IAAa+J,OAAd,GAA0B,KAAKhK,GAAL,KAAaqK,CAF1B,EAGb,KAAKnK,QAHQ,CAAf;AAKD;;AACD8J,QAAAA,OAAO,IAAI,EAAX;AACAK,QAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,eAAOlJ,QAAQ,CACZ,KAAKb,IAAL,IAAa+J,OAAd,GAA0B,KAAKhK,GAAL,KAAaqK,CAD1B,EAEZ,KAAKrK,GAAL,IAAYgK,OAAb,GAAyB,KAAK/J,IAAL,KAAcoK,CAF1B,EAGb,KAAKnK,QAHQ,CAAf;AAKD,OApBD;AAqBA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACmG,IAAd,GAAqBnG,aAAa,CAACiG,UAAnC;AAEA;AACA;AACA;AACA;AACA;AACA;;AACAjG,MAAAA,aAAa,CAACoG,WAAd,GAA4B,SAASA,WAAT,CAAqBP,OAArB,EAA8B;AACxD,YAAIK,CAAJ;AACA,YAAIlK,MAAM,CAAC6J,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,YAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,YAAIA,OAAO,KAAK,EAAhB,EAAoB,OAAOlJ,QAAQ,CAAC,KAAKb,IAAN,EAAY,KAAKD,GAAjB,EAAsB,KAAKE,QAA3B,CAAf;;AACpB,YAAI8J,OAAO,GAAG,EAAd,EAAkB;AAChBK,UAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,iBAAOlJ,QAAQ,CACZ,KAAKb,IAAL,IAAaoK,CAAd,GAAoB,KAAKrK,GAAL,KAAagK,OADpB,EAEZ,KAAKhK,GAAL,IAAYqK,CAAb,GAAmB,KAAKpK,IAAL,KAAc+J,OAFpB,EAGb,KAAK9J,QAHQ,CAAf;AAKD;;AACD8J,QAAAA,OAAO,IAAI,EAAX;AACAK,QAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,eAAOlJ,QAAQ,CACZ,KAAKd,GAAL,IAAYqK,CAAb,GAAmB,KAAKpK,IAAL,KAAc+J,OADpB,EAEZ,KAAK/J,IAAL,IAAaoK,CAAd,GAAoB,KAAKrK,GAAL,KAAagK,OAFpB,EAGb,KAAK9J,QAHQ,CAAf;AAKD,OApBD;AAqBA;AACA;AACA;AACA;AACA;AACA;;;AACAiE,MAAAA,aAAa,CAACqG,IAAd,GAAqBrG,aAAa,CAACoG,WAAnC;AAEA;AACA;AACA;AACA;AACA;;AACApG,MAAAA,aAAa,CAACsG,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,YAAI,CAAC,KAAKvK,QAAV,EAAoB,OAAO,IAAP;AACpB,eAAOY,QAAQ,CAAC,KAAKd,GAAN,EAAW,KAAKC,IAAhB,EAAsB,KAAtB,CAAf;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACwE,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,YAAI,KAAKzI,QAAT,EAAmB,OAAO,IAAP;AACnB,eAAOY,QAAQ,CAAC,KAAKd,GAAN,EAAW,KAAKC,IAAhB,EAAsB,IAAtB,CAAf;AACD,OAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;;AACAkE,MAAAA,aAAa,CAACuG,OAAd,GAAwB,SAASA,OAAT,CAAiBlE,EAAjB,EAAqB;AAC3C,eAAOA,EAAE,GAAG,KAAKmE,SAAL,EAAH,GAAsB,KAAKC,SAAL,EAA/B;AACD,OAFD;AAIA;AACA;AACA;AACA;AACA;;;AACAzG,MAAAA,aAAa,CAACwG,SAAd,GAA0B,SAASA,SAAT,GAAqB;AAC7C,YAAIE,EAAE,GAAG,KAAK5K,IAAd;AAAA,YACE6K,EAAE,GAAG,KAAK9K,GADZ;AAEA,eAAO,CACL8K,EAAE,GAAG,IADA,EAEJA,EAAE,KAAK,CAAR,GAAa,IAFR,EAGJA,EAAE,KAAK,EAAR,GAAc,IAHT,EAILA,EAAE,KAAK,EAJF,EAKLD,EAAE,GAAG,IALA,EAMJA,EAAE,KAAK,CAAR,GAAa,IANR,EAOJA,EAAE,KAAK,EAAR,GAAc,IAPT,EAQLA,EAAE,KAAK,EARF,CAAP;AAUD,OAbD;AAeA;AACA;AACA;AACA;AACA;;;AACA1G,MAAAA,aAAa,CAACyG,SAAd,GAA0B,SAASA,SAAT,GAAqB;AAC7C,YAAIC,EAAE,GAAG,KAAK5K,IAAd;AAAA,YACE6K,EAAE,GAAG,KAAK9K,GADZ;AAEA,eAAO,CACL6K,EAAE,KAAK,EADF,EAEJA,EAAE,KAAK,EAAR,GAAc,IAFT,EAGJA,EAAE,KAAK,CAAR,GAAa,IAHR,EAILA,EAAE,GAAG,IAJA,EAKLC,EAAE,KAAK,EALF,EAMJA,EAAE,KAAK,EAAR,GAAc,IANT,EAOJA,EAAE,KAAK,CAAR,GAAa,IAPR,EAQLA,EAAE,GAAG,IARA,CAAP;AAUD,OAbD;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA/K,MAAAA,IAAI,CAACgL,SAAL,GAAiB,SAASA,SAAT,CAAmBC,KAAnB,EAA0B9K,QAA1B,EAAoCsG,EAApC,EAAwC;AACvD,eAAOA,EAAE,GACLzG,IAAI,CAACkL,WAAL,CAAiBD,KAAjB,EAAwB9K,QAAxB,CADK,GAELH,IAAI,CAACmL,WAAL,CAAiBF,KAAjB,EAAwB9K,QAAxB,CAFJ;AAGD,OAJD;AAMA;AACA;AACA;AACA;AACA;AACA;;;AACAH,MAAAA,IAAI,CAACkL,WAAL,GAAmB,SAASA,WAAT,CAAqBD,KAArB,EAA4B9K,QAA5B,EAAsC;AACvD,eAAO,IAAIH,IAAJ,CACLiL,KAAK,CAAC,CAAD,CAAL,GAAYA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAxB,GAA8BA,KAAK,CAAC,CAAD,CAAL,IAAY,EAA1C,GAAiDA,KAAK,CAAC,CAAD,CAAL,IAAY,EADxD,EAELA,KAAK,CAAC,CAAD,CAAL,GAAYA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAxB,GAA8BA,KAAK,CAAC,CAAD,CAAL,IAAY,EAA1C,GAAiDA,KAAK,CAAC,CAAD,CAAL,IAAY,EAFxD,EAGL9K,QAHK,CAAP;AAKD,OAND;AAQA;AACA;AACA;AACA;AACA;AACA;;;AACAH,MAAAA,IAAI,CAACmL,WAAL,GAAmB,SAASA,WAAT,CAAqBF,KAArB,EAA4B9K,QAA5B,EAAsC;AACvD,eAAO,IAAIH,IAAJ,CACJiL,KAAK,CAAC,CAAD,CAAL,IAAY,EAAb,GAAoBA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAhC,GAAuCA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAnD,GAAwDA,KAAK,CAAC,CAAD,CADxD,EAEJA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAb,GAAoBA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAhC,GAAuCA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAnD,GAAwDA,KAAK,CAAC,CAAD,CAFxD,EAGL9K,QAHK,CAAP;AAKD,OAND,C,CAQA;;;AACA,UAAI,OAAOiL,MAAP,KAAkB,UAAtB,EAAkC;AAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACEpL,QAAAA,IAAI,CAACqL,UAAL,GAAkB,SAASA,UAAT,CAAoB9K,KAApB,EAA2BJ,QAA3B,EAAqC;AACrD,cAAIyB,OAAO,GAAG0J,MAAM,CAACF,MAAM,CAACG,MAAP,CAAc,EAAd,EAAkBhL,KAAlB,CAAD,CAApB;AACA,cAAIsB,QAAQ,GAAGyJ,MAAM,CAACF,MAAM,CAACG,MAAP,CAAc,EAAd,EAAkBhL,KAAK,IAAI6K,MAAM,CAAC,EAAD,CAAjC,CAAD,CAArB;AACA,iBAAOrK,QAAQ,CAACa,OAAD,EAAUC,QAAV,EAAoB1B,QAApB,CAAf;AACD,SAJD,CARgC,CAchC;;;AACAH,QAAAA,IAAI,CAACiD,SAAL,GAAiB,SAASuI,mBAAT,CAA6BjL,KAA7B,EAAoCJ,QAApC,EAA8C;AAC7D,cAAI,OAAOI,KAAP,KAAiB,QAArB,EAA+B,OAAOP,IAAI,CAACqL,UAAL,CAAgB9K,KAAhB,EAAuBJ,QAAvB,CAAP;AAC/B,iBAAO8C,SAAS,CAAC1C,KAAD,EAAQJ,QAAR,CAAhB;AACD,SAHD;AAKA;AACF;AACA;AACA;AACA;;;AACEiE,QAAAA,aAAa,CAACqH,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,cAAIC,SAAS,GAAGN,MAAM,CAAC,KAAKnL,GAAL,KAAa,CAAd,CAAtB;AACA,cAAI0L,UAAU,GAAGP,MAAM,CAAC,KAAKjL,QAAL,GAAgB,KAAKD,IAAL,KAAc,CAA9B,GAAkC,KAAKA,IAAxC,CAAvB;AACA,iBAAQyL,UAAU,IAAIP,MAAM,CAAC,EAAD,CAArB,GAA6BM,SAApC;AACD,SAJD;AAKD;;yBAEc1L,I", "sourcesContent": ["/**\n * @license\n * Copyright 2009 The Closure Library Authors\n * Copyright 2020 <PERSON> / The long.js Authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * SPDX-License-Identifier: Apache-2.0\n */\n\n// WebAssembly optimizations to do native i64 multiplication and divide\nvar wasm = null;\ntry {\n  wasm = new WebAssembly.Instance(\n    new WebAssembly.Module(\n      new Uint8Array([\n        // \\0asm\n        0, 97, 115, 109,\n        // version 1\n        1, 0, 0, 0,\n\n        // section \"type\"\n        1, 13, 2,\n        // 0, () => i32\n        96, 0, 1, 127,\n        // 1, (i32, i32, i32, i32) => i32\n        96, 4, 127, 127, 127, 127, 1, 127,\n\n        // section \"function\"\n        3, 7, 6,\n        // 0, type 0\n        0,\n        // 1, type 1\n        1,\n        // 2, type 1\n        1,\n        // 3, type 1\n        1,\n        // 4, type 1\n        1,\n        // 5, type 1\n        1,\n\n        // section \"global\"\n        6, 6, 1,\n        // 0, \"high\", mutable i32\n        127, 1, 65, 0, 11,\n\n        // section \"export\"\n        7, 50, 6,\n        // 0, \"mul\"\n        3, 109, 117, 108, 0, 1,\n        // 1, \"div_s\"\n        5, 100, 105, 118, 95, 115, 0, 2,\n        // 2, \"div_u\"\n        5, 100, 105, 118, 95, 117, 0, 3,\n        // 3, \"rem_s\"\n        5, 114, 101, 109, 95, 115, 0, 4,\n        // 4, \"rem_u\"\n        5, 114, 101, 109, 95, 117, 0, 5,\n        // 5, \"get_high\"\n        8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n\n        // section \"code\"\n        10, 191, 1, 6,\n        // 0, \"get_high\"\n        4, 0, 35, 0, 11,\n        // 1, \"mul\"\n        36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32,\n        3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,\n        167, 11,\n        // 2, \"div_s\"\n        36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32,\n        3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,\n        167, 11,\n        // 3, \"div_u\"\n        36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32,\n        3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,\n        167, 11,\n        // 4, \"rem_s\"\n        36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32,\n        3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,\n        167, 11,\n        // 5, \"rem_u\"\n        36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32,\n        3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,\n        167, 11,\n      ]),\n    ),\n    {},\n  ).exports;\n} catch {\n  // no wasm support :(\n}\n\n/**\n * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n *  See the from* functions below for more convenient ways of constructing Longs.\n * @exports Long\n * @class A Long class for representing a 64 bit two's-complement integer value.\n * @param {number} low The low (signed) 32 bits of the long\n * @param {number} high The high (signed) 32 bits of the long\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @constructor\n */\nfunction Long(low, high, unsigned) {\n  /**\n   * The low 32 bits as a signed value.\n   * @type {number}\n   */\n  this.low = low | 0;\n\n  /**\n   * The high 32 bits as a signed value.\n   * @type {number}\n   */\n  this.high = high | 0;\n\n  /**\n   * Whether unsigned or not.\n   * @type {boolean}\n   */\n  this.unsigned = !!unsigned;\n}\n\n// The internal representation of a long is the two given signed, 32-bit values.\n// We use 32-bit pieces because these are the size of integers on which\n// Javascript performs bit-operations.  For operations like addition and\n// multiplication, we split each number into 16 bit pieces, which can easily be\n// multiplied within Javascript's floating-point representation without overflow\n// or change in sign.\n//\n// In the algorithms below, we frequently reduce the negative case to the\n// positive case by negating the input(s) and then post-processing the result.\n// Note that we must ALWAYS check specially whether those values are MIN_VALUE\n// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n// a positive number, it overflows back into a negative).  Not handling this\n// case would often result in infinite recursion.\n//\n// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n// methods on which they depend.\n\n/**\n * An indicator used to reliably determine if an object is a Long or not.\n * @type {boolean}\n * @const\n * @private\n */\nLong.prototype.__isLong__;\n\nObject.defineProperty(Long.prototype, \"__isLong__\", { value: true });\n\n/**\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n * @inner\n */\nfunction isLong(obj) {\n  return (obj && obj[\"__isLong__\"]) === true;\n}\n\n/**\n * @function\n * @param {*} value number\n * @returns {number}\n * @inner\n */\nfunction ctz32(value) {\n  var c = Math.clz32(value & -value);\n  return value ? 31 - c : c;\n}\n\n/**\n * Tests if the specified object is a Long.\n * @function\n * @param {*} obj Object\n * @returns {boolean}\n */\nLong.isLong = isLong;\n\n/**\n * A cache of the Long representations of small integer values.\n * @type {!Object}\n * @inner\n */\nvar INT_CACHE = {};\n\n/**\n * A cache of the Long representations of small unsigned integer values.\n * @type {!Object}\n * @inner\n */\nvar UINT_CACHE = {};\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromInt(value, unsigned) {\n  var obj, cachedObj, cache;\n  if (unsigned) {\n    value >>>= 0;\n    if ((cache = 0 <= value && value < 256)) {\n      cachedObj = UINT_CACHE[value];\n      if (cachedObj) return cachedObj;\n    }\n    obj = fromBits(value, 0, true);\n    if (cache) UINT_CACHE[value] = obj;\n    return obj;\n  } else {\n    value |= 0;\n    if ((cache = -128 <= value && value < 128)) {\n      cachedObj = INT_CACHE[value];\n      if (cachedObj) return cachedObj;\n    }\n    obj = fromBits(value, value < 0 ? -1 : 0, false);\n    if (cache) INT_CACHE[value] = obj;\n    return obj;\n  }\n}\n\n/**\n * Returns a Long representing the given 32 bit integer value.\n * @function\n * @param {number} value The 32 bit integer in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromInt = fromInt;\n\n/**\n * @param {number} value\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromNumber(value, unsigned) {\n  if (isNaN(value)) return unsigned ? UZERO : ZERO;\n  if (unsigned) {\n    if (value < 0) return UZERO;\n    if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n  } else {\n    if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n    if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n  }\n  if (value < 0) return fromNumber(-value, unsigned).neg();\n  return fromBits(\n    value % TWO_PWR_32_DBL | 0,\n    (value / TWO_PWR_32_DBL) | 0,\n    unsigned,\n  );\n}\n\n/**\n * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n * @function\n * @param {number} value The number in question\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromNumber = fromNumber;\n\n/**\n * @param {number} lowBits\n * @param {number} highBits\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromBits(lowBits, highBits, unsigned) {\n  return new Long(lowBits, highBits, unsigned);\n}\n\n/**\n * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n *  assumed to use 32 bits.\n * @function\n * @param {number} lowBits The low 32 bits\n * @param {number} highBits The high 32 bits\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long} The corresponding Long value\n */\nLong.fromBits = fromBits;\n\n/**\n * @function\n * @param {number} base\n * @param {number} exponent\n * @returns {number}\n * @inner\n */\nvar pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n/**\n * @param {string} str\n * @param {(boolean|number)=} unsigned\n * @param {number=} radix\n * @returns {!Long}\n * @inner\n */\nfunction fromString(str, unsigned, radix) {\n  if (str.length === 0) throw Error(\"empty string\");\n  if (typeof unsigned === \"number\") {\n    // For goog.math.long compatibility\n    radix = unsigned;\n    unsigned = false;\n  } else {\n    unsigned = !!unsigned;\n  }\n  if (\n    str === \"NaN\" ||\n    str === \"Infinity\" ||\n    str === \"+Infinity\" ||\n    str === \"-Infinity\"\n  )\n    return unsigned ? UZERO : ZERO;\n  radix = radix || 10;\n  if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n\n  var p;\n  if ((p = str.indexOf(\"-\")) > 0) throw Error(\"interior hyphen\");\n  else if (p === 0) {\n    return fromString(str.substring(1), unsigned, radix).neg();\n  }\n\n  // Do several (8) digits each time through the loop, so as to\n  // minimize the calls to the very expensive emulated div.\n  var radixToPower = fromNumber(pow_dbl(radix, 8));\n\n  var result = ZERO;\n  for (var i = 0; i < str.length; i += 8) {\n    var size = Math.min(8, str.length - i),\n      value = parseInt(str.substring(i, i + size), radix);\n    if (size < 8) {\n      var power = fromNumber(pow_dbl(radix, size));\n      result = result.mul(power).add(fromNumber(value));\n    } else {\n      result = result.mul(radixToPower);\n      result = result.add(fromNumber(value));\n    }\n  }\n  result.unsigned = unsigned;\n  return result;\n}\n\n/**\n * Returns a Long representation of the given string, written using the specified radix.\n * @function\n * @param {string} str The textual representation of the Long\n * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n * @returns {!Long} The corresponding Long value\n */\nLong.fromString = fromString;\n\n/**\n * @function\n * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n * @param {boolean=} unsigned\n * @returns {!Long}\n * @inner\n */\nfunction fromValue(val, unsigned) {\n  if (typeof val === \"number\") return fromNumber(val, unsigned);\n  if (typeof val === \"string\") return fromString(val, unsigned);\n  // Throws for non-objects, converts non-instanceof Long:\n  return fromBits(\n    val.low,\n    val.high,\n    typeof unsigned === \"boolean\" ? unsigned : val.unsigned,\n  );\n}\n\n/**\n * Converts the specified value to a Long using the appropriate from* function for its type.\n * @function\n * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {!Long}\n */\nLong.fromValue = fromValue;\n\n// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n// no runtime penalty for these.\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_16_DBL = 1 << 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_24_DBL = 1 << 24;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n/**\n * @type {!Long}\n * @const\n * @inner\n */\nvar TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ZERO = fromInt(0);\n\n/**\n * Signed zero.\n * @type {!Long}\n */\nLong.ZERO = ZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UZERO = fromInt(0, true);\n\n/**\n * Unsigned zero.\n * @type {!Long}\n */\nLong.UZERO = UZERO;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar ONE = fromInt(1);\n\n/**\n * Signed one.\n * @type {!Long}\n */\nLong.ONE = ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar UONE = fromInt(1, true);\n\n/**\n * Unsigned one.\n * @type {!Long}\n */\nLong.UONE = UONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar NEG_ONE = fromInt(-1);\n\n/**\n * Signed negative one.\n * @type {!Long}\n */\nLong.NEG_ONE = NEG_ONE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_VALUE = fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n\n/**\n * Maximum signed value.\n * @type {!Long}\n */\nLong.MAX_VALUE = MAX_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MAX_UNSIGNED_VALUE = fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n\n/**\n * Maximum unsigned value.\n * @type {!Long}\n */\nLong.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n/**\n * @type {!Long}\n * @inner\n */\nvar MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n\n/**\n * Minimum signed value.\n * @type {!Long}\n */\nLong.MIN_VALUE = MIN_VALUE;\n\n/**\n * @alias Long.prototype\n * @inner\n */\nvar LongPrototype = Long.prototype;\n\n/**\n * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toInt = function toInt() {\n  return this.unsigned ? this.low >>> 0 : this.low;\n};\n\n/**\n * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.toNumber = function toNumber() {\n  if (this.unsigned)\n    return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n  return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n};\n\n/**\n * Converts the Long to a string written in the specified radix.\n * @this {!Long}\n * @param {number=} radix Radix (2-36), defaults to 10\n * @returns {string}\n * @override\n * @throws {RangeError} If `radix` is out of range\n */\nLongPrototype.toString = function toString(radix) {\n  radix = radix || 10;\n  if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n  if (this.isZero()) return \"0\";\n  if (this.isNegative()) {\n    // Unsigned Longs are never negative\n    if (this.eq(MIN_VALUE)) {\n      // We need to change the Long value before it can be negated, so we remove\n      // the bottom-most digit in this base and then recurse to do the rest.\n      var radixLong = fromNumber(radix),\n        div = this.div(radixLong),\n        rem1 = div.mul(radixLong).sub(this);\n      return div.toString(radix) + rem1.toInt().toString(radix);\n    } else return \"-\" + this.neg().toString(radix);\n  }\n\n  // Do several (6) digits each time through the loop, so as to\n  // minimize the calls to the very expensive emulated div.\n  var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n    rem = this;\n  var result = \"\";\n  while (true) {\n    var remDiv = rem.div(radixToPower),\n      intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n      digits = intval.toString(radix);\n    rem = remDiv;\n    if (rem.isZero()) return digits + result;\n    else {\n      while (digits.length < 6) digits = \"0\" + digits;\n      result = \"\" + digits + result;\n    }\n  }\n};\n\n/**\n * Gets the high 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed high bits\n */\nLongPrototype.getHighBits = function getHighBits() {\n  return this.high;\n};\n\n/**\n * Gets the high 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned high bits\n */\nLongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n  return this.high >>> 0;\n};\n\n/**\n * Gets the low 32 bits as a signed integer.\n * @this {!Long}\n * @returns {number} Signed low bits\n */\nLongPrototype.getLowBits = function getLowBits() {\n  return this.low;\n};\n\n/**\n * Gets the low 32 bits as an unsigned integer.\n * @this {!Long}\n * @returns {number} Unsigned low bits\n */\nLongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n  return this.low >>> 0;\n};\n\n/**\n * Gets the number of bits needed to represent the absolute value of this Long.\n * @this {!Long}\n * @returns {number}\n */\nLongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n  if (this.isNegative())\n    // Unsigned Longs are never negative\n    return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n  var val = this.high != 0 ? this.high : this.low;\n  for (var bit = 31; bit > 0; bit--) if ((val & (1 << bit)) != 0) break;\n  return this.high != 0 ? bit + 33 : bit + 1;\n};\n\n/**\n * Tests if this Long can be safely represented as a JavaScript number.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isSafeInteger = function isSafeInteger() {\n  // 2^53-1 is the maximum safe value\n  var top11Bits = this.high >> 21;\n  // [0, 2^53-1]\n  if (!top11Bits) return true;\n  // > 2^53-1\n  if (this.unsigned) return false;\n  // [-2^53, -1] except -2^53\n  return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n};\n\n/**\n * Tests if this Long's value equals zero.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isZero = function isZero() {\n  return this.high === 0 && this.low === 0;\n};\n\n/**\n * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n * @returns {boolean}\n */\nLongPrototype.eqz = LongPrototype.isZero;\n\n/**\n * Tests if this Long's value is negative.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isNegative = function isNegative() {\n  return !this.unsigned && this.high < 0;\n};\n\n/**\n * Tests if this Long's value is positive or zero.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isPositive = function isPositive() {\n  return this.unsigned || this.high >= 0;\n};\n\n/**\n * Tests if this Long's value is odd.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isOdd = function isOdd() {\n  return (this.low & 1) === 1;\n};\n\n/**\n * Tests if this Long's value is even.\n * @this {!Long}\n * @returns {boolean}\n */\nLongPrototype.isEven = function isEven() {\n  return (this.low & 1) === 0;\n};\n\n/**\n * Tests if this Long's value equals the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.equals = function equals(other) {\n  if (!isLong(other)) other = fromValue(other);\n  if (\n    this.unsigned !== other.unsigned &&\n    this.high >>> 31 === 1 &&\n    other.high >>> 31 === 1\n  )\n    return false;\n  return this.high === other.high && this.low === other.low;\n};\n\n/**\n * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.eq = LongPrototype.equals;\n\n/**\n * Tests if this Long's value differs from the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.notEquals = function notEquals(other) {\n  return !this.eq(/* validates */ other);\n};\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.neq = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ne = LongPrototype.notEquals;\n\n/**\n * Tests if this Long's value is less than the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThan = function lessThan(other) {\n  return this.comp(/* validates */ other) < 0;\n};\n\n/**\n * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lt = LongPrototype.lessThan;\n\n/**\n * Tests if this Long's value is less than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n  return this.comp(/* validates */ other) <= 0;\n};\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.le = LongPrototype.lessThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThan = function greaterThan(other) {\n  return this.comp(/* validates */ other) > 0;\n};\n\n/**\n * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gt = LongPrototype.greaterThan;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n  return this.comp(/* validates */ other) >= 0;\n};\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n/**\n * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {boolean}\n */\nLongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n/**\n * Compares this Long's value with the specified's.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.compare = function compare(other) {\n  if (!isLong(other)) other = fromValue(other);\n  if (this.eq(other)) return 0;\n  var thisNeg = this.isNegative(),\n    otherNeg = other.isNegative();\n  if (thisNeg && !otherNeg) return -1;\n  if (!thisNeg && otherNeg) return 1;\n  // At this point the sign bits are the same\n  if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n  // Both are positive if at least one is unsigned\n  return other.high >>> 0 > this.high >>> 0 ||\n    (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n    ? -1\n    : 1;\n};\n\n/**\n * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n * @function\n * @param {!Long|number|bigint|string} other Other value\n * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n *  if the given one is greater\n */\nLongPrototype.comp = LongPrototype.compare;\n\n/**\n * Negates this Long's value.\n * @this {!Long}\n * @returns {!Long} Negated Long\n */\nLongPrototype.negate = function negate() {\n  if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n  return this.not().add(ONE);\n};\n\n/**\n * Negates this Long's value. This is an alias of {@link Long#negate}.\n * @function\n * @returns {!Long} Negated Long\n */\nLongPrototype.neg = LongPrototype.negate;\n\n/**\n * Returns the sum of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|bigint|string} addend Addend\n * @returns {!Long} Sum\n */\nLongPrototype.add = function add(addend) {\n  if (!isLong(addend)) addend = fromValue(addend);\n\n  // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n  var a48 = this.high >>> 16;\n  var a32 = this.high & 0xffff;\n  var a16 = this.low >>> 16;\n  var a00 = this.low & 0xffff;\n\n  var b48 = addend.high >>> 16;\n  var b32 = addend.high & 0xffff;\n  var b16 = addend.low >>> 16;\n  var b00 = addend.low & 0xffff;\n\n  var c48 = 0,\n    c32 = 0,\n    c16 = 0,\n    c00 = 0;\n  c00 += a00 + b00;\n  c16 += c00 >>> 16;\n  c00 &= 0xffff;\n  c16 += a16 + b16;\n  c32 += c16 >>> 16;\n  c16 &= 0xffff;\n  c32 += a32 + b32;\n  c48 += c32 >>> 16;\n  c32 &= 0xffff;\n  c48 += a48 + b48;\n  c48 &= 0xffff;\n  return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the difference of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|bigint|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.subtract = function subtract(subtrahend) {\n  if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n  return this.add(subtrahend.neg());\n};\n\n/**\n * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n * @function\n * @param {!Long|number|bigint|string} subtrahend Subtrahend\n * @returns {!Long} Difference\n */\nLongPrototype.sub = LongPrototype.subtract;\n\n/**\n * Returns the product of this and the specified Long.\n * @this {!Long}\n * @param {!Long|number|bigint|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.multiply = function multiply(multiplier) {\n  if (this.isZero()) return this;\n  if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n\n  // use wasm support if present\n  if (wasm) {\n    var low = wasm[\"mul\"](this.low, this.high, multiplier.low, multiplier.high);\n    return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n  }\n\n  if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n  if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n  if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n\n  if (this.isNegative()) {\n    if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());\n    else return this.neg().mul(multiplier).neg();\n  } else if (multiplier.isNegative()) return this.mul(multiplier.neg()).neg();\n\n  // If both longs are small, use float multiplication\n  if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n    return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n\n  // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n  // We can skip products that would overflow.\n\n  var a48 = this.high >>> 16;\n  var a32 = this.high & 0xffff;\n  var a16 = this.low >>> 16;\n  var a00 = this.low & 0xffff;\n\n  var b48 = multiplier.high >>> 16;\n  var b32 = multiplier.high & 0xffff;\n  var b16 = multiplier.low >>> 16;\n  var b00 = multiplier.low & 0xffff;\n\n  var c48 = 0,\n    c32 = 0,\n    c16 = 0,\n    c00 = 0;\n  c00 += a00 * b00;\n  c16 += c00 >>> 16;\n  c00 &= 0xffff;\n  c16 += a16 * b00;\n  c32 += c16 >>> 16;\n  c16 &= 0xffff;\n  c16 += a00 * b16;\n  c32 += c16 >>> 16;\n  c16 &= 0xffff;\n  c32 += a32 * b00;\n  c48 += c32 >>> 16;\n  c32 &= 0xffff;\n  c32 += a16 * b16;\n  c48 += c32 >>> 16;\n  c32 &= 0xffff;\n  c32 += a00 * b32;\n  c48 += c32 >>> 16;\n  c32 &= 0xffff;\n  c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n  c48 &= 0xffff;\n  return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n};\n\n/**\n * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n * @function\n * @param {!Long|number|bigint|string} multiplier Multiplier\n * @returns {!Long} Product\n */\nLongPrototype.mul = LongPrototype.multiply;\n\n/**\n * Returns this Long divided by the specified. The result is signed if this Long is signed or\n *  unsigned if this Long is unsigned.\n * @this {!Long}\n * @param {!Long|number|bigint|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.divide = function divide(divisor) {\n  if (!isLong(divisor)) divisor = fromValue(divisor);\n  if (divisor.isZero()) throw Error(\"division by zero\");\n\n  // use wasm support if present\n  if (wasm) {\n    // guard against signed division overflow: the largest\n    // negative number / -1 would be 1 larger than the largest\n    // positive number, due to two's complement.\n    if (\n      !this.unsigned &&\n      this.high === -0x80000000 &&\n      divisor.low === -1 &&\n      divisor.high === -1\n    ) {\n      // be consistent with non-wasm code path\n      return this;\n    }\n    var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n      this.low,\n      this.high,\n      divisor.low,\n      divisor.high,\n    );\n    return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n  }\n\n  if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n  var approx, rem, res;\n  if (!this.unsigned) {\n    // This section is only relevant for signed longs and is derived from the\n    // closure library as a whole.\n    if (this.eq(MIN_VALUE)) {\n      if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n        return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n      else if (divisor.eq(MIN_VALUE)) return ONE;\n      else {\n        // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n        var halfThis = this.shr(1);\n        approx = halfThis.div(divisor).shl(1);\n        if (approx.eq(ZERO)) {\n          return divisor.isNegative() ? ONE : NEG_ONE;\n        } else {\n          rem = this.sub(divisor.mul(approx));\n          res = approx.add(rem.div(divisor));\n          return res;\n        }\n      }\n    } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n    if (this.isNegative()) {\n      if (divisor.isNegative()) return this.neg().div(divisor.neg());\n      return this.neg().div(divisor).neg();\n    } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n    res = ZERO;\n  } else {\n    // The algorithm below has not been made for unsigned longs. It's therefore\n    // required to take special care of the MSB prior to running it.\n    if (!divisor.unsigned) divisor = divisor.toUnsigned();\n    if (divisor.gt(this)) return UZERO;\n    if (divisor.gt(this.shru(1)))\n      // 15 >>> 1 = 7 ; with divisor = 8 ; true\n      return UONE;\n    res = UZERO;\n  }\n\n  // Repeat the following until the remainder is less than other:  find a\n  // floating-point that approximates remainder / other *from below*, add this\n  // into the result, and subtract it from the remainder.  It is critical that\n  // the approximate value is less than or equal to the real value so that the\n  // remainder never becomes negative.\n  rem = this;\n  while (rem.gte(divisor)) {\n    // Approximate the result of division. This may be a little greater or\n    // smaller than the actual value.\n    approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n    // We will tweak the approximate result by changing it in the 48-th digit or\n    // the smallest non-fractional digit, whichever is larger.\n    var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n      delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n      // Decrease the approximation until it is smaller than the remainder.  Note\n      // that if it is too large, the product overflows and is negative.\n      approxRes = fromNumber(approx),\n      approxRem = approxRes.mul(divisor);\n    while (approxRem.isNegative() || approxRem.gt(rem)) {\n      approx -= delta;\n      approxRes = fromNumber(approx, this.unsigned);\n      approxRem = approxRes.mul(divisor);\n    }\n\n    // We know the answer can't be zero... and actually, zero would cause\n    // infinite recursion since we would make no progress.\n    if (approxRes.isZero()) approxRes = ONE;\n\n    res = res.add(approxRes);\n    rem = rem.sub(approxRem);\n  }\n  return res;\n};\n\n/**\n * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n * @function\n * @param {!Long|number|bigint|string} divisor Divisor\n * @returns {!Long} Quotient\n */\nLongPrototype.div = LongPrototype.divide;\n\n/**\n * Returns this Long modulo the specified.\n * @this {!Long}\n * @param {!Long|number|bigint|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.modulo = function modulo(divisor) {\n  if (!isLong(divisor)) divisor = fromValue(divisor);\n\n  // use wasm support if present\n  if (wasm) {\n    var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n      this.low,\n      this.high,\n      divisor.low,\n      divisor.high,\n    );\n    return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n  }\n\n  return this.sub(this.div(divisor).mul(divisor));\n};\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|bigint|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.mod = LongPrototype.modulo;\n\n/**\n * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n * @function\n * @param {!Long|number|bigint|string} divisor Divisor\n * @returns {!Long} Remainder\n */\nLongPrototype.rem = LongPrototype.modulo;\n\n/**\n * Returns the bitwise NOT of this Long.\n * @this {!Long}\n * @returns {!Long}\n */\nLongPrototype.not = function not() {\n  return fromBits(~this.low, ~this.high, this.unsigned);\n};\n\n/**\n * Returns count leading zeros of this Long.\n * @this {!Long}\n * @returns {!number}\n */\nLongPrototype.countLeadingZeros = function countLeadingZeros() {\n  return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n};\n\n/**\n * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n * @function\n * @param {!Long}\n * @returns {!number}\n */\nLongPrototype.clz = LongPrototype.countLeadingZeros;\n\n/**\n * Returns count trailing zeros of this Long.\n * @this {!Long}\n * @returns {!number}\n */\nLongPrototype.countTrailingZeros = function countTrailingZeros() {\n  return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n};\n\n/**\n * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n * @function\n * @param {!Long}\n * @returns {!number}\n */\nLongPrototype.ctz = LongPrototype.countTrailingZeros;\n\n/**\n * Returns the bitwise AND of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.and = function and(other) {\n  if (!isLong(other)) other = fromValue(other);\n  return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise OR of this Long and the specified.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.or = function or(other) {\n  if (!isLong(other)) other = fromValue(other);\n  return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n};\n\n/**\n * Returns the bitwise XOR of this Long and the given one.\n * @this {!Long}\n * @param {!Long|number|bigint|string} other Other Long\n * @returns {!Long}\n */\nLongPrototype.xor = function xor(other) {\n  if (!isLong(other)) other = fromValue(other);\n  return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftLeft = function shiftLeft(numBits) {\n  if (isLong(numBits)) numBits = numBits.toInt();\n  if ((numBits &= 63) === 0) return this;\n  else if (numBits < 32)\n    return fromBits(\n      this.low << numBits,\n      (this.high << numBits) | (this.low >>> (32 - numBits)),\n      this.unsigned,\n    );\n  else return fromBits(0, this.low << (numBits - 32), this.unsigned);\n};\n\n/**\n * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shl = LongPrototype.shiftLeft;\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRight = function shiftRight(numBits) {\n  if (isLong(numBits)) numBits = numBits.toInt();\n  if ((numBits &= 63) === 0) return this;\n  else if (numBits < 32)\n    return fromBits(\n      (this.low >>> numBits) | (this.high << (32 - numBits)),\n      this.high >> numBits,\n      this.unsigned,\n    );\n  else\n    return fromBits(\n      this.high >> (numBits - 32),\n      this.high >= 0 ? 0 : -1,\n      this.unsigned,\n    );\n};\n\n/**\n * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr = LongPrototype.shiftRight;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n  if (isLong(numBits)) numBits = numBits.toInt();\n  if ((numBits &= 63) === 0) return this;\n  if (numBits < 32)\n    return fromBits(\n      (this.low >>> numBits) | (this.high << (32 - numBits)),\n      this.high >>> numBits,\n      this.unsigned,\n    );\n  if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n  return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n};\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Shifted Long\n */\nLongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n/**\n * Returns this Long with bits rotated to the left by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateLeft = function rotateLeft(numBits) {\n  var b;\n  if (isLong(numBits)) numBits = numBits.toInt();\n  if ((numBits &= 63) === 0) return this;\n  if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n  if (numBits < 32) {\n    b = 32 - numBits;\n    return fromBits(\n      (this.low << numBits) | (this.high >>> b),\n      (this.high << numBits) | (this.low >>> b),\n      this.unsigned,\n    );\n  }\n  numBits -= 32;\n  b = 32 - numBits;\n  return fromBits(\n    (this.high << numBits) | (this.low >>> b),\n    (this.low << numBits) | (this.high >>> b),\n    this.unsigned,\n  );\n};\n/**\n * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotl = LongPrototype.rotateLeft;\n\n/**\n * Returns this Long with bits rotated to the right by the given amount.\n * @this {!Long}\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotateRight = function rotateRight(numBits) {\n  var b;\n  if (isLong(numBits)) numBits = numBits.toInt();\n  if ((numBits &= 63) === 0) return this;\n  if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n  if (numBits < 32) {\n    b = 32 - numBits;\n    return fromBits(\n      (this.high << b) | (this.low >>> numBits),\n      (this.low << b) | (this.high >>> numBits),\n      this.unsigned,\n    );\n  }\n  numBits -= 32;\n  b = 32 - numBits;\n  return fromBits(\n    (this.low << b) | (this.high >>> numBits),\n    (this.high << b) | (this.low >>> numBits),\n    this.unsigned,\n  );\n};\n/**\n * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n * @function\n * @param {number|!Long} numBits Number of bits\n * @returns {!Long} Rotated Long\n */\nLongPrototype.rotr = LongPrototype.rotateRight;\n\n/**\n * Converts this Long to signed.\n * @this {!Long}\n * @returns {!Long} Signed long\n */\nLongPrototype.toSigned = function toSigned() {\n  if (!this.unsigned) return this;\n  return fromBits(this.low, this.high, false);\n};\n\n/**\n * Converts this Long to unsigned.\n * @this {!Long}\n * @returns {!Long} Unsigned long\n */\nLongPrototype.toUnsigned = function toUnsigned() {\n  if (this.unsigned) return this;\n  return fromBits(this.low, this.high, true);\n};\n\n/**\n * Converts this Long to its byte representation.\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @this {!Long}\n * @returns {!Array.<number>} Byte representation\n */\nLongPrototype.toBytes = function toBytes(le) {\n  return le ? this.toBytesLE() : this.toBytesBE();\n};\n\n/**\n * Converts this Long to its little endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Little endian byte representation\n */\nLongPrototype.toBytesLE = function toBytesLE() {\n  var hi = this.high,\n    lo = this.low;\n  return [\n    lo & 0xff,\n    (lo >>> 8) & 0xff,\n    (lo >>> 16) & 0xff,\n    lo >>> 24,\n    hi & 0xff,\n    (hi >>> 8) & 0xff,\n    (hi >>> 16) & 0xff,\n    hi >>> 24,\n  ];\n};\n\n/**\n * Converts this Long to its big endian byte representation.\n * @this {!Long}\n * @returns {!Array.<number>} Big endian byte representation\n */\nLongPrototype.toBytesBE = function toBytesBE() {\n  var hi = this.high,\n    lo = this.low;\n  return [\n    hi >>> 24,\n    (hi >>> 16) & 0xff,\n    (hi >>> 8) & 0xff,\n    hi & 0xff,\n    lo >>> 24,\n    (lo >>> 16) & 0xff,\n    (lo >>> 8) & 0xff,\n    lo & 0xff,\n  ];\n};\n\n/**\n * Creates a Long from its byte representation.\n * @param {!Array.<number>} bytes Byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @param {boolean=} le Whether little or big endian, defaults to big endian\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytes = function fromBytes(bytes, unsigned, le) {\n  return le\n    ? Long.fromBytesLE(bytes, unsigned)\n    : Long.fromBytesBE(bytes, unsigned);\n};\n\n/**\n * Creates a Long from its little endian byte representation.\n * @param {!Array.<number>} bytes Little endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n  return new Long(\n    bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24),\n    bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24),\n    unsigned,\n  );\n};\n\n/**\n * Creates a Long from its big endian byte representation.\n * @param {!Array.<number>} bytes Big endian byte representation\n * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n * @returns {Long} The corresponding Long value\n */\nLong.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n  return new Long(\n    (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7],\n    (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3],\n    unsigned,\n  );\n};\n\n// Support conversion to/from BigInt where available\nif (typeof BigInt === \"function\") {\n  /**\n   * Returns a Long representing the given big integer.\n   * @function\n   * @param {number} value The big integer value\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromBigInt = function fromBigInt(value, unsigned) {\n    var lowBits = Number(BigInt.asIntN(32, value));\n    var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n    return fromBits(lowBits, highBits, unsigned);\n  };\n\n  // Override\n  Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n    if (typeof value === \"bigint\") return Long.fromBigInt(value, unsigned);\n    return fromValue(value, unsigned);\n  };\n\n  /**\n   * Converts the Long to its big integer representation.\n   * @this {!Long}\n   * @returns {bigint}\n   */\n  LongPrototype.toBigInt = function toBigInt() {\n    var lowBigInt = BigInt(this.low >>> 0);\n    var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n    return (highBigInt << BigInt(32)) | lowBigInt;\n  };\n}\n\nexport default Long;\n"]}