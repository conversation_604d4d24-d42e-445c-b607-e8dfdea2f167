
import { MainData } from "../data/MainData";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameIns } from "../GameIns";
import { MainPlane } from "../ui/plane/mainPlane/MainPlane";
import { MyApp } from "../../MyApp";
import { MainPlaneLv } from "../../AutoGen/Luban/schema";
import BattleLayer from "../ui/layer/BattleLayer";
import { SpriteAtlas } from "cc";
import GameResourceList from "../const/GameResourceList";

export enum Attribute {
    hp = 1,
    atk = 2,
    hpNum = 5,
    atkNum = 6,
    cirt = 11,
    cirtAtk = 12,
    bossAtk = 13,
    turretArk = 14,
    bulletLessDemage = 15,
    colLessDemage = 16,
    heal = 17,
    planeLessDemage = 18,
    bossLessDemage = 19,
    hurtAddAtk = 20,
    passAddHp = 21,
    passAddAtk = 22,
    killAddHp = 51,
    killAddAtk = 52,
    MainHighHpAtk = 53,
    EnemyLowHpAtk = 54,
}


export class MainPlaneManager extends SingletonBase<MainPlaneManager> {

    mainPlane: MainPlane = null;
    curPlaneId: number = 0;
    curPlaneStarId: number = 0;
    mainData: MainData = new MainData();
    _mainConfig: any = null; // MainPlaneConfig
    _mainLvConfig: MainPlaneLv;

    m_fireEnable: boolean = false;
    hurtTotal: number = 0;

    get fireEnable(): boolean {
        return this.m_fireEnable;
    }

    set fireEnable(value: boolean) {
        this.m_fireEnable = value;
        if (this.mainPlane) {
            this.mainPlane.setFireEnable(value);
        }
    }

    set moveAble(value: boolean) {
        this.m_fireEnable = value;
        if (this.mainPlane) {
            this.mainPlane.setMoveAble(value);
        }
    }

    async preload() {
        this.curPlaneId = 701;//模版id
        this.curPlaneStarId = 70100;//根据星级，获取的id(暂时写死，后续会根据玩家的战绩信息获取)

        this.mainData.id = this.curPlaneStarId;
        this._mainConfig = MyApp.lubanTables.TbMainPlane.get(this.curPlaneStarId);
        this._mainLvConfig = MyApp.lubanTables.TbMainPlaneLv.get(this.curPlaneId * 1000 + 1);

        GameIns.battleManager.addLoadCount(1);
        await this.createMainPlane();
        GameIns.battleManager.checkLoadFinish();
        this.reset();
    }

    /**
     * 创建主飞机
     * @param isTrans 是否为特殊状态
     * @returns 主飞机对象
     */
    async createMainPlane(isTrans: boolean = false): Promise<MainPlane | null> {
        if (this.mainPlane) {
            return this.mainPlane;
        }

        if (isTrans) {
            await MyApp.resMgr.loadAsync(GameResourceList.atlas_mainPlane + "701",SpriteAtlas);
        }

        const plane = await GameIns.prefabManager.createComponent(MainPlane) as MainPlane;
        if (this.mainPlane) {
            plane && plane.destroy();
        } else {
            this.mainPlane = plane;
            BattleLayer.me?.addMainPlane();
            this.mainPlane.node.active = false;
        }

        return this.mainPlane;
    }

    mainReset() {
        if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
        }
    }

    reset() {
        this.mainData.die = false;

        if (this.mainPlane) {
            this.mainPlane.battleQuit();
        }

        this.setScreenLv(1);
        this.refreshPlaneData();
        this.fireEnable = false;
    }

    refreshPlaneData(): void {
        this.mainData.attack = Math.floor(this._mainLvConfig.atk);
        this.mainData.hp = Math.floor(this._mainLvConfig.hp);
        this.mainData.maxhp = this.mainData.hp;
    }

    /**
    * 设置屏幕等级
    * @param level 屏幕等级
    */
    setScreenLv(level: number) {
        this.mainData.screenLv = level;

        if (this.mainPlane) {
            this.mainPlane.changeScreenLv(level);
        }
    }
    /**
 * 根据 ID 获取飞机类型
 * @param id 飞机 ID
 */
    idToType(id: number): number {
        return Math.floor(id / 100);
    }
}
