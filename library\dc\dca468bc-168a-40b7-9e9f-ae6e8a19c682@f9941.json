{"__type__": "cc.SpriteFrame", "content": {"name": "leidian", "atlas": "", "rect": {"x": 27, "y": 1, "width": 75, "height": 126}, "offset": {"x": 0.5, "y": 0}, "originalSize": {"width": 128, "height": 128}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-37.5, -63, 0, 37.5, -63, 0, -37.5, 63, 0, 37.5, 63, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [27, 127, 102, 127, 27, 1, 102, 1], "nuv": [0.2109375, 0.0078125, 0.796875, 0.0078125, 0.2109375, 0.9921875, 0.796875, 0.9921875], "minPos": {"x": -37.5, "y": -63, "z": 0}, "maxPos": {"x": 37.5, "y": 63, "z": 0}}, "texture": "dca468bc-168a-40b7-9e9f-ae6e8a19c682@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}