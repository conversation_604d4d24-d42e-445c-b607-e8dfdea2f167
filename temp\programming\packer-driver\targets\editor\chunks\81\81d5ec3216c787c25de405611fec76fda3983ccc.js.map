{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts"], "names": ["Bullet<PERSON>ly", "v2", "GameConst", "GameIns", "Tools", "BaseComp", "FCollider", "constructor", "props", "m_aim", "m_aimWait", "m_isEnemy", "m_target", "m_targetCollider", "m_beginTime", "m_changeTime", "m_waittime", "m_idx", "m_speed", "m_mainEntity", "m_angle", "m_speedX", "m_speedY", "bustyle", "para", "onInit", "initialve", "Math", "random", "spdiff", "setData", "angle", "mainEntity", "isEnemy", "refreshSpeed", "waittime", "length", "calculateSpeed", "deltaTime", "time", "accnumber", "newSpeed", "acc", "speed", "radian", "PI", "sin", "cos", "update", "GameAble", "getAngle", "adjustedAngle", "m_entity", "node", "layerSpeed", "sceneManager", "getLayerSpeed", "posx", "position", "x", "posy", "y", "setPosition", "get<PERSON><PERSON><PERSON>", "mainPlaneNode", "mainPlaneManager", "mainPlane", "targetPosition", "entity", "getComp", "planeManager", "getRandomTargetEnemy"], "mappings": ";;;mIAQqBA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARZC,MAAAA,E,OAAAA,E;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Q;;AACAC,MAAAA,S;;;;;;;;;yBAGcN,S,GAAN,MAAMA,SAAN;AAAA;AAAA,gCAAiC;AAkB9B;AAGdO,QAAAA,WAAW,CAACC,KAAD,EAAQ;AACf;AADe,eApBnBC,KAoBmB,GApBX,KAoBW;AApBJ;AAoBI,eAnBnBC,SAmBmB,GAnBP,CAmBO;AAnBJ;AAmBI,eAlBnBC,SAkBmB,GAlBP,KAkBO;AAlBA;AAkBA,eAjBnBC,QAiBmB,GAjBR,IAiBQ;AAjBF;AAiBE,eAhBnBC,gBAgBmB,GAhBU,IAgBV;AAhBgB;AAgBhB,eAfnBL,KAemB;AAfZ;AAeY,eAZnBM,WAYmB,GAZL,CAYK;AAZF;AAYE,eAXnBC,YAWmB,GAXJ,CAWI;AAXD;AAWC,eAVnBC,UAUmB,GAVN,CAUM;AAVH;AAUG,eATnBC,KASmB,GATX,CASW;AATR;AASQ,eARnBC,OAQmB,GART,CAQS;AARN;AAQM,eANnBC,YAMmB;AANC;AAMD,eALnBC,OAKmB,GALT,CAKS;AALN;AAKM,eAJnBC,QAImB,GAJR,CAIQ;AAJL;AAIK,eAHnBC,QAGmB,GAHR,CAGQ;AAEf,eAAKd,KAAL,GAAaA,KAAb,CAFe,CAEK;;AACpB,eAAKC,KAAL,GAAaD,KAAK,CAACe,OAAN,KAAkB,EAA/B,CAHe,CAGoB;;AACnC,eAAKb,SAAL,GAAiB,KAAKD,KAAL,GAAaD,KAAK,CAACgB,IAAN,CAAW,CAAX,CAAb,GAA6B,CAA9C,CAJe,CAIkC;AACpD;AAED;AACJ;AACA;;;AACIC,QAAAA,MAAM,GAAG;AACL,eAAKX,WAAL,GAAmB,CAAnB,CADK,CACiB;;AACtB,eAAKC,YAAL,GAAoB,CAApB,CAFK,CAEkB;;AACvB,eAAKC,UAAL,GAAkB,CAAlB,CAHK,CAGgB;;AACrB,eAAKC,KAAL,GAAa,CAAb,CAJK,CAIW;;AAChB,eAAKC,OAAL,GAAe,KAAKV,KAAL,CAAWkB,SAAX,GAAuBC,IAAI,CAACC,MAAL,KAAgB,KAAKpB,KAAL,CAAWqB,MAAjE,CALK,CAKoE;AAC5E;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACC,KAAD,EAAQC,UAAR,EAAoBC,OAAO,GAAG,IAA9B,EAAoC;AACvC,eAAKtB,SAAL,GAAiBsB,OAAjB;AACA,eAAKhB,KAAL,GAAa,CAAb;AACA,eAAKE,YAAL,GAAoBa,UAApB;AACA,eAAKZ,OAAL,GAAeW,KAAf;AACA,eAAKG,YAAL,CAAkB,KAAKhB,OAAvB;;AAEA,cAAI,KAAKV,KAAL,CAAW2B,QAAX,CAAoBC,MAApB,GAA6B,CAAjC,EAAoC;AAChC,iBAAKpB,UAAL,GAAkB,KAAKR,KAAL,CAAW2B,QAAX,CAAoB,KAAKlB,KAAzB,CAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIoB,QAAAA,cAAc,CAACC,SAAD,EAAY;AACtB,cAAI,KAAKxB,WAAL,IAAoB,KAAKN,KAAL,CAAW+B,IAA/B,IAAuC,KAAKxB,YAAL,GAAoB,KAAKP,KAAL,CAAWgC,SAA1E,EAAqF;AACjF,iBAAKzB,YAAL,IAAqBuB,SAArB;AACA,kBAAMG,QAAQ,GAAG,KAAKvB,OAAL,GAAe,KAAKH,YAAL,GAAoB,KAAKP,KAAL,CAAWkC,GAA/D;AACA,iBAAKR,YAAL,CAAkBO,QAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIP,QAAAA,YAAY,CAACS,KAAD,EAAQ;AAChB,gBAAMC,MAAM,GAAI,CAAC,KAAKxB,OAAN,GAAgB,GAAjB,GAAwBO,IAAI,CAACkB,EAA5C;AACA,eAAKxB,QAAL,GAAgBM,IAAI,CAACmB,GAAL,CAASF,MAAT,IAAmBD,KAAnC;AACA,eAAKrB,QAAL,GAAgBK,IAAI,CAACoB,GAAL,CAASH,MAAT,IAAmBD,KAAnC;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,MAAM,CAACV,SAAD,EAAY;AACd,cAAI,CAAC;AAAA;AAAA,sCAAUW,QAAf,EAAyB;AACrB;AACH;;AAED,cAAIX,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AAED,cAAI,KAAKtB,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKA,UAAL,IAAmBsB,SAAnB;AACH,WAFD,MAEO;AACH,gBAAI,KAAKrB,KAAL,GAAa,KAAKT,KAAL,CAAW2B,QAAX,CAAoBC,MAArC,EAA6C;AACzC,mBAAKnB,KAAL;AACA,mBAAKD,UAAL,GAAkB,KAAKR,KAAL,CAAW2B,QAAX,CAAoB,KAAKlB,KAAzB,CAAlB;AACA;AACH;;AAED,gBAAI,KAAKP,SAAL,GAAiB,CAArB,EAAwB;AACpB,mBAAKA,SAAL,IAAkB4B,SAAlB;;AACA,kBAAI,KAAK7B,KAAT,EAAgB;AACZ,qBAAKW,OAAL,GAAe,CAAC,KAAK8B,QAAL,EAAhB;AACA,sBAAMC,aAAa,GAAG,KAAKxC,SAAL,GAAiB,KAAKS,OAAL,GAAe,GAAhC,GAAsC,KAAKA,OAAjE;AACA,qBAAKgC,QAAL,CAAcC,IAAd,CAAmBtB,KAAnB,GAA2BoB,aAA3B;;AAEA,oBAAI,KAAKzC,SAAL,IAAkB,CAAtB,EAAyB;AACrB,uBAAKwB,YAAL,CAAkB,KAAKhB,OAAvB;AACH;AACJ;AACJ,aAXD,MAWO;AACH,mBAAKJ,WAAL,IAAoBwB,SAApB;AACA,mBAAKD,cAAL,CAAoBC,SAApB;AAEA,kBAAIgB,UAAU,GAAG,CAAjB;;AACA,kBAAI,KAAKnC,YAAT,EAAuB;AACnBmC,gBAAAA,UAAU,GAAG;AAAA;AAAA,wCAAQC,YAAR,CAAqBC,aAArB,CAAmC,KAAKrC,YAAxC,CAAb;AACH;;AAED,kBAAIsC,IAAI,GAAG,KAAKL,QAAL,CAAcC,IAAd,CAAmBK,QAAnB,CAA4BC,CAA5B,GAAgC,KAAKtC,QAAL,GAAgBiB,SAA3D;AACA,kBAAIsB,IAAI,GAAG,KAAKR,QAAL,CAAcC,IAAd,CAAmBK,QAAnB,CAA4BG,CAA5B,GAAgC,KAAKvC,QAAL,GAAgBgB,SAAhD,GAA4DgB,UAAU,GAAGhB,SAApF;AACA,mBAAKc,QAAL,CAAcC,IAAd,CAAmBS,WAAnB,CAA+BL,IAA/B,EAAqCG,IAArC;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIV,QAAAA,QAAQ,GAAG;AACP,cAAI,KAAKvC,SAAT,EAAoB;AAChB,iBAAKoD,SAAL;AACH;;AAED,cAAI,CAAC,KAAKnD,QAAV,EAAoB;AAChB,mBAAO,CAAP;AACH;;AAED,gBAAM8C,QAAQ,GAAG,KAAKN,QAAL,CAAcC,IAAd,CAAmBK,QAApC;;AACA,cAAI,KAAK/C,SAAT,EAAoB;AAChB,kBAAMqD,aAAa,GAAG;AAAA;AAAA,oCAAQC,gBAAR,CAAyBC,SAAzB,CAAmCb,IAAzD;AACA,mBAAOW,aAAa,GAAG;AAAA;AAAA,gCAAMd,QAAN,CAAeQ,QAAf,EAAyBzD,EAAE,CAAC+D,aAAa,CAACN,QAAd,CAAuBC,CAAxB,EAA0BK,aAAa,CAACN,QAAd,CAAuBG,CAAjD,CAA3B,CAAH,GAAqF,CAAzG;AACH,WAHD,MAGO;AACH,kBAAMM,cAAc,GAAG,KAAKtD,gBAAL,CAAsBuD,MAAtB,CAA6Bf,IAA7B,CAAkCK,QAAzD;AACA,mBAAO;AAAA;AAAA,gCAAMR,QAAN,CAAeQ,QAAf,EAAyBS,cAAzB,CAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACIJ,QAAAA,SAAS,GAAG;AACR,cAAI,CAAC,KAAKnD,QAAV,EAAoB;AAChB,gBAAI,KAAKD,SAAT,EAAoB;AAChB,mBAAKC,QAAL,GAAgB;AAAA;AAAA,sCAAQqD,gBAAR,CAAyBC,SAAzC;AACA,mBAAKrD,gBAAL,GAAwB;AAAA;AAAA,sCAAQoD,gBAAR,CAAyBC,SAAzB,CAAmCG,OAAnC;AAAA;AAAA,yCAAxB;AACH,aAHD,MAGO;AACH,mBAAKxD,gBAAL,GAAwB;AAAA;AAAA,sCAAQyD,YAAR,CAAqBC,oBAArB,EAAxB;AACA,mBAAK3D,QAAL,GAAgB,KAAKC,gBAAL,CAAsBuD,MAAtC;AACH;AACJ;AACJ;;AApK2C,O", "sourcesContent": ["import { v2 } from \"cc\";\r\nimport { GameConst } from \"../../const/GameConst\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport { Tools } from \"../../utils/Tools\";\r\nimport BaseComp from \"../base/BaseComp\";\r\nimport FCollider from \"../../collider-system/FCollider\";\r\n\r\n\r\nexport default class BulletFly extends BaseComp {\r\n    m_aim = false; // 是否为瞄准子弹\r\n    m_aimWait = 0; // 瞄准等待时间\r\n    m_isEnemy = false; // 是否为敌方子弹\r\n    m_target = null; // 目标实体\r\n    m_targetCollider:FCollider = null; // 目标碰撞组件\r\n    props; // 子弹配置参数\r\n\r\n\r\n    m_beginTime = 0; // 子弹开始时间\r\n    m_changeTime = 0; // 子弹加速时间\r\n    m_waittime = 0; // 子弹等待时间\r\n    m_idx = 0; // 当前等待时间索引\r\n    m_speed = 0; // 初始速度\r\n\r\n    m_mainEntity: null; // 发射子弹的实体\r\n    m_angle = 0; // 子弹角度\r\n    m_speedX = 0; // X方向速度\r\n    m_speedY = 0; // Y方向速度\r\n\r\n\r\n    constructor(props) {\r\n        super();\r\n        this.props = props; // 子弹配置参数\r\n        this.m_aim = props.bustyle === 58; // 判断是否为瞄准子弹\r\n        this.m_aimWait = this.m_aim ? props.para[2] : 0; // 设置瞄准等待时间\r\n    }\r\n\r\n    /**\r\n     * 初始化组件\r\n     */\r\n    onInit() {\r\n        this.m_beginTime = 0; // 子弹开始时间\r\n        this.m_changeTime = 0; // 子弹加速时间\r\n        this.m_waittime = 0; // 子弹等待时间\r\n        this.m_idx = 0; // 当前等待时间索引\r\n        this.m_speed = this.props.initialve + Math.random() * this.props.spdiff; // 初始速度\r\n    }\r\n\r\n    /**\r\n     * 设置子弹数据\r\n     * @param {number} angle 子弹角度\r\n     * @param {Entity} mainEntity 发射子弹的实体\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     */\r\n    setData(angle, mainEntity, isEnemy = true) {\r\n        this.m_isEnemy = isEnemy;\r\n        this.m_idx = 0;\r\n        this.m_mainEntity = mainEntity;\r\n        this.m_angle = angle;\r\n        this.refreshSpeed(this.m_speed);\r\n\r\n        if (this.props.waittime.length > 0) {\r\n            this.m_waittime = this.props.waittime[this.m_idx];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 计算子弹速度\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    calculateSpeed(deltaTime) {\r\n        if (this.m_beginTime >= this.props.time && this.m_changeTime < this.props.accnumber) {\r\n            this.m_changeTime += deltaTime;\r\n            const newSpeed = this.m_speed + this.m_changeTime * this.props.acc;\r\n            this.refreshSpeed(newSpeed);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 刷新子弹速度\r\n     * @param {number} speed 子弹速度\r\n     */\r\n    refreshSpeed(speed) {\r\n        const radian = (-this.m_angle / 180) * Math.PI;\r\n        this.m_speedX = Math.sin(radian) * speed;\r\n        this.m_speedY = Math.cos(radian) * speed;\r\n    }\r\n\r\n    /**\r\n     * 更新子弹逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 限制最大帧间隔时间\r\n        }\r\n\r\n        if (this.m_waittime > 0) {\r\n            this.m_waittime -= deltaTime;\r\n        } else {\r\n            if (this.m_idx < this.props.waittime.length) {\r\n                this.m_idx++;\r\n                this.m_waittime = this.props.waittime[this.m_idx];\r\n                return;\r\n            }\r\n\r\n            if (this.m_aimWait > 0) {\r\n                this.m_aimWait -= deltaTime;\r\n                if (this.m_aim) {\r\n                    this.m_angle = -this.getAngle();\r\n                    const adjustedAngle = this.m_isEnemy ? this.m_angle + 180 : this.m_angle;\r\n                    this.m_entity.node.angle = adjustedAngle;\r\n\r\n                    if (this.m_aimWait <= 0) {\r\n                        this.refreshSpeed(this.m_speed);\r\n                    }\r\n                }\r\n            } else {\r\n                this.m_beginTime += deltaTime;\r\n                this.calculateSpeed(deltaTime);\r\n\r\n                let layerSpeed = 0;\r\n                if (this.m_mainEntity) {\r\n                    layerSpeed = GameIns.sceneManager.getLayerSpeed(this.m_mainEntity);\r\n                }\r\n\r\n                let posx = this.m_entity.node.position.x + this.m_speedX * deltaTime;\r\n                let posy = this.m_entity.node.position.y + this.m_speedY * deltaTime - layerSpeed * deltaTime;\r\n                this.m_entity.node.setPosition(posx, posy);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的角度\r\n     * @returns {number} 子弹的角度\r\n     */\r\n    getAngle() {\r\n        if (this.m_isEnemy) {\r\n            this.getTarget();\r\n        }\r\n\r\n        if (!this.m_target) {\r\n            return 0;\r\n        }\r\n\r\n        const position = this.m_entity.node.position;\r\n        if (this.m_isEnemy) {\r\n            const mainPlaneNode = GameIns.mainPlaneManager.mainPlane.node;\r\n            return mainPlaneNode ? Tools.getAngle(position, v2(mainPlaneNode.position.x,mainPlaneNode.position.y)) : 0;\r\n        } else {\r\n            const targetPosition = this.m_targetCollider.entity.node.position;\r\n            return Tools.getAngle(position, targetPosition);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的目标\r\n     */\r\n    getTarget() {\r\n        if (!this.m_target) {\r\n            if (this.m_isEnemy) {\r\n                this.m_target = GameIns.mainPlaneManager.mainPlane;\r\n                this.m_targetCollider = GameIns.mainPlaneManager.mainPlane.getComp(FCollider);\r\n            } else {\r\n                this.m_targetCollider = GameIns.planeManager.getRandomTargetEnemy();\r\n                this.m_target = this.m_targetCollider.entity;\r\n            }\r\n        }\r\n    }\r\n}"]}