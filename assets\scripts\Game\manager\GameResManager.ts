import { _decorator, NodePool, instantiate, Prefab, Node} from 'cc';
import { GameIns } from '../GameIns';
import { SingletonBase } from '../../core/base/SingletonBase';
import { MyApp } from '../../MyApp';
import GameResourceList from '../const/GameResourceList';

const { ccclass } = _decorator;

@ccclass('GameResManager')
export default class GameResManager extends SingletonBase<GameResManager> {
    frameAnim: Prefab = null;

    async preload(){
        if (!this.frameAnim){
            GameIns.battleManager.addLoadCount(1);
            this.frameAnim = await MyApp.resMgr.loadAsync(GameResourceList.FrameAnim,Prefab);
            GameIns.battleManager.checkLoadFinish();
        }
    }
}