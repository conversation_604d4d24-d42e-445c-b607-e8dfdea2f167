import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;
import { LevelData, LevelDataBackgroundLayer, LevelDataLayer } from '../../scripts/leveldata/leveldata';
import { LevelEditorLayerUI } from './LevelEditorLayerUI';
import { LevelEditorUtils } from './utils';

const BackgroundsNodeName = "backgrounds";

@ccclass('EditorLevelLayer')
class LevelLayer {
    @property(Node)
    public node: Node | null = null;
    @property(CCFloat)
    public speed: number = 10;
}
@ccclass('EditorLevelBackgroundLayer')
class LevelBackgroundLayer extends LevelLayer {
    @property([Prefab])
    public backgrounds: Prefab[] = [];
    public backgroundsNode: Node|null = null;
}

@ccclass('LevelEditorBaseUI')
@executeInEditMode()
export class LevelEditorBaseUI extends Component {
    @property(CCString)
    public levelname: string = "";
    @property(CCFloat)
    public totalTime: number = 10;

    @property(LevelBackgroundLayer)
    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();
    @property([LevelLayer])
    public floorLayers: LevelLayer[] = [];
    @property([LevelLayer])
    public skyLayers: LevelLayer[] = [];

    private backgroundLayerNode:Node|null = null;
    private floorLayersNode:Node|null = null;
    private skyLayersNode:Node|null = null;

    onLoad():void {
        console.log(`LevelEditorBaseUI start.`);
        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, "BackgroundLayer");
        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, "FloorLayers");
        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, "SkyLayers");
        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);
    }
    update(dt:number):void {
        this.checkLayerNode(this.floorLayersNode, this.floorLayers);
        this.checkLayerNode(this.skyLayersNode, this.skyLayers);
    }
    public tick(progress: number):void {
        var bgCount = Math.ceil(this.totalTime * this.backgroundLayer.speed / 1280)
        while (this.backgroundLayer.backgrounds.length > 0 && bgCount > this.backgroundLayer.backgroundsNode.children.length) {
            var bg = instantiate(this.backgroundLayer.backgrounds[this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length])
            bg.setPosition(0, this.backgroundLayer.backgroundsNode.children.length * 1280, 0)
            this.backgroundLayer.backgroundsNode.addChild(bg)
        }
        while(bgCount < this.backgroundLayer.backgroundsNode.children.length) {
            this.backgroundLayer.backgroundsNode.children[this.backgroundLayer.backgroundsNode.children.length - 1].removeFromParent()
        }

        this.backgroundLayer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(
            progress, this.totalTime, this.backgroundLayer.speed);
        this.floorLayers.forEach((layer) => {
            layer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
        });
        this.skyLayers.forEach((layer) => {
            layer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
        });
    }

    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    private checkLayerNode(parentNode: Node, layers: LevelLayer[]):void {
        var removeLayerNodes = []
        parentNode.children.forEach(node => {
            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
            if (layerCom == null) {
                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null"`);
                removeLayerNodes.push(node)
                return;
            }
            if (layers.find((layer) => layer.node == node) == null) {
                console.log(`Level checkLayerNode remove ${node.name} because not in layers"`);
                removeLayerNodes.push(node)
                return;
            }
        });
        removeLayerNodes.forEach(element => {
            element.removeFromParent();    
        });
        layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
                console.log(`Level checkLayerNode add because layer == null`);
                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; 
            }
        });
    }

    public initByLevelData(data: LevelData):void {
        this.levelname = data.name;
        this.totalTime = data.totalTime

        this.backgroundLayerNode.removeAllChildren()
        this.backgroundLayer = new LevelBackgroundLayer();
        this.backgroundLayer.backgrounds = [];
        data.backgroundLayer?.backgrounds?.forEach((background) => {
            console.log("LevelEditorBaseUI initByLevelData load background")
            assetManager.loadAny({uuid:background}, (err, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                    return
                } 
                this.backgroundLayer.backgrounds.push(prefab);
            });
        });
        this.backgroundLayer.speed = data.backgroundLayer?.speed
        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, "layer").node;
        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
        this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).initByLevelData(data.backgroundLayer);
    
        this.floorLayers = []
        this.skyLayers = []
        LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);
        LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);
    }

    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        parentNode.removeAllChildren()
        dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();

            levelLayer.speed = layer.speed;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).initByLevelData(layer);

            layers.push(levelLayer);
        });
    }

    private static fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {
        dataLayer.speed = layer.speed;
        layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).fillLevelData(dataLayer);
    }
    private static fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        layers.forEach((layer) => {
            var levelLayer = new LevelDataLayer();
            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
        });
    }
    public fillLevelData(data: LevelData):void {
        data.name = this.levelname;
        data.totalTime = this.totalTime;

        data.backgroundLayer = new LevelDataBackgroundLayer();
        data.backgroundLayer.backgrounds = this.backgroundLayer.backgrounds.map((prefab) => prefab.uuid);
        LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);

        data.floorLayers = []
        data.skyLayers = []
        LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);
        LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);
    }
}