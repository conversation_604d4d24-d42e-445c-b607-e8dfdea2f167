System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, <PERSON><PERSON>, EventHandler, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, executeInEditMode, menu, help, inspector, ButtonPlus;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      EventHandler = _cc.EventHandler;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "13f7fOXLHFLK6LXmwAmgjNH", "ButtonPlus", undefined);

      __checkObsolete__(['_decorator', 'Button', 'EventHandler', 'EventTouch']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu,
        help,
        inspector
      } = _decorator);

      _export("ButtonPlus", ButtonPlus = (_dec = property({
        tooltip: "点击放大默认倍数"
      }), _dec2 = property({
        tooltip: "音效路径",
        type: '',
        multiline: true,
        formerlySerializedAs: '_N$string'
      }), _dec3 = property({
        tooltip: "屏蔽连续点击"
      }), _dec4 = property({
        tooltip: "屏蔽时间, 单位:秒"
      }), _dec5 = property({
        tooltip: "是否开启长按事件"
      }), _dec6 = property({
        tooltip: "长按时间"
      }), ccclass(_class = executeInEditMode(_class = (_class2 = class ButtonPlus extends Button {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "clickDefZoomScale", _descriptor, this);

          _initializerDefineProperty(this, "audioUrl", _descriptor2, this);

          _initializerDefineProperty(this, "openContinuous", _descriptor3, this);

          _initializerDefineProperty(this, "continuousTime", _descriptor4, this);

          // false表示可以点击
          this.continuous = false;
          // 定时器
          this._continuousTimer = null;

          // 长按触发
          _initializerDefineProperty(this, "openLongPress", _descriptor5, this);

          // 触发时间
          _initializerDefineProperty(this, "longPressTime", _descriptor6, this);

          this.longPressFlag = false;
          this.longPressTimer = null;
        }

        onEnable() {
          this.continuous = false;
          super.onEnable(); // if (!CC_EDITOR) {
          // }

          if (this.clickDefZoomScale) {
            this.transition = 3;
            this.zoomScale = 1.05;
            this.duration = 0.1;
          }
        }

        onDisable() {
          if (this._continuousTimer) {
            clearTimeout(this._continuousTimer);
            this._continuousTimer = null;
          }

          if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
          }

          super.onDisable();
        }
        /** 重写 */


        _onTouchBegan(event) {
          if (!this.interactable || !this.enabledInHierarchy) return;

          if (this.openLongPress && !this.longPressFlag) {
            // 开启长按
            if (this.longPressTimer) clearTimeout(this.longPressTimer);
            this.longPressTimer = setTimeout(function () {
              // 还在触摸中 触发事件
              if (this["_pressed"]) {
                this.node.emit('longclickStart', this);
                this.longPressFlag = true;
              }
            }.bind(this), this.longPressTime * 1000);
          }

          this["_pressed"] = true;
          this["_updateState"](); //event.stopPropagation();

          event.propagationStopped = true;
        }

        _onTouchEnded(event) {
          if (!this.interactable || !this.enabledInHierarchy) return;

          if (this["_pressed"] && this.longPressFlag) {
            this.node.emit('longclickEnd', this);
            this.longPressFlag = false;
          } else if (this["_pressed"] && !this.continuous) {
            this.continuous = this.openContinuous ? true : false;
            EventHandler.emitEvents(this.clickEvents, event);
            this.node.emit('click', event); //SoundMgr.inst.playEffect(this.audioUrl)

            if (this.openContinuous) {
              this._continuousTimer = setTimeout(function () {
                this.continuous = false;
              }.bind(this), this.continuousTime * 1000);
            }
          }

          this["_pressed"] = false;
          this["_updateState"]();
          event.propagationStopped = true; //event.stopPropagation();
        }

        _onTouchCancel() {
          if (!this.interactable || !this.enabledInHierarchy) return;

          if (this["_pressed"] && this.longPressFlag) {
            this.node.emit('longclickEnd', this);
            this.longPressFlag = false;
          }

          this["_pressed"] = false;
          this["_updateState"]();
        }
        /** 添加点击事件 */


        addClick(callback, target) {
          this.node.off('click');
          this.node.on('click', callback, target);
        }
        /** 添加一个长按事件 */


        addLongClick(startFunc, endFunc, target) {
          this.node.off('longclickStart');
          this.node.off('longclickEnd');
          this.node.on('longclickStart', startFunc, target);
          this.node.on('longclickEnd', endFunc, target);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "clickDefZoomScale", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "audioUrl", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "openContinuous", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "continuousTime", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0.5;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "openLongPress", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "longPressTime", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=38540334dd1b6c2a86c997edd8ccd67d8776846f.js.map