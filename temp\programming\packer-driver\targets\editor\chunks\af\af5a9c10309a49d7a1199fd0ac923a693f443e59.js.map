{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts"], "names": ["_decorator", "Component", "Vec2", "CCFloat", "LevelEditorBaseUI", "ccclass", "property", "executeInEditMode", "LevelEditorElemUI", "elemID", "time", "layerNode", "node", "parent", "rootNode", "baseUI", "getComponent", "layer", "floorLayers", "find", "skyLayers", "position", "y", "speed", "onLoad", "uuid", "initByLevelData", "data", "setPosition", "x", "name", "fillLevelData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,S,OAAAA,S;AAAqLC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;;AAClNC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;mCAIpCQ,iB,WAFZH,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAACH,OAAD,C,0CAHb,MAEaK,iBAFb,SAEuCP,SAFvC,CAEiD;AAAA;AAAA;AAAA,eAqBtCQ,MArBsC,GAqB7B,EArB6B;AAAA;;AAE9B,YAAJC,IAAI,GAAW;AAAA;;AACtB,gBAAMC,SAAS,wBAAG,KAAKC,IAAL,CAAUC,MAAb,qBAAG,kBAAkBA,MAApC;;AACA,cAAI,CAACF,SAAL,EAAgB;AACZ,mBAAO,CAAP;AACH;;AACD,gBAAMG,QAAQ,GAAGH,SAAS,CAACE,MAAV,CAAiBA,MAAlC;;AACA,cAAI,CAACC,QAAL,EAAe;AACX,mBAAO,CAAP;AACH;;AACD,gBAAMC,MAAM,GAAGD,QAAQ,CAACE,YAAT;AAAA;AAAA,qDAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACT,mBAAO,CAAP;AACH;;AACD,gBAAME,KAAK,GAAGF,MAAM,CAACG,WAAP,CAAmBC,IAAnB,CAAyBF,KAAD,IAAWA,KAAK,CAACL,IAAN,IAAcD,SAAjD,KAA+DI,MAAM,CAACK,SAAP,CAAiBD,IAAjB,CAAuBF,KAAD,IAAWA,KAAK,CAACL,IAAN,IAAcD,SAA/C,CAA7E;;AACA,cAAI,CAACM,KAAL,EAAY;AACR,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAKL,IAAL,CAAUS,QAAV,CAAmBC,CAAnB,GAAuBL,KAAK,CAACM,KAApC;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAI,KAAKf,MAAL,IAAe,EAAnB,EAAuB;AACnB,iBAAKA,MAAL,GAAc,KAAKgB,IAAnB;AACH;AACJ;;AACMC,QAAAA,eAAe,CAACC,IAAD,EAAsB;AACxC,eAAKf,IAAL,CAAUgB,WAAV,CAAsBD,IAAI,CAACN,QAAL,CAAcQ,CAApC,EAAuCF,IAAI,CAACN,QAAL,CAAcC,CAArD;AACA,eAAKb,MAAL,GAAckB,IAAI,CAAClB,MAAnB;AACA,eAAKG,IAAL,CAAUkB,IAAV,GAAiBH,IAAI,CAACG,IAAtB;AACH;;AACMC,QAAAA,aAAa,CAACJ,IAAD,EAAsB;AACtCA,UAAAA,IAAI,CAACN,QAAL,GAAgB,IAAInB,IAAJ,CAAS,KAAKU,IAAL,CAAUS,QAAV,CAAmBQ,CAA5B,EAA+B,KAAKjB,IAAL,CAAUS,QAAV,CAAmBC,CAAlD,CAAhB;AACAK,UAAAA,IAAI,CAAClB,MAAL,GAAc,KAAKA,MAAnB;AACAkB,UAAAA,IAAI,CAACG,IAAL,GAAY,KAAKlB,IAAL,CAAUkB,IAAtB;AACH;;AApC4C,O", "sourcesContent": ["import { _decorator, CCInteger, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCFloat } from 'cc';\r\nimport { LevelEditorBaseUI } from './LevelEditorBaseUI';\r\nimport { LevelDataElem } from '../../scripts/leveldata/leveldata';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('LevelEditorElemUI')\r\n@executeInEditMode()\r\nexport class LevelEditorElemUI extends Component {\r\n    @property(CCFloat)\r\n    public get time(): number {\r\n        const layerNode = this.node.parent?.parent\r\n        if (!layerNode) {\r\n            return 0;\r\n        }\r\n        const rootNode = layerNode.parent.parent\r\n        if (!rootNode) {\r\n            return 0;\r\n        }\r\n        const baseUI = rootNode.getComponent(LevelEditorBaseUI)\r\n        if (!baseUI) {\r\n            return 0;\r\n        }\r\n        const layer = baseUI.floorLayers.find((layer) => layer.node == layerNode) || baseUI.skyLayers.find((layer) => layer.node == layerNode)\r\n        if (!layer) {\r\n            return 0;\r\n        }\r\n        return this.node.position.y / layer.speed;\r\n    }\r\n    public elemID = \"\";\r\n    protected onLoad(): void {\r\n        if (this.elemID == \"\") {\r\n            this.elemID = this.uuid;\r\n        }\r\n    }\r\n    public initByLevelData(data: LevelDataElem) {\r\n        this.node.setPosition(data.position.x, data.position.y);\r\n        this.elemID = data.elemID;\r\n        this.node.name = data.name;\r\n    }\r\n    public fillLevelData(data: LevelDataElem) {\r\n        data.position = new Vec2(this.node.position.x, this.node.position.y);\r\n        data.elemID = this.elemID;\r\n        data.name = this.node.name;\r\n    }\r\n}"]}