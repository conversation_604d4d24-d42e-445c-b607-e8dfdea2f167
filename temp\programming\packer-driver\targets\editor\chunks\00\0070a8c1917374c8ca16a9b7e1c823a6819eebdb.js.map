{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts"], "names": ["_decorator", "assetManager", "Component", "instantiate", "Node", "UITransform", "LevelLayerUI", "ccclass", "property", "BackgroundsNodeName", "<PERSON><PERSON><PERSON><PERSON>", "node", "speed", "LevelBackgroundLayer", "backgrounds", "backgroundsNode", "LevelBaseUI", "_totalTime", "_lastLevelHeight", "_lastLevelOffsetY", "_backgroundLayerNode", "_floorLayersNode", "_skyLayersNode", "_backgroundLayer", "_floorLayers", "_skyLayers", "floorLayers", "skyLayers", "background<PERSON>ayer", "TotalTime", "getLevelTotalHeightByIndex", "index", "totalHeight", "lastBgNode", "getChildByName", "children", "for<PERSON>ach", "bg", "height", "getComponent", "contentSize", "onLoad", "_getOrAddNode", "node_parent", "name", "<PERSON><PERSON><PERSON><PERSON>", "levelPrefab", "levelData", "levelInfo", "bFristLevel", "_initByLevelData", "Promise", "resolve", "setBackgroundLayerInfo", "time", "data", "_initBackgroundLayer", "_initLayers", "parentNode", "layers", "dataLayers", "layer", "i", "<PERSON><PERSON>ayer", "_addLayer", "initByLevelData", "push", "length", "bgCount", "Math", "ceil", "totalTime", "loadPromises", "map", "reject", "loadAny", "uuid", "err", "prefab", "console", "error", "all", "offsetY", "levelIndex", "getPosition", "y", "log", "setSiblingIndex", "pos", "setPosition", "layerNode", "layerCom", "addComponent", "tick", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,W,OAAAA,W;;AAEhEC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAExBS,MAAAA,mB,GAAsB,a;AAGtBC,MAAAA,U,WADLH,OAAO,CAAC,YAAD,C,gBAAR,MACMG,UADN,CACiB;AAAA;AAAA,eACNC,IADM,GACc,IADd;AAAA,eAENC,KAFM,GAEU,CAFV;AAAA;;AAAA,O;AAMXC,MAAAA,oB,YADLN,OAAO,CAAC,sBAAD,C,kBAAR,MACMM,oBADN,SACmCH,UADnC,CAC8C;AAAA;AAAA;AAAA,eACnCI,WADmC,GACX,EADW;AAAA,eAEnCC,eAFmC,GAEN,IAFM;AAAA;;AAAA,O;;6BAMjCC,W,YADZT,OAAO,CAAC,aAAD,C,kBAAR,MACaS,WADb,SACiCd,SADjC,CAC2C;AAAA;AAAA;AAAA,eAC/Be,UAD+B,GACV,EADU;AACN;AADM,eAE/BC,gBAF+B,GAEJ,CAFI;AAED;AAFC,eAG/BC,iBAH+B,GAGH,CAHG;AAGA;AAHA,eAK/BC,oBAL+B,GAKE,IALF;AAAA,eAM/BC,gBAN+B,GAMF,IANE;AAAA,eAO/BC,cAP+B,GAOJ,IAPI;AAAA,eAS/BC,gBAT+B,GASU,IATV;AAAA,eAU/BC,YAV+B,GAUF,EAVE;AAAA,eAW/BC,UAX+B,GAWJ,EAXI;AAAA;;AAajB,YAAXC,WAAW,GAAiB;AACnC,iBAAO,KAAKF,YAAZ;AACH;;AACmB,YAATG,SAAS,GAAiB;AACjC,iBAAO,KAAKF,UAAZ;AACH;;AACyB,YAAfG,eAAe,GAAyB;AAC/C,iBAAO,KAAKL,gBAAZ;AACH;;AAEmB,YAATM,SAAS,GAAW;AAC3B,iBAAO,KAAKZ,UAAZ;AACH;;AAEMa,QAAAA,0BAA0B,CAACC,KAAD,EAAwB;AACrD,cAAIC,WAAW,GAAG,CAAlB;;AACA,cAAI,KAAKJ,eAAT,EAA0B;AACtB,gBAAIK,UAAU,GAAG,KAAKb,oBAAL,CAA0Bc,cAA1B,CAA0C,SAAQH,KAAM,EAAxD,CAAjB;;AACA,gBAAIE,UAAJ,EAAgB;AACZ,oBAAMlB,eAAe,GAAGkB,UAAU,CAACC,cAAX,CAA0BzB,mBAA1B,CAAxB;;AACA,kBAAIM,eAAJ,EAAqB;AACjBA,gBAAAA,eAAe,CAACoB,QAAhB,CAAyBC,OAAzB,CAAkCC,EAAD,IAAQ;AAAA;;AACrC,wBAAMC,MAAM,uBAAGD,EAAE,CAACE,YAAH,CAAgBlC,WAAhB,CAAH,qBAAG,iBAA8BmC,WAA9B,CAA0CF,MAAzD;AACAN,kBAAAA,WAAW,IAAIM,MAAf;AACH,iBAHD;AAIH;AACJ;AACJ;;AAED,iBAAON,WAAP;AACH;;AAESS,QAAAA,MAAM,GAAS,CACxB;;AAEOC,QAAAA,aAAa,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AACzD,cAAIjC,IAAI,GAAGgC,WAAW,CAACT,cAAZ,CAA2BU,IAA3B,CAAX;;AACA,cAAIjC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIP,IAAJ,CAASwC,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACE,QAAZ,CAAqBlC,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEuB,cAAXmC,WAAW,CAACC,SAAD,EAAuBC,SAAvB,EAA0EC,WAAoB,GAAG,KAAjG,EAAsH;AAC1I,eAAK7B,oBAAL,GAA4B,KAAKsB,aAAL,CAAmB,KAAK/B,IAAxB,EAA8B,iBAA9B,CAA5B;AACA,eAAKU,gBAAL,GAAwB,KAAKqB,aAAL,CAAmB,KAAK/B,IAAxB,EAA8B,aAA9B,CAAxB;AACA,eAAKW,cAAL,GAAsB,KAAKoB,aAAL,CAAmB,KAAK/B,IAAxB,EAA8B,WAA9B,CAAtB;;AACA,cAAIsC,WAAJ,EAAiB;AACb,kBAAM,KAAKC,gBAAL,CAAsBH,SAAtB,EAAgCC,SAAhC,CAAN;AACH,WAFD,MAEO;AACH,iBAAKE,gBAAL,CAAsBH,SAAtB,EAAgCC,SAAhC;;AACA,mBAAOG,OAAO,CAACC,OAAR,EAAP;AACH;AACJ;;AAEMC,QAAAA,sBAAsB,CAACzC,KAAD,EAAgB0C,IAAhB,EAAoC;AAC7D,eAAK1B,eAAL,CAAqBhB,KAArB,GAA6BA,KAA7B;AACA,eAAKK,UAAL,GAAkBqC,IAAlB;AACH;;AAE4B,cAAhBJ,gBAAgB,CAACK,IAAD,EAAkBP,SAAlB,EAAqEC,WAAoB,GAAG,KAA5F,EAAiH;AAC1I;AACI,gBAAM,KAAKO,oBAAL,CAA0B,KAAKpC,oBAA/B,EAAqDmC,IAArD,EAA2DP,SAA3D,CAAN,CAFsI,CAG1I;AACI;AACJ;;AACA,eAAKS,WAAL,CAAiB,KAAKpC,gBAAtB,EAAwC,KAAKK,WAA7C,EAA0D6B,IAAI,CAAC7B,WAA/D,EAA4EuB,WAA5E;;AACA,eAAKQ,WAAL,CAAiB,KAAKnC,cAAtB,EAAsC,KAAKK,SAA3C,EAAsD4B,IAAI,CAAC5B,SAA3D,EAAsEsB,WAAtE;AACH;;AAEOQ,QAAAA,WAAW,CAACC,UAAD,EAAmBC,MAAnB,EAAyCC,UAAzC,EAAuEX,WAAvE,EAAmG;AAClHW,UAAAA,UAAU,CAACxB,OAAX,CAAmB,CAACyB,KAAD,EAAQC,CAAR,KAAc;AAC7B,gBAAIC,UAAU,GAAG,IAAIrD,UAAJ,EAAjB;AACAqD,YAAAA,UAAU,CAACnD,KAAX,GAAmBiD,KAAK,CAACjD,KAAzB;AACAmD,YAAAA,UAAU,CAACpD,IAAX,GAAkB,KAAKqD,SAAL,CAAeN,UAAf,EAA4B,SAAQI,CAAE,EAAtC,EAAyCnD,IAA3D;AACAoD,YAAAA,UAAU,CAACpD,IAAX,CAAgB4B,YAAhB;AAAA;AAAA,8CAAyD0B,eAAzD,CAAyEJ,KAAzE,EAA+E,KAAK1C,iBAApF;AACAwC,YAAAA,MAAM,CAACO,IAAP,CAAYH,UAAZ;AACH,WAND;AAOH;;AAEiC,cAApBP,oBAAoB,CAACE,UAAD,EAAmBH,IAAnB,EAAoCP,SAApC,EAAsG;AACpI,cAAIO,IAAI,CAAC3B,eAAL,CAAqBd,WAArB,CAAiCqD,MAAjC,GAA0C,CAA9C,EAAiD;AAC7C,gBAAI,KAAK5C,gBAAL,KAA0B,IAA9B,EAAoC;AAChC,mBAAKA,gBAAL,GAAwB,IAAIV,oBAAJ,EAAxB;AACA,mBAAKU,gBAAL,CAAsBT,WAAtB,GAAoC,EAApC;AACH;;AACD,iBAAKS,gBAAL,CAAsBX,KAAtB,GAA8B2C,IAAI,CAAC3B,eAAL,CAAqBhB,KAAnD;AACA,gBAAIwD,OAAO,GAAGC,IAAI,CAACC,IAAL,CAAUf,IAAI,CAACgB,SAAL,GAAiB,KAAKhD,gBAAL,CAAsBX,KAAvC,GAA+C,IAAzD,CAAd;AACA,kBAAM4D,YAAY,GAAGjB,IAAI,CAAC3B,eAAL,CAAqBd,WAArB,CAAiC2D,GAAjC,CAAsC7C,eAAD,IAAqB;AAC3E,qBAAO,IAAIuB,OAAJ,CAAkB,CAACC,OAAD,EAAUsB,MAAV,KAAqB;AAC1CzE,gBAAAA,YAAY,CAAC0E,OAAb,CAAqB;AAAEC,kBAAAA,IAAI,EAAEhD;AAAR,iBAArB,EAAgD,CAACiD,GAAD,EAAMC,MAAN,KAAyB;AACrE,sBAAID,GAAJ,EAAS;AACLE,oBAAAA,OAAO,CAACC,KAAR,CAAc,aAAd,EAA6B,4CAA7B,EAA2EH,GAA3E;AACAH,oBAAAA,MAAM,CAACG,GAAD,CAAN;AACH,mBAHD,MAGO;AACH,yBAAKtD,gBAAL,CAAsBT,WAAtB,CAAkCoD,IAAlC,CAAuCY,MAAvC;;AACA1B,oBAAAA,OAAO;AACV;AACJ,iBARD;AASH,eAVM,CAAP;AAWH,aAZoB,CAArB;AAcA,kBAAMD,OAAO,CAAC8B,GAAR,CAAYT,YAAZ,CAAN,CArB6C,CAsB7C;;AACA,gBAAIU,OAAO,GAAG,CAAd;AACA,iBAAKhE,gBAAL,GAAwB,CAAxB;;AACA,gBAAI,KAAKU,eAAT,EAA0B;AACtB,kBAAIK,UAAU,GAAG,KAAKb,oBAAL,CAA0Bc,cAA1B,CAA0C,SAAQc,SAAS,CAACmC,UAAV,GAAuB,CAAE,EAA3E,CAAjB;;AACA,kBAAIlD,UAAJ,EAAgB;AACZiD,gBAAAA,OAAO,GAAGjD,UAAU,CAACmD,WAAX,GAAyBC,CAAnC;AACA,sBAAMtE,eAAe,GAAGkB,UAAU,CAACC,cAAX,CAA0BzB,mBAA1B,CAAxB;;AACA,oBAAIM,eAAJ,EAAqB;AACjBA,kBAAAA,eAAe,CAACoB,QAAhB,CAAyBC,OAAzB,CAAkCC,EAAD,IAAQ;AAAA;;AACrC,0BAAMC,MAAM,wBAAGD,EAAE,CAACE,YAAH,CAAgBlC,WAAhB,CAAH,qBAAG,kBAA8BmC,WAA9B,CAA0CF,MAAzD;AACA,yBAAKpB,gBAAL,IAAyBoB,MAAzB;AACH,mBAHD;AAIH;AACJ;AACJ;;AAED,iBAAKnB,iBAAL,GAAyB,KAAKD,gBAAL,GAAwBgE,OAAjD;AACAH,YAAAA,OAAO,CAACO,GAAR,CAAY,aAAZ,EAA2B,uCAA3B,EAAoE,KAAKpE,gBAAzE,EAA2F,SAA3F,EAAsGgE,OAAtG;AAEA,iBAAKtD,eAAL,CAAqBjB,IAArB,GAA4B,KAAKqD,SAAL,CAAeN,UAAf,EAA4B,SAAQV,SAAS,CAACmC,UAAW,EAAzD,EAA4DxE,IAAxF;AACA,iBAAKiB,eAAL,CAAqBb,eAArB,GAAuC,KAAK2B,aAAL,CAAmB,KAAKd,eAAL,CAAqBjB,IAAxC,EAA8CF,mBAA9C,CAAvC;AACA,iBAAKmB,eAAL,CAAqBjB,IAArB,CAA0B4B,YAA1B;AAAA;AAAA,8CAAmE0B,eAAnE,CAAmFV,IAAI,CAAC3B,eAAxF,EAAyG,KAAKT,iBAA9G;AACA,iBAAKS,eAAL,CAAqBb,eAArB,CAAqCwE,eAArC,CAAqD,CAArD;AAEA,gBAAIC,GAAG,GAAG,CAAV;;AACA,mBAAO,KAAKjE,gBAAL,CAAsBT,WAAtB,CAAkCqD,MAAlC,GAA2C,CAA3C,IAAgDC,OAAO,GAAG,KAAK7C,gBAAL,CAAsBR,eAAtB,CAAsCoB,QAAtC,CAA+CgC,MAAhH,EAAwH;AAAA;;AACpH,kBAAI9B,EAAE,GAAGlC,WAAW,CAAC,KAAKoB,gBAAL,CAAsBT,WAAtB,CAAkC,KAAKS,gBAAL,CAAsBR,eAAtB,CAAsCoB,QAAtC,CAA+CgC,MAA/C,GAAwD,KAAK5C,gBAAL,CAAsBT,WAAtB,CAAkCqD,MAA5H,CAAD,CAApB;AACA,oBAAM7B,MAAM,wBAAGD,EAAE,CAACE,YAAH,CAAgBlC,WAAhB,CAAH,qBAAG,kBAA8BmC,WAA9B,CAA0CF,MAAzD;AAEAD,cAAAA,EAAE,CAACoD,WAAH,CAAe,CAAf,EAAkBD,GAAlB,EAAuB,CAAvB;AACAA,cAAAA,GAAG,IAAIlD,MAAP;;AACA,mBAAKf,gBAAL,CAAsBR,eAAtB,CAAsC8B,QAAtC,CAA+CR,EAA/C;AACH;AACJ;AACJ;;AAEO2B,QAAAA,SAAS,CAACN,UAAD,EAAmBd,IAAnB,EAA+C;AAC5D,cAAI8C,SAAS,GAAG,IAAItF,IAAJ,CAASwC,IAAT,CAAhB;AACA,cAAI+C,QAAQ,GAAGD,SAAS,CAACE,YAAV;AAAA;AAAA,2CAAf;AACAlC,UAAAA,UAAU,CAACb,QAAX,CAAoB6C,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEME,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,eAAK1E,oBAAL,CAA0Be,QAA1B,CAAmCC,OAAnC,CAA4CzB,IAAD,IAAU;AACjDA,YAAAA,IAAI,CAAC4B,YAAL;AAAA;AAAA,8CAA8CsD,IAA9C,CAAmDC,SAAnD,EAA8D,KAAKlE,eAAL,CAAqBhB,KAAnF;AACH,WAFD;;AAGA,eAAKc,WAAL,CAAiBU,OAAjB,CAA0ByB,KAAD,IAAW;AAAA;;AAChC,2BAAAA,KAAK,CAAClD,IAAN,yBAAY4B,YAAZ;AAAA;AAAA,8CAAqDsD,IAArD,CAA0DC,SAA1D,EAAqEjC,KAAK,CAACjD,KAA3E;AACH,WAFD;AAGA,eAAKe,SAAL,CAAeS,OAAf,CAAwByB,KAAD,IAAW;AAAA;;AAC9B,4BAAAA,KAAK,CAAClD,IAAN,0BAAY4B,YAAZ;AAAA;AAAA,8CAAqDsD,IAArD,CAA0DC,SAA1D,EAAqEjC,KAAK,CAACjD,KAA3E;AACH,WAFD;AAGH;;AA3KsC,O", "sourcesContent": ["import { _decorator, assetManager, Component, instantiate, Node, Prefab, UITransform } from \"cc\";\r\nimport { LevelData, LevelDataLayer } from \"../../../leveldata/leveldata\";\r\nimport { LevelLayerUI } from \"./LevelLayerUI\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('LevelLayer')\r\nclass LevelLayer {\r\n    public node: Node | null = null;\r\n    public speed: number = 0;\r\n}\r\n\r\n@ccclass('LevelBackgroundLayer')\r\nclass LevelBackgroundLayer extends LevelLayer {\r\n    public backgrounds: Prefab[] = [];\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\n@ccclass('LevelBaseUI')\r\nexport class LevelBaseUI extends Component {\r\n    private _totalTime: number = 10; // 当前关卡的时长\r\n    private _lastLevelHeight: number = 0; // 上一关的关卡高度\r\n    private _lastLevelOffsetY: number = 0; // 上一关的关卡偏移量\r\n    \r\n    private _backgroundLayerNode:Node|null = null;\r\n    private _floorLayersNode:Node|null = null;\r\n    private _skyLayersNode:Node|null = null;\r\n\r\n    private _backgroundLayer: LevelBackgroundLayer = null;\r\n    private _floorLayers: LevelLayer[] = [];\r\n    private _skyLayers: LevelLayer[] = [];\r\n\r\n    public get floorLayers(): LevelLayer[] {\r\n        return this._floorLayers;\r\n    }\r\n    public get skyLayers(): LevelLayer[] {\r\n        return this._skyLayers;\r\n    }\r\n    public get backgroundLayer(): LevelBackgroundLayer {\r\n        return this._backgroundLayer;\r\n    }\r\n\r\n    public get TotalTime(): number {\r\n        return this._totalTime;\r\n    }\r\n\r\n    public getLevelTotalHeightByIndex(index: number): number {\r\n        var totalHeight = 0;\r\n        if (this.backgroundLayer) {\r\n            var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${index}`);\r\n            if (lastBgNode) {\r\n                const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);\r\n                if (backgroundsNode) {\r\n                    backgroundsNode.children.forEach((bg) => {\r\n                        const height = bg.getComponent(UITransform)?.contentSize.height;\r\n                        totalHeight += height;\r\n                    });\r\n                }\r\n            }\r\n        }\r\n\r\n        return totalHeight;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public async levelPrefab(levelData: LevelData, levelInfo:{ levelID: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {\r\n        this._backgroundLayerNode = this._getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this._floorLayersNode = this._getOrAddNode(this.node, \"FloorLayers\");\r\n        this._skyLayersNode = this._getOrAddNode(this.node, \"SkyLayers\");\r\n        if (bFristLevel) {\r\n            await this._initByLevelData(levelData,levelInfo);\r\n        } else {\r\n            this._initByLevelData(levelData,levelInfo);\r\n            return Promise.resolve();\r\n        }\r\n    }\r\n\r\n    public setBackgroundLayerInfo(speed: number, time: number): void {\r\n        this.backgroundLayer.speed = speed;\r\n        this._totalTime = time;\r\n    }\r\n\r\n    public async _initByLevelData(data: LevelData, levelInfo:{ levelID: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {\r\n        //if (bFristLevel) {\r\n            await this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo);\r\n        //} else {\r\n            //this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo);\r\n        //}\r\n        this._initLayers(this._floorLayersNode, this.floorLayers, data.floorLayers, bFristLevel);\r\n        this._initLayers(this._skyLayersNode, this.skyLayers, data.skyLayers, bFristLevel);\r\n    }\r\n\r\n    private _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[], bFristLevel: boolean): void {\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;\r\n            levelLayer.node.getComponent<LevelLayerUI>(LevelLayerUI).initByLevelData(layer,this._lastLevelOffsetY);\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private async _initBackgroundLayer(parentNode: Node, data: LevelData, levelInfo:{ levelID: number, levelIndex: number }): Promise<void> {\r\n        if (data.backgroundLayer.backgrounds.length > 0) { \r\n            if (this._backgroundLayer === null) {\r\n                this._backgroundLayer = new LevelBackgroundLayer();\r\n                this._backgroundLayer.backgrounds = [];\r\n            }\r\n            this._backgroundLayer.speed = data.backgroundLayer.speed;\r\n            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1280);\r\n            const loadPromises = data.backgroundLayer.backgrounds.map((backgroundLayer) => {\r\n                return new Promise<void>((resolve, reject) => {\r\n                    assetManager.loadAny({ uuid: backgroundLayer }, (err, prefab: Prefab) => {\r\n                        if (err) {\r\n                            console.error('LevelBaseUI', 'initByLevelData load background prefab err', err);\r\n                            reject(err);\r\n                        } else {\r\n                            this._backgroundLayer.backgrounds.push(prefab);\r\n                            resolve();\r\n                        }\r\n                    });\r\n                });\r\n            });\r\n\r\n            await Promise.all(loadPromises);\r\n            // 节点设置偏移\r\n            var offsetY = 0;\r\n            this._lastLevelHeight = 0;\r\n            if (this.backgroundLayer) {\r\n                var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${levelInfo.levelIndex - 1}`);\r\n                if (lastBgNode) {\r\n                    offsetY = lastBgNode.getPosition().y;\r\n                    const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);\r\n                    if (backgroundsNode) {\r\n                        backgroundsNode.children.forEach((bg) => {\r\n                            const height = bg.getComponent(UITransform)?.contentSize.height;\r\n                            this._lastLevelHeight += height;\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n\r\n            this._lastLevelOffsetY = this._lastLevelHeight + offsetY;\r\n            console.log('LevelBaseUI', \"_initBackgroundLayer _lastLevelHeight\", this._lastLevelHeight, \"offsetY\", offsetY);\r\n\r\n            this.backgroundLayer.node = this._addLayer(parentNode, `layer_${levelInfo.levelIndex}`).node;\r\n            this.backgroundLayer.backgroundsNode = this._getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);\r\n            this.backgroundLayer.node.getComponent<LevelLayerUI>(LevelLayerUI).initByLevelData(data.backgroundLayer, this._lastLevelOffsetY);\r\n            this.backgroundLayer.backgroundsNode.setSiblingIndex(0);\r\n\r\n            var pos = 0;\r\n            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode.children.length) {\r\n                var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode.children.length % this._backgroundLayer.backgrounds.length]);\r\n                const height = bg.getComponent(UITransform)?.contentSize.height;\r\n                \r\n                bg.setPosition(0, pos, 0);\r\n                pos += height;\r\n                this._backgroundLayer.backgroundsNode.addChild(bg);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _addLayer(parentNode: Node, name: string): LevelLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        this._backgroundLayerNode.children.forEach((node) => {\r\n            node.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, this.backgroundLayer.speed);\r\n        });\r\n        this.floorLayers.forEach((layer) => {\r\n            layer.node?.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, layer.speed);\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            layer.node?.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, layer.speed);\r\n        });\r\n    }\r\n}"]}