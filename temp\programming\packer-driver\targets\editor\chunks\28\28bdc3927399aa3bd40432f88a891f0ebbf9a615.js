System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, Node, tween, UITransform, Tween, v3, instantiate, UIOpacity, GameIns, Tools, EnemyAnim, GameConst, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, EnemyPlaneRole;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyAnim(extras) {
    _reporterNs.report("EnemyAnim", "./EnemyAnim", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyUIData(extras) {
    _reporterNs.report("EnemyUIData", "../../../data/EnemyData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      Node = _cc.Node;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
      v3 = _cc.v3;
      instantiate = _cc.instantiate;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      Tools = _unresolved_3.Tools;
    }, function (_unresolved_4) {
      EnemyAnim = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameConst = _unresolved_5.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "814a7K8QVRKfreLfXMsDdOE", "EnemyPlaneRole", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'Vec2', 'Node', 'tween', 'v2', 'UITransform', 'Tween', 'v3', 'Prefab', 'instantiate', 'UIOpacity']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlaneRole = (_dec = property(Sprite), _dec2 = property(Sprite), _dec3 = property(Sprite), _dec4 = property(Node), _dec5 = property([Node]), ccclass(_class = (_class2 = class EnemyPlaneRole extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "pedestal", _descriptor, this);

          _initializerDefineProperty(this, "role", _descriptor2, this);

          _initializerDefineProperty(this, "white", _descriptor3, this);

          _initializerDefineProperty(this, "fireNode", _descriptor4, this);

          _initializerDefineProperty(this, "tailFireArr", _descriptor5, this);

          this._data = null;
          this._curUId = -1;
          this._anim = null;
          this._curAnim = "";
        }

        /**
         * 预加载 UI
         * @param {Object} data 敌机数据
         */
        preLoadUI(data) {
          if (this._data = data) {
            this._initUI(true);
          }
        }
        /**
         * 初始化敌机
         * @param {Object} data 敌机数据
         * @param {Object} target 目标对象
         * @param {string} param 参数
         */


        async init(data, target, param = "") {
          this._reset();

          this._data = data;

          if (!this._data.isAm) {
            await this._initUI();
          } else {
            if (this._anim) {
              this.playAnim("idle1");
              this._anim.node.getComponent(UIOpacity).opacity = 255;
            }
          }
        }
        /**
         * 重置敌机状态
         */


        _reset() {
          this.white.node.getComponent(UIOpacity).opacity = 0;
          this.pedestal.spriteFrame = null;
          this._curAnim = "";
          this.stopAnim();
          if (this._anim) this._anim.node.getComponent(UIOpacity).opacity = 0;
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 时间增量
         */


        updateGameLogic(dt) {}
        /**
         * 初始化 UI
         * @param {boolean} isPreload 是否预加载
         */


        async _initUI(isPreload = false) {
          if (this._data.isAm) {
            this.role.spriteFrame = null;
            this.white.spriteFrame = null; // this._winkAct = tween().to(0, { opacity: 204 }).to(3 * GameConst.ActionFrameTime, { opacity: 0 });

            if (this._curUId === this._data.id && this._anim) {
              this._anim.node.getComponent(UIOpacity).opacity = 255;
            } else {
              this._curUId = this._data.id;

              if (this._anim) {
                this._anim.node.destroy();

                this._anim = null;
              }

              let pf = await (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.getPlaneRole(this._data.image);
              const animNode = instantiate(pf);
              this.node.addChild(animNode);
              this._anim = animNode.getComponent(_crd && EnemyAnim === void 0 ? (_reportPossibleCrUseOfEnemyAnim({
                error: Error()
              }), EnemyAnim) : EnemyAnim);

              this._anim.init(this._data.extraParam);
            }
          } else {
            if (this._anim) this._anim.node.active = false;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.role, this._data.image);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.setPlaneFrame(this.white, this._data.image);
            const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ActionFrameTime;

            for (let i = 0; i < this._data.extraParam.length; i++) {
              const param = this._data.extraParam[i];
              let tailFire = this.tailFireArr[i];

              if (!tailFire) {
                tailFire = new Node();
                this.fireNode.addChild(tailFire);
                tailFire.addComponent(Sprite);
                tailFire.getComponent(UITransform).anchorY = 0;
                this.tailFireArr.push(tailFire);
              }

              Tween.stopAllByTarget(tailFire);
              tailFire.active = true;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.setPlaneFrame(tailFire.getComponent(Sprite), "fire" + param[0]);
              tailFire.setPosition(param[1], param[2]);
              tailFire.getComponent(UITransform).setContentSize(param[3], param[4]);
              tailFire.angle = param[7] || 0;
              tween(tailFire).to(frameTime * param[5], {
                scale: v3(param[6], param[6])
              }).to(frameTime * param[5], {
                scale: v3(1, 1)
              }).repeatForever().start();
            }

            for (let i = this._data.extraParam.length; i < this.tailFireArr.length; i++) {
              const tailFire = this.tailFireArr[i];
              Tween.stopAllByTarget(tailFire);
              tailFire.active = false;
            }
          }
        }
        /**
         * 设置动画事件回调
         * @param {string} eventName 事件名称
         * @param {Function} callback 回调函数
         */


        setEventCallback(eventName, callback) {
          this._anim && this._anim.setAnimEventCall(eventName, callback);
        }
        /**
         * 开始攻击
         */


        startAttack() {}
        /**
         * 攻击结束
         */


        attackOver() {}
        /**
         * 播放动画
         * @param {string} animName 动画名称
         * @param {Function} [callback] 动画结束回调
         * @returns {boolean} 是否成功播放
         */


        playAnim(animName, callback = null) {
          if (!this._data.isAm) return false;

          if (this._curAnim === animName) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).log("save anim", animName);
            return true;
          }

          if (this._anim) {
            this._anim.playAnim(animName, callback);

            return true;
          }

          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).log("anim is null", animName);
          return false;
        }
        /**
         * 暂停动画
         */


        pauseAnim() {
          this._anim && this._anim.pauseAnim();
        }
        /**
         * 恢复动画
         */


        resumeAnim() {
          this._anim && this._anim.resumeAnim();
        }
        /**
         * 停止动画
         */


        stopAnim() {
          this._anim && this._anim.stopAnim();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "pedestal", [_dec], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "role", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "white", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "fireNode", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "tailFireArr", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=28bdc3927399aa3bd40432f88a891f0ebbf9a615.js.map