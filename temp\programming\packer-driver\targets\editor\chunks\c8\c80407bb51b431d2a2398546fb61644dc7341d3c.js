System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, CCFloat, LevelDataEventCondtionComb, LevelDataEventCondtionType, LevelDataEventCondtionDelayTime, newCondition, LevelElemUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _crd, ccclass, property, LevelCondition;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfLevelDataEventCondtion(extras) {
    _reporterNs.report("LevelDataEventCondtion", "../../../leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionComb(extras) {
    _reporterNs.report("LevelDataEventCondtionComb", "../../../leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "../../../leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionDelayTime(extras) {
    _reporterNs.report("LevelDataEventCondtionDelayTime", "../../../leveldata/condition/LevelDataEventCondtionDelayTime", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewCondition(extras) {
    _reporterNs.report("newCondition", "../../../leveldata/condition/newCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionDelayDistance(extras) {
    _reporterNs.report("LevelDataEventCondtionDelayDistance", "../../../leveldata/condition/LevelDataEventCondtionDelayDistance", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelElemUI(extras) {
    _reporterNs.report("LevelElemUI", "./LevelElemUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionWave(extras) {
    _reporterNs.report("LevelDataEventCondtionWave", "../../../leveldata/condition/LevelDataEventCondtionWave", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
      CCFloat = _cc.CCFloat;
    }, function (_unresolved_2) {
      LevelDataEventCondtionComb = _unresolved_2.LevelDataEventCondtionComb;
      LevelDataEventCondtionType = _unresolved_2.LevelDataEventCondtionType;
    }, function (_unresolved_3) {
      LevelDataEventCondtionDelayTime = _unresolved_3.LevelDataEventCondtionDelayTime;
    }, function (_unresolved_4) {
      newCondition = _unresolved_4.newCondition;
    }, function (_unresolved_5) {
      LevelElemUI = _unresolved_5.LevelElemUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "93b5buH5fNBWrbvnQFmb0ja", "LevelCondition", undefined);

      __checkObsolete__(['_decorator', 'Enum', 'CCFloat']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelCondition", LevelCondition = (_dec = ccclass('LevelCondition'), _dec2 = property({
        type: Enum(_crd && LevelDataEventCondtionComb === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionComb({
          error: Error()
        }), LevelDataEventCondtionComb) : LevelDataEventCondtionComb),

        visible() {
          return this._index != 0;
        }

      }), _dec3 = property({
        type: Enum(_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
          error: Error()
        }), LevelDataEventCondtionType) : LevelDataEventCondtionType)
      }), _dec4 = property({
        type: CCFloat,

        visible() {
          return this.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
            error: Error()
          }), LevelDataEventCondtionType) : LevelDataEventCondtionType).DelayTime;
        }

      }), _dec5 = property({
        type: CCFloat,

        visible() {
          return this.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
            error: Error()
          }), LevelDataEventCondtionType) : LevelDataEventCondtionType).DelayDistance;
        }

      }), _dec6 = property({
        type: _crd && LevelElemUI === void 0 ? (_reportPossibleCrUseOfLevelElemUI({
          error: Error()
        }), LevelElemUI) : LevelElemUI,

        visible() {
          return this.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
            error: Error()
          }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave;
        }

      }), _dec(_class = (_class2 = class LevelCondition {
        constructor() {
          this._index = 0;
          this.data = new (_crd && LevelDataEventCondtionDelayTime === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionDelayTime({
            error: Error()
          }), LevelDataEventCondtionDelayTime) : LevelDataEventCondtionDelayTime)((_crd && LevelDataEventCondtionComb === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionComb({
            error: Error()
          }), LevelDataEventCondtionComb) : LevelDataEventCondtionComb).And);
          this._targetElem = null;
        }

        get comb() {
          return this.data.comb;
        }

        set comb(value) {
          this.data.comb = value;
        }

        get type() {
          return this.data._type;
        }

        set type(value) {
          if (this.data._type != value) {
            this.data = (_crd && newCondition === void 0 ? (_reportPossibleCrUseOfnewCondition({
              error: Error()
            }), newCondition) : newCondition)({
              comb: this.data.comb,
              _type: value
            });
          }
        }

        get delayTime() {
          return this.data.time;
        }

        set delayTime(value) {
          this.data.time = value;
        }

        get delayDistance() {
          return this.data.distance;
        }

        set delayDistance(value) {
          this.data.distance = value;
        }

        get targetElem() {
          return this._targetElem;
        }

        set targetElem(value) {
          var _value$elemID;

          this._targetElem = value;
          this.data.targetElemID = (_value$elemID = value == null ? void 0 : value.elemID) != null ? _value$elemID : "";
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "comb", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "comb"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "type", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "type"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "delayTime", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "delayTime"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "delayDistance", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "delayDistance"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "targetElem", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "targetElem"), _class2.prototype)), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c80407bb51b431d2a2398546fb61644dc7341d3c.js.map