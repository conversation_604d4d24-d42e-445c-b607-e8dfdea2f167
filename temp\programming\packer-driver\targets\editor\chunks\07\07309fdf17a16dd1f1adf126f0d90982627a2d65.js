System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, Bag, BaseInfo, Equip, GameLevel, GM, DataManager, _class, _crd, DataMgr;

  function _reportPossibleCrUseOfBag(extras) {
    _reporterNs.report("Bag", "./bag/Bag", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseInfo(extras) {
    _reporterNs.report("BaseInfo", "./BaseInfo", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEquip(extras) {
    _reporterNs.report("Equip", "./equip/Equip", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameLevel(extras) {
    _reporterNs.report("GameLevel", "./GameLevel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGM(extras) {
    _reporterNs.report("GM", "./gm/GM", _context.meta, extras);
  }

  _export("DataManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      Bag = _unresolved_2.Bag;
    }, function (_unresolved_3) {
      BaseInfo = _unresolved_3.BaseInfo;
    }, function (_unresolved_4) {
      Equip = _unresolved_4.Equip;
    }, function (_unresolved_5) {
      GameLevel = _unresolved_5.GameLevel;
    }, function (_unresolved_6) {
      GM = _unresolved_6.GM;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5eca8ZRHmRJQrCXkncrUIyF", "DataManager", undefined);

      //数据基础类
      // 数据管理类，负责游戏数据的管理和提供
      _export("DataManager", DataManager = class DataManager {
        constructor() {
          // 基础信息
          this.baseInfo = new (_crd && BaseInfo === void 0 ? (_reportPossibleCrUseOfBaseInfo({
            error: Error()
          }), BaseInfo) : BaseInfo)();
          // 背包数据
          this.bag = new (_crd && Bag === void 0 ? (_reportPossibleCrUseOfBag({
            error: Error()
          }), Bag) : Bag)();
          // 装备数据
          this.equip = new (_crd && Equip === void 0 ? (_reportPossibleCrUseOfEquip({
            error: Error()
          }), Equip) : Equip)();
          // 游戏关卡数据
          this.gameLevel = new (_crd && GameLevel === void 0 ? (_reportPossibleCrUseOfGameLevel({
            error: Error()
          }), GameLevel) : GameLevel)();
          // gm数据
          this.gm = new (_crd && GM === void 0 ? (_reportPossibleCrUseOfGM({
            error: Error()
          }), GM) : GM)();
        } // 数据更新


        update() {
          this.baseInfo.update();
          this.bag.update();
          this.equip.update();
          this.gameLevel.update();
        } // 初始化数据


        init() {
          this.baseInfo.init();
          this.gm.init();
          this.bag.init();
          this.equip.init();
          this.gameLevel.init();
        }

      });

      _class = DataManager;
      // 单例实例
      DataManager.Instance = new _class();

      _export("DataMgr", DataMgr = DataManager.Instance);

      window["DataMgr"] = DataMgr;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=07309fdf17a16dd1f1adf126f0d90982627a2d65.js.map