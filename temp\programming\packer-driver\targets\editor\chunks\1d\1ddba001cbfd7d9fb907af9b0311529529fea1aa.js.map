{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.mjs?cjs=&original=.js"], "names": ["req", "__cjsMetaURL", "loader", "d", "default", "throwInvalidWrapper", "url", "require"], "mappings": ";;;;;;;;;;;;;;;;;AACyBA,MAAAA,G,iBAAhBC,Y;;AACFC,MAAAA,M;;;;;;;;;;AAMaC,MAAAA,C,iBAAXC,O;;;mBART;;AAGA,UAAI;AAAA;AAAA,qBAAJ,EAAU;AACNF,QAAAA,MAAM,CAACG,mBAAP,CAA2B,oBAA3B,EAAiD,cAAYC,GAA7D;AACH;;AACDJ,MAAAA,MAAM,CAACK,OAAP;AAAA;AAAA;;yBAGSJ,C", "sourcesContent": ["// I am the facade module who provides access to the CommonJS module './init_cs_proto.js'~\nimport { __cjsMetaURL as req } from './init_cs_proto.js';\nimport loader from 'cce:/internal/ml/cjs-loader.mjs';\nif (!req) {\n    loader.throwInvalidWrapper('./init_cs_proto.js', import.meta.url);\n}\nloader.require(req);\nexport * from './init_cs_proto.js';\nimport { default as d } from './init_cs_proto.js'\nexport { d as default };"]}