System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, LevelDataEventTrigger, LevelDataEventTriggerType, LevelDataEventTriggerLog, _crd;

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "./LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "./LevelDataEventTrigger", _context.meta, extras);
  }

  _export("LevelDataEventTriggerLog", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      LevelDataEventTrigger = _unresolved_2.LevelDataEventTrigger;
      LevelDataEventTriggerType = _unresolved_2.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81eb3vpkB1AcbApsXCVQIy7", "LevelDataEventTriggerLog", undefined);

      _export("LevelDataEventTriggerLog", LevelDataEventTriggerLog = class LevelDataEventTriggerLog extends (_crd && LevelDataEventTrigger === void 0 ? (_reportPossibleCrUseOfLevelDataEventTrigger({
        error: Error()
      }), LevelDataEventTrigger) : LevelDataEventTrigger) {
        constructor() {
          super((_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log);
          this.message = "";
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2394e871a323c663a84b7d238b0e0de9d055eed0.js.map