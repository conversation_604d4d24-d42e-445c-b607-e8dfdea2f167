2025-8-28 15:16:04 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-8-28 15:16:04 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-8-28 15:16:04 - log: Request namespace: device-list
2025-8-28 15:16:07 - log: [Level Editor] Extension loaded
2025-8-28 15:16:07 - log: [Level Editor] File watchers setup (placeholder)
2025-8-28 15:16:09 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:16:25 - log: [Scene] meshopt wasm decoder initialized
2025-8-28 15:16:25 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-8-28 15:16:25 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-8-28 15:16:25 - log: [Scene] [PHYSICS]: using builtin.
2025-8-28 15:16:25 - log: [Scene] Cocos Creator v3.8.6
2025-8-28 15:16:26 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:26 - warn: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:26 - warn: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:26 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:26 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - warn: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - warn: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - warn: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - warn: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:27 - log: [Scene] Using custom pipeline: Builtin
2025-8-28 15:16:27 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:16:50 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-8-28 15:16:50 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-8-28 15:16:50 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-8-28 15:16:50 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-8-28 15:16:50 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-8-28 15:16:50 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - warn: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:16:50 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-8-28 15:16:50 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Network manager initialized
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Registered handler for msgId: 2
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Registered handler for msgId: 11
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] ResUpdate load main scene, 1, 2, 50.00, 0fe3d3b7-130d-477b-a1b8-02765e5c46c4@import
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] ResUpdate load main scene, 2, 3, 66.67, 32d536d6-b832-4420-b738-55a1503c7e37@f9941@import
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] ResUpdate load main scene, 3, 4, 75.00, 32d536d6-b832-4420-b738-55a1503c7e37@6c48a@import
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] ResUpdate load main scene, 4, 5, 80.00, 32d536d6-b832-4420-b738-55a1503c7e37@import
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] ResUpdate load main scene, 5, 5, 100.00, 32d536d6-b832-4420-b738-55a1503c7e37@native
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Registered handler for msgId: 53
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:16:51 - log: [PreviewInEditor] [2025/08/28 15:16:51] [DEBUG] DevLoginData localStorage devLoginData:null
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [INFO] Login dev login
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [INFO] NetMgr Connecting to ws://175.178.238.98:9011
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [INFO] NetMgr WebSocket connected
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr sendMessage 2 {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr Sent message 2, size: 59
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"*************","is_fighting":0}}
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_GET_SESSION","ret_code":"RET_CODE_NOT_IN_WHITE_LIST"}
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr onGetSession [object Object]
2025-8-28 15:17:01 - warn: [PreviewInEditor] [2025/08/28 15:17:01] [WARN] NetMgr onGetSession failed 2Error: [PreviewInEditor] [2025/08/28 15:17:01] [WARN] NetMgr onGetSession failed 2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at Logger.warn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:306:11)
    at logWarn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:35:12)
    at NetMgr.onGetSession (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:616:36)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:389:19
    at Array.forEach (<anonymous>)
    at NetMgr.dispatchMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:387:24)
    at NetMgr.handleMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:370:18)
    at NetMgr.onWebSocketMessage (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:281:18)
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":3,"body":{"heartbeat":{}}}
2025-8-28 15:17:01 - log: [PreviewInEditor] [2025/08/28 15:17:01] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:04 - log: [PreviewInEditor] [2025/08/28 15:17:04] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365424225","is_fighting":0}}
2025-8-28 15:17:04 - log: [PreviewInEditor] [2025/08/28 15:17:04] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:04 - log: [PreviewInEditor] [2025/08/28 15:17:04] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:04 - log: [PreviewInEditor] [2025/08/28 15:17:04] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":5,"body":{"heartbeat":{}}}
2025-8-28 15:17:04 - log: [PreviewInEditor] [2025/08/28 15:17:04] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:07 - log: [PreviewInEditor] [2025/08/28 15:17:07] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365427239","is_fighting":0}}
2025-8-28 15:17:07 - log: [PreviewInEditor] [2025/08/28 15:17:07] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:07 - log: [PreviewInEditor] [2025/08/28 15:17:07] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:07 - log: [PreviewInEditor] [2025/08/28 15:17:07] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":7,"body":{"heartbeat":{}}}
2025-8-28 15:17:07 - log: [PreviewInEditor] [2025/08/28 15:17:07] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:09 - log: [PreviewInEditor] [2025/08/28 15:17:09] [INFO] Login dev login
2025-8-28 15:17:09 - warn: [PreviewInEditor] [2025/08/28 15:17:09] [WARN] NetMgr Already connecting or connectedError: [PreviewInEditor] [2025/08/28 15:17:09] [WARN] NetMgr Already connecting or connected
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at Logger.warn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:306:11)
    at logWarn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:35:12)
    at NetMgr.connect (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:171:36)
    at NetMgr.login (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:185:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8b/8b75698035ac168457ee15bd41f3f20b43007833.js:192:40
    at DevLogin.login (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ed/ed203dd66a3a7da1558d359e5b74e2fd8b4ac79a.js:64:11)
    at DevLoginUI.onLoginButtonClick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8b/8b75698035ac168457ee15bd41f3f20b43007833.js:182:43)
    at CallbacksInvoker.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129789:28)
    at NodeEventProcessor.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51809:135)
    at Node.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55079:32)
    at Button._onTouchEnded (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:81013:23)
    at CallbacksInvoker.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129789:28)
    at NodeEventProcessor.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51845:33)
    at Node.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55090:32)
    at NodeEventProcessor._handleTouchEnd (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:52204:16)
    at NodeEventProcessor._handleEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:52153:29)
    at PointerEventDispatcher.dispatchEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:241060:41)
    at PointerEventDispatcher.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:240980:25)
    at Input._emitEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23482:31)
    at Input._dispatchEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23620:18)
    at Input._simulateEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23467:16)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23526:20)
    at Eventified.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129791:19)
    at MouseInputSource.eval [as _handleMouseUp] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:260698:31)
    at MouseInputSource.dispatchMouseUpEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:260588:16)
    at Input._dispatchMouseUpEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23321:179)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\facade\preview-scene-facade.ccc:1:2918
    at Operation._emitMouseEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\public\operation\index.ccc:1:2999)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\ipc.ccc:2:630
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-8-28 15:17:10 - log: [PreviewInEditor] [2025/08/28 15:17:10] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365430240","is_fighting":0}}
2025-8-28 15:17:10 - log: [PreviewInEditor] [2025/08/28 15:17:10] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:10 - log: [PreviewInEditor] [2025/08/28 15:17:10] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:10 - log: [PreviewInEditor] [2025/08/28 15:17:10] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":9,"body":{"heartbeat":{}}}
2025-8-28 15:17:10 - log: [PreviewInEditor] [2025/08/28 15:17:10] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:13 - log: [PreviewInEditor] [2025/08/28 15:17:13] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365433255","is_fighting":0}}
2025-8-28 15:17:13 - log: [PreviewInEditor] [2025/08/28 15:17:13] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:13 - log: [PreviewInEditor] [2025/08/28 15:17:13] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:13 - log: [PreviewInEditor] [2025/08/28 15:17:13] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":11,"body":{"heartbeat":{}}}
2025-8-28 15:17:13 - log: [PreviewInEditor] [2025/08/28 15:17:13] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:16 - log: [PreviewInEditor] [2025/08/28 15:17:16] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365436257","is_fighting":0}}
2025-8-28 15:17:16 - log: [PreviewInEditor] [2025/08/28 15:17:16] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:16 - log: [PreviewInEditor] [2025/08/28 15:17:16] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:16 - log: [PreviewInEditor] [2025/08/28 15:17:16] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":13,"body":{"heartbeat":{}}}
2025-8-28 15:17:16 - log: [PreviewInEditor] [2025/08/28 15:17:16] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:19 - log: [PreviewInEditor] [2025/08/28 15:17:19] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365439258","is_fighting":0}}
2025-8-28 15:17:19 - log: [PreviewInEditor] [2025/08/28 15:17:19] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:19 - log: [PreviewInEditor] [2025/08/28 15:17:19] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:19 - log: [PreviewInEditor] [2025/08/28 15:17:19] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":15,"body":{"heartbeat":{}}}
2025-8-28 15:17:19 - log: [PreviewInEditor] [2025/08/28 15:17:19] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:22 - log: [PreviewInEditor] [2025/08/28 15:17:22] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365442258","is_fighting":0}}
2025-8-28 15:17:22 - log: [PreviewInEditor] [2025/08/28 15:17:22] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:22 - log: [PreviewInEditor] [2025/08/28 15:17:22] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:22 - log: [PreviewInEditor] [2025/08/28 15:17:22] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":17,"body":{"heartbeat":{}}}
2025-8-28 15:17:22 - log: [PreviewInEditor] [2025/08/28 15:17:22] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:25 - log: [PreviewInEditor] [2025/08/28 15:17:25] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365445258","is_fighting":0}}
2025-8-28 15:17:25 - log: [PreviewInEditor] [2025/08/28 15:17:25] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:25 - log: [PreviewInEditor] [2025/08/28 15:17:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:25 - log: [PreviewInEditor] [2025/08/28 15:17:25] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":19,"body":{"heartbeat":{}}}
2025-8-28 15:17:25 - log: [PreviewInEditor] [2025/08/28 15:17:25] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:28 - log: [PreviewInEditor] [2025/08/28 15:17:28] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365448273","is_fighting":0}}
2025-8-28 15:17:28 - log: [PreviewInEditor] [2025/08/28 15:17:28] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:28 - log: [PreviewInEditor] [2025/08/28 15:17:28] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:28 - log: [PreviewInEditor] [2025/08/28 15:17:28] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":21,"body":{"heartbeat":{}}}
2025-8-28 15:17:28 - log: [PreviewInEditor] [2025/08/28 15:17:28] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:31 - log: [PreviewInEditor] [2025/08/28 15:17:31] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365451276","is_fighting":0}}
2025-8-28 15:17:31 - log: [PreviewInEditor] [2025/08/28 15:17:31] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:31 - log: [PreviewInEditor] [2025/08/28 15:17:31] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:31 - log: [PreviewInEditor] [2025/08/28 15:17:31] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":23,"body":{"heartbeat":{}}}
2025-8-28 15:17:31 - log: [PreviewInEditor] [2025/08/28 15:17:31] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:34 - log: [PreviewInEditor] [2025/08/28 15:17:34] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365454285","is_fighting":0}}
2025-8-28 15:17:34 - log: [PreviewInEditor] [2025/08/28 15:17:34] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:34 - log: [PreviewInEditor] [2025/08/28 15:17:34] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:34 - log: [PreviewInEditor] [2025/08/28 15:17:34] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":25,"body":{"heartbeat":{}}}
2025-8-28 15:17:34 - log: [PreviewInEditor] [2025/08/28 15:17:34] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:37 - log: [PreviewInEditor] [2025/08/28 15:17:37] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365457291","is_fighting":0}}
2025-8-28 15:17:37 - log: [PreviewInEditor] [2025/08/28 15:17:37] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:37 - log: [PreviewInEditor] [2025/08/28 15:17:37] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:37 - log: [PreviewInEditor] [2025/08/28 15:17:37] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":27,"body":{"heartbeat":{}}}
2025-8-28 15:17:37 - log: [PreviewInEditor] [2025/08/28 15:17:37] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:40 - log: [PreviewInEditor] [2025/08/28 15:17:40] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365460292","is_fighting":0}}
2025-8-28 15:17:40 - log: [PreviewInEditor] [2025/08/28 15:17:40] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:40 - log: [PreviewInEditor] [2025/08/28 15:17:40] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:40 - log: [PreviewInEditor] [2025/08/28 15:17:40] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":29,"body":{"heartbeat":{}}}
2025-8-28 15:17:40 - log: [PreviewInEditor] [2025/08/28 15:17:40] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:43 - log: [PreviewInEditor] [2025/08/28 15:17:43] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365463308","is_fighting":0}}
2025-8-28 15:17:43 - log: [PreviewInEditor] [2025/08/28 15:17:43] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:43 - log: [PreviewInEditor] [2025/08/28 15:17:43] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:43 - log: [PreviewInEditor] [2025/08/28 15:17:43] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":31,"body":{"heartbeat":{}}}
2025-8-28 15:17:43 - log: [PreviewInEditor] [2025/08/28 15:17:43] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:46 - log: [PreviewInEditor] [2025/08/28 15:17:46] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365466322","is_fighting":0}}
2025-8-28 15:17:46 - log: [PreviewInEditor] [2025/08/28 15:17:46] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:46 - log: [PreviewInEditor] [2025/08/28 15:17:46] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:46 - log: [PreviewInEditor] [2025/08/28 15:17:46] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":33,"body":{"heartbeat":{}}}
2025-8-28 15:17:46 - log: [PreviewInEditor] [2025/08/28 15:17:46] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:49 - log: [PreviewInEditor] [2025/08/28 15:17:49] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365469326","is_fighting":0}}
2025-8-28 15:17:49 - log: [PreviewInEditor] [2025/08/28 15:17:49] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:49 - log: [PreviewInEditor] [2025/08/28 15:17:49] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:49 - log: [PreviewInEditor] [2025/08/28 15:17:49] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":35,"body":{"heartbeat":{}}}
2025-8-28 15:17:49 - log: [PreviewInEditor] [2025/08/28 15:17:49] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:52 - log: [PreviewInEditor] [2025/08/28 15:17:52] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365472342","is_fighting":0}}
2025-8-28 15:17:52 - log: [PreviewInEditor] [2025/08/28 15:17:52] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:52 - log: [PreviewInEditor] [2025/08/28 15:17:52] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:52 - log: [PreviewInEditor] [2025/08/28 15:17:52] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":37,"body":{"heartbeat":{}}}
2025-8-28 15:17:52 - log: [PreviewInEditor] [2025/08/28 15:17:52] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:55 - log: [PreviewInEditor] [2025/08/28 15:17:55] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365475359","is_fighting":0}}
2025-8-28 15:17:55 - log: [PreviewInEditor] [2025/08/28 15:17:55] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:55 - log: [PreviewInEditor] [2025/08/28 15:17:55] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:55 - log: [PreviewInEditor] [2025/08/28 15:17:55] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":39,"body":{"heartbeat":{}}}
2025-8-28 15:17:55 - log: [PreviewInEditor] [2025/08/28 15:17:55] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:17:58 - log: [PreviewInEditor] [2025/08/28 15:17:58] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365478367","is_fighting":0}}
2025-8-28 15:17:58 - log: [PreviewInEditor] [2025/08/28 15:17:58] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:17:58 - log: [PreviewInEditor] [2025/08/28 15:17:58] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:17:58 - log: [PreviewInEditor] [2025/08/28 15:17:58] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":41,"body":{"heartbeat":{}}}
2025-8-28 15:17:58 - log: [PreviewInEditor] [2025/08/28 15:17:58] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:01 - log: [PreviewInEditor] [2025/08/28 15:18:01] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365481372","is_fighting":0}}
2025-8-28 15:18:01 - log: [PreviewInEditor] [2025/08/28 15:18:01] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:01 - log: [PreviewInEditor] [2025/08/28 15:18:01] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:01 - log: [PreviewInEditor] [2025/08/28 15:18:01] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":43,"body":{"heartbeat":{}}}
2025-8-28 15:18:01 - log: [PreviewInEditor] [2025/08/28 15:18:01] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:04 - log: [PreviewInEditor] [2025/08/28 15:18:04] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365484377","is_fighting":0}}
2025-8-28 15:18:04 - log: [PreviewInEditor] [2025/08/28 15:18:04] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:04 - log: [PreviewInEditor] [2025/08/28 15:18:04] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:04 - log: [PreviewInEditor] [2025/08/28 15:18:04] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":45,"body":{"heartbeat":{}}}
2025-8-28 15:18:04 - log: [PreviewInEditor] [2025/08/28 15:18:04] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:07 - log: [PreviewInEditor] [2025/08/28 15:18:07] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365487391","is_fighting":0}}
2025-8-28 15:18:07 - log: [PreviewInEditor] [2025/08/28 15:18:07] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:07 - log: [PreviewInEditor] [2025/08/28 15:18:07] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:07 - log: [PreviewInEditor] [2025/08/28 15:18:07] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":47,"body":{"heartbeat":{}}}
2025-8-28 15:18:07 - log: [PreviewInEditor] [2025/08/28 15:18:07] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:10 - log: [PreviewInEditor] [2025/08/28 15:18:10] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365490394","is_fighting":0}}
2025-8-28 15:18:10 - log: [PreviewInEditor] [2025/08/28 15:18:10] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:10 - log: [PreviewInEditor] [2025/08/28 15:18:10] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:10 - log: [PreviewInEditor] [2025/08/28 15:18:10] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":49,"body":{"heartbeat":{}}}
2025-8-28 15:18:10 - log: [PreviewInEditor] [2025/08/28 15:18:10] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:13 - log: [PreviewInEditor] [2025/08/28 15:18:13] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365493394","is_fighting":0}}
2025-8-28 15:18:13 - log: [PreviewInEditor] [2025/08/28 15:18:13] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:13 - log: [PreviewInEditor] [2025/08/28 15:18:13] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:13 - log: [PreviewInEditor] [2025/08/28 15:18:13] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":51,"body":{"heartbeat":{}}}
2025-8-28 15:18:13 - log: [PreviewInEditor] [2025/08/28 15:18:13] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:16 - log: [PreviewInEditor] [2025/08/28 15:18:16] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365496394","is_fighting":0}}
2025-8-28 15:18:16 - log: [PreviewInEditor] [2025/08/28 15:18:16] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:16 - log: [PreviewInEditor] [2025/08/28 15:18:16] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:16 - log: [PreviewInEditor] [2025/08/28 15:18:16] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":53,"body":{"heartbeat":{}}}
2025-8-28 15:18:16 - log: [PreviewInEditor] [2025/08/28 15:18:16] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:19 - log: [PreviewInEditor] [2025/08/28 15:18:19] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365499408","is_fighting":0}}
2025-8-28 15:18:19 - log: [PreviewInEditor] [2025/08/28 15:18:19] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:19 - log: [PreviewInEditor] [2025/08/28 15:18:19] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:19 - log: [PreviewInEditor] [2025/08/28 15:18:19] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":55,"body":{"heartbeat":{}}}
2025-8-28 15:18:19 - log: [PreviewInEditor] [2025/08/28 15:18:19] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:22 - log: [PreviewInEditor] [2025/08/28 15:18:22] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365502411","is_fighting":0}}
2025-8-28 15:18:22 - log: [PreviewInEditor] [2025/08/28 15:18:22] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:22 - log: [PreviewInEditor] [2025/08/28 15:18:22] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:22 - log: [PreviewInEditor] [2025/08/28 15:18:22] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":57,"body":{"heartbeat":{}}}
2025-8-28 15:18:22 - log: [PreviewInEditor] [2025/08/28 15:18:22] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:25 - log: [PreviewInEditor] [2025/08/28 15:18:25] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365505422","is_fighting":0}}
2025-8-28 15:18:25 - log: [PreviewInEditor] [2025/08/28 15:18:25] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:25 - log: [PreviewInEditor] [2025/08/28 15:18:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:25 - log: [PreviewInEditor] [2025/08/28 15:18:25] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":59,"body":{"heartbeat":{}}}
2025-8-28 15:18:25 - log: [PreviewInEditor] [2025/08/28 15:18:25] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:28 - log: [PreviewInEditor] [2025/08/28 15:18:28] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365508428","is_fighting":0}}
2025-8-28 15:18:28 - log: [PreviewInEditor] [2025/08/28 15:18:28] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:28 - log: [PreviewInEditor] [2025/08/28 15:18:28] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:28 - log: [PreviewInEditor] [2025/08/28 15:18:28] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":61,"body":{"heartbeat":{}}}
2025-8-28 15:18:28 - log: [PreviewInEditor] [2025/08/28 15:18:28] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:31 - log: [PreviewInEditor] [2025/08/28 15:18:31] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365511444","is_fighting":0}}
2025-8-28 15:18:31 - log: [PreviewInEditor] [2025/08/28 15:18:31] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:31 - log: [PreviewInEditor] [2025/08/28 15:18:31] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:31 - log: [PreviewInEditor] [2025/08/28 15:18:31] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":63,"body":{"heartbeat":{}}}
2025-8-28 15:18:31 - log: [PreviewInEditor] [2025/08/28 15:18:31] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:34 - log: [PreviewInEditor] [2025/08/28 15:18:34] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365514445","is_fighting":0}}
2025-8-28 15:18:34 - log: [PreviewInEditor] [2025/08/28 15:18:34] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:34 - log: [PreviewInEditor] [2025/08/28 15:18:34] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:34 - log: [PreviewInEditor] [2025/08/28 15:18:34] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":65,"body":{"heartbeat":{}}}
2025-8-28 15:18:34 - log: [PreviewInEditor] [2025/08/28 15:18:34] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:37 - log: [PreviewInEditor] [2025/08/28 15:18:37] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365517461","is_fighting":0}}
2025-8-28 15:18:37 - log: [PreviewInEditor] [2025/08/28 15:18:37] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:37 - log: [PreviewInEditor] [2025/08/28 15:18:37] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:37 - log: [PreviewInEditor] [2025/08/28 15:18:37] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":67,"body":{"heartbeat":{}}}
2025-8-28 15:18:37 - log: [PreviewInEditor] [2025/08/28 15:18:37] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:40 - log: [PreviewInEditor] [2025/08/28 15:18:40] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365520476","is_fighting":0}}
2025-8-28 15:18:40 - log: [PreviewInEditor] [2025/08/28 15:18:40] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:40 - log: [PreviewInEditor] [2025/08/28 15:18:40] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:40 - log: [PreviewInEditor] [2025/08/28 15:18:40] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":69,"body":{"heartbeat":{}}}
2025-8-28 15:18:40 - log: [PreviewInEditor] [2025/08/28 15:18:40] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:43 - log: [PreviewInEditor] [2025/08/28 15:18:43] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365523479","is_fighting":0}}
2025-8-28 15:18:43 - log: [PreviewInEditor] [2025/08/28 15:18:43] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:43 - log: [PreviewInEditor] [2025/08/28 15:18:43] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:43 - log: [PreviewInEditor] [2025/08/28 15:18:43] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":71,"body":{"heartbeat":{}}}
2025-8-28 15:18:43 - log: [PreviewInEditor] [2025/08/28 15:18:43] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:46 - log: [PreviewInEditor] [2025/08/28 15:18:46] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365526492","is_fighting":0}}
2025-8-28 15:18:46 - log: [PreviewInEditor] [2025/08/28 15:18:46] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:46 - log: [PreviewInEditor] [2025/08/28 15:18:46] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:46 - log: [PreviewInEditor] [2025/08/28 15:18:46] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":73,"body":{"heartbeat":{}}}
2025-8-28 15:18:46 - log: [PreviewInEditor] [2025/08/28 15:18:46] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:49 - log: [PreviewInEditor] [2025/08/28 15:18:49] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365529496","is_fighting":0}}
2025-8-28 15:18:49 - log: [PreviewInEditor] [2025/08/28 15:18:49] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:49 - log: [PreviewInEditor] [2025/08/28 15:18:49] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:49 - log: [PreviewInEditor] [2025/08/28 15:18:49] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":75,"body":{"heartbeat":{}}}
2025-8-28 15:18:49 - log: [PreviewInEditor] [2025/08/28 15:18:49] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:52 - log: [PreviewInEditor] [2025/08/28 15:18:52] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365532496","is_fighting":0}}
2025-8-28 15:18:52 - log: [PreviewInEditor] [2025/08/28 15:18:52] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:52 - log: [PreviewInEditor] [2025/08/28 15:18:52] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:52 - log: [PreviewInEditor] [2025/08/28 15:18:52] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":77,"body":{"heartbeat":{}}}
2025-8-28 15:18:52 - log: [PreviewInEditor] [2025/08/28 15:18:52] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:55 - log: [PreviewInEditor] [2025/08/28 15:18:55] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365535496","is_fighting":0}}
2025-8-28 15:18:55 - log: [PreviewInEditor] [2025/08/28 15:18:55] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:55 - log: [PreviewInEditor] [2025/08/28 15:18:55] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:55 - log: [PreviewInEditor] [2025/08/28 15:18:55] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":79,"body":{"heartbeat":{}}}
2025-8-28 15:18:55 - log: [PreviewInEditor] [2025/08/28 15:18:55] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:18:58 - log: [PreviewInEditor] [2025/08/28 15:18:58] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365538496","is_fighting":0}}
2025-8-28 15:18:58 - log: [PreviewInEditor] [2025/08/28 15:18:58] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:18:58 - log: [PreviewInEditor] [2025/08/28 15:18:58] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:18:58 - log: [PreviewInEditor] [2025/08/28 15:18:58] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":81,"body":{"heartbeat":{}}}
2025-8-28 15:18:58 - log: [PreviewInEditor] [2025/08/28 15:18:58] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:01 - log: [PreviewInEditor] [2025/08/28 15:19:01] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365541514","is_fighting":0}}
2025-8-28 15:19:01 - log: [PreviewInEditor] [2025/08/28 15:19:01] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:01 - log: [PreviewInEditor] [2025/08/28 15:19:01] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:01 - log: [PreviewInEditor] [2025/08/28 15:19:01] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":83,"body":{"heartbeat":{}}}
2025-8-28 15:19:01 - log: [PreviewInEditor] [2025/08/28 15:19:01] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:04 - log: [PreviewInEditor] [2025/08/28 15:19:04] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365544527","is_fighting":0}}
2025-8-28 15:19:04 - log: [PreviewInEditor] [2025/08/28 15:19:04] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:04 - log: [PreviewInEditor] [2025/08/28 15:19:04] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:04 - log: [PreviewInEditor] [2025/08/28 15:19:04] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":85,"body":{"heartbeat":{}}}
2025-8-28 15:19:04 - log: [PreviewInEditor] [2025/08/28 15:19:04] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:07 - log: [PreviewInEditor] [2025/08/28 15:19:07] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365547530","is_fighting":0}}
2025-8-28 15:19:07 - log: [PreviewInEditor] [2025/08/28 15:19:07] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:07 - log: [PreviewInEditor] [2025/08/28 15:19:07] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:07 - log: [PreviewInEditor] [2025/08/28 15:19:07] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":87,"body":{"heartbeat":{}}}
2025-8-28 15:19:07 - log: [PreviewInEditor] [2025/08/28 15:19:07] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:10 - log: [PreviewInEditor] [2025/08/28 15:19:10] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365550529","is_fighting":0}}
2025-8-28 15:19:10 - log: [PreviewInEditor] [2025/08/28 15:19:10] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:10 - log: [PreviewInEditor] [2025/08/28 15:19:10] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:10 - log: [PreviewInEditor] [2025/08/28 15:19:10] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":89,"body":{"heartbeat":{}}}
2025-8-28 15:19:10 - log: [PreviewInEditor] [2025/08/28 15:19:10] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:13 - log: [PreviewInEditor] [2025/08/28 15:19:13] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365553546","is_fighting":0}}
2025-8-28 15:19:13 - log: [PreviewInEditor] [2025/08/28 15:19:13] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:13 - log: [PreviewInEditor] [2025/08/28 15:19:13] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:13 - log: [PreviewInEditor] [2025/08/28 15:19:13] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":91,"body":{"heartbeat":{}}}
2025-8-28 15:19:13 - log: [PreviewInEditor] [2025/08/28 15:19:13] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:16 - log: [PreviewInEditor] [2025/08/28 15:19:16] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365556547","is_fighting":0}}
2025-8-28 15:19:16 - log: [PreviewInEditor] [2025/08/28 15:19:16] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:16 - log: [PreviewInEditor] [2025/08/28 15:19:16] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:16 - log: [PreviewInEditor] [2025/08/28 15:19:16] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":93,"body":{"heartbeat":{}}}
2025-8-28 15:19:16 - log: [PreviewInEditor] [2025/08/28 15:19:16] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:19 - log: [PreviewInEditor] [2025/08/28 15:19:19] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365559563","is_fighting":0}}
2025-8-28 15:19:19 - log: [PreviewInEditor] [2025/08/28 15:19:19] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:19 - log: [PreviewInEditor] [2025/08/28 15:19:19] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:19 - log: [PreviewInEditor] [2025/08/28 15:19:19] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":95,"body":{"heartbeat":{}}}
2025-8-28 15:19:19 - log: [PreviewInEditor] [2025/08/28 15:19:19] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:21 - log: [PreviewInEditor] [2025/08/28 15:19:21] [INFO] Login dev login
2025-8-28 15:19:21 - warn: [PreviewInEditor] [2025/08/28 15:19:21] [WARN] NetMgr Already connecting or connectedError: [PreviewInEditor] [2025/08/28 15:19:21] [WARN] NetMgr Already connecting or connected
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at Logger.warn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:306:11)
    at logWarn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/12/1267a4e4e87636ca3fcca0ce75a2153737637253.js:35:12)
    at NetMgr.connect (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:171:36)
    at NetMgr.login (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ad/adac3babafbcaaff82b91cc080fe88b86548f97a.js:185:16)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8b/8b75698035ac168457ee15bd41f3f20b43007833.js:192:40
    at DevLogin.login (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ed/ed203dd66a3a7da1558d359e5b74e2fd8b4ac79a.js:64:11)
    at DevLoginUI.onLoginButtonClick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8b/8b75698035ac168457ee15bd41f3f20b43007833.js:182:43)
    at CallbacksInvoker.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129789:28)
    at NodeEventProcessor.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51809:135)
    at Node.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55079:32)
    at Button._onTouchEnded (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:81013:23)
    at CallbacksInvoker.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129789:28)
    at NodeEventProcessor.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51845:33)
    at Node.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55090:32)
    at NodeEventProcessor._handleTouchEnd (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:52204:16)
    at NodeEventProcessor._handleEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:52153:29)
    at PointerEventDispatcher.dispatchEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:241060:41)
    at PointerEventDispatcher.dispatchEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:240980:25)
    at Input._emitEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23482:31)
    at Input._dispatchEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23620:18)
    at Input._simulateEventTouch (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23467:16)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23526:20)
    at Eventified.emit (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:129791:19)
    at MouseInputSource.eval [as _handleMouseUp] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:260698:31)
    at MouseInputSource.dispatchMouseUpEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:260588:16)
    at Input._dispatchMouseUpEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:23321:179)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\facade\preview-scene-facade.ccc:1:2918
    at Operation._emitMouseEvent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\public\operation\index.ccc:1:2999)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\ipc.ccc:2:630
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1004)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-8-28 15:19:22 - log: [PreviewInEditor] [2025/08/28 15:19:22] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365562578","is_fighting":0}}
2025-8-28 15:19:22 - log: [PreviewInEditor] [2025/08/28 15:19:22] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:22 - log: [PreviewInEditor] [2025/08/28 15:19:22] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:22 - log: [PreviewInEditor] [2025/08/28 15:19:22] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":97,"body":{"heartbeat":{}}}
2025-8-28 15:19:22 - log: [PreviewInEditor] [2025/08/28 15:19:22] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:25 - log: [PreviewInEditor] [2025/08/28 15:19:25] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365565580","is_fighting":0}}
2025-8-28 15:19:25 - log: [PreviewInEditor] [2025/08/28 15:19:25] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:25 - log: [PreviewInEditor] [2025/08/28 15:19:25] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:25 - log: [PreviewInEditor] [2025/08/28 15:19:25] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":99,"body":{"heartbeat":{}}}
2025-8-28 15:19:25 - log: [PreviewInEditor] [2025/08/28 15:19:25] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:28 - log: [PreviewInEditor] [2025/08/28 15:19:28] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365568580","is_fighting":0}}
2025-8-28 15:19:28 - log: [PreviewInEditor] [2025/08/28 15:19:28] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:28 - log: [PreviewInEditor] [2025/08/28 15:19:28] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:28 - log: [PreviewInEditor] [2025/08/28 15:19:28] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":101,"body":{"heartbeat":{}}}
2025-8-28 15:19:28 - log: [PreviewInEditor] [2025/08/28 15:19:28] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:31 - log: [PreviewInEditor] [2025/08/28 15:19:31] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365571596","is_fighting":0}}
2025-8-28 15:19:31 - log: [PreviewInEditor] [2025/08/28 15:19:31] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:31 - log: [PreviewInEditor] [2025/08/28 15:19:31] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:31 - log: [PreviewInEditor] [2025/08/28 15:19:31] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":103,"body":{"heartbeat":{}}}
2025-8-28 15:19:31 - log: [PreviewInEditor] [2025/08/28 15:19:31] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:34 - log: [PreviewInEditor] [2025/08/28 15:19:34] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756365574612","is_fighting":0}}
2025-8-28 15:19:34 - log: [PreviewInEditor] [2025/08/28 15:19:34] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:19:34 - log: [PreviewInEditor] [2025/08/28 15:19:34] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:19:34 - log: [PreviewInEditor] [2025/08/28 15:19:34] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":105,"body":{"heartbeat":{}}}
2025-8-28 15:19:34 - log: [PreviewInEditor] [2025/08/28 15:19:34] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:19:36 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:25:26 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:25:26 - log: [Scene] meshopt wasm decoder initialized
2025-8-28 15:25:26 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-8-28 15:25:26 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-8-28 15:25:26 - log: [Scene] [PHYSICS]: using builtin.
2025-8-28 15:25:27 - log: [Scene] Cocos Creator v3.8.6
2025-8-28 15:25:27 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - warn: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:27 - log: [Scene] Using custom pipeline: Builtin
2025-8-28 15:25:27 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:25:31 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-8-28 15:25:31 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-8-28 15:25:31 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-8-28 15:25:31 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-8-28 15:25:31 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-8-28 15:25:31 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - warn: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:25:31 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-8-28 15:25:31 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Network manager initialized
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Registered handler for msgId: 2
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Registered handler for msgId: 11
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] ResUpdate load main scene, 1, 2, 50.00, 0fe3d3b7-130d-477b-a1b8-02765e5c46c4@import
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] ResUpdate load main scene, 2, 3, 66.67, 32d536d6-b832-4420-b738-55a1503c7e37@f9941@import
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] ResUpdate load main scene, 3, 4, 75.00, 32d536d6-b832-4420-b738-55a1503c7e37@6c48a@import
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] ResUpdate load main scene, 4, 5, 80.00, 32d536d6-b832-4420-b738-55a1503c7e37@import
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] ResUpdate load main scene, 5, 5, 100.00, 32d536d6-b832-4420-b738-55a1503c7e37@native
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Registered handler for msgId: 53
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:25:32 - log: [PreviewInEditor] [2025/08/28 15:25:32] [DEBUG] DevLoginData localStorage devLoginData:{"servername":"jerry","username":"youngxiang07","password":"123456"}
2025-8-28 15:26:07 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-8-28 15:26:07 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:27:42 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:27:42 - log: [Scene] meshopt wasm decoder initialized
2025-8-28 15:27:42 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-8-28 15:27:42 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-8-28 15:27:42 - log: [Scene] [PHYSICS]: using builtin.
2025-8-28 15:27:42 - log: [Scene] Cocos Creator v3.8.6
2025-8-28 15:27:42 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [Scene] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [Scene] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [Scene] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [Scene] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [Scene] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - warn: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [Scene] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:27:42 - log: [Scene] Using custom pipeline: Builtin
2025-8-28 15:27:42 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:28:46 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-8-28 15:28:46 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-8-28 15:28:46 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-8-28 15:28:46 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-8-28 15:28:46 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-8-28 15:28:46 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "LevelEditorUI.newName", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js:71:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.position" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint position cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2Error: [PreviewInEditor] No need to specify the "type" of "PathPoint.direction" because cc.Vec2 is a child class of ValueType. Just set the default value to 'new cc.Vec2()' and it will be handled properly. PathPoint direction cc.Vec2
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271327:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at proxyFn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270978:16)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/29/295f944ba5e787926e9e3cd797c68a5e1b32ccfc.js:39:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionA' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an arrayError: [PreviewInEditor] The 'default' attribute of 'EventGroup.conditionB' must be an array
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271962:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/37/37126d0ac44771ea4fad6837948cff6d069289c4.js:68:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "uiSelect.itemMaxNum" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.defaultItem" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.Error: [PreviewInEditor] The type of "uiSelect.itemDatas" must be CCString, not String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271975:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.Error: [PreviewInEditor] No needs to indicate the 'cc.String' attribute for "uiSelect.defaultItem", which its default value is type of String.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271299:11)
    at defineProp (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127744:27)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127905:11)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/22/228dea4dea42298577d918418bbbc180c75fe6cc.js:69:11)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:46 - warn: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.Error: [PreviewInEditor] The type of "RogueSelectIcon.index" must be CCFloat or CCInteger, not Number.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146866:12)
    at warnID (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:147054:5)
    at parseType (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:271985:11)
    at preprocessAttrs (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:272090:11)
    at declareProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127901:7)
    at CCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:127930:5)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:269970:21)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:270981:18)
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/f8/f8dfccebc97155f88a4e0b51d68ffa7cee412d93.js:64:267)
    at doExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:517:30)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:508:21)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:493:28
    at Array.forEach (<anonymous>)
    at postOrderExec (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:491:10)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\static\executor\systemjs-bridge\out\index.js:440:12
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:13:13
    at Object.execute (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js:8:7)
    at Executor._importPrerequisiteModules (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:358:13)
    at Executor.reload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@editor\lib-programming\src\executor\index.ts:235:13)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1787
    at GlobalEnv.processQueue (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:2184)
    at GlobalEnv.record (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:1955)
    at ScriptManager.init (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\scripts.ccc:1:3794)
2025-8-28 15:28:47 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-8-28 15:28:47 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Network manager initialized
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Registered handler for msgId: 2
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Registered handler for msgId: 11
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] ResUpdate load main scene, 1, 2, 50.00, 0fe3d3b7-130d-477b-a1b8-02765e5c46c4@import
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] ResUpdate load main scene, 2, 3, 66.67, 32d536d6-b832-4420-b738-55a1503c7e37@f9941@import
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] ResUpdate load main scene, 3, 4, 75.00, 32d536d6-b832-4420-b738-55a1503c7e37@6c48a@import
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] ResUpdate load main scene, 4, 5, 80.00, 32d536d6-b832-4420-b738-55a1503c7e37@import
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] ResUpdate load main scene, 5, 5, 100.00, 32d536d6-b832-4420-b738-55a1503c7e37@native
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Registered handler for msgId: 53
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [INFO] NetMgr Registered handler for msgId: 6
2025-8-28 15:28:47 - log: [PreviewInEditor] [2025/08/28 15:28:47] [DEBUG] DevLoginData localStorage devLoginData:{"servername":"jerry","username":"youngxiang07","password":"123456"}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] Login dev login
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Connecting to ws://175.178.238.98:9011
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr WebSocket connected
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr sendMessage 2 {"get_session":{"account_type":"ACCOUNT_TYPE_NONE","platform":"PLATFORM_EDITOR","code":"youngxiang0700#e10adc3949ba59abbe56e057f20f883e","version":1}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Sent message 2, size: 63
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"*************","is_fighting":0}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_GET_SESSION","body":{"get_session":{"openid":"youngxiang0700"}}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr onGetSession [object Object]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr onGetSession youngxiang0700:
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr sendMessage 6 {"get_role":{"uin":"0","area_id":0}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Sent message 6, size: 19
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":3,"body":{"heartbeat":{}}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_GET_ROLE","body":{"get_role":{"base":{"uin":"7205760517782687745","openid":"youngxiang0700","area_id":15,"level":1,"energy":100},"cur_time":1756366131,"client_data":{},"cmd_seq":{"items":[{"cmd":102,"seq_no":1},{"cmd":103,"seq_no":1},{"cmd":104,"seq_no":1},{"cmd":202,"seq_no":1},{"cmd":203,"seq_no":1}]}}}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Registered handler for msgId: 101
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Registered handler for msgId: 102
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr sendMessage 101 {"get_item_list":{}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Sent message 101, size: 9
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Registered handler for msgId: 201
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Registered handler for msgId: 204
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Registered handler for msgId: 205
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr sendMessage 201 {"get_equip_slot_info":{}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Sent message 201, size: 10
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [INFO] NetMgr Unregistered handler for msgId: 6
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_GET_ITEM_LIST","body":{"get_item_list":{}}}
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:51 - log: [PreviewInEditor] [2025/08/28 15:28:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_GET_EQUIP_SLOT_INFO","body":{"get_equip_slot_info":{"slots":[{"slot_id":1,"level":1,"equip_class":1},{"slot_id":2,"level":1,"equip_class":2},{"slot_id":3,"level":1,"equip_class":2},{"slot_id":4,"level":1,"equip_class":3},{"slot_id":5,"level":1,"equip_class":4}]}}}
2025-8-28 15:28:54 - log: [PreviewInEditor] [2025/08/28 15:28:54] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366134684","is_fighting":0}}
2025-8-28 15:28:54 - log: [PreviewInEditor] [2025/08/28 15:28:54] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:28:54 - log: [PreviewInEditor] [2025/08/28 15:28:54] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:54 - log: [PreviewInEditor] [2025/08/28 15:28:54] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":11,"body":{"heartbeat":{}}}
2025-8-28 15:28:54 - log: [PreviewInEditor] [2025/08/28 15:28:54] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:28:57 - log: [PreviewInEditor] [2025/08/28 15:28:57] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366137685","is_fighting":0}}
2025-8-28 15:28:57 - log: [PreviewInEditor] [2025/08/28 15:28:57] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:28:57 - log: [PreviewInEditor] [2025/08/28 15:28:57] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:28:57 - log: [PreviewInEditor] [2025/08/28 15:28:57] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":13,"body":{"heartbeat":{}}}
2025-8-28 15:28:57 - log: [PreviewInEditor] [2025/08/28 15:28:57] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:00 - log: [PreviewInEditor] [2025/08/28 15:29:00] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366140700","is_fighting":0}}
2025-8-28 15:29:00 - log: [PreviewInEditor] [2025/08/28 15:29:00] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:00 - log: [PreviewInEditor] [2025/08/28 15:29:00] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:00 - log: [PreviewInEditor] [2025/08/28 15:29:00] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":15,"body":{"heartbeat":{}}}
2025-8-28 15:29:00 - log: [PreviewInEditor] [2025/08/28 15:29:00] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:03 - log: [PreviewInEditor] [2025/08/28 15:29:03] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366143701","is_fighting":0}}
2025-8-28 15:29:03 - log: [PreviewInEditor] [2025/08/28 15:29:03] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:03 - log: [PreviewInEditor] [2025/08/28 15:29:03] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:03 - log: [PreviewInEditor] [2025/08/28 15:29:03] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":17,"body":{"heartbeat":{}}}
2025-8-28 15:29:03 - log: [PreviewInEditor] [2025/08/28 15:29:03] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:06 - log: [PreviewInEditor] [2025/08/28 15:29:06] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366146716","is_fighting":0}}
2025-8-28 15:29:06 - log: [PreviewInEditor] [2025/08/28 15:29:06] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:06 - log: [PreviewInEditor] [2025/08/28 15:29:06] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:06 - log: [PreviewInEditor] [2025/08/28 15:29:06] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":19,"body":{"heartbeat":{}}}
2025-8-28 15:29:06 - log: [PreviewInEditor] [2025/08/28 15:29:06] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:09 - log: [PreviewInEditor] [2025/08/28 15:29:09] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366149717","is_fighting":0}}
2025-8-28 15:29:09 - log: [PreviewInEditor] [2025/08/28 15:29:09] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:09 - log: [PreviewInEditor] [2025/08/28 15:29:09] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:09 - log: [PreviewInEditor] [2025/08/28 15:29:09] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":21,"body":{"heartbeat":{}}}
2025-8-28 15:29:09 - log: [PreviewInEditor] [2025/08/28 15:29:09] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:12 - log: [PreviewInEditor] [2025/08/28 15:29:12] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366152718","is_fighting":0}}
2025-8-28 15:29:12 - log: [PreviewInEditor] [2025/08/28 15:29:12] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:12 - log: [PreviewInEditor] [2025/08/28 15:29:12] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:12 - log: [PreviewInEditor] [2025/08/28 15:29:12] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":23,"body":{"heartbeat":{}}}
2025-8-28 15:29:12 - log: [PreviewInEditor] [2025/08/28 15:29:12] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:15 - log: [PreviewInEditor] [2025/08/28 15:29:15] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366155719","is_fighting":0}}
2025-8-28 15:29:15 - log: [PreviewInEditor] [2025/08/28 15:29:15] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:15 - log: [PreviewInEditor] [2025/08/28 15:29:15] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:15 - log: [PreviewInEditor] [2025/08/28 15:29:15] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":25,"body":{"heartbeat":{}}}
2025-8-28 15:29:15 - log: [PreviewInEditor] [2025/08/28 15:29:15] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:18 - log: [PreviewInEditor] [2025/08/28 15:29:18] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366158734","is_fighting":0}}
2025-8-28 15:29:18 - log: [PreviewInEditor] [2025/08/28 15:29:18] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:18 - log: [PreviewInEditor] [2025/08/28 15:29:18] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:18 - log: [PreviewInEditor] [2025/08/28 15:29:18] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":27,"body":{"heartbeat":{}}}
2025-8-28 15:29:18 - log: [PreviewInEditor] [2025/08/28 15:29:18] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:21 - log: [PreviewInEditor] [2025/08/28 15:29:21] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366161736","is_fighting":0}}
2025-8-28 15:29:21 - log: [PreviewInEditor] [2025/08/28 15:29:21] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:21 - log: [PreviewInEditor] [2025/08/28 15:29:21] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:21 - log: [PreviewInEditor] [2025/08/28 15:29:21] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":29,"body":{"heartbeat":{}}}
2025-8-28 15:29:21 - log: [PreviewInEditor] [2025/08/28 15:29:21] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:24 - log: [PreviewInEditor] [2025/08/28 15:29:24] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366164736","is_fighting":0}}
2025-8-28 15:29:24 - log: [PreviewInEditor] [2025/08/28 15:29:24] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:24 - log: [PreviewInEditor] [2025/08/28 15:29:24] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:24 - log: [PreviewInEditor] [2025/08/28 15:29:24] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":31,"body":{"heartbeat":{}}}
2025-8-28 15:29:24 - log: [PreviewInEditor] [2025/08/28 15:29:24] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:27 - log: [PreviewInEditor] [2025/08/28 15:29:27] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366167752","is_fighting":0}}
2025-8-28 15:29:27 - log: [PreviewInEditor] [2025/08/28 15:29:27] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:27 - log: [PreviewInEditor] [2025/08/28 15:29:27] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:27 - log: [PreviewInEditor] [2025/08/28 15:29:27] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":33,"body":{"heartbeat":{}}}
2025-8-28 15:29:27 - log: [PreviewInEditor] [2025/08/28 15:29:27] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:28 - warn: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/plane/PlaneUIError: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/plane/PlaneUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.closeUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5e/5e1cf596f6b1c93eea960bb66596523aeb25389e.js:413:21)
    at BattleUI.onBattleClick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d7/d76c4c3e5817bb91737df46b7182d1489f90970e.js:267:31)
2025-8-28 15:29:28 - warn: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/TalentUIError: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/TalentUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.closeUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5e/5e1cf596f6b1c93eea960bb66596523aeb25389e.js:413:21)
    at BattleUI.onBattleClick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d7/d76c4c3e5817bb91737df46b7182d1489f90970e.js:272:31)
2025-8-28 15:29:28 - warn: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/ShopUIError: [PreviewInEditor] [UIManager] closeUI uiMap not found ui/main/ShopUI
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at UIManager.closeUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5e/5e1cf596f6b1c93eea960bb66596523aeb25389e.js:413:21)
    at BattleUI.onBattleClick (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d7/d76c4c3e5817bb91737df46b7182d1489f90970e.js:277:31)
2025-8-28 15:29:28 - log: [PreviewInEditor] [2025/08/28 15:29:28] [INFO] NetMgr Unregistered handler for msgId: 53
2025-8-28 15:29:28 - log: [PreviewInEditor] [2025/08/28 15:29:28] [INFO] NetMgr Removed empty handler array for msgId: 53
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  _levelList  [
  90002, 90001, 90001,
  90001, 90002, 90005,
  90001, 90001, 90001,
  90001, 90001, 90005
]
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: -1 remainingLevels: 6
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  ----- this._levelLoadIndex: 0
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _lastLevelHeight 0 offsetY 0
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 Game/level/1
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  开始加载到关卡: 1 PRELOAD_STATE: 2 this._levelLoadIndex: 0
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: 0 remainingLevels: 5
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun  ----- this._levelLoadIndex: 1
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 Game/level/1
2025-8-28 15:29:28 - log: [PreviewInEditor] GameMapRun 关卡预加载完成
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _lastLevelHeight 3840 offsetY -4.344000000357628
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:29:28 - log: [PreviewInEditor] LevelLayerUI trigger log test log
2025-8-28 15:29:28 - warn: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99Error: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at 20831 (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:14:399)
    at m (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:57:489)
    at null.<anonymous> (wasm://wasm/000c506e:1:144019)
    at null.<anonymous> (wasm://wasm/000c506e:1:144157)
    at Function.eval [as createSpineSkeletonDataWithJson] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:31:49)
    at SkeletonData.getRuntimeData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:63993:52)
    at Skeleton._updateSkeletonData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:65046:44)
    at Skeleton.__preload (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:64934:16)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50951:171)
    at UnsortedInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at UnsortedInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:50940:16)
    at NodeActivator.activateNode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:51013:28)
    at Node._onHierarchyChangedBase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55237:46)
    at Node._onHierarchyChanged (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:55803:16)
    at Node.setParent (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:54325:16)
    at Node.set parent [as parent] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:54069:16)
    at BattleLayer.addMainPlane (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c4/c44b6f331d6851c1582071213ac895c4c7fd45e7.js:101:74)
    at MainPlaneManager.createMainPlane (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/fc/fc412a59b35c503f84168865fbed99e2916398db.js:187:64)
    at MainPlaneManager.preload (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/fc/fc412a59b35c503f84168865fbed99e2916398db.js:147:11)
2025-8-28 15:29:28 - log: [PreviewInEditor] getBullet 215
2025-8-28 15:29:28 - log: [PreviewInEditor] getBullet 214
2025-8-28 15:29:28 - log: [PreviewInEditor] getBullet 216
2025-8-28 15:29:29 - warn: [PreviewInEditor] stage 1 1Error: [PreviewInEditor] stage 1 1
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at EnemyManager.initBattle (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/1a/1a92c234f2404e83e3978d8d055d6530a05db75b.js:260:19)
    at BattleManager.initBattle (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:216:48)
    at BattleManager.checkLoadFinish (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:152:18)
    at file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/0c/0cb86716f51aa13682cf462516236d9754ebfc99.js:110:51
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:117467:9)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:151169:9)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:29:29 - warn: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99Error: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at 20831 (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:14:399)
    at m (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:57:489)
    at null.<anonymous> (wasm://wasm/000c506e:1:144019)
    at null.<anonymous> (wasm://wasm/000c506e:1:144157)
    at Function.eval [as createSpineSkeletonDataWithJson] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:31:49)
    at SkeletonData.getRuntimeData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:63993:52)
    at Skeleton._updateSkeletonData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:65046:44)
    at Skeleton.set skeletonData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:64653:18)
    at Skeleton.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\asset\asset-watcher.ccc:1:1091)
    at MainPlane.addFireAnim (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/3f/3f1fb1b0ed938bdce99b77de634e782c06fd0829.js:376:35)
    at MainPlane.initPlane (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/3f/3f1fb1b0ed938bdce99b77de634e782c06fd0829.js:289:11)
2025-8-28 15:29:30 - log: [PreviewInEditor] [2025/08/28 15:29:30] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366170763","is_fighting":0}}
2025-8-28 15:29:30 - log: [PreviewInEditor] [2025/08/28 15:29:30] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:30 - log: [PreviewInEditor] [2025/08/28 15:29:30] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:30 - log: [PreviewInEditor] [2025/08/28 15:29:30] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":35,"body":{"heartbeat":{}}}
2025-8-28 15:29:30 - log: [PreviewInEditor] [2025/08/28 15:29:30] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:33 - log: [PreviewInEditor] [2025/08/28 15:29:33] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366173770","is_fighting":0}}
2025-8-28 15:29:33 - log: [PreviewInEditor] [2025/08/28 15:29:33] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:33 - log: [PreviewInEditor] [2025/08/28 15:29:33] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:33 - log: [PreviewInEditor] [2025/08/28 15:29:33] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":37,"body":{"heartbeat":{}}}
2025-8-28 15:29:33 - log: [PreviewInEditor] [2025/08/28 15:29:33] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:34 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:35 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:a
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:b
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:e
2025-8-28 15:29:36 - log: [PreviewInEditor] Can't find letter definition in texture atlas whiteHurtNum.png for letter:l
2025-8-28 15:29:36 - log: [PreviewInEditor] [2025/08/28 15:29:36] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366176781","is_fighting":0}}
2025-8-28 15:29:36 - log: [PreviewInEditor] [2025/08/28 15:29:36] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:36 - log: [PreviewInEditor] [2025/08/28 15:29:36] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:36 - log: [PreviewInEditor] [2025/08/28 15:29:36] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":39,"body":{"heartbeat":{}}}
2025-8-28 15:29:36 - log: [PreviewInEditor] [2025/08/28 15:29:36] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:37 - log: [PreviewInEditor] getBullet 3008
2025-8-28 15:29:37 - log: [PreviewInEditor] getBullet 3009
2025-8-28 15:29:39 - warn: [PreviewInEditor] Boss stage 100 5Error: [PreviewInEditor] Boss stage 100 5
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at WaveManager._updateCurAction (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:227:29)
    at WaveManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:178:16)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:290:49)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/cb/cb88689fcc0481e95cacffd77ca87b46c40b70e8.js:153:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:29:39 - warn: [PreviewInEditor] Loading Boss Resources 100 5Error: [PreviewInEditor] Loading Boss Resources 100 5
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at DYTools.warn (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/5f/5f40ea786cecb9774c3a2f41d853e7d51ba064dc.js:51:19)
    at BossManager.loadBossRes (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/80/801f3855dadb7927fbe58a803c46364f0079289b.js:252:33)
    at WaveManager._updateCurAction (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:231:57)
    at WaveManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:178:16)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:290:49)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/cb/cb88689fcc0481e95cacffd77ca87b46c40b70e8.js:153:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:29:39 - error: [PreviewInEditor] enemy overError: [PreviewInEditor] enemy over
    at console.error (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at error (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:146879:12)
    at WaveManager._updateCurAction (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:196:15)
    at WaveManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:178:16)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:290:49)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/cb/cb88689fcc0481e95cacffd77ca87b46c40b70e8.js:153:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:29:39 - log: [PreviewInEditor] [2025/08/28 15:29:39] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366179786","is_fighting":0}}
2025-8-28 15:29:39 - log: [PreviewInEditor] [2025/08/28 15:29:39] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:39 - log: [PreviewInEditor] [2025/08/28 15:29:39] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:39 - log: [PreviewInEditor] [2025/08/28 15:29:39] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":41,"body":{"heartbeat":{}}}
2025-8-28 15:29:39 - log: [PreviewInEditor] [2025/08/28 15:29:39] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:42 - log: [PreviewInEditor] [2025/08/28 15:29:42] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366182802","is_fighting":0}}
2025-8-28 15:29:42 - log: [PreviewInEditor] [2025/08/28 15:29:42] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:42 - log: [PreviewInEditor] [2025/08/28 15:29:42] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:42 - log: [PreviewInEditor] [2025/08/28 15:29:42] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":43,"body":{"heartbeat":{}}}
2025-8-28 15:29:42 - log: [PreviewInEditor] [2025/08/28 15:29:42] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:45 - log: [PreviewInEditor] [2025/08/28 15:29:45] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366185818","is_fighting":0}}
2025-8-28 15:29:45 - log: [PreviewInEditor] [2025/08/28 15:29:45] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:45 - log: [PreviewInEditor] [2025/08/28 15:29:45] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:45 - log: [PreviewInEditor] [2025/08/28 15:29:45] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":45,"body":{"heartbeat":{}}}
2025-8-28 15:29:45 - log: [PreviewInEditor] [2025/08/28 15:29:45] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:47 - warn: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99Error: [PreviewInEditor] [Spine] Skeleton version 3.6.53 does not match runtime version 3.8.99
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at 20831 (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:14:399)
    at m (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:57:489)
    at null.<anonymous> (wasm://wasm/000c506e:1:144019)
    at null.<anonymous> (wasm://wasm/000c506e:1:144157)
    at Function.eval [as createSpineSkeletonDataWithJson] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native\external\emscripten\spine\3.8\spine.wasm.js:31:49)
    at SkeletonData.getRuntimeData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:63993:52)
    at Skeleton._updateSkeletonData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:65046:44)
    at Skeleton.set skeletonData (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:64653:18)
    at Skeleton.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\asset\asset-watcher.ccc:1:1091)
    at BossUnit._initUI (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/1c/1c9b50f204ca398abc6070f3ea138dda5f0bd096.js:199:41)
    at BossUnit.init (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/1c/1c9b50f204ca398abc6070f3ea138dda5f0bd096.js:138:16)
    at BossEntity._initUnits (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/09/0931db329f80459385e2c21cbe97f595594665a3.js:814:20)
    at BossEntity.setFormIndex (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/09/0931db329f80459385e2c21cbe97f595594665a3.js:204:20)
    at BossEntity.init (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/09/0931db329f80459385e2c21cbe97f595594665a3.js:176:16)
    at BossManager._createBoss (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/80/801f3855dadb7927fbe58a803c46364f0079289b.js:510:16)
    at BossManager.addBoss (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/80/801f3855dadb7927fbe58a803c46364f0079289b.js:361:27)
    at WaveManager._updateBoss (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:416:51)
    at WaveManager.updateGameLogic (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/8a/8a93a9f1be11d84a9ade43fb7a4ea28f602ee82f.js:182:16)
    at BattleManager.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/c5/c55363f6e63c2e18f13afb50d619a053371a4d21.js:290:49)
    at GameMain.update (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/cb/cb88689fcc0481e95cacffd77ca87b46c40b70e8.js:153:53)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49057:29)
    at ReusableInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at ReusableInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49035:16)
    at ComponentScheduler.updatePhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49248:30)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19509:35)
    at Game._updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:20564:22)
    at updateCallback (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:78359:20)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:29:48 - log: [PreviewInEditor] [2025/08/28 15:29:48] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366188819","is_fighting":0}}
2025-8-28 15:29:48 - log: [PreviewInEditor] [2025/08/28 15:29:48] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:48 - log: [PreviewInEditor] [2025/08/28 15:29:48] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:48 - log: [PreviewInEditor] [2025/08/28 15:29:48] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":47,"body":{"heartbeat":{}}}
2025-8-28 15:29:48 - log: [PreviewInEditor] [2025/08/28 15:29:48] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:51 - log: [PreviewInEditor] [2025/08/28 15:29:51] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366191819","is_fighting":0}}
2025-8-28 15:29:51 - log: [PreviewInEditor] [2025/08/28 15:29:51] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:51 - log: [PreviewInEditor] [2025/08/28 15:29:51] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:51 - log: [PreviewInEditor] [2025/08/28 15:29:51] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":49,"body":{"heartbeat":{}}}
2025-8-28 15:29:51 - log: [PreviewInEditor] [2025/08/28 15:29:51] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:52 - log: [PreviewInEditor] getBullet 1072
2025-8-28 15:29:52 - log: [PreviewInEditor] getBullet 1073
2025-8-28 15:29:52 - log: [PreviewInEditor] getBullet 1074
2025-8-28 15:29:52 - log: [PreviewInEditor] getBullet 1075
2025-8-28 15:29:54 - log: [PreviewInEditor] [2025/08/28 15:29:54] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366194819","is_fighting":0}}
2025-8-28 15:29:54 - log: [PreviewInEditor] [2025/08/28 15:29:54] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:54 - log: [PreviewInEditor] [2025/08/28 15:29:54] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:54 - log: [PreviewInEditor] [2025/08/28 15:29:54] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":51,"body":{"heartbeat":{}}}
2025-8-28 15:29:54 - log: [PreviewInEditor] [2025/08/28 15:29:54] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:57 - log: [PreviewInEditor] [2025/08/28 15:29:57] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366197819","is_fighting":0}}
2025-8-28 15:29:57 - log: [PreviewInEditor] [2025/08/28 15:29:57] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:29:57 - log: [PreviewInEditor] [2025/08/28 15:29:57] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:29:57 - log: [PreviewInEditor] [2025/08/28 15:29:57] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":53,"body":{"heartbeat":{}}}
2025-8-28 15:29:57 - log: [PreviewInEditor] [2025/08/28 15:29:57] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:29:57 - log: [PreviewInEditor] getBullet 1077
2025-8-28 15:30:00 - log: [PreviewInEditor] getBullet 1070
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun 关卡完成: 0 levelDistance: 3841.2240000003612 levelTotalHeight: 3840
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun  开始加载到关卡: 2 PRELOAD_STATE: 2 this._levelLoadIndex: 1
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun  levelCount: 6 this._levelLoadIndex: 1 remainingLevels: 4
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun  ----- this._levelLoadIndex: 2
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun 加载关卡: 90001 Game/level/1
2025-8-28 15:30:00 - log: [PreviewInEditor] GameMapRun 关卡预加载完成
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelBaseUI _initBackgroundLayer _lastLevelHeight 3840 offsetY -1.2240000003626226
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelLayerUI  initByLevelData
2025-8-28 15:30:00 - log: [PreviewInEditor] LevelLayerUI trigger log test log
2025-8-28 15:30:00 - log: [PreviewInEditor] [2025/08/28 15:30:00] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366200820","is_fighting":0}}
2025-8-28 15:30:00 - log: [PreviewInEditor] [2025/08/28 15:30:00] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:30:00 - log: [PreviewInEditor] [2025/08/28 15:30:00] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:30:00 - log: [PreviewInEditor] [2025/08/28 15:30:00] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":55,"body":{"heartbeat":{}}}
2025-8-28 15:30:00 - log: [PreviewInEditor] [2025/08/28 15:30:00] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:30:03 - log: [PreviewInEditor] [2025/08/28 15:30:03] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366203837","is_fighting":0}}
2025-8-28 15:30:03 - log: [PreviewInEditor] [2025/08/28 15:30:03] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:30:03 - log: [PreviewInEditor] [2025/08/28 15:30:03] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:30:03 - log: [PreviewInEditor] [2025/08/28 15:30:03] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":57,"body":{"heartbeat":{}}}
2025-8-28 15:30:03 - log: [PreviewInEditor] [2025/08/28 15:30:03] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:30:06 - log: [PreviewInEditor] [2025/08/28 15:30:06] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366206853","is_fighting":0}}
2025-8-28 15:30:06 - log: [PreviewInEditor] [2025/08/28 15:30:06] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:30:06 - log: [PreviewInEditor] [2025/08/28 15:30:06] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:30:06 - log: [PreviewInEditor] [2025/08/28 15:30:06] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":59,"body":{"heartbeat":{}}}
2025-8-28 15:30:06 - log: [PreviewInEditor] [2025/08/28 15:30:06] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:30:09 - log: [PreviewInEditor] [2025/08/28 15:30:09] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366209854","is_fighting":0}}
2025-8-28 15:30:09 - log: [PreviewInEditor] [2025/08/28 15:30:09] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:30:09 - log: [PreviewInEditor] [2025/08/28 15:30:09] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:30:09 - log: [PreviewInEditor] [2025/08/28 15:30:09] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":61,"body":{"heartbeat":{}}}
2025-8-28 15:30:09 - log: [PreviewInEditor] [2025/08/28 15:30:09] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:30:12 - log: [PreviewInEditor] [2025/08/28 15:30:12] [DEBUG] NetMgr sendMessage 11 {"heartbeat":{"clent_time":"1756366212870","is_fighting":0}}
2025-8-28 15:30:12 - log: [PreviewInEditor] [2025/08/28 15:30:12] [DEBUG] NetMgr Sent message 11, size: 17
2025-8-28 15:30:12 - log: [PreviewInEditor] [2025/08/28 15:30:12] [DEBUG] NetMgr WebSocket message received [object MessageEvent]
2025-8-28 15:30:12 - log: [PreviewInEditor] [2025/08/28 15:30:12] [DEBUG] NetMgr Received message {"cmd":"CS_CMD_HEARTBEAT","seq":63,"body":{"heartbeat":{}}}
2025-8-28 15:30:12 - log: [PreviewInEditor] [2025/08/28 15:30:12] [DEBUG] NetMgr onHeartbeat [object Object]
2025-8-28 15:30:13 - log: [PreviewInEditor] [2025/08/28 15:30:13] [INFO] NetMgr Registered handler for msgId: 53
2025-8-28 15:30:16 - info: [PreviewInEditor] preview process is ready
2025-8-28 15:30:24 - warn: [Scene] Missing class: EventGroupDataError: [Scene] Missing class: EventGroupData
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Object.classFinder (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:982)
    at _Deserializer.classFinder [as _classFinder] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:111535:34)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46686:30)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46807:38)
    at _Deserializer._deserializeArray (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46668:40)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46646:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46819:36)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46408:15)
    at _Deserializer._deserializeFireClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46790:11)
    at _Deserializer._deserializeInto (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46732:18)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46704:18)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46807:38)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46408:15)
    at _Deserializer._deserializeFireClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46790:11)
    at _Deserializer._deserializeInto (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46732:18)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46709:20)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46807:38)
    at _Deserializer._deserializeArray (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46668:40)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46646:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46819:36)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46408:15)
    at _Deserializer._deserializeFireClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46790:11)
    at _Deserializer._deserializeInto (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46732:18)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46704:18)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46807:38)
    at _Deserializer._deserializeArray (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46668:40)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46646:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46819:36)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46408:15)
    at _Deserializer._deserializeFireClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46790:11)
    at _Deserializer._deserializeInto (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46732:18)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46704:18)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46807:38)
    at _Deserializer._deserializeArray (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46668:40)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46646:29)
    at _Deserializer._deserializeAndAssignField (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46819:36)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46408:15)
    at _Deserializer._deserializeFireClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46790:11)
    at _Deserializer._deserializeInto (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46732:18)
    at _Deserializer._deserializeTypeTaggedObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46704:18)
    at _Deserializer._deserializeObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:46640:29)
2025-8-28 15:30:24 - error: [Scene] Class "EventGroupData" used by component "Emitter" is missing or invalid. Detailed information:
Node path: "Canvas/Emitter"
Error: [Scene] Class "EventGroupData" used by component "Emitter" is missing or invalid. Detailed information:
Node path: "Canvas/Emitter"

    at console.error (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at report (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:25)
    at reportByWalker (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:133)
    at C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:746
    at ObjectWalkerBehavior.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:2292)
    at ObjectWalkerBehavior.forEach (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:790)
    at ObjectWalkerBehavior.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:298)
    at doWalkProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:2086)
    at ObjectWalker.iteratee (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:2229)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1664)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.forEach (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:790)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:298)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.forEach (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:790)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:298)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.forEach (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:790)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:298)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at ObjectWalker.walk (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1771)
    at ObjectWalker.parseCCClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:615)
    at ObjectWalker.parseObject (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:428)
    at new ObjectWalker (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:1321)
    at Object.walkProperties (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\object-walker.ccc:1:2158)
    at MissingClassReporter.reportByOwner (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:658)
    at Object.reportMissingClass (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\engine\dist\editor-extends\missing-reporter\missing-class-reporter.ccc:7:1262)
    at deserializeAsset (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:111559:20)
    at parseImport (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115393:22)
    at Parser.parse (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115476:11)
    at parse (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115201:20)
    at Pipeline._flow (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115759:11)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115769:22)
    at Array.eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:115141:11)
2025-8-28 15:30:24 - warn: [Scene] 没有找到子弹父节点请检查路径:Canvas/GameUI/bullet_rootError: [Scene] 没有找到子弹父节点请检查路径:Canvas/GameUI/bullet_root
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.onCreateEmitter (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/dd/dd521e5474d7c0ad57a2bafcebbd7ba1d6db92dd.js:102:25)
    at Emitter.start (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/81/81c15cd8deb1b0578cc0cd23f8b485dd4fa7beb9.js:165:45)
    at eval (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49048:16)
    at OneOffInvoker.eval [as _invoke] (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48877:9)
    at OneOffInvoker.invoke (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:48991:16)
    at ComponentScheduler.startPhase (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:49222:29)
    at Director.tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\bin\.cache\dev\editor\bundled\index.js:19507:35)
    at EngineManager.tickInEditMode (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\engine\index.ccc:1:3044)
    at EngineManager._tick (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\engine\index.ccc:1:4087)
    at sentryWrapped (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\src\helpers.ts:100:17)
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:24 - log: [Scene] lost focus in editor  false
2025-8-28 15:30:31 - log: [Scene] lost focus in editor  true
