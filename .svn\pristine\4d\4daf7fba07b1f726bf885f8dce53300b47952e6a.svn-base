import { _decorator, NodePool, instantiate, Prefab, Node} from 'cc';
import { GameIns } from '../GameIns';
import { SingletonBase } from '../../core/base/SingletonBase';
import { MyApp } from '../../MyApp';

const { ccclass } = _decorator;

@ccclass('PrefabManager')
export default class PrefabManager extends SingletonBase<PrefabManager> {

    m_pool = new Map(); // 存储所有预制体对象池


    /**
     * 预加载指定的预制体
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Promise<void>}
     */
    async preload(prefabClass) {
        if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
        }
        await MyApp.resMgr.preloadAsync(prefabClass.PrefabName);
    }

    /**
     * 创建预制体实例
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Node} 预制体实例
     */
    async _create(prefabClass) {
        var node:Node = null
        if (prefabClass || prefabClass.PrefabName) {
            const prefab = await MyApp.resMgr.loadAsync("Game/prefabs/"+prefabClass.PrefabName, Prefab);
            if (prefab == null) {
                console.log(`create prefab[${prefabClass.PrefabName}] but prefab is null`);
            } else {
                node = instantiate(prefab);
            }
        }
        return node;
    }

    /**
     * 异步创建预制体实例
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Promise<Node>} 预制体实例
     */
    async create(prefabClass) {
        return this._create(prefabClass);
    }

    /**
     * 同步创建预制体实例
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Node} 预制体实例
     */
    createSync(prefabClass) {
        return this._create(prefabClass);
    }

    /**
     * 异步创建预制体组件
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Promise<Component>} 预制体组件
     */
    async createComponent(prefabClass) {
        const node:Node = await this.create(prefabClass);
        return node.getComponent(prefabClass);
    }

    /**
     * 创建指定数量的预制体实例（逐帧创建）
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @param {number} count 需要创建的数量
     * @param {Array} result 存储创建结果的数组
     * @param {number} batch 每帧创建的数量
     * @returns {Promise<void>}
     */
    async createFrame(prefabClass, count, result, batch = 10) {
        for (let i = 0; i < batch; i++) {
            const node = await this._create(prefabClass);
            if (!node) {
                return;
            }
            result.push(node.getComponent(prefabClass));
            if (result.length >= count) {
                return;
            }
        }
        if (result.length < count) {
            setTimeout(() => {
                this.createFrame(prefabClass, count, result, batch);
            }, 0);
        }
    }

    /**
     * 创建指定数量的预制体实例（通过名称逐帧创建）
     * @param {string} prefabName 预制体名称
     * @param {number} count 需要创建的数量
     * @param {Array} result 存储创建结果的数组
     * @param {number} batch 每帧创建的数量
     * @returns {Promise<void>}
     */
    async createFrameByName(prefabName, count, result, batch = 10) {
        for (let i = 0; i < batch; i++) {
            const node = await this.create({ PrefabName: prefabName });
            if (!node) {
                return;
            }
            result.push(node);
            if (result.length >= count) {
                return;
            }
        }
        if (result.length < count) {
            setTimeout(() => {
                this.createFrameByName(prefabName, count, result, batch);
            }, 0);
        }
    }

    /**
     * 获取对象池中的预制体实例
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @returns {Component} 预制体组件
     */
    get(prefabClass) {
        if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
        }
        const pool = this.m_pool.get(prefabClass.PrefabName);
        const node = pool && pool.size() > 0 ? pool.get() : this._create(prefabClass);
        return node.getComponent(prefabClass);
    }

    /**
     * 创建对象池
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @param {number} size 对象池的初始大小
     * @returns {Promise<void>}
     */
    async createPool(prefabClass, size = 1) {
        await this.preload(prefabClass);
        if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
        }
        const pool = new NodePool();
        this.m_pool.set(prefabClass.PrefabName, pool);
        for (let i = 0; i < size; i++) {
            const node = await this._create(prefabClass);
            pool.put(node);
        }
    }

    /**
     * 添加预制体实例到对象池
     * @param {Object} prefabClass 包含 PrefabName 的类
     */
    add(prefabClass) {
        if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
        }
        const pool = this.m_pool.get(prefabClass.PrefabName);
        if (!pool) {
            return null;
        }
        const node = this._create(prefabClass);
        pool.put(node);
    }

    /**
     * 将预制体实例放回对象池
     * @param {Object} prefabClass 包含 PrefabName 的类
     * @param {Node} node 需要放回的节点
     */
    put(prefabClass, node) {
        if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
        }
        const pool = this.m_pool.get(prefabClass.PrefabName);
        if (!pool) {
            return null;
        }
        pool.put(node);
    }
}