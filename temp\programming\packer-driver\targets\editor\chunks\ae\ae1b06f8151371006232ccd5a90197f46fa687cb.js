System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, JsonAsset, CCBoolean, CCString, assetManager, log, LevelData, LevelEditorBaseUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, executeInEditMode, LevelEditorUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorBaseUI(extras) {
    _reporterNs.report("LevelEditorBaseUI", "./LevelEditorBaseUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      JsonAsset = _cc.JsonAsset;
      CCBoolean = _cc.CCBoolean;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      log = _cc.log;
    }, function (_unresolved_2) {
      LevelData = _unresolved_2.LevelData;
    }, function (_unresolved_3) {
      LevelEditorBaseUI = _unresolved_3.LevelEditorBaseUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "68a25Vb5mhGApMaV59XFjQ0", "LevelEditorUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'Asset', 'resources', 'assetManager', 'AssetManager', 'Canvas', 'log', 'math']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelEditorUI", LevelEditorUI = (_dec = ccclass('LevelEditorUI'), _dec2 = executeInEditMode(), _dec3 = property({
        type: CCFloat,
        range: [0, 1, 0.01],
        slide: true,
        visible: true
      }), _dec4 = property({
        type: JsonAsset
      }), _dec5 = property(CCString), _dec6 = property(CCBoolean), _dec7 = property({
        group: {
          name: "new"
        },
        type: CCString,
        displayName: "Name"
      }), _dec8 = property({
        group: {
          name: "new"
        },
        type: CCBoolean
      }), _dec(_class = _dec2(_class = (_class2 = class LevelEditorUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "progress", _descriptor, this);

          _initializerDefineProperty(this, "label", _descriptor2, this);

          this._levelPrefab = null;

          _initializerDefineProperty(this, "newName", _descriptor3, this);

          this.baseCom = null;
          this._levelElapsedTime = 0;
        }

        set levelPrefab(value) {
          var _this$_levelPrefab;

          if (((_this$_levelPrefab = this._levelPrefab) == null ? void 0 : _this$_levelPrefab.uuid) != (value == null ? void 0 : value.uuid)) {
            this._levelPrefab = value;
            var levelData = (_crd && LevelData === void 0 ? (_reportPossibleCrUseOfLevelData({
              error: Error()
            }), LevelData) : LevelData).fromJSON(value == null ? void 0 : value.json);
            this.node.getComponent(_crd && LevelEditorBaseUI === void 0 ? (_reportPossibleCrUseOfLevelEditorBaseUI({
              error: Error()
            }), LevelEditorBaseUI) : LevelEditorBaseUI).initByLevelData(levelData);
          }
        }

        get levelPrefab() {
          return this._levelPrefab;
        }

        set levelPrefabUUID(value) {
          log("LevelEditorUI set levelPrefabUUID", value);

          if (value == null || value == "") {
            this.levelPrefab = null;
            return;
          }

          assetManager.loadAny({
            uuid: value
          }, (err, asset) => {
            if (err) {
              log("LevelEditor set levelPrefabUUID but load err:", err);
              return;
            }

            this.levelPrefab = asset;
          });
        }

        get levelPrefabUUID() {
          var _this$_levelPrefab2;

          return (_this$_levelPrefab2 = this._levelPrefab) == null ? void 0 : _this$_levelPrefab2.uuid;
        }

        get save() {
          return false;
        }

        set save(value) {
          log("LevelEditorUI save");

          if (!value) {
            return;
          }

          if (this._levelPrefab == null) {
            return;
          }

          log(`json:[${JSON.stringify(this._levelPrefab.json)}]`);
          var data = this.genLevelData(); //@ts-ignore

          Editor.Message.send('asset-db', 'save-asset', this._levelPrefab.uuid, JSON.stringify(data, null, 2));
          log("LevelEditorUI save success");
        }

        get new() {
          return false;
        }

        set new(value) {
          if (value) {
            log(`Level new ${this.newName}`);
          }

          const filePath = `db://assets/resources/Game/level/${this.newName}.json`;
          var levelData = new (_crd && LevelData === void 0 ? (_reportPossibleCrUseOfLevelData({
            error: Error()
          }), LevelData) : LevelData)();
          const jsonString = JSON.stringify(levelData, null, 2); //@ts-ignore

          var p = Editor.Message.request('asset-db', 'create-asset', filePath, jsonString);
          p.then(res => {
            assetManager.loadAny({
              uuid: res.uuid
            }, (err, asset) => {
              if (err) {
                log(err);
              } else {
                this.levelPrefab = asset;
              }
            });
          });
        }

        get levelElapsedTime() {
          return this._levelElapsedTime;
        }

        start() {
          log("LevelUI start");
          this.baseCom = this.node.getComponent(_crd && LevelEditorBaseUI === void 0 ? (_reportPossibleCrUseOfLevelEditorBaseUI({
            error: Error()
          }), LevelEditorBaseUI) : LevelEditorBaseUI);
          this.levelPrefab = null;
        }

        checkLevelPrebab() {
          if (this._levelPrefab == null) {
            return;
          }
        }

        update(deltaTime) {
          this.tick();
        }

        tick() {
          if (this._levelPrefab == null) {
            return;
          }

          this.baseCom.tick(this.progress);
        }

        genLevelData() {
          var data = new (_crd && LevelData === void 0 ? (_reportPossibleCrUseOfLevelData({
            error: Error()
          }), LevelData) : LevelData)();
          this.node.getComponent(_crd && LevelEditorBaseUI === void 0 ? (_reportPossibleCrUseOfLevelEditorBaseUI({
            error: Error()
          }), LevelEditorBaseUI) : LevelEditorBaseUI).fillLevelData(data);
          return data;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "progress", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "label", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "levelPrefab", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "levelPrefab"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "levelPrefabUUID", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "levelPrefabUUID"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "save", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "save"), _class2.prototype), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "newName", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "new", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "new"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ae1b06f8151371006232ccd5a90197f46fa687cb.js.map