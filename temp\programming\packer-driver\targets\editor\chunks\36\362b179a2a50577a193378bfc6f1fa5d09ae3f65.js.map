{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts"], "names": ["_decorator", "BaseScreen", "GameIns", "BossEntity", "ccclass", "property", "CircleScreen", "constructor", "config", "mainEntity", "follow", "m_follow", "props", "setData", "params", "m_config", "para", "bulletNum", "beginAngle", "endAngle", "radius", "posOffset", "offset", "update", "deltaTime", "onInit", "fire", "target", "m_enemy", "mainPlaneManager", "mainPlane", "planeManager", "enemyTarget", "isDead", "isDamageable", "attackPoint", "getAttackPoint", "bustyle", "angle", "x", "y", "Math", "sin", "PI", "cos", "bullet", "createBullet", "init", "m_bulletState", "m_mainEntity", "angleStep", "i", "bulletX", "bulletY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,U;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,U;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,Y,WADpBF,OAAO,CAAC,cAAD,C,gBAAR,MACqBE,YADrB;AAAA;AAAA,oCACqD;AAGjD;AACJ;AACA;AACA;AACA;AACA;AACIC,QAAAA,WAAW,CAACC,MAAD,EAAcC,UAAd,EAA+BC,MAAe,GAAG,KAAjD,EAAwD;AAC/D;AAD+D,eAR3DC,QAQ2D,GARvC,KAQuC;AAAA,eAPnEC,KAOmE;AAE/D,eAAKD,QAAL,GAAgBD,MAAhB;AACA,eAAKG,OAAL,CAAaL,MAAb,EAAqBC,UAArB;AAEA,gBAAMK,MAAM,GAAG,KAAKC,QAAL,CAAcC,IAA7B;AACA,cAAIC,SAAS,GAAGH,MAAM,CAAC,CAAD,CAAtB;AACA,gBAAMI,UAAU,GAAGJ,MAAM,CAAC,CAAD,CAAzB;AACA,gBAAMK,QAAQ,GAAGL,MAAM,CAAC,CAAD,CAAvB;AACA,gBAAMM,MAAM,GAAGN,MAAM,CAAC,CAAD,CAAN,IAAa,CAA5B;AACA,gBAAMO,SAAS,GAAG,KAAKN,QAAL,CAAcO,MAAhC;AAEA,eAAKV,KAAL,GAAa;AACTK,YAAAA,SADS;AAETC,YAAAA,UAFS;AAGTC,YAAAA,QAHS;AAITC,YAAAA,MAJS;AAKTC,YAAAA;AALS,WAAb;AAOH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,MAAM,CAACC,SAAD,EAA0B,CAC5B;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,MAAM,GAAS,CACX;AACH;AAED;AACJ;AACA;;;AACc,cAAJC,IAAI,GAAkB;AACxB,cAAI,KAAKf,QAAT,EAAmB;AACf,gBAAIgB,MAAM,GAAG,IAAb;;AACA,gBAAI,KAAKC,OAAT,EAAkB;AACdD,cAAAA,MAAM,GAAG;AAAA;AAAA,sCAAQE,gBAAR,CAAyBC,SAAlC;AACH,aAFD,MAEO,IAAI;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,WAAzB,EAAsC;AACzCL,cAAAA,MAAM,GAAG;AAAA;AAAA,sCAAQI,YAAR,CAAqBC,WAA9B;AACH;;AAED,gBAAI,CAACL,MAAL,EAAa;AACb,gBAAIA,MAAM;AAAA;AAAA,yCAAN,KAAiCA,MAAM,CAACM,MAAP,IAAiB,CAACN,MAAM,CAACO,YAAP,EAAnD,CAAJ,EAA+E;AAClF;;AAED,gBAAMC,WAAW,GAAG,KAAKC,cAAL,EAApB;AACA,gBAAMnB,SAAS,GAAG,KAAKL,KAAL,CAAWK,SAA7B;;AAEA,cAAIA,SAAS,KAAK,CAAd,IAAmB,KAAKF,QAAL,CAAcsB,OAAd,KAA0B,EAAjD,EAAqD;AACjD;AACA,kBAAMC,KAAK,GAAG,CAAC,KAAK1B,KAAL,CAAWO,QAAX,GAAsB,KAAKP,KAAL,CAAWM,UAAlC,IAAgD,CAAhD,GAAoD,EAAlE;AACA,gBAAIqB,CAAC,GAAGJ,WAAW,CAACI,CAApB;AACA,gBAAIC,CAAC,GAAGL,WAAW,CAACK,CAApB;;AAEA,gBAAI,KAAK5B,KAAL,CAAWQ,MAAX,KAAsB,CAA1B,EAA6B;AACzBmB,cAAAA,CAAC,IAAIE,IAAI,CAACC,GAAL,CAAUJ,KAAK,GAAG,GAAT,GAAgBG,IAAI,CAACE,EAA9B,IAAoC,KAAK/B,KAAL,CAAWQ,MAApD;AACAoB,cAAAA,CAAC,IAAIC,IAAI,CAACG,GAAL,CAAUN,KAAK,GAAG,GAAT,GAAgBG,IAAI,CAACE,EAA9B,IAAoC,KAAK/B,KAAL,CAAWQ,MAApD;AACH;;AAED,kBAAMyB,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;;AACA,gBAAID,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACE,IAAP,CACI,KAAKnB,OADT,EAEI;AAAEW,gBAAAA,CAAF;AAAKC,gBAAAA,CAAL;AAAQF,gBAAAA;AAAR,eAFJ,EAGI,KAAKU,aAHT,EAII,KAAKC,YAJT;AAMH;AACJ,WApBD,MAoBO;AACH;AACA,kBAAMC,SAAS,GAAG,CAAC,KAAKtC,KAAL,CAAWO,QAAX,GAAsB,KAAKP,KAAL,CAAWM,UAAlC,KAAiDD,SAAS,GAAG,CAA7D,CAAlB;AACA,kBAAMsB,CAAC,GAAGJ,WAAW,CAACI,CAAtB;AACA,kBAAMC,CAAC,GAAGL,WAAW,CAACK,CAAtB;;AAEA,gBAAI,KAAK5B,KAAL,CAAWQ,MAAX,KAAsB,CAA1B,EAA6B;AACzB;AACA,mBAAK,IAAI+B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlC,SAApB,EAA+BkC,CAAC,EAAhC,EAAoC;AAChC,sBAAMb,KAAK,GAAG,KAAK1B,KAAL,CAAWM,UAAX,GAAwBgC,SAAS,GAAGC,CAApC,GAAwC,EAAtD;AACA,sBAAMN,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;;AACA,oBAAID,MAAJ,EAAY;AACRA,kBAAAA,MAAM,CAACE,IAAP,CACI,KAAKnB,OADT,EAEI;AAAEW,oBAAAA,CAAF;AAAKC,oBAAAA,CAAL;AAAQF,oBAAAA;AAAR,mBAFJ,EAGI,KAAKU,aAHT,EAII,KAAKC,YAJT;AAMH;AACJ;AACJ,aAdD,MAcO;AACH;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlC,SAApB,EAA+BkC,CAAC,EAAhC,EAAoC;AAChC,sBAAMb,KAAK,GAAG,KAAK1B,KAAL,CAAWM,UAAX,GAAwBgC,SAAS,GAAGC,CAApC,GAAwC,EAAtD;AACA,sBAAMN,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;AACA,sBAAMM,OAAO,GAAGb,CAAC,GAAGE,IAAI,CAACC,GAAL,CAAUJ,KAAK,GAAG,GAAT,GAAgBG,IAAI,CAACE,EAA9B,IAAoC,KAAK/B,KAAL,CAAWQ,MAAnE;AACA,sBAAMiC,OAAO,GAAGb,CAAC,GAAGC,IAAI,CAACG,GAAL,CAAUN,KAAK,GAAG,GAAT,GAAgBG,IAAI,CAACE,EAA9B,IAAoC,KAAK/B,KAAL,CAAWQ,MAAnE;;AAEA,oBAAIyB,MAAJ,EAAY;AACRA,kBAAAA,MAAM,CAACE,IAAP,CACI,KAAKnB,OADT,EAEI;AAAEW,oBAAAA,CAAC,EAAEa,OAAL;AAAcZ,oBAAAA,CAAC,EAAEa,OAAjB;AAA0Bf,oBAAAA;AAA1B,mBAFJ,EAGI,KAAKU,aAHT,EAII,KAAKC,YAJT;AAMH;AACJ;AACJ;AACJ;AACJ;;AA3HgD,O", "sourcesContent": ["import { _decorator } from \"cc\";\r\nimport BaseScreen from \"./BaseScreen\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport BossEntity from \"../plane/boss/BossEntity\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"CircleScreen\")\r\nexport default class CircleScreen extends BaseScreen {\r\n    private m_follow: boolean = false;\r\n    props;\r\n    /**\r\n     * 构造函数\r\n     * @param config 配置数据\r\n     * @param mainEntity 主实体\r\n     * @param follow 是否跟随\r\n     */\r\n    constructor(config: any, mainEntity: any, follow: boolean = false) {\r\n        super();\r\n        this.m_follow = follow;\r\n        this.setData(config, mainEntity);\r\n\r\n        const params = this.m_config.para;\r\n        let bulletNum = params[0];\r\n        const beginAngle = params[1];\r\n        const endAngle = params[2];\r\n        const radius = params[3] || 0;\r\n        const posOffset = this.m_config.offset;\r\n\r\n        this.props = {\r\n            bulletNum,\r\n            beginAngle,\r\n            endAngle,\r\n            radius,\r\n            posOffset,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        // 可根据需求实现更新逻辑\r\n    }\r\n\r\n    /**\r\n     * 初始化逻辑\r\n     */\r\n    onInit(): void {\r\n        // 可根据需求实现初始化逻辑\r\n    }\r\n\r\n    /**\r\n     * 发射子弹\r\n     */\r\n    async fire(): Promise<void> {\r\n        if (this.m_follow) {\r\n            let target = null;\r\n            if (this.m_enemy) {\r\n                target = GameIns.mainPlaneManager.mainPlane;\r\n            } else if (GameIns.planeManager.enemyTarget) {\r\n                target = GameIns.planeManager.enemyTarget;\r\n            }\r\n\r\n            if (!target) return;\r\n            if (target instanceof BossEntity && (target.isDead || !target.isDamageable())) return;\r\n        }\r\n\r\n        const attackPoint = this.getAttackPoint();\r\n        const bulletNum = this.props.bulletNum;\r\n\r\n        if (bulletNum === 1 && this.m_config.bustyle !== 37) {\r\n            // 单发子弹逻辑\r\n            const angle = (this.props.endAngle + this.props.beginAngle) / 2 + 90;\r\n            let x = attackPoint.x;\r\n            let y = attackPoint.y;\r\n\r\n            if (this.props.radius !== 0) {\r\n                x += Math.sin((angle / 180) * Math.PI) * this.props.radius;\r\n                y += Math.cos((angle / 180) * Math.PI) * this.props.radius;\r\n            }\r\n\r\n            const bullet = await this.createBullet();\r\n            if (bullet) {\r\n                bullet.init(\r\n                    this.m_enemy,\r\n                    { x, y, angle },\r\n                    this.m_bulletState,\r\n                    this.m_mainEntity\r\n                );\r\n            }\r\n        } else {\r\n            // 多发子弹逻辑\r\n            const angleStep = (this.props.endAngle - this.props.beginAngle) / (bulletNum - 1);\r\n            const x = attackPoint.x;\r\n            const y = attackPoint.y;\r\n\r\n            if (this.props.radius === 0) {\r\n                // 没有半径，子弹从同一点发射\r\n                for (let i = 0; i < bulletNum; i++) {\r\n                    const angle = this.props.beginAngle + angleStep * i - 90;\r\n                    const bullet = await this.createBullet();\r\n                    if (bullet) {\r\n                        bullet.init(\r\n                            this.m_enemy,\r\n                            { x, y, angle },\r\n                            this.m_bulletState,\r\n                            this.m_mainEntity\r\n                        );\r\n                    }\r\n                }\r\n            } else {\r\n                // 有半径，子弹沿圆周发射\r\n                for (let i = 0; i < bulletNum; i++) {\r\n                    const angle = this.props.beginAngle + angleStep * i + 90;\r\n                    const bullet = await this.createBullet();\r\n                    const bulletX = x + Math.sin((angle / 180) * Math.PI) * this.props.radius;\r\n                    const bulletY = y + Math.cos((angle / 180) * Math.PI) * this.props.radius;\r\n\r\n                    if (bullet) {\r\n                        bullet.init(\r\n                            this.m_enemy,\r\n                            { x: bulletX, y: bulletY, angle },\r\n                            this.m_bulletState,\r\n                            this.m_mainEntity\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}"]}