{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts"], "names": ["_decorator", "misc", "Sprite", "tween", "v2", "Node", "NodePool", "UITransform", "EnemyAttrBaseCom", "Tools", "GameEnum", "GameIns", "GameConst", "ccclass", "property", "EnemyAttrDoctorCom", "radius", "count", "bloodTime", "freeTime", "blood", "time", "state", "enemyArr", "lightNode", "lightPool", "lightList", "actLightList", "attrData", "init", "params", "stringToNumber", "param", "reset", "initUI", "_resetSkill", "setState", "stopAllActions", "active", "to", "scale", "ActionFrameTime", "union", "repeatF<PERSON><PERSON>", "start", "updateGameLogic", "deltaTime", "i", "length", "enemy", "distance", "node", "position", "sub", "Math", "abs", "x", "y", "isDead", "isFullBlood", "removeBuff", "Enemy<PERSON>uff", "<PERSON><PERSON><PERSON>", "splice", "light", "arr<PERSON><PERSON><PERSON>", "put", "checkEnemy", "opacity", "direction", "angle", "radiansToDegrees", "signAngle", "mag", "scaleY", "height", "arrC<PERSON>ain", "changeHp", "getMaxHp", "setSiblingIndex", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "enemyManager", "setPlaneFrame", "size", "die"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAGC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;;AACjEC,MAAAA,gB;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,Q;;AAEEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAGTe,kB,WADpBF,OAAO,CAAC,oBAAD,C,gBAAR,MACqBE,kBADrB;AAAA;AAAA,gDACiE;AAAA;AAAA;AAAA,eAE7DC,MAF6D,GAEpD,CAFoD;AAEjD;AAFiD,eAG7DC,KAH6D,GAGrD,CAHqD;AAGlD;AAHkD,eAI7DC,SAJ6D,GAIjD,CAJiD;AAI9C;AAJ8C,eAK7DC,QAL6D,GAKlD,CALkD;AAK/C;AAL+C,eAM7DC,KAN6D,GAMrD,CANqD;AAMlD;AANkD,eAO7DC,IAP6D,GAOtD,CAPsD;AAOnD;AAPmD,eAQ7DC,KAR6D,GAQrD,CARqD;AAQlD;AARkD,eAS7DC,QAT6D,GASlD,EATkD;AAS9C;AAT8C,eAU7DC,SAV6D,GAUjD,IAViD;AAU3C;AAV2C,eAW7DC,SAX6D,GAWjD,IAXiD;AAW3C;AAX2C,eAY7DC,SAZ6D,GAYjD,EAZiD;AAY7C;AAZ6C,eAa7DC,YAb6D,GAa9C,EAb8C;AAa1C;AAb0C,eAe7DC,QAf6D,GAelD,IAfkD;AAAA;;AAe5C;;AAGjB;AACJ;AACA;AACA;AACIC,QAAAA,IAAI,CAACD,QAAD,EAAW;AACX,eAAKA,QAAL,GAAgBA,QAAhB;AACA,gBAAME,MAAM,GAAG;AAAA;AAAA,8BAAMC,cAAN,CAAqBH,QAAQ,CAACI,KAA9B,EAAqC,GAArC,CAAf;AACA,eAAKhB,MAAL,GAAcc,MAAM,CAAC,CAAD,CAApB;AACA,eAAKb,KAAL,GAAaa,MAAM,CAAC,CAAD,CAAnB;AACA,eAAKZ,SAAL,GAAiBY,MAAM,CAAC,CAAD,CAAvB;AACA,eAAKX,QAAL,GAAgBW,MAAM,CAAC,CAAD,CAAtB;AACA,eAAKV,KAAL,GAAaU,MAAM,CAAC,CAAD,CAAN,GAAY,GAAzB;AACA,eAAKG,KAAL;AACA,eAAKC,MAAL;AACH;AAED;AACJ;AACA;;;AACID,QAAAA,KAAK,GAAG;AACJ,eAAKE,WAAL;;AACA,eAAKC,QAAL,CAAc,CAAd;AACH;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,QAAQ,CAACd,KAAD,EAAQ;AACZ,eAAKA,KAAL,GAAaA,KAAb;AACA,eAAKD,IAAL,GAAY,CAAZ;;AAEA,cAAIC,KAAK,KAAK,CAAd,EAAiB;AACb,gBAAI,KAAKE,SAAT,EAAoB;AAChB,mBAAKA,SAAL,CAAea,cAAf;AACA,mBAAKb,SAAL,CAAec,MAAf,GAAwB,IAAxB;AACAnC,cAAAA,KAAK,CAAC,KAAKqB,SAAN,CAAL,CACKe,EADL,CACQ,CADR,EACW;AAAEC,gBAAAA,KAAK,EAAE;AAAT,eADX,EAEKD,EAFL,CAEQ,IAAI;AAAA;AAAA,0CAAUE,eAFtB,EAEuC;AAAED,gBAAAA,KAAK,EAAE;AAAT,eAFvC,EAGKD,EAHL,CAGQ,IAAI;AAAA;AAAA,0CAAUE,eAHtB,EAGuC;AAAED,gBAAAA,KAAK,EAAE;AAAT,eAHvC,EAIKE,KAJL,GAKKC,aALL,GAMKC,KANL;AAOH;AACJ,WAZD,MAYO,IAAI,KAAKpB,SAAL,IAAkB,KAAKA,SAAL,CAAec,MAArC,EAA6C;AAChD,iBAAKd,SAAL,CAAea,cAAf;AACA,iBAAKb,SAAL,CAAec,MAAf,GAAwB,KAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,eAAe,CAACC,SAAD,EAAY;AACvB,kBAAQ,KAAKxB,KAAb;AACI,iBAAK,CAAL;AAAQ;AACJ,mBAAKD,IAAL,IAAayB,SAAb;;AACA,kBAAI,KAAKzB,IAAL,GAAY,KAAKF,QAArB,EAA+B;AAC3B,qBAAKiB,QAAL,CAAc,CAAd;AACH;;AACD;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,kBAAI,CAAC,KAAKZ,SAAL,CAAec,MAApB,EAA4B;AACxB,qBAAKd,SAAL,CAAec,MAAf,GAAwB,IAAxB;AACH;;AACD,mBAAKjB,IAAL,IAAayB,SAAb,CAJJ,CAMI;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxB,QAAL,CAAcyB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC3C,sBAAME,KAAK,GAAG,KAAK1B,QAAL,CAAcwB,CAAd,CAAd;AACA,sBAAMG,QAAQ,GAAGD,KAAK,CAACE,IAAN,CAAWC,QAAX,CAAoBC,GAApB,CAAwB,KAAKF,IAAL,CAAUC,QAAlC,CAAjB;;AACA,oBACIE,IAAI,CAACC,GAAL,CAASL,QAAQ,CAACM,CAAlB,IAAuB,KAAKxC,MAA5B,IACAsC,IAAI,CAACC,GAAL,CAASL,QAAQ,CAACO,CAAlB,IAAuB,KAAKzC,MAD5B,IAEAiC,KAAK,CAACS,MAFN,IAGAT,KAAK,CAACU,WAAN,EAJJ,EAKE;AACEV,kBAAAA,KAAK,CAACW,UAAN,CAAiB;AAAA;AAAA,4CAASC,SAAT,CAAmBC,KAApC;AACA,uBAAKvC,QAAL,CAAcwC,MAAd,CAAqBhB,CAArB,EAAwB,CAAxB;AACA,wBAAMiB,KAAK,GAAG,KAAKtC,SAAL,CAAeqB,CAAf,CAAd;AACA;AAAA;AAAA,sCAAMkB,SAAN,CAAgB,KAAKtC,YAArB,EAAmCqC,KAAnC;AACA,uBAAKvC,SAAL,CAAeyC,GAAf,CAAmBF,KAAnB;AACA,uBAAKtC,SAAL,CAAeqC,MAAf,CAAsBhB,CAAtB,EAAyB,CAAzB;AACAA,kBAAAA,CAAC;AACJ;AACJ,eAxBL,CA0BI;;;AACA,kBAAI,KAAKxB,QAAL,CAAcyB,MAAd,GAAuB,KAAK/B,KAAhC,EAAuC;AACnC,qBAAKkD,UAAL;AACH,eA7BL,CA+BI;;;AACA,mBAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxB,QAAL,CAAcyB,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC3C,sBAAME,KAAK,GAAG,KAAK1B,QAAL,CAAcwB,CAAd,CAAd;AACA,sBAAMiB,KAAK,GAAG,KAAKtC,SAAL,CAAeqB,CAAf,CAAd;;AACA,oBAAIE,KAAK,IAAIe,KAAb,EAAoB;AAChBA,kBAAAA,KAAK,CAACI,OAAN,GAAgB,GAAhB;AACA,wBAAMC,SAAS,GAAGpB,KAAK,CAACE,IAAN,CAAWC,QAAX,CAAoBC,GAApB,CAAwB,KAAKF,IAAL,CAAUC,QAAlC,CAAlB;AACA,wBAAMkB,KAAK,GAAGrE,IAAI,CAACsE,gBAAL,CAAsBnE,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL,CAAF,CAAUoE,SAAV,CAAoBH,SAApB,CAAtB,CAAd;AACA,wBAAMnB,QAAQ,GAAGmB,SAAS,CAACI,GAAV,EAAjB;AACAT,kBAAAA,KAAK,CAACM,KAAN,GAAcA,KAAd;AACA,wBAAMI,MAAM,GAAGxB,QAAQ,GAAGc,KAAK,CAACW,MAAhC;;AACA,sBAAI;AAAA;AAAA,sCAAMC,UAAN,CAAiB,KAAKjD,YAAtB,EAAoCqC,KAApC,CAAJ,EAAgD;AAC5CA,oBAAAA,KAAK,CAACU,MAAN,IAAgB,GAAhB;;AACA,wBAAIV,KAAK,CAACU,MAAN,GAAeA,MAAnB,EAA2B;AACvBV,sBAAAA,KAAK,CAACU,MAAN,GAAeA,MAAf;AACA;AAAA;AAAA,0CAAMT,SAAN,CAAgB,KAAKtC,YAArB,EAAmCqC,KAAnC;AACH;AACJ,mBAND,MAMO;AACHA,oBAAAA,KAAK,CAACU,MAAN,GAAeA,MAAf;AACAzB,oBAAAA,KAAK,CAAC4B,QAAN,CAAe/B,SAAS,GAAG,KAAK1B,KAAjB,GAAyB6B,KAAK,CAAC6B,QAAN,EAAxC;AACH;AACJ;AACJ,eArDL,CAuDI;;;AACA,kBAAI,KAAKzD,IAAL,GAAY,KAAKH,SAArB,EAAgC;AAC5B,qBAAK,MAAM+B,KAAX,IAAoB,KAAK1B,QAAzB,EAAmC;AAC/B0B,kBAAAA,KAAK,CAACW,UAAN,CAAiB;AAAA;AAAA,4CAASC,SAAT,CAAmBC,KAApC;AACH;;AACD,qBAAKvC,QAAL,GAAgB,EAAhB;;AACA,qBAAK,MAAMyC,KAAX,IAAoB,KAAKtC,SAAzB,EAAoC;AAChC,uBAAKD,SAAL,CAAeyC,GAAf,CAAmBF,KAAnB;AACH;;AACD,qBAAKrC,YAAL,GAAoB,EAApB;AACA,qBAAKD,SAAL,GAAiB,EAAjB;AACA,qBAAKU,QAAL,CAAc,CAAd;AACH;;AACD;;AAEJ,iBAAK,CAAL;AAAQ;AACJ,mBAAK+B,UAAL;;AACA,kBAAI,KAAK5C,QAAL,CAAcyB,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,qBAAKZ,QAAL,CAAc,CAAd;AACH;;AACD;AAnFR;AAqFH;AAED;AACJ;AACA;;;AACI+B,QAAAA,UAAU,GAAG,CACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIjC,QAAAA,MAAM,GAAG;AACL,eAAKiB,IAAL,CAAU4B,eAAV,CAA0B,CAA1B;;AAEA,cAAI,CAAC,KAAKvD,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,IAAInB,IAAJ,EAAjB;AACA,iBAAKmB,SAAL,CAAewD,YAAf,CAA4BzE,WAA5B;AACA,iBAAK4C,IAAL,CAAU8B,QAAV,CAAmB,KAAKzD,SAAxB;AACA;AAAA;AAAA,oCAAQ0D,YAAR,CAAqBC,aAArB,CAAmC,KAAK3D,SAAL,CAAewD,YAAf,CAA4B9E,MAA5B,CAAnC,EAAwE,iBAAxE;AACA,iBAAKsB,SAAL,CAAec,MAAf,GAAwB,KAAxB;AACH;;AAED,cAAI,CAAC,KAAKb,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,IAAInB,QAAJ,EAAjB;AACH;;AAED,eAAK,IAAIyC,CAAC,GAAG,KAAKtB,SAAL,CAAe2D,IAAf,EAAb,EAAoCrC,CAAC,GAAG,KAAK9B,KAA7C,EAAoD8B,CAAC,EAArD,EAAyD;AACrD,kBAAMiB,KAAK,GAAG,IAAI3D,IAAJ,EAAd;AACA2D,YAAAA,KAAK,CAACgB,YAAN,CAAmBzE,WAAnB,EAFqD,CAGrD;;AACAyD,YAAAA,KAAK,CAACe,eAAN,CAAsB,CAAtB,EAJqD,CAKrD;;AACA;AAAA;AAAA,oCAAQG,YAAR,CAAqBC,aAArB,CAAmCnB,KAAK,CAACgB,YAAN,CAAmB9E,MAAnB,CAAnC,EAA+D,eAA/D;AACA,iBAAKuB,SAAL,CAAeyC,GAAf,CAAmBF,KAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACI7B,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKX,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAea,cAAf;AACA,iBAAKb,SAAL,CAAec,MAAf,GAAwB,KAAxB;AACH;;AAED,eAAK,MAAM0B,KAAX,IAAoB,KAAKtC,SAAzB,EAAoC;AAChCsC,YAAAA,KAAK,CAAC3B,cAAN;AACA,iBAAKZ,SAAL,CAAeyC,GAAf,CAAmBF,KAAnB;AACH;;AAED,eAAKrC,YAAL,GAAoB,EAApB;AACA,eAAKD,SAAL,GAAiB,EAAjB;AACA,eAAKH,QAAL,GAAgB,EAAhB;AACH;AAED;AACJ;AACA;;;AACI8D,QAAAA,GAAG,GAAG;AACF,eAAK,MAAMpC,KAAX,IAAoB,KAAK1B,QAAzB,EAAmC;AAC/B0B,YAAAA,KAAK,CAACW,UAAN,CAAiB;AAAA;AAAA,sCAASC,SAAT,CAAmBC,KAApC;AACH;;AACD,eAAK3B,WAAL;AACH;;AAjP4D,O", "sourcesContent": ["import { _decorator, Component, misc, Sprite, tween, v2,Node, NodePool, UITransform } from 'cc';\r\nimport EnemyAttrBaseCom from './EnemyAttrBaseCom';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport { GameFunc } from '../../../GameFunc';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyAttrDoctorCom')\r\nexport default class EnemyAttrDoctorCom extends EnemyAttrBaseCom {\r\n\r\n    radius = 0; // 医生技能的作用半径\r\n    count = 0; // 同时治疗的敌人数量\r\n    bloodTime = 0; // 治疗持续时间\r\n    freeTime = 0; // 空闲时间\r\n    blood = 0; // 每秒治疗的血量百分比\r\n    time = 0; // 当前状态持续时间\r\n    state = 0; // 当前状态\r\n    enemyArr = []; // 当前正在治疗的敌人数组\r\n    lightNode = null; // 医生技能的光圈节点\r\n    lightPool = null; // 光线节点池\r\n    lightList = []; // 当前光线节点数组\r\n    actLightList = []; // 当前正在激活的光线节点数组\r\n\r\n    attrData = null; // 属性数据\r\n\r\n\r\n    /**\r\n     * 初始化医生属性组件\r\n     * @param {Object} attrData 属性数据\r\n     */\r\n    init(attrData) {\r\n        this.attrData = attrData;\r\n        const params = Tools.stringToNumber(attrData.param, ',');\r\n        this.radius = params[0];\r\n        this.count = params[1];\r\n        this.bloodTime = params[2];\r\n        this.freeTime = params[3];\r\n        this.blood = params[4] / 100;\r\n        this.reset();\r\n        this.initUI();\r\n    }\r\n\r\n    /**\r\n     * 重置医生技能状态\r\n     */\r\n    reset() {\r\n        this._resetSkill();\r\n        this.setState(0);\r\n    }\r\n\r\n    /**\r\n     * 设置医生技能的状态\r\n     * @param {number} state 状态值\r\n     */\r\n    setState(state) {\r\n        this.state = state;\r\n        this.time = 0;\r\n\r\n        if (state === 1) {\r\n            if (this.lightNode) {\r\n                this.lightNode.stopAllActions();\r\n                this.lightNode.active = true;\r\n                tween(this.lightNode)\r\n                    .to(0, { scale: 1.15 })\r\n                    .to(4 * GameConst.ActionFrameTime, { scale: 1 })\r\n                    .to(6 * GameConst.ActionFrameTime, { scale: 1.15 })\r\n                    .union()\r\n                    .repeatForever()\r\n                    .start();\r\n            }\r\n        } else if (this.lightNode && this.lightNode.active) {\r\n            this.lightNode.stopAllActions();\r\n            this.lightNode.active = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime) {\r\n        switch (this.state) {\r\n            case 0: // 空闲状态\r\n                this.time += deltaTime;\r\n                if (this.time > this.freeTime) {\r\n                    this.setState(2);\r\n                }\r\n                break;\r\n\r\n            case 1: // 治疗状态\r\n                if (!this.lightNode.active) {\r\n                    this.lightNode.active = true;\r\n                }\r\n                this.time += deltaTime;\r\n\r\n                // 检查治疗中的敌人\r\n                for (let i = 0; i < this.enemyArr.length; i++) {\r\n                    const enemy = this.enemyArr[i];\r\n                    const distance = enemy.node.position.sub(this.node.position);\r\n                    if (\r\n                        Math.abs(distance.x) > this.radius ||\r\n                        Math.abs(distance.y) > this.radius ||\r\n                        enemy.isDead ||\r\n                        enemy.isFullBlood()\r\n                    ) {\r\n                        enemy.removeBuff(GameEnum.EnemyBuff.Treat);\r\n                        this.enemyArr.splice(i, 1);\r\n                        const light = this.lightList[i];\r\n                        Tools.arrRemove(this.actLightList, light);\r\n                        this.lightPool.put(light);\r\n                        this.lightList.splice(i, 1);\r\n                        i--;\r\n                    }\r\n                }\r\n\r\n                // 检查是否需要治疗新的敌人\r\n                if (this.enemyArr.length < this.count) {\r\n                    this.checkEnemy();\r\n                }\r\n\r\n                // 更新光线和治疗效果\r\n                for (let i = 0; i < this.enemyArr.length; i++) {\r\n                    const enemy = this.enemyArr[i];\r\n                    const light = this.lightList[i];\r\n                    if (enemy && light) {\r\n                        light.opacity = 255;\r\n                        const direction = enemy.node.position.sub(this.node.position);\r\n                        const angle = misc.radiansToDegrees(v2(0, -1).signAngle(direction));\r\n                        const distance = direction.mag();\r\n                        light.angle = angle;\r\n                        const scaleY = distance / light.height;\r\n                        if (Tools.arrContain(this.actLightList, light)) {\r\n                            light.scaleY += 0.4;\r\n                            if (light.scaleY > scaleY) {\r\n                                light.scaleY = scaleY;\r\n                                Tools.arrRemove(this.actLightList, light);\r\n                            }\r\n                        } else {\r\n                            light.scaleY = scaleY;\r\n                            enemy.changeHp(deltaTime * this.blood * enemy.getMaxHp());\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // 检查治疗时间是否结束\r\n                if (this.time > this.bloodTime) {\r\n                    for (const enemy of this.enemyArr) {\r\n                        enemy.removeBuff(GameEnum.EnemyBuff.Treat);\r\n                    }\r\n                    this.enemyArr = [];\r\n                    for (const light of this.lightList) {\r\n                        this.lightPool.put(light);\r\n                    }\r\n                    this.actLightList = [];\r\n                    this.lightList = [];\r\n                    this.setState(0);\r\n                }\r\n                break;\r\n\r\n            case 2: // 检查敌人状态\r\n                this.checkEnemy();\r\n                if (this.enemyArr.length > 0) {\r\n                    this.setState(1);\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查范围内的敌人并开始治疗\r\n     */\r\n    checkEnemy() {\r\n        // const enemies = GameFunc.getRangeEnemys(this.node.position, this.radius);\r\n        // for (const enemy of enemies) {\r\n        //     if (\r\n        //         enemy.node !== this.node &&\r\n        //         !enemy.isDead &&\r\n        //         !enemy.isFullBlood() &&\r\n        //         this.enemyArr.length < this.count &&\r\n        //         !Tools.arrContain(this.enemyArr, enemy)\r\n        //     ) {\r\n        //         enemy.addBuff(GameEnum.EnemyBuff.Treat, [this.bloodTime]);\r\n        //         this.enemyArr.push(enemy);\r\n        //         const light = this.lightPool.get();\r\n        //         this.node.addChild(light);\r\n        //         light.opacity = 0;\r\n        //         light.scaleY = 0;\r\n        //         this.lightList.push(light);\r\n        //         this.actLightList.push(light);\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 初始化 UI\r\n     */\r\n    initUI() {\r\n        this.node.setSiblingIndex(0);\r\n\r\n        if (!this.lightNode) {\r\n            this.lightNode = new Node();\r\n            this.lightNode.addComponent(UITransform);\r\n            this.node.addChild(this.lightNode);\r\n            GameIns.enemyManager.setPlaneFrame(this.lightNode.addComponent(Sprite), 'a_doctor_circle');\r\n            this.lightNode.active = false;\r\n        }\r\n\r\n        if (!this.lightPool) {\r\n            this.lightPool = new NodePool();\r\n        }\r\n\r\n        for (let i = this.lightPool.size(); i < this.count; i++) {\r\n            const light = new Node();\r\n            light.addComponent(UITransform);\r\n            // light.anchorY = 1;\r\n            light.setSiblingIndex(0);\r\n            // light.opacity = 0;\r\n            GameIns.enemyManager.setPlaneFrame(light.addComponent(Sprite), 'a_doctor_line');\r\n            this.lightPool.put(light);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 重置技能状态\r\n     */\r\n    _resetSkill() {\r\n        if (this.lightNode) {\r\n            this.lightNode.stopAllActions();\r\n            this.lightNode.active = false;\r\n        }\r\n\r\n        for (const light of this.lightList) {\r\n            light.stopAllActions();\r\n            this.lightPool.put(light);\r\n        }\r\n\r\n        this.actLightList = [];\r\n        this.lightList = [];\r\n        this.enemyArr = [];\r\n    }\r\n\r\n    /**\r\n     * 处理医生死亡逻辑\r\n     */\r\n    die() {\r\n        for (const enemy of this.enemyArr) {\r\n            enemy.removeBuff(GameEnum.EnemyBuff.Treat);\r\n        }\r\n        this._resetSkill();\r\n    }\r\n}"]}