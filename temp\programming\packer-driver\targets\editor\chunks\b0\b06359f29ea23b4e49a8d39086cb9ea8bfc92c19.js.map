{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts"], "names": ["newCondition", "obj", "_type", "DelayTime", "Object", "assign", "comb", "DelayDistance", "Wave", "LevelDataEventCondtionType", "LevelDataEventCondtionDelayDistance", "LevelDataEventCondtionDelayTime", "LevelDataEventCondtionWave"], "mappings": ";;;;;AAKO,WAASA,YAAT,CAAsBC,GAAtB,EAA0H;AACzH,YAAOA,GAAG,CAACC,KAAX;AACI,WAAK;AAAA;AAAA,oEAA2BC,SAAhC;AACI,eAAOC,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,gFAAoCJ,GAAG,CAACK,IAAxC,CAAd,EAA6DL,GAA7D,CAAP;;AACJ,WAAK;AAAA;AAAA,oEAA2BM,aAAhC;AACI,eAAOH,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,wFAAwCJ,GAAG,CAACK,IAA5C,CAAd,EAAiEL,GAAjE,CAAP;;AACJ,WAAK;AAAA;AAAA,oEAA2BO,IAAhC;AACI,eAAOJ,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,sEAA+BJ,GAAG,CAACK,IAAnC,CAAd,EAAwDL,GAAxD,CAAP;AANR;AAQH;;;;;;;;;;;;;;;;;;;;;;;;;;0BATWD,Y;;;;;;;;AAL6CS,MAAAA,0B,iBAAAA,0B;;AACpDC,MAAAA,mC,iBAAAA,mC;;AACAC,MAAAA,+B,iBAAAA,+B;;AACAC,MAAAA,0B,iBAAAA,0B", "sourcesContent": ["import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from \"./LevelDataEventCondtion\";\r\nimport { LevelDataEventCondtionDelayDistance } from \"./LevelDataEventCondtionDelayDistance\";\r\nimport { LevelDataEventCondtionDelayTime } from \"./LevelDataEventCondtionDelayTime\";\r\nimport { LevelDataEventCondtionWave } from \"./LevelDataEventCondtionWave\";\r\n\r\nexport function newCondition(obj: {_type: LevelDataEventCondtionType, comb: LevelDataEventCondtionComb}): LevelDataEventCondtion {\r\n        switch(obj._type) {\r\n            case LevelDataEventCondtionType.DelayTime:\r\n                return Object.assign(new LevelDataEventCondtionDelayTime(obj.comb), obj)\r\n            case LevelDataEventCondtionType.DelayDistance:\r\n                return Object.assign(new LevelDataEventCondtionDelayDistance(obj.comb), obj)\r\n            case LevelDataEventCondtionType.Wave:\r\n                return Object.assign(new LevelDataEventCondtionWave(obj.comb), obj)\r\n        }\r\n    }"]}