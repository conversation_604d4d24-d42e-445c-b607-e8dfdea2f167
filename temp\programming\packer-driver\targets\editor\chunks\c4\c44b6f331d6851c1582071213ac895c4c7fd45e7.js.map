{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts"], "names": ["_decorator", "Component", "Node", "GameIns", "ccclass", "property", "BattleLayer", "onLoad", "me", "addShadow", "node", "shadow<PERSON>ayer", "<PERSON><PERSON><PERSON><PERSON>", "addBullet", "bullet", "enemy", "parent", "enemyBullet<PERSON>ayer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addBulletNode", "isEnemy", "addCopyPlane", "plane", "selfPlane<PERSON><PERSON><PERSON>", "addMainPlane", "mainPlaneManager", "mainPlane", "addWinePlane", "addFriendPlane", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEnemy", "zIndex", "enemyPlane<PERSON><PERSON>er", "setSiblingIndex", "addMissile", "addMissileWarn", "addLootGold", "lootGold<PERSON>ayer", "addLootBox", "lootBoxLayer"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAEvBC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,UAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,WAGRG,QAAQ,CAACH,IAAD,C,sCAzCb,MACqBI,WADrB,SACyCL,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AA6C/CM,QAAAA,MAAM,GAAG;AACLD,UAAAA,WAAW,CAACE,EAAZ,GAAiB,IAAjB;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAa;AAClB,eAAKC,WAAL,CAAiBC,QAAjB,CAA0BF,IAA1B;AACH;;AAEDG,QAAAA,SAAS,CAACC,MAAD,EAAyC;AAC9C,cAAIA,MAAM,CAACC,KAAX,EAAkB;AACdD,YAAAA,MAAM,CAACJ,IAAP,CAAYM,MAAZ,GAAqB,KAAKC,gBAA1B;AACH,WAFD,MAEO;AACHH,YAAAA,MAAM,CAACJ,IAAP,CAAYM,MAAZ,GAAqB,KAAKE,eAA1B;AACH;AACJ;;AAEDC,QAAAA,aAAa,CAACT,IAAD,EAAaU,OAAgB,GAAG,KAAhC,EAAuC;AAChDV,UAAAA,IAAI,CAACM,MAAL,GAAcI,OAAO,GAAG,KAAKH,gBAAR,GAA2B,KAAKC,eAArD;AACH;;AAEDG,QAAAA,YAAY,CAACC,KAAD,EAAwB;AAChCA,UAAAA,KAAK,CAACZ,IAAN,CAAWM,MAAX,GAAoB,KAAKO,cAAzB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,CAAmChB,IAAnC,CAAwCM,MAAxC,GAAiD,KAAKO,cAAtD;AACH;;AAEDI,QAAAA,YAAY,CAACL,KAAD,EAAwB;AAChCA,UAAAA,KAAK,CAACZ,IAAN,CAAWM,MAAX,GAAoB,KAAKO,cAAzB;AACH;;AAEDK,QAAAA,cAAc,CAACN,KAAD,EAAwB;AAClCA,UAAAA,KAAK,CAACZ,IAAN,CAAWM,MAAX,GAAoB,KAAKa,QAAzB;AACH;;AAEDC,QAAAA,QAAQ,CAACpB,IAAD,EAAaqB,MAAc,GAAG,CAA9B,EAAiC;AACrCrB,UAAAA,IAAI,CAACM,MAAL,GAAc,KAAKgB,eAAnB;AACAtB,UAAAA,IAAI,CAACuB,eAAL,CAAqBF,MAArB;AACH;;AAEDG,QAAAA,UAAU,CAACxB,IAAD,EAAa;AACnBA,UAAAA,IAAI,CAACM,MAAL,GAAc,KAAKC,gBAAnB;AACH;;AAEDkB,QAAAA,cAAc,CAACzB,IAAD,EAAa;AACvBA,UAAAA,IAAI,CAACM,MAAL,GAAc,KAAKgB,eAAnB;AACH;;AAEDI,QAAAA,WAAW,CAAC1B,IAAD,EAAa;AACpBA,UAAAA,IAAI,CAACM,MAAL,GAAc,KAAKqB,aAAnB;AACH;;AAEDC,QAAAA,UAAU,CAAC5B,IAAD,EAAa;AACnBA,UAAAA,IAAI,CAACM,MAAL,GAAc,KAAKuB,YAAnB;AACH;;AApG8C,O,UA2CxC/B,E;;;;;iBAzCa,I;;;;;;;iBAGI,I;;;;;;;iBAGC,I;;;;;;;iBAGA,I;;;;;;;iBAGF,I;;;;;;;iBAGN,I;;;;;;;iBAGO,I;;;;;;;iBAGC,I;;;;;;;iBAGP,I;;;;;;;iBAGI,I;;;;;;;iBAGD,I;;;;;;;iBAGN,I;;;;;;;iBAGI,I;;;;;;;iBAGM,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { PlaneManager } from '../../manager/PlaneManager';\r\nimport { GameIns } from '../../GameIns';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BattleLayer')\r\nexport default class BattleLayer extends Component {\r\n    @property(Node)\r\n    shadowLayer: Node = null;\r\n\r\n    @property(Node)\r\n    enemyPlaneLayer: Node = null;\r\n\r\n    @property(Node)\r\n    enemyBulletLayer: Node = null;\r\n\r\n    @property(Node)\r\n    enemyEffectLayer: Node = null;\r\n\r\n    @property(Node)\r\n    selfPlaneLayer: Node = null;\r\n\r\n    @property(Node)\r\n    dunLayer: Node = null;\r\n\r\n    @property(Node)\r\n    selfBulletLayer: Node = null;\r\n\r\n    @property(Node)\r\n    selfBulletEffect: Node = null;\r\n\r\n    @property(Node)\r\n    lootLayer: Node = null;\r\n\r\n    @property(Node)\r\n    lootGoldLayer: Node = null;\r\n\r\n    @property(Node)\r\n    lootBoxLayer: Node = null;\r\n\r\n    @property(Node)\r\n    streak: Node = null;\r\n\r\n    @property(Node)\r\n    laserPoint: Node = null;\r\n\r\n    @property(Node)\r\n    enemyBulletLight: Node = null;\r\n\r\n    static me: BattleLayer;\r\n\r\n    onLoad() {\r\n        BattleLayer.me = this;\r\n    }\r\n\r\n    addShadow(node: Node) {\r\n        this.shadowLayer.addChild(node);\r\n    }\r\n\r\n    addBullet(bullet: { enemy: boolean; node: Node }) {\r\n        if (bullet.enemy) {\r\n            bullet.node.parent = this.enemyBulletLayer;\r\n        } else {\r\n            bullet.node.parent = this.selfBulletLayer;\r\n        }\r\n    }\r\n\r\n    addBulletNode(node: Node, isEnemy: boolean = false) {\r\n        node.parent = isEnemy ? this.enemyBulletLayer : this.selfBulletLayer;\r\n    }\r\n\r\n    addCopyPlane(plane: { node: Node }) {\r\n        plane.node.parent = this.selfPlaneLayer;\r\n    }\r\n\r\n    addMainPlane() {\r\n        GameIns.mainPlaneManager.mainPlane.node.parent = this.selfPlaneLayer;\r\n    }\r\n\r\n    addWinePlane(plane: { node: Node }) {\r\n        plane.node.parent = this.selfPlaneLayer;\r\n    }\r\n\r\n    addFriendPlane(plane: { node: Node }) {\r\n        plane.node.parent = this.dunLayer;\r\n    }\r\n\r\n    addEnemy(node: Node, zIndex: number = 0) {\r\n        node.parent = this.enemyPlaneLayer;\r\n        node.setSiblingIndex(zIndex);\r\n    }\r\n\r\n    addMissile(node: Node) {\r\n        node.parent = this.enemyBulletLayer;\r\n    }\r\n\r\n    addMissileWarn(node: Node) {\r\n        node.parent = this.enemyPlaneLayer;\r\n    }\r\n\r\n    addLootGold(node: Node) {\r\n        node.parent = this.lootGoldLayer;\r\n    }\r\n\r\n    addLootBox(node: Node) {\r\n        node.parent = this.lootBoxLayer;\r\n    }\r\n}"]}