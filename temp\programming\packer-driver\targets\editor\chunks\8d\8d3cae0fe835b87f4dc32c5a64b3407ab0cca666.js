System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, v2, PolygonCollider2D, FCollider, ColliderType, Intersection, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _crd, ccclass, property, menu, FPolygonCollider;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderType(extras) {
    _reporterNs.report("ColliderType", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIntersection(extras) {
    _reporterNs.report("Intersection", "./Intersection", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      v2 = _cc.v2;
      PolygonCollider2D = _cc.PolygonCollider2D;
    }, function (_unresolved_2) {
      FCollider = _unresolved_2.default;
      ColliderType = _unresolved_2.ColliderType;
    }, function (_unresolved_3) {
      Intersection = _unresolved_3.Intersection;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2d2a3OXiElDrJtde5fuQv+j", "FPolygonCollider", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'v2', 'PolygonCollider2D']);

      ({
        ccclass,
        property,
        menu
      } = _decorator);

      _export("default", FPolygonCollider = (_dec = ccclass('FPolygonCollider'), _dec2 = menu("碰撞组件Ex/FPolygonCollider"), _dec3 = property({
        type: [Vec2]
      }), _dec4 = property({
        type: [Vec2]
      }), _dec(_class = _dec2(_class = (_class2 = class FPolygonCollider extends (_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
        error: Error()
      }), FCollider) : FCollider) {
        constructor(...args) {
          super(...args);
          this.worldPoints = [v2(-100, 0), v2(0, 50), v2(100, 0)];
          this.worldEdge = [];

          _initializerDefineProperty(this, "_points", _descriptor, this);
        }

        get type() {
          return (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Polygon;
        }

        get points() {
          return this._points;
        }

        set points(value) {
          this._points = value;
        }

        initCollider() {
          super.initCollider();
          this.isConvex = !(_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).isConcavePolygon(this.points);
        }

        onLoad() {
          let collider = this.node.getComponent(PolygonCollider2D);

          if (collider) {
            this.points = collider.points.map(p => v2(p.x, p.y));
            this.offset = v2(collider.offset.x, collider.offset.y);
          }
        }

        init(entity, offset = v2(0, 0)) {
          this.initBaseData(entity, offset);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "_points", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [v2(-50, -50), v2(50, -50), v2(50, 50), v2(-50, 50)];
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "points", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "points"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8d3cae0fe835b87f4dc32c5a64b3407ab0cca666.js.map