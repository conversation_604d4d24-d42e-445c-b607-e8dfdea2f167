{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts"], "names": ["_decorator", "Sprite", "game", "size", "UITransform", "GameIns", "Tools", "Entity", "AngleComp", "Bullet<PERSON>ly", "GameFunc", "MainPlane", "GameConst", "CircleZoomFly", "FBoxCollider", "ColliderGroupType", "ccclass", "property", "Bullet", "enemy", "bulletID", "m_collideComp", "isCirt", "m_createTime", "m_lifeTime", "aliveTime", "m_fireTween", "_catapultCount", "_catapultAtkRatio", "_collideEntity", "_catapultTargets", "m_throughArr", "m_config", "m_mainEntity", "bulletState", "create", "bulletManager", "getConfig", "removeAllComp", "angleSpeed", "addComp", "bustyle", "getComponent", "addComponent", "init", "groupType", "BULLET_ENEMY", "BULLET_SELF", "isEnable", "<PERSON><PERSON><PERSON>", "node", "setScale", "scale", "removeChildByName", "skinImg", "parent", "opacity", "spriteFrame", "setImage", "image", "enemyBulletAtlas", "mainBulletAtlas", "error", "isEnemy", "position", "state", "mainEntity", "setPosition", "x", "y", "angle", "retrieve", "totalTime", "clone", "through", "disappear", "m_comps", "for<PERSON>ach", "comp", "bulletFlyComp", "getComp", "setData", "body", "_getAttack", "randomValue", "Math", "random", "crit<PERSON><PERSON>ce", "cirt", "atkChallenge", "attack", "config", "type", "indexOf", "getType", "critMultiplier", "getAttack", "target", "playHurtAudio", "onCollide", "collider", "remove", "exstyle1", "worldPos", "entity", "convertToWorldSpaceAR", "hurtEffectManager", "createHurtEffect", "exstyle2", "onOutScreen", "currentTime", "force", "will<PERSON><PERSON><PERSON>", "removeBullet", "dieRemove", "playBulletDieAnim", "console", "splice", "update", "deltaTime", "GameAble", "updateComp", "refresh", "canCollideEntity", "length", "PrefabName", "audioMaxNum", "hit1", "hit2", "hit3", "hit4", "hit5", "hit6", "swordHit"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAyDC,MAAAA,I,OAAAA,I;AAAmCC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAC9GC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,M;;AACAC,MAAAA,S;;AACAC,MAAAA,S;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,a;;AACAC,MAAAA,Y;;AACaC,MAAAA,iB,kBAAAA,iB;;;;;;;;;OAGd;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;yBAGTkB,M,WADpBF,OAAO,CAAC,QAAD,C,UAIHC,QAAQ,CAAChB,MAAD,C,sCAJb,MACqBiB,MADrB;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA,eAOvCC,KAPuC,GAO/B,KAP+B;AAAA,eAQvCC,QARuC,GAQ5B,CAR4B;AAAA,eASvCC,aATuC;AAAA,eAUvCC,MAVuC,GAU9B,KAV8B;AAAA,eAWvCC,YAXuC,GAWxB,CAXwB;AAAA,eAYvCC,UAZuC,GAY1B,CAZ0B;AAAA,eAavCC,SAbuC,GAa3B,CAb2B;AAAA,eAcvCC,WAduC,GAczB,IAdyB;AAAA,eAevCC,cAfuC,GAetB,CAfsB;AAAA,eAgBvCC,iBAhBuC,GAgBnB,CAhBmB;AAAA,eAiBvCC,cAjBuC,GAiBtB,IAjBsB;AAAA,eAkBvCC,gBAlBuC,GAkBpB,EAlBoB;AAAA,eAmBvCC,YAnBuC,GAmBxB,EAnBwB;AAAA,eAoBvCC,QApBuC;AAAA,eAqBvCC,YArBuC;AAAA,eAsBvCC,WAtBuC;AAAA;;AAyBvC;AACJ;AACA;AACA;AACIC,QAAAA,MAAM,CAACf,QAAD,EAAW;AACb,eAAKA,QAAL,GAAgBA,QAAhB;AACA,eAAKY,QAAL,GAAgB;AAAA;AAAA,kCAAQI,aAAR,CAAsBC,SAAtB,CAAgC,KAAKjB,QAArC,CAAhB;AACA,eAAKkB,aAAL,GAHa,CAKb;;AACA,cAAI,KAAKN,QAAL,CAAcO,UAAd,IAA4B,KAAKP,QAAL,CAAcO,UAAd,KAA6B,CAA7D,EAAgE;AAC5D,iBAAKC,OAAL;AAAA;AAAA,wCAAwB;AAAA;AAAA,wCAAc;AAAED,cAAAA,UAAU,EAAE,KAAKP,QAAL,CAAcO;AAA5B,aAAd,EAAwD,KAAKP,QAAL,CAAcS,OAAtE,CAAxB;AACH;;AACD,kBAAQ,KAAKT,QAAL,CAAcS,OAAtB;AACI,iBAAK,EAAL;AACI,mBAAKD,OAAL;AAAA;AAAA,kDAA4B;AAAA;AAAA,kDAAkB,KAAKR,QAAvB,CAA5B;AACA;;AAEJ;AACI,mBAAKQ,OAAL;AAAA;AAAA,0CAAwB;AAAA;AAAA,0CAAc,KAAKR,QAAnB,CAAxB;AANR,WATa,CAkBb;;;AACA,eAAKX,aAAL,GAAqB,KAAKqB,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAxD;AACA,eAAKtB,aAAL,CAAmBuB,IAAnB,CAAwB,IAAxB,EAA6BzC,IAAI,CAAC,EAAD,EAAI,EAAJ,CAAjC,EApBa,CAoB8B;;AAC3C,eAAKkB,aAAL,CAAmBwB,SAAnB,GAA+B,KAAK1B,KAAL,GAAa;AAAA;AAAA,sDAAkB2B,YAA/B,GAA8C;AAAA;AAAA,sDAAkBC,WAA/F;AACA,eAAK1B,aAAL,CAAmB2B,QAAnB,GAA8B,IAA9B,CAtBa,CAwBb;;AACA,eAAKC,OAAL,GAzBa,CA2Bb;;AACA,eAAKC,IAAL,CAAUC,QAAV,CAAmB,KAAKnB,QAAL,CAAcoB,KAAjC,EAAwC,KAAKpB,QAAL,CAAcoB,KAAtD;AACH;AACD;AACJ;AACA;;;AACiB,cAAPH,OAAO,GAAG;AACZ;AACA;AAAA;AAAA,8BAAMI,iBAAN,CAAwB,KAAKC,OAAL,CAAaJ,IAAb,CAAkBK,MAA1C,EAAkD,MAAlD;AACA;AAAA;AAAA,8BAAMF,iBAAN,CAAwB,KAAKC,OAAL,CAAaJ,IAAb,CAAkBK,MAA1C,EAAkD,QAAlD;AACA;AAAA;AAAA,8BAAMF,iBAAN,CAAwB,KAAKC,OAAL,CAAaJ,IAAb,CAAkBK,MAA1C,EAAkD,MAAlD,EAJY,CAMZ;;AACA,eAAKD,OAAL,CAAaJ,IAAb,CAAkBM,OAAlB,GAA4B,GAA5B,CAPY,CASZ;;AACA,eAAKF,OAAL,CAAaG,WAAb,GAA2B,IAA3B;;AAEA,cAAI,KAAKrC,QAAL,GAAgB,IAApB,EAA0B;AACtB;AACA;AAAA;AAAA,sCAASsC,QAAT,CAAkB,KAAKJ,OAAvB,EAAgC,KAAKtB,QAAL,CAAc2B,KAA9C,EAAqD;AAAA;AAAA,oCAAQvB,aAAR,CAAsBwB,gBAA3E;AACH,WAHD,MAGO;AACH;AACA;AAAA;AAAA,sCAASF,QAAT,CAAkB,KAAKJ,OAAvB,EAAgC,KAAKtB,QAAL,CAAc2B,KAA9C,EAAqD;AAAA;AAAA,oCAAQvB,aAAR,CAAsByB,eAA3E;AACH,WAlBW,CAoBZ;;;AACA,cAAI,CAAC,KAAKP,OAAL,CAAaG,WAAlB,EAA+B;AAC3B;AAAA;AAAA,gCAAMK,KAAN,CAAY,qBAAZ,EAAmC,KAAK9B,QAAL,CAAc2B,KAAjD;AACH;AACJ,SAtFsC,CAuFvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIf,QAAAA,IAAI,CAACmB,OAAD,EAAUC,QAAV,EAAoBC,KAAK,GAAG,IAA5B,EAAkCC,UAAU,GAAG,IAA/C,EAAqD;AAErD,eAAKjC,YAAL,GAAoBiC,UAApB;AACA,eAAKhB,IAAL,CAAUiB,WAAV,CAAsBH,QAAQ,CAACI,CAA/B,EAAkCJ,QAAQ,CAACK,CAA3C;AACA,eAAKf,OAAL,CAAaJ,IAAb,CAAkBoB,KAAlB,GAA0B,CAA1B;AACA,eAAK9C,UAAL,GAAkB,KAAKQ,QAAL,CAAcuC,QAAhC;;AAEA,cAAI,KAAK/C,UAAL,GAAkB,CAAtB,EAAyB;AACrB,iBAAKD,YAAL,GAAoBrB,IAAI,CAACsE,SAAzB;AACH;;AAED,eAAKtB,IAAL,CAAUoB,KAAV,GAAkBP,OAAO,GAAG,MAAMC,QAAQ,CAACM,KAAlB,GAA0B,CAACN,QAAQ,CAACM,KAA7D;AACA,eAAKnD,KAAL,GAAa4C,OAAb;;AAEA,cAAI,CAACE,KAAK,CAACQ,KAAX,EAAkB;AACdR,YAAAA,KAAK,CAACS,OAAN,GAAgB,KAAK1C,QAAL,CAAc2C,SAAd,KAA4B,CAA5C;AACH;;AAED,eAAKzC,WAAL,GAAmB+B,KAAnB,CAlBqD,CAqBrD;;AACA,eAAKW,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAAClC,IAAL,CAAU,IAAV;AACH,WAFD,EAtBqD,CA0BrD;;AACA,gBAAMmC,aAAa,GAAG,KAAKC,OAAL;AAAA;AAAA,qCAAtB;;AACA,cAAID,aAAJ,EAAmB;AACfA,YAAAA,aAAa,CAACE,OAAd,CAAsB,CAACjB,QAAQ,CAACM,KAAhC,EAAuC,KAAKrC,YAA5C,EAA0D,KAAKd,KAA/D;AACH;;AAED,eAAKE,aAAL,CAAmBuB,IAAnB,CAAwB,IAAxB,EAA6BzC,IAAI,CAAC,KAAK6B,QAAL,CAAckD,IAAf,EAAqB,KAAKlD,QAAL,CAAckD,IAAnC,CAAjC;AACH;AACD;AACJ;AACA;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,gBAAMC,WAAW,GAAGC,IAAI,CAACC,MAAL,EAApB;AACA,gBAAMC,UAAU,GAAG,KAAKrD,WAAL,CAAiBsD,IAAjB,GAAwB,KAAKtD,WAAL,CAAiBsD,IAAjB,CAAsB,CAAtB,CAAxB,GAAmD,CAAtE;AACA,gBAAMC,YAAY,GAAG,KAAKvD,WAAL,CAAiBuD,YAAjB,IAAiC,CAAtD;AACA,cAAIC,MAAM,GAAG,KAAKxD,WAAL,CAAiBwD,MAA9B,CAJS,CAMT;;AACA,cAAI,KAAKzD,YAAL;AAAA;AAAA,qCAAJ,EAA4C;AACxC,kBAAM0D,MAAM,GAAG,KAAK1D,YAAL,CAAkBD,QAAjC;;AACA,gBAAI2D,MAAM,IAAIA,MAAM,CAACC,IAAP,KAAgB,GAA9B,EAAmC;AAC/B,kBAAI,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiBC,OAAjB,CAAyB,KAAKC,OAAL,EAAzB,KAA4C,CAAC,CAAjD,EAAoD;AAChDJ,gBAAAA,MAAM,IAAI,CAAV;AACH;AACJ;AACJ,WAdQ,CAgBT;;;AACA,cAAIN,WAAW,IAAIG,UAAnB,EAA+B;AAC3B,kBAAMQ,cAAc,GAAG,KAAK7D,WAAL,CAAiBsD,IAAjB,GAAwB,KAAKtD,WAAL,CAAiBsD,IAAjB,CAAsB,CAAtB,CAAxB,GAAmD,CAA1E;AACA,iBAAKlE,MAAL,GAAc,IAAd;AACA,mBAAO,CAACoE,MAAM,GAAGD,YAAV,IAA0BM,cAAjC;AACH,WAJD,MAIO;AACH,iBAAKzE,MAAL,GAAc,KAAd;AACA,mBAAOoE,MAAM,GAAGD,YAAhB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIO,QAAAA,SAAS,CAACC,MAAM,GAAG,IAAV,EAAgB;AACrB,cAAIP,MAAM,GAAG,KAAKP,UAAL,EAAb;;AACA,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,OAAO,GAAG;AACN,iBAAO,KAAK9D,QAAL,CAAcS,OAArB;AACH;AACD;AACJ;AACA;;;AACIyD,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AACH;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAqB;AAC1B;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA,eAAKC,MAAL,CAAY,IAAZ;;AACA,cAAI,KAAKrE,QAAL,CAAcsE,QAAlB,EAA4B;AACxB,gBAAIC,QAAQ,GAAGH,QAAQ,CAACI,MAAT,CAAgBtD,IAAhB,CAAqBK,MAArB,CAA4Bb,YAA5B,CAAyCtC,WAAzC,EAAsDqG,qBAAtD,CAA4EL,QAAQ,CAACI,MAAT,CAAgBtD,IAAhB,CAAqBc,QAAjG,CAAf;AACA;AAAA;AAAA,oCAAQ0C,iBAAR,CAA0BC,gBAA1B,CAA2CJ,QAA3C,EAAqD,KAAKvE,QAAL,CAAcsE,QAAnE,EAA6E,KAAKtE,QAAL,CAAc4E,QAA3F;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKrF,UAAL,GAAkB,CAAtB,EAAyB;AACrB,kBAAMsF,WAAW,GAAG5G,IAAI,CAACsE,SAAzB;AACA,iBAAK/C,SAAL,GAAiB,CAACqF,WAAW,GAAG,KAAKvF,YAApB,IAAoC,IAArD;AACH;;AAED,cAAI,KAAKE,SAAL,IAAkB,KAAKD,UAA3B,EAAuC;AACnC,iBAAK6E,MAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,MAAM,CAACU,KAAK,GAAG,KAAT,EAAgB;AAClB,eAAKC,UAAL;AACA;AAAA;AAAA,kCAAQ5E,aAAR,CAAsB6E,YAAtB,CAAmC,IAAnC;AACA,eAAKhF,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;;;AACIiF,QAAAA,SAAS,GAAG;AACR,eAAKF,UAAL;;AAEA,cAAI;AACA;AAAA;AAAA,oCAAQN,iBAAR,CAA0BS,iBAA1B,CAA4C,KAAKjE,IAAL,CAAUc,QAAtD;AACH,WAFD,CAEE,OAAOF,KAAP,EAAc;AACZsD,YAAAA,OAAO,CAACtD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;AACH;;AAED;AAAA;AAAA,kCAAQ1B,aAAR,CAAsB6E,YAAtB,CAAmC,IAAnC,EAAyC,KAAzC;AACA,eAAKhF,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;;;AACI+E,QAAAA,UAAU,GAAG;AACT,cAAI,KAAK3F,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB2B,QAAnB,GAA8B,KAA9B;AACH;;AAED,cAAI,KAAKM,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaG,WAAb,GAA2B,IAA3B;AACH;;AAED,eAAK1B,YAAL,CAAkBsF,MAAlB,CAAyB,CAAzB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAAY;AACd,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH;;AAED,eAAK5C,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,YAAAA,IAAI,CAACwC,MAAL,CAAYC,SAAZ;AACH,WAFD;;AAIA,cAAI,KAAKvF,QAAL,CAAcS,OAAd,KAA0B,EAA1B,IAAgC,KAAKS,IAAL,CAAUmB,CAAV,GAAc,CAAC,IAAnD,EAAyD;AACrD,iBAAKgC,MAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIoB,QAAAA,UAAU,CAACF,SAAD,EAAY;AAClB,eAAK3C,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3B,gBAAIA,IAAI,CAAC2C,UAAT,EAAqB;AACjB3C,cAAAA,IAAI,CAAC2C,UAAL,CAAgBF,SAAhB;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACIG,QAAAA,OAAO,GAAG;AACN,eAAK/F,cAAL,GAAsB,CAAtB;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKC,cAAL,GAAsB,IAAtB;;AACA,eAAKC,gBAAL,CAAsBuF,MAAtB,CAA6B,CAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,gBAAgB,CAAC1B,MAAD,EAAS;AACrB,iBACI,KAAKnE,gBAAL,CAAsB8F,MAAtB,KAAiC,CAAjC,IACA,KAAK9F,gBAAL,CAAsB+D,OAAtB,CAA8BI,MAA9B,IAAwC,CAF5C;AAIH;AAGD;AACJ;AACA;AAWI;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA9WuC,O,UAChC4B,U,GAAa,Q,UAsUbC,W,GAAc;AACjBC,QAAAA,IAAI,EAAE,CADW;AAEjBC,QAAAA,IAAI,EAAE,CAFW;AAGjBC,QAAAA,IAAI,EAAE,CAHW;AAIjBC,QAAAA,IAAI,EAAE,CAJW;AAKjBC,QAAAA,IAAI,EAAE,CALW;AAMjBC,QAAAA,IAAI,EAAE,CANW;AAOjBC,QAAAA,QAAQ,EAAE;AAPO,O;;;;;iBAnUX,I", "sourcesContent": ["import { _decorator, Sprite, ParticleSystem, Node, tween, v2, misc, director, game, ParticleSystem2D, Tween, v3, size, UITransform } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport { Tools } from '../../utils/Tools';\r\nimport Entity from '../base/Entity';\r\nimport AngleComp from '../base/AngleComp';\r\nimport BulletFly from './BulletFly';\r\nimport { GameFunc } from '../../GameFunc';\r\nimport { MainPlane } from '../plane/mainPlane/MainPlane';\r\nimport { GameConst } from '../../const/GameConst';\r\nimport CircleZoomFly from './CircleZoomFly';\r\nimport FBoxCollider from '../../collider-system/FBoxCollider';\r\nimport FCollider, { ColliderGroupType } from '../../collider-system/FCollider';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Bullet')\r\nexport default class Bullet extends Entity {\r\n    static PrefabName = \"Bullet\";\r\n\r\n    @property(Sprite)\r\n    skinImg = null;\r\n\r\n\r\n    enemy = false;\r\n    bulletID = 1;\r\n    m_collideComp:FBoxCollider;\r\n    isCirt = false;\r\n    m_createTime = 0;\r\n    m_lifeTime = 0;\r\n    aliveTime = 0;\r\n    m_fireTween = null;\r\n    _catapultCount = 0;\r\n    _catapultAtkRatio = 1;\r\n    _collideEntity = null;\r\n    _catapultTargets = [];\r\n    m_throughArr = [];\r\n    m_config;\r\n    m_mainEntity: any;\r\n    bulletState: any;\r\n\r\n\r\n    /**\r\n     * 初始化子弹\r\n     * @param {number} bulletID 子弹的唯一标识符\r\n     */\r\n    create(bulletID) {\r\n        this.bulletID = bulletID;\r\n        this.m_config = GameIns.bulletManager.getConfig(this.bulletID);\r\n        this.removeAllComp();\r\n\r\n        // 添加子弹飞行组件（如旋转角度）\r\n        if (this.m_config.angleSpeed && this.m_config.angleSpeed !== 0) {\r\n            this.addComp(AngleComp, new AngleComp({ angleSpeed: this.m_config.angleSpeed }, this.m_config.bustyle));\r\n        }\r\n        switch (this.m_config.bustyle) {\r\n            case 25:\r\n                this.addComp(CircleZoomFly, new CircleZoomFly(this.m_config));\r\n                break;\r\n\r\n            default:\r\n                this.addComp(BulletFly, new BulletFly(this.m_config));\r\n        }\r\n\r\n        // 添加碰撞组件\r\n        this.m_collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.m_collideComp.init(this,size(40,40)); // 初始化碰撞组件\r\n        this.m_collideComp.groupType = this.enemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;\r\n        this.m_collideComp.isEnable = true;\r\n\r\n        // 设置子弹皮肤\r\n        this.setSkin();\r\n\r\n        // 设置子弹缩放\r\n        this.node.setScale(this.m_config.scale, this.m_config.scale);\r\n    }\r\n    /**\r\n * 设置子弹的皮肤\r\n */\r\n    async setSkin() {\r\n        // 移除旧的子弹特效节点\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'fist');\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'gatlin');\r\n        Tools.removeChildByName(this.skinImg.node.parent, 'tail');\r\n\r\n        // 重置子弹透明度\r\n        this.skinImg.node.opacity = 255;\r\n\r\n        // 设置子弹的默认皮肤\r\n        this.skinImg.spriteFrame = null;\r\n\r\n        if (this.bulletID > 1000) {\r\n            // 敌方子弹\r\n            GameFunc.setImage(this.skinImg, this.m_config.image, GameIns.bulletManager.enemyBulletAtlas);\r\n        } else {\r\n            // 主角子弹\r\n            GameFunc.setImage(this.skinImg, this.m_config.image, GameIns.bulletManager.mainBulletAtlas);\r\n        }\r\n\r\n        // 检查图片是否加载成功\r\n        if (!this.skinImg.spriteFrame) {\r\n            Tools.error('Bullet image error:', this.m_config.image);\r\n        }\r\n    }\r\n    //     /**\r\n    //  * 初始化跟踪子弹\r\n    //  * @param {boolean} isEnemy 是否为敌方子弹\r\n    //  * @param {Object} position 子弹的初始位置\r\n    //  * @param {number} attack 子弹的攻击力\r\n    //  */\r\n    //     initFollow(isEnemy, position, attack) {\r\n    //         this.init(isEnemy, position, {\r\n    //             attack: attack,\r\n    //             through: false,\r\n    //         });\r\n    //     }\r\n\r\n    /**\r\n     * 初始化子弹\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @param {Object} position 子弹的初始位置\r\n     * @param {Object} state 子弹的状态\r\n     * @param {Entity} mainEntity 子弹的发射实体\r\n     */\r\n    init(isEnemy, position, state = null, mainEntity = null) {\r\n\r\n        this.m_mainEntity = mainEntity;\r\n        this.node.setPosition(position.x, position.y);\r\n        this.skinImg.node.angle = 0;\r\n        this.m_lifeTime = this.m_config.retrieve;\r\n\r\n        if (this.m_lifeTime > 0) {\r\n            this.m_createTime = game.totalTime;\r\n        }\r\n\r\n        this.node.angle = isEnemy ? 180 - position.angle : -position.angle;\r\n        this.enemy = isEnemy;\r\n\r\n        if (!state.clone) {\r\n            state.through = this.m_config.disappear === 1;\r\n        }\r\n\r\n        this.bulletState = state;\r\n\r\n\r\n        // 初始化子弹的所有组件\r\n        this.m_comps.forEach((comp) => {\r\n            comp.init(this);\r\n        });\r\n\r\n        // 设置子弹飞行组件\r\n        const bulletFlyComp = this.getComp(BulletFly);\r\n        if (bulletFlyComp) {\r\n            bulletFlyComp.setData(-position.angle, this.m_mainEntity, this.enemy);\r\n        }\r\n\r\n        this.m_collideComp.init(this,size(this.m_config.body, this.m_config.body));\r\n    }\r\n    /**\r\n     * 获取子弹的基础攻击力\r\n     * @returns {number} 子弹的攻击力\r\n     */\r\n    _getAttack() {\r\n        const randomValue = Math.random();\r\n        const critChance = this.bulletState.cirt ? this.bulletState.cirt[0] : 0;\r\n        const atkChallenge = this.bulletState.atkChallenge || 0;\r\n        let attack = this.bulletState.attack;\r\n\r\n        // 特殊处理主机类型子弹\r\n        if (this.m_mainEntity instanceof MainPlane) {\r\n            const config = this.m_mainEntity.m_config;\r\n            if (config && config.type === 702) {\r\n                if ([27, 28, 29, 37].indexOf(this.getType()) != -1) {\r\n                    attack /= 5;\r\n                }\r\n            }\r\n        }\r\n\r\n        // 判断是否暴击\r\n        if (randomValue <= critChance) {\r\n            const critMultiplier = this.bulletState.cirt ? this.bulletState.cirt[1] : 1;\r\n            this.isCirt = true;\r\n            return (attack + atkChallenge) * critMultiplier;\r\n        } else {\r\n            this.isCirt = false;\r\n            return attack + atkChallenge;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取子弹对目标的实际攻击力\r\n     * @param {Entity} target 目标实体\r\n     * @returns {number} 实际攻击力\r\n     */\r\n    getAttack(target = null) {\r\n        let attack = this._getAttack();\r\n        return attack;\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的类型\r\n     * @returns {number} 子弹的类型\r\n     */\r\n    getType() {\r\n        return this.m_config.bustyle;\r\n    }\r\n    /**\r\n     * 播放子弹命中音效\r\n     */\r\n    playHurtAudio() {\r\n        // if (this.m_config.hit.length > 0) {\r\n        //     Bullet.playAudio('hit2');\r\n        // }\r\n    }\r\n\r\n    onCollide(collider:FCollider) {\r\n        // if (this.getComp(ResistBulletComp)) {\r\n        //     this.getComp(ResistBulletComp).onCollide(target);\r\n        // }\r\n\r\n        // if (this.getComp(OnceCollideComp)) {\r\n        //     if (this.getComp(OnceCollideComp).onCollide(target)) {\r\n        //         this.m_throughArr.push(target);\r\n        //     }\r\n        // } else \r\n\r\n        this.remove(true);\r\n        if (this.m_config.exstyle1) {\r\n            let worldPos = collider.entity.node.parent.getComponent(UITransform).convertToWorldSpaceAR(collider.entity.node.position);\r\n            GameIns.hurtEffectManager.createHurtEffect(worldPos, this.m_config.exstyle1, this.m_config.exstyle2);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 子弹超出屏幕处理\r\n     */\r\n    onOutScreen() {\r\n        if (this.m_lifeTime > 0) {\r\n            const currentTime = game.totalTime;\r\n            this.aliveTime = (currentTime - this.m_createTime) / 1000;\r\n        }\r\n\r\n        if (this.aliveTime >= this.m_lifeTime) {\r\n            this.remove();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param {boolean} force 是否强制移除\r\n     */\r\n    remove(force = false) {\r\n        this.willRemove();\r\n        GameIns.bulletManager.removeBullet(this);\r\n        this.m_mainEntity = null;\r\n    }\r\n\r\n    /**\r\n     * 子弹死亡移除\r\n     */\r\n    dieRemove() {\r\n        this.willRemove();\r\n\r\n        try {\r\n            GameIns.hurtEffectManager.playBulletDieAnim(this.node.position);\r\n        } catch (error) {\r\n            console.error('Error during dieRemove:', error);\r\n        }\r\n\r\n        GameIns.bulletManager.removeBullet(this, false);\r\n        this.m_mainEntity = null;\r\n    }\r\n\r\n    /**\r\n     * 子弹移除前的清理操作\r\n     */\r\n    willRemove() {\r\n        if (this.m_collideComp) {\r\n            this.m_collideComp.isEnable = false;\r\n        }\r\n\r\n        if (this.skinImg) {\r\n            this.skinImg.spriteFrame = null;\r\n        }\r\n\r\n        this.m_throughArr.splice(0);\r\n    }\r\n\r\n    /**\r\n     * 更新子弹逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        this.m_comps.forEach((comp) => {\r\n            comp.update(deltaTime);\r\n        });\r\n\r\n        if (this.m_config.bustyle === 55 && this.node.y < -2000) {\r\n            this.remove();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新子弹的组件逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateComp(deltaTime) {\r\n        this.m_comps.forEach((comp) => {\r\n            if (comp.updateComp) {\r\n                comp.updateComp(deltaTime);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 刷新子弹状态\r\n     */\r\n    refresh() {\r\n        this._catapultCount = 0;\r\n        this._catapultAtkRatio = 1;\r\n        this._collideEntity = null;\r\n        this._catapultTargets.splice(0);\r\n    }\r\n\r\n    /**\r\n     * 检查子弹是否可以与目标碰撞\r\n     * @param {Entity} target 碰撞目标\r\n     * @returns {boolean} 是否可以碰撞\r\n     */\r\n    canCollideEntity(target) {\r\n        return (\r\n            this._catapultTargets.length === 0 ||\r\n            this._catapultTargets.indexOf(target) < 0\r\n        );\r\n    }\r\n\r\n\r\n    /**\r\n     * 子弹音效的最大播放数量\r\n     */\r\n    static audioMaxNum = {\r\n        hit1: 5,\r\n        hit2: 3,\r\n        hit3: 2,\r\n        hit4: 5,\r\n        hit5: 5,\r\n        hit6: 5,\r\n        swordHit: 5,\r\n    };\r\n\r\n    //     /**\r\n    //      * 子弹音效的播放状态\r\n    //      */\r\n    //     static audios = new Map();\r\n\r\n    //     /**\r\n    //      * 播放子弹音效\r\n    //      * @param {string} audioName 音效名称\r\n    //      */\r\n    //     static playAudio(audioName) {\r\n    //         const maxNum = Bullet.audioMaxNum[audioName];\r\n    //         let currentNum = Bullet.audios.get(audioName) || 0;\r\n\r\n    //         if (currentNum < maxNum) {\r\n    //             Bullet.audios.set(audioName, ++currentNum);\r\n    //             // const audioId = GameIns.audioManager.playEffectSync(audioName);\r\n    //             // audioEngine.setFinishCallback(audioId, () => {\r\n    //             //     Bullet.onAudioFinish(audioName);\r\n    //             // });\r\n    //         }\r\n    //     }\r\n\r\n    //     /**\r\n    //      * 子弹音效播放完成的回调\r\n    //      * @param {string} audioName 音效名称\r\n    //      */\r\n    //     static onAudioFinish(audioName) {\r\n    //         const currentNum = Bullet.audios.get(audioName);\r\n    //         Bullet.audios.set(audioName, Math.max(0, currentNum - 1));\r\n    //     }\r\n}"]}