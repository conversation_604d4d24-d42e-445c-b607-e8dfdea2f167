
import {IPlatformSDK, PlatformSDKUserInfo} from './IPlatformSDK'
import { DevLoginData } from './DevLoginData';
import { LoginInfo } from '../Network/NetMgr';
import { logInfo } from '../Utils/Logger';
import csproto from '../AutoGen/PB/cs_proto.js';
import CryptoJ<PERSON> from 'crypto-js';

export class DevLogin implements IPlatformSDK
{
    showUserInfoButton()
    {
    }
    hideUserInfoButton()
    {
    }
    login(cb:(err:string, req:LoginInfo)=>void) : void
    {
        logInfo("Login", "dev login")
        cb(null, {
            accountType: csproto.cs.ACCOUNT_TYPE.ACCOUNT_TYPE_NONE,
            code: DevLoginData.instance.user + "#" + CryptoJS.MD5(DevLoginData.instance.password),
            serverAddr: DevLoginData.instance.getServerAddr(),
        })
    }

    getUserInfo(cb:(err:string, req:PlatformSDKUserInfo, hasTap:boolean)=>void, param:any)
    {
        cb(null, {
            name : 'dev',
            icon : null,
        }, false)
    }
}