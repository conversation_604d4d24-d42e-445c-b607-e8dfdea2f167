import { System } from "./System";
import { World } from "./World";
import { TypeID, TypeIDUtils } from "./TypeID";

/**
 * Type definition for system update functions
 */
export type SystemUpdateFn = (deltaTime: number) => void;
export type SystemLateUpdateFn = (deltaTime: number) => void;

/**
 * SystemContainer manages a collection of game systems
 * Similar to MyApp's ManagerPool but specifically for game world systems
 */
export class SystemContainer {
    private _world: World | null = null;
    private _systems: System[] = [];
    private _systemRegistry: TypeIDUtils.TypedRegistry<System> = new TypeIDUtils.TypedRegistry<System>();
    private _updateContainer: SystemUpdateFn[] = [];
    private _lateUpdateContainer: SystemLateUpdateFn[] = [];
    private _isInitialized: boolean = false;
    
    /**
     * Register a system to the container
     * @param system The system to register
     * @returns true if registration was successful, false if system already exists
     */
    public registerSystem(system: System): boolean {
        const systemName = system.getTypeName();
        const typeId = system.getTypeId();

        if (this._systemRegistry.has(system.constructor as new (...args: any[]) => System)) {
            console.warn(`SystemContainer: System ${systemName} (TypeID: ${typeId}) is already registered`);
            return false;
        }

        this._systems.push(system);
        this._systemRegistry.register(system);

        // If container is already initialized, initialize the new system immediately
        if (this._isInitialized) {
            this._initializeSystem(system);
        }

        console.log(`SystemContainer: Registered system ${systemName} (TypeID: ${typeId})`);
        return true;
    }
    
    /**
     * Unregister a system from the container
     * @param systemConstructor The constructor of the system to unregister
     * @returns true if unregistration was successful, false if system not found
     */
    public unregisterSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {
        const system = this._systemRegistry.get(systemConstructor);
        if (!system) {
            const typeName = TypeID.getTypeName(TypeID.get(systemConstructor));
            console.warn(`SystemContainer: System ${typeName} not found`);
            return false;
        }

        const systemName = system.getTypeName();

        // Cleanup the system
        system.unInit();

        // Remove from collections
        const index = this._systems.indexOf(system);
        if (index >= 0) {
            this._systems.splice(index, 1);
            this._updateContainer.splice(index, 1);
            this._lateUpdateContainer.splice(index, 1);
        }

        this._systemRegistry.remove(systemConstructor);

        console.log(`SystemContainer: Unregistered system ${systemName}`);
        return true;
    }
    
    /**
     * Get a system by type
     * @param systemConstructor The constructor of the system to get
     * @returns The system instance or null if not found
     */
    public getSystem<T extends System>(systemConstructor: new (...args: any[]) => T): T | null {
        return this._systemRegistry.get(systemConstructor);
    }

    /**
     * Check if a system is registered
     * @param systemConstructor The constructor of the system to check
     * @returns true if the system is registered
     */
    public hasSystem<T extends System>(systemConstructor: new (...args: any[]) => T): boolean {
        return this._systemRegistry.has(systemConstructor);
    }
    
    /**
     * Get all registered systems
     * @returns Array of all registered systems
     */
    public getAllSystems(): System[] {
        return [...this._systems];
    }
    
    /**
     * Get the number of registered systems
     * @returns The number of registered systems
     */
    public getSystemCount(): number {
        return this._systems.length;
    }
    
    /**
     * Initialize all registered systems
     */
    public init(world: World): void {
        if (this._isInitialized) {
            console.warn("SystemContainer: Already initialized");
            return;
        }
        
        console.log(`SystemContainer: Initializing ${this._systems.length} systems`);
        this._world = world;
        // Clear update containers
        this._updateContainer.length = 0;
        this._lateUpdateContainer.length = 0;
        
        // Initialize all systems
        this._systems.forEach(system => {
            this._initializeSystem(system);
        });
        
        this._isInitialized = true;
        console.log("SystemContainer: Initialization complete");
    }
    
    /**
     * Cleanup all systems
     */
    public unInit(): void {
        if (!this._isInitialized) {
            return;
        }
        
        console.log("SystemContainer: Cleaning up systems");
        
        // Cleanup all systems in reverse order
        for (let i = this._systems.length - 1; i >= 0; i--) {
            this._systems[i].unInit();
        }
        
        // Clear all containers
        this._systems.length = 0;
        this._systemRegistry.clear();
        this._updateContainer.length = 0;
        this._lateUpdateContainer.length = 0;
        
        this._isInitialized = false;
        console.log("SystemContainer: Cleanup complete");
    }
    
    /**
     * Update all systems
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public update(deltaTime: number): void {
        if (!this._isInitialized) {
            return;
        }
        
        for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
        }
    }
    
    /**
     * Late update all systems
     * @param deltaTime Time elapsed since last frame in seconds
     */
    public lateUpdate(deltaTime: number): void {
        if (!this._isInitialized) {
            return;
        }
        
        for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i](deltaTime);
        }
    }
    
    /**
     * Enable or disable a specific system
     * @param systemConstructor The constructor of the system to enable/disable
     * @param enabled Whether to enable or disable the system
     * @returns true if the operation was successful
     */
    public setSystemEnabled<T extends System>(systemConstructor: new (...args: any[]) => T, enabled: boolean): boolean {
        const system = this._systemRegistry.get(systemConstructor);
        if (!system) {
            const typeName = TypeID.getTypeName(TypeID.get(systemConstructor));
            console.warn(`SystemContainer: System ${typeName} not found`);
            return false;
        }

        const systemName = system.getTypeName();
        system.setEnabled(enabled);
        console.log(`SystemContainer: System ${systemName} ${enabled ? 'enabled' : 'disabled'}`);
        return true;
    }
    
    /**
     * Check if the container is initialized
     */
    public isInitialized(): boolean {
        return this._isInitialized;
    }
    
    /**
     * Initialize a single system and add it to update containers
     * @param system The system to initialize
     */
    private _initializeSystem(system: System): void {
        try {
            system.init(this._world);
            this._updateContainer.push(system.update.bind(system));
            this._lateUpdateContainer.push(system.lateUpdate.bind(system));
            console.log(`SystemContainer: Initialized system ${system.getTypeName()}`);
        } catch (error) {
            console.error(`SystemContainer: Failed to initialize system ${system.getTypeName()}:`, error);
        }
    }
}
