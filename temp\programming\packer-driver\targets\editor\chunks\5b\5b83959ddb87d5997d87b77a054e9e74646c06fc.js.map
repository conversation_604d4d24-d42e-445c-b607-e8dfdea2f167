{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts"], "names": ["audioManager", "_decorator", "AudioSource", "clamp01", "IMgr", "MyApp", "ccclass", "property", "instance", "_instance", "init", "audioSource", "GetInstance", "node", "getComponent", "_audioSource", "playMusic", "loop", "playing", "play", "playSound", "audioClip", "volumeScale", "playOneShot", "setMusicVolume", "flag", "volume"], "mappings": ";;;8HAKaA,Y;;;;;;;;;;;;;;;;;;;AALLC,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,W,OAAAA,W;AAAqCC,MAAAA,O,OAAAA,O;;AAE3DC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OAFH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;8BAIjBD,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,wBAAgC;AAKhB,mBAARQ,QAAQ,GAAI;AACnB,cAAI,KAAKC,SAAT,EAAoB;AAChB,mBAAO,KAAKA,SAAZ;AACH;;AAED,eAAKA,SAAL,GAAiB,IAAIT,YAAJ,EAAjB;AACA,iBAAO,KAAKS,SAAZ;AACH;AAED;;;AACAC,QAAAA,IAAI,GAAI;AACJ,cAAIC,WAAW,GAAG;AAAA;AAAA,8BAAMC,WAAN,GAAoBC,IAApB,CAAyBC,YAAzB,CAAsCZ,WAAtC,CAAlB;AACAF,UAAAA,YAAY,CAACe,YAAb,GAA4BJ,WAA5B;AACH;AAEC;AACN;AACA;AACA;;;AACIK,QAAAA,SAAS,CAAEC,IAAF,EAAiB;AACtB,gBAAMN,WAAW,GAAGX,YAAY,CAACe,YAAjC;;AACA,cAAI,CAACJ,WAAL,EAAkB;AACd;AACH;;AAEDA,UAAAA,WAAW,CAACM,IAAZ,GAAmBA,IAAnB;;AACA,cAAI,CAACN,WAAW,CAACO,OAAjB,EAA0B;AACtBP,YAAAA,WAAW,CAACQ,IAAZ;AACH;AACJ;AAEA;AACL;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAAEC,SAAF,EAAwBC,WAAmB,GAAG,CAA9C,EAAkD;AACvD,gBAAMX,WAAW,GAAGX,YAAY,CAACe,YAAjC;;AACA,cAAI,CAACJ,WAAL,EAAkB;AACd;AACH,WAJsD,CAMvD;;;AACAA,UAAAA,WAAW,CAACY,WAAZ,CAAwBF,SAAxB,EAAmCC,WAAnC;AAEH,SAlDkC,CAmDnC;;;AACAE,QAAAA,cAAc,CAAEC,IAAF,EAAgB;AAC1B,gBAAMd,WAAW,GAAGX,YAAY,CAACe,YAAjC;;AACA,cAAI,CAACJ,WAAL,EAAkB;AACd;AACH;;AAEDc,UAAAA,IAAI,GAAGtB,OAAO,CAACsB,IAAD,CAAd;AACAd,UAAAA,WAAW,CAACe,MAAZ,GAAqBD,IAArB;AACH;;AA5DkC,O;;AAA1BzB,MAAAA,Y,CAEMS,S;AAFNT,MAAAA,Y,CAGMe,Y", "sourcesContent": ["import {_decorator, AudioClip, AudioSource,Component, assert, warn, clamp01, resources } from \"cc\";\nconst { ccclass, property } = _decorator;\nimport { IMgr } from '../IMgr';\nimport { MyApp } from \"../MyApp\";\n\nexport class audioManager extends IMgr {\n\n    private static _instance: audioManager;\n    private static _audioSource?: AudioSource;\n\n    static get instance () {\n        if (this._instance) {\n            return this._instance;\n        }\n\n        this._instance = new audioManager();\n        return this._instance;\n    }\n\n    /**管理器初始化*/\n    init () {\n        var audioSource = MyApp.GetInstance().node.getComponent(AudioSource);\n        audioManager._audioSource = audioSource;\n    }\n\n      /**\n     * 播放音乐\n     * @param {Boolean} loop 是否循环播放\n     */\n    playMusic (loop: boolean) {\n        const audioSource = audioManager._audioSource!;\n        if (!audioSource) {\n            return;\n        }\n\n        audioSource.loop = loop;\n        if (!audioSource.playing) {\n            audioSource.play();\n        }\n    }\n\n     /**\n     * 播放音效\n     * @param {audioClip} audioClip 音效名称\n     * @param {Number} volumeScale 播放音量倍数\n     */\n    playSound (audioClip: AudioClip, volumeScale: number = 1 ) {\n        const audioSource = audioManager._audioSource!;\n        if (!audioSource) {\n            return;\n        }\n            \n        // 注意：第二个参数 “volumeScale” 是指播放音量的倍数，最终播放的音量为 “audioSource.volume * volumeScale”\n        audioSource.playOneShot(audioClip, volumeScale);\n\n    }\n    // 设置音乐音量\n    setMusicVolume (flag: number) {\n        const audioSource = audioManager._audioSource!;\n        if (!audioSource) {\n            return;\n        }\n\n        flag = clamp01(flag);\n        audioSource.volume = flag;\n    }\n\n}"]}