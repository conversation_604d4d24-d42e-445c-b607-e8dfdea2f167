{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts"], "names": ["LoginInfo", "_decorator", "<PERSON>", "csproto", "IMgr", "logDebug", "logError", "logInfo", "log<PERSON>arn", "ccclass", "NetStatus", "accountType", "code", "serverAddr", "NetMgr", "_websocket", "_status", "NotConnect", "loginInfo", "_reconnectAttempts", "_maxReconnectAttempts", "_reconnectDelay", "_heartbeatInterval", "_heartbeatTimer", "_lastHeartbeatTime", "_messageHandlers", "Map", "_messageQueue", "_currentSeq", "registerInInitHandlerBound", "initRegistered", "msgId", "handler", "bind", "set", "registerHandler", "uninitRegistered", "for<PERSON>ach", "unregister<PERSON><PERSON><PERSON>", "clear", "init", "cs", "CS_CMD", "CS_CMD_GET_SESSION", "onGetSession", "CS_CMD_HEARTBEAT", "onHeartbeat", "CS_CMD_GET_ROLE", "onGetRole", "unInit", "disconnect", "length", "onUpdate", "dt", "Connected", "sendHeartbeat", "Date", "now", "handleDisconnection", "connect", "Connecting", "createWebSocket", "login", "info", "close", "Disconnected", "getStatus", "isConnected", "WebSocket", "binaryType", "onopen", "onWebSocketOpen", "onmessage", "onWebSocketMessage", "onclose", "onWebSocketClose", "onerror", "onWebSocketError", "err", "_event", "sendMessage", "get_session", "account_type", "platform", "PLATFORM", "PLATFORM_EDITOR", "version", "event", "buffer", "Uint8Array", "data", "handleMessage", "reason", "setTimeout", "processMessageQueue", "message", "shift", "sendRawMessage", "msg", "S2CMsg", "decode", "JSON", "stringify", "dispatchMessage", "handlers", "get", "cmd", "stack", "decodeProtobufMessage", "_msgId", "has", "push", "index", "indexOf", "splice", "delete", "C2SMsgBody", "create", "netMessage", "encodeProtobufMessage", "readyState", "OPEN", "send", "byteLength", "seq", "C2SMsg", "body", "clientData", "encode", "finish", "heartbeatData", "heartbeat", "clent_time", "fromNumber", "is_fighting", "clearAllHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "sessionRsp", "openid", "uin_list", "get_role", "uin", "ZERO", "area_id", "roleRsp", "disableReconnect"], "mappings": ";;;sJAwBaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxBJC,MAAAA,U,OAAAA,U;;AACFC,MAAAA,I;;AACAC,MAAAA,O;;AACEC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;;;;;;;;OAChC;AAAEC,QAAAA;AAAF,O,GAAcR,U;;2BAERS,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;2BAiBCV,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACnBW,WADmB;AAAA,eAEnBC,IAFmB;AAAA,eAGnBC,UAHmB;AAAA;;AAAA,O;;wBAOVC,M,WADZL,OAAO,CAAC,QAAD,C,iBAAR,MACaK,MADb;AAAA;AAAA,wBACiC;AAAA;AAAA;AAAA,eACrBC,UADqB,GACU,IADV;AAAA,eAErBC,OAFqB,GAEAN,SAAS,CAACO,UAFV;AAAA,eAGrBC,SAHqB,GAGE,IAHF;AAAA,eAIrBC,kBAJqB,GAIQ,CAJR;AAAA,eAKrBC,qBALqB,GAKW,CALX;AAAA,eAMrBC,eANqB,GAMK,IANL;AAMW;AANX,eAOrBC,kBAPqB,GAOQ,IAPR;AAOc;AAPd,eAQrBC,eARqB,GAQK,CARL;AAAA,eASrBC,kBATqB,GASQ,CATR;AAAA,eAUrBC,gBAVqB,GAU8B,IAAIC,GAAJ,EAV9B;AAAA,eAWrBC,aAXqB,GAWU,EAXV;AAAA,eAYrBC,WAZqB,GAYC,CAZD;AAAA,eAcrBC,0BAdqB,GAcsC,IAAIH,GAAJ,EAdtC;AAAA;;AAgBrBI,QAAAA,cAAc,CAACC,KAAD,EAAgBC,OAAhB,EAAgD;AAClEA,UAAAA,OAAO,GAAGA,OAAO,CAACC,IAAR,CAAa,IAAb,CAAV;AACA,eAAKJ,0BAAL,CAAgCK,GAAhC,CAAoCH,KAApC,EAA2CC,OAA3C;AACA,eAAKG,eAAL,CAAqBJ,KAArB,EAA4BC,OAA5B;AACH;;AACOI,QAAAA,gBAAgB,GAAS;AAC7B,eAAKP,0BAAL,CAAgCQ,OAAhC,CAAwC,CAACL,OAAD,EAAUD,KAAV,KAAoB;AACxD,iBAAKO,iBAAL,CAAuBP,KAAvB,EAA8BC,OAA9B;AACH,WAFD;AAGA,eAAKH,0BAAL,CAAgCU,KAAhC;AACH;;AAEDC,QAAAA,IAAI,GAAS;AACT;AAAA;AAAA,kCAAQ,QAAR,EAAkB,6BAAlB;AAEA,eAAKV,cAAL,CAAoB;AAAA;AAAA,kCAAQW,EAAR,CAAWC,MAAX,CAAkBC,kBAAtC,EAA0D,KAAKC,YAA/D;AACA,eAAKd,cAAL,CAAoB;AAAA;AAAA,kCAAQW,EAAR,CAAWC,MAAX,CAAkBG,gBAAtC,EAAwD,KAAKC,WAA7D;AACA,eAAKhB,cAAL,CAAoB;AAAA;AAAA,kCAAQW,EAAR,CAAWC,MAAX,CAAkBK,eAAtC,EAAuD,KAAKC,SAA5D;AACH;;AAEDC,QAAAA,MAAM,GAAS;AACX,eAAKb,gBAAL;AAEA,eAAKc,UAAL;;AACA,eAAKzB,gBAAL,CAAsBc,KAAtB;;AACA,eAAKZ,aAAL,CAAmBwB,MAAnB,GAA4B,CAA5B;AACA,gBAAMF,MAAN;AACH;;AAEDG,QAAAA,QAAQ,CAACC,EAAD,EAAmB;AACvB,eAAK9B,eAAL,IAAwB8B,EAAE,GAAG,IAA7B,CADuB,CAGvB;;AACA,cAAI,KAAKrC,OAAL,KAAiBN,SAAS,CAAC4C,SAA3B,IACA,KAAK/B,eAAL,IAAwB,KAAKD,kBADjC,EACqD;AACjD,iBAAKiC,aAAL;AACA,iBAAKhC,eAAL,GAAuB,CAAvB;AACH,WARsB,CAUvB;;;AACA,cAAI,KAAKP,OAAL,KAAiBN,SAAS,CAAC4C,SAA3B,IACAE,IAAI,CAACC,GAAL,KAAa,KAAKjC,kBAAlB,GAAuC,KAAKF,kBAAL,GAA0B,CADrE,EACwE;AACpE;AAAA;AAAA,oCAAQ,QAAR,EAAkB,6CAAlB;AACA,iBAAKoC,mBAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,cAAI,KAAK3C,OAAL,KAAiBN,SAAS,CAACkD,UAA3B,IAAyC,KAAK5C,OAAL,KAAiBN,SAAS,CAAC4C,SAAxE,EAAmF;AAC/E;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AAED,eAAKtC,OAAL,GAAeN,SAAS,CAACkD,UAAzB;AACA,eAAKzC,kBAAL,GAA0B,CAA1B;AAEA;AAAA;AAAA,kCAAQ,QAAR,EAAmB,iBAAgB,KAAKD,SAAL,CAAeL,UAAW,EAA7D;AACA,eAAKgD,eAAL;AACH;;AAEDC,QAAAA,KAAK,CAACC,IAAD,EAAwB;AACzB,eAAK7C,SAAL,GAAiB6C,IAAjB;AACA,eAAKJ,OAAL;AACH;AAED;AACJ;AACA;;;AACIT,QAAAA,UAAU,GAAS;AACf,cAAI,KAAKnC,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBiD,KAAhB;;AACA,iBAAKjD,UAAL,GAAkB,IAAlB;AACH;;AACD,eAAKC,OAAL,GAAeN,SAAS,CAACuD,YAAzB;AACA,eAAK9C,kBAAL,GAA0B,CAA1B;AACA;AAAA;AAAA,kCAAQ,QAAR,EAAkB,0BAAlB;AACH;AAED;AACJ;AACA;;;AACI+C,QAAAA,SAAS,GAAc;AACnB,iBAAO,KAAKlD,OAAZ;AACH;AAED;AACJ;AACA;;;AACImD,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKnD,OAAL,KAAiBN,SAAS,CAAC4C,SAAlC;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,eAAe,GAAS;AAC5B,cAAI;AACA,iBAAK9C,UAAL,GAAkB,IAAIqD,SAAJ,CAAc,KAAKlD,SAAL,CAAeL,UAA7B,CAAlB;AACA,iBAAKE,UAAL,CAAgBsD,UAAhB,GAA6B,aAA7B;AAEA,iBAAKtD,UAAL,CAAgBuD,MAAhB,GAAyB,KAAKC,eAAL,CAAqBtC,IAArB,CAA0B,IAA1B,CAAzB;AACA,iBAAKlB,UAAL,CAAgByD,SAAhB,GAA4B,KAAKC,kBAAL,CAAwBxC,IAAxB,CAA6B,IAA7B,CAA5B;AACA,iBAAKlB,UAAL,CAAgB2D,OAAhB,GAA0B,KAAKC,gBAAL,CAAsB1C,IAAtB,CAA2B,IAA3B,CAA1B;AACA,iBAAKlB,UAAL,CAAgB6D,OAAhB,GAA0B,KAAKC,gBAAL,CAAsB5C,IAAtB,CAA2B,IAA3B,CAA1B;AAEH,WATD,CASE,OAAO6C,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,EAAoB,+BAA8BA,GAAI,EAAtD;AACA,iBAAKpB,mBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACYa,QAAAA,eAAe,CAACQ,MAAD,EAAsB;AACzC;AAAA;AAAA,kCAAQ,QAAR,EAAkB,qBAAlB;AACA,eAAK/D,OAAL,GAAeN,SAAS,CAAC4C,SAAzB;AACA,eAAKnC,kBAAL,GAA0B,CAA1B;AACA,eAAKK,kBAAL,GAA0BgC,IAAI,CAACC,GAAL,EAA1B;AAEA,eAAKuB,WAAL,CAAiB;AAAA;AAAA,kCAAQvC,EAAR,CAAWC,MAAX,CAAkBC,kBAAnC,EAAuD;AACnDsC,YAAAA,WAAW,EAAE;AACTC,cAAAA,YAAY,EAAE,KAAKhE,SAAL,CAAeP,WADpB;AACiC;AAC1CwE,cAAAA,QAAQ,EAAE;AAAA;AAAA,sCAAQ1C,EAAR,CAAW2C,QAAX,CAAoBC,eAFrB;AAEsC;AAC/CzE,cAAAA,IAAI,EAAE,KAAKM,SAAL,CAAeN,IAHZ;AAGkB;AAC3B0E,cAAAA,OAAO,EAAE,CAJA,CAIG;;AAJH;AADsC,WAAvD;AASH;AAED;AACJ;AACA;;;AACYb,QAAAA,kBAAkB,CAACc,KAAD,EAA4B;AAClD,cAAI;AACA;AAAA;AAAA,sCAAS,QAAT,EAAoB,8BAA6BA,KAAM,EAAvD;AACA,kBAAMC,MAAM,GAAG,IAAIC,UAAJ,CAAeF,KAAK,CAACG,IAArB,CAAf;AACA,iBAAKC,aAAL,CAAmBH,MAAnB;AACA,iBAAKhE,kBAAL,GAA0BgC,IAAI,CAACC,GAAL,EAA1B;AACH,WALD,CAKE,OAAOqB,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,EAAoB,6BAA4BA,GAAI,EAApD;AACH;AACJ;AAED;AACJ;AACA;;;AACYH,QAAAA,gBAAgB,CAACY,KAAD,EAA0B;AAC9C;AAAA;AAAA,kCAAQ,QAAR,EAAmB,qBAAoBA,KAAK,CAAC3E,IAAK,MAAK2E,KAAK,CAACK,MAAO,EAApE;AACA,eAAKlC,mBAAL;AACH;AAED;AACJ;AACA;;;AACYmB,QAAAA,gBAAgB,CAACE,MAAD,EAAsB;AAC1C;AAAA;AAAA,oCAAS,QAAT,EAAmB,0BAAnB;AACA,eAAKrB,mBAAL;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,mBAAmB,GAAS;AAChC,cAAI,KAAK3C,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBiD,KAAhB;;AACA,iBAAKjD,UAAL,GAAkB,IAAlB;AACH;;AAED,eAAKC,OAAL,GAAeN,SAAS,CAACO,UAAzB,CANgC,CAQhC;;AACA,cAAI,KAAKE,kBAAL,GAA0B,KAAKC,qBAAnC,EAA0D;AACtD,iBAAKD,kBAAL;AACA;AAAA;AAAA,oCAAQ,QAAR,EAAmB,2BAA0B,KAAKA,kBAAmB,IAAG,KAAKC,qBAAsB,EAAnG;AAEAyE,YAAAA,UAAU,CAAC,MAAM;AACb,kBAAI,KAAK3E,SAAL,CAAeL,UAAf,IAA6B,KAAKG,OAAL,KAAiBN,SAAS,CAACuD,YAA5D,EAA0E;AACtE,qBAAKN,OAAL;AACH;AACJ,aAJS,EAIP,KAAKtC,eAJE,CAAV;AAKH,WATD,MASO;AACH;AAAA;AAAA,sCAAS,QAAT,EAAmB,mCAAnB;AACA,iBAAKL,OAAL,GAAeN,SAAS,CAACuD,YAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACY6B,QAAAA,mBAAmB,GAAS;AAChC,iBAAO,KAAKnE,aAAL,CAAmBwB,MAAnB,GAA4B,CAA5B,IAAiC,KAAKgB,WAAL,EAAxC,EAA4D;AACxD,kBAAM4B,OAAO,GAAG,KAAKpE,aAAL,CAAmBqE,KAAnB,EAAhB;;AACA,gBAAID,OAAJ,EAAa;AACT,mBAAKE,cAAL,CAAoBF,OAApB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYJ,QAAAA,aAAa,CAACH,MAAD,EAA2B;AAC5C,cAAI;AACA;AACA,gBAAIU,GAAG,GAAG;AAAA;AAAA,oCAAQzD,EAAR,CAAW0D,MAAX,CAAkBC,MAAlB,CAAyBZ,MAAzB,CAAV;AACA;AAAA;AAAA,sCAAS,QAAT,EAAoB,oBAAmBa,IAAI,CAACC,SAAL,CAAeJ,GAAf,CAAoB,EAA3D;AACA,iBAAKK,eAAL,CAAqBL,GAArB;AAEH,WAND,CAME,OAAOpB,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,EAAoB,4BAA2BA,GAAI,EAAnD;AACH;AACJ;AAED;AACJ;AACA;;;AACYyB,QAAAA,eAAe,CAACL,GAAD,EAAgC;AACnD,gBAAMM,QAAQ,GAAG,KAAK/E,gBAAL,CAAsBgF,GAAtB,CAA0BP,GAAG,CAACQ,GAA9B,CAAjB;;AACA,cAAIF,QAAQ,IAAIA,QAAQ,CAACrD,MAAT,GAAkB,CAAlC,EAAqC;AACjC,gBAAI;AACAqD,cAAAA,QAAQ,CAACnE,OAAT,CAAiBL,OAAO,IAAI;AACxB,oBAAI;AACAA,kBAAAA,OAAO,CAACkE,GAAD,CAAP;AACH,iBAFD,CAEE,OAAOpB,GAAP,EAAY;AACV;AAAA;AAAA,4CAAS,QAAT,EAAoB,2BAA0BoB,GAAG,CAACQ,GAAI,KAAK5B,GAAD,CAAe6B,KAAM,EAA/E;AACH;AACJ,eAND;AAOH,aARD,CAQE,OAAO7B,GAAP,EAAY;AACV;AAAA;AAAA,wCAAS,QAAT,EAAoB,4BAA2BoB,GAAG,CAACQ,GAAI,KAAK5B,GAAD,CAAe6B,KAAM,EAAhF;AACH;AACJ,WAZD,MAYO;AACH;AAAA;AAAA,oCAAQ,QAAR,EAAmB,oCAAmCT,GAAG,CAACQ,GAAI,GAA9D;AACH;AACJ;AAED;AACJ;AACA;;;AACYE,QAAAA,qBAAqB,CAACC,MAAD,EAAiBnB,IAAjB,EAAwC;AACjE;AACA;AACA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;;;AACIvD,QAAAA,eAAe,CAACJ,KAAD,EAAgBC,OAAhB,EAAgD;AAC3D,cAAI,CAAC,KAAKP,gBAAL,CAAsBqF,GAAtB,CAA0B/E,KAA1B,CAAL,EAAuC;AACnC,iBAAKN,gBAAL,CAAsBS,GAAtB,CAA0BH,KAA1B,EAAiC,EAAjC;AACH;;AACD,eAAKN,gBAAL,CAAsBgF,GAAtB,CAA0B1E,KAA1B,EAAkCgF,IAAlC,CAAuC/E,OAAvC;;AACA;AAAA;AAAA,kCAAQ,QAAR,EAAmB,iCAAgCD,KAAM,EAAzD;AACH;AAED;AACJ;AACA;;;AACIO,QAAAA,iBAAiB,CAACP,KAAD,EAAgBC,OAAhB,EAAgD;AAC7D,gBAAMwE,QAAQ,GAAG,KAAK/E,gBAAL,CAAsBgF,GAAtB,CAA0B1E,KAA1B,CAAjB;;AACA,cAAIyE,QAAJ,EAAc;AACV,kBAAMQ,KAAK,GAAGR,QAAQ,CAACS,OAAT,CAAiBjF,OAAjB,CAAd;;AACA,gBAAIgF,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdR,cAAAA,QAAQ,CAACU,MAAT,CAAgBF,KAAhB,EAAuB,CAAvB;AACA;AAAA;AAAA,sCAAQ,QAAR,EAAmB,mCAAkCjF,KAAM,EAA3D,EAFc,CAId;;AACA,kBAAIyE,QAAQ,CAACrD,MAAT,KAAoB,CAAxB,EAA2B;AACvB,qBAAK1B,gBAAL,CAAsB0F,MAAtB,CAA6BpF,KAA7B;;AACA;AAAA;AAAA,wCAAQ,QAAR,EAAmB,0CAAyCA,KAAM,EAAlE;AACH;AACJ,aATD,MASO;AACH;AAAA;AAAA,sCAAQ,QAAR,EAAmB,gCAA+BA,KAAM,EAAxD;AACH;AACJ,WAdD,MAcO;AACH;AAAA;AAAA,oCAAQ,QAAR,EAAmB,qCAAoCA,KAAM,EAA7D;AACH;AACJ;AAED;AACJ;AACA;;;AACIiD,QAAAA,WAAW,CAACjD,KAAD,EAAgBgE,OAAhB,EAAuD;AAC9D;AAAA;AAAA,oCAAS,QAAT,EAAoB,eAAchE,KAAM,IAAGsE,IAAI,CAACC,SAAL,CAAe;AAAA;AAAA,kCAAQ7D,EAAR,CAAW2E,UAAX,CAAsBC,MAAtB,CAA6BtB,OAA7B,CAAf,CAAsD,EAAjG;;AACA,cAAI;AACA;AACA,kBAAMuB,UAAU,GAAG,KAAKC,qBAAL,CAA2BxF,KAA3B,EAAkCgE,OAAlC,CAAnB;;AAEA,gBAAI,KAAK5B,WAAL,EAAJ,EAAwB;AACpB,mBAAK8B,cAAL,CAAoBqB,UAApB;AACH,aAFD,MAEO;AACH;AACA,mBAAK3F,aAAL,CAAmBoF,IAAnB,CAAwBO,UAAxB;;AACA;AAAA;AAAA,sCAAQ,QAAR,EAAmB,kBAAiBvF,KAAM,kBAA1C;AACH;AACJ,WAXD,CAWE,OAAO+C,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,EAAoB,0BAAyB/C,KAAM,KAAI+C,GAAI,EAA3D;AACH;AACJ;AAED;AACJ;AACA;;;AACYmB,QAAAA,cAAc,CAACF,OAAD,EAA6B;AAC/C,cAAI,CAAC,KAAKhF,UAAN,IAAoB,KAAKA,UAAL,CAAgByG,UAAhB,KAA+BpD,SAAS,CAACqD,IAAjE,EAAuE;AACnE;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AAED,cAAI;AACA,iBAAK1G,UAAL,CAAgB2G,IAAhB,CAAqB3B,OAAO,CAACL,IAA7B;;AACA;AAAA;AAAA,sCAAS,QAAT,EAAoB,gBAAeK,OAAO,CAAChE,KAAM,WAAUgE,OAAO,CAACL,IAAR,CAAaiC,UAAW,EAAnF;AAEH,WAJD,CAIE,OAAO7C,GAAP,EAAY;AACV;AAAA;AAAA,sCAAS,QAAT,EAAoB,+BAA8BA,GAAI,EAAtD;AACH;AACJ;AAED;AACJ;AACA;;;AACYyC,QAAAA,qBAAqB,CAACV,MAAD,EAAiBd,OAAjB,EAA+D;AACxF;AACA;AAEA,cAAIA,OAAO,YAAYN,UAAvB,EAAmC;AAC/B,kBAAM6B,UAAuB,GAAG;AAC5BvF,cAAAA,KAAK,EAAE8E,MADqB;AAE5Be,cAAAA,GAAG,EAAE,KAAKhG,WAAL,EAFuB;AAG5B8D,cAAAA,IAAI,EAAEK;AAHsB,aAAhC;AAKA,mBAAOuB,UAAP;AACH;;AAED,cAAIpB,GAAG,GAAG,IAAI;AAAA;AAAA,kCAAQzD,EAAR,CAAWoF,MAAf,EAAV;AACA3B,UAAAA,GAAG,CAACQ,GAAJ,GAAUG,MAAV;AACAX,UAAAA,GAAG,CAAC0B,GAAJ,GAAU,KAAKhG,WAAL,EAAV;AACAsE,UAAAA,GAAG,CAAC4B,IAAJ,GAAW/B,OAAX;AAEA,gBAAMgC,UAAU,GAAG;AAAA;AAAA,kCAAQtF,EAAR,CAAWoF,MAAX,CAAkBG,MAAlB,CAAyB9B,GAAzB,EAA8B+B,MAA9B,EAAnB;AACA,gBAAMX,UAAuB,GAAG;AAC5BvF,YAAAA,KAAK,EAAE8E,MADqB;AAE5Be,YAAAA,GAAG,EAAE,KAAKhG,WAAL,EAFuB;AAG5B8D,YAAAA,IAAI,EAAEqC;AAHsB,WAAhC;AAKA,iBAAOT,UAAP;AACH;AAED;AACJ;AACA;;;AACY/D,QAAAA,aAAa,GAAS;AAC1B;AACA,gBAAM2E,aAAqC,GAAG;AAC1CC,YAAAA,SAAS,EAAE;AACPC,cAAAA,UAAU,EAAE;AAAA;AAAA,gCAAKC,UAAL,CAAgB7E,IAAI,CAACC,GAAL,EAAhB,CADL;AACkC;AACzC6E,cAAAA,WAAW,EAAE,CAFN,CAES;;AAFT;AAD+B,WAA9C;AAMA,eAAKtD,WAAL,CAAiB;AAAA;AAAA,kCAAQvC,EAAR,CAAWC,MAAX,CAAkBG,gBAAnC,EAAqDqF,aAArD;AACH;AAED;AACJ;AACA;;;AACIK,QAAAA,gBAAgB,GAAS;AACrB,eAAK9G,gBAAL,CAAsBc,KAAtB;;AACA;AAAA;AAAA,kCAAQ,QAAR,EAAkB,8BAAlB;AACH;AAED;AACJ;AACA;;;AACIiG,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAK7G,aAAL,CAAmBwB,MAA1B;AACH;;AAEDL,QAAAA,WAAW,CAACoD,GAAD,EAAgC;AACvC;AAAA;AAAA,oCAAS,QAAT,EAAoB,eAAcA,GAAI,EAAtC;AACH;;AAEDtD,QAAAA,YAAY,CAACsD,GAAD,EAAgC;AACxC;AAAA;AAAA,oCAAS,QAAT,EAAoB,gBAAeA,GAAI,EAAvC;;AACA,cAAIA,GAAG,CAACuC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,uBAAsB1C,GAAG,CAACuC,QAAS,EAAtD;AACA;AACH;;AACD,cAAII,UAAU,GAAG3C,GAAG,CAAC4B,IAAJ,CAAS7C,WAA1B;;AACA,cAAI,CAAC4D,UAAL,EAAiB;AACb;AAAA;AAAA,oCAAQ,QAAR,EAAkB,2BAAlB;AACA;AACH;;AACD;AAAA;AAAA,kCAAQ,QAAR,EAAmB,gBAAeA,UAAU,CAACC,MAAO,IAAGD,UAAU,CAACE,QAAS,EAA3E;AACA,eAAK/D,WAAL,CAAiB;AAAA;AAAA,kCAAQvC,EAAR,CAAWC,MAAX,CAAkBK,eAAnC,EAAoD;AAChDiG,YAAAA,QAAQ,EAAE;AACNC,cAAAA,GAAG,EAAE;AAAA;AAAA,gCAAKC,IADJ;AAENC,cAAAA,OAAO,EAAE;AAFH;AADsC,WAApD;AAMH;;AAEDnG,QAAAA,SAAS,CAACkD,GAAD,EAAgC;AACrC,cAAIA,GAAG,CAACuC,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,EAAmB,oBAAmB1C,GAAG,CAACuC,QAAS,EAAnD;AACA;AACH;;AACD,cAAIW,OAAO,GAAGlD,GAAG,CAAC4B,IAAJ,CAASkB,QAAvB;;AACA,cAAI,CAACI,OAAL,EAAc;AACV;AAAA;AAAA,oCAAQ,QAAR,EAAkB,wBAAlB;AACA;AACH;AACJ;;AACDC,QAAAA,gBAAgB,GAAS;AACrB,eAAKlI,kBAAL,GAA0B,KAAKC,qBAA/B;AACH;;AApb4B,O", "sourcesContent": ["import { _decorator } from \"cc\";\r\nimport Long from 'long'\r\nimport csproto from '../AutoGen/PB/cs_proto.js';\r\nimport { IMgr } from '../IMgr';\r\nimport { logDebug, logError, logInfo, logWarn } from '../Utils/Logger';\r\nconst { ccclass } = _decorator;\r\n\r\nexport enum NetStatus {\r\n    NotConnect = 0,\r\n    Connecting = 1,\r\n    ServerPassed = 2,\r\n    Disconnected = 3,    // 服务器或客户端主动断开, 该状态不会自动重连\r\n    Connected = 4,\r\n}\r\n\r\nexport interface INetMessage {\r\n    msgId: number;\r\n    data: Uint8Array;\r\n    seq?: number;\r\n}\r\n\r\nexport interface IMessageHandler {\r\n    (data: csproto.cs.IS2CMsg): void;\r\n}\r\nexport class LoginInfo {\r\n    accountType: csproto.cs.ACCOUNT_TYPE;\r\n    code: string;\r\n    serverAddr: string;\r\n}\r\n\r\n@ccclass(\"NetMgr\")\r\nexport class NetMgr extends IMgr {\r\n    private _websocket: WebSocket | null = null;\r\n    private _status: NetStatus = NetStatus.NotConnect;\r\n    private loginInfo: LoginInfo = null;\r\n    private _reconnectAttempts: number = 0;\r\n    private _maxReconnectAttempts: number = 5;\r\n    private _reconnectDelay: number = 3000; // 3 seconds\r\n    private _heartbeatInterval: number = 3000; // 30 seconds\r\n    private _heartbeatTimer: number = 0;\r\n    private _lastHeartbeatTime: number = 0;\r\n    private _messageHandlers: Map<number, IMessageHandler[]> = new Map();\r\n    private _messageQueue: INetMessage[] = [];\r\n    private _currentSeq: number = 1;\r\n\r\n    private registerInInitHandlerBound: Map<number, IMessageHandler> = new Map();\r\n\r\n    private initRegistered(msgId: number, handler: IMessageHandler): void {\r\n        handler = handler.bind(this);\r\n        this.registerInInitHandlerBound.set(msgId, handler);\r\n        this.registerHandler(msgId, handler)\r\n    }\r\n    private uninitRegistered(): void {\r\n        this.registerInInitHandlerBound.forEach((handler, msgId) => {\r\n            this.unregisterHandler(msgId, handler);\r\n        });\r\n        this.registerInInitHandlerBound.clear();\r\n    }\r\n\r\n    init(): void {\r\n        logInfo(\"NetMgr\", \"Network manager initialized\");\r\n\r\n        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession);\r\n        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat);\r\n        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole);\r\n    }\r\n\r\n    unInit(): void {\r\n        this.uninitRegistered()\r\n\r\n        this.disconnect();\r\n        this._messageHandlers.clear();\r\n        this._messageQueue.length = 0;\r\n        super.unInit();\r\n    }\r\n\r\n    onUpdate(dt: number): void {\r\n        this._heartbeatTimer += dt * 1000;\r\n\r\n        // Send heartbeat\r\n        if (this._status === NetStatus.Connected &&\r\n            this._heartbeatTimer >= this._heartbeatInterval) {\r\n            this.sendHeartbeat();\r\n            this._heartbeatTimer = 0;\r\n        }\r\n\r\n        // Check connection timeout\r\n        if (this._status === NetStatus.Connected &&\r\n            Date.now() - this._lastHeartbeatTime > this._heartbeatInterval * 2) {\r\n            logWarn(\"NetMgr\", \"Connection timeout, attempting to reconnect\");\r\n            this.handleDisconnection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect to server\r\n     * @param url WebSocket server URL\r\n     */\r\n    connect(): void {\r\n        if (this._status === NetStatus.Connecting || this._status === NetStatus.Connected) {\r\n            logWarn(\"NetMgr\", \"Already connecting or connected\");\r\n            return;\r\n        }\r\n\r\n        this._status = NetStatus.Connecting;\r\n        this._reconnectAttempts = 0;\r\n\r\n        logInfo(\"NetMgr\", `Connecting to ${this.loginInfo.serverAddr}`);\r\n        this.createWebSocket();\r\n    }\r\n\r\n    login(info: LoginInfo): void {\r\n        this.loginInfo = info;\r\n        this.connect();\r\n    }\r\n\r\n    /**\r\n     * Disconnect from server\r\n     */\r\n    disconnect(): void {\r\n        if (this._websocket) {\r\n            this._websocket.close();\r\n            this._websocket = null;\r\n        }\r\n        this._status = NetStatus.Disconnected;\r\n        this._reconnectAttempts = 0;\r\n        logInfo(\"NetMgr\", \"Disconnected from server\");\r\n    }\r\n\r\n    /**\r\n     * Get current connection status\r\n     */\r\n    getStatus(): NetStatus {\r\n        return this._status;\r\n    }\r\n\r\n    /**\r\n     * Check if connected\r\n     */\r\n    isConnected(): boolean {\r\n        return this._status === NetStatus.Connected;\r\n    }\r\n\r\n    /**\r\n     * Create WebSocket connection\r\n     */\r\n    private createWebSocket(): void {\r\n        try {\r\n            this._websocket = new WebSocket(this.loginInfo.serverAddr);\r\n            this._websocket.binaryType = 'arraybuffer';\r\n\r\n            this._websocket.onopen = this.onWebSocketOpen.bind(this);\r\n            this._websocket.onmessage = this.onWebSocketMessage.bind(this);\r\n            this._websocket.onclose = this.onWebSocketClose.bind(this);\r\n            this._websocket.onerror = this.onWebSocketError.bind(this);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to create WebSocket: ${err}`);\r\n            this.handleDisconnection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * WebSocket open event handler\r\n     */\r\n    private onWebSocketOpen(_event: Event): void {\r\n        logInfo(\"NetMgr\", \"WebSocket connected\");\r\n        this._status = NetStatus.Connected;\r\n        this._reconnectAttempts = 0;\r\n        this._lastHeartbeatTime = Date.now();\r\n\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, {\r\n            get_session: {\r\n                account_type: this.loginInfo.accountType, // 账号类型\r\n                platform: csproto.cs.PLATFORM.PLATFORM_EDITOR, // 平台类型\r\n                code: this.loginInfo.code, // 账号名\r\n                version: 1, // 版本号\r\n            }\r\n        })\r\n\r\n    }\r\n\r\n    /**\r\n     * WebSocket message event handler\r\n     */\r\n    private onWebSocketMessage(event: MessageEvent): void {\r\n        try {\r\n            logDebug(\"NetMgr\", `WebSocket message received ${event}`);\r\n            const buffer = new Uint8Array(event.data);\r\n            this.handleMessage(buffer);\r\n            this._lastHeartbeatTime = Date.now();\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to handle message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * WebSocket close event handler\r\n     */\r\n    private onWebSocketClose(event: CloseEvent): void {\r\n        logInfo(\"NetMgr\", `WebSocket closed: ${event.code} - ${event.reason}`);\r\n        this.handleDisconnection();\r\n    }\r\n\r\n    /**\r\n     * WebSocket error event handler\r\n     */\r\n    private onWebSocketError(_event: Event): void {\r\n        logError(\"NetMgr\", \"WebSocket error occurred\");\r\n        this.handleDisconnection();\r\n    }\r\n\r\n    /**\r\n     * Handle disconnection and attempt reconnection\r\n     */\r\n    private handleDisconnection(): void {\r\n        if (this._websocket) {\r\n            this._websocket.close();\r\n            this._websocket = null;\r\n        }\r\n\r\n        this._status = NetStatus.NotConnect;\r\n\r\n        // Attempt reconnection if not manually disconnected\r\n        if (this._reconnectAttempts < this._maxReconnectAttempts) {\r\n            this._reconnectAttempts++;\r\n            logInfo(\"NetMgr\", `Attempting reconnection ${this._reconnectAttempts}/${this._maxReconnectAttempts}`);\r\n\r\n            setTimeout(() => {\r\n                if (this.loginInfo.serverAddr && this._status !== NetStatus.Disconnected) {\r\n                    this.connect();\r\n                }\r\n            }, this._reconnectDelay);\r\n        } else {\r\n            logError(\"NetMgr\", \"Max reconnection attempts reached\");\r\n            this._status = NetStatus.Disconnected;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Process queued messages\r\n     */\r\n    private processMessageQueue(): void {\r\n        while (this._messageQueue.length > 0 && this.isConnected()) {\r\n            const message = this._messageQueue.shift();\r\n            if (message) {\r\n                this.sendRawMessage(message);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Handle incoming message\r\n     */\r\n    private handleMessage(buffer: Uint8Array): void {\r\n        try {\r\n            // Parse message header (assuming first 4 bytes are message ID)\r\n            var msg = csproto.cs.S2CMsg.decode(buffer)\r\n            logDebug(\"NetMgr\", `Received message ${JSON.stringify(msg)}`);\r\n            this.dispatchMessage(msg);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to parse message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Dispatch message to registered handlers\r\n     */\r\n    private dispatchMessage(msg: csproto.cs.IS2CMsg): void {\r\n        const handlers = this._messageHandlers.get(msg.cmd);\r\n        if (handlers && handlers.length > 0) {\r\n            try {\r\n                handlers.forEach(handler => {\r\n                    try {\r\n                        handler(msg);\r\n                    } catch (err) {\r\n                        logError(\"NetMgr\", `Handler error for msgId ${msg.cmd}: ${(err as Error).stack}`);\r\n                    }\r\n                });\r\n            } catch (err) {\r\n                logError(\"NetMgr\", `Failed to decode message ${msg.cmd}: ${(err as Error).stack}`);\r\n            }\r\n        } else {\r\n            logWarn(\"NetMgr\", `No handler registered for msgId: ${msg.cmd}}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Decode protobuf message based on message ID\r\n     */\r\n    private decodeProtobufMessage(_msgId: number, data: Uint8Array): any {\r\n        // This is a simplified example - you would need to map msgId to specific protobuf types\r\n        // For now, return the raw data\r\n        return data;\r\n    }\r\n\r\n    /**\r\n     * Register message handler\r\n     */\r\n    registerHandler(msgId: number, handler: IMessageHandler): void {\r\n        if (!this._messageHandlers.has(msgId)) {\r\n            this._messageHandlers.set(msgId, []);\r\n        }\r\n        this._messageHandlers.get(msgId)!.push(handler);\r\n        logInfo(\"NetMgr\", `Registered handler for msgId: ${msgId}`);\r\n    }\r\n\r\n    /**\r\n     * Unregister message handler\r\n     */\r\n    unregisterHandler(msgId: number, handler: IMessageHandler): void {\r\n        const handlers = this._messageHandlers.get(msgId);\r\n        if (handlers) {\r\n            const index = handlers.indexOf(handler);\r\n            if (index !== -1) {\r\n                handlers.splice(index, 1);\r\n                logInfo(\"NetMgr\", `Unregistered handler for msgId: ${msgId}`);\r\n\r\n                // Clean up empty handler arrays to prevent memory leaks\r\n                if (handlers.length === 0) {\r\n                    this._messageHandlers.delete(msgId);\r\n                    logInfo(\"NetMgr\", `Removed empty handler array for msgId: ${msgId}`);\r\n                }\r\n            } else {\r\n                logWarn(\"NetMgr\", `Handler not found for msgId: ${msgId}`);\r\n            }\r\n        } else {\r\n            logWarn(\"NetMgr\", `No handlers registered for msgId: ${msgId}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Send protobuf message\r\n     */\r\n    sendMessage(msgId: number, message: csproto.cs.IC2SMsgBody): void {\r\n        logDebug(\"NetMgr\", `sendMessage ${msgId} ${JSON.stringify(csproto.cs.C2SMsgBody.create(message))}`);\r\n        try {\r\n            // Encode protobuf message\r\n            const netMessage = this.encodeProtobufMessage(msgId, message);\r\n\r\n            if (this.isConnected()) {\r\n                this.sendRawMessage(netMessage);\r\n            } else {\r\n                // Queue message if not connected\r\n                this._messageQueue.push(netMessage);\r\n                logInfo(\"NetMgr\", `Queued message ${msgId} (not connected)`);\r\n            }\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to send message ${msgId}: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Send raw message over WebSocket\r\n     */\r\n    private sendRawMessage(message: INetMessage): void {\r\n        if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {\r\n            logWarn(\"NetMgr\", \"WebSocket not ready for sending\");\r\n            return;\r\n        }\r\n\r\n        try {\r\n            this._websocket.send(message.data);\r\n            logDebug(\"NetMgr\", `Sent message ${message.msgId}, size: ${message.data.byteLength}`);\r\n\r\n        } catch (err) {\r\n            logError(\"NetMgr\", `Failed to send raw message: ${err}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Encode protobuf message\r\n     */\r\n    private encodeProtobufMessage(_msgId: number, message: csproto.cs.IC2SMsgBody): INetMessage {\r\n        // This is a simplified example - you would need to map msgId to specific protobuf types\r\n        // For now, if message is already Uint8Array, return it; otherwise encode as ClientData\r\n\r\n        if (message instanceof Uint8Array) {\r\n            const netMessage: INetMessage = {\r\n                msgId: _msgId,\r\n                seq: this._currentSeq++,\r\n                data: message\r\n            };\r\n            return netMessage;\r\n        }\r\n\r\n        var msg = new csproto.cs.C2SMsg()\r\n        msg.cmd = _msgId;\r\n        msg.seq = this._currentSeq++;\r\n        msg.body = message;\r\n\r\n        const clientData = csproto.cs.C2SMsg.encode(msg).finish();\r\n        const netMessage: INetMessage = {\r\n            msgId: _msgId,\r\n            seq: this._currentSeq++,\r\n            data: clientData,\r\n        }\r\n        return netMessage;\r\n    }\r\n\r\n    /**\r\n     * Send heartbeat message\r\n     */\r\n    private sendHeartbeat(): void {\r\n        // Send a simple heartbeat message (you can define a specific heartbeat message type)\r\n        const heartbeatData: csproto.cs.IC2SMsgBody = {\r\n            heartbeat: {\r\n                clent_time: Long.fromNumber(Date.now()), // 客户端时间\r\n                is_fighting: 0, // 是否战斗中\r\n            }\r\n        };\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, heartbeatData);\r\n    }\r\n\r\n    /**\r\n     * Clear all message handlers\r\n     */\r\n    clearAllHandlers(): void {\r\n        this._messageHandlers.clear();\r\n        logInfo(\"NetMgr\", \"Cleared all message handlers\");\r\n    }\r\n\r\n    /**\r\n     * Get message queue length\r\n     */\r\n    getQueueLength(): number {\r\n        return this._messageQueue.length;\r\n    }\r\n\r\n    onHeartbeat(msg: csproto.cs.IS2CMsg): void {\r\n        logDebug(\"NetMgr\", `onHeartbeat ${msg}`);\r\n    }\r\n\r\n    onGetSession(msg: csproto.cs.IS2CMsg): void {\r\n        logDebug(\"NetMgr\", `onGetSession ${msg}`);\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetSession failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var sessionRsp = msg.body.get_session;\r\n        if (!sessionRsp) {\r\n            logWarn(\"NetMgr\", \"onGetSession data is null\");\r\n            return;\r\n        }\r\n        logInfo(\"NetMgr\", `onGetSession ${sessionRsp.openid}:${sessionRsp.uin_list}`);\r\n        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, {\r\n            get_role: {\r\n                uin: Long.ZERO,\r\n                area_id: 0,\r\n            }\r\n        })\r\n    }\r\n\r\n    onGetRole(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetRole failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var roleRsp = msg.body.get_role;\r\n        if (!roleRsp) {\r\n            logWarn(\"NetMgr\", \"onGetRole data is null\");\r\n            return;\r\n        }\r\n    }\r\n    disableReconnect(): void {\r\n        this._reconnectAttempts = this._maxReconnectAttempts;\r\n    }\r\n}\r\n"]}