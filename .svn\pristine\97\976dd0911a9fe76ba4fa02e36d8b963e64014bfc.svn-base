import { _decorator } from "cc";
import Long from 'long'
import csproto from '../AutoGen/PB/cs_proto.js';
import { IMgr } from '../IMgr';
import { logDebug, logError, logInfo, logWarn } from '../Utils/Logger';
const { ccclass } = _decorator;

export enum NetStatus {
    NotConnect = 0,
    Connecting = 1,
    ServerPassed = 2,
    Disconnected = 3,    // 服务器或客户端主动断开, 该状态不会自动重连
    Connected = 4,
}

export interface INetMessage {
    msgId: number;
    data: Uint8Array;
    seq?: number;
}

export interface IMessageHandler {
    (data: csproto.cs.IS2CMsg): void;
}
export class LoginInfo {
    accountType: csproto.cs.ACCOUNT_TYPE;
    code: string;
    serverAddr: string;
}

@ccclass("NetMgr")
export class NetMgr extends IMgr {
    private _websocket: WebSocket | null = null;
    private _status: NetStatus = NetStatus.NotConnect;
    private loginInfo: LoginInfo = null;
    private _reconnectAttempts: number = 0;
    private _maxReconnectAttempts: number = 5;
    private _reconnectDelay: number = 3000; // 3 seconds
    private _heartbeatInterval: number = 3000; // 30 seconds
    private _heartbeatTimer: number = 0;
    private _lastHeartbeatTime: number = 0;
    private _messageHandlers: Map<number, IMessageHandler[]> = new Map();
    private _messageQueue: INetMessage[] = [];
    private _currentSeq: number = 1;

    private registerInInitHandlerBound: Map<number, IMessageHandler> = new Map();

    private initRegistered(msgId: number, handler: IMessageHandler): void {
        handler = handler.bind(this);
        this.registerInInitHandlerBound.set(msgId, handler);
        this.registerHandler(msgId, handler)
    }
    private uninitRegistered(): void {
        this.registerInInitHandlerBound.forEach((handler, msgId) => {
            this.unregisterHandler(msgId, handler);
        });
        this.registerInInitHandlerBound.clear();
    }

    init(): void {
        logInfo("NetMgr", "Network manager initialized");

        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession);
        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat);
        this.initRegistered(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole);
    }

    unInit(): void {
        this.uninitRegistered()

        this.disconnect();
        this._messageHandlers.clear();
        this._messageQueue.length = 0;
        super.unInit();
    }

    onUpdate(dt: number): void {
        this._heartbeatTimer += dt * 1000;

        // Send heartbeat
        if (this._status === NetStatus.Connected &&
            this._heartbeatTimer >= this._heartbeatInterval) {
            this.sendHeartbeat();
            this._heartbeatTimer = 0;
        }

        // Check connection timeout
        if (this._status === NetStatus.Connected &&
            Date.now() - this._lastHeartbeatTime > this._heartbeatInterval * 2) {
            logWarn("NetMgr", "Connection timeout, attempting to reconnect");
            this.handleDisconnection();
        }
    }

    /**
     * Connect to server
     * @param url WebSocket server URL
     */
    connect(): void {
        if (this._status === NetStatus.Connecting || this._status === NetStatus.Connected) {
            logWarn("NetMgr", "Already connecting or connected");
            return;
        }

        this._status = NetStatus.Connecting;
        this._reconnectAttempts = 0;

        logInfo("NetMgr", `Connecting to ${this.loginInfo.serverAddr}`);
        this.createWebSocket();
    }

    login(info: LoginInfo): void {
        this.loginInfo = info;
        this.connect();
    }

    /**
     * Disconnect from server
     */
    disconnect(): void {
        if (this._websocket) {
            this._websocket.close();
            this._websocket = null;
        }
        this._status = NetStatus.Disconnected;
        this._reconnectAttempts = 0;
        logInfo("NetMgr", "Disconnected from server");
    }

    /**
     * Get current connection status
     */
    getStatus(): NetStatus {
        return this._status;
    }

    /**
     * Check if connected
     */
    isConnected(): boolean {
        return this._status === NetStatus.Connected;
    }

    /**
     * Create WebSocket connection
     */
    private createWebSocket(): void {
        try {
            this._websocket = new WebSocket(this.loginInfo.serverAddr);
            this._websocket.binaryType = 'arraybuffer';

            this._websocket.onopen = this.onWebSocketOpen.bind(this);
            this._websocket.onmessage = this.onWebSocketMessage.bind(this);
            this._websocket.onclose = this.onWebSocketClose.bind(this);
            this._websocket.onerror = this.onWebSocketError.bind(this);

        } catch (err) {
            logError("NetMgr", `Failed to create WebSocket: ${err}`);
            this.handleDisconnection();
        }
    }

    /**
     * WebSocket open event handler
     */
    private onWebSocketOpen(_event: Event): void {
        logInfo("NetMgr", "WebSocket connected");
        this._status = NetStatus.Connected;
        this._reconnectAttempts = 0;
        this._lastHeartbeatTime = Date.now();

        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_SESSION, {
            get_session: {
                account_type: this.loginInfo.accountType, // 账号类型
                platform: csproto.cs.PLATFORM.PLATFORM_EDITOR, // 平台类型
                code: this.loginInfo.code, // 账号名
                version: 1, // 版本号
            }
        })

    }

    /**
     * WebSocket message event handler
     */
    private onWebSocketMessage(event: MessageEvent): void {
        try {
            logDebug("NetMgr", `WebSocket message received ${event}`);
            const buffer = new Uint8Array(event.data);
            this.handleMessage(buffer);
            this._lastHeartbeatTime = Date.now();
        } catch (err) {
            logError("NetMgr", `Failed to handle message: ${err}`);
        }
    }

    /**
     * WebSocket close event handler
     */
    private onWebSocketClose(event: CloseEvent): void {
        logInfo("NetMgr", `WebSocket closed: ${event.code} - ${event.reason}`);
        this.handleDisconnection();
    }

    /**
     * WebSocket error event handler
     */
    private onWebSocketError(_event: Event): void {
        logError("NetMgr", "WebSocket error occurred");
        this.handleDisconnection();
    }

    /**
     * Handle disconnection and attempt reconnection
     */
    private handleDisconnection(): void {
        if (this._websocket) {
            this._websocket.close();
            this._websocket = null;
        }

        this._status = NetStatus.NotConnect;

        // Attempt reconnection if not manually disconnected
        if (this._reconnectAttempts < this._maxReconnectAttempts) {
            this._reconnectAttempts++;
            logInfo("NetMgr", `Attempting reconnection ${this._reconnectAttempts}/${this._maxReconnectAttempts}`);

            setTimeout(() => {
                if (this.loginInfo.serverAddr && this._status !== NetStatus.Disconnected) {
                    this.connect();
                }
            }, this._reconnectDelay);
        } else {
            logError("NetMgr", "Max reconnection attempts reached");
            this._status = NetStatus.Disconnected;
        }
    }

    /**
     * Process queued messages
     */
    private processMessageQueue(): void {
        while (this._messageQueue.length > 0 && this.isConnected()) {
            const message = this._messageQueue.shift();
            if (message) {
                this.sendRawMessage(message);
            }
        }
    }

    /**
     * Handle incoming message
     */
    private handleMessage(buffer: Uint8Array): void {
        try {
            // Parse message header (assuming first 4 bytes are message ID)
            var msg = csproto.cs.S2CMsg.decode(buffer)
            logDebug("NetMgr", `Received message ${JSON.stringify(msg)}`);
            this.dispatchMessage(msg);

        } catch (err) {
            logError("NetMgr", `Failed to parse message: ${err}`);
        }
    }

    /**
     * Dispatch message to registered handlers
     */
    private dispatchMessage(msg: csproto.cs.IS2CMsg): void {
        const handlers = this._messageHandlers.get(msg.cmd);
        if (handlers && handlers.length > 0) {
            try {
                handlers.forEach(handler => {
                    try {
                        handler(msg);
                    } catch (err) {
                        logError("NetMgr", `Handler error for msgId ${msg.cmd}: ${(err as Error).stack}`);
                    }
                });
            } catch (err) {
                logError("NetMgr", `Failed to decode message ${msg.cmd}: ${(err as Error).stack}`);
            }
        } else {
            logWarn("NetMgr", `No handler registered for msgId: ${msg.cmd}}`);
        }
    }

    /**
     * Decode protobuf message based on message ID
     */
    private decodeProtobufMessage(_msgId: number, data: Uint8Array): any {
        // This is a simplified example - you would need to map msgId to specific protobuf types
        // For now, return the raw data
        return data;
    }

    /**
     * Register message handler
     */
    registerHandler(msgId: number, handler: IMessageHandler): void {
        if (!this._messageHandlers.has(msgId)) {
            this._messageHandlers.set(msgId, []);
        }
        this._messageHandlers.get(msgId)!.push(handler);
        logInfo("NetMgr", `Registered handler for msgId: ${msgId}`);
    }

    /**
     * Unregister message handler
     */
    unregisterHandler(msgId: number, handler: IMessageHandler): void {
        const handlers = this._messageHandlers.get(msgId);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
                logInfo("NetMgr", `Unregistered handler for msgId: ${msgId}`);

                // Clean up empty handler arrays to prevent memory leaks
                if (handlers.length === 0) {
                    this._messageHandlers.delete(msgId);
                    logInfo("NetMgr", `Removed empty handler array for msgId: ${msgId}`);
                }
            } else {
                logWarn("NetMgr", `Handler not found for msgId: ${msgId}`);
            }
        } else {
            logWarn("NetMgr", `No handlers registered for msgId: ${msgId}`);
        }
    }

    /**
     * Send protobuf message
     */
    sendMessage(msgId: number, message: csproto.cs.IC2SMsgBody): void {
        logDebug("NetMgr", `sendMessage ${msgId} ${JSON.stringify(csproto.cs.C2SMsgBody.create(message))}`);
        try {
            // Encode protobuf message
            const netMessage = this.encodeProtobufMessage(msgId, message);

            if (this.isConnected()) {
                this.sendRawMessage(netMessage);
            } else {
                // Queue message if not connected
                this._messageQueue.push(netMessage);
                logInfo("NetMgr", `Queued message ${msgId} (not connected)`);
            }
        } catch (err) {
            logError("NetMgr", `Failed to send message ${msgId}: ${err}`);
        }
    }

    /**
     * Send raw message over WebSocket
     */
    private sendRawMessage(message: INetMessage): void {
        if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {
            logWarn("NetMgr", "WebSocket not ready for sending");
            return;
        }

        try {
            this._websocket.send(message.data);
            logDebug("NetMgr", `Sent message ${message.msgId}, size: ${message.data.byteLength}`);

        } catch (err) {
            logError("NetMgr", `Failed to send raw message: ${err}`);
        }
    }

    /**
     * Encode protobuf message
     */
    private encodeProtobufMessage(_msgId: number, message: csproto.cs.IC2SMsgBody): INetMessage {
        // This is a simplified example - you would need to map msgId to specific protobuf types
        // For now, if message is already Uint8Array, return it; otherwise encode as ClientData

        if (message instanceof Uint8Array) {
            const netMessage: INetMessage = {
                msgId: _msgId,
                seq: this._currentSeq++,
                data: message
            };
            return netMessage;
        }

        var msg = new csproto.cs.C2SMsg()
        msg.cmd = _msgId;
        msg.seq = this._currentSeq++;
        msg.body = message;

        const clientData = csproto.cs.C2SMsg.encode(msg).finish();
        const netMessage: INetMessage = {
            msgId: _msgId,
            seq: this._currentSeq++,
            data: clientData,
        }
        return netMessage;
    }

    /**
     * Send heartbeat message
     */
    private sendHeartbeat(): void {
        // Send a simple heartbeat message (you can define a specific heartbeat message type)
        const heartbeatData: csproto.cs.IC2SMsgBody = {
            heartbeat: {
                clent_time: Long.fromNumber(Date.now()), // 客户端时间
                is_fighting: 0, // 是否战斗中
            }
        };
        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_HEARTBEAT, heartbeatData);
    }

    /**
     * Clear all message handlers
     */
    clearAllHandlers(): void {
        this._messageHandlers.clear();
        logInfo("NetMgr", "Cleared all message handlers");
    }

    /**
     * Get message queue length
     */
    getQueueLength(): number {
        return this._messageQueue.length;
    }

    onHeartbeat(msg: csproto.cs.IS2CMsg): void {
        logDebug("NetMgr", `onHeartbeat ${msg}`);
    }

    onGetSession(msg: csproto.cs.IS2CMsg): void {
        logDebug("NetMgr", `onGetSession ${msg}`);
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetSession failed ${msg.ret_code}`);
            return;
        }
        var sessionRsp = msg.body.get_session;
        if (!sessionRsp) {
            logWarn("NetMgr", "onGetSession data is null");
            return;
        }
        logInfo("NetMgr", `onGetSession ${sessionRsp.openid}:${sessionRsp.uin_list}`);
        this.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, {
            get_role: {
                uin: Long.ZERO,
                area_id: 0,
            }
        })
    }

    onGetRole(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetRole failed ${msg.ret_code}`);
            return;
        }
        var roleRsp = msg.body.get_role;
        if (!roleRsp) {
            logWarn("NetMgr", "onGetRole data is null");
            return;
        }
    }
    disableReconnect(): void {
        this._reconnectAttempts = this._maxReconnectAttempts;
    }
}
