System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, LevelDataEventCondtion, LevelDataEventCondtionType, LevelDataEventCondtionWave, _crd;

  function _reportPossibleCrUseOfLevelDataEventCondtion(extras) {
    _reporterNs.report("LevelDataEventCondtion", "./LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionComb(extras) {
    _reporterNs.report("LevelDataEventCondtionComb", "./LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "./LevelDataEventCondtion", _context.meta, extras);
  }

  _export("LevelDataEventCondtionWave", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      LevelDataEventCondtion = _unresolved_2.LevelDataEventCondtion;
      LevelDataEventCondtionType = _unresolved_2.LevelDataEventCondtionType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bcae83VbM5BsrvhJuqLra3o", "LevelDataEventCondtionWave", undefined);

      _export("LevelDataEventCondtionWave", LevelDataEventCondtionWave = class LevelDataEventCondtionWave extends (_crd && LevelDataEventCondtion === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtion({
        error: Error()
      }), LevelDataEventCondtion) : LevelDataEventCondtion) {
        constructor(comb) {
          super(comb, (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
            error: Error()
          }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave);
          this.targetElemID = "";
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a4290da83039b78cbcb466a49f1b87ca17155fd6.js.map