{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/MainUI.ts"], "names": ["_decorator", "Component", "Sprite", "csproto", "MyApp", "DevLoginUI", "BattleUI", "BottomUI", "TopUI", "UIMgr", "logError", "logInfo", "WECHAT", "ccclass", "property", "MainUI", "onLoad", "start", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_KICK_OFF", "onKickOff", "needLogin", "platformSDK", "login", "err", "info", "openUI", "disableAllSprite", "uiClass", "ui", "get", "sprites", "node", "getComponentsInChildren", "for<PERSON>ach", "sprite", "enabled", "onDestroy", "unregister<PERSON><PERSON><PERSON>", "msg", "disableReconnect", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;AACzBC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;;AAEVC,MAAAA,M,UAAAA,M;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;wBAGjBe,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb,SAC4Bd,SAD5B,CACsC;AAClCe,QAAAA,MAAM,GAAS,CACd;;AAEU,cAALC,KAAK,GAAG;AACV;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAA/C,EAAgE,KAAKC,SAArE;;AACA,cAAI;AAAA;AAAA,wCAAWC,SAAf,EAA0B;AACtB,gBAAIZ,MAAJ,EAAY;AACR;AAAA;AAAA,kCAAMa,WAAN,CAAkBC,KAAlB,CAAwB,CAACC,GAAD,EAAMC,IAAN,KAAe;AACnC,oBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,4CAAS,QAAT,EAAoB,gBAAeA,GAAI,EAAvC;AACA;AACH;;AACD;AAAA;AAAA,oCAAMT,MAAN,CAAaQ,KAAb,CAAmBE,IAAnB;AACH,eAND;AAOH,aARD,MAQO;AACH,oBAAM;AAAA;AAAA,kCAAMC,MAAN;AAAA;AAAA,2CAAN;AACH;AACJ;;AACD,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,6BAAN,CAjBU,CAmBV;;AACA,cAAI,KAAJ,EAAW;AACP,gBAAIC,gBAAgB,GAAIC,OAAD,IAAa;AAChC,kBAAIC,EAAE,GAAG;AAAA;AAAA,kCAAMC,GAAN,CAAUF,OAAV,CAAT;AACA,kBAAIG,OAAO,GAAGF,EAAE,CAACG,IAAH,CAAQC,uBAAR,CAAgClC,MAAhC,CAAd;AACAgC,cAAAA,OAAO,CAACG,OAAR,CAAiBC,MAAD,IAAY;AACxBA,gBAAAA,MAAM,CAACC,OAAP,GAAiB,KAAjB;AACH,eAFD;AAGH,aAND;;AAOAT,YAAAA,gBAAgB;AAAA;AAAA,qCAAhB;AACAA,YAAAA,gBAAgB;AAAA;AAAA,qCAAhB;AACAA,YAAAA,gBAAgB;AAAA;AAAA,+BAAhB;AACH;AACJ;;AAESU,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,8BAAMtB,MAAN,CAAauB,iBAAb,CAA+B;AAAA;AAAA,kCAAQrB,EAAR,CAAWC,MAAX,CAAkBC,eAAjD,EAAkE,KAAKC,SAAvE;AACH;;AACDA,QAAAA,SAAS,CAACmB,GAAD,EAA0B;AAC/B;AAAA;AAAA,kCAAQ,QAAR,EAAkB,WAAlB;AACA;AAAA;AAAA,8BAAMxB,MAAN,CAAayB,gBAAb;;AACA,cAAI/B,MAAJ,EAAY,CACX,CADD,MACO;AACH;AAAA;AAAA,gCAAMiB,MAAN;AAAA;AAAA;AACH;AACJ;;AAEDe,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AApDiC,O", "sourcesContent": ["import { _decorator, Component, Sprite } from 'cc';\nimport csproto from './AutoGen/PB/cs_proto.js';\nimport { MyApp } from './MyApp';\nimport { DevLoginUI } from './ui/DevLoginUI';\nimport { BattleUI } from './ui/main/BattleUI';\nimport { BottomUI } from './ui/main/BottomUI';\nimport { TopUI } from './ui/main/TopUI';\nimport { UIMgr } from './ui/UIMgr';\nimport { logError, logInfo } from './Utils/Logger';\n\nimport { WECHAT } from 'cc/env';\n\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('MainUI')\nexport class MainUI extends Component {\n    onLoad(): void {\n    }\n\n    async start() {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff)\n        if (DevLoginUI.needLogin) {\n            if (WECHAT) {\n                MyApp.platformSDK.login((err, info) => {\n                    if (err) {\n                        logError(\"MainUI\", `login failed ${err}`);\n                        return;\n                    }\n                    MyApp.netMgr.login(info);\n                })\n            } else {\n                await UIMgr.openUI(DevLoginUI)\n            }\n        }\n        await UIMgr.openUI(BattleUI)\n        await UIMgr.openUI(BottomUI)\n        await UIMgr.openUI(TopUI)\n\n        // TODO 为了显示临时背景图，去掉各个UI的背景图 by binbin\n        if (false) {\n            let disableAllSprite = (uiClass) => {\n                let ui = UIMgr.get(uiClass)\n                let sprites = ui.node.getComponentsInChildren(Sprite)\n                sprites.forEach((sprite) => {\n                    sprite.enabled = false\n                })\n            }\n            disableAllSprite(BattleUI)\n            disableAllSprite(BottomUI)\n            disableAllSprite(TopUI)\n        }\n    }\n\n    protected onDestroy(): void {\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff)\n    }\n    onKickOff(msg: csproto.cs.IS2CMsg) {\n        logInfo(\"MainUI\", \"onKickOff\")\n        MyApp.netMgr.disableReconnect()\n        if (WECHAT) {\n        } else {\n            UIMgr.openUI(DevLoginUI)\n        }\n    }\n\n    update(deltaTime: number) {\n\n    }\n}\n\n"]}