System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, DataEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4ff57oMUJ1Gv7GKi2FOnT04", "DataEvent", undefined);

      _export("DataEvent", DataEvent = {
        ItemsRefresh: 'DataEvent_ItemsRefresh',
        EquipSlotRefresh: 'DataEvent_EquipSlotRefresh'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=436d5db630eec2a1fa875b96df376619571cf417.js.map