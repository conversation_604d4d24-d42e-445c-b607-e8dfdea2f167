{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendUI.ts"], "names": ["_decorator", "instantiate", "Label", "Node", "Prefab", "resources", "EventMgr", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BattleUI", "Tabs", "PlaneUIEvent", "TabStatus", "PopupUI", "ccclass", "property", "FriendUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onLoad", "tabs", "init", "updateLabelAfterColon", "LabelTimes", "tabBagBtn", "getComponentInChildren", "LabelUpdate", "timestamp", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "now", "btnClose", "addClick", "closeUI", "btnGet", "onPower", "btnIgnoreAll", "onIgnore", "btnAgreeAll", "onAgree", "btnRefreshAll", "onRefresh", "on", "TabChange", "onTabChange", "load", "err", "asset", "console", "error", "node", "panel1", "<PERSON><PERSON><PERSON><PERSON>", "log", "panel2", "panel3", "active", "node2", "tabStatus", "Bag", "node1", "label", "args", "originalText", "string", "colonIndex", "indexOf", "formattedValue", "length", "join", "prefix", "substring", "openUI", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAC9CC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;0BAGjBkB,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAGRc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACf,KAAD,C,WAERe,QAAQ,CAACf,KAAD,C,WAGRe,QAAQ,CAACd,IAAD,C,WAERc,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,2BA/Bb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAMd;AANc;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAiCb,eAANC,MAAM,GAAW;AAAE,iBAAO,yBAAP;AAAmC;;AAC9C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AAClDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,IAAL,CAAUC,IAAV;AACA,eAAKC,qBAAL,CAA2B,KAAKC,UAAhC,EAA4C,IAA5C,EAAkD,KAAlD;AACA,eAAKD,qBAAL,CAA2B,KAAKF,IAAL,CAAUI,SAAV,CAAoBC,sBAApB,CAA2C1B,KAA3C,CAA3B,EAA8E,IAA9E,EAAoF,KAApF;AACA,eAAKuB,qBAAL,CAA2B,KAAKI,WAAhC,EACI,CAAEC,SAAD,IAAuB;AACpB,kBAAMC,IAAI,GAAG,IAAIC,IAAJ,CAASF,SAAT,CAAb;AACA,kBAAMG,KAAK,GAAGF,IAAI,CAACG,QAAL,GAAgBC,QAAhB,GAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,kBAAMC,OAAO,GAAGN,IAAI,CAACO,UAAL,GAAkBH,QAAlB,GAA6BC,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CAAhB;AACA,mBAAQ,GAAEH,KAAM,IAAGI,OAAQ,GAA3B;AACH,WALD,EAKGL,IAAI,CAACO,GAAL,KAAa,IAAI,EAAJ,GAAS,EAAT,GAAc,IAL9B,CADJ;AAQA,eAAKC,QAAL,CAAcC,QAAd,CAAuB,KAAKC,OAA5B,EAAqC,IAArC;AACA,eAAKC,MAAL,CAAYF,QAAZ,CAAqB,KAAKG,OAA1B,EAAmC,IAAnC;AACA,eAAKC,YAAL,CAAkBJ,QAAlB,CAA2B,KAAKK,QAAhC,EAA0C,IAA1C;AACA,eAAKC,WAAL,CAAiBN,QAAjB,CAA0B,KAAKO,OAA/B,EAAwC,IAAxC;AACA,eAAKC,aAAL,CAAmBR,QAAnB,CAA4B,KAAKS,SAAjC,EAA4C,IAA5C;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,WAAzC,EAAsD,IAAtD;AAEAhD,UAAAA,SAAS,CAACiD,IAAV,CAAe,6BAAf,EAA8ClD,MAA9C,EAAsD,CAACmD,GAAD,EAAMC,KAAN,KAAgB;AAClE,gBAAID,GAAJ,EAAS;AACLE,cAAAA,OAAO,CAACC,KAAR,CAAc,SAAd,EAAyBH,GAAzB;AACA;AACH;;AAED,kBAAMI,IAAI,GAAG1D,WAAW,CAACuD,KAAD,CAAxB;AACA,iBAAKI,MAAL,CAAYC,QAAZ,CAAqBF,IAArB;AAEAF,YAAAA,OAAO,CAACK,GAAR,CAAY,mBAAZ;AACH,WAVD;AAYAzD,UAAAA,SAAS,CAACiD,IAAV,CAAe,4BAAf,EAA6ClD,MAA7C,EAAqD,CAACmD,GAAD,EAAMC,KAAN,KAAgB;AACjE,gBAAID,GAAJ,EAAS;AACLE,cAAAA,OAAO,CAACC,KAAR,CAAc,SAAd,EAAyBH,GAAzB;AACA;AACH;;AACD,kBAAMI,IAAI,GAAG1D,WAAW,CAACuD,KAAD,CAAxB;AACA,iBAAKO,MAAL,CAAYF,QAAZ,CAAqBF,IAArB;AACAF,YAAAA,OAAO,CAACK,GAAR,CAAY,mBAAZ;AACH,WARD;AAUAzD,UAAAA,SAAS,CAACiD,IAAV,CAAe,iCAAf,EAAkDlD,MAAlD,EAA0D,CAACmD,GAAD,EAAMC,KAAN,KAAgB;AACtE,gBAAID,GAAJ,EAAS;AACLE,cAAAA,OAAO,CAACC,KAAR,CAAc,SAAd,EAAyBH,GAAzB;AACA;AACH;;AACD,kBAAMI,IAAI,GAAG1D,WAAW,CAACuD,KAAD,CAAxB;AACA,iBAAKQ,MAAL,CAAYH,QAAZ,CAAqBF,IAArB;AACAF,YAAAA,OAAO,CAACK,GAAR,CAAY,mBAAZ;AACH,WARD;AASA,eAAKC,MAAL,CAAYE,MAAZ,GAAqB,KAArB;AACA,eAAKD,MAAL,CAAYC,MAAZ,GAAqB,KAArB;AACA,eAAKC,KAAL,CAAWD,MAAX,GAAoB,KAApB;AACH;;AACOZ,QAAAA,WAAW,CAACc,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,KAAL,CAAWJ,MAAX,GAAoB,IAApB;AACA,iBAAKC,KAAL,CAAWD,MAAX,GAAoB,KAApB;AACA,iBAAKL,MAAL,CAAYK,MAAZ,GAAqB,IAArB;AACA,iBAAKF,MAAL,CAAYE,MAAZ,GAAqB,KAArB;AACA,iBAAKD,MAAL,CAAYC,MAAZ,GAAqB,KAArB;AACH,WAND,MAMO;AACH,iBAAKI,KAAL,CAAWJ,MAAX,GAAoB,KAApB;AACA,iBAAKC,KAAL,CAAWD,MAAX,GAAoB,IAApB;AACA,iBAAKL,MAAL,CAAYK,MAAZ,GAAqB,KAArB;AACA,iBAAKF,MAAL,CAAYE,MAAZ,GAAqB,IAArB;AACA,iBAAKD,MAAL,CAAYC,MAAZ,GAAqB,IAArB;AACH;AACJ;;AACMxC,QAAAA,qBAAqB,CAAC6C,KAAD,EAAe,GAAGC,IAAlB,EAAwC;AAChE,gBAAMC,YAAY,GAAGF,KAAK,CAACG,MAA3B;AACA,cAAIC,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAjB;;AACA,cAAID,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBA,YAAAA,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAb,CADmB,CACqB;AAC3C;;AACD,cAAIC,cAAJ;;AACA,cAAIL,IAAI,CAACM,MAAL,KAAgB,CAApB,EAAuB;AACnBD,YAAAA,cAAc,GAAGL,IAAI,CAAC,CAAD,CAArB;AACH,WAFD,MAEO,IAAIA,IAAI,CAACM,MAAL,KAAgB,CAApB,EAAuB;AAC1BD,YAAAA,cAAc,GAAI,GAAEL,IAAI,CAAC,CAAD,CAAI,IAAGA,IAAI,CAAC,CAAD,CAAI,EAAvC;AACH,WAFM,MAEA,IAAIA,IAAI,CAACM,MAAL,GAAc,CAAlB,EAAqB;AACxBD,YAAAA,cAAc,GAAGL,IAAI,CAACO,IAAL,CAAU,GAAV,CAAjB;AACH,WAFM,MAEA;AACHF,YAAAA,cAAc,GAAG,EAAjB;AACH;;AACD,cAAIF,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBJ,YAAAA,KAAK,CAACG,MAAN,GAAgB,GAAED,YAAa,IAAGI,cAAe,EAAjD;AACA;AACH;;AACD,gBAAMG,MAAM,GAAGP,YAAY,CAACQ,SAAb,CAAuB,CAAvB,EAA0BN,UAAU,GAAG,CAAvC,CAAf,CApBgE,CAoBN;;AAC1DJ,UAAAA,KAAK,CAACG,MAAN,GAAgB,GAAEM,MAAO,GAAEH,cAAe,EAA1C;AACH;;AAEY,cAAPlC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcxB,QAAd;AACA,gBAAM;AAAA;AAAA,8BAAM+D,MAAN;AAAA;AAAA,mCAAN;AACH;;AACOrC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAMqC,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACOnC,QAAAA,QAAQ,GAAG;AACf;AAAA;AAAA,8BAAMmC,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACOjC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAMiC,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACO/B,QAAAA,SAAS,GAAG;AAChB;AAAA;AAAA,8BAAM+B,MAAN;AAAA;AAAA,kCAAsB,OAAtB;AACH;;AAEW,cAANC,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AA1JgC,O;;;;;;;;;;iBAMpB,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGD,I;;;;;;;;;;;;iBAIM,I;;;;;;;iBAEC,I;;;;;;;iBAGP,I", "sourcesContent": ["import { _decorator, instantiate, Label, Node, Prefab, resources } from 'cc';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr } from '../../UIMgr';\r\nimport { BattleUI } from '../BattleUI';\r\nimport { Tabs } from '../plane/components/back_pack/Tabs';\r\nimport { PlaneUIEvent } from '../plane/PlaneEvent';\r\nimport { TabStatus } from '../plane/PlaneTypes';\r\nimport { PopupUI } from '../PopupUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendUI')\r\nexport class FriendUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus;\r\n\r\n    @property(Tabs)\r\n    tabs: Tabs = null; //标签页\r\n\r\n    @property(Node)\r\n    panel1: Node = null;\r\n    @property(Node)\r\n    panel2: Node = null;\r\n    @property(Node)\r\n    panel3: Node = null;\r\n\r\n    @property(Node)\r\n    node1: Node = null;\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus;\r\n    @property(Label)\r\n    LabelTimes: Label = null;\r\n    @property(Label)\r\n    LabelUpdate: Label = null;\r\n\r\n    @property(Node)\r\n    node2: Node = null;\r\n    @property(ButtonPlus)\r\n    btnIgnoreAll: ButtonPlus;\r\n    @property(ButtonPlus)\r\n    btnAgreeAll: ButtonPlus;\r\n    @property(ButtonPlus)\r\n    btnRefreshAll: ButtonPlus;\r\n\r\n    public static getUrl(): string { return \"ui/main/friend/FriendUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    protected onLoad(): void {\r\n        this.tabs.init();\r\n        this.updateLabelAfterColon(this.LabelTimes, \"50\", \"100\");\r\n        this.updateLabelAfterColon(this.tabs.tabBagBtn.getComponentInChildren(Label), \"30\", \"100\");\r\n        this.updateLabelAfterColon(this.LabelUpdate,\r\n            ((timestamp: number) => {\r\n                const date = new Date(timestamp);\r\n                const hours = date.getHours().toString().padStart(2, '0');\r\n                const minutes = date.getMinutes().toString().padStart(2, '0');\r\n                return `${hours}时${minutes}分`;\r\n            })(Date.now() + 3 * 60 * 60 * 1000)\r\n        );\r\n        this.btnClose.addClick(this.closeUI, this);\r\n        this.btnGet.addClick(this.onPower, this);\r\n        this.btnIgnoreAll.addClick(this.onIgnore, this);\r\n        this.btnAgreeAll.addClick(this.onAgree, this);\r\n        this.btnRefreshAll.addClick(this.onRefresh, this);\r\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this);\r\n\r\n        resources.load(\"ui/main/friend/FriendListUI\", Prefab, (err, asset) => {\r\n            if (err) {\r\n                console.error(\"资源加载失败:\", err);\r\n                return;\r\n            }\r\n\r\n            const node = instantiate(asset);\r\n            this.panel1.addChild(node);\r\n\r\n            console.log(\"资源加载并添加到 panel 完成\");\r\n        });\r\n\r\n        resources.load(\"ui/main/friend/FriendAddUI\", Prefab, (err, asset) => {\r\n            if (err) {\r\n                console.error(\"资源加载失败:\", err);\r\n                return;\r\n            }\r\n            const node = instantiate(asset);\r\n            this.panel2.addChild(node);\r\n            console.log(\"资源加载并添加到 panel 完成\");\r\n        });\r\n\r\n        resources.load(\"ui/main/friend/FriendStrangerUI\", Prefab, (err, asset) => {\r\n            if (err) {\r\n                console.error(\"资源加载失败:\", err);\r\n                return;\r\n            }\r\n            const node = instantiate(asset);\r\n            this.panel3.addChild(node);\r\n            console.log(\"资源加载并添加到 panel 完成\");\r\n        });\r\n        this.panel2.active = false;\r\n        this.panel3.active = false;\r\n        this.node2.active = false;\r\n    }\r\n    private onTabChange(tabStatus: TabStatus) {\r\n        if (tabStatus == TabStatus.Bag) {\r\n            this.node1.active = true;\r\n            this.node2.active = false;\r\n            this.panel1.active = true;\r\n            this.panel2.active = false;\r\n            this.panel3.active = false;\r\n        } else {\r\n            this.node1.active = false;\r\n            this.node2.active = true;\r\n            this.panel1.active = false;\r\n            this.panel2.active = true;\r\n            this.panel3.active = true;\r\n        }\r\n    }\r\n    public updateLabelAfterColon(label: Label, ...args: string[]): void {\r\n        const originalText = label.string;\r\n        let colonIndex = originalText.indexOf(\":\");\r\n        if (colonIndex === -1) {\r\n            colonIndex = originalText.indexOf(\"：\"); // 中文冒号\r\n        }\r\n        let formattedValue: string;\r\n        if (args.length === 1) {\r\n            formattedValue = args[0];\r\n        } else if (args.length === 2) {\r\n            formattedValue = `${args[0]}/${args[1]}`;\r\n        } else if (args.length > 2) {\r\n            formattedValue = args.join(\",\");\r\n        } else {\r\n            formattedValue = \"\";\r\n        }\r\n        if (colonIndex === -1) {\r\n            label.string = `${originalText}:${formattedValue}`;\r\n            return;\r\n        }\r\n        const prefix = originalText.substring(0, colonIndex + 1); // 包含冒号\r\n        label.string = `${prefix}${formattedValue}`;\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(FriendUI);\r\n        await UIMgr.openUI(BattleUI)\r\n    }\r\n    private onPower() {\r\n        UIMgr.openUI(PopupUI, \"一键收赠\");\r\n    }\r\n    private onIgnore() {\r\n        UIMgr.openUI(PopupUI, \"全部忽略\");\r\n    }\r\n    private onAgree() {\r\n        UIMgr.openUI(PopupUI, \"全部同意\");\r\n    }\r\n    private onRefresh() {\r\n        UIMgr.openUI(PopupUI, \"刷新陌生人\");\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}"]}