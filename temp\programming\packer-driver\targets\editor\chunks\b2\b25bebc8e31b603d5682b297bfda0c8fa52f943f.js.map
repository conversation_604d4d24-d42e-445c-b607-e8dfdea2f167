{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts"], "names": ["_decorator", "Component", "Node", "UITransform", "DataMgr", "EventMgr", "logDebug", "List", "UIMgr", "PlaneEquipInfoUI", "PlaneUIEvent", "OpenEquipInfoUISource", "TabStatus", "BagItem", "ccclass", "property", "BagGrid", "_sortedItems", "_sortedPlaneParts", "_lineGridNum", "_separatorRow", "_tabStatus", "None", "onLoad", "separator", "removeFromParent", "mergeSelectMaskBg", "active", "on", "SortTypeChange", "onSortTypeChange", "BagItemClick", "onBagItemClick", "UpdateMergeEquipStatus", "onUpdateMergeEquipStatus", "onDestroy", "targetOff", "bagList", "updateAll", "item", "Bag", "openUI", "<PERSON><PERSON>", "isEmpty", "equip", "eqCombine", "size", "isFull", "getByGuid", "guid", "isMainMat", "item_id", "isCanCombineWith", "tabStatus", "equips", "items", "_customSize", "_resizeContent", "Math", "ceil", "length", "itemRowNum", "numItems", "scrollTo", "onListRenderInBagStatus", "listItem", "row", "name", "normalSize", "tmpNode", "getComponent", "contentSize", "itemUITrans", "children", "for<PERSON>ach", "v", "<PERSON><PERSON><PERSON><PERSON>", "setContentSize", "width", "height", "bagItems", "getComponentsInChildren", "index", "dataIndex", "node", "renderPlanePart", "onRenderItem", "onListRenderInMergeStatus", "onList<PERSON>ender"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAE7BC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,S,iBAAAA,S;;AACvBC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAGjBgB,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACb,IAAD,C,2BANb,MACac,OADb,SAC6Bf,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAQ3BgB,YAR2B,GAQU,EARV;AAAA,eAS3BC,iBAT2B,GASe,EATf;AAAA,eAU3BC,YAV2B,GAUJ,CAVI;AAAA,eAW3BC,aAX2B,GAWH,CAXG;AAAA,eAY3BC,UAZ2B,GAYH;AAAA;AAAA,sCAAUC,IAZP;AAAA;;AAcnCC,QAAAA,MAAM,GAAG;AACL,eAAKC,SAAL,CAAeC,gBAAf;AACA,eAAKC,iBAAL,CAAuBC,MAAvB,GAAgC,KAAhC;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,cAAzB,EAAyC,KAAKC,gBAA9C,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,YAAzB,EAAuC,KAAKC,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,sBAAzB,EAAiD,KAAKC,wBAAtD,EAAgF,IAAhF;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,wBAAwB,GAAG;AAC/B,eAAKG,OAAL,CAAaC,SAAb;AACH;AAED;;;AACQN,QAAAA,cAAc,CAACO,IAAD,EAA2B;AAC7C,kBAAQ,KAAKlB,UAAb;AACI,iBAAK;AAAA;AAAA,wCAAUmB,GAAf;AACI,mBAAKd,iBAAL,CAAuBC,MAAvB,GAAgC,KAAhC;AACA;AAAA;AAAA,kCAAMc,MAAN;AAAA;AAAA,wDAA+BF,IAA/B,EAAqC;AAAA;AAAA,kEAAsBvB,OAA3D;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAU0B,KAAf;AACI,oBAAMC,OAAO,GAAG;AAAA;AAAA,sCAAQC,KAAR,CAAcC,SAAd,CAAwBC,IAAxB,MAAkC,CAAlD;;AACA,kBAAI;AAAA;AAAA,sCAAQF,KAAR,CAAcC,SAAd,CAAwBE,MAAxB,MACA,CAAC;AAAA;AAAA,sCAAQH,KAAR,CAAcC,SAAd,CAAwBG,SAAxB,CAAkCT,IAAI,CAACU,IAAvC,CADD,IAECN,OAAO,IAAI,CAAC;AAAA;AAAA,sCAAQC,KAAR,CAAcC,SAAd,CAAwBK,SAAxB,CAAkCX,IAAI,CAACY,OAAvC,CAFb,IAGC,CAACR,OAAD,IAAY;AAAA;AAAA,sCAAQC,KAAR,CAAcC,SAAd,CAAwBO,gBAAxB,CAAyCb,IAAzC,CAHjB,EAIE;AACE;AACH;;AAED,mBAAKL,wBAAL;AACA;AAhBR;AAkBH;;AAEOJ,QAAAA,gBAAgB,CAACuB,SAAD,EAAuBC,MAAvB,EAAqDC,KAArD,EAAkF;AACtG,eAAKlC,UAAL,GAAkBgC,SAAlB;AACA,eAAK3B,iBAAL,CAAuBC,MAAvB,GAAgC,KAAhC;AACA,eAAKH,SAAL,CAAeG,MAAf,GAAwB,KAAxB;AACA,eAAKH,SAAL,CAAeC,gBAAf;AACA,eAAKY,OAAL,CAAamB,WAAb,GAA2B,EAA3B;;AACA,eAAKnB,OAAL,CAAaoB,cAAb;;AACA,kBAAQJ,SAAR;AACI,iBAAK;AAAA;AAAA,wCAAUb,GAAf;AACI,mBAAKvB,YAAL,GAAoBsC,KAApB;AACA,mBAAKrC,iBAAL,GAAyBoC,MAAzB;AACA,mBAAKlC,aAAL,GAAqBsC,IAAI,CAACC,IAAL,CAAU,KAAKzC,iBAAL,CAAuB0C,MAAvB,GAAgC,KAAKzC,YAA/C,CAArB;AACA,oBAAM0C,UAAU,GAAGH,IAAI,CAACC,IAAL,CAAU,KAAK1C,YAAL,CAAkB2C,MAAlB,GAA2B,KAAKzC,YAA1C,CAAnB;AACA,mBAAKkB,OAAL,CAAayB,QAAb,GAAwB,KAAK1C,aAAL,GAAqByC,UAArB,GAAkC,CAA1D;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAUnB,KAAf;AACI,mBAAKtB,aAAL,GAAqB,CAAC,CAAtB;AACA,mBAAKF,iBAAL,GAAyBoC,MAAzB;AACA,mBAAKjB,OAAL,CAAayB,QAAb,GAAwBJ,IAAI,CAACC,IAAL,CAAU,KAAKzC,iBAAL,CAAuB0C,MAAvB,GAAgC,KAAKzC,YAA/C,CAAxB;AACA;AAZR;;AAcA;AAAA;AAAA,oCAAS,SAAT,EAAqB,6BAA4B,KAAKkB,OAAL,CAAayB,QAAS,uBAAsB,KAAK1C,aAAc,EAAhH;AACA,eAAKiB,OAAL,CAAa0B,QAAb,CAAsB,CAAtB,EAAyB,CAAzB;AACH;;AAEDC,QAAAA,uBAAuB,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACjDD,UAAAA,QAAQ,CAACE,IAAT,GAAiB,GAAED,GAAI,EAAvB;;AACA,cAAIA,GAAG,IAAI,KAAK9C,aAAhB,EAA+B;AAC3B,kBAAMgD,UAAU,GAAG,KAAK/B,OAAL,CAAagC,OAAb,CAAqBC,YAArB,CAAkCnE,WAAlC,EAA+CoE,WAAlE;AACA,kBAAMC,WAAW,GAAGP,QAAQ,CAACK,YAAT,CAAsBnE,WAAtB,CAApB;AACA8D,YAAAA,QAAQ,CAACQ,QAAT,CAAkBC,OAAlB,CAA0BC,CAAC,IAAIA,CAAC,CAAChD,MAAF,GAAW,KAA1C;AACA,iBAAKH,SAAL,CAAeC,gBAAf;AACA,iBAAKD,SAAL,CAAeG,MAAf,GAAwB,IAAxB;AACAsC,YAAAA,QAAQ,CAACW,QAAT,CAAkB,KAAKpD,SAAvB;AACAgD,YAAAA,WAAW,CAACK,cAAZ,CAA2BT,UAAU,CAACU,KAAtC,EAA6CV,UAAU,CAACW,MAAX,GAAoB,CAAjE;AACA;AACH;;AAED,cAAId,QAAQ,CAACQ,QAAT,CAAkBb,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAKpC,SAAL,CAAeC,gBAAf;AACA,iBAAKD,SAAL,CAAeG,MAAf,GAAwB,KAAxB;AACH;;AAED,gBAAMqD,QAAQ,GAAGf,QAAQ,CAACgB,uBAAT;AAAA;AAAA,iCAAjB;;AACA,cAAIf,GAAG,GAAG,KAAK9C,aAAf,EAA8B;AAC1B,iBAAK,IAAI8D,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAACpB,MAArC,EAA6CsB,KAAK,EAAlD,EAAsD;AAClD,oBAAM3C,IAAI,GAAGyC,QAAQ,CAACE,KAAD,CAArB;AACA,oBAAMC,SAAS,GAAGjB,GAAG,GAAG,KAAK/C,YAAX,GAA0B+D,KAA5C;;AACA,kBAAIC,SAAS,IAAI,KAAKjE,iBAAL,CAAuB0C,MAAxC,EAAgD;AAC5CrB,gBAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,0CAAS,SAAT,EAAqB,8BAA6BuD,KAAM,cAAaC,SAAU,QAAOjB,GAAI,cAAa,KAAKhD,iBAAL,CAAuB0C,MAAO,EAArI;AACA;AACH;;AACDrB,cAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,IAAnB;AACAY,cAAAA,IAAI,CAAC8C,eAAL,CAAqB,KAAKnE,iBAAL,CAAuBiE,SAAvB,CAArB,EAAwD,KAAK9D,UAA7D;AACH;AACJ,WAZD,MAYO;AACH,iBAAK,IAAI6D,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAACpB,MAArC,EAA6CsB,KAAK,EAAlD,EAAsD;AAClD,oBAAM3C,IAAI,GAAGyC,QAAQ,CAACE,KAAD,CAArB;AACA,oBAAMC,SAAS,GAAG,CAACjB,GAAG,GAAG,KAAK9C,aAAX,GAA2B,CAA5B,IAAiC,KAAKD,YAAtC,GAAqD+D,KAAvE;;AACA,kBAAIC,SAAS,IAAI,KAAKlE,YAAL,CAAkB2C,MAAnC,EAA2C;AACvCrB,gBAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,KAAnB;AACA;AACH;;AACDY,cAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,IAAnB;AACAY,cAAAA,IAAI,CAAC+C,YAAL,CAAkB,KAAKrE,YAAL,CAAkBkE,SAAlB,CAAlB;AACH;AACJ;AACJ;;AAEOI,QAAAA,yBAAyB,CAACtB,QAAD,EAAiBC,GAAjB,EAA8B;AAC3D,gBAAMc,QAAQ,GAAGf,QAAQ,CAACgB,uBAAT;AAAA;AAAA,iCAAjB;;AACA,eAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAACpB,MAArC,EAA6CsB,KAAK,EAAlD,EAAsD;AAClD,kBAAM3C,IAAI,GAAGyC,QAAQ,CAACE,KAAD,CAArB;AACA,kBAAMC,SAAS,GAAGjB,GAAG,GAAG,KAAK/C,YAAX,GAA0B+D,KAA5C;;AACA,gBAAIC,SAAS,IAAI,KAAKjE,iBAAL,CAAuB0C,MAAxC,EAAgD;AAC5CrB,cAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,KAAnB;AACA;AACH;;AACDY,YAAAA,IAAI,CAAC6C,IAAL,CAAUzD,MAAV,GAAmB,IAAnB;AACAY,YAAAA,IAAI,CAAC8C,eAAL,CAAqB,KAAKnE,iBAAL,CAAuBiE,SAAvB,CAArB,EAAwD,KAAK9D,UAA7D;AACH;AACJ;;AAEDmE,QAAAA,YAAY,CAACvB,QAAD,EAAiBC,GAAjB,EAA8B;AACtCD,UAAAA,QAAQ,CAACE,IAAT,GAAiB,WAAUD,GAAI,EAA/B;;AACA,cAAI,KAAK7C,UAAL,IAAmB;AAAA;AAAA,sCAAUmB,GAAjC,EAAsC;AAClC,iBAAKwB,uBAAL,CAA6BC,QAA7B,EAAuCC,GAAvC;AACH,WAFD,MAEO;AACH,iBAAKqB,yBAAL,CAA+BtB,QAA/B,EAAyCC,GAAzC;AACH;AACJ;;AA/IkC,O;;;;;iBAEnB,I;;;;;;;iBAEE,I;;;;;;;iBAEQ,I", "sourcesContent": ["import { _decorator, Component, Node, UITransform } from 'cc';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { DataMgr } from 'db://assets/scripts/Data/DataManager';\nimport { EventMgr } from 'db://assets/scripts/event/EventManager';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport List from '../../../../common/components/list/List';\nimport { UIMgr } from '../../../../UIMgr';\nimport { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';\nimport { PlaneUIEvent } from '../../PlaneEvent';\nimport { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';\nimport { BagItem } from './BagItem';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('BagGrid')\nexport class BagGrid extends Component {\n    @property(List)\n    bagList: List = null;\n    @property(Node)\n    separator: Node = null;\n    @property(Node)\n    mergeSelectMaskBg: Node = null;\n\n    private _sortedItems: csproto.cs.ICSItem[] = [];\n    private _sortedPlaneParts: csproto.cs.ICSItem[] = [];\n    private _lineGridNum: number = 5;\n    private _separatorRow: number = 0;\n    private _tabStatus: TabStatus = TabStatus.None;\n\n    onLoad() {\n        this.separator.removeFromParent();\n        this.mergeSelectMaskBg.active = false;\n        EventMgr.on(PlaneUIEvent.SortTypeChange, this.onSortTypeChange, this);\n        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this)\n        EventMgr.on(PlaneUIEvent.UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onUpdateMergeEquipStatus() {\n        this.bagList.updateAll();\n    }\n\n    /*暂时只有装备点击*/\n    private onBagItemClick(item: csproto.cs.ICSItem) {\n        switch (this._tabStatus) {\n            case TabStatus.Bag:\n                this.mergeSelectMaskBg.active = false;\n                UIMgr.openUI(PlaneEquipInfoUI, item, OpenEquipInfoUISource.BagGrid)\n                break;\n            case TabStatus.Merge:\n                const isEmpty = DataMgr.equip.eqCombine.size() == 0\n                if (DataMgr.equip.eqCombine.isFull() &&\n                    !DataMgr.equip.eqCombine.getByGuid(item.guid) &&\n                    (isEmpty && !DataMgr.equip.eqCombine.isMainMat(item.item_id)) &&\n                    (!isEmpty && DataMgr.equip.eqCombine.isCanCombineWith(item))\n                ) {\n                    return\n                }\n\n                this.onUpdateMergeEquipStatus();\n                break;\n        }\n    }\n\n    private onSortTypeChange(tabStatus: TabStatus, equips: csproto.cs.ICSItem[], items: csproto.cs.ICSItem[]) {\n        this._tabStatus = tabStatus;\n        this.mergeSelectMaskBg.active = false;\n        this.separator.active = false\n        this.separator.removeFromParent();\n        this.bagList._customSize = {}\n        this.bagList._resizeContent();\n        switch (tabStatus) {\n            case TabStatus.Bag:\n                this._sortedItems = items\n                this._sortedPlaneParts = equips\n                this._separatorRow = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum)\n                const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum)\n                this.bagList.numItems = this._separatorRow + itemRowNum + 1;\n                break;\n            case TabStatus.Merge:\n                this._separatorRow = -1\n                this._sortedPlaneParts = equips\n                this.bagList.numItems = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum)\n                break;\n        }\n        logDebug(\"PlaneUI\", `onSortTypeChange list num:${this.bagList.numItems} maxPlanePartRowNum:${this._separatorRow}`)\n        this.bagList.scrollTo(0, 1)\n    }\n\n    onListRenderInBagStatus(listItem: Node, row: number) {\n        listItem.name = `${row}`\n        if (row == this._separatorRow) {\n            const normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize\n            const itemUITrans = listItem.getComponent(UITransform)\n            listItem.children.forEach(v => v.active = false)\n            this.separator.removeFromParent();\n            this.separator.active = true;\n            listItem.addChild(this.separator)\n            itemUITrans.setContentSize(normalSize.width, normalSize.height / 2)\n            return\n        }\n\n        if (listItem.children.length > 5) {\n            this.separator.removeFromParent();\n            this.separator.active = false;\n        }\n\n        const bagItems = listItem.getComponentsInChildren(BagItem)\n        if (row < this._separatorRow) {\n            for (let index = 0; index < bagItems.length; index++) {\n                const item = bagItems[index];\n                const dataIndex = row * this._lineGridNum + index;\n                if (dataIndex >= this._sortedPlaneParts.length) {\n                    item.node.active = false;\n                    logDebug(\"PlaneUI\", `onListRender bagItem index:${index} dataIndex:${dataIndex} row:${row} sortedLen:${this._sortedPlaneParts.length}`)\n                    continue\n                }\n                item.node.active = true;\n                item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);\n            }\n        } else {\n            for (let index = 0; index < bagItems.length; index++) {\n                const item = bagItems[index];\n                const dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + index;\n                if (dataIndex >= this._sortedItems.length) {\n                    item.node.active = false;\n                    continue\n                }\n                item.node.active = true;\n                item.onRenderItem(this._sortedItems[dataIndex]);\n            }\n        }\n    }\n\n    private onListRenderInMergeStatus(listItem: Node, row: number) {\n        const bagItems = listItem.getComponentsInChildren(BagItem)\n        for (let index = 0; index < bagItems.length; index++) {\n            const item = bagItems[index];\n            const dataIndex = row * this._lineGridNum + index;\n            if (dataIndex >= this._sortedPlaneParts.length) {\n                item.node.active = false;\n                continue\n            }\n            item.node.active = true;\n            item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);\n        }\n    }\n\n    onListRender(listItem: Node, row: number) {\n        listItem.name = `listItem${row}`\n        if (this._tabStatus == TabStatus.Bag) {\n            this.onListRenderInBagStatus(listItem, row)\n        } else {\n            this.onListRenderInMergeStatus(listItem, row)\n        }\n    }\n}"]}