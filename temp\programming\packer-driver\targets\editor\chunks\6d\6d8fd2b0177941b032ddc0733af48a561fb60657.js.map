{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAmgC,uCAAngC,EAAylC,uCAAzlC,EAA4qC,uCAA5qC,EAAgwC,wCAAhwC,EAAo1C,wCAAp1C,EAAm6C,wCAAn6C,EAAs/C,wCAAt/C,EAA0kD,wCAA1kD,EAAipD,wCAAjpD,EAA+tD,wCAA/tD,EAAgzD,wCAAhzD,EAAg4D,wCAAh4D,EAAy8D,wCAAz8D,EAAmhE,wCAAnhE,EAA8lE,wCAA9lE,EAA2qE,wCAA3qE,EAAsvE,wCAAtvE,EAA+zE,wCAA/zE,EAA44E,wCAA54E,EAAg+E,wCAAh+E,EAAkjF,wCAAljF,EAAynF,wCAAznF,EAAmsF,wCAAnsF,EAA4wF,wCAA5wF,EAA21F,wCAA31F,EAAg7F,wCAAh7F,EAAggG,wCAAhgG,EAAolG,wCAAplG,EAAuqG,wCAAvqG,EAAiwG,wCAAjwG,EAA+1G,wCAA/1G,EAAg8G,wCAAh8G,EAA2hH,wCAA3hH,EAA6nH,wCAA7nH,EAA+tH,wCAA/tH,EAA6zH,wCAA7zH,EAAu5H,wCAAv5H,EAAw+H,wCAAx+H,EAAwjI,wCAAxjI,EAAgpI,wCAAhpI,EAA+tI,wCAA/tI,EAAgzI,wCAAhzI,EAAs4I,wCAAt4I,EAAw9I,wCAAx9I,EAAwiJ,wCAAxiJ,EAAwnJ,wCAAxnJ,EAA8sJ,wCAA9sJ,EAAuyJ,wCAAvyJ,EAA43J,wCAA53J,EAA88J,wCAA98J,EAA6hK,wCAA7hK,EAA+mK,wCAA/mK,EAA+rK,wCAA/rK,EAA+wK,wCAA/wK,EAAg2K,wCAAh2K,EAAs7K,wCAAt7K,EAA6gL,wCAA7gL,EAAkmL,wCAAlmL,EAAyrL,wCAAzrL,EAA+wL,wCAA/wL,EAAw2L,wCAAx2L,EAAg8L,wCAAh8L,EAAyhM,wCAAzhM,EAAonM,wCAApnM,EAA8sM,wCAA9sM,EAAoyM,wCAApyM,EAA23M,wCAA33M,EAAi9M,wCAAj9M,EAAuiN,wCAAviN,EAA4nN,wCAA5nN,EAAstN,wCAAttN,EAAqyN,wCAAryN,EAAm3N,wCAAn3N,EAAo8N,wCAAp8N,EAAuhO,wCAAvhO,EAA4mO,wCAA5mO,EAA8rO,wCAA9rO,EAAixO,wCAAjxO,EAAq2O,wCAAr2O,EAAq7O,wCAAr7O,EAA0gP,wCAA1gP,EAAimP,wCAAjmP,EAAmrP,wCAAnrP,EAAwwP,wCAAxwP,EAAg2P,wCAAh2P,EAAu7P,wCAAv7P,EAAygQ,wCAAzgQ,EAA8lQ,wCAA9lQ,EAAurQ,wCAAvrQ,EAAuxQ,wCAAvxQ,EAA23Q,wCAA33Q,EAAs9Q,wCAAt9Q,EAAmjR,wCAAnjR,EAAopR,yCAAppR,EAA+uR,yCAA/uR,EAAq0R,yCAAr0R,EAA25R,yCAA35R,EAAs/R,yCAAt/R,EAAykS,yCAAzkS,EAA6pS,yCAA7pS,EAAovS,yCAApvS,EAAw0S,yCAAx0S,EAA65S,yCAA75S,EAAk/S,yCAAl/S,EAAskT,yCAAtkT,EAAspT,yCAAtpT,EAA8uT,yCAA9uT,EAA00T,yCAA10T,EAAo6T,yCAAp6T,EAA4/T,yCAA5/T,EAAolU,yCAAplU,EAAgrU,yCAAhrU,EAA0wU,yCAA1wU,EAA22U,yCAA32U,EAA88U,yCAA98U,EAAijV,yCAAjjV,EAAopV,yCAAppV,EAA8uV,yCAA9uV,EAA60V,yCAA70V,EAA66V,yCAA76V,EAAygW,yCAAzgW,EAAomW,yCAApmW,EAAmsW,yCAAnsW,EAAuyW,yCAAvyW,EAAs4W,yCAAt4W,EAAo+W,yCAAp+W,EAAkjX,yCAAljX,EAA+nX,yCAA/nX,EAA0sX,yCAA1sX,EAAuyX,yCAAvyX,EAAg4X,yCAAh4X,EAA89X,yCAA99X,EAA2jY,yCAA3jY,EAAypY,yCAAzpY,EAAqvY,yCAArvY,EAA8zY,yCAA9zY,EAA64Y,yCAA74Y,EAAu9Y,yCAAv9Y,EAAuiZ,yCAAviZ,EAAonZ,yCAApnZ,EAAwsZ,yCAAxsZ,EAAmxZ,yCAAnxZ,EAA61Z,yCAA71Z,EAA06Z,yCAA16Z,EAAm/Z,yCAAn/Z,EAA8ja,yCAA9ja,EAA+oa,yCAA/oa,EAAuua,yCAAvua,EAAs0a,yCAAt0a,EAA+5a,yCAA/5a,EAAu/a,yCAAv/a,EAAglb,yCAAhlb,EAAgrb,yCAAhrb,EAA2wb,yCAA3wb,EAAk2b,yCAAl2b,EAA07b,yCAA17b,EAAghc,yCAAhhc,EAAimc,yCAAjmc,EAAqrc,yCAArrc,EAAmxc,yCAAnxc,EAAy2c,yCAAz2c,EAAw8c,yCAAx8c,EAAmid,yCAAnid,EAAiod,yCAAjod,EAA2td,yCAA3td,EAAozd,yCAApzd,EAA04d,yCAA14d,EAAg+d,yCAAh+d,EAA+je,yCAA/je,EAAqpe,yCAArpe,EAA0ue,yCAA1ue,EAAg0e,yCAAh0e,EAAg6e,yCAAh6e,EAA4/e,yCAA5/e,EAAgmf,yCAAhmf,EAAysf,yCAAzsf,EAA4zf,yCAA5zf,EAAo7f,yCAAp7f,EAAwigB,yCAAxigB,EAAmpgB,yCAAnpgB,EAAivgB,yCAAjvgB,EAAy0gB,yCAAz0gB,EAAq6gB,yCAAr6gB,EAAs+gB,yCAAt+gB,EAAijhB,yCAAjjhB,EAAonhB,yCAApnhB,EAAsrhB,yCAAtrhB,EAAiwhB,yCAAjwhB,EAAk1hB,yCAAl1hB,EAAu6hB,yCAAv6hB,EAA4/hB,yCAA5/hB,EAA4kiB,yCAA5kiB,EAA4piB,yCAA5piB,EAA8uiB,yCAA9uiB,EAAi0iB,yCAAj0iB,EAA04iB,yCAA14iB,EAA29iB,yCAA39iB,EAA+ijB,yCAA/ijB,EAA8njB,yCAA9njB,EAAqujB,yCAArujB,EAAy1jB,yCAAz1jB,EAAy8jB,yCAAz8jB,EAAojkB,yCAApjkB,EAAipkB,yCAAjpkB,EAAiukB,yCAAjukB,EAAq0kB,yCAAr0kB,EAA86kB,yCAA96kB,EAAqhlB,yCAArhlB,EAA6nlB,yCAA7nlB,EAAstlB,yCAAttlB,EAAgylB,yCAAhylB,EAAy2lB,yCAAz2lB,EAA86lB,yCAA96lB,EAAogmB,yCAApgmB,EAAymmB,yCAAzmmB,EAAktmB,yCAAltmB,EAAqzmB,yCAArzmB,EAAw5mB,yCAAx5mB,EAA2/mB,yCAA3/mB,EAAslnB,yCAAtlnB,EAAqrnB,yCAArrnB,EAAkwnB,yCAAlwnB,EAAy0nB,yCAAz0nB,EAAs5nB,yCAAt5nB,EAAm+nB,yCAAn+nB,EAAijoB,yCAAjjoB,EAAoooB,yCAApooB,EAAktoB,yCAAltoB,EAAgyoB,yCAAhyoB,EAAg3oB,yCAAh3oB,EAA47oB,yCAA57oB,EAAugpB,yCAAvgpB,EAAulpB,yCAAvlpB,EAAoqpB,yCAApqpB,EAA8upB,yCAA9upB,EAAi0pB,yCAAj0pB,EAAy5pB,yCAAz5pB,EAAm/pB,yCAAn/pB,EAAqkqB,yCAArkqB,EAA4pqB,yCAA5pqB,EAAovqB,yCAApvqB,EAA40qB,yCAA50qB,EAAw6qB,yCAAx6qB,EAA4/qB,yCAA5/qB,EAA2lrB,yCAA3lrB,EAAsrrB,yCAAtrrB,EAA2wrB,yCAA3wrB,EAAg2rB,yCAAh2rB,EAAk7rB,yCAAl7rB,EAAyhsB,yCAAzhsB,EAAgosB,yCAAhosB,EAAgvsB,yCAAhvsB,EAAo1sB,yCAAp1sB,EAAg8sB,yCAAh8sB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEdtiorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/utils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/DataEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/bag/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/equip/Equip.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/equip/EquipCombine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/equip/EquipSlots.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/gm/GM.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCircleCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BossData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EventActionData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EventGroupData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MainData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItemEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ExchangeMap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/wave/Wave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/eventgroup/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MyApp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/event/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/dropdown/DropDown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/common/components/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/gm/GmButtonUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/gm/GmUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BattleUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BottomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BuidingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/BuildingInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/MainEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/MapModeUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/PlaneShowUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/PopupUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/TopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/WheelSpinnerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/dialogue/DialogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/fight/RogueSelectIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/fight/RogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendAddUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendListUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendStrangerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/friend/FriendUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneCombineResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/CombineDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}