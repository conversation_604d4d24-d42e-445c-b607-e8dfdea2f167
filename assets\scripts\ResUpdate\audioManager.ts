import {_decorator, AudioClip, AudioSource,Component, assert, warn, clamp01, resources } from "cc";
const { ccclass, property } = _decorator;
import { IMgr } from '../IMgr';
import { MyApp } from "../MyApp";

export class audioManager extends IMgr {

    private static _instance: audioManager;
    private static _audioSource?: AudioSource;

    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new audioManager();
        return this._instance;
    }

    /**管理器初始化*/
    init () {
        var audioSource = MyApp.GetInstance().node.getComponent(AudioSource);
        audioManager._audioSource = audioSource;
    }

      /**
     * 播放音乐
     * @param {Boolean} loop 是否循环播放
     */
    playMusic (loop: boolean) {
        const audioSource = audioManager._audioSource!;
        if (!audioSource) {
            return;
        }

        audioSource.loop = loop;
        if (!audioSource.playing) {
            audioSource.play();
        }
    }

     /**
     * 播放音效
     * @param {audioClip} audioClip 音效名称
     * @param {Number} volumeScale 播放音量倍数
     */
    playSound (audioClip: AudioClip, volumeScale: number = 1 ) {
        const audioSource = audioManager._audioSource!;
        if (!audioSource) {
            return;
        }
            
        // 注意：第二个参数 “volumeScale” 是指播放音量的倍数，最终播放的音量为 “audioSource.volume * volumeScale”
        audioSource.playOneShot(audioClip, volumeScale);

    }
    // 设置音乐音量
    setMusicVolume (flag: number) {
        const audioSource = audioManager._audioSource!;
        if (!audioSource) {
            return;
        }

        flag = clamp01(flag);
        audioSource.volume = flag;
    }

}