{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/plane/PlaneCombineResultUI.ts"], "names": ["_decorator", "Label", "Node", "logDebug", "List", "ListItem", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "PlaneCombineResultUI", "_results", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "PopUp", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "results", "length", "list", "node", "active", "singleResult", "numItems", "getComponentInChildren", "string", "name", "onHide", "onClose", "onList<PERSON>ender", "item", "index", "getComponent", "listId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAEnBC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACAC,MAAAA,Q;;AACEC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;sCAGjBU,oB,WADZF,OAAO,CAAC,sBAAD,C,UAOHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACP,IAAD,C,2BATb,MACaQ,oBADb;AAAA;AAAA,4BACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAWrCC,QAXqC,GAWJ,EAXI;AAAA;;AACzB,eAANC,MAAM,GAAW;AAAE,iBAAO,oCAAP;AAA8C;;AACzD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,KAAf;AAAsB;;AACjC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAQSC,QAAAA,MAAM,GAAS,CACxB;;AAEW,cAANC,MAAM,CAACC,OAAD,EAA+C;AACvD,eAAKR,QAAL,GAAgBQ,OAAhB;;AACA,cAAIA,OAAO,CAACC,MAAR,GAAiB,CAArB,EAAwB;AACpB,iBAAKC,IAAL,CAAUC,IAAV,CAAeC,MAAf,GAAwB,IAAxB;AACA,iBAAKC,YAAL,CAAkBD,MAAlB,GAA2B,KAA3B;AACA,iBAAKF,IAAL,CAAUI,QAAV,GAAqBN,OAAO,CAACC,MAA7B;AACA;AACH;;AACD,eAAKC,IAAL,CAAUC,IAAV,CAAeC,MAAf,GAAwB,KAAxB;AACA,eAAKC,YAAL,CAAkBD,MAAlB,GAA2B,IAA3B;AACA,eAAKC,YAAL,CAAkBE,sBAAlB,CAAyCzB,KAAzC,EAAgD0B,MAAhD,GAAyD,KAAKhB,QAAL,CAAc,CAAd,EAAiBiB,IAA1E;AACH;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACDC,QAAAA,YAAY,CAACC,IAAD,EAAaC,KAAb,EAA4B;AACpC;AAAA;AAAA,oCAAS,SAAT,EAAqB,SAAQA,KAAM,OAAMD,IAAI,CAACE,YAAL;AAAA;AAAA,oCAA4BC,MAAO,EAA5E;AACAH,UAAAA,IAAI,CAACN,sBAAL,CAA4BzB,KAA5B,EAAmC0B,MAAnC,GAA4C,KAAKhB,QAAL,CAAcsB,KAAd,EAAqBL,IAAjE;AACH;;AAnC4C,O;;;;;iBAOhC,I;;;;;;;iBAEQ,I", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\n\nimport { logDebug } from '../../../Utils/Logger';\nimport List from '../../common/components/list/List';\nimport ListItem from '../../common/components/list/ListItem';\nimport { BaseUI, UILayer, UIOpt } from '../../UIMgr';\nimport { mockPlaneEquipInfo } from './PlaneTypes';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlaneCombineResultUI')\nexport class PlaneCombineResultUI extends BaseUI {\n    public static getUrl(): string { return \"ui/main/plane/PlaneCombineResultUI\"; }\n    public static getLayer(): UILayer { return UILayer.PopUp }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n    @property(List)\n    list: List = null;\n    @property(Node)\n    singleResult: Node = null;\n\n    private _results: mockPlaneEquipInfo[] = [];\n\n    protected onLoad(): void {\n    }\n\n    async onShow(results: mockPlaneEquipInfo[]): Promise<void> {\n        this._results = results\n        if (results.length > 1) {\n            this.list.node.active = true;\n            this.singleResult.active = false;\n            this.list.numItems = results.length;\n            return\n        }\n        this.list.node.active = false;\n        this.singleResult.active = true;\n        this.singleResult.getComponentInChildren(Label).string = this._results[0].name;\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n    }\n    onListRender(item: Node, index: number) {\n        logDebug(\"PlaneUI\", `index:${index} id:${item.getComponent(ListItem).listId}`)\n        item.getComponentInChildren(Label).string = this._results[index].name;\n    }\n}\n"]}