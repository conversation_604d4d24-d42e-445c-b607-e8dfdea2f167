import { _decorator, Label } from 'cc';
import { BaseUI, UILayer, UIOpt } from '../UIMgr';

const { ccclass, property } = _decorator;

@ccclass('PlaneShowUI')
export class PlaneShowUI extends BaseUI {

    @property(Label)
    planeName: Label = null;
    @property(Label)
    planePower: Label = null;
    @property(Label)
    planeType: Label = null;

    public static getUrl(): string { return "ui/main/PlaneShowUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected onLoad(): void {
        
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

}
