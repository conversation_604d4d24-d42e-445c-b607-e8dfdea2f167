import { _decorator, Layout, Node, ScrollView, UITransform } from 'cc';
import { BaseUI, UILayer, UIMgr } from '../UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass("BuildingInfoUI")
export class BuildingInfoUI extends BaseUI {

    @property(ScrollView)
    scroll: ScrollView;
    @property(Node)
    scrollContent: Node;
    @property(Layout)
    scrollLayout: Layout;
    @property(ButtonPlus)
    closeBtn: ButtonPlus;

    public static getUrl(): string { return "ui/main/BuildingInfoUI"; };
    public static getLayer(): UILayer { return UILayer.Top }
    protected onLoad(): void {
        let len = this.scrollLayout.node.children.length;
        if (len > 0) {
            let wid = this.scrollLayout.node.children[0].getComponent(UITransform).width;
            let sx = this.scrollLayout.spacingX;
            let newWid = (len - 1) * sx + wid * len;
            let hei = this.scrollContent.getComponent(UITransform).height;
            this.scrollContent.getComponent(UITransform).setContentSize(newWid, hei);
        }
        this.closeBtn.addClick(this.closeUI, this);
    }
    async closeUI() {
        UIMgr.closeUI(BuildingInfoUI);
    }

    protected onDestroy(): void {

    }

    async onShow(...args: any[]): Promise<void> {

    }

    protected update(dt: number): void {

    }

    async onHide(...args: any[]): Promise<void> { }

    async onClose(...args: any[]): Promise<void> { }

}
