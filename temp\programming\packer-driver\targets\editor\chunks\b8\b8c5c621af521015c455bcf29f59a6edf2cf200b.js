System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, csproto, EventMgr, MyApp, logError, DataEvent, Bag, _crd;

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../../AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "../../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  _export("Bag", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      logError = _unresolved_5.logError;
    }, function (_unresolved_6) {
      DataEvent = _unresolved_6.DataEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8ae96Ryw0pCda72h7ZrtiFJ", "Bag", undefined);

      _export("Bag", Bag = class Bag {
        constructor() {
          // 物品槽位
          this.items = [];
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ITEM_LIST, this.onGetItemListMsg.bind(this));
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_UPDATE_ITEM, this.onUpdateItemMsg.bind(this));
          this.refreshItems();
        } // 刷新物品 


        refreshItems() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ITEM_LIST, {
            get_item_list: {}
          });
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).ItemsRefresh);
        } //物品推送


        onUpdateItemMsg(msg) {
          const updateItems = msg.body.update_item.items;

          switch (msg.body.update_item.reason) {
            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_ADD:
              updateItems.forEach(element => {
                if (this.getItemByGuid(element.guid)) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)("PlaneUI", `item guid ${element.guid} already exist`);
                  return;
                }

                this.items.push(element);
              });
              break;

            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_REMOVE:
              updateItems.forEach(element => {
                const idx = this.items.findIndex(v => {
                  return v.guid.eq(element.guid);
                });

                if (idx < 0) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)("PlaneUI", `item guid ${element.guid} not exist`);
                  return;
                }

                this.items.splice(idx, 1);
              });
              break;

            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_CHANGE:
              this.items = this.items.map(v => {
                const item = updateItems.find(item => item.guid.eq(v.guid));
                return item ? item : v;
              });
              break;

            default:
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("PlaneUI", `unknown item update reason ${msg.body.update_item.reason}`);
              break;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).ItemsRefresh);
        }

        getItemByGuid(guid) {
          return this.items.find(v => v.guid.eq(guid)) || null;
        }

        onGetItemListMsg(msg) {
          this.items = msg.body.get_item_list.items || [];
        }

        update() {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b8c5c621af521015c455bcf29f59a6edf2cf200b.js.map