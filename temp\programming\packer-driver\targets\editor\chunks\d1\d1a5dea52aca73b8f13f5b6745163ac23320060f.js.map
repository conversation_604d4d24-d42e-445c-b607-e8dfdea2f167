{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyData.ts"], "names": ["EnemyAttackPointData", "EnemyAttrData", "EnemyCollider", "EnemyPlaneData", "TrackGroup", "EnemyData", "EnemyUIData", "EnemyShootData", "v2", "Tools", "x", "y", "shootInterval", "bulletID", "bulletNum", "bulletInterval", "bulletAttackRate", "soundId", "soundDelay", "loadJson", "positionData", "attackData", "position", "stringToNumber", "attack", "length", "error", "type", "param", "data", "hasOwnProperty", "parseInt", "width", "height", "values", "id", "uiId", "hp", "dieBullet", "defence", "collideLevel", "bTurnDir", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "bMoveAttack", "bStayAttack", "attackInterval", "attackNum", "dieShoot", "attr", "bAttackAbles", "attackArrNums", "attackPointArr", "atk", "turn", "sections", "split", "h", "attackGroups", "isAttackAble", "points", "attackPoints", "n", "attackPoint", "push", "point", "stringToPoint", "pos", "trackArr", "json", "dataParts", "trackData", "i", "trackGroup", "image", "isAm", "collider", "hpParam", "damageParam", "clipArr", "blastSound", "blastCount", "blastParam", "blastDurations", "blastShake", "extraParam", "extraParam1", "skillResistUIDict", "lootParam0", "lootParam1", "sneak<PERSON>ara<PERSON>", "sneakAnim", "showParam", "params", "blp", "damageParams", "extraParams", "extraParam0", "skillResistData", "entry", "parts", "key", "attackArrNum", "parseFloat", "ain", "aa", "arrNum", "pointsData"], "mappings": ";;;0FAGaA,oB,EAqCAC,a,EAoBAC,a,EA4BAC,c,EA6EPC,U,EAqBOC,S,EAiCAC,W,EA8FAC,c;;;;;;;;;;;;;;;;;;;;;;;AAzTGC,MAAAA,E,OAAAA,E;;AACPC,MAAAA,K,iBAAAA,K;;;;;;;;;sCAEIT,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eAC9BU,CAD8B,GAClB,CADkB;AAAA,eAE9BC,CAF8B,GAElB,CAFkB;AAAA,eAG9BC,aAH8B,GAGN,CAHM;AAAA,eAI9BC,QAJ8B,GAIX,CAJW;AAAA,eAK9BC,SAL8B,GAKV,CALU;AAAA,eAM9BC,cAN8B,GAML,CANK;AAAA,eAO9BC,gBAP8B,GAOH,CAPG;AAAA,eAQ9BC,OAR8B,GAQZ,CARY;AAAA,eAS9BC,UAT8B,GAST,CATS;AAAA;;AAW9B;AACJ;AACA;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,YAAD,EAAuBC,UAAvB,EAAiD;AACrD,cAAI;AACA,kBAAMC,QAAQ,GAAG;AAAA;AAAA,gCAAMC,cAAN,CAAqBH,YAArB,EAAmC,GAAnC,CAAjB;AACA,iBAAKV,CAAL,GAASY,QAAQ,CAAC,CAAD,CAAjB;AACA,iBAAKX,CAAL,GAASW,QAAQ,CAAC,CAAD,CAAjB;AAEA,kBAAME,MAAM,GAAG;AAAA;AAAA,gCAAMD,cAAN,CAAqBF,UAArB,EAAiC,GAAjC,CAAf;AACA,iBAAKT,aAAL,GAAqBY,MAAM,CAAC,CAAD,CAA3B;AACA,iBAAKX,QAAL,GAAgBW,MAAM,CAAC,CAAD,CAAtB;AACA,iBAAKV,SAAL,GAAiBU,MAAM,CAAC,CAAD,CAAvB;AACA,iBAAKT,cAAL,GAAsBS,MAAM,CAAC,CAAD,CAA5B;AACA,iBAAKR,gBAAL,GAAwBQ,MAAM,CAAC,CAAD,CAAN,GAAY,GAApC;AAEA,gBAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB,KAAKR,OAAL,GAAeO,MAAM,CAAC,CAAD,CAArB;AACvB,gBAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB,KAAKP,UAAL,GAAkBM,MAAM,CAAC,CAAD,CAAxB;AAC1B,WAdD,CAcE,OAAOE,KAAP,EAAc,CACZ;AACH;AACJ;;AAlC6B,O;;+BAqCrBzB,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAEvB0B,IAFuB,GAEhB,CAFgB;AAEb;AAFa,eAGvBC,KAHuB,GAGf,EAHe;AAAA;;AAGX;;AAGZ;AACJ;AACA;AACA;AACIT,QAAAA,QAAQ,CAACU,IAAD,EAAO;AACX,cAAIA,IAAI,CAACC,cAAL,CAAoB,MAApB,CAAJ,EAAiC;AAC7B,iBAAKH,IAAL,GAAYI,QAAQ,CAACF,IAAI,CAACF,IAAN,CAApB;AACH;;AACD,cAAIE,IAAI,CAACC,cAAL,CAAoB,OAApB,CAAJ,EAAkC;AAC9B,iBAAKF,KAAL,GAAaC,IAAI,CAACD,KAAlB;AACH;AACJ;;AAjBsB,O;;+BAoBd1B,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAEvByB,IAFuB,GAEhB,CAFgB;AAAA,eAGvBjB,CAHuB,GAGnB,CAHmB;AAAA,eAIvBC,CAJuB,GAInB,CAJmB;AAAA,eAKvBqB,KALuB,GAKf,CALe;AAAA,eAMvBC,MANuB,GAMd,CANc;AAAA;;AAQvB;AACJ;AACA;AACA;AACId,QAAAA,QAAQ,CAACU,IAAD,EAAO;AACX,cAAI;AACA,kBAAMK,MAAM,GAAG;AAAA;AAAA,gCAAMX,cAAN,CAAqBM,IAArB,EAA2B,GAA3B,CAAf;AACA,iBAAKF,IAAL,GAAYO,MAAM,CAAC,CAAD,CAAlB;AACA,iBAAKxB,CAAL,GAASwB,MAAM,CAAC,CAAD,CAAf;AACA,iBAAKvB,CAAL,GAASuB,MAAM,CAAC,CAAD,CAAf;AACA,iBAAKF,KAAL,GAAaE,MAAM,CAAC,CAAD,CAAnB;AACA,iBAAKD,MAAL,GAAcC,MAAM,CAAC,CAAD,CAApB;AACH,WAPD,CAOE,OAAOR,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAY,sBAAZ,EAAoCG,IAApC;AACH;AACJ;;AAvBsB,O;AAyB3B;AACA;AACA;;;gCACa1B,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACxBgC,EADwB,GACX,CADW;AAAA,eAExBC,IAFwB,GAET,CAFS;AAAA,eAGxBC,EAHwB,GAGX,CAHW;AAAA,eAIxBC,SAJwB,GAIH,KAJG;AAAA,eAKxBd,MALwB,GAKP,CALO;AAAA,eAMxBe,OANwB,GAMN,CANM;AAAA,eAOxBC,YAPwB,GAOD,CAPC;AAAA,eAQxBC,QARwB,GAQJ,KARI;AAAA,eASxBC,aATwB,GASA,CATA;AAAA,eAUxBC,YAVwB,GAUA,KAVA;AAAA,eAWxBC,WAXwB,GAWD,KAXC;AAAA,eAYxBC,WAZwB,GAYD,KAZC;AAAA,eAaxBC,cAbwB,GAaC,CAbD;AAAA,eAcxBC,SAdwB,GAcJ,CAdI;AAAA,eAexBC,QAfwB,GAeN,EAfM;AAAA,eAgBxBC,IAhBwB,GAgBT,EAhBS;AAAA,eAiBxBrB,KAjBwB,GAiBR,EAjBQ;AAAA,eAkBxBsB,YAlBwB,GAkBE,EAlBF;AAAA,eAmBxBC,aAnBwB,GAmBE,EAnBF;AAAA,eAoBxBC,cApBwB,GAoBA,EApBA;AAAA;;AAsBxB;AACJ;AACA;AACA;AACIjC,QAAAA,QAAQ,CAACU,IAAD,EAAkB;AACtB,eAAKM,EAAL,GAAUN,IAAI,CAACM,EAAf;AACA,eAAKC,IAAL,GAAYP,IAAI,CAACO,IAAjB;AACA,eAAKZ,MAAL,GAAcK,IAAI,CAACwB,GAAnB;AACA,eAAKhB,EAAL,GAAUR,IAAI,CAACQ,EAAf;AACA,eAAKG,YAAL,GAAoBX,IAAI,CAACW,YAAzB;AACA,eAAKC,QAAL,GAAgBZ,IAAI,CAACyB,IAArB;AACA,eAAKZ,aAAL,GAAqBb,IAAI,CAACa,aAA1B;AACA,eAAKC,YAAL,GAAoBd,IAAI,CAACc,YAAzB;AACA,eAAKC,WAAL,GAAmBf,IAAI,CAACe,WAAxB;AACA,eAAKC,WAAL,GAAmBhB,IAAI,CAACgB,WAAxB;AACA,eAAKC,cAAL,GAAsBjB,IAAI,CAACiB,cAA3B;AACA,eAAKC,SAAL,GAAiBlB,IAAI,CAACkB,SAAtB;AACA,eAAKnB,KAAL,GAAaC,IAAI,CAACD,KAAlB;AACA,eAAKoB,QAAL,GAAgBnB,IAAI,CAACmB,QAArB;AACA,eAAKV,SAAL,GAAiBT,IAAI,CAACS,SAAtB,CAfsB,CAkBtB;AACA;;AACA,cAAIT,IAAI,CAACC,cAAL,CAAoB,YAApB,CAAJ,EAAuC;AACnC,kBAAMyB,QAAQ,GAAG1B,IAAI,CAACR,UAAL,CAAgBmC,KAAhB,CAAsB,GAAtB,CAAjB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,QAAQ,CAAC9B,MAA7B,EAAqCgC,CAAC,EAAtC,EAA0C;AACtC,oBAAMC,YAAY,GAAG,EAArB,CADsC,CACb;;AACzB,kBAAIC,YAAY,GAAG,KAAnB;;AACA,kBAAIJ,QAAQ,CAACE,CAAD,CAAR,KAAgB,EAApB,EAAwB;AACpB,sBAAMG,MAAM,GAAGL,QAAQ,CAACE,CAAD,CAAR,CAAYD,KAAZ,CAAkB,GAAlB,CAAf;AACA,sBAAMK,YAAmB,GAAG,EAA5B,CAFoB,CAEY;;AAEhC,qBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACnC,MAA3B,EAAmCqC,CAAC,IAAI,CAAxC,EAA2C;AACvC,sBAAIF,MAAM,CAACE,CAAD,CAAN,KAAc,EAAd,IAAoBF,MAAM,CAACE,CAAC,GAAG,CAAL,CAAN,KAAkB,EAA1C,EAA8C;AAC1C,0BAAMC,WAAW,GAAG,IAAI/D,oBAAJ,EAApB;AACA+D,oBAAAA,WAAW,CAAC5C,QAAZ,CAAqByC,MAAM,CAACE,CAAD,CAA3B,EAAgCF,MAAM,CAACE,CAAC,GAAG,CAAL,CAAtC;AACAD,oBAAAA,YAAY,CAACG,IAAb,CAAkBD,WAAlB;AACAJ,oBAAAA,YAAY,GAAG,IAAf;AACH;AACJ;;AAEDD,gBAAAA,YAAY,CAACM,IAAb,CAAkBH,YAAlB,EAboB,CAaa;AACpC;;AACD,mBAAKT,cAAL,CAAoBY,IAApB,CAAyBN,YAAzB,EAlBsC,CAkBE;;AACxC,mBAAKP,aAAL,CAAmBa,IAAnB,CAAwBN,YAAY,CAACjC,MAArC,EAnBsC,CAmBQ;;AAC9C,mBAAKyB,YAAL,CAAkBc,IAAlB,CAAuBL,YAAvB,EApBsC,CAoBA;AACzC;AACJ;AACJ;;AAxEuB,O;;AA6EtBvD,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAEbwD,MAFa,GAEJ,EAFI;AAAA;;AAEA;;AAEb;AACJ;AACA;AACA;AACIzC,QAAAA,QAAQ,CAACU,IAAD,EAAO;AACX,gBAAM+B,MAAM,GAAG/B,IAAI,CAAC2B,KAAL,CAAW,GAAX,CAAf;;AACA,eAAK,MAAMS,KAAX,IAAoBL,MAApB,EAA4B;AACxB,gBAAIK,KAAK,KAAK,EAAd,EAAkB;AACd,mBAAKL,MAAL,CAAYI,IAAZ,CAAiB;AAAA;AAAA,kCAAME,aAAN,CAAoBD,KAApB,EAA2B,GAA3B,CAAjB;AACH;AACJ;AACJ;;AAfY,O;AAkBjB;AACA;AACA;;2BACa5D,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eAEnB8B,EAFmB,GAEd,CAFc;AAEX;AAFW,eAGnBgC,GAHmB,GAGb3D,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHW;AAGH;AAHG,eAInB4D,QAJmB,GAIR,EAJQ;AAAA;;AAIJ;;AAGf;AACJ;AACA;AACA;AACIjD,QAAAA,QAAQ,CAACkD,IAAD,EAAO;AACX,gBAAMC,SAAS,GAAGD,IAAI,CAACb,KAAL,CAAW,GAAX,CAAlB;;AACA,cAAIc,SAAS,CAAC7C,MAAV,GAAmB,CAAvB,EAA0B;AACtB,kBAAML,YAAY,GAAG;AAAA;AAAA,gCAAMG,cAAN,CAAqB+C,SAAS,CAAC,CAAD,CAA9B,EAAmC,GAAnC,CAArB;AACA,iBAAKnC,EAAL,GAAUf,YAAY,CAAC,CAAD,CAAtB;AACA,iBAAK+C,GAAL,GAAW3D,EAAE,CAACY,YAAY,CAAC,CAAD,CAAb,EAAkBA,YAAY,CAAC,CAAD,CAA9B,CAAb;AACH;;AAED,cAAIkD,SAAS,CAAC7C,MAAV,GAAmB,CAAnB,IAAwB6C,SAAS,CAAC,CAAD,CAAT,KAAiB,EAA7C,EAAiD;AAC7C,kBAAMC,SAAS,GAAGD,SAAS,CAAC,CAAD,CAAT,CAAad,KAAb,CAAmB,GAAnB,CAAlB;;AACA,iBAAK,IAAIgB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,SAAS,CAAC9C,MAA9B,EAAsC+C,CAAC,EAAvC,EAA2C;AACvC,kBAAID,SAAS,CAACC,CAAD,CAAT,KAAiB,EAAjB,IAAuBD,SAAS,CAACC,CAAD,CAAT,CAAahB,KAAb,CAAmB,GAAnB,EAAwB/B,MAAxB,GAAiC,CAA5D,EAA+D;AAC3D,sBAAMgD,UAAU,GAAG,IAAIrE,UAAJ,EAAnB;AACAqE,gBAAAA,UAAU,CAACtD,QAAX,CAAoBoD,SAAS,CAACC,CAAD,CAA7B;AACA,qBAAKJ,QAAL,CAAcJ,IAAd,CAAmBS,UAAnB;AACH;AACJ;AACJ;AACJ;;AA7BkB,O;;6BAiCVnE,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,eACrB6B,EADqB,GAChB,CADgB;AACb;AADa,eAErBuC,KAFqB,GAEb,EAFa;AAET;AAFS,eAGrBC,IAHqB,GAGd,KAHc;AAGP;AAHO,eAIrBC,QAJqB,GAIV,EAJU;AAIN;AAJM,eAKrBC,OALqB,GAKX,EALW;AAKP;AALO,eAMrBC,WANqB,GAMP,EANO;AAMH;AANG,eAOrBC,OAPqB,GAOX,EAPW;AAOP;AAPO,eAQrBC,UARqB,GAQR,CARQ;AAQL;AARK,eASrBC,UATqB,GASR,CATQ;AASL;AATK,eAUrBC,UAVqB,GAUR,EAVQ;AAUJ;AAVI,eAWrBC,cAXqB,GAWJ,EAXI;AAWA;AAXA,eAYrBC,UAZqB,GAYR,EAZQ;AAYJ;AAZI,eAarBC,UAbqB,GAaR,EAbQ;AAaJ;AAbI,eAcrBC,WAdqB,GAcP,EAdO;AAcH;AAdG,eAerBC,iBAfqB,GAeD,EAfC;AAeG;AAfH,eAgBrBC,UAhBqB,GAgBR,EAhBQ;AAgBJ;AAhBI,eAiBrBC,UAjBqB,GAiBR,EAjBQ;AAiBJ;AAjBI,eAkBrBC,UAlBqB,GAkBR,EAlBQ;AAkBJ;AAlBI,eAmBrBC,SAnBqB,GAmBT,EAnBS;AAmBL;AAnBK,eAoBrBC,SApBqB,GAoBT,EApBS;AAAA;;AAoBL;;AAEhB;AACJ;AACA;AACA;AACIzE,QAAAA,QAAQ,CAACU,IAAD,EAAO;AACX,eAAKM,EAAL,GAAUN,IAAI,CAACM,EAAf;AACA,eAAKuC,KAAL,GAAa7C,IAAI,CAAC6C,KAAlB;AACA,eAAKC,IAAL,GAAY9C,IAAI,CAAC8C,IAAjB;AACA,eAAKC,QAAL,GAAgB;AAAA;AAAA,8BAAMrD,cAAN,CAAqBM,IAAI,CAAC+C,QAA1B,EAAoC,GAApC,CAAhB;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,8BAAMtD,cAAN,CAAqBM,IAAI,CAACgD,OAA1B,EAAmC,GAAnC,CAAf;AACA,eAAKG,UAAL,GAAkBnD,IAAI,CAACmD,UAAvB;AACA,eAAKA,UAAL,GAAkBnD,IAAI,CAACmD,UAAvB;;AAGA,cAAInD,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAM+D,MAAM,GAAGhE,IAAI,CAACiE,GAAL,CAAStC,KAAT,CAAe,GAAf,CAAf;;AACA,gBAAIqC,MAAM,CAACpE,MAAP,GAAgB,CAAhB,IAAqBoE,MAAM,CAAC,CAAD,CAAN,KAAc,EAAvC,EAA2C;AACvC,mBAAKZ,UAAL,GAAkBlD,QAAQ,CAAC8D,MAAM,CAAC,CAAD,CAAP,CAA1B;;AACA,mBAAK,IAAIrB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqB,MAAM,CAACpE,MAA3B,EAAmC+C,CAAC,EAApC,EAAwC;AACpC,oBAAIqB,MAAM,CAACrB,CAAD,CAAN,KAAc,EAAlB,EAAsB;AAClB,uBAAKU,UAAL,CAAgBlB,IAAhB,CAAqB;AAAA;AAAA,sCAAMzC,cAAN,CAAqBsE,MAAM,CAACrB,CAAD,CAA3B,EAAgC,GAAhC,CAArB;AACH;AACJ;AACJ;AACJ;;AAED,eAAKW,cAAL,GAAsB;AAAA;AAAA,8BAAM5D,cAAN,CAAqBM,IAAI,CAACsD,cAA1B,EAA0C,GAA1C,CAAtB;AACA,eAAKC,UAAL,GAAkB;AAAA;AAAA,8BAAM7D,cAAN,CAAqBM,IAAI,CAACuD,UAA1B,EAAsC,GAAtC,CAAlB;;AACA,cAAIvD,IAAI,CAACC,cAAL,CAAoB,aAApB,CAAJ,EAAwC;AACpC,kBAAMiE,YAAY,GAAGlE,IAAI,CAACiD,WAAL,CAAiBtB,KAAjB,CAAuB,GAAvB,CAArB;;AACA,iBAAK,MAAM5B,KAAX,IAAoBmE,YAApB,EAAkC;AAC9B,kBAAInE,KAAK,KAAK,EAAd,EAAkB;AACd,qBAAKkD,WAAL,CAAiBd,IAAjB,CAAsB;AAAA;AAAA,oCAAMzC,cAAN,CAAqBK,KAArB,EAA4B,GAA5B,CAAtB;AACH;AACJ;AACJ;;AAED,cAAIC,IAAI,CAACC,cAAL,CAAoB,aAApB,CAAJ,EAAwC;AACpC,kBAAMkE,WAAW,GAAGnE,IAAI,CAACoE,WAAL,CAAiBzC,KAAjB,CAAuB,GAAvB,CAApB;;AACA,iBAAK,MAAM5B,KAAX,IAAoBoE,WAApB,EAAiC;AAC7B,kBAAIpE,KAAK,KAAK,EAAd,EAAkB;AACd,qBAAKyD,UAAL,CAAgBrB,IAAhB,CAAqB;AAAA;AAAA,oCAAMzC,cAAN,CAAqBK,KAArB,EAA4B,GAA5B,CAArB;AACH;AACJ;AACJ;;AACD,cAAIC,IAAI,CAACC,cAAL,CAAoB,aAApB,CAAJ,EAAwC,KAAKwD,WAAL,GAAmBzD,IAAI,CAACyD,WAAxB;;AAExC,cAAIzD,IAAI,CAACC,cAAL,CAAoB,mBAApB,CAAJ,EAA8C;AAC1C,kBAAMoE,eAAe,GAAGrE,IAAI,CAAC0D,iBAAL,CAAuB/B,KAAvB,CAA6B,GAA7B,CAAxB;;AACA,iBAAK,MAAM2C,KAAX,IAAoBD,eAApB,EAAqC;AACjC,kBAAIC,KAAK,KAAK,EAAd,EAAkB;AACd,sBAAMC,KAAK,GAAGD,KAAK,CAAC3C,KAAN,CAAY,GAAZ,CAAd;;AACA,oBAAI4C,KAAK,CAAC3E,MAAN,GAAe,CAAnB,EAAsB;AAClB,wBAAM4E,GAAG,GAAGtE,QAAQ,CAACqE,KAAK,CAAC,CAAD,CAAN,CAApB;AACA,wBAAMlE,MAAM,GAAG,EAAf;;AACA,uBAAK,IAAIsC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4B,KAAK,CAAC3E,MAA1B,EAAkC+C,CAAC,EAAnC,EAAuC;AACnC,wBAAI4B,KAAK,CAAC5B,CAAD,CAAL,KAAa,EAAjB,EAAqB;AACjBtC,sBAAAA,MAAM,CAAC8B,IAAP,CAAY;AAAA;AAAA,0CAAMzC,cAAN,CAAqB6E,KAAK,CAAC5B,CAAD,CAA1B,EAA+B,GAA/B,CAAZ;AACH;AACJ;;AACD,uBAAKe,iBAAL,CAAuBc,GAAvB,IAA8BnE,MAA9B;AACH;AACJ;AACJ;AACJ;;AACD,eAAKsD,UAAL,GAAkB;AAAA;AAAA,8BAAMjE,cAAN,CAAqBM,IAAI,CAAC2D,UAA1B,EAAsC,GAAtC,CAAlB;AACA,eAAKC,UAAL,GAAkB;AAAA;AAAA,8BAAMlE,cAAN,CAAqBM,IAAI,CAAC4D,UAA1B,EAAsC,GAAtC,CAAlB;AACA,eAAKE,SAAL,GAAiB9D,IAAI,CAAC8D,SAAtB;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,8BAAMrE,cAAN,CAAqBM,IAAI,CAAC+D,SAA1B,EAAqC,GAArC,CAAjB;AACH;;AA3FoB,O;;gCA8FZrF,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACxBuC,cADwB,GACP,CADO;AACJ;AADI,eAExBC,SAFwB,GAEZ,CAFY;AAET;AAFS,eAGxBuD,YAHwB,GAGT,CAHS;AAGN;AAHM,eAIxBlD,cAJwB,GAIP,EAJO;AAAA;;AAIH;;AAErB;AACJ;AACA;AACA;AACIjC,QAAAA,QAAQ,CAACU,IAAD,EAAO;AACX,cAAIA,IAAI,CAACC,cAAL,CAAoB,KAApB,CAAJ,EAAgC,KAAKgB,cAAL,GAAsByD,UAAU,CAAC1E,IAAI,CAAC2E,GAAN,CAAhC;AAChC,cAAI3E,IAAI,CAACC,cAAL,CAAoB,IAApB,CAAJ,EAA+B,KAAKiB,SAAL,GAAiBhB,QAAQ,CAACF,IAAI,CAAC4E,EAAN,CAAzB;AAC/B,cAAI5E,IAAI,CAACC,cAAL,CAAoB,QAApB,CAAJ,EAAmC,KAAKwE,YAAL,GAAoBvE,QAAQ,CAACF,IAAI,CAAC6E,MAAN,CAA5B;;AACnC,cAAI7E,IAAI,CAACC,cAAL,CAAoB,QAApB,CAAJ,EAAmC;AAC/B,kBAAM6E,UAAU,GAAG9E,IAAI,CAAC+B,MAAL,CAAYJ,KAAZ,CAAkB,GAAlB,CAAnB;;AACA,iBAAK,MAAMS,KAAX,IAAoB0C,UAApB,EAAgC;AAC5B,kBAAI1C,KAAK,KAAK,EAAd,EAAkB;AACd,qBAAKb,cAAL,CAAoBY,IAApB,CAAyB;AAAA;AAAA,oCAAMzC,cAAN,CAAqB0C,KAArB,EAA4B,GAA5B,CAAzB;AACH;AACJ;AACJ;AACJ;;AAtBuB,O", "sourcesContent": ["import { error, v2, Vec2 } from \"cc\";\r\nimport { Tools } from \"../utils/Tools\";\r\n\r\nexport class EnemyAttackPointData {\r\n    x: number = 0;\r\n    y: number = 0;\r\n    shootInterval: number = 0;\r\n    bulletID: number = 0;\r\n    bulletNum: number = 0;\r\n    bulletInterval: number = 0;\r\n    bulletAttackRate: number = 0;\r\n    soundId: number = 0;\r\n    soundDelay: number = 0;\r\n\r\n    /**\r\n     * 从 JSON 数据加载攻击点数据\r\n     * @param positionData 位置数据\r\n     * @param attackData 攻击数据\r\n     */\r\n    loadJson(positionData: string, attackData: string): void {\r\n        try {\r\n            const position = Tools.stringToNumber(positionData, \",\");\r\n            this.x = position[0];\r\n            this.y = position[1];\r\n\r\n            const attack = Tools.stringToNumber(attackData, \",\");\r\n            this.shootInterval = attack[0];\r\n            this.bulletID = attack[1];\r\n            this.bulletNum = attack[2];\r\n            this.bulletInterval = attack[3];\r\n            this.bulletAttackRate = attack[4] / 100;\r\n\r\n            if (attack.length > 5) this.soundId = attack[5];\r\n            if (attack.length > 6) this.soundDelay = attack[6];\r\n        } catch (error) {\r\n            // Log.e(\"EnemyAttackPointData error:\", positionData, attackData);\r\n        }\r\n    }\r\n}\r\n\r\nexport class EnemyAttrData {\r\n\r\n    type = 0; // 属性类型\r\n    param = \"\"; // 属性参数\r\n\r\n\r\n    /**\r\n     * 从 JSON 数据加载敌人属性数据\r\n     * @param {Object} data 属性数据\r\n     */\r\n    loadJson(data) {\r\n        if (data.hasOwnProperty(\"type\")) {\r\n            this.type = parseInt(data.type);\r\n        }\r\n        if (data.hasOwnProperty(\"param\")) {\r\n            this.param = data.param;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EnemyCollider {\r\n\r\n    type = 0;\r\n    x = 0;\r\n    y = 0;\r\n    width = 0;\r\n    height = 0;\r\n\r\n    /**\r\n     * 从 JSON 数据加载碰撞器数据\r\n     * @param {string} data 碰撞器数据\r\n     */\r\n    loadJson(data) {\r\n        try {\r\n            const values = Tools.stringToNumber(data, ',');\r\n            this.type = values[0];\r\n            this.x = values[1];\r\n            this.y = values[2];\r\n            this.width = values[3];\r\n            this.height = values[4];\r\n        } catch (error) {\r\n            Tools.error('EnemyCollider error:', data);\r\n        }\r\n    }\r\n}\r\n/**\r\n * 敌机数据类\r\n */\r\nexport class EnemyPlaneData {\r\n    id: number = 0;\r\n    uiId: number = 0;\r\n    hp: number = 0;\r\n    dieBullet: boolean = false;\r\n    attack: number = 0;\r\n    defence: number = 0;\r\n    collideLevel: number = 0;\r\n    bTurnDir: boolean = false;\r\n    collideAttack: number = 0;\r\n    bCollideDead: boolean = false;\r\n    bMoveAttack: boolean = false;\r\n    bStayAttack: boolean = false;\r\n    attackInterval: number = 0;\r\n    attackNum: number = 0;\r\n    dieShoot: any[] = [];\r\n    attr: string = \"\";\r\n    param: string = \"\";\r\n    bAttackAbles: boolean[] = [];\r\n    attackArrNums: number[] = [];\r\n    attackPointArr: any[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载敌机数据\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: any): void {\r\n        this.id = data.id;\r\n        this.uiId = data.uiId;\r\n        this.attack = data.atk;\r\n        this.hp = data.hp;\r\n        this.collideLevel = data.collideLevel;\r\n        this.bTurnDir = data.turn;\r\n        this.collideAttack = data.collideAttack;\r\n        this.bCollideDead = data.bCollideDead;\r\n        this.bMoveAttack = data.bMoveAttack;\r\n        this.bStayAttack = data.bStayAttack;\r\n        this.attackInterval = data.attackInterval;\r\n        this.attackNum = data.attackNum;\r\n        this.param = data.param;\r\n        this.dieShoot = data.dieShoot;\r\n        this.dieBullet = data.dieBullet;\r\n\r\n\r\n        // 0,-20;0,1002,1,0.3,100;0,-20;0,1003,1,1,100;0,-20;0,1004,1,1,100\r\n        // 解析攻击点数据\r\n        if (data.hasOwnProperty(\"attackData\")) {\r\n            const sections = data.attackData.split(\"#\");\r\n\r\n            for (let h = 0; h < sections.length; h++) {\r\n                const attackGroups = []; // 二维数组，存储一个攻击点组的所有攻击点\r\n                let isAttackAble = false;\r\n                if (sections[h] !== \"\") {\r\n                    const points = sections[h].split(\";\");\r\n                    const attackPoints: any[] = []; // 一维数组，存储一个攻击点组中的单个攻击点\r\n\r\n                    for (let n = 0; n < points.length; n += 2) {\r\n                        if (points[n] !== \"\" && points[n + 1] !== \"\") {\r\n                            const attackPoint = new EnemyAttackPointData();\r\n                            attackPoint.loadJson(points[n], points[n + 1]);\r\n                            attackPoints.push(attackPoint);\r\n                            isAttackAble = true;\r\n                        }\r\n                    }\r\n\r\n                    attackGroups.push(attackPoints); // 将当前攻击点组添加到二维数组\r\n                }\r\n                this.attackPointArr.push(attackGroups); // 将二维数组作为一个整体添加到三维数组\r\n                this.attackArrNums.push(attackGroups.length); // 存储当前攻击点组的数量\r\n                this.bAttackAbles.push(isAttackAble); // 存储当前攻击点组是否可用\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\nclass TrackGroup {\r\n\r\n    points = []; // 轨迹点数组\r\n\r\n    /**\r\n     * 从 JSON 数据加载轨迹组\r\n     * @param {string} data 轨迹数据\r\n     */\r\n    loadJson(data) {\r\n        const points = data.split(';');\r\n        for (const point of points) {\r\n            if (point !== '') {\r\n                this.points.push(Tools.stringToPoint(point, ','));\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 敌人数据类\r\n */\r\nexport class EnemyData {\r\n\r\n    id = 0; // 敌人 ID\r\n    pos = v2(0, 0); // 敌人位置\r\n    trackArr = []; // 敌人轨迹数组\r\n\r\n\r\n    /**\r\n     * 从 JSON 数据加载敌人数据\r\n     * @param {Object} json JSON 数据\r\n     */\r\n    loadJson(json) {\r\n        const dataParts = json.split(\"#\");\r\n        if (dataParts.length > 0) {\r\n            const positionData = Tools.stringToNumber(dataParts[0], \",\");\r\n            this.id = positionData[0];\r\n            this.pos = v2(positionData[1], positionData[2]);\r\n        }\r\n\r\n        if (dataParts.length > 1 && dataParts[1] !== \"\") {\r\n            const trackData = dataParts[1].split(\"#\");\r\n            for (let i = 0; i < trackData.length; i++) {\r\n                if (trackData[i] !== \"\" && trackData[i].split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(trackData[i]);\r\n                    this.trackArr.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\nexport class EnemyUIData {\r\n    id = 0; // 敌人 ID\r\n    image = ''; // 敌人图片路径\r\n    isAm = false; // 是否为动画\r\n    collider = []; // 碰撞器数据\r\n    hpParam = []; // 血量参数\r\n    damageParam = []; // 伤害参数\r\n    clipArr = []; // 动画剪辑数组\r\n    blastSound = 0; // 爆炸音效 ID\r\n    blastCount = 0; // 爆炸次数\r\n    blastParam = []; // 爆炸参数\r\n    blastDurations = []; // 爆炸持续时间\r\n    blastShake = []; // 爆炸震动参数\r\n    extraParam = []; // 额外参数\r\n    extraParam1 = ''; // 额外参数 1\r\n    skillResistUIDict = {}; // 技能抗性字典\r\n    lootParam0 = []; // 掉落参数 0\r\n    lootParam1 = []; // 掉落参数 1\r\n    sneakParam = []; // 潜行参数\r\n    sneakAnim = ''; // 潜行动画\r\n    showParam = []; // 显示参数\r\n\r\n    /**\r\n     * 从 JSON 数据加载敌人 UI 数据\r\n     * @param {Object} data JSON 数据\r\n     */\r\n    loadJson(data) {\r\n        this.id = data.id;\r\n        this.image = data.image;\r\n        this.isAm = data.isAm;\r\n        this.collider = Tools.stringToNumber(data.collider, ',');\r\n        this.hpParam = Tools.stringToNumber(data.hpParam, ',');\r\n        this.blastSound = data.blastSound;\r\n        this.blastSound = data.blastSound;\r\n\r\n\r\n        if (data.hasOwnProperty('blp')) {\r\n            const params = data.blp.split(';');\r\n            if (params.length > 0 && params[0] !== '') {\r\n                this.blastCount = parseInt(params[0]);\r\n                for (let i = 1; i < params.length; i++) {\r\n                    if (params[i] !== '') {\r\n                        this.blastParam.push(Tools.stringToNumber(params[i], ','));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this.blastDurations = Tools.stringToNumber(data.blastDurations, ',');\r\n        this.blastShake = Tools.stringToNumber(data.blastShake, ',');\r\n        if (data.hasOwnProperty('damageParam')) {\r\n            const damageParams = data.damageParam.split(';');\r\n            for (const param of damageParams) {\r\n                if (param !== '') {\r\n                    this.damageParam.push(Tools.stringToNumber(param, ','));\r\n                }\r\n            }\r\n        }\r\n\r\n        if (data.hasOwnProperty('extraParam0')) {\r\n            const extraParams = data.extraParam0.split(';');\r\n            for (const param of extraParams) {\r\n                if (param !== '') {\r\n                    this.extraParam.push(Tools.stringToNumber(param, ','));\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty('extraParam1')) this.extraParam1 = data.extraParam1;\r\n\r\n        if (data.hasOwnProperty('skillResistUIDict')) {\r\n            const skillResistData = data.skillResistUIDict.split('#');\r\n            for (const entry of skillResistData) {\r\n                if (entry !== '') {\r\n                    const parts = entry.split(';');\r\n                    if (parts.length > 1) {\r\n                        const key = parseInt(parts[0]);\r\n                        const values = [];\r\n                        for (let i = 1; i < parts.length; i++) {\r\n                            if (parts[i] !== '') {\r\n                                values.push(Tools.stringToNumber(parts[i], ','));\r\n                            }\r\n                        }\r\n                        this.skillResistUIDict[key] = values;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        this.lootParam0 = Tools.stringToNumber(data.lootParam0, ',');\r\n        this.lootParam1 = Tools.stringToNumber(data.lootParam1, ',');\r\n        this.sneakAnim = data.sneakAnim;\r\n        this.showParam = Tools.stringToNumber(data.showParam, ',');\r\n    }\r\n}\r\n\r\nexport class EnemyShootData {\r\n    attackInterval = 0; // 攻击间隔\r\n    attackNum = 0; // 攻击次数\r\n    attackArrNum = 0; // 攻击数组数量\r\n    attackPointArr = []; // 攻击点数组\r\n\r\n    /**\r\n     * 从 JSON 数据加载敌人射击数据\r\n     * @param {Object} data JSON 数据\r\n     */\r\n    loadJson(data) {\r\n        if (data.hasOwnProperty('ain')) this.attackInterval = parseFloat(data.ain);\r\n        if (data.hasOwnProperty('aa')) this.attackNum = parseInt(data.aa);\r\n        if (data.hasOwnProperty('arrNum')) this.attackArrNum = parseInt(data.arrNum);\r\n        if (data.hasOwnProperty('points')) {\r\n            const pointsData = data.points.split(';');\r\n            for (const point of pointsData) {\r\n                if (point !== '') {\r\n                    this.attackPointArr.push(Tools.stringToNumber(point, ','));\r\n                }\r\n            }\r\n        }\r\n    }\r\n}"]}