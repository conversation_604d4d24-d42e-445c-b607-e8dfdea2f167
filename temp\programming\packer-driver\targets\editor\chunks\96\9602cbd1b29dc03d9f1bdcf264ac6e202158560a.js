System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, Tools, TrackData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  _export("TrackData", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c5ff6th7+NByIT6gIHL8PTk", "TrackData", undefined);

      _export("TrackData", TrackData = class TrackData {
        constructor() {
          this.trackID = 0;
          // 轨迹 ID
          this.type = 0;
          // 轨迹类型
          this.startX = 0;
          // 起点 X 坐标
          this.startY = 0;
          // 起点 Y 坐标
          this.control1X = 0;
          // 控制点 1 的 X 坐标
          this.control1Y = 0;
          // 控制点 1 的 Y 坐标
          this.control2X = 0;
          // 控制点 2 的 X 坐标
          this.control2Y = 0;
          // 控制点 2 的 Y 坐标
          this.endX = 0;
          // 终点 X 坐标
          this.endY = 0;
        }

        // 终点 Y 坐标

        /**
         * 加载 JSON 数据并解析轨迹信息
         * @param {Object} data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty('id')) {
            this.trackID = parseInt(data.id);
          }

          if (data.hasOwnProperty('tpe')) {
            this.type = parseInt(data.tpe);
          }

          if (data.hasOwnProperty('value')) {
            const variable = data.value;

            try {
              switch (this.type) {
                case 2:
                case 3:
                  {
                    const points = variable.split(';');
                    const start = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToPoint(points[0], ',');
                    this.startX = start.x;
                    this.startY = start.y;
                    const end = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToPoint(points[1], ',');
                    this.endX = end.x;
                    this.endY = end.y;
                    break;
                  }

                case 11:
                  {
                    const end = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToPoint(variable, ',');
                    this.endX = end.x;
                    this.endY = end.y;
                    break;
                  }

                default:
                  {
                    const points = variable.split(';');
                    const start = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToPoint(points[0], ',');
                    this.startX = start.x;
                    this.startY = start.y;

                    if (this.type === 0 || this.type === 4) {
                      const control1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).stringToPoint(points[1], ',');
                      this.control1X = control1.x;
                      this.control1Y = control1.y;
                      const control2 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).stringToPoint(points[2], ',');
                      this.control2X = control2.x;
                      this.control2Y = control2.y;
                      const end = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).stringToPoint(points[3], ',');
                      this.endX = end.x;
                      this.endY = end.y;
                    } else if (this.type === 1 || this.type === 5) {
                      const end = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).stringToPoint(points[1], ',');
                      this.endX = end.x;
                      this.endY = end.y;
                    }

                    break;
                  }
              }
            } catch (error) {
              console.error('Error parsing track data:', error);
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9602cbd1b29dc03d9f1bdcf264ac6e202158560a.js.map