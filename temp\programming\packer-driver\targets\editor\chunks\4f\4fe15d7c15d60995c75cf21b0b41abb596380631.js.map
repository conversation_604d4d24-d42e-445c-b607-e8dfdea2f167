{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts"], "names": ["LevelDataEventTriggerAudio", "LevelDataEventTrigger", "LevelDataEventTriggerType", "constructor", "Audio", "audioUUID"], "mappings": ";;;gFAEaA,0B;;;;;;;;;;;;;;;;;;AAFJC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,yB,iBAAAA,yB;;;;;;;4CAEnBF,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,0DAA+D;AAElEG,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sEAA0BC,KAAhC;AADU,eADPC,SACO,GADa,EACb;AAEb;;AAJiE,O", "sourcesContent": ["import { LevelDataEventTrigger, LevelDataEventTriggerType } from \"./LevelDataEventTrigger\";\r\n\r\nexport class LevelDataEventTriggerAudio extends LevelDataEventTrigger {\r\n    public audioUUID: string = \"\";\r\n    constructor() {\r\n        super(LevelDataEventTriggerType.Audio);\r\n    }\r\n}"]}