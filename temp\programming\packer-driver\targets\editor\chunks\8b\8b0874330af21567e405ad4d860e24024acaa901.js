System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Color, tween, UITransform, Sprite, UIOpacity, GameConst, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _class3, _crd, ccclass, property, EffectLayer;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Color = _cc.Color;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      Sprite = _cc.Sprite;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64819pKJyBLjJb6SVRBW/JT", "EffectLayer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Color', 'tween', 'UITransform', 'Sprite', 'UIOpacity']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EffectLayer = (_dec = ccclass('EffectLayer'), _dec2 = property(Node), _dec3 = property(Node), _dec(_class = (_class2 = (_class3 = class EffectLayer extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "whiteNode", _descriptor, this);

          _initializerDefineProperty(this, "redNode", _descriptor2, this);
        }

        onLoad() {
          EffectLayer.me = this;
          this.whiteNode.getComponent(UITransform).width = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewWidth;
          this.whiteNode.getComponent(UITransform).height = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight;
        }

        showWhiteScreen(delay, opacity = 255) {
          this.whiteNode.active = true;
          this.whiteNode.getComponent(UIOpacity).opacity = opacity;
          this.whiteNode.getComponent(Sprite).color = Color.WHITE;
          tween(this.whiteNode.getComponent(UIOpacity)).delay(4 * (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime).to(0.33, {
            opacity: 0
          }).call(() => {
            this.whiteNode.active = false;
          }).start();
        }

        lightingShow() {
          this.whiteNode.active = true;
          this.whiteNode.getComponent(UIOpacity).opacity = 140.25;
          this.whiteNode.getComponent(Sprite).color = Color.WHITE;
          tween(this.whiteNode).delay(2 / 30).call(() => {
            this.whiteNode.getComponent(UIOpacity).opacity = 178.5;
            this.whiteNode.getComponent(Sprite).color = Color.BLACK;
          }).delay(2 / 30).call(() => {
            this.whiteNode.active = false;
          }).delay(1 / 30).call(() => {
            this.whiteNode.getComponent(UIOpacity).opacity = 127.5;
            this.whiteNode.getComponent(Sprite).color = Color.BLACK;
          }).delay(1 / 30).call(() => {
            this.whiteNode.active = false;
          }).start();
        }

        showRedScreen() {
          this.redNode.getComponent(UIOpacity).opacity = 0;
          this.redNode.getComponent(UITransform).width = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewWidth;
          this.redNode.getComponent(UITransform).height = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight;
          const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime;
          tween(this.redNode.getComponent(UIOpacity)).to(0, {
            opacity: 204
          }).to(4 * frameTime, {
            opacity: 255
          }).to(2 * frameTime, {
            opacity: 224
          }).to(15 * frameTime, {
            opacity: 0
          }).start();
        }

      }, _class3.me = void 0, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "whiteNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "redNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8b0874330af21567e405ad4d860e24024acaa901.js.map