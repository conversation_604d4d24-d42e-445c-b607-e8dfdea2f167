{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts"], "names": ["_decorator", "BaseScreen", "GameConst", "ccclass", "property", "AimSingleLineScreen", "constructor", "config", "mainEntity", "m_angle", "props", "m_target", "setData", "sameDir", "posOffset", "m_config", "para", "length", "offset", "onInit", "m_count", "fire", "m_enemy", "attackPoint", "getAttackPoint", "getAimAngle", "i", "bullet", "createBullet", "x", "y", "angle", "init", "m_bulletState", "m_mainEntity", "update", "deltaTime", "GameAble"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACFC,MAAAA,U;;AACEC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;yBAGTK,mB,WADpBF,OAAO,CAAC,qBAAD,C,gBAAR,MACqBE,mBADrB;AAAA;AAAA,oCAC4D;AAQxDC,QAAAA,WAAW,CAACC,MAAD,EAAcC,UAAd,EAA+B;AACtC;AADsC,eAPlCC,OAOkC,GAPhB,CAOgB;AAAA,eANlCC,KAMkC;AAAA,eAF1CC,QAE0C;AAEtC,eAAKC,OAAL,CAAaL,MAAb,EAAqBC,UAArB;AAEA,eAAKE,KAAL,GAAa;AACTG,YAAAA,OAAO,EAAE,CADA;AAETC,YAAAA,SAAS,EAAE;AAFF,WAAb;;AAKA,cAAI,KAAKC,QAAL,CAAcC,IAAd,IAAsB,KAAKD,QAAL,CAAcC,IAAd,CAAmBC,MAAnB,GAA4B,CAAtD,EAAyD;AACrD,iBAAKP,KAAL,CAAWG,OAAX,GAAqB,KAAKE,QAAL,CAAcC,IAAd,CAAmB,CAAnB,CAArB;AACH,WAFD,MAEO;AACH,iBAAKN,KAAL,CAAWG,OAAX,GAAqB,CAArB;AACH;;AAED,eAAKH,KAAL,CAAWI,SAAX,GAAuB,KAAKC,QAAL,CAAcG,MAArC;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,eAAKC,OAAL,GAAe,CAAf;AACH;;AAES,cAAJC,IAAI,GAAG;AACT,cAAI,KAAKC,OAAL,KAAiB,CAAjB,IAAsB,KAAKX,QAAL,KAAkB,IAA5C,EAAkD;AAC9C,kBAAMY,WAAW,GAAG,KAAKC,cAAL,EAApB;;AAEA,gBAAI,KAAKd,KAAL,CAAWG,OAAX,KAAuB,CAAvB,IAA4B,KAAKO,OAAL,KAAiB,CAAjD,EAAoD;AAChD,mBAAKX,OAAL,GAAe,KAAKgB,WAAL,EAAf;AACH;;AAED,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,oBAAMC,MAAM,GAAG,MAAM,KAAKC,YAAL,EAArB;AACA,oBAAMC,CAAC,GAAGN,WAAW,CAACM,CAAtB;AACA,oBAAMC,CAAC,GAAGP,WAAW,CAACO,CAAtB;AACA,oBAAMC,KAAK,GAAG,KAAKtB,OAAnB;;AAEA,kBAAIkB,MAAJ,EAAY;AACRA,gBAAAA,MAAM,CAACK,IAAP,CAAY,KAAKV,OAAjB,EAA0B;AAAEO,kBAAAA,CAAF;AAAKC,kBAAAA,CAAL;AAAQC,kBAAAA;AAAR,iBAA1B,EAA2C,KAAKE,aAAhD,EAA+D,KAAKC,YAApE;AACH;AACJ;AACJ;AACJ;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,sCAAUC,QAAd,EAAwB,CACpB;AACH;AACJ;;AAvDuD,O", "sourcesContent": ["import { _decorator, Component, Tween,Node, Animation, Vec2, Prefab } from 'cc';\r\nimport BaseScreen from './BaseScreen';\r\nimport { GameConst } from '../../const/GameConst';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('AimSingleLineScreen')\r\nexport default class AimSingleLineScreen extends BaseScreen {\r\n    private m_angle: number = 0;\r\n    private props: {\r\n        sameDir: number;\r\n        posOffset: any[];\r\n    };\r\n    m_target: null;\r\n\r\n    constructor(config: any, mainEntity: any) {\r\n        super();\r\n        this.setData(config, mainEntity);\r\n\r\n        this.props = {\r\n            sameDir: 0,\r\n            posOffset: [],\r\n        };\r\n\r\n        if (this.m_config.para && this.m_config.para.length > 0) {\r\n            this.props.sameDir = this.m_config.para[0];\r\n        } else {\r\n            this.props.sameDir = 0;\r\n        }\r\n\r\n        this.props.posOffset = this.m_config.offset;\r\n    }\r\n\r\n    onInit() {\r\n        this.m_count = 0;\r\n    }\r\n\r\n    async fire() {\r\n        if (this.m_enemy !== 0 || this.m_target !== null) {\r\n            const attackPoint = this.getAttackPoint();\r\n\r\n            if (this.props.sameDir === 0 || this.m_count === 0) {\r\n                this.m_angle = this.getAimAngle();\r\n            }\r\n\r\n            for (let i = 0; i < 1; i++) {\r\n                const bullet = await this.createBullet();\r\n                const x = attackPoint.x;\r\n                const y = attackPoint.y;\r\n                const angle = this.m_angle;\r\n\r\n                if (bullet) {\r\n                    bullet.init(this.m_enemy, { x, y, angle }, this.m_bulletState, this.m_mainEntity);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameConst.GameAble) {\r\n            // Update logic can be added here if needed\r\n        }\r\n    }\r\n}"]}