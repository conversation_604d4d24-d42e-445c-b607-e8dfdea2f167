import { _decorator, Label, v3 } from 'cc';

import { logDebug } from '../../Utils/Logger';
import { DragButton } from '../common/components/button/DragButton';
import { BaseUI, UILayer, UIMgr } from '../UIMgr';
import { GmUI } from './GmUI';
const { ccclass, property } = _decorator;

@ccclass('GmButtonUI')
export class GmButtonUI extends BaseUI {
    public static getUrl(): string { return "ui/gm/GmButtonUI"; }
    public static getLayer(): UILayer { return UILayer.Top }

    protected start(): void {
        this.node.position = v3(300, 400, 0)
        this.getComponent(DragButton).addClick(this.onClick, this)
    }

    async onClick() {
        logDebug("GmButtonUI onClick", "aaaaaa")
        await UIMgr.openUI(GmUI)
        await UIMgr.hideUI(GmButtonUI)
    }

    async onShow(extraText?: string): Promise<void> {
        if (extraText) {
            this.getComponentInChildren(Label).string = "GM" + "\n(" + extraText + ")"
        } else {
            this.getComponentInChildren(Label).string = "GM"
        }
    }

    async onHide(...args: any[]): Promise<void> {
    }

    async onClose(...args: any[]): Promise<void> {
    }
}