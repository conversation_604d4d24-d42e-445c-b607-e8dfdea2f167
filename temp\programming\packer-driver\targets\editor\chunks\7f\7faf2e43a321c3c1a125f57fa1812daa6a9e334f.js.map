{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts"], "names": ["_decorator", "CCFloat", "Component", "Node", "CCString", "Prefab", "assetManager", "instantiate", "LevelDataBackgroundLayer", "LevelDataLayer", "LevelEditorLayerUI", "LevelEditorUtils", "ccclass", "property", "executeInEditMode", "BackgroundsNodeName", "<PERSON><PERSON><PERSON><PERSON>", "LevelBackgroundLayer", "backgroundsNode", "LevelEditorBaseUI", "backgroundLayerNode", "floorLayersNode", "skyLayersNode", "onLoad", "console", "log", "getOrAddNode", "node", "uuid", "update", "dt", "checkLayerNode", "floorLayers", "skyLayers", "tick", "progress", "bgCount", "Math", "ceil", "totalTime", "background<PERSON>ayer", "speed", "backgrounds", "length", "children", "bg", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "removeFromParent", "getComponent", "for<PERSON>ach", "layer", "add<PERSON><PERSON>er", "parentNode", "name", "layerNode", "layerCom", "addComponent", "layers", "removeLayerNodes", "push", "find", "element", "i", "<PERSON><PERSON><PERSON><PERSON>", "initByLevelData", "data", "levelname", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "background", "loadAny", "err", "prefab", "error", "setSiblingIndex", "initLayers", "dataLayers", "<PERSON><PERSON>ayer", "fillLevelLayerData", "dataLayer", "fillLevelData", "fillLevelLayersData", "map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;;AAE3DC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,c,iBAAAA,c;;AACrCC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2Cd,U;AAK3Ce,MAAAA,mB,GAAsB,a;AAGtBC,MAAAA,U,WADLJ,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACZ,OAAD,C,2BAJb,MACMe,UADN,CACiB;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEc,I;;;;;;;iBAEJ,E;;;AAGrBC,MAAAA,oB,YADLL,OAAO,CAAC,4BAAD,C,UAEHC,QAAQ,CAAC,CAACR,MAAD,CAAD,C,6BAFb,MACMY,oBADN,SACmCD,UADnC,CAC8C;AAAA;AAAA;;AAAA;;AAAA,eAGnCE,eAHmC,GAGN,IAHM;AAAA;;AAAA,O;;;;;iBAEX,E;;;;mCAMtBC,iB,YAFZP,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAACT,QAAD,C,UAERS,QAAQ,CAACZ,OAAD,C,WAGRY,QAAQ,CAACI,oBAAD,C,WAERJ,QAAQ,CAAC,CAACG,UAAD,CAAD,C,WAERH,QAAQ,CAAC,CAACG,UAAD,CAAD,C,6CAZb,MAEaG,iBAFb,SAEuCjB,SAFvC,CAEiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAarCkB,mBAbqC,GAaL,IAbK;AAAA,eAcrCC,eAdqC,GAcT,IAdS;AAAA,eAerCC,aAfqC,GAeX,IAfW;AAAA;;AAiB7CC,QAAAA,MAAM,GAAQ;AAAA;;AACVC,UAAAA,OAAO,CAACC,GAAR,CAAa,0BAAb;AACA,eAAKL,mBAAL,GAA2B;AAAA;AAAA,oDAAiBM,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,iBAAzC,CAA3B;AACA,eAAKN,eAAL,GAAuB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,aAAzC,CAAvB;AACA,eAAKL,aAAL,GAAqB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,WAAzC,CAArB;AACAH,UAAAA,OAAO,CAACC,GAAR,CAAa,2BAAD,yBAA2B,KAAKJ,eAAhC,qBAA2B,sBAAsBO,IAAK,EAAlE;AACH;;AACDC,QAAAA,MAAM,CAACC,EAAD,EAAiB;AACnB,eAAKC,cAAL,CAAoB,KAAKV,eAAzB,EAA0C,KAAKW,WAA/C;AACA,eAAKD,cAAL,CAAoB,KAAKT,aAAzB,EAAwC,KAAKW,SAA7C;AACH;;AACMC,QAAAA,IAAI,CAACC,QAAD,EAAwB;AAAA;;AAC/B,cAAIC,OAAO,GAAGC,IAAI,CAACC,IAAL,CAAU,KAAKC,SAAL,GAAiB,KAAKC,eAAL,CAAqBC,KAAtC,GAA8C,IAAxD,CAAd;;AACA,iBAAO,KAAKD,eAAL,CAAqBE,WAArB,CAAiCC,MAAjC,GAA0C,CAA1C,IAA+CP,OAAO,GAAG,KAAKI,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8CD,MAA9G,EAAsH;AAClH,gBAAIE,EAAE,GAAGtC,WAAW,CAAC,KAAKiC,eAAL,CAAqBE,WAArB,CAAiC,KAAKF,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8CD,MAA9C,GAAuD,KAAKH,eAAL,CAAqBE,WAArB,CAAiCC,MAAzH,CAAD,CAApB;AACAE,YAAAA,EAAE,CAACC,WAAH,CAAe,CAAf,EAAkB,KAAKN,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8CD,MAA9C,GAAuD,IAAzE,EAA+E,CAA/E;AACA,iBAAKH,eAAL,CAAqBtB,eAArB,CAAqC6B,QAArC,CAA8CF,EAA9C;AACH;;AACD,iBAAMT,OAAO,GAAG,KAAKI,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8CD,MAA9D,EAAsE;AAClE,iBAAKH,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8C,KAAKJ,eAAL,CAAqBtB,eAArB,CAAqC0B,QAArC,CAA8CD,MAA9C,GAAuD,CAArG,EAAwGK,gBAAxG;AACH;;AAED,wCAAKR,eAAL,CAAqBb,IAArB,mCAA2BsB,YAA3B;AAAA;AAAA,wDAAgFf,IAAhF,CACIC,QADJ,EACc,KAAKI,SADnB,EAC8B,KAAKC,eAAL,CAAqBC,KADnD;AAEA,eAAKT,WAAL,CAAiBkB,OAAjB,CAA0BC,KAAD,IAAW;AAAA;;AAChC,2BAAAA,KAAK,CAACxB,IAAN,yBAAYsB,YAAZ;AAAA;AAAA,0DAAiEf,IAAjE,CAAsEC,QAAtE,EAAgF,KAAKI,SAArF,EAAgGY,KAAK,CAACV,KAAtG;AACH,WAFD;AAGA,eAAKR,SAAL,CAAeiB,OAAf,CAAwBC,KAAD,IAAW;AAAA;;AAC9B,4BAAAA,KAAK,CAACxB,IAAN,0BAAYsB,YAAZ;AAAA;AAAA,0DAAiEf,IAAjE,CAAsEC,QAAtE,EAAgF,KAAKI,SAArF,EAAgGY,KAAK,CAACV,KAAtG;AACH,WAFD;AAGH;;AAEsB,eAARW,QAAQ,CAACC,UAAD,EAAmBC,IAAnB,EAAqD;AACxE,cAAIC,SAAS,GAAG,IAAIpD,IAAJ,CAASmD,IAAT,CAAhB;AACA,cAAIE,QAAQ,GAAGD,SAAS,CAACE,YAAV;AAAA;AAAA,uDAAf;AACAJ,UAAAA,UAAU,CAACN,QAAX,CAAoBQ,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEOzB,QAAAA,cAAc,CAACsB,UAAD,EAAmBK,MAAnB,EAA8C;AAChE,cAAIC,gBAAgB,GAAG,EAAvB;AACAN,UAAAA,UAAU,CAACT,QAAX,CAAoBM,OAApB,CAA4BvB,IAAI,IAAI;AAChC,gBAAI6B,QAAQ,GAAG7B,IAAI,CAACsB,YAAL;AAAA;AAAA,yDAAf;;AACA,gBAAIO,QAAQ,IAAI,IAAhB,EAAsB;AAClBhC,cAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BE,IAAI,CAAC2B,IAAK,4BAArD;AACAK,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBjC,IAAtB;AACA;AACH;;AACD,gBAAI+B,MAAM,CAACG,IAAP,CAAaV,KAAD,IAAWA,KAAK,CAACxB,IAAN,IAAcA,IAArC,KAA8C,IAAlD,EAAwD;AACpDH,cAAAA,OAAO,CAACC,GAAR,CAAa,+BAA8BE,IAAI,CAAC2B,IAAK,yBAArD;AACAK,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBjC,IAAtB;AACA;AACH;AACJ,WAZD;AAaAgC,UAAAA,gBAAgB,CAACT,OAAjB,CAAyBY,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACd,gBAAR;AACH,WAFD;AAGAU,UAAAA,MAAM,CAACR,OAAP,CAAe,CAACC,KAAD,EAAQY,CAAR,KAAc;AACzB,gBAAIZ,KAAK,CAACxB,IAAN,IAAc,IAAd,IAAsBwB,KAAK,CAACxB,IAAN,CAAWqC,OAAX,IAAsB,KAAhD,EAAuD;AACnDxC,cAAAA,OAAO,CAACC,GAAR,CAAa,gDAAb;AACA0B,cAAAA,KAAK,CAACxB,IAAN,GAAaR,iBAAiB,CAACiC,QAAlB,CAA2BC,UAA3B,EAAwC,SAAQU,CAAE,EAAlD,EAAqDpC,IAAlE;AACH;AACJ,WALD;AAMH;;AAEMsC,QAAAA,eAAe,CAACC,IAAD,EAAuB;AAAA;;AACzC,eAAKC,SAAL,GAAiBD,IAAI,CAACZ,IAAtB;AACA,eAAKf,SAAL,GAAiB2B,IAAI,CAAC3B,SAAtB;AAEA,eAAKnB,mBAAL,CAAyBgD,iBAAzB;AACA,eAAK5B,eAAL,GAAuB,IAAIvB,oBAAJ,EAAvB;AACA,eAAKuB,eAAL,CAAqBE,WAArB,GAAmC,EAAnC;AACA,mCAAAwB,IAAI,CAAC1B,eAAL,4DAAsBE,WAAtB,mCAAmCQ,OAAnC,CAA4CmB,UAAD,IAAgB;AACvD7C,YAAAA,OAAO,CAACC,GAAR,CAAY,mDAAZ;AACAnB,YAAAA,YAAY,CAACgE,OAAb,CAAqB;AAAC1C,cAAAA,IAAI,EAACyC;AAAN,aAArB,EAAwC,CAACE,GAAD,EAAMC,MAAN,KAAwB;AAC5D,kBAAID,GAAJ,EAAS;AACL/C,gBAAAA,OAAO,CAACiD,KAAR,CAAc,8DAAd,EAA8EF,GAA9E;AACA;AACH;;AACD,mBAAK/B,eAAL,CAAqBE,WAArB,CAAiCkB,IAAjC,CAAsCY,MAAtC;AACH,aAND;AAOH,WATD;AAUA,eAAKhC,eAAL,CAAqBC,KAArB,6BAA6ByB,IAAI,CAAC1B,eAAlC,qBAA6B,uBAAsBC,KAAnD;AACA,eAAKD,eAAL,CAAqBb,IAArB,GAA4BR,iBAAiB,CAACiC,QAAlB,CAA2B,KAAKhC,mBAAhC,EAAqD,OAArD,EAA8DO,IAA1F;AACA,eAAKa,eAAL,CAAqBtB,eAArB,GAAuC;AAAA;AAAA,oDAAiBQ,YAAjB,CAA8B,KAAKc,eAAL,CAAqBb,IAAnD,EAAyDZ,mBAAzD,CAAvC;AACA,eAAKyB,eAAL,CAAqBtB,eAArB,CAAqCwD,eAArC,CAAqD,CAArD;AACA,eAAKlC,eAAL,CAAqBb,IAArB,CAA0BsB,YAA1B;AAAA;AAAA,wDAA+EgB,eAA/E,CAA+FC,IAAI,CAAC1B,eAApG;AAEA,eAAKR,WAAL,GAAmB,EAAnB;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACAd,UAAAA,iBAAiB,CAACwD,UAAlB,CAA6B,KAAKtD,eAAlC,EAAmD,KAAKW,WAAxD,EAAqEkC,IAAI,CAAClC,WAA1E;AACAb,UAAAA,iBAAiB,CAACwD,UAAlB,CAA6B,KAAKrD,aAAlC,EAAiD,KAAKW,SAAtD,EAAiEiC,IAAI,CAACjC,SAAtE;AACH;;AAEwB,eAAV0C,UAAU,CAACtB,UAAD,EAAmBK,MAAnB,EAAyCkB,UAAzC,EAA4E;AACjGvB,UAAAA,UAAU,CAACe,iBAAX;AACAQ,UAAAA,UAAU,CAAC1B,OAAX,CAAmB,CAACC,KAAD,EAAQY,CAAR,KAAc;AAC7B,gBAAIc,UAAU,GAAG,IAAI7D,UAAJ,EAAjB;AAEA6D,YAAAA,UAAU,CAACpC,KAAX,GAAmBU,KAAK,CAACV,KAAzB;AACAoC,YAAAA,UAAU,CAAClD,IAAX,GAAkBR,iBAAiB,CAACiC,QAAlB,CAA2BC,UAA3B,EAAwC,SAAQU,CAAE,EAAlD,EAAqDpC,IAAvE;AACAkD,YAAAA,UAAU,CAAClD,IAAX,CAAgBsB,YAAhB;AAAA;AAAA,0DAAqEgB,eAArE,CAAqFd,KAArF;AAEAO,YAAAA,MAAM,CAACE,IAAP,CAAYiB,UAAZ;AACH,WARD;AASH;;AAEgC,eAAlBC,kBAAkB,CAAC3B,KAAD,EAAoB4B,SAApB,EAAoD;AACjFA,UAAAA,SAAS,CAACtC,KAAV,GAAkBU,KAAK,CAACV,KAAxB;AACAU,UAAAA,KAAK,CAACxB,IAAN,CAAWsB,YAAX;AAAA;AAAA,wDAAgE+B,aAAhE,CAA8ED,SAA9E;AACH;;AACiC,eAAnBE,mBAAmB,CAACvB,MAAD,EAAuBkB,UAAvB,EAA0D;AACxFlB,UAAAA,MAAM,CAACR,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAI0B,UAAU,GAAG;AAAA;AAAA,mDAAjB;AACA1D,YAAAA,iBAAiB,CAAC2D,kBAAlB,CAAqC3B,KAArC,EAA4C0B,UAA5C;AACAD,YAAAA,UAAU,CAAChB,IAAX,CAAgBiB,UAAhB;AACH,WAJD;AAKH;;AACMG,QAAAA,aAAa,CAACd,IAAD,EAAuB;AACvCA,UAAAA,IAAI,CAACZ,IAAL,GAAY,KAAKa,SAAjB;AACAD,UAAAA,IAAI,CAAC3B,SAAL,GAAiB,KAAKA,SAAtB;AAEA2B,UAAAA,IAAI,CAAC1B,eAAL,GAAuB;AAAA;AAAA,qEAAvB;AACA0B,UAAAA,IAAI,CAAC1B,eAAL,CAAqBE,WAArB,GAAmC,KAAKF,eAAL,CAAqBE,WAArB,CAAiCwC,GAAjC,CAAsCV,MAAD,IAAYA,MAAM,CAAC5C,IAAxD,CAAnC;AACAT,UAAAA,iBAAiB,CAAC2D,kBAAlB,CAAqC,KAAKtC,eAA1C,EAA2D0B,IAAI,CAAC1B,eAAhE;AAEA0B,UAAAA,IAAI,CAAClC,WAAL,GAAmB,EAAnB;AACAkC,UAAAA,IAAI,CAACjC,SAAL,GAAiB,EAAjB;AACAd,UAAAA,iBAAiB,CAAC8D,mBAAlB,CAAsC,KAAKjD,WAA3C,EAAwDkC,IAAI,CAAClC,WAA7D;AACAb,UAAAA,iBAAiB,CAAC8D,mBAAlB,CAAsC,KAAKhD,SAA3C,EAAsDiC,IAAI,CAACjC,SAA3D;AACH;;AAnJ4C,O;;;;;iBAElB,E;;;;;;;iBAEA,E;;;;;;;iBAGoB,IAAIhB,oBAAJ,E;;;;;;;iBAEZ,E;;;;;;;iBAEF,E", "sourcesContent": ["import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LevelData, LevelDataBackgroundLayer, LevelDataLayer } from '../../scripts/leveldata/leveldata';\r\nimport { LevelEditorLayerUI } from './LevelEditorLayerUI';\r\nimport { LevelEditorUtils } from './utils';\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('EditorLevelLayer')\r\nclass LevelLayer {\r\n    @property(Node)\r\n    public node: Node | null = null;\r\n    @property(CCFloat)\r\n    public speed: number = 10;\r\n}\r\n@ccclass('EditorLevelBackgroundLayer')\r\nclass LevelBackgroundLayer extends LevelLayer {\r\n    @property([Prefab])\r\n    public backgrounds: Prefab[] = [];\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\n@ccclass('LevelEditorBaseUI')\r\n@executeInEditMode()\r\nexport class LevelEditorBaseUI extends Component {\r\n    @property(CCString)\r\n    public levelname: string = \"\";\r\n    @property(CCFloat)\r\n    public totalTime: number = 10;\r\n\r\n    @property(LevelBackgroundLayer)\r\n    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();\r\n    @property([LevelLayer])\r\n    public floorLayers: LevelLayer[] = [];\r\n    @property([LevelLayer])\r\n    public skyLayers: LevelLayer[] = [];\r\n\r\n    private backgroundLayerNode:Node|null = null;\r\n    private floorLayersNode:Node|null = null;\r\n    private skyLayersNode:Node|null = null;\r\n\r\n    onLoad():void {\r\n        console.log(`LevelEditorBaseUI start.`);\r\n        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"FloorLayers\");\r\n        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"SkyLayers\");\r\n        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);\r\n    }\r\n    update(dt:number):void {\r\n        this.checkLayerNode(this.floorLayersNode, this.floorLayers);\r\n        this.checkLayerNode(this.skyLayersNode, this.skyLayers);\r\n    }\r\n    public tick(progress: number):void {\r\n        var bgCount = Math.ceil(this.totalTime * this.backgroundLayer.speed / 1280)\r\n        while (this.backgroundLayer.backgrounds.length > 0 && bgCount > this.backgroundLayer.backgroundsNode.children.length) {\r\n            var bg = instantiate(this.backgroundLayer.backgrounds[this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length])\r\n            bg.setPosition(0, this.backgroundLayer.backgroundsNode.children.length * 1280, 0)\r\n            this.backgroundLayer.backgroundsNode.addChild(bg)\r\n        }\r\n        while(bgCount < this.backgroundLayer.backgroundsNode.children.length) {\r\n            this.backgroundLayer.backgroundsNode.children[this.backgroundLayer.backgroundsNode.children.length - 1].removeFromParent()\r\n        }\r\n\r\n        this.backgroundLayer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(\r\n            progress, this.totalTime, this.backgroundLayer.speed);\r\n        this.floorLayers.forEach((layer) => {\r\n            layer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            layer.node?.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);\r\n        });\r\n    }\r\n\r\n    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    private checkLayerNode(parentNode: Node, layers: LevelLayer[]):void {\r\n        var removeLayerNodes = []\r\n        parentNode.children.forEach(node => {\r\n            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n            if (layerCom == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n            if (layers.find((layer) => layer.node == node) == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because not in layers\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n        });\r\n        removeLayerNodes.forEach(element => {\r\n            element.removeFromParent();    \r\n        });\r\n        layers.forEach((layer, i) => {\r\n            if (layer.node == null || layer.node.isValid == false) {\r\n                console.log(`Level checkLayerNode add because layer == null`);\r\n                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; \r\n            }\r\n        });\r\n    }\r\n\r\n    public initByLevelData(data: LevelData):void {\r\n        this.levelname = data.name;\r\n        this.totalTime = data.totalTime\r\n\r\n        this.backgroundLayerNode.removeAllChildren()\r\n        this.backgroundLayer = new LevelBackgroundLayer();\r\n        this.backgroundLayer.backgrounds = [];\r\n        data.backgroundLayer?.backgrounds?.forEach((background) => {\r\n            console.log(\"LevelEditorBaseUI initByLevelData load background\")\r\n            assetManager.loadAny({uuid:background}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorBaseUI initByLevelData load background prefab err\", err);\r\n                    return\r\n                } \r\n                this.backgroundLayer.backgrounds.push(prefab);\r\n            });\r\n        });\r\n        this.backgroundLayer.speed = data.backgroundLayer?.speed\r\n        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, \"layer\").node;\r\n        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);\r\n        this.backgroundLayer.backgroundsNode.setSiblingIndex(0);\r\n        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).initByLevelData(data.backgroundLayer);\r\n    \r\n        this.floorLayers = []\r\n        this.skyLayers = []\r\n        LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        parentNode.removeAllChildren()\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;\r\n            levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).initByLevelData(layer);\r\n\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private static fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {\r\n        dataLayer.speed = layer.speed;\r\n        layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI).fillLevelData(dataLayer);\r\n    }\r\n    private static fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        layers.forEach((layer) => {\r\n            var levelLayer = new LevelDataLayer();\r\n            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);\r\n            dataLayers.push(levelLayer);\r\n        });\r\n    }\r\n    public fillLevelData(data: LevelData):void {\r\n        data.name = this.levelname;\r\n        data.totalTime = this.totalTime;\r\n\r\n        data.backgroundLayer = new LevelDataBackgroundLayer();\r\n        data.backgroundLayer.backgrounds = this.backgroundLayer.backgrounds.map((prefab) => prefab.uuid);\r\n        LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);\r\n\r\n        data.floorLayers = []\r\n        data.skyLayers = []\r\n        LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);\r\n    }\r\n}"]}