{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EditBox", "csproto", "MyApp", "DevLoginData", "logDebug", "logError", "DataMgr", "uiSelect", "GmButtonUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "DevLoginUI", "_onGetRoleBound", "onGetRole", "bind", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onHide", "args", "onShow", "loginButton", "node", "on", "EventType", "CLICK", "onLoginButtonClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ROLE", "serverList", "for<PERSON>ach", "value", "key", "serverSelect", "itemDatas", "push", "setChooseItemData", "instance", "servername", "onChooseItem", "itemData", "usernameEditBox", "string", "user", "passwordEditBox", "password", "onClose", "unregister<PERSON><PERSON><PERSON>", "username", "platformSDK", "login", "err", "info", "msg", "closeUI", "needLogin", "init", "openUI"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;;AACtBC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AAEVC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OACpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;4BAGjBgB,U,WADZF,OAAO,CAAC,YAAD,C,UAMHC,QAAQ,CAACd,MAAD,C,UAERc,QAAQ,CAACb,OAAD,C,UAERa,QAAQ,CAACb,OAAD,C,UAGRa,QAAQ;AAAA;AAAA,+B,sCAbb,MACaC,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAe3BC,eAf2B,GAe0B,KAAKC,SAAL,CAAeC,IAAf,CAAoB,IAApB,CAf1B;AAAA;;AACf,eAANC,MAAM,GAAW;AAAE,iBAAO,eAAP;AAAwB;;AACnC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAe5C,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAC3C;;AAEW,cAANC,MAAM,CAAC,GAAGD,IAAJ,EAAgC;AACxC,eAAKE,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyB3B,MAAM,CAAC4B,SAAP,CAAiBC,KAA1C,EAAiD,KAAKC,kBAAtD,EAA0E,IAA1E;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAA/C,EAAgE,KAAKnB,eAArE;AAEA;AAAA;AAAA,4CAAaoB,UAAb,CAAwBC,OAAxB,CAAgC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAC5C,iBAAKC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAjC;AACA,iBAAKC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAG,GAAG,GAAvC;AACH,WAHD;AAIA,eAAKC,YAAL,CAAkBG,iBAAlB,CAAoC;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,UAA1D;;AACA,eAAKL,YAAL,CAAkBM,YAAlB,GAAkCC,QAAD,IAAsB;AACnD;AAAA;AAAA,sCAAS,SAAT,EAAqB,iBAAgBA,QAAS,EAA9C;AACA;AAAA;AAAA,8CAAaH,QAAb,CAAsBC,UAAtB,GAAmCE,QAAnC;AACH,WAHD;;AAIA,eAAKC,eAAL,CAAqBC,MAArB,GAA8B;AAAA;AAAA,4CAAaL,QAAb,CAAsBM,IAApD;AACA,eAAKC,eAAL,CAAqBF,MAArB,GAA8B;AAAA;AAAA,4CAAaL,QAAb,CAAsBQ,QAApD;AACH;;AAEY,cAAPC,OAAO,CAAC,GAAG9B,IAAJ,EAAgC;AACzC;AAAA;AAAA,8BAAMQ,MAAN,CAAauB,iBAAb,CAA+B;AAAA;AAAA,kCAAQrB,EAAR,CAAWC,MAAX,CAAkBC,eAAjD,EAAkE,KAAKnB,eAAvE;AACH;;AAEDc,QAAAA,kBAAkB,GAAG;AACjB,cAAIyB,QAAQ,GAAG,KAAKP,eAAL,CAAqBC,MAApC;AACA,cAAIG,QAAQ,GAAG,KAAKD,eAAL,CAAqBF,MAApC;AACA;AAAA;AAAA,4CAAaL,QAAb,CAAsBM,IAAtB,GAA6BK,QAA7B;AACA;AAAA;AAAA,4CAAaX,QAAb,CAAsBQ,QAAtB,GAAiCA,QAAjC;AAEA;AAAA;AAAA,8BAAMI,WAAN,CAAkBC,KAAlB,CAAwB,CAACC,GAAD,EAAMC,IAAN,KAAe;AACnC,gBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,wCAAS,YAAT,EAAwB,gBAAeA,GAAI,EAA3C;AACA;AACH;;AACD;AAAA;AAAA,gCAAM3B,MAAN,CAAa0B,KAAb,CAAmBE,IAAnB;AACH,WAND;AAOH;;AAED1C,QAAAA,SAAS,CAAC2C,GAAD,EAA0B;AAC/B;AAAA;AAAA,8BAAMC,OAAN,CAAc9C,UAAd;AACAA,UAAAA,UAAU,CAAC+C,SAAX,GAAuB,KAAvB;AACA;AAAA;AAAA,kCAAQC,IAAR;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AA7DkC,O,UAG5BF,S,GAAY,I;;;;;iBAGG,I;;;;;;;iBAEK,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Button, EditBox } from 'cc';\nimport csproto from '../AutoGen/PB/cs_proto.js';\nimport { MyApp } from '../MyApp';\nimport { DevLoginData } from '../PlatformSDK/DevLoginData';\nimport { logDebug, logError } from '../Utils/Logger';\n\nimport { DataMgr } from '../Data/DataManager';\nimport { uiSelect } from './common/components/SelectList/uiSelect';\nimport { GmButtonUI } from './gm/GmButtonUI';\nimport { BaseUI, UILayer, UIMgr } from './UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('DevLoginUI')\nexport class Dev<PERSON>oginUI extends BaseUI {\n    public static getUrl(): string { return \"ui/DevLoginUI\" };\n    public static getLayer(): UILayer { return UILayer.Top }\n    static needLogin = true;\n\n    @property(Button)\n    loginButton: Button = null!;\n    @property(EditBox)\n    usernameEditBox: EditBox = null!;\n    @property(EditBox)\n    passwordEditBox: EditBox = null!;\n\n    @property(uiSelect)\n    serverSelect: uiSelect = null!;\n\n    private _onGetRoleBound: (msg: csproto.cs.IS2CMsg) => void = this.onGetRole.bind(this);\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound);\n\n        DevLoginData.serverList.forEach((value, key) => {\n            this.serverSelect.itemDatas.push(key)\n            this.serverSelect.itemDatas.push(key + \"1\")\n        });\n        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)\n        this.serverSelect.onChooseItem = (itemData: string) => {\n            logDebug(\"LoginUI\", `choose server ${itemData}`)\n            DevLoginData.instance.servername = itemData;\n        }\n        this.usernameEditBox.string = DevLoginData.instance.user;\n        this.passwordEditBox.string = DevLoginData.instance.password;\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound)\n    }\n\n    onLoginButtonClick() {\n        var username = this.usernameEditBox.string;\n        var password = this.passwordEditBox.string;\n        DevLoginData.instance.user = username;\n        DevLoginData.instance.password = password;\n\n        MyApp.platformSDK.login((err, info) => {\n            if (err) {\n                logError(\"DevLoginUI\", `login failed ${err}`);\n                return;\n            }\n            MyApp.netMgr.login(info);\n        });\n    }\n\n    onGetRole(msg: csproto.cs.IS2CMsg) {\n        UIMgr.closeUI(DevLoginUI);\n        DevLoginUI.needLogin = false;\n        DataMgr.init()\n        UIMgr.openUI(GmButtonUI)\n    }\n}"]}