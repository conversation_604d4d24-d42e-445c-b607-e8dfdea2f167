import { _decorator, assetManager, Component, instantiate, Node, Prefab, UITransform } from "cc";
import { LevelData, LevelDataLayer } from "../../../leveldata/leveldata";
import { LevelLayerUI } from "./LevelLayerUI";

const { ccclass, property } = _decorator;

const BackgroundsNodeName = "backgrounds";

@ccclass('LevelLayer')
class LevelLayer {
    public node: Node | null = null;
    public speed: number = 0;
}

@ccclass('LevelBackgroundLayer')
class LevelBackgroundLayer extends LevelLayer {
    public backgrounds: Prefab[] = [];
    public backgroundsNode: Node|null = null;
}

@ccclass('LevelBaseUI')
export class LevelBaseUI extends Component {
    private _totalTime: number = 10; // 当前关卡的时长
    private _lastLevelHeight: number = 0; // 上一关的关卡高度
    private _lastLevelOffsetY: number = 0; // 上一关的关卡偏移量
    
    private _backgroundLayerNode:Node|null = null;
    private _floorLayersNode:Node|null = null;
    private _skyLayersNode:Node|null = null;

    private _backgroundLayer: LevelBackgroundLayer = null;
    private _floorLayers: LevelLayer[] = [];
    private _skyLayers: LevelLayer[] = [];

    public get floorLayers(): LevelLayer[] {
        return this._floorLayers;
    }
    public get skyLayers(): LevelLayer[] {
        return this._skyLayers;
    }
    public get backgroundLayer(): LevelBackgroundLayer {
        return this._backgroundLayer;
    }

    public get TotalTime(): number {
        return this._totalTime;
    }

    public getLevelTotalHeightByIndex(index: number): number {
        var totalHeight = 0;
        if (this.backgroundLayer) {
            var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${index}`);
            if (lastBgNode) {
                const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);
                if (backgroundsNode) {
                    backgroundsNode.children.forEach((bg) => {
                        const height = bg.getComponent(UITransform)?.contentSize.height;
                        totalHeight += height;
                    });
                }
            }
        }

        return totalHeight;
    }

    protected onLoad(): void {
    }

    private _getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    public async levelPrefab(levelData: LevelData, levelInfo:{ levelID: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {
        this._backgroundLayerNode = this._getOrAddNode(this.node, "BackgroundLayer");
        this._floorLayersNode = this._getOrAddNode(this.node, "FloorLayers");
        this._skyLayersNode = this._getOrAddNode(this.node, "SkyLayers");
        if (bFristLevel) {
            await this._initByLevelData(levelData,levelInfo);
        } else {
            this._initByLevelData(levelData,levelInfo);
            return Promise.resolve();
        }
    }

    public setBackgroundLayerInfo(speed: number, time: number): void {
        this.backgroundLayer.speed = speed;
        this._totalTime = time;
    }

    public async _initByLevelData(data: LevelData, levelInfo:{ levelID: number, levelIndex: number }, bFristLevel: boolean = false):Promise<void> {
        //if (bFristLevel) {
            await this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo);
        //} else {
            //this._initBackgroundLayer(this._backgroundLayerNode, data, levelInfo);
        //}
        this._initLayers(this._floorLayersNode, this.floorLayers, data.floorLayers, bFristLevel);
        this._initLayers(this._skyLayersNode, this.skyLayers, data.skyLayers, bFristLevel);
    }

    private _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[], bFristLevel: boolean): void {
        dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = this._addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.getComponent<LevelLayerUI>(LevelLayerUI).initByLevelData(layer,this._lastLevelOffsetY);
            layers.push(levelLayer);
        });
    }

    private async _initBackgroundLayer(parentNode: Node, data: LevelData, levelInfo:{ levelID: number, levelIndex: number }): Promise<void> {
        if (data.backgroundLayer.backgrounds.length > 0) { 
            if (this._backgroundLayer === null) {
                this._backgroundLayer = new LevelBackgroundLayer();
                this._backgroundLayer.backgrounds = [];
            }
            this._backgroundLayer.speed = data.backgroundLayer.speed;
            var bgCount = Math.ceil(data.totalTime * this._backgroundLayer.speed / 1280);
            const loadPromises = data.backgroundLayer.backgrounds.map((backgroundLayer) => {
                return new Promise<void>((resolve, reject) => {
                    assetManager.loadAny({ uuid: backgroundLayer }, (err, prefab: Prefab) => {
                        if (err) {
                            console.error('LevelBaseUI', 'initByLevelData load background prefab err', err);
                            reject(err);
                        } else {
                            this._backgroundLayer.backgrounds.push(prefab);
                            resolve();
                        }
                    });
                });
            });

            await Promise.all(loadPromises);
            // 节点设置偏移
            var offsetY = 0;
            this._lastLevelHeight = 0;
            if (this.backgroundLayer) {
                var lastBgNode = this._backgroundLayerNode.getChildByName(`layer_${levelInfo.levelIndex - 1}`);
                if (lastBgNode) {
                    offsetY = lastBgNode.getPosition().y;
                    const backgroundsNode = lastBgNode.getChildByName(BackgroundsNodeName);
                    if (backgroundsNode) {
                        backgroundsNode.children.forEach((bg) => {
                            const height = bg.getComponent(UITransform)?.contentSize.height;
                            this._lastLevelHeight += height;
                        });
                    }
                }
            }

            this._lastLevelOffsetY = this._lastLevelHeight + offsetY;
            console.log('LevelBaseUI', "_initBackgroundLayer _lastLevelHeight", this._lastLevelHeight, "offsetY", offsetY);

            this.backgroundLayer.node = this._addLayer(parentNode, `layer_${levelInfo.levelIndex}`).node;
            this.backgroundLayer.backgroundsNode = this._getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
            this.backgroundLayer.node.getComponent<LevelLayerUI>(LevelLayerUI).initByLevelData(data.backgroundLayer, this._lastLevelOffsetY);
            this.backgroundLayer.backgroundsNode.setSiblingIndex(0);

            var pos = 0;
            while (this._backgroundLayer.backgrounds.length > 0 && bgCount > this._backgroundLayer.backgroundsNode.children.length) {
                var bg = instantiate(this._backgroundLayer.backgrounds[this._backgroundLayer.backgroundsNode.children.length % this._backgroundLayer.backgrounds.length]);
                const height = bg.getComponent(UITransform)?.contentSize.height;
                
                bg.setPosition(0, pos, 0);
                pos += height;
                this._backgroundLayer.backgroundsNode.addChild(bg);
            }
        }
    }

    private _addLayer(parentNode: Node, name: string): LevelLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    public tick(deltaTime: number): void {
        this._backgroundLayerNode.children.forEach((node) => {
            node.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, this.backgroundLayer.speed);
        });
        this.floorLayers.forEach((layer) => {
            layer.node?.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, layer.speed);
        });
        this.skyLayers.forEach((layer) => {
            layer.node?.getComponent<LevelLayerUI>(LevelLayerUI).tick(deltaTime, layer.speed);
        });
    }
}