{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Enum", "Vec3", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "acceleration", "accelerationAngle", "target", "arrivalDistance", "isVisible", "onBecomeVisible", "onBecomeInvisible", "tick", "dt", "lastPos", "node", "getPosition", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "newPos", "z", "setPosition", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "checkVisibility", "setVisible", "visible", "<PERSON><PERSON><PERSON><PERSON>", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;;;;;;;;OACtC;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCL,I;OACzC;AAAEM,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;sCAGrCU,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;yBASCC,O,WAFZJ,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAET,IAAI,CAACO,oBAAD,CAAX;AAAmCG,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gBAHZJ,iB,qBADD,MAEaE,OAFb,SAE6BT,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDY,eALgD,GAKrB,KALqB;AAKb;AALa,eAMhDC,gBANgD,GAMpB,KANoB;AAMb;AANa,eAOhDC,KAPgD;AAOb;AAPa,eAQhDC,UARgD;AAQb;AARa,eAShDC,YATgD;AASb;AATa,eAUhDC,iBAVgD;AAUb;AAVa,eAYhDC,MAZgD,GAY1B,IAZ0B;AAYb;AAZa,eAahDC,eAbgD,GAatB,EAbsB;AAab;AAba,eAe/CC,SAf+C,GAe1B,KAf0B;AAeb;AAfa,eAgBhDC,eAhBgD,GAgBb,IAhBa;AAAA,eAiBhDC,iBAjBgD,GAiBX,IAjBW;AAAA;;AAmBhDC,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B;AACA,gBAAMC,OAAO,GAAG,KAAKC,IAAL,CAAUC,WAAV,EAAhB,CAF0B,CAI1B;;AACA,cAAIC,SAAS,GAAG,KAAKd,KAAL,GAAae,IAAI,CAACC,GAAL,CAAS3B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;AACA,cAAIgB,SAAS,GAAG,KAAKjB,KAAL,GAAae,IAAI,CAACG,GAAL,CAAS7B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;;AAEA,cAAI,KAAKF,gBAAL,IAAyB,KAAKK,MAAlC,EAA0C;AACtC,kBAAMe,SAAS,GAAG,KAAKf,MAAL,CAAYS,WAAZ,EAAlB;AACA,kBAAMO,UAAU,GAAG,KAAKR,IAAL,CAAUC,WAAV,EAAnB,CAFsC,CAItC;;AACA,kBAAMQ,UAAU,GAAGF,SAAS,CAACG,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,kBAAMC,UAAU,GAAGJ,SAAS,CAACK,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,kBAAMC,QAAQ,GAAGV,IAAI,CAACW,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,oBAAME,YAAY,GAAGrC,gBAAgB,CAACyB,IAAI,CAACa,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,oBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAK1B,UAAtC,CALc,CAMd;;AACA,oBAAM6B,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,oBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,oBAAMC,WAAW,GAAG,GAApB,CAXc,CAWW;;AACzB,oBAAMC,UAAU,GAAGlB,IAAI,CAACmB,GAAL,CAASnB,IAAI,CAACoB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGtB,EAAtD,IAA4DK,IAAI,CAACqB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAK7B,UAAL,IAAmBgC,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAjB,cAAAA,SAAS,GAAG,KAAKd,KAAL,GAAae,IAAI,CAACC,GAAL,CAAS3B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACAgB,cAAAA,SAAS,GAAG,KAAKjB,KAAL,GAAae,IAAI,CAACG,GAAL,CAAS7B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACH;AACJ,WArCyB,CAuC1B;;;AACA,gBAAMoC,aAAa,GAAG,KAAKnC,YAAL,GAAoBa,IAAI,CAACC,GAAL,CAAS3B,gBAAgB,CAAC,KAAKc,iBAAN,CAAzB,CAA1C;AACA,gBAAMmC,aAAa,GAAG,KAAKpC,YAAL,GAAoBa,IAAI,CAACG,GAAL,CAAS7B,gBAAgB,CAAC,KAAKc,iBAAN,CAAzB,CAA1C,CAzC0B,CA2C1B;;AACA,gBAAMoC,YAAY,GAAGzB,SAAS,GAAGuB,aAAa,GAAG3B,EAAjD;AACA,gBAAM8B,YAAY,GAAGvB,SAAS,GAAGqB,aAAa,GAAG5B,EAAjD,CA7C0B,CA+C1B;;AACA,eAAKV,KAAL,GAAae,IAAI,CAACW,IAAL,CAAUa,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb;AACA,eAAKvC,UAAL,GAAkBX,gBAAgB,CAACyB,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAlC,CAjD0B,CAmD1B;;AACA,gBAAME,MAAM,GAAG,IAAIrD,IAAJ,CACXuB,OAAO,CAACW,CAAR,GAAYiB,YAAY,GAAG7B,EADhB,EAEXC,OAAO,CAACa,CAAR,GAAYgB,YAAY,GAAG9B,EAFhB,EAGXC,OAAO,CAAC+B,CAHG,CAAf;AAKA,eAAK9B,IAAL,CAAU+B,WAAV,CAAsBF,MAAtB;;AAEA,cAAI,KAAK3C,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,kBAAM4C,aAAa,GAAGtD,gBAAgB,CAACyB,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAtC;AACA,kBAAMM,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAKlC,IAAL,CAAUmC,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACWG,QAAAA,eAAe,GAAS;AAC3B,gBAAM1C,SAAS,GAAG,IAAlB,CAD2B,CACJ;;AACvB,eAAK2C,UAAL,CAAgB3C,SAAhB;AACH;;AAEM2C,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAK5C,SAAL,KAAmB4C,OAAvB,EAAgC;AAEhC,eAAK5C,SAAL,GAAiB4C,OAAjB;;AACA,cAAIA,OAAO,IAAI,KAAK3C,eAApB,EAAqC;AACjC,iBAAKA,eAAL;AACH,WAFD,MAEO,IAAI,CAAC2C,OAAD,IAAY,KAAK1C,iBAArB,EAAwC;AAC3C,iBAAKA,iBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACW2C,QAAAA,SAAS,CAAC/C,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKL,gBAAL,GAAwBK,MAAM,KAAK,IAAnC;AACH;;AA9GsD,O;;;;;iBAGVV,oBAAoB,CAAC0D,E", "sourcesContent": ["import { _decorator, misc, Component, Enum, Vec3, Node } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable } from './IMovable';\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;  // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false; // 是否正在追踪目标\r\n    public speed: number;                     // 速度\r\n    public speedAngle: number;                // 速度方向 (用角度表示)\r\n    public acceleration: number;              // 加速度\r\n    public accelerationAngle: number;         // 加速度方向 (用角度表示)\r\n\r\n    public target: Node | null = null;        // 追踪的目标节点\r\n    public arrivalDistance: number = 10;      // 到达目标的距离\r\n\r\n    private isVisible: boolean = false;       // 是否可见\r\n    public onBecomeVisible: Function | null = null;\r\n    public onBecomeInvisible: Function | null = null;\r\n\r\n    public tick(dt: number): void {\r\n        // 根据移动属性更新位置\r\n        const lastPos = this.node.getPosition();\r\n        \r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n            \r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n            \r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n                \r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n                \r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = 180; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n                \r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                \r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));\r\n        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        const newPos = new Vec3(\r\n            lastPos.x + newVelocityX * dt,\r\n            lastPos.y + newVelocityY * dt,\r\n            lastPos.z\r\n        );\r\n        this.node.setPosition(newPos);\r\n\r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞，这里可以直接查询格子坐标来获取当前是否在屏幕内\r\n     */\r\n    public checkVisibility(): void {\r\n        const isVisible = true;// && this.node.getWorldBoundingBox().intersects(this.node.getScene().getBoundingBox());\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this.isVisible === visible) return;\r\n\r\n        this.isVisible = visible;\r\n        if (visible && this.onBecomeVisible) {\r\n            this.onBecomeVisible();\r\n        } else if (!visible && this.onBecomeInvisible) {\r\n            this.onBecomeInvisible();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n}"]}