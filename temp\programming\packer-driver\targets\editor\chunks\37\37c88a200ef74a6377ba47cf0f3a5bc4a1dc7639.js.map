{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts"], "names": ["_decorator", "NodePool", "instantiate", "Prefab", "SingletonBase", "MyApp", "ccclass", "PrefabManager", "m_pool", "Map", "preload", "prefabClass", "PrefabName", "Error", "resMgr", "preloadAsync", "_create", "node", "prefab", "loadAsync", "console", "log", "create", "createSync", "createComponent", "getComponent", "createFrame", "count", "result", "batch", "i", "push", "length", "setTimeout", "createFrameByName", "prefabName", "get", "pool", "size", "createPool", "set", "put", "add"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;;AAEnCC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcN,U;;yBAGCO,a,WADpBD,OAAO,CAAC,eAAD,C,gBAAR,MACqBC,aADrB;AAAA;AAAA,0CACwE;AAAA;AAAA;AAAA,eAEpEC,MAFoE,GAE3D,IAAIC,GAAJ,EAF2D;AAAA;;AAEhD;;AAGpB;AACJ;AACA;AACA;AACA;AACiB,cAAPC,OAAO,CAACC,WAAD,EAAc;AACvB,cAAI,CAACA,WAAW,CAACC,UAAjB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,gBAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,YAAb,CAA0BJ,WAAW,CAACC,UAAtC,CAAN;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACiB,cAAPI,OAAO,CAACL,WAAD,EAAc;AACvB,cAAIM,IAAS,GAAG,IAAhB;;AACA,cAAIN,WAAW,IAAIA,WAAW,CAACC,UAA/B,EAA2C;AACvC,kBAAMM,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMJ,MAAN,CAAaK,SAAb,CAAuB,kBAAgBR,WAAW,CAACC,UAAnD,EAA+DT,MAA/D,CAArB;;AACA,gBAAIe,MAAM,IAAI,IAAd,EAAoB;AAChBE,cAAAA,OAAO,CAACC,GAAR,CAAa,iBAAgBV,WAAW,CAACC,UAAW,sBAApD;AACH,aAFD,MAEO;AACHK,cAAAA,IAAI,GAAGf,WAAW,CAACgB,MAAD,CAAlB;AACH;AACJ;;AACD,iBAAOD,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACgB,cAANK,MAAM,CAACX,WAAD,EAAc;AACtB,iBAAO,KAAKK,OAAL,CAAaL,WAAb,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIY,QAAAA,UAAU,CAACZ,WAAD,EAAc;AACpB,iBAAO,KAAKK,OAAL,CAAaL,WAAb,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACyB,cAAfa,eAAe,CAACb,WAAD,EAAc;AAC/B,gBAAMM,IAAS,GAAG,MAAM,KAAKK,MAAL,CAAYX,WAAZ,CAAxB;AACA,iBAAOM,IAAI,CAACQ,YAAL,CAAkBd,WAAlB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqB,cAAXe,WAAW,CAACf,WAAD,EAAcgB,KAAd,EAAqBC,MAArB,EAA6BC,KAAK,GAAG,EAArC,EAAyC;AACtD,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAApB,EAA2BC,CAAC,EAA5B,EAAgC;AAC5B,kBAAMb,IAAI,GAAG,MAAM,KAAKD,OAAL,CAAaL,WAAb,CAAnB;;AACA,gBAAI,CAACM,IAAL,EAAW;AACP;AACH;;AACDW,YAAAA,MAAM,CAACG,IAAP,CAAYd,IAAI,CAACQ,YAAL,CAAkBd,WAAlB,CAAZ;;AACA,gBAAIiB,MAAM,CAACI,MAAP,IAAiBL,KAArB,EAA4B;AACxB;AACH;AACJ;;AACD,cAAIC,MAAM,CAACI,MAAP,GAAgBL,KAApB,EAA2B;AACvBM,YAAAA,UAAU,CAAC,MAAM;AACb,mBAAKP,WAAL,CAAiBf,WAAjB,EAA8BgB,KAA9B,EAAqCC,MAArC,EAA6CC,KAA7C;AACH,aAFS,EAEP,CAFO,CAAV;AAGH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC2B,cAAjBK,iBAAiB,CAACC,UAAD,EAAaR,KAAb,EAAoBC,MAApB,EAA4BC,KAAK,GAAG,EAApC,EAAwC;AAC3D,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAApB,EAA2BC,CAAC,EAA5B,EAAgC;AAC5B,kBAAMb,IAAI,GAAG,MAAM,KAAKK,MAAL,CAAY;AAAEV,cAAAA,UAAU,EAAEuB;AAAd,aAAZ,CAAnB;;AACA,gBAAI,CAAClB,IAAL,EAAW;AACP;AACH;;AACDW,YAAAA,MAAM,CAACG,IAAP,CAAYd,IAAZ;;AACA,gBAAIW,MAAM,CAACI,MAAP,IAAiBL,KAArB,EAA4B;AACxB;AACH;AACJ;;AACD,cAAIC,MAAM,CAACI,MAAP,GAAgBL,KAApB,EAA2B;AACvBM,YAAAA,UAAU,CAAC,MAAM;AACb,mBAAKC,iBAAL,CAAuBC,UAAvB,EAAmCR,KAAnC,EAA0CC,MAA1C,EAAkDC,KAAlD;AACH,aAFS,EAEP,CAFO,CAAV;AAGH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIO,QAAAA,GAAG,CAACzB,WAAD,EAAc;AACb,cAAI,CAACA,WAAW,CAACC,UAAjB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,gBAAMwB,IAAI,GAAG,KAAK7B,MAAL,CAAY4B,GAAZ,CAAgBzB,WAAW,CAACC,UAA5B,CAAb;AACA,gBAAMK,IAAI,GAAGoB,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAc,CAAtB,GAA0BD,IAAI,CAACD,GAAL,EAA1B,GAAuC,KAAKpB,OAAL,CAAaL,WAAb,CAApD;AACA,iBAAOM,IAAI,CAACQ,YAAL,CAAkBd,WAAlB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACoB,cAAV4B,UAAU,CAAC5B,WAAD,EAAc2B,IAAI,GAAG,CAArB,EAAwB;AACpC,gBAAM,KAAK5B,OAAL,CAAaC,WAAb,CAAN;;AACA,cAAI,CAACA,WAAW,CAACC,UAAjB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,gBAAMwB,IAAI,GAAG,IAAIpC,QAAJ,EAAb;AACA,eAAKO,MAAL,CAAYgC,GAAZ,CAAgB7B,WAAW,CAACC,UAA5B,EAAwCyB,IAAxC;;AACA,eAAK,IAAIP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,IAApB,EAA0BR,CAAC,EAA3B,EAA+B;AAC3B,kBAAMb,IAAI,GAAG,MAAM,KAAKD,OAAL,CAAaL,WAAb,CAAnB;AACA0B,YAAAA,IAAI,CAACI,GAAL,CAASxB,IAAT;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,GAAG,CAAC/B,WAAD,EAAc;AACb,cAAI,CAACA,WAAW,CAACC,UAAjB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,gBAAMwB,IAAI,GAAG,KAAK7B,MAAL,CAAY4B,GAAZ,CAAgBzB,WAAW,CAACC,UAA5B,CAAb;;AACA,cAAI,CAACyB,IAAL,EAAW;AACP,mBAAO,IAAP;AACH;;AACD,gBAAMpB,IAAI,GAAG,KAAKD,OAAL,CAAaL,WAAb,CAAb;;AACA0B,UAAAA,IAAI,CAACI,GAAL,CAASxB,IAAT;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIwB,QAAAA,GAAG,CAAC9B,WAAD,EAAcM,IAAd,EAAoB;AACnB,cAAI,CAACN,WAAW,CAACC,UAAjB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAU,kCAAV,CAAN;AACH;;AACD,gBAAMwB,IAAI,GAAG,KAAK7B,MAAL,CAAY4B,GAAZ,CAAgBzB,WAAW,CAACC,UAA5B,CAAb;;AACA,cAAI,CAACyB,IAAL,EAAW;AACP,mBAAO,IAAP;AACH;;AACDA,UAAAA,IAAI,CAACI,GAAL,CAASxB,IAAT;AACH;;AAlLmE,O", "sourcesContent": ["import { _decorator, NodePool, instantiate, Prefab, Node} from 'cc';\r\nimport { GameIns } from '../GameIns';\r\nimport { SingletonBase } from '../../core/base/SingletonBase';\r\nimport { MyApp } from '../../MyApp';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('PrefabManager')\r\nexport default class PrefabManager extends SingletonBase<PrefabManager> {\r\n\r\n    m_pool = new Map(); // 存储所有预制体对象池\r\n\r\n\r\n    /**\r\n     * 预加载指定的预制体\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Promise<void>}\r\n     */\r\n    async preload(prefabClass) {\r\n        if (!prefabClass.PrefabName) {\r\n            throw new Error('Please set static var PrefabName');\r\n        }\r\n        await MyApp.resMgr.preloadAsync(prefabClass.PrefabName);\r\n    }\r\n\r\n    /**\r\n     * 创建预制体实例\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Node} 预制体实例\r\n     */\r\n    async _create(prefabClass) {\r\n        var node:Node = null\r\n        if (prefabClass || prefabClass.PrefabName) {\r\n            const prefab = await MyApp.resMgr.loadAsync(\"Game/prefabs/\"+prefabClass.PrefabName, Prefab);\r\n            if (prefab == null) {\r\n                console.log(`create prefab[${prefabClass.PrefabName}] but prefab is null`);\r\n            } else {\r\n                node = instantiate(prefab);\r\n            }\r\n        }\r\n        return node;\r\n    }\r\n\r\n    /**\r\n     * 异步创建预制体实例\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Promise<Node>} 预制体实例\r\n     */\r\n    async create(prefabClass) {\r\n        return this._create(prefabClass);\r\n    }\r\n\r\n    /**\r\n     * 同步创建预制体实例\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Node} 预制体实例\r\n     */\r\n    createSync(prefabClass) {\r\n        return this._create(prefabClass);\r\n    }\r\n\r\n    /**\r\n     * 异步创建预制体组件\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Promise<Component>} 预制体组件\r\n     */\r\n    async createComponent(prefabClass) {\r\n        const node:Node = await this.create(prefabClass);\r\n        return node.getComponent(prefabClass);\r\n    }\r\n\r\n    /**\r\n     * 创建指定数量的预制体实例（逐帧创建）\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @param {number} count 需要创建的数量\r\n     * @param {Array} result 存储创建结果的数组\r\n     * @param {number} batch 每帧创建的数量\r\n     * @returns {Promise<void>}\r\n     */\r\n    async createFrame(prefabClass, count, result, batch = 10) {\r\n        for (let i = 0; i < batch; i++) {\r\n            const node = await this._create(prefabClass);\r\n            if (!node) {\r\n                return;\r\n            }\r\n            result.push(node.getComponent(prefabClass));\r\n            if (result.length >= count) {\r\n                return;\r\n            }\r\n        }\r\n        if (result.length < count) {\r\n            setTimeout(() => {\r\n                this.createFrame(prefabClass, count, result, batch);\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 创建指定数量的预制体实例（通过名称逐帧创建）\r\n     * @param {string} prefabName 预制体名称\r\n     * @param {number} count 需要创建的数量\r\n     * @param {Array} result 存储创建结果的数组\r\n     * @param {number} batch 每帧创建的数量\r\n     * @returns {Promise<void>}\r\n     */\r\n    async createFrameByName(prefabName, count, result, batch = 10) {\r\n        for (let i = 0; i < batch; i++) {\r\n            const node = await this.create({ PrefabName: prefabName });\r\n            if (!node) {\r\n                return;\r\n            }\r\n            result.push(node);\r\n            if (result.length >= count) {\r\n                return;\r\n            }\r\n        }\r\n        if (result.length < count) {\r\n            setTimeout(() => {\r\n                this.createFrameByName(prefabName, count, result, batch);\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取对象池中的预制体实例\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @returns {Component} 预制体组件\r\n     */\r\n    get(prefabClass) {\r\n        if (!prefabClass.PrefabName) {\r\n            throw new Error('Please set static var PrefabName');\r\n        }\r\n        const pool = this.m_pool.get(prefabClass.PrefabName);\r\n        const node = pool && pool.size() > 0 ? pool.get() : this._create(prefabClass);\r\n        return node.getComponent(prefabClass);\r\n    }\r\n\r\n    /**\r\n     * 创建对象池\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @param {number} size 对象池的初始大小\r\n     * @returns {Promise<void>}\r\n     */\r\n    async createPool(prefabClass, size = 1) {\r\n        await this.preload(prefabClass);\r\n        if (!prefabClass.PrefabName) {\r\n            throw new Error('Please set static var PrefabName');\r\n        }\r\n        const pool = new NodePool();\r\n        this.m_pool.set(prefabClass.PrefabName, pool);\r\n        for (let i = 0; i < size; i++) {\r\n            const node = await this._create(prefabClass);\r\n            pool.put(node);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加预制体实例到对象池\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     */\r\n    add(prefabClass) {\r\n        if (!prefabClass.PrefabName) {\r\n            throw new Error('Please set static var PrefabName');\r\n        }\r\n        const pool = this.m_pool.get(prefabClass.PrefabName);\r\n        if (!pool) {\r\n            return null;\r\n        }\r\n        const node = this._create(prefabClass);\r\n        pool.put(node);\r\n    }\r\n\r\n    /**\r\n     * 将预制体实例放回对象池\r\n     * @param {Object} prefabClass 包含 PrefabName 的类\r\n     * @param {Node} node 需要放回的节点\r\n     */\r\n    put(prefabClass, node) {\r\n        if (!prefabClass.PrefabName) {\r\n            throw new Error('Please set static var PrefabName');\r\n        }\r\n        const pool = this.m_pool.get(prefabClass.PrefabName);\r\n        if (!pool) {\r\n            return null;\r\n        }\r\n        pool.put(node);\r\n    }\r\n}"]}