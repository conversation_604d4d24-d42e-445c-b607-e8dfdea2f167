import { _decorator, Enum, CCFloat } from 'cc';
import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from '../../../leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionDelayTime } from '../../../leveldata/condition/LevelDataEventCondtionDelayTime';
import { newCondition } from '../../../leveldata/condition/newCondition';
import { LevelDataEventCondtionDelayDistance } from '../../../leveldata/condition/LevelDataEventCondtionDelayDistance';
import { LevelElemUI } from './LevelElemUI';
import { LevelDataEventCondtionWave } from '../../../leveldata/condition/LevelDataEventCondtionWave';
const { ccclass, property } = _decorator;

@ccclass('LevelCondition')
export class LevelCondition {
    public _index = 0;
    public data : LevelDataEventCondtion = new LevelDataEventCondtionDelayTime(LevelDataEventCondtionComb.And);

    @property({
        type:Enum(LevelDataEventCondtionComb),
        visible() {
            return this._index != 0;
        }
    })
    public get comb(): LevelDataEventCondtionComb {
        return this.data.comb;
    }
    public set comb(value: LevelDataEventCondtionComb) {
        this.data.comb = value;
    }

    @property({
        type:Enum(LevelDataEventCondtionType),
    })
    public get type(): LevelDataEventCondtionType {
        return this.data._type;
    }
    public set type(value: LevelDataEventCondtionType) {
        if (this.data._type != value) {
            this.data = newCondition({comb: this.data.comb, _type: value});
        }
    }

    @property({
        type :CCFloat,
        visible () {
            return this.type == LevelDataEventCondtionType.DelayTime ;
        }
    })
    public get delayTime(): number {
        return (this.data as LevelDataEventCondtionDelayTime).time;
    }
    public set delayTime(value: number) {
        (this.data as LevelDataEventCondtionDelayTime).time = value;
    }

    @property({
        type :CCFloat,
        visible () {
            return this.type == LevelDataEventCondtionType.DelayDistance;
        }
    })
    public get delayDistance(): number {
        return (this.data as LevelDataEventCondtionDelayDistance).distance;
    }
    public set delayDistance(value: number) {
        (this.data as LevelDataEventCondtionDelayDistance).distance = value;
    }

    public _targetElem: LevelElemUI | null = null;
    @property({
        type: LevelElemUI,
        visible () {
            return this.type == LevelDataEventCondtionType.Wave;
        }
    })
    public get targetElem(): LevelElemUI | null {
         return this._targetElem;
    }
    public set targetElem(value: LevelElemUI | null) { 
        this._targetElem = value;
        (this.data as LevelDataEventCondtionWave).targetElemID = value?.elemID ?? "";
    }
}