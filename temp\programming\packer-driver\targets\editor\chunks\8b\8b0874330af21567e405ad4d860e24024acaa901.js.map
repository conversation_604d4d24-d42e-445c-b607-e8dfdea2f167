{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts"], "names": ["_decorator", "Component", "Node", "Color", "tween", "UITransform", "Sprite", "UIOpacity", "GameConst", "ccclass", "property", "EffectLayer", "onLoad", "me", "whiteNode", "getComponent", "width", "ViewWidth", "height", "ViewHeight", "showWhiteScreen", "delay", "opacity", "active", "color", "WHITE", "ActionFrameTime", "to", "call", "start", "lightingShow", "BLACK", "showRedScreen", "redNode", "frameTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAChEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;yBAGTW,W,WADpBF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACR,IAAD,C,UAGRQ,QAAQ,CAACR,IAAD,C,sCALb,MACqBS,WADrB,SACyCV,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAS/CW,QAAAA,MAAM,GAAG;AACLD,UAAAA,WAAW,CAACE,EAAZ,GAAiB,IAAjB;AACA,eAAKC,SAAL,CAAeC,YAAf,CAA4BV,WAA5B,EAAyCW,KAAzC,GAAiD;AAAA;AAAA,sCAAUC,SAA3D;AACA,eAAKH,SAAL,CAAeC,YAAf,CAA4BV,WAA5B,EAAyCa,MAAzC,GAAkD;AAAA;AAAA,sCAAUC,UAA5D;AACH;;AAEDC,QAAAA,eAAe,CAACC,KAAD,EAAgBC,OAAe,GAAG,GAAlC,EAAuC;AAClD,eAAKR,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAuCe,OAAvC,GAAiDA,OAAjD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAoCkB,KAApC,GAA4CrB,KAAK,CAACsB,KAAlD;AAEArB,UAAAA,KAAK,CAAC,KAAKU,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,CAAD,CAAL,CACKc,KADL,CACW,IAAI;AAAA;AAAA,sCAAUK,eADzB,EAEKC,EAFL,CAEQ,IAFR,EAEc;AAAEL,YAAAA,OAAO,EAAE;AAAX,WAFd,EAGKM,IAHL,CAGU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WALL,EAMKM,KANL;AAOH;;AAEDC,QAAAA,YAAY,GAAG;AACX,eAAKhB,SAAL,CAAeS,MAAf,GAAwB,IAAxB;AACA,eAAKT,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAuCe,OAAvC,GAAiD,MAAjD;AACA,eAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAoCkB,KAApC,GAA4CrB,KAAK,CAACsB,KAAlD;AAEArB,UAAAA,KAAK,CAAC,KAAKU,SAAN,CAAL,CACKO,KADL,CACW,IAAI,EADf,EAEKO,IAFL,CAEU,MAAM;AACR,iBAAKd,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAuCe,OAAvC,GAAiD,KAAjD;AACA,iBAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAoCkB,KAApC,GAA4CrB,KAAK,CAAC4B,KAAlD;AACH,WALL,EAMKV,KANL,CAMW,IAAI,EANf,EAOKO,IAPL,CAOU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WATL,EAUKF,KAVL,CAUW,IAAI,EAVf,EAWKO,IAXL,CAWU,MAAM;AACR,iBAAKd,SAAL,CAAeC,YAAf,CAA4BR,SAA5B,EAAuCe,OAAvC,GAAiD,KAAjD;AACA,iBAAKR,SAAL,CAAeC,YAAf,CAA4BT,MAA5B,EAAoCkB,KAApC,GAA4CrB,KAAK,CAAC4B,KAAlD;AACH,WAdL,EAeKV,KAfL,CAeW,IAAI,EAff,EAgBKO,IAhBL,CAgBU,MAAM;AACR,iBAAKd,SAAL,CAAeS,MAAf,GAAwB,KAAxB;AACH,WAlBL,EAmBKM,KAnBL;AAoBH;;AAEDG,QAAAA,aAAa,GAAG;AACZ,eAAKC,OAAL,CAAalB,YAAb,CAA0BR,SAA1B,EAAqCe,OAArC,GAA+C,CAA/C;AACA,eAAKW,OAAL,CAAalB,YAAb,CAA0BV,WAA1B,EAAuCW,KAAvC,GAA+C;AAAA;AAAA,sCAAUC,SAAzD;AACA,eAAKgB,OAAL,CAAalB,YAAb,CAA0BV,WAA1B,EAAuCa,MAAvC,GAAgD;AAAA;AAAA,sCAAUC,UAA1D;AAEA,gBAAMe,SAAS,GAAG;AAAA;AAAA,sCAAUR,eAA5B;AAEAtB,UAAAA,KAAK,CAAC,KAAK6B,OAAL,CAAalB,YAAb,CAA0BR,SAA1B,CAAD,CAAL,CACKoB,EADL,CACQ,CADR,EACW;AAAEL,YAAAA,OAAO,EAAE;AAAX,WADX,EAEKK,EAFL,CAEQ,IAAIO,SAFZ,EAEuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAFvB,EAGKK,EAHL,CAGQ,IAAIO,SAHZ,EAGuB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAHvB,EAIKK,EAJL,CAIQ,KAAKO,SAJb,EAIwB;AAAEZ,YAAAA,OAAO,EAAE;AAAX,WAJxB,EAKKO,KALL;AAMH;;AArE8C,O,UAOxChB,E;;;;;iBALW,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Component, Node, Color, tween, UITransform, Sprite, UIOpacity } from 'cc';\r\nimport { GameConst } from '../../const/GameConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EffectLayer')\r\nexport default class EffectLayer extends Component {\r\n    @property(Node)\r\n    whiteNode: Node = null;\r\n\r\n    @property(Node)\r\n    redNode: Node = null;\r\n\r\n    static me: EffectLayer;\r\n\r\n    onLoad() {\r\n        EffectLayer.me = this;\r\n        this.whiteNode.getComponent(UITransform).width = GameConst.ViewWidth;\r\n        this.whiteNode.getComponent(UITransform).height = GameConst.ViewHeight;\r\n    }\r\n\r\n    showWhiteScreen(delay: number, opacity: number = 255) {\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity).opacity = opacity;\r\n        this.whiteNode.getComponent(Sprite).color = Color.WHITE;\r\n\r\n        tween(this.whiteNode.getComponent(UIOpacity))\r\n            .delay(4 * GameConst.ActionFrameTime)\r\n            .to(0.33, { opacity: 0 })\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    lightingShow() {\r\n        this.whiteNode.active = true;\r\n        this.whiteNode.getComponent(UIOpacity).opacity = 140.25;\r\n        this.whiteNode.getComponent(Sprite).color = Color.WHITE;\r\n\r\n        tween(this.whiteNode)\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode.getComponent(UIOpacity).opacity = 178.5;\r\n                this.whiteNode.getComponent(Sprite).color = Color.BLACK;\r\n            })\r\n            .delay(2 / 30)\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode.getComponent(UIOpacity).opacity = 127.5;\r\n                this.whiteNode.getComponent(Sprite).color = Color.BLACK;\r\n            })\r\n            .delay(1 / 30)\r\n            .call(() => {\r\n                this.whiteNode.active = false;\r\n            })\r\n            .start();\r\n    }\r\n\r\n    showRedScreen() {\r\n        this.redNode.getComponent(UIOpacity).opacity = 0;\r\n        this.redNode.getComponent(UITransform).width = GameConst.ViewWidth;\r\n        this.redNode.getComponent(UITransform).height = GameConst.ViewHeight;\r\n\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        tween(this.redNode.getComponent(UIOpacity))\r\n            .to(0, { opacity: 204 })\r\n            .to(4 * frameTime, { opacity: 255 })\r\n            .to(2 * frameTime, { opacity: 224 })\r\n            .to(15 * frameTime, { opacity: 0 })\r\n            .start();\r\n    }\r\n}"]}