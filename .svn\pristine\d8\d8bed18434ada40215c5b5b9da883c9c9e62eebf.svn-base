import { Component, NodePool, v2, Node, Vec2, misc, Size, size, Sprite, sp, resources, SpriteFrame } from 'cc';
import { GameConst } from '../const/GameConst';

class DYTools {
    /**
     * 打印日志
     */
    log(message: string, ...args: any[]) {
        console.log(message, ...args);
    }

    /**
     * 打印错误日志
     */
    error(message: string, ...args: any[]) {
        console.error(message, ...args);
    }

    /**
     * 打印警告日志
     */
    warn(message: string, ...args: any[]) {
        console.warn(message, ...args);
    }

    /**
     * 生成随机整数
     */
    random_int(min: number, max: number): number {
        const random = Math.floor(Math.random() * (max - min + 1)) + min;
        return Math.max(min, Math.min(max, random));
    }

    /**
     * 从数组中随机获取一个元素
     */
    getRandomInArray<T>(array: T[], remove: boolean = false): T | null {
        const length = array.length;
        if (length === 0) return null;

        const index = this.random_int(0, length - 1);
        const element = array[index];

        if (remove) {
            array.splice(index, 1);
        }

        return element;
    }

    /**
     * 将字符串转换为 Vec2
     * @param str 原字符串
     * @param delimiter 分隔符
     * @returns Vec2 对象
     */
    stringToPoint(str: string, delimiter: string): Vec2 {
        const parts = str.split(delimiter);
        if (parts.length > 1) {
            return v2(Number(parts[0]), Number(parts[1]));
        }
        return Vec2.ZERO;
    }


    /**
     * 将字符串转换为数字数组
     * @param str 原字符串
     * @param delimiter 分隔符
     * @returns 数字数组
     */
    stringToNumber(str: string, delimiter: string): number[] {
        const parts = str.split(delimiter);
        return parts.map((part) => Number(part)).filter((num) => !isNaN(num));
    }

    /**
     * 从数组中移除某个元素
     * @param array 数组
     * @param element 元素
     * @returns 是否成功移除
     */
    arrRemove<T>(array: T[], element: T): boolean {
        const index = array.indexOf(element);
        if (index >= 0) {
            array.splice(index, 1);
            return true;
        }
        return false;
    }

    /**
     * 检查数组是否包含某个元素（与 arrContains 类似）
     * @param array 数组
     * @param element 元素
     * @returns 是否包含
     */
    arrContain<T>(array: T[], element: T): boolean {
        return array.indexOf(element) != -1;;
    }

    /**
     * 添加脚本组件到节点
     * @param node 节点
     * @param script 脚本类
     * @returns 添加的脚本组件
     */
    addScript<T extends Component>(node: Node, script: { new(): T }): T | null {
        if (!node || !script) return null;
        return node.getComponent(script) || node.addComponent(script);
    }

    /**
     * 根据名称移除子节点
     * @param parent 父节点
     * @param name 子节点名称
     */
    removeChildByName(parent: Node, name: string) {
        if (!parent) return;
        const child = parent.getChildByName(name);
        if (child) {
            child.destroy();
        }
    }

    /**
     * 检查飞机是否超出屏幕范围
     * @param position 飞机位置
     * @returns 是否超出屏幕
     */
    isPlaneOutScreen(position: Vec2): boolean {
        const viewCenterX = GameConst.ViewCenter.x + 50;
        const viewHeight = GameConst.ViewHeight + 50;
        return position.x < -viewCenterX || position.x > viewCenterX || position.y < -viewHeight || position.y > 50;
    }

    /**
     * 获取贝塞尔曲线上的点
     * @param p0 起点
     * @param p1 控制点1
     * @param p2 控制点2
     * @param p3 终点
     * @param t 参数 t（0 到 1）
     * @returns 贝塞尔曲线上的点
     */
    getBezier(p0: number, p1: number, p2: number, p3: number, t: number): number {
        return (
            p0 * Math.pow(1 - t, 3) +
            3 * p1 * t * Math.pow(1 - t, 2) +
            3 * p2 * Math.pow(t, 2) * (1 - t) +
            p3 * Math.pow(t, 3)
        );
    }

    /**
     * 获取直线上的点
     * @param start 起点
     * @param direction 方向向量
     * @param distance 距离
     * @param t 参数 t（比例）
     * @returns 直线上的点
     */
    getStraight(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {
        const normalizedDir = direction.subtract(start).normalize();
        return start.add(normalizedDir.multiplyScalar(distance * t));
    }

    /**
     * 获取方向向量上的点
     * @param start 起点
     * @param direction 方向向量
     * @param distance 距离
     * @param t 参数 t（比例）
     * @returns 方向向量上的点
     */
    getStraightForDir(start: Vec2, direction: Vec2, distance: number, t: number): Vec2 {
        return start.add(direction.multiplyScalar(distance * t));
    }

    /**
     * 获取两点之间的角度
     * @param start 起点
     * @param end 终点
     * @returns 角度
     */
    getAngle(start: Vec2, end: Vec2): number {
        const dx = end.x - start.x;
        const dy = end.y - start.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.asin(dx / distance);
        return dy < 0 ? 180 - misc.radiansToDegrees(angle) : misc.radiansToDegrees(angle);
    }

    /**
     * 根据角度获取点的位置
     * @param point 点
     * @param angle 角度
     * @returns 新位置
     */
    getPositionByAngle(point: Vec2, angle: number): Vec2 {
        const radius = Math.sqrt(point.x * point.x + point.y * point.y);
        const radian = (Math.atan2(point.y, point.x) + misc.degreesToRadians(angle));
        return v2(Math.cos(radian) * radius, Math.sin(radian) * radius);
    }

    /**
     * 获取方向向量
     * @param x1 起点 x 坐标
     * @param y1 起点 y 坐标
     * @param x2 终点 x 坐标
     * @param y2 终点 y 坐标
     * @returns 方向向量
     */
    getDir(x1: number, y1: number, x2: number, y2: number): Vec2 {
        return v2(x2, y2).subtract(v2(x1, y1)).normalize();
    }

    /**
     * 获取方向向量的角度（以度为单位）
     * @param dir 方向向量
     * @returns 角度
     */
    getDegreeForDir(dir: Vec2): number {
        const reference = v2(0, -1); // 参考向量
        if (dir.equals(reference) || dir.equals(Vec2.ZERO)) {
            return 0;
        }
        const angle = dir.signAngle(reference);
        return misc.radiansToDegrees(angle);
    }

    /**
     * 清空 Map 中的组件数组
     * @param map 组件数组的 Map
     */
    clearMapForCompArr(map: Map<any, Component[]>) {
        if (!map) return;
        map.forEach((compArray) => {
            compArray.forEach((comp) => {
                if (comp && comp.node) {
                    comp.node.destroy();
                }
            });
        });
        map.clear();
    }
}

export const Tools = new DYTools();