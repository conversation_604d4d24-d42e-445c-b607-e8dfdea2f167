import { _decorator, ccenum, Component, director, loader, LabelComponent, ProgressBar } from 'cc';
import { WECHAT } from "cc/env";
import "../AAA/init_cs_proto.js";
import { DevLoginUI } from '../ui/DevLoginUI';
import { BattleUI } from '../ui/main/BattleUI';
import { BottomUI } from '../ui/main/BottomUI';
import { TopUI } from '../ui/main/TopUI';
import { UIMgr } from "../ui/UIMgr";
import { logDebug } from "../Utils/Logger";
const { ccclass, property } = _decorator;

class ResLoadingTips {
    public Progress: number;
    public Context: string;
}

if (WECHAT) {
    var warnCustom = console.warn;
    console.warn = function (res) {
        if (typeof res == "string" && res.indexOf("文件路径在真机上可能无法读取") > -1) {
            return;
        } else {
            warnCustom(res)
        }


    }
    var groupCustom = console.group;
    console.group = function (res) {
        if (typeof res == "string" && res.indexOf("读取文件/文件夹警告") > -1) {
            return;
        } else {
            groupCustom(res)
        }
    }
}

enum GameLogLevel {
    TRACE = 0,
    DEBUG = 1,
    LOG = 2,
    INFO = 3,
    WARN = 4,
    ERROR = 5,
}
ccenum(GameLogLevel)

@ccclass("ResUpdate")
export class ResUpdate extends Component {
    /* class member could be defined like this */
    // dummy = '';

    @property(LabelComponent)
    private countLabel: LabelComponent = null;
    @property(LabelComponent)
    private perLabel: LabelComponent = null;
    @property(LabelComponent)
    private versionLabel: LabelComponent = null;
    @property(ProgressBar)
    private loadingBar: ProgressBar = null;

    start() {
        UIMgr.initializeLayers();
        // Your initialization goes here.
        let THIS = this;
        director.preloadScene("Main", this.OnLoadProgress.bind(this), async () => {
            // dev 先load login UI
            await UIMgr.loadUI(DevLoginUI)
            await UIMgr.loadUI(BattleUI)
            await UIMgr.loadUI(BottomUI)
            await UIMgr.loadUI(TopUI)
            director.loadScene("Main")
        })
    }

    OnLoadProgress(completedCount: number, totalCount: number, item) {
        let progress = (completedCount / totalCount)
        logDebug("ResUpdate", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}, ${item.id}`)
        if (this.node == null) {
            return;
        }
        this.perLabel.string = (progress * 100).toFixed(2) + "%"
        this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')'
        this.loadingBar.progress = progress
    }

    onLoad() {
    }
    onDestroy() {
        loader.onProgress = null;
    }

    update(deltaTime: number) {
        //if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)
        //{
        //    director.loadScene("MainScene")
        //}
        // Your update function goes here.
    }
}
