System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, MainEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "db630a0KUxMc5cLT/YZAmJZ", "MainEvent", undefined);

      _export("MainEvent", MainEvent = {
        BattleItemClick: 'MainEvent_BattleItemClick',
        RogueSelectClick: 'MainEvent_RogueSelectClick'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2028c24350ddfe9c2c3743e570b5c51e3d1aaf4c.js.map