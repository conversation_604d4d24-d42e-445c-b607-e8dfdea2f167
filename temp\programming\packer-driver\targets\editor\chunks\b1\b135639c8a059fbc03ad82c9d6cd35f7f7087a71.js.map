{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts"], "names": ["_decorator", "Enum", "ccclass", "property", "eEmitterCondition", "eBulletCondition", "eEventConditionOp", "EmitterConditionData", "type", "displayName", "Level_Duration", "Equal", "BulletConditionData", "Bullet_ElapsedTime"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqCC,MAAAA,I,OAAAA,I;;;;;;;;;OACxC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;mCAElBI,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;kCAuDAC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;;mCAmBAC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;AASZ;AACA;AACA;AACA;;;sCAEaC,oB,WADZL,OAAO,CAAC,sBAAD,C,UAEHC,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEP,IAAI,CAACG,iBAAD,CAAZ;AAAiCK,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEP,IAAI,CAACK,iBAAD,CAAZ;AAAiCG,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,2BAXb,MACaF,oBADb,CACkC;AAAA;AAAA;;AAAA;;AAAA;;AAQJ;AARI;AAAA;;AAAA,O;;;;;iBAEEH,iBAAiB,CAACM,c;;;;;;;iBAGpBJ,iBAAiB,CAACK,K;;;;;;;iBAGzB,C;;;;;;;iBAGG,E;;;AAG9B;AACA;AACA;AACA;;;qCAEaC,mB,YADZV,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEP,IAAI,CAACI,gBAAD,CAAZ;AAAgCI,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEP,IAAI,CAACK,iBAAD,CAAZ;AAAiCG,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,6BAXb,MACaG,mBADb,CACiC;AAAA;AAAA;;AAAA;;AAAA;;AAQH;AARG;AAAA;;AAAA,O;;;;;iBAEEP,gBAAgB,CAACQ,kB;;;;;;;iBAGlBP,iBAAiB,CAACK,K;;;;;;;iBAGzB,C;;;;;;;iBAGG,E", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eEmitterCondition {\r\n    Level_Duration = 1,     // 已持续时间\r\n    Level_Distance,         // 已飞行距离\r\n    Level_InfLevel,         // 无尽模式当前关卡等级\r\n    Level_ChallengeLevel,   // 闯关模式当前等级\r\n\r\n    Player_ActLevel,        // 玩家账号等级\r\n    Player_PosX,            // 玩家当前坐标X\r\n    Player_PosY,            // 玩家当前坐标Y\r\n    Player_LifePercent,     // 玩家当前生命百分比\r\n    Player_GainBuff,        // 玩家获得buff\r\n\r\n    Unit_Life,              // 单位当前生命值\r\n    Unit_LifePercent,       // 单位当前生命百分比\r\n    Unit_Duration,          // 单位当前持续时间\r\n    Unit_PosX,              // 单位当前坐标X\r\n    Unit_PosY,              // 单位当前坐标Y\r\n    Unit_Speed,             // 单位当前速度\r\n    Unit_SpeedAngle,        // 单位当前速度角度\r\n    Unit_Acceleration,      // 单位当前加速度\r\n    Unit_AccelerationAngle, // 单位当前加速度角度\r\n    Unit_DistanceToPlayer,  // 单位与玩家的距离\r\n    Unit_AngleToPlayer,     // 单位与玩家的角度\r\n\r\n    Emitter_Active,         // 发射器是否启用\r\n    Emitter_InitialDelay,   // 发射器当前的初始延迟\r\n    Emitter_Prewarm,        // 发射器是否启用预热\r\n    Emitter_PrewarmDuration, // 发射器预热的持续时间\r\n    Emitter_Duration,       // 发射器配置的持续时间\r\n    Emitter_ElapsedTime,    // 发射器已运行的时间\r\n    Emitter_Loop,           // 发射器是否循环\r\n    Emitter_LoopInterval,   // 发射器循环的间隔时间\r\n\r\n    Emitter_EmitInterval,   // 发射器开火间隔\r\n    Emitter_EmitCount,      // 发射器开火次数\r\n    Emitter_EmitOffsetX,    // 发射器开火偏移\r\n\r\n    Emitter_Angle,          // 发射器弹道角度\r\n    Emitter_Count,          // 发射器弹道数量\r\n\r\n    Bullet_Sprite,          // 外观,这个需要id或者路径\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_Duration,\r\n    Bullet_ElapsedTime,\r\n    Bullet_Speed,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport enum eBulletCondition {\r\n    Bullet_Duration = 100,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_FacingMoveDir,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport enum eEventConditionOp {\r\n    Equal, // 等于\r\n    NotEqual, // 不等于\r\n    Greater, // 大于\r\n    Less, // 小于\r\n    GreaterEqual, // 大于等于\r\n    LessEqual, // 小\r\n}\r\n\r\n/**\r\n * 发射器事件数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"EmitterConditionData\")\r\nexport class EmitterConditionData {\r\n    @property({ type: Enum(eEmitterCondition), displayName: '条件类型' })\r\n    eventType : eEmitterCondition = eEmitterCondition.Level_Duration;\r\n\r\n    @property({ type: Enum(eEventConditionOp), displayName: '条件操作' })\r\n    eventOp : eEventConditionOp = eEventConditionOp.Equal;\r\n\r\n    @property({ displayName: '条件值' })\r\n    targetValue : number = 0; // 条件值: 例如持续时间、距离\r\n\r\n    @property({ displayName: '条件值(string)'})\r\n    targetValueStr : string = '';\r\n}\r\n\r\n/**\r\n * 子弹事件数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"BulletConditionData\")\r\nexport class BulletConditionData {\r\n    @property({ type: Enum(eBulletCondition), displayName: '条件类型' })\r\n    eventType : eBulletCondition = eBulletCondition.Bullet_ElapsedTime;\r\n\r\n    @property({ type: Enum(eEventConditionOp), displayName: '条件操作' })\r\n    eventOp : eEventConditionOp = eEventConditionOp.Equal;\r\n\r\n    @property({ displayName: '条件值' })\r\n    targetValue : number = 0; // 条件值: 例如持续时间、距离\r\n\r\n    @property({ displayName: '条件值(string)'})\r\n    targetValueStr : string = '';\r\n}\r\n"]}