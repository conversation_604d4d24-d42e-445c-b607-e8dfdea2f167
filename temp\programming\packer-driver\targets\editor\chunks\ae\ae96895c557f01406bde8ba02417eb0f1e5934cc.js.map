{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "TopBlockInputUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onLoad", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;iCAGjBK,e,WADZF,OAAO,CAAC,iBAAD,C,gBAAR,MACaE,eADb;AAAA;AAAA,4BAC4C;AACpB,eAANC,MAAM,GAAW;AAAE,iBAAO,2BAAP;AAAqC;;AAChD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAqB;;AAE/CC,QAAAA,MAAM,GAAS,CACxB;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAAG;;AAVM,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BaseUI, UILayer } from '../UIMgr';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('TopBlockInputUI')\nexport class TopBlockInputUI extends BaseUI {\n    public static getUrl(): string { return \"ui/common/TopBlockInputUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top; }\n\n    protected onLoad(): void {\n    }\n    async onShow(): Promise<void> {\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> { }\n}"]}