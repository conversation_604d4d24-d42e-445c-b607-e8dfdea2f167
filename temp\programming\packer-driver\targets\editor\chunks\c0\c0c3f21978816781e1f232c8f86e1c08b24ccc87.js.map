{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts"], "names": ["_decorator", "Vec2", "tween", "misc", "UIOpacity", "BossHurt", "GameConst", "ccclass", "property", "BossUnitBase", "owner", "_curHp", "_maxHp", "defence", "blastParam", "blastShake", "_whiteNode", "_winkCount", "_bW<PERSON><PERSON><PERSON>e", "_winkAct", "initWinkWhite", "whiteNode", "to", "opacity", "ActionFrameTime", "getComponent", "setPropertyRate", "rates", "length", "attack", "_collideAtk", "curHp", "value", "maxHp", "getAngleToOwner", "angle", "parent", "node", "getScenePos", "pos", "position", "x", "y", "rotate", "degreesToRadians", "scaleX", "scaleY", "name", "scale", "updateGameLogic", "deltaTime", "isDead", "m_comps", "for<PERSON>ach", "comp", "update", "hurt", "damage", "active", "changeHp", "onHurt", "<PERSON><PERSON><PERSON><PERSON>", "amount", "change", "newHp", "onHpChange", "checkHp", "hpChange", "die", "onDie", "playDieAnim", "clone", "start", "onDieAnimEnd", "getHpPercent"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAClDC,MAAAA,Q;;AAEEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAKH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAGTS,Y,WADpBF,OAAO,CAAC,cAAD,C,gBAAR,MACqBE,YADrB;AAAA;AAAA,gCACmD;AAAA;AAAA;AAAA,eAC/CC,KAD+C,GAClC,IADkC;AAAA,eAE/CC,MAF+C,GAE9B,CAF8B;AAAA,eAG/CC,MAH+C,GAG9B,CAH8B;AAAA,eAI/CC,OAJ+C,GAI7B,CAJ6B;AAAA,eAK/CC,UAL+C,GAK3B,EAL2B;AAAA,eAM/CC,UAN+C,GAM3B,EAN2B;AAAA,eAO/CC,UAP+C,GAO5B,IAP4B;AAAA,eAQ/CC,UAR+C,GAQ1B,CAR0B;AAAA,eAS/CC,WAT+C,GASxB,KATwB;AAAA,eAU/CC,QAV+C,GAU/B,IAV+B;AAAA;;AAY/CC,QAAAA,aAAa,CAACC,SAAD,EAAwB;AACjC,eAAKL,UAAL,GAAkBK,SAAlB;AACA,eAAKF,QAAL,GAAgBjB,KAAK,GAChBoB,EADW,CACR,CADQ,EACL;AAAEC,YAAAA,OAAO,EAAE;AAAX,WADK,EAEXD,EAFW,CAER,IAAI;AAAA;AAAA,sCAAUE,eAFN,EAEuB;AAAED,YAAAA,OAAO,EAAE;AAAX,WAFvB,CAAhB;;AAGA,cAAI,KAAKP,UAAT,EAAqB;AACjB,iBAAKA,UAAL,CAAgBS,YAAhB,CAA6BrB,SAA7B,EAAwCmB,OAAxC,GAAkD,CAAlD;AACH;AACJ;;AAEDG,QAAAA,eAAe,CAACC,KAAD,EAAwB;AACnC,cAAIA,KAAK,CAACC,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAKjB,MAAL,IAAegB,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKf,MAAL,GAAc,KAAKD,MAAnB;AACA,iBAAKkB,MAAL,IAAeF,KAAK,CAAC,CAAD,CAApB;AACA,iBAAKG,WAAL,IAAoBH,KAAK,CAAC,CAAD,CAAzB;AACH;AACJ;;AAEQ,YAALI,KAAK,GAAW;AAChB,iBAAO,KAAKpB,MAAZ;AACH;;AAEQ,YAALoB,KAAK,CAACC,KAAD,EAAgB;AACrB,eAAKrB,MAAL,GAAcqB,KAAd;AACH;;AAEQ,YAALC,KAAK,GAAW;AAChB,iBAAO,KAAKrB,MAAZ;AACH;;AAEQ,YAALqB,KAAK,CAACD,KAAD,EAAgB;AACrB,eAAKpB,MAAL,GAAcoB,KAAd;AACH;;AAGDE,QAAAA,eAAe,GAAW;AACtB,cAAIC,KAAK,GAAG,CAAZ;AACA,cAAIC,MAAM,GAAG,KAAKC,IAAL,CAAUD,MAAvB;;AACA,iBAAOA,MAAM,IAAIA,MAAM,KAAK,KAAK1B,KAAL,CAAW2B,IAAvC,EAA6C;AACzCF,YAAAA,KAAK,IAAIC,MAAM,CAACD,KAAhB;AACAC,YAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AACD,iBAAOD,KAAP;AACH;;AAEDG,QAAAA,WAAW,GAAS;AAChB,cAAIC,GAAG,GAAG,IAAItC,IAAJ,CAAS,KAAKoC,IAAL,CAAUG,QAAV,CAAmBC,CAA5B,EAA+B,KAAKJ,IAAL,CAAUG,QAAV,CAAmBE,CAAlD,EAAqDC,MAArD,CACNxC,IAAI,CAACyC,gBAAL,CAAsB,KAAKV,eAAL,EAAtB,CADM,CAAV;AAGA,cAAIE,MAAM,GAAG,KAAKC,IAAL,CAAUD,MAAvB;AACA,cAAIS,MAAM,GAAG,CAAb;AACA,cAAIC,MAAM,GAAG,CAAb;;AAEA,iBAAOV,MAAM,IAAIA,MAAM,CAACW,IAAP,KAAgB,YAAjC,EAA+C;AAC3CF,YAAAA,MAAM,IAAIT,MAAM,CAACY,KAAP,CAAaP,CAAvB;AACAK,YAAAA,MAAM,IAAIV,MAAM,CAACY,KAAP,CAAaN,CAAvB;AACAH,YAAAA,GAAG,CAACE,CAAJ,IAASL,MAAM,CAACI,QAAP,CAAgBC,CAAzB;AACAF,YAAAA,GAAG,CAACG,CAAJ,IAASN,MAAM,CAACI,QAAP,CAAgBE,CAAzB;AACAN,YAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AAEDG,UAAAA,GAAG,CAACE,CAAJ,IAASI,MAAT;AACAN,UAAAA,GAAG,CAACG,CAAJ,IAASI,MAAT;AACA,iBAAOP,GAAP;AACH;;AAEDU,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,iBAAKC,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACC,MAAL,CAAYL,SAAZ;AACH,aAFD;;AAIA,gBAAI,KAAKhC,WAAT,EAAsB;AAClB,mBAAKD,UAAL;;AACA,kBAAI,KAAKA,UAAL,GAAkB,EAAtB,EAA0B;AACtB,qBAAKA,UAAL,GAAkB,CAAlB;AACA,qBAAKC,WAAL,GAAmB,KAAnB;AACH;AACJ;AACJ;AACJ;;AAEDsC,QAAAA,IAAI,CAACC,MAAD,EAA0B;AAC1B,cAAI,KAAKN,MAAL,IAAe,CAAC,KAAKO,MAAzB,EAAiC;AAC7B,mBAAO,KAAP;AACH;;AACD,eAAKC,QAAL,CAAc,CAACF,MAAf;AACA,eAAKG,MAAL;AACA,iBAAO,IAAP;AACH;;AAEDA,QAAAA,MAAM,GAAS;AACX,eAAKC,SAAL;AACH;;AAEDF,QAAAA,QAAQ,CAACG,MAAD,EAAuB;AAC3B,cAAIC,MAAM,GAAGD,MAAb;AACA,cAAIE,KAAK,GAAG,KAAKrD,MAAL,GAAcmD,MAA1B;;AAEA,cAAIE,KAAK,GAAG,CAAZ,EAAe;AACXD,YAAAA,MAAM,GAAG,CAAC,KAAKpD,MAAf;AACH;;AAED,eAAKA,MAAL,GAAcqD,KAAd;;AACA,cAAI,KAAKrD,MAAL,GAAc,CAAlB,EAAqB;AACjB,iBAAKA,MAAL,GAAc,CAAd;AACH;;AAED,eAAKsD,UAAL,CAAgBF,MAAhB;AACA,eAAKG,OAAL;AACH;;AAEDD,QAAAA,UAAU,CAACF,MAAD,EAAuB;AAC7B,cAAI,KAAKrD,KAAL,IAAc,KAAKA,KAAL,CAAWyD,QAA7B,EAAuC;AACnC,iBAAKzD,KAAL,CAAWyD,QAAX,CAAoBJ,MAApB;AACH;AACJ;;AAEDG,QAAAA,OAAO,GAAY;AACf,cAAI,KAAKnC,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKqC,GAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEDA,QAAAA,GAAG,GAAS;AACR,cAAI,CAAC,KAAKjB,MAAV,EAAkB;AACd,iBAAKA,MAAL,GAAc,IAAd;AACA,iBAAKkB,KAAL;AACH;AACJ;;AAEDA,QAAAA,KAAK,GAAS;AACV,eAAKC,WAAL;AACH;;AAEDT,QAAAA,SAAS,GAAS;AACd,cAAI,CAAC,KAAK3C,WAAV,EAAuB;AACnB,iBAAKA,WAAL,GAAmB,IAAnB;;AACA,gBAAI,KAAKF,UAAL,IAAmB,KAAKG,QAA5B,EAAsC;AAClC,mBAAKA,QAAL,CAAcoD,KAAd,CAAoB,KAAKvD,UAAzB,EAAqCwD,KAArC;AACH;AACJ;AACJ;;AAEDF,QAAAA,WAAW,GAAS;AAChB,eAAKG,YAAL;AACH;;AAEDA,QAAAA,YAAY,GAAS,CAAE;;AAEvBC,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAK3C,KAAL,GAAa,KAAKE,KAAzB;AACH;;AAvK8C,O", "sourcesContent": ["import { _decorator, Component, Node, Vec2, tween, misc, UIOpacity, v2 } from 'cc';\r\nimport BossHurt from './BossHurt';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport EnemyEffectLayer from '../../layer/EnemyEffectLayer';\r\nimport FCollider from '../../../collider-system/FCollider';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('BossUnitBase')\r\nexport default class BossUnitBase extends BossHurt {\r\n    owner: any = null;\r\n    _curHp: number = 0;\r\n    _maxHp: number = 0;\r\n    defence: number = 0;\r\n    blastParam: any[] = [];\r\n    blastShake: any[] = [];\r\n    _whiteNode: Node = null;\r\n    _winkCount: number = 0;\r\n    _bWinkWhite: boolean = false;\r\n    _winkAct: any = null;\r\n\r\n    initWinkWhite(whiteNode: Node): void {\r\n        this._whiteNode = whiteNode;\r\n        this._winkAct = tween()\r\n            .to(0, { opacity: 255 })\r\n            .to(3 * GameConst.ActionFrameTime, { opacity: 0 });\r\n        if (this._whiteNode) {\r\n            this._whiteNode.getComponent(UIOpacity).opacity = 0;\r\n        }\r\n    }\r\n\r\n    setPropertyRate(rates: number[]): void {\r\n        if (rates.length > 2) {\r\n            this._curHp *= rates[0];\r\n            this._maxHp = this._curHp;\r\n            this.attack *= rates[1];\r\n            this._collideAtk *= rates[2];\r\n        }\r\n    }\r\n\r\n    get curHp(): number {\r\n        return this._curHp;\r\n    }\r\n\r\n    set curHp(value: number) {\r\n        this._curHp = value;\r\n    }\r\n\r\n    get maxHp(): number {\r\n        return this._maxHp;\r\n    }\r\n\r\n    set maxHp(value: number) {\r\n        this._maxHp = value;\r\n    }\r\n\r\n\r\n    getAngleToOwner(): number {\r\n        let angle = 0;\r\n        let parent = this.node.parent;\r\n        while (parent && parent !== this.owner.node) {\r\n            angle += parent.angle;\r\n            parent = parent.parent;\r\n        }\r\n        return angle;\r\n    }\r\n\r\n    getScenePos(): Vec2 {\r\n        let pos = new Vec2(this.node.position.x, this.node.position.y).rotate(\r\n            misc.degreesToRadians(this.getAngleToOwner())\r\n        );\r\n        let parent = this.node.parent;\r\n        let scaleX = 1;\r\n        let scaleY = 1;\r\n\r\n        while (parent && parent.name !== 'enemyPlane') {\r\n            scaleX *= parent.scale.x;\r\n            scaleY *= parent.scale.y;\r\n            pos.x += parent.position.x;\r\n            pos.y += parent.position.y;\r\n            parent = parent.parent;\r\n        }\r\n\r\n        pos.x *= scaleX;\r\n        pos.y *= scaleY;\r\n        return pos;\r\n    }\r\n\r\n    updateGameLogic(deltaTime: number): void {\r\n        if (!this.isDead) {\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n\r\n            if (this._bWinkWhite) {\r\n                this._winkCount++;\r\n                if (this._winkCount > 10) {\r\n                    this._winkCount = 0;\r\n                    this._bWinkWhite = false;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    hurt(damage: number): boolean {\r\n        if (this.isDead || !this.active) {\r\n            return false;\r\n        }\r\n        this.changeHp(-damage);\r\n        this.onHurt();\r\n        return true;\r\n    }\r\n\r\n    onHurt(): void {\r\n        this.winkWhite();\r\n    }\r\n\r\n    changeHp(amount: number): void {\r\n        let change = amount;\r\n        let newHp = this._curHp + amount;\r\n\r\n        if (newHp < 0) {\r\n            change = -this._curHp;\r\n        }\r\n\r\n        this._curHp = newHp;\r\n        if (this._curHp < 0) {\r\n            this._curHp = 0;\r\n        }\r\n\r\n        this.onHpChange(change);\r\n        this.checkHp();\r\n    }\r\n\r\n    onHpChange(change: number): void {\r\n        if (this.owner && this.owner.hpChange) {\r\n            this.owner.hpChange(change);\r\n        }\r\n    }\r\n\r\n    checkHp(): boolean {\r\n        if (this.curHp <= 0) {\r\n            this.die();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    die(): void {\r\n        if (!this.isDead) {\r\n            this.isDead = true;\r\n            this.onDie();\r\n        }\r\n    }\r\n\r\n    onDie(): void {\r\n        this.playDieAnim();\r\n    }\r\n\r\n    winkWhite(): void {\r\n        if (!this._bWinkWhite) {\r\n            this._bWinkWhite = true;\r\n            if (this._whiteNode && this._winkAct) {\r\n                this._winkAct.clone(this._whiteNode).start();\r\n            }\r\n        }\r\n    }\r\n\r\n    playDieAnim(): void {\r\n        this.onDieAnimEnd()\r\n    }\r\n\r\n    onDieAnimEnd(): void {}\r\n\r\n    getHpPercent(): number {\r\n        return this.curHp / this.maxHp;\r\n    }\r\n}"]}