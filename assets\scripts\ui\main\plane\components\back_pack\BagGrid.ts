import { _decorator, Component, Node, UITransform } from 'cc';
import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { DataMgr } from 'db://assets/scripts/Data/DataManager';
import { EventMgr } from 'db://assets/scripts/event/EventManager';
import { logDebug } from 'db://assets/scripts/Utils/Logger';
import List from '../../../../common/components/list/List';
import { UIMgr } from '../../../../UIMgr';
import { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';
import { PlaneUIEvent } from '../../PlaneEvent';
import { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';
import { BagItem } from './BagItem';

const { ccclass, property } = _decorator;

@ccclass('BagGrid')
export class BagGrid extends Component {
    @property(List)
    bagList: List = null;
    @property(Node)
    separator: Node = null;
    @property(Node)
    mergeSelectMaskBg: Node = null;

    private _sortedItems: csproto.cs.ICSItem[] = [];
    private _sortedPlaneParts: csproto.cs.ICSItem[] = [];
    private _lineGridNum: number = 5;
    private _separatorRow: number = 0;
    private _tabStatus: TabStatus = TabStatus.None;

    onLoad() {
        this.separator.removeFromParent();
        this.mergeSelectMaskBg.active = false;
        EventMgr.on(PlaneUIEvent.SortTypeChange, this.onSortTypeChange, this);
        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this)
        EventMgr.on(PlaneUIEvent.UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this)
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    private onUpdateMergeEquipStatus() {
        this.bagList.updateAll();
    }

    /*暂时只有装备点击*/
    private onBagItemClick(item: csproto.cs.ICSItem) {
        switch (this._tabStatus) {
            case TabStatus.Bag:
                this.mergeSelectMaskBg.active = false;
                UIMgr.openUI(PlaneEquipInfoUI, item, OpenEquipInfoUISource.BagGrid)
                break;
            case TabStatus.Merge:
                const isEmpty = DataMgr.equip.eqCombine.size() == 0
                if (DataMgr.equip.eqCombine.isFull() &&
                    !DataMgr.equip.eqCombine.getByGuid(item.guid) &&
                    (isEmpty && !DataMgr.equip.eqCombine.isMainMat(item.item_id)) &&
                    (!isEmpty && DataMgr.equip.eqCombine.isCanCombineWith(item))
                ) {
                    return
                }

                this.onUpdateMergeEquipStatus();
                break;
        }
    }

    private onSortTypeChange(tabStatus: TabStatus, equips: csproto.cs.ICSItem[], items: csproto.cs.ICSItem[]) {
        this._tabStatus = tabStatus;
        this.mergeSelectMaskBg.active = false;
        this.separator.active = false
        this.separator.removeFromParent();
        this.bagList._customSize = {}
        this.bagList._resizeContent();
        switch (tabStatus) {
            case TabStatus.Bag:
                this._sortedItems = items
                this._sortedPlaneParts = equips
                this._separatorRow = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum)
                const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum)
                this.bagList.numItems = this._separatorRow + itemRowNum + 1;
                break;
            case TabStatus.Merge:
                this._separatorRow = -1
                this._sortedPlaneParts = equips
                this.bagList.numItems = Math.ceil(this._sortedPlaneParts.length / this._lineGridNum)
                break;
        }
        logDebug("PlaneUI", `onSortTypeChange list num:${this.bagList.numItems} maxPlanePartRowNum:${this._separatorRow}`)
        this.bagList.scrollTo(0, 1)
    }

    onListRenderInBagStatus(listItem: Node, row: number) {
        listItem.name = `${row}`
        if (row == this._separatorRow) {
            const normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize
            const itemUITrans = listItem.getComponent(UITransform)
            listItem.children.forEach(v => v.active = false)
            this.separator.removeFromParent();
            this.separator.active = true;
            listItem.addChild(this.separator)
            itemUITrans.setContentSize(normalSize.width, normalSize.height / 2)
            return
        }

        if (listItem.children.length > 5) {
            this.separator.removeFromParent();
            this.separator.active = false;
        }

        const bagItems = listItem.getComponentsInChildren(BagItem)
        if (row < this._separatorRow) {
            for (let index = 0; index < bagItems.length; index++) {
                const item = bagItems[index];
                const dataIndex = row * this._lineGridNum + index;
                if (dataIndex >= this._sortedPlaneParts.length) {
                    item.node.active = false;
                    logDebug("PlaneUI", `onListRender bagItem index:${index} dataIndex:${dataIndex} row:${row} sortedLen:${this._sortedPlaneParts.length}`)
                    continue
                }
                item.node.active = true;
                item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);
            }
        } else {
            for (let index = 0; index < bagItems.length; index++) {
                const item = bagItems[index];
                const dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + index;
                if (dataIndex >= this._sortedItems.length) {
                    item.node.active = false;
                    continue
                }
                item.node.active = true;
                item.onRenderItem(this._sortedItems[dataIndex]);
            }
        }
    }

    private onListRenderInMergeStatus(listItem: Node, row: number) {
        const bagItems = listItem.getComponentsInChildren(BagItem)
        for (let index = 0; index < bagItems.length; index++) {
            const item = bagItems[index];
            const dataIndex = row * this._lineGridNum + index;
            if (dataIndex >= this._sortedPlaneParts.length) {
                item.node.active = false;
                continue
            }
            item.node.active = true;
            item.renderPlanePart(this._sortedPlaneParts[dataIndex], this._tabStatus);
        }
    }

    onListRender(listItem: Node, row: number) {
        listItem.name = `listItem${row}`
        if (this._tabStatus == TabStatus.Bag) {
            this.onListRenderInBagStatus(listItem, row)
        } else {
            this.onListRenderInMergeStatus(listItem, row)
        }
    }
}