System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, v2, Tools, EnemyAttackPointData, EnemyAttrData, EnemyCollider, EnemyPlaneData, TrackGroup, EnemyData, EnemyUIData, EnemyShootData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  _export({
    EnemyAttackPointData: void 0,
    EnemyAttrData: void 0,
    EnemyCollider: void 0,
    EnemyPlaneData: void 0,
    EnemyData: void 0,
    EnemyUIData: void 0,
    EnemyShootData: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "254ebYdpPJGcaAylvfAz4Zc", "EnemyData", undefined);

      __checkObsolete__(['error', 'v2', 'Vec2']);

      _export("EnemyAttackPointData", EnemyAttackPointData = class EnemyAttackPointData {
        constructor() {
          this.x = 0;
          this.y = 0;
          this.shootInterval = 0;
          this.bulletID = 0;
          this.bulletNum = 0;
          this.bulletInterval = 0;
          this.bulletAttackRate = 0;
          this.soundId = 0;
          this.soundDelay = 0;
        }

        /**
         * 从 JSON 数据加载攻击点数据
         * @param positionData 位置数据
         * @param attackData 攻击数据
         */
        loadJson(positionData, attackData) {
          try {
            const position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(positionData, ",");
            this.x = position[0];
            this.y = position[1];
            const attack = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(attackData, ",");
            this.shootInterval = attack[0];
            this.bulletID = attack[1];
            this.bulletNum = attack[2];
            this.bulletInterval = attack[3];
            this.bulletAttackRate = attack[4] / 100;
            if (attack.length > 5) this.soundId = attack[5];
            if (attack.length > 6) this.soundDelay = attack[6];
          } catch (error) {// Log.e("EnemyAttackPointData error:", positionData, attackData);
          }
        }

      });

      _export("EnemyAttrData", EnemyAttrData = class EnemyAttrData {
        constructor() {
          this.type = 0;
          // 属性类型
          this.param = "";
        }

        // 属性参数

        /**
         * 从 JSON 数据加载敌人属性数据
         * @param {Object} data 属性数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("type")) {
            this.type = parseInt(data.type);
          }

          if (data.hasOwnProperty("param")) {
            this.param = data.param;
          }
        }

      });

      _export("EnemyCollider", EnemyCollider = class EnemyCollider {
        constructor() {
          this.type = 0;
          this.x = 0;
          this.y = 0;
          this.width = 0;
          this.height = 0;
        }

        /**
         * 从 JSON 数据加载碰撞器数据
         * @param {string} data 碰撞器数据
         */
        loadJson(data) {
          try {
            const values = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data, ',');
            this.type = values[0];
            this.x = values[1];
            this.y = values[2];
            this.width = values[3];
            this.height = values[4];
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error('EnemyCollider error:', data);
          }
        }

      });
      /**
       * 敌机数据类
       */


      _export("EnemyPlaneData", EnemyPlaneData = class EnemyPlaneData {
        constructor() {
          this.id = 0;
          this.uiId = 0;
          this.hp = 0;
          this.dieBullet = false;
          this.attack = 0;
          this.defence = 0;
          this.collideLevel = 0;
          this.bTurnDir = false;
          this.collideAttack = 0;
          this.bCollideDead = false;
          this.bMoveAttack = false;
          this.bStayAttack = false;
          this.attackInterval = 0;
          this.attackNum = 0;
          this.dieShoot = [];
          this.attr = "";
          this.param = "";
          this.bAttackAbles = [];
          this.attackArrNums = [];
          this.attackPointArr = [];
        }

        /**
         * 从 JSON 数据加载敌机数据
         * @param data JSON 数据
         */
        loadJson(data) {
          this.id = data.id;
          this.uiId = data.uiId;
          this.attack = data.atk;
          this.hp = data.hp;
          this.collideLevel = data.collideLevel;
          this.bTurnDir = data.turn;
          this.collideAttack = data.collideAttack;
          this.bCollideDead = data.bCollideDead;
          this.bMoveAttack = data.bMoveAttack;
          this.bStayAttack = data.bStayAttack;
          this.attackInterval = data.attackInterval;
          this.attackNum = data.attackNum;
          this.param = data.param;
          this.dieShoot = data.dieShoot;
          this.dieBullet = data.dieBullet; // 0,-20;0,1002,1,0.3,100;0,-20;0,1003,1,1,100;0,-20;0,1004,1,1,100
          // 解析攻击点数据

          if (data.hasOwnProperty("attackData")) {
            const sections = data.attackData.split("#");

            for (let h = 0; h < sections.length; h++) {
              const attackGroups = []; // 二维数组，存储一个攻击点组的所有攻击点

              let isAttackAble = false;

              if (sections[h] !== "") {
                const points = sections[h].split(";");
                const attackPoints = []; // 一维数组，存储一个攻击点组中的单个攻击点

                for (let n = 0; n < points.length; n += 2) {
                  if (points[n] !== "" && points[n + 1] !== "") {
                    const attackPoint = new EnemyAttackPointData();
                    attackPoint.loadJson(points[n], points[n + 1]);
                    attackPoints.push(attackPoint);
                    isAttackAble = true;
                  }
                }

                attackGroups.push(attackPoints); // 将当前攻击点组添加到二维数组
              }

              this.attackPointArr.push(attackGroups); // 将二维数组作为一个整体添加到三维数组

              this.attackArrNums.push(attackGroups.length); // 存储当前攻击点组的数量

              this.bAttackAbles.push(isAttackAble); // 存储当前攻击点组是否可用
            }
          }
        }

      });

      TrackGroup = class TrackGroup {
        constructor() {
          this.points = [];
        }

        // 轨迹点数组

        /**
         * 从 JSON 数据加载轨迹组
         * @param {string} data 轨迹数据
         */
        loadJson(data) {
          const points = data.split(';');

          for (const point of points) {
            if (point !== '') {
              this.points.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToPoint(point, ','));
            }
          }
        }

      };
      /**
       * 敌人数据类
       */

      _export("EnemyData", EnemyData = class EnemyData {
        constructor() {
          this.id = 0;
          // 敌人 ID
          this.pos = v2(0, 0);
          // 敌人位置
          this.trackArr = [];
        }

        // 敌人轨迹数组

        /**
         * 从 JSON 数据加载敌人数据
         * @param {Object} json JSON 数据
         */
        loadJson(json) {
          const dataParts = json.split("#");

          if (dataParts.length > 0) {
            const positionData = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(dataParts[0], ",");
            this.id = positionData[0];
            this.pos = v2(positionData[1], positionData[2]);
          }

          if (dataParts.length > 1 && dataParts[1] !== "") {
            const trackData = dataParts[1].split("#");

            for (let i = 0; i < trackData.length; i++) {
              if (trackData[i] !== "" && trackData[i].split(";").length > 1) {
                const trackGroup = new TrackGroup();
                trackGroup.loadJson(trackData[i]);
                this.trackArr.push(trackGroup);
              }
            }
          }
        }

      });

      _export("EnemyUIData", EnemyUIData = class EnemyUIData {
        constructor() {
          this.id = 0;
          // 敌人 ID
          this.image = '';
          // 敌人图片路径
          this.isAm = false;
          // 是否为动画
          this.collider = [];
          // 碰撞器数据
          this.hpParam = [];
          // 血量参数
          this.damageParam = [];
          // 伤害参数
          this.clipArr = [];
          // 动画剪辑数组
          this.blastSound = 0;
          // 爆炸音效 ID
          this.blastCount = 0;
          // 爆炸次数
          this.blastParam = [];
          // 爆炸参数
          this.blastDurations = [];
          // 爆炸持续时间
          this.blastShake = [];
          // 爆炸震动参数
          this.extraParam = [];
          // 额外参数
          this.extraParam1 = '';
          // 额外参数 1
          this.skillResistUIDict = {};
          // 技能抗性字典
          this.lootParam0 = [];
          // 掉落参数 0
          this.lootParam1 = [];
          // 掉落参数 1
          this.sneakParam = [];
          // 潜行参数
          this.sneakAnim = '';
          // 潜行动画
          this.showParam = [];
        }

        // 显示参数

        /**
         * 从 JSON 数据加载敌人 UI 数据
         * @param {Object} data JSON 数据
         */
        loadJson(data) {
          this.id = data.id;
          this.image = data.image;
          this.isAm = data.isAm;
          this.collider = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.collider, ',');
          this.hpParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hpParam, ',');
          this.blastSound = data.blastSound;
          this.blastSound = data.blastSound;

          if (data.hasOwnProperty('blp')) {
            const params = data.blp.split(';');

            if (params.length > 0 && params[0] !== '') {
              this.blastCount = parseInt(params[0]);

              for (let i = 1; i < params.length; i++) {
                if (params[i] !== '') {
                  this.blastParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                    error: Error()
                  }), Tools) : Tools).stringToNumber(params[i], ','));
                }
              }
            }
          }

          this.blastDurations = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.blastDurations, ',');
          this.blastShake = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.blastShake, ',');

          if (data.hasOwnProperty('damageParam')) {
            const damageParams = data.damageParam.split(';');

            for (const param of damageParams) {
              if (param !== '') {
                this.damageParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(param, ','));
              }
            }
          }

          if (data.hasOwnProperty('extraParam0')) {
            const extraParams = data.extraParam0.split(';');

            for (const param of extraParams) {
              if (param !== '') {
                this.extraParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(param, ','));
              }
            }
          }

          if (data.hasOwnProperty('extraParam1')) this.extraParam1 = data.extraParam1;

          if (data.hasOwnProperty('skillResistUIDict')) {
            const skillResistData = data.skillResistUIDict.split('#');

            for (const entry of skillResistData) {
              if (entry !== '') {
                const parts = entry.split(';');

                if (parts.length > 1) {
                  const key = parseInt(parts[0]);
                  const values = [];

                  for (let i = 1; i < parts.length; i++) {
                    if (parts[i] !== '') {
                      values.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                        error: Error()
                      }), Tools) : Tools).stringToNumber(parts[i], ','));
                    }
                  }

                  this.skillResistUIDict[key] = values;
                }
              }
            }
          }

          this.lootParam0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lootParam0, ',');
          this.lootParam1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lootParam1, ',');
          this.sneakAnim = data.sneakAnim;
          this.showParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.showParam, ',');
        }

      });

      _export("EnemyShootData", EnemyShootData = class EnemyShootData {
        constructor() {
          this.attackInterval = 0;
          // 攻击间隔
          this.attackNum = 0;
          // 攻击次数
          this.attackArrNum = 0;
          // 攻击数组数量
          this.attackPointArr = [];
        }

        // 攻击点数组

        /**
         * 从 JSON 数据加载敌人射击数据
         * @param {Object} data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty('ain')) this.attackInterval = parseFloat(data.ain);
          if (data.hasOwnProperty('aa')) this.attackNum = parseInt(data.aa);
          if (data.hasOwnProperty('arrNum')) this.attackArrNum = parseInt(data.arrNum);

          if (data.hasOwnProperty('points')) {
            const pointsData = data.points.split(';');

            for (const point of pointsData) {
              if (point !== '') {
                this.attackPointArr.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(point, ','));
              }
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d1a5dea52aca73b8f13f5b6745163ac23320060f.js.map