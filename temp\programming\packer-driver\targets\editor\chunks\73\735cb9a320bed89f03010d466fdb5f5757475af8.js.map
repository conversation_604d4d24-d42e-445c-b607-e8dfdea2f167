{"version": 3, "sources": ["file:///E:/M2Game/Client/node_modules/long/umd/index.js"], "names": ["_cjs<PERSON><PERSON><PERSON>", "__cjsMetaURL", "url", "define", "exports", "require", "module", "__filename", "__dirname", "global", "factory", "preferDefault", "default", "amd", "<PERSON>", "globalThis", "self", "_exports", "Object", "defineProperty", "value", "wasm", "WebAssembly", "Instance", "<PERSON><PERSON><PERSON>", "Uint8Array", "low", "high", "unsigned", "prototype", "__isLong__", "isLong", "obj", "ctz32", "c", "Math", "clz32", "INT_CACHE", "UINT_CACHE", "fromInt", "cachedObj", "cache", "fromBits", "fromNumber", "isNaN", "UZERO", "ZERO", "TWO_PWR_64_DBL", "MAX_UNSIGNED_VALUE", "TWO_PWR_63_DBL", "MIN_VALUE", "MAX_VALUE", "neg", "TWO_PWR_32_DBL", "lowBits", "highBits", "pow_dbl", "pow", "fromString", "str", "radix", "length", "Error", "RangeError", "p", "indexOf", "substring", "radixToPower", "result", "i", "size", "min", "parseInt", "power", "mul", "add", "fromValue", "val", "TWO_PWR_16_DBL", "TWO_PWR_24_DBL", "TWO_PWR_24", "ONE", "UONE", "NEG_ONE", "LongPrototype", "toInt", "toNumber", "toString", "isZero", "isNegative", "eq", "radixLong", "div", "rem1", "sub", "rem", "remDiv", "intval", "digits", "getHighBits", "getHighBitsUnsigned", "getLowBits", "getLowBitsUnsigned", "getNumBitsAbs", "bit", "isSafeInteger", "top11Bits", "eqz", "isPositive", "isOdd", "isEven", "equals", "other", "notEquals", "neq", "ne", "lessThan", "comp", "lt", "lessThanOrEqual", "lte", "le", "greaterThan", "gt", "greaterThanOrEqual", "gte", "ge", "compare", "thisNeg", "otherNeg", "negate", "not", "addend", "a48", "a32", "a16", "a00", "b48", "b32", "b16", "b00", "c48", "c32", "c16", "c00", "subtract", "subtrahend", "multiply", "multiplier", "divide", "divisor", "approx", "res", "halfThis", "shr", "shl", "toUnsigned", "shru", "max", "floor", "log2", "ceil", "log", "LN2", "delta", "approxRes", "approxRem", "modulo", "mod", "countLeadingZeros", "clz", "countTrailingZeros", "ctz", "and", "or", "xor", "shiftLeft", "numBits", "shiftRight", "shiftRightUnsigned", "shr_u", "rotateLeft", "b", "rotl", "rotateRight", "rotr", "toSigned", "toBytes", "toBytesLE", "toBytesBE", "hi", "lo", "fromBytes", "bytes", "fromBytesLE", "fromBytesBE", "BigInt", "fromBigInt", "Number", "asIntN", "fromValueWithBigInt", "toBigInt", "lowBigInt", "highBigInt", "_default", "_cjsExports"], "mappings": ";;;;;;;;;AAAOA,MAAAA,U;;;8BAEDC,Y,GAAe,cAAYC,G;;AACjCF,MAAAA,UAAU,CAACG,MAAX,CAAkBF,YAAlB,EAAgC,UAAUG,OAAV,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoCC,UAApC,EAAgDC,SAAhD,EAA2D;AAC3F;AAGC;AACA,SAAC,UAAUC,MAAV,EAAkBC,OAAlB,EAA2B;AAC1B,mBAASC,aAAT,CAAuBP,OAAvB,EAAgC;AAC9B,mBAAOA,OAAO,CAACQ,OAAR,IAAmBR,OAA1B;AACD;;AACD,cAAI,OAAOD,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACU,GAA3C,EAAgD;AAC9CV,YAAAA,MAAM,CAAC,EAAD,EAAK,YAAY;AACrB,kBAAIC,OAAO,GAAG,EAAd;AACAM,cAAAA,OAAO,CAACN,OAAD,CAAP;AACA,qBAAOO,aAAa,CAACP,OAAD,CAApB;AACD,aAJK,CAAN;AAKD,WAND,MAMO,IAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;AACtCM,YAAAA,OAAO,CAACN,OAAD,CAAP;AACA,gBAAI,OAAOE,MAAP,KAAkB,QAAtB,EAAgCA,MAAM,CAACF,OAAP,GAAiBO,aAAa,CAACP,OAAD,CAA9B;AACjC,WAHM,MAGA;AACL,aAAC,YAAY;AACX,kBAAIA,OAAO,GAAG,EAAd;AACAM,cAAAA,OAAO,CAACN,OAAD,CAAP;AACAK,cAAAA,MAAM,CAACK,IAAP,GAAcH,aAAa,CAACP,OAAD,CAA3B;AACD,aAJD;AAKD;AACF,SApBD,EAqBE,OAAOW,UAAP,KAAsB,WAAtB,GACIA,UADJ,GAEI,OAAOC,IAAP,KAAgB,WAAhB,GACEA,IADF,GAEE,IAzBR,EA0BE,UAAUC,QAAV,EAAoB;AAClB;;AAEAC,UAAAA,MAAM,CAACC,cAAP,CAAsBF,QAAtB,EAAgC,YAAhC,EAA8C;AAC5CG,YAAAA,KAAK,EAAE;AADqC,WAA9C;AAGAH,UAAAA,QAAQ,CAACL,OAAT,GAAmB,KAAK,CAAxB;AACA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEK;;AACA,cAAIS,IAAI,GAAG,IAAX;;AACA,cAAI;AACFA,YAAAA,IAAI,GAAG,IAAIC,WAAW,CAACC,QAAhB,CACL,IAAID,WAAW,CAACE,MAAhB,CACE,IAAIC,UAAJ,CAAe,CACb;AACA,aAFa,EAEV,EAFU,EAEN,GAFM,EAED,GAFC,EAGb;AACA,aAJa,EAIV,CAJU,EAIP,CAJO,EAIJ,CAJI,EAKb;AACA,aANa,EAMV,EANU,EAMN,CANM,EAOb;AACA,cARa,EAQT,CARS,EAQN,CARM,EAQH,GARG,EASb;AACA,cAVa,EAUT,CAVS,EAUN,GAVM,EAUD,GAVC,EAUI,GAVJ,EAUS,GAVT,EAUc,CAVd,EAUiB,GAVjB,EAWb;AACA,aAZa,EAYV,CAZU,EAYP,CAZO,EAab;AACA,aAda,EAeb;AACA,aAhBa,EAiBb;AACA,aAlBa,EAmBb;AACA,aApBa,EAqBb;AACA,aAtBa,EAuBb;AACA,aAxBa,EAyBb;AACA,aA1Ba,EA0BV,CA1BU,EA0BP,CA1BO,EA2Bb;AACA,eA5Ba,EA4BR,CA5BQ,EA4BL,EA5BK,EA4BD,CA5BC,EA4BE,EA5BF,EA6Bb;AACA,aA9Ba,EA8BV,EA9BU,EA8BN,CA9BM,EA+Bb;AACA,aAhCa,EAgCV,GAhCU,EAgCL,GAhCK,EAgCA,GAhCA,EAgCK,CAhCL,EAgCQ,CAhCR,EAiCb;AACA,aAlCa,EAkCV,GAlCU,EAkCL,GAlCK,EAkCA,GAlCA,EAkCK,EAlCL,EAkCS,GAlCT,EAkCc,CAlCd,EAkCiB,CAlCjB,EAmCb;AACA,aApCa,EAoCV,GApCU,EAoCL,GApCK,EAoCA,GApCA,EAoCK,EApCL,EAoCS,GApCT,EAoCc,CApCd,EAoCiB,CApCjB,EAqCb;AACA,aAtCa,EAsCV,GAtCU,EAsCL,GAtCK,EAsCA,GAtCA,EAsCK,EAtCL,EAsCS,GAtCT,EAsCc,CAtCd,EAsCiB,CAtCjB,EAuCb;AACA,aAxCa,EAwCV,GAxCU,EAwCL,GAxCK,EAwCA,GAxCA,EAwCK,EAxCL,EAwCS,GAxCT,EAwCc,CAxCd,EAwCiB,CAxCjB,EAyCb;AACA,aA1Ca,EA0CV,GA1CU,EA0CL,GA1CK,EA0CA,GA1CA,EA0CK,EA1CL,EA0CS,GA1CT,EA0Cc,GA1Cd,EA0CmB,GA1CnB,EA0CwB,GA1CxB,EA0C6B,CA1C7B,EA0CgC,CA1ChC,EA2Cb;AACA,cA5Ca,EA4CT,GA5CS,EA4CJ,CA5CI,EA4CD,CA5CC,EA6Cb;AACA,aA9Ca,EA8CV,CA9CU,EA8CP,EA9CO,EA8CH,CA9CG,EA8CA,EA9CA,EA+Cb;AACA,cAhDa,EAgDT,CAhDS,EAgDN,CAhDM,EAgDH,GAhDG,EAgDE,EAhDF,EAgDM,CAhDN,EAgDS,GAhDT,EAgDc,EAhDd,EAgDkB,CAhDlB,EAgDqB,GAhDrB,EAgD0B,EAhD1B,EAgD8B,EAhD9B,EAgDkC,GAhDlC,EAgDuC,GAhDvC,EAgD4C,EAhD5C,EAgDgD,CAhDhD,EAgDmD,GAhDnD,EAiDb,EAjDa,EAiDT,CAjDS,EAiDN,GAjDM,EAiDD,EAjDC,EAiDG,EAjDH,EAiDO,GAjDP,EAiDY,GAjDZ,EAiDiB,GAjDjB,EAiDsB,EAjDtB,EAiD0B,CAjD1B,EAiD6B,EAjD7B,EAiDiC,EAjDjC,EAiDqC,GAjDrC,EAiD0C,GAjD1C,EAiD+C,EAjD/C,EAiDmD,CAjDnD,EAkDb,EAlDa,EAkDT,CAlDS,EAkDN,GAlDM,EAkDD,EAlDC,EAmDb;AACA,cApDa,EAoDT,CApDS,EAoDN,CApDM,EAoDH,GApDG,EAoDE,EApDF,EAoDM,CApDN,EAoDS,GApDT,EAoDc,EApDd,EAoDkB,CApDlB,EAoDqB,GApDrB,EAoD0B,EApD1B,EAoD8B,EApD9B,EAoDkC,GApDlC,EAoDuC,GApDvC,EAoD4C,EApD5C,EAoDgD,CApDhD,EAoDmD,GApDnD,EAqDb,EArDa,EAqDT,CArDS,EAqDN,GArDM,EAqDD,EArDC,EAqDG,EArDH,EAqDO,GArDP,EAqDY,GArDZ,EAqDiB,GArDjB,EAqDsB,EArDtB,EAqD0B,CArD1B,EAqD6B,EArD7B,EAqDiC,EArDjC,EAqDqC,GArDrC,EAqD0C,GArD1C,EAqD+C,EArD/C,EAqDmD,CArDnD,EAsDb,EAtDa,EAsDT,CAtDS,EAsDN,GAtDM,EAsDD,EAtDC,EAuDb;AACA,cAxDa,EAwDT,CAxDS,EAwDN,CAxDM,EAwDH,GAxDG,EAwDE,EAxDF,EAwDM,CAxDN,EAwDS,GAxDT,EAwDc,EAxDd,EAwDkB,CAxDlB,EAwDqB,GAxDrB,EAwD0B,EAxD1B,EAwD8B,EAxD9B,EAwDkC,GAxDlC,EAwDuC,GAxDvC,EAwD4C,EAxD5C,EAwDgD,CAxDhD,EAwDmD,GAxDnD,EAyDb,EAzDa,EAyDT,CAzDS,EAyDN,GAzDM,EAyDD,EAzDC,EAyDG,EAzDH,EAyDO,GAzDP,EAyDY,GAzDZ,EAyDiB,GAzDjB,EAyDsB,EAzDtB,EAyD0B,CAzD1B,EAyD6B,EAzD7B,EAyDiC,EAzDjC,EAyDqC,GAzDrC,EAyD0C,GAzD1C,EAyD+C,EAzD/C,EAyDmD,CAzDnD,EA0Db,EA1Da,EA0DT,CA1DS,EA0DN,GA1DM,EA0DD,EA1DC,EA2Db;AACA,cA5Da,EA4DT,CA5DS,EA4DN,CA5DM,EA4DH,GA5DG,EA4DE,EA5DF,EA4DM,CA5DN,EA4DS,GA5DT,EA4Dc,EA5Dd,EA4DkB,CA5DlB,EA4DqB,GA5DrB,EA4D0B,EA5D1B,EA4D8B,EA5D9B,EA4DkC,GA5DlC,EA4DuC,GA5DvC,EA4D4C,EA5D5C,EA4DgD,CA5DhD,EA4DmD,GA5DnD,EA6Db,EA7Da,EA6DT,CA7DS,EA6DN,GA7DM,EA6DD,EA7DC,EA6DG,EA7DH,EA6DO,GA7DP,EA6DY,GA7DZ,EA6DiB,GA7DjB,EA6DsB,EA7DtB,EA6D0B,CA7D1B,EA6D6B,EA7D7B,EA6DiC,EA7DjC,EA6DqC,GA7DrC,EA6D0C,GA7D1C,EA6D+C,EA7D/C,EA6DmD,CA7DnD,EA8Db,EA9Da,EA8DT,CA9DS,EA8DN,GA9DM,EA8DD,EA9DC,EA+Db;AACA,cAhEa,EAgET,CAhES,EAgEN,CAhEM,EAgEH,GAhEG,EAgEE,EAhEF,EAgEM,CAhEN,EAgES,GAhET,EAgEc,EAhEd,EAgEkB,CAhElB,EAgEqB,GAhErB,EAgE0B,EAhE1B,EAgE8B,EAhE9B,EAgEkC,GAhElC,EAgEuC,GAhEvC,EAgE4C,EAhE5C,EAgEgD,CAhEhD,EAgEmD,GAhEnD,EAiEb,EAjEa,EAiET,CAjES,EAiEN,GAjEM,EAiED,EAjEC,EAiEG,EAjEH,EAiEO,GAjEP,EAiEY,GAjEZ,EAiEiB,GAjEjB,EAiEsB,EAjEtB,EAiE0B,CAjE1B,EAiE6B,EAjE7B,EAiEiC,EAjEjC,EAiEqC,GAjErC,EAiE0C,GAjE1C,EAiE+C,EAjE/C,EAiEmD,CAjEnD,EAkEb,EAlEa,EAkET,CAlES,EAkEN,GAlEM,EAkED,EAlEC,CAAf,CADF,CADK,EAuEL,EAvEK,EAwELrB,OAxEF;AAyED,WA1ED,CA0EE,MAAM,CACN;AACD;AAED;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACK,mBAASU,IAAT,CAAcY,GAAd,EAAmBC,IAAnB,EAAyBC,QAAzB,EAAmC;AACjC;AACP;AACA;AACA;AACO,iBAAKF,GAAL,GAAWA,GAAG,GAAG,CAAjB;AAEA;AACP;AACA;AACA;;AACO,iBAAKC,IAAL,GAAYA,IAAI,GAAG,CAAnB;AAEA;AACP;AACA;AACA;;AACO,iBAAKC,QAAL,GAAgB,CAAC,CAACA,QAAlB;AACD,WAvIiB,CAyIlB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACL;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAACe,SAAL,CAAeC,UAAf;AACAZ,UAAAA,MAAM,CAACC,cAAP,CAAsBL,IAAI,CAACe,SAA3B,EAAsC,YAAtC,EAAoD;AAClDT,YAAAA,KAAK,EAAE;AAD2C,WAApD;AAIA;AACL;AACA;AACA;AACA;AACA;;AACK,mBAASW,MAAT,CAAgBC,GAAhB,EAAqB;AACnB,mBAAO,CAACA,GAAG,IAAIA,GAAG,CAAC,YAAD,CAAX,MAA+B,IAAtC;AACD;AAED;AACL;AACA;AACA;AACA;AACA;;;AACK,mBAASC,KAAT,CAAeb,KAAf,EAAsB;AACpB,gBAAIc,CAAC,GAAGC,IAAI,CAACC,KAAL,CAAWhB,KAAK,GAAG,CAACA,KAApB,CAAR;AACA,mBAAOA,KAAK,GAAG,KAAKc,CAAR,GAAYA,CAAxB;AACD;AAED;AACL;AACA;AACA;AACA;AACA;;;AACKpB,UAAAA,IAAI,CAACiB,MAAL,GAAcA,MAAd;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIM,SAAS,GAAG,EAAhB;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIC,UAAU,GAAG,EAAjB;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK,mBAASC,OAAT,CAAiBnB,KAAjB,EAAwBQ,QAAxB,EAAkC;AAChC,gBAAII,GAAJ,EAASQ,SAAT,EAAoBC,KAApB;;AACA,gBAAIb,QAAJ,EAAc;AACZR,cAAAA,KAAK,MAAM,CAAX;;AACA,kBAAKqB,KAAK,GAAG,KAAKrB,KAAL,IAAcA,KAAK,GAAG,GAAnC,EAAyC;AACvCoB,gBAAAA,SAAS,GAAGF,UAAU,CAAClB,KAAD,CAAtB;AACA,oBAAIoB,SAAJ,EAAe,OAAOA,SAAP;AAChB;;AACDR,cAAAA,GAAG,GAAGU,QAAQ,CAACtB,KAAD,EAAQ,CAAR,EAAW,IAAX,CAAd;AACA,kBAAIqB,KAAJ,EAAWH,UAAU,CAAClB,KAAD,CAAV,GAAoBY,GAApB;AACX,qBAAOA,GAAP;AACD,aATD,MASO;AACLZ,cAAAA,KAAK,IAAI,CAAT;;AACA,kBAAKqB,KAAK,GAAG,CAAC,GAAD,IAAQrB,KAAR,IAAiBA,KAAK,GAAG,GAAtC,EAA4C;AAC1CoB,gBAAAA,SAAS,GAAGH,SAAS,CAACjB,KAAD,CAArB;AACA,oBAAIoB,SAAJ,EAAe,OAAOA,SAAP;AAChB;;AACDR,cAAAA,GAAG,GAAGU,QAAQ,CAACtB,KAAD,EAAQA,KAAK,GAAG,CAAR,GAAY,CAAC,CAAb,GAAiB,CAAzB,EAA4B,KAA5B,CAAd;AACA,kBAAIqB,KAAJ,EAAWJ,SAAS,CAACjB,KAAD,CAAT,GAAmBY,GAAnB;AACX,qBAAOA,GAAP;AACD;AACF;AAED;AACL;AACA;AACA;AACA;AACA;AACA;;;AACKlB,UAAAA,IAAI,CAACyB,OAAL,GAAeA,OAAf;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK,mBAASI,UAAT,CAAoBvB,KAApB,EAA2BQ,QAA3B,EAAqC;AACnC,gBAAIgB,KAAK,CAACxB,KAAD,CAAT,EAAkB,OAAOQ,QAAQ,GAAGiB,KAAH,GAAWC,IAA1B;;AAClB,gBAAIlB,QAAJ,EAAc;AACZ,kBAAIR,KAAK,GAAG,CAAZ,EAAe,OAAOyB,KAAP;AACf,kBAAIzB,KAAK,IAAI2B,cAAb,EAA6B,OAAOC,kBAAP;AAC9B,aAHD,MAGO;AACL,kBAAI5B,KAAK,IAAI,CAAC6B,cAAd,EAA8B,OAAOC,SAAP;AAC9B,kBAAI9B,KAAK,GAAG,CAAR,IAAa6B,cAAjB,EAAiC,OAAOE,SAAP;AAClC;;AACD,gBAAI/B,KAAK,GAAG,CAAZ,EAAe,OAAOuB,UAAU,CAAC,CAACvB,KAAF,EAASQ,QAAT,CAAV,CAA6BwB,GAA7B,EAAP;AACf,mBAAOV,QAAQ,CACbtB,KAAK,GAAGiC,cAAR,GAAyB,CADZ,EAEZjC,KAAK,GAAGiC,cAAT,GAA2B,CAFd,EAGbzB,QAHa,CAAf;AAKD;AAED;AACL;AACA;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAAC6B,UAAL,GAAkBA,UAAlB;AAEA;AACL;AACA;AACA;AACA;AACA;AACA;;AACK,mBAASD,QAAT,CAAkBY,OAAlB,EAA2BC,QAA3B,EAAqC3B,QAArC,EAA+C;AAC7C,mBAAO,IAAId,IAAJ,CAASwC,OAAT,EAAkBC,QAAlB,EAA4B3B,QAA5B,CAAP;AACD;AAED;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAAC4B,QAAL,GAAgBA,QAAhB;AAEA;AACL;AACA;AACA;AACA;AACA;AACA;;AACK,cAAIc,OAAO,GAAGrB,IAAI,CAACsB,GAAnB,CAnTkB,CAmTM;;AAExB;AACL;AACA;AACA;AACA;AACA;AACA;;AACK,mBAASC,UAAT,CAAoBC,GAApB,EAAyB/B,QAAzB,EAAmCgC,KAAnC,EAA0C;AACxC,gBAAID,GAAG,CAACE,MAAJ,KAAe,CAAnB,EAAsB,MAAMC,KAAK,CAAC,cAAD,CAAX;;AACtB,gBAAI,OAAOlC,QAAP,KAAoB,QAAxB,EAAkC;AAChC;AACAgC,cAAAA,KAAK,GAAGhC,QAAR;AACAA,cAAAA,QAAQ,GAAG,KAAX;AACD,aAJD,MAIO;AACLA,cAAAA,QAAQ,GAAG,CAAC,CAACA,QAAb;AACD;;AACD,gBACE+B,GAAG,KAAK,KAAR,IACAA,GAAG,KAAK,UADR,IAEAA,GAAG,KAAK,WAFR,IAGAA,GAAG,KAAK,WAJV,EAME,OAAO/B,QAAQ,GAAGiB,KAAH,GAAWC,IAA1B;AACFc,YAAAA,KAAK,GAAGA,KAAK,IAAI,EAAjB;AACA,gBAAIA,KAAK,GAAG,CAAR,IAAa,KAAKA,KAAtB,EAA6B,MAAMG,UAAU,CAAC,OAAD,CAAhB;AAC7B,gBAAIC,CAAJ;AACA,gBAAI,CAACA,CAAC,GAAGL,GAAG,CAACM,OAAJ,CAAY,GAAZ,CAAL,IAAyB,CAA7B,EAAgC,MAAMH,KAAK,CAAC,iBAAD,CAAX,CAAhC,KACK,IAAIE,CAAC,KAAK,CAAV,EAAa;AAChB,qBAAON,UAAU,CAACC,GAAG,CAACO,SAAJ,CAAc,CAAd,CAAD,EAAmBtC,QAAnB,EAA6BgC,KAA7B,CAAV,CAA8CR,GAA9C,EAAP;AACD,aAtBuC,CAwBxC;AACA;;AACA,gBAAIe,YAAY,GAAGxB,UAAU,CAACa,OAAO,CAACI,KAAD,EAAQ,CAAR,CAAR,CAA7B;AACA,gBAAIQ,MAAM,GAAGtB,IAAb;;AACA,iBAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,GAAG,CAACE,MAAxB,EAAgCQ,CAAC,IAAI,CAArC,EAAwC;AACtC,kBAAIC,IAAI,GAAGnC,IAAI,CAACoC,GAAL,CAAS,CAAT,EAAYZ,GAAG,CAACE,MAAJ,GAAaQ,CAAzB,CAAX;AAAA,kBACEjD,KAAK,GAAGoD,QAAQ,CAACb,GAAG,CAACO,SAAJ,CAAcG,CAAd,EAAiBA,CAAC,GAAGC,IAArB,CAAD,EAA6BV,KAA7B,CADlB;;AAEA,kBAAIU,IAAI,GAAG,CAAX,EAAc;AACZ,oBAAIG,KAAK,GAAG9B,UAAU,CAACa,OAAO,CAACI,KAAD,EAAQU,IAAR,CAAR,CAAtB;AACAF,gBAAAA,MAAM,GAAGA,MAAM,CAACM,GAAP,CAAWD,KAAX,EAAkBE,GAAlB,CAAsBhC,UAAU,CAACvB,KAAD,CAAhC,CAAT;AACD,eAHD,MAGO;AACLgD,gBAAAA,MAAM,GAAGA,MAAM,CAACM,GAAP,CAAWP,YAAX,CAAT;AACAC,gBAAAA,MAAM,GAAGA,MAAM,CAACO,GAAP,CAAWhC,UAAU,CAACvB,KAAD,CAArB,CAAT;AACD;AACF;;AACDgD,YAAAA,MAAM,CAACxC,QAAP,GAAkBA,QAAlB;AACA,mBAAOwC,MAAP;AACD;AAED;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;;AACKtD,UAAAA,IAAI,CAAC4C,UAAL,GAAkBA,UAAlB;AAEA;AACL;AACA;AACA;AACA;AACA;AACA;;AACK,mBAASkB,SAAT,CAAmBC,GAAnB,EAAwBjD,QAAxB,EAAkC;AAChC,gBAAI,OAAOiD,GAAP,KAAe,QAAnB,EAA6B,OAAOlC,UAAU,CAACkC,GAAD,EAAMjD,QAAN,CAAjB;AAC7B,gBAAI,OAAOiD,GAAP,KAAe,QAAnB,EAA6B,OAAOnB,UAAU,CAACmB,GAAD,EAAMjD,QAAN,CAAjB,CAFG,CAGhC;;AACA,mBAAOc,QAAQ,CACbmC,GAAG,CAACnD,GADS,EAEbmD,GAAG,CAAClD,IAFS,EAGb,OAAOC,QAAP,KAAoB,SAApB,GAAgCA,QAAhC,GAA2CiD,GAAG,CAACjD,QAHlC,CAAf;AAKD;AAED;AACL;AACA;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAAC8D,SAAL,GAAiBA,SAAjB,CA1YkB,CA4YlB;AACA;;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIE,cAAc,GAAG,KAAK,EAA1B;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIC,cAAc,GAAG,KAAK,EAA1B;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAI1B,cAAc,GAAGyB,cAAc,GAAGA,cAAtC;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAI/B,cAAc,GAAGM,cAAc,GAAGA,cAAtC;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIJ,cAAc,GAAGF,cAAc,GAAG,CAAtC;AAEA;AACL;AACA;AACA;AACA;;AACK,cAAIiC,UAAU,GAAGzC,OAAO,CAACwC,cAAD,CAAxB;AAEA;AACL;AACA;AACA;;AACK,cAAIjC,IAAI,GAAGP,OAAO,CAAC,CAAD,CAAlB;AAEA;AACL;AACA;AACA;;AACKzB,UAAAA,IAAI,CAACgC,IAAL,GAAYA,IAAZ;AAEA;AACL;AACA;AACA;;AACK,cAAID,KAAK,GAAGN,OAAO,CAAC,CAAD,EAAI,IAAJ,CAAnB;AAEA;AACL;AACA;AACA;;AACKzB,UAAAA,IAAI,CAAC+B,KAAL,GAAaA,KAAb;AAEA;AACL;AACA;AACA;;AACK,cAAIoC,GAAG,GAAG1C,OAAO,CAAC,CAAD,CAAjB;AAEA;AACL;AACA;AACA;;AACKzB,UAAAA,IAAI,CAACmE,GAAL,GAAWA,GAAX;AAEA;AACL;AACA;AACA;;AACK,cAAIC,IAAI,GAAG3C,OAAO,CAAC,CAAD,EAAI,IAAJ,CAAlB;AAEA;AACL;AACA;AACA;;AACKzB,UAAAA,IAAI,CAACoE,IAAL,GAAYA,IAAZ;AAEA;AACL;AACA;AACA;;AACK,cAAIC,OAAO,GAAG5C,OAAO,CAAC,CAAC,CAAF,CAArB;AAEA;AACL;AACA;AACA;;AACKzB,UAAAA,IAAI,CAACqE,OAAL,GAAeA,OAAf;AAEA;AACL;AACA;AACA;;AACK,cAAIhC,SAAS,GAAGT,QAAQ,CAAC,aAAa,CAAd,EAAiB,aAAa,CAA9B,EAAiC,KAAjC,CAAxB;AAEA;AACL;AACA;AACA;;AACK5B,UAAAA,IAAI,CAACqC,SAAL,GAAiBA,SAAjB;AAEA;AACL;AACA;AACA;;AACK,cAAIH,kBAAkB,GAAGN,QAAQ,CAAC,aAAa,CAAd,EAAiB,aAAa,CAA9B,EAAiC,IAAjC,CAAjC;AAEA;AACL;AACA;AACA;;AACK5B,UAAAA,IAAI,CAACkC,kBAAL,GAA0BA,kBAA1B;AAEA;AACL;AACA;AACA;;AACK,cAAIE,SAAS,GAAGR,QAAQ,CAAC,CAAD,EAAI,aAAa,CAAjB,EAAoB,KAApB,CAAxB;AAEA;AACL;AACA;AACA;;AACK5B,UAAAA,IAAI,CAACoC,SAAL,GAAiBA,SAAjB;AAEA;AACL;AACA;AACA;;AACK,cAAIkC,aAAa,GAAGtE,IAAI,CAACe,SAAzB;AAEA;AACL;AACA;AACA;AACA;;AACKuD,UAAAA,aAAa,CAACC,KAAd,GAAsB,SAASA,KAAT,GAAiB;AACrC,mBAAO,KAAKzD,QAAL,GAAgB,KAAKF,GAAL,KAAa,CAA7B,GAAiC,KAAKA,GAA7C;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACE,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,gBAAI,KAAK1D,QAAT,EACE,OAAO,CAAC,KAAKD,IAAL,KAAc,CAAf,IAAoB0B,cAApB,IAAsC,KAAK3B,GAAL,KAAa,CAAnD,CAAP;AACF,mBAAO,KAAKC,IAAL,GAAY0B,cAAZ,IAA8B,KAAK3B,GAAL,KAAa,CAA3C,CAAP;AACD,WAJD;AAMA;AACL;AACA;AACA;AACA;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACG,QAAd,GAAyB,SAASA,QAAT,CAAkB3B,KAAlB,EAAyB;AAChDA,YAAAA,KAAK,GAAGA,KAAK,IAAI,EAAjB;AACA,gBAAIA,KAAK,GAAG,CAAR,IAAa,KAAKA,KAAtB,EAA6B,MAAMG,UAAU,CAAC,OAAD,CAAhB;AAC7B,gBAAI,KAAKyB,MAAL,EAAJ,EAAmB,OAAO,GAAP;;AACnB,gBAAI,KAAKC,UAAL,EAAJ,EAAuB;AACrB;AACA,kBAAI,KAAKC,EAAL,CAAQxC,SAAR,CAAJ,EAAwB;AACtB;AACA;AACA,oBAAIyC,SAAS,GAAGhD,UAAU,CAACiB,KAAD,CAA1B;AAAA,oBACEgC,GAAG,GAAG,KAAKA,GAAL,CAASD,SAAT,CADR;AAAA,oBAEEE,IAAI,GAAGD,GAAG,CAAClB,GAAJ,CAAQiB,SAAR,EAAmBG,GAAnB,CAAuB,IAAvB,CAFT;AAGA,uBAAOF,GAAG,CAACL,QAAJ,CAAa3B,KAAb,IAAsBiC,IAAI,CAACR,KAAL,GAAaE,QAAb,CAAsB3B,KAAtB,CAA7B;AACD,eAPD,MAOO,OAAO,MAAM,KAAKR,GAAL,GAAWmC,QAAX,CAAoB3B,KAApB,CAAb;AACR,aAd+C,CAgBhD;AACA;;;AACA,gBAAIO,YAAY,GAAGxB,UAAU,CAACa,OAAO,CAACI,KAAD,EAAQ,CAAR,CAAR,EAAoB,KAAKhC,QAAzB,CAA7B;AAAA,gBACEmE,GAAG,GAAG,IADR;AAEA,gBAAI3B,MAAM,GAAG,EAAb;;AACA,mBAAO,IAAP,EAAa;AACX,kBAAI4B,MAAM,GAAGD,GAAG,CAACH,GAAJ,CAAQzB,YAAR,CAAb;AAAA,kBACE8B,MAAM,GAAGF,GAAG,CAACD,GAAJ,CAAQE,MAAM,CAACtB,GAAP,CAAWP,YAAX,CAAR,EAAkCkB,KAAlC,OAA8C,CADzD;AAAA,kBAEEa,MAAM,GAAGD,MAAM,CAACV,QAAP,CAAgB3B,KAAhB,CAFX;AAGAmC,cAAAA,GAAG,GAAGC,MAAN;AACA,kBAAID,GAAG,CAACP,MAAJ,EAAJ,EAAkB,OAAOU,MAAM,GAAG9B,MAAhB,CAAlB,KACK;AACH,uBAAO8B,MAAM,CAACrC,MAAP,GAAgB,CAAvB,EAA0BqC,MAAM,GAAG,MAAMA,MAAf;;AAC1B9B,gBAAAA,MAAM,GAAG,KAAK8B,MAAL,GAAc9B,MAAvB;AACD;AACF;AACF,WAhCD;AAkCA;AACL;AACA;AACA;AACA;;;AACKgB,UAAAA,aAAa,CAACe,WAAd,GAA4B,SAASA,WAAT,GAAuB;AACjD,mBAAO,KAAKxE,IAAZ;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACgB,mBAAd,GAAoC,SAASA,mBAAT,GAA+B;AACjE,mBAAO,KAAKzE,IAAL,KAAc,CAArB;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACiB,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,mBAAO,KAAK3E,GAAZ;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACkB,kBAAd,GAAmC,SAASA,kBAAT,GAA8B;AAC/D,mBAAO,KAAK5E,GAAL,KAAa,CAApB;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACmB,aAAd,GAA8B,SAASA,aAAT,GAAyB;AACrD,gBAAI,KAAKd,UAAL,EAAJ,EACE;AACA,qBAAO,KAAKC,EAAL,CAAQxC,SAAR,IAAqB,EAArB,GAA0B,KAAKE,GAAL,GAAWmD,aAAX,EAAjC;AACF,gBAAI1B,GAAG,GAAG,KAAKlD,IAAL,IAAa,CAAb,GAAiB,KAAKA,IAAtB,GAA6B,KAAKD,GAA5C;;AACA,iBAAK,IAAI8E,GAAG,GAAG,EAAf,EAAmBA,GAAG,GAAG,CAAzB,EAA4BA,GAAG,EAA/B,EAAmC,IAAI,CAAC3B,GAAG,GAAI,KAAK2B,GAAb,KAAsB,CAA1B,EAA6B;;AAChE,mBAAO,KAAK7E,IAAL,IAAa,CAAb,GAAiB6E,GAAG,GAAG,EAAvB,GAA4BA,GAAG,GAAG,CAAzC;AACD,WAPD;AASA;AACL;AACA;AACA;AACA;;;AACKpB,UAAAA,aAAa,CAACqB,aAAd,GAA8B,SAASA,aAAT,GAAyB;AACrD;AACA,gBAAIC,SAAS,GAAG,KAAK/E,IAAL,IAAa,EAA7B,CAFqD,CAGrD;;AACA,gBAAI,CAAC+E,SAAL,EAAgB,OAAO,IAAP,CAJqC,CAKrD;;AACA,gBAAI,KAAK9E,QAAT,EAAmB,OAAO,KAAP,CANkC,CAOrD;;AACA,mBAAO8E,SAAS,KAAK,CAAC,CAAf,IAAoB,EAAE,KAAKhF,GAAL,KAAa,CAAb,IAAkB,KAAKC,IAAL,KAAc,CAAC,QAAnC,CAA3B;AACD,WATD;AAWA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACI,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,mBAAO,KAAK7D,IAAL,KAAc,CAAd,IAAmB,KAAKD,GAAL,KAAa,CAAvC;AACD,WAFD;AAIA;AACL;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACuB,GAAd,GAAoBvB,aAAa,CAACI,MAAlC;AAEA;AACL;AACA;AACA;AACA;;AACKJ,UAAAA,aAAa,CAACK,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,mBAAO,CAAC,KAAK7D,QAAN,IAAkB,KAAKD,IAAL,GAAY,CAArC;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACwB,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,mBAAO,KAAKhF,QAAL,IAAiB,KAAKD,IAAL,IAAa,CAArC;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACyB,KAAd,GAAsB,SAASA,KAAT,GAAiB;AACrC,mBAAO,CAAC,KAAKnF,GAAL,GAAW,CAAZ,MAAmB,CAA1B;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAAC0B,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,mBAAO,CAAC,KAAKpF,GAAL,GAAW,CAAZ,MAAmB,CAA1B;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAAC2B,MAAd,GAAuB,SAASA,MAAT,CAAgBC,KAAhB,EAAuB;AAC5C,gBAAI,CAACjF,MAAM,CAACiF,KAAD,CAAX,EAAoBA,KAAK,GAAGpC,SAAS,CAACoC,KAAD,CAAjB;AACpB,gBACE,KAAKpF,QAAL,KAAkBoF,KAAK,CAACpF,QAAxB,IACA,KAAKD,IAAL,KAAc,EAAd,KAAqB,CADrB,IAEAqF,KAAK,CAACrF,IAAN,KAAe,EAAf,KAAsB,CAHxB,EAKE,OAAO,KAAP;AACF,mBAAO,KAAKA,IAAL,KAAcqF,KAAK,CAACrF,IAApB,IAA4B,KAAKD,GAAL,KAAasF,KAAK,CAACtF,GAAtD;AACD,WATD;AAWA;AACL;AACA;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACM,EAAd,GAAmBN,aAAa,CAAC2B,MAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK3B,UAAAA,aAAa,CAAC6B,SAAd,GAA0B,SAASA,SAAT,CAAmBD,KAAnB,EAA0B;AAClD,mBAAO,CAAC,KAAKtB,EAAL;AAAQ;AAAgBsB,YAAAA,KAAxB,CAAR;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK5B,UAAAA,aAAa,CAAC8B,GAAd,GAAoB9B,aAAa,CAAC6B,SAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK7B,UAAAA,aAAa,CAAC+B,EAAd,GAAmB/B,aAAa,CAAC6B,SAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK7B,UAAAA,aAAa,CAACgC,QAAd,GAAyB,SAASA,QAAT,CAAkBJ,KAAlB,EAAyB;AAChD,mBAAO,KAAKK,IAAL;AAAU;AAAgBL,YAAAA,KAA1B,IAAmC,CAA1C;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK5B,UAAAA,aAAa,CAACkC,EAAd,GAAmBlC,aAAa,CAACgC,QAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKhC,UAAAA,aAAa,CAACmC,eAAd,GAAgC,SAASA,eAAT,CAAyBP,KAAzB,EAAgC;AAC9D,mBAAO,KAAKK,IAAL;AAAU;AAAgBL,YAAAA,KAA1B,KAAoC,CAA3C;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK5B,UAAAA,aAAa,CAACoC,GAAd,GAAoBpC,aAAa,CAACmC,eAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKnC,UAAAA,aAAa,CAACqC,EAAd,GAAmBrC,aAAa,CAACmC,eAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKnC,UAAAA,aAAa,CAACsC,WAAd,GAA4B,SAASA,WAAT,CAAqBV,KAArB,EAA4B;AACtD,mBAAO,KAAKK,IAAL;AAAU;AAAgBL,YAAAA,KAA1B,IAAmC,CAA1C;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK5B,UAAAA,aAAa,CAACuC,EAAd,GAAmBvC,aAAa,CAACsC,WAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKtC,UAAAA,aAAa,CAACwC,kBAAd,GAAmC,SAASA,kBAAT,CAA4BZ,KAA5B,EAAmC;AACpE,mBAAO,KAAKK,IAAL;AAAU;AAAgBL,YAAAA,KAA1B,KAAoC,CAA3C;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK5B,UAAAA,aAAa,CAACyC,GAAd,GAAoBzC,aAAa,CAACwC,kBAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKxC,UAAAA,aAAa,CAAC0C,EAAd,GAAmB1C,aAAa,CAACwC,kBAAjC;AAEA;AACL;AACA;AACA;AACA;AACA;AACA;;AACKxC,UAAAA,aAAa,CAAC2C,OAAd,GAAwB,SAASA,OAAT,CAAiBf,KAAjB,EAAwB;AAC9C,gBAAI,CAACjF,MAAM,CAACiF,KAAD,CAAX,EAAoBA,KAAK,GAAGpC,SAAS,CAACoC,KAAD,CAAjB;AACpB,gBAAI,KAAKtB,EAAL,CAAQsB,KAAR,CAAJ,EAAoB,OAAO,CAAP;AACpB,gBAAIgB,OAAO,GAAG,KAAKvC,UAAL,EAAd;AAAA,gBACEwC,QAAQ,GAAGjB,KAAK,CAACvB,UAAN,EADb;AAEA,gBAAIuC,OAAO,IAAI,CAACC,QAAhB,EAA0B,OAAO,CAAC,CAAR;AAC1B,gBAAI,CAACD,OAAD,IAAYC,QAAhB,EAA0B,OAAO,CAAP,CANoB,CAO9C;;AACA,gBAAI,CAAC,KAAKrG,QAAV,EAAoB,OAAO,KAAKkE,GAAL,CAASkB,KAAT,EAAgBvB,UAAhB,KAA+B,CAAC,CAAhC,GAAoC,CAA3C,CAR0B,CAS9C;;AACA,mBAAOuB,KAAK,CAACrF,IAAN,KAAe,CAAf,GAAmB,KAAKA,IAAL,KAAc,CAAjC,IACJqF,KAAK,CAACrF,IAAN,KAAe,KAAKA,IAApB,IAA4BqF,KAAK,CAACtF,GAAN,KAAc,CAAd,GAAkB,KAAKA,GAAL,KAAa,CADvD,GAEH,CAAC,CAFE,GAGH,CAHJ;AAID,WAdD;AAgBA;AACL;AACA;AACA;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACiC,IAAd,GAAqBjC,aAAa,CAAC2C,OAAnC;AAEA;AACL;AACA;AACA;AACA;;AACK3C,UAAAA,aAAa,CAAC8C,MAAd,GAAuB,SAASA,MAAT,GAAkB;AACvC,gBAAI,CAAC,KAAKtG,QAAN,IAAkB,KAAK8D,EAAL,CAAQxC,SAAR,CAAtB,EAA0C,OAAOA,SAAP;AAC1C,mBAAO,KAAKiF,GAAL,GAAWxD,GAAX,CAAeM,GAAf,CAAP;AACD,WAHD;AAKA;AACL;AACA;AACA;AACA;;;AACKG,UAAAA,aAAa,CAAChC,GAAd,GAAoBgC,aAAa,CAAC8C,MAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK9C,UAAAA,aAAa,CAACT,GAAd,GAAoB,SAASA,GAAT,CAAayD,MAAb,EAAqB;AACvC,gBAAI,CAACrG,MAAM,CAACqG,MAAD,CAAX,EAAqBA,MAAM,GAAGxD,SAAS,CAACwD,MAAD,CAAlB,CADkB,CAGvC;;AAEA,gBAAIC,GAAG,GAAG,KAAK1G,IAAL,KAAc,EAAxB;AACA,gBAAI2G,GAAG,GAAG,KAAK3G,IAAL,GAAY,MAAtB;AACA,gBAAI4G,GAAG,GAAG,KAAK7G,GAAL,KAAa,EAAvB;AACA,gBAAI8G,GAAG,GAAG,KAAK9G,GAAL,GAAW,MAArB;AACA,gBAAI+G,GAAG,GAAGL,MAAM,CAACzG,IAAP,KAAgB,EAA1B;AACA,gBAAI+G,GAAG,GAAGN,MAAM,CAACzG,IAAP,GAAc,MAAxB;AACA,gBAAIgH,GAAG,GAAGP,MAAM,CAAC1G,GAAP,KAAe,EAAzB;AACA,gBAAIkH,GAAG,GAAGR,MAAM,CAAC1G,GAAP,GAAa,MAAvB;AACA,gBAAImH,GAAG,GAAG,CAAV;AAAA,gBACEC,GAAG,GAAG,CADR;AAAA,gBAEEC,GAAG,GAAG,CAFR;AAAA,gBAGEC,GAAG,GAAG,CAHR;AAIAA,YAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAI,YAAAA,GAAG,IAAI,MAAP;AACA,mBAAOnG,QAAQ,CAAEqG,GAAG,IAAI,EAAR,GAAcC,GAAf,EAAqBH,GAAG,IAAI,EAAR,GAAcC,GAAlC,EAAuC,KAAKlH,QAA5C,CAAf;AACD,WA7BD;AA+BA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAAC6D,QAAd,GAAyB,SAASA,QAAT,CAAkBC,UAAlB,EAA8B;AACrD,gBAAI,CAACnH,MAAM,CAACmH,UAAD,CAAX,EAAyBA,UAAU,GAAGtE,SAAS,CAACsE,UAAD,CAAtB;AACzB,mBAAO,KAAKvE,GAAL,CAASuE,UAAU,CAAC9F,GAAX,EAAT,CAAP;AACD,WAHD;AAKA;AACL;AACA;AACA;AACA;AACA;;;AACKgC,UAAAA,aAAa,CAACU,GAAd,GAAoBV,aAAa,CAAC6D,QAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK7D,UAAAA,aAAa,CAAC+D,QAAd,GAAyB,SAASA,QAAT,CAAkBC,UAAlB,EAA8B;AACrD,gBAAI,KAAK5D,MAAL,EAAJ,EAAmB,OAAO,IAAP;AACnB,gBAAI,CAACzD,MAAM,CAACqH,UAAD,CAAX,EAAyBA,UAAU,GAAGxE,SAAS,CAACwE,UAAD,CAAtB,CAF4B,CAIrD;;AACA,gBAAI/H,IAAJ,EAAU;AACR,kBAAIK,GAAG,GAAGL,IAAI,CAAC,KAAD,CAAJ,CACR,KAAKK,GADG,EAER,KAAKC,IAFG,EAGRyH,UAAU,CAAC1H,GAHH,EAIR0H,UAAU,CAACzH,IAJH,CAAV;AAMA,qBAAOe,QAAQ,CAAChB,GAAD,EAAML,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKO,QAA/B,CAAf;AACD;;AACD,gBAAIwH,UAAU,CAAC5D,MAAX,EAAJ,EAAyB,OAAO,KAAK5D,QAAL,GAAgBiB,KAAhB,GAAwBC,IAA/B;AACzB,gBAAI,KAAK4C,EAAL,CAAQxC,SAAR,CAAJ,EAAwB,OAAOkG,UAAU,CAACvC,KAAX,KAAqB3D,SAArB,GAAiCJ,IAAxC;AACxB,gBAAIsG,UAAU,CAAC1D,EAAX,CAAcxC,SAAd,CAAJ,EAA8B,OAAO,KAAK2D,KAAL,KAAe3D,SAAf,GAA2BJ,IAAlC;;AAC9B,gBAAI,KAAK2C,UAAL,EAAJ,EAAuB;AACrB,kBAAI2D,UAAU,CAAC3D,UAAX,EAAJ,EAA6B,OAAO,KAAKrC,GAAL,GAAWsB,GAAX,CAAe0E,UAAU,CAAChG,GAAX,EAAf,CAAP,CAA7B,KACK,OAAO,KAAKA,GAAL,GAAWsB,GAAX,CAAe0E,UAAf,EAA2BhG,GAA3B,EAAP;AACN,aAHD,MAGO,IAAIgG,UAAU,CAAC3D,UAAX,EAAJ,EACL,OAAO,KAAKf,GAAL,CAAS0E,UAAU,CAAChG,GAAX,EAAT,EAA2BA,GAA3B,EAAP,CArBmD,CAuBrD;;;AACA,gBAAI,KAAKkE,EAAL,CAAQtC,UAAR,KAAuBoE,UAAU,CAAC9B,EAAX,CAActC,UAAd,CAA3B,EACE,OAAOrC,UAAU,CACf,KAAK2C,QAAL,KAAkB8D,UAAU,CAAC9D,QAAX,EADH,EAEf,KAAK1D,QAFU,CAAjB,CAzBmD,CA8BrD;AACA;;AAEA,gBAAIyG,GAAG,GAAG,KAAK1G,IAAL,KAAc,EAAxB;AACA,gBAAI2G,GAAG,GAAG,KAAK3G,IAAL,GAAY,MAAtB;AACA,gBAAI4G,GAAG,GAAG,KAAK7G,GAAL,KAAa,EAAvB;AACA,gBAAI8G,GAAG,GAAG,KAAK9G,GAAL,GAAW,MAArB;AACA,gBAAI+G,GAAG,GAAGW,UAAU,CAACzH,IAAX,KAAoB,EAA9B;AACA,gBAAI+G,GAAG,GAAGU,UAAU,CAACzH,IAAX,GAAkB,MAA5B;AACA,gBAAIgH,GAAG,GAAGS,UAAU,CAAC1H,GAAX,KAAmB,EAA7B;AACA,gBAAIkH,GAAG,GAAGQ,UAAU,CAAC1H,GAAX,GAAiB,MAA3B;AACA,gBAAImH,GAAG,GAAG,CAAV;AAAA,gBACEC,GAAG,GAAG,CADR;AAAA,gBAEEC,GAAG,GAAG,CAFR;AAAA,gBAGEC,GAAG,GAAG,CAHR;AAIAA,YAAAA,GAAG,IAAIR,GAAG,GAAGI,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGK,GAAb;AACAE,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAA,YAAAA,GAAG,IAAIP,GAAG,GAAGG,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGM,GAAb;AACAC,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAA,YAAAA,GAAG,IAAIP,GAAG,GAAGI,GAAb;AACAE,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAA,YAAAA,GAAG,IAAIN,GAAG,GAAGE,GAAb;AACAG,YAAAA,GAAG,IAAIC,GAAG,KAAK,EAAf;AACAA,YAAAA,GAAG,IAAI,MAAP;AACAD,YAAAA,GAAG,IAAIR,GAAG,GAAGO,GAAN,GAAYN,GAAG,GAAGK,GAAlB,GAAwBJ,GAAG,GAAGG,GAA9B,GAAoCF,GAAG,GAAGC,GAAjD;AACAI,YAAAA,GAAG,IAAI,MAAP;AACA,mBAAOnG,QAAQ,CAAEqG,GAAG,IAAI,EAAR,GAAcC,GAAf,EAAqBH,GAAG,IAAI,EAAR,GAAcC,GAAlC,EAAuC,KAAKlH,QAA5C,CAAf;AACD,WAlED;AAoEA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACV,GAAd,GAAoBU,aAAa,CAAC+D,QAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;AACA;;AACK/D,UAAAA,aAAa,CAACiE,MAAd,GAAuB,SAASA,MAAT,CAAgBC,OAAhB,EAAyB;AAC9C,gBAAI,CAACvH,MAAM,CAACuH,OAAD,CAAX,EAAsBA,OAAO,GAAG1E,SAAS,CAAC0E,OAAD,CAAnB;AACtB,gBAAIA,OAAO,CAAC9D,MAAR,EAAJ,EAAsB,MAAM1B,KAAK,CAAC,kBAAD,CAAX,CAFwB,CAI9C;;AACA,gBAAIzC,IAAJ,EAAU;AACR;AACA;AACA;AACA,kBACE,CAAC,KAAKO,QAAN,IACA,KAAKD,IAAL,KAAc,CAAC,UADf,IAEA2H,OAAO,CAAC5H,GAAR,KAAgB,CAAC,CAFjB,IAGA4H,OAAO,CAAC3H,IAAR,KAAiB,CAAC,CAJpB,EAKE;AACA;AACA,uBAAO,IAAP;AACD;;AACD,kBAAID,GAAG,GAAG,CAAC,KAAKE,QAAL,GAAgBP,IAAI,CAAC,OAAD,CAApB,GAAgCA,IAAI,CAAC,OAAD,CAArC,EACR,KAAKK,GADG,EAER,KAAKC,IAFG,EAGR2H,OAAO,CAAC5H,GAHA,EAIR4H,OAAO,CAAC3H,IAJA,CAAV;AAMA,qBAAOe,QAAQ,CAAChB,GAAD,EAAML,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKO,QAA/B,CAAf;AACD;;AACD,gBAAI,KAAK4D,MAAL,EAAJ,EAAmB,OAAO,KAAK5D,QAAL,GAAgBiB,KAAhB,GAAwBC,IAA/B;AACnB,gBAAIyG,MAAJ,EAAYxD,GAAZ,EAAiByD,GAAjB;;AACA,gBAAI,CAAC,KAAK5H,QAAV,EAAoB;AAClB;AACA;AACA,kBAAI,KAAK8D,EAAL,CAAQxC,SAAR,CAAJ,EAAwB;AACtB,oBAAIoG,OAAO,CAAC5D,EAAR,CAAWT,GAAX,KAAmBqE,OAAO,CAAC5D,EAAR,CAAWP,OAAX,CAAvB,EACE,OAAOjC,SAAP,CADF,CACoB;AADpB,qBAEK,IAAIoG,OAAO,CAAC5D,EAAR,CAAWxC,SAAX,CAAJ,EAA2B,OAAO+B,GAAP,CAA3B,KACA;AACH;AACA,sBAAIwE,QAAQ,GAAG,KAAKC,GAAL,CAAS,CAAT,CAAf;AACAH,kBAAAA,MAAM,GAAGE,QAAQ,CAAC7D,GAAT,CAAa0D,OAAb,EAAsBK,GAAtB,CAA0B,CAA1B,CAAT;;AACA,sBAAIJ,MAAM,CAAC7D,EAAP,CAAU5C,IAAV,CAAJ,EAAqB;AACnB,2BAAOwG,OAAO,CAAC7D,UAAR,KAAuBR,GAAvB,GAA6BE,OAApC;AACD,mBAFD,MAEO;AACLY,oBAAAA,GAAG,GAAG,KAAKD,GAAL,CAASwD,OAAO,CAAC5E,GAAR,CAAY6E,MAAZ,CAAT,CAAN;AACAC,oBAAAA,GAAG,GAAGD,MAAM,CAAC5E,GAAP,CAAWoB,GAAG,CAACH,GAAJ,CAAQ0D,OAAR,CAAX,CAAN;AACA,2BAAOE,GAAP;AACD;AACF;AACF,eAhBD,MAgBO,IAAIF,OAAO,CAAC5D,EAAR,CAAWxC,SAAX,CAAJ,EAA2B,OAAO,KAAKtB,QAAL,GAAgBiB,KAAhB,GAAwBC,IAA/B;;AAClC,kBAAI,KAAK2C,UAAL,EAAJ,EAAuB;AACrB,oBAAI6D,OAAO,CAAC7D,UAAR,EAAJ,EAA0B,OAAO,KAAKrC,GAAL,GAAWwC,GAAX,CAAe0D,OAAO,CAAClG,GAAR,EAAf,CAAP;AAC1B,uBAAO,KAAKA,GAAL,GAAWwC,GAAX,CAAe0D,OAAf,EAAwBlG,GAAxB,EAAP;AACD,eAHD,MAGO,IAAIkG,OAAO,CAAC7D,UAAR,EAAJ,EAA0B,OAAO,KAAKG,GAAL,CAAS0D,OAAO,CAAClG,GAAR,EAAT,EAAwBA,GAAxB,EAAP;;AACjCoG,cAAAA,GAAG,GAAG1G,IAAN;AACD,aAzBD,MAyBO;AACL;AACA;AACA,kBAAI,CAACwG,OAAO,CAAC1H,QAAb,EAAuB0H,OAAO,GAAGA,OAAO,CAACM,UAAR,EAAV;AACvB,kBAAIN,OAAO,CAAC3B,EAAR,CAAW,IAAX,CAAJ,EAAsB,OAAO9E,KAAP;AACtB,kBAAIyG,OAAO,CAAC3B,EAAR,CAAW,KAAKkC,IAAL,CAAU,CAAV,CAAX,CAAJ,EACE;AACA,uBAAO3E,IAAP;AACFsE,cAAAA,GAAG,GAAG3G,KAAN;AACD,aA9D6C,CAgE9C;AACA;AACA;AACA;AACA;;;AACAkD,YAAAA,GAAG,GAAG,IAAN;;AACA,mBAAOA,GAAG,CAAC8B,GAAJ,CAAQyB,OAAR,CAAP,EAAyB;AACvB;AACA;AACAC,cAAAA,MAAM,GAAGpH,IAAI,CAAC2H,GAAL,CAAS,CAAT,EAAY3H,IAAI,CAAC4H,KAAL,CAAWhE,GAAG,CAACT,QAAJ,KAAiBgE,OAAO,CAAChE,QAAR,EAA5B,CAAZ,CAAT,CAHuB,CAKvB;AACA;;AACA,kBAAI0E,IAAI,GAAG7H,IAAI,CAAC8H,IAAL,CAAU9H,IAAI,CAAC+H,GAAL,CAASX,MAAT,IAAmBpH,IAAI,CAACgI,GAAlC,CAAX;AAAA,kBACEC,KAAK,GAAGJ,IAAI,IAAI,EAAR,GAAa,CAAb,GAAiBxG,OAAO,CAAC,CAAD,EAAIwG,IAAI,GAAG,EAAX,CADlC;AAAA,kBAEE;AACA;AACAK,cAAAA,SAAS,GAAG1H,UAAU,CAAC4G,MAAD,CAJxB;AAAA,kBAKEe,SAAS,GAAGD,SAAS,CAAC3F,GAAV,CAAc4E,OAAd,CALd;;AAMA,qBAAOgB,SAAS,CAAC7E,UAAV,MAA0B6E,SAAS,CAAC3C,EAAV,CAAa5B,GAAb,CAAjC,EAAoD;AAClDwD,gBAAAA,MAAM,IAAIa,KAAV;AACAC,gBAAAA,SAAS,GAAG1H,UAAU,CAAC4G,MAAD,EAAS,KAAK3H,QAAd,CAAtB;AACA0I,gBAAAA,SAAS,GAAGD,SAAS,CAAC3F,GAAV,CAAc4E,OAAd,CAAZ;AACD,eAjBsB,CAmBvB;AACA;;;AACA,kBAAIe,SAAS,CAAC7E,MAAV,EAAJ,EAAwB6E,SAAS,GAAGpF,GAAZ;AACxBuE,cAAAA,GAAG,GAAGA,GAAG,CAAC7E,GAAJ,CAAQ0F,SAAR,CAAN;AACAtE,cAAAA,GAAG,GAAGA,GAAG,CAACD,GAAJ,CAAQwE,SAAR,CAAN;AACD;;AACD,mBAAOd,GAAP;AACD,WAhGD;AAkGA;AACL;AACA;AACA;AACA;AACA;;;AACKpE,UAAAA,aAAa,CAACQ,GAAd,GAAoBR,aAAa,CAACiE,MAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKjE,UAAAA,aAAa,CAACmF,MAAd,GAAuB,SAASA,MAAT,CAAgBjB,OAAhB,EAAyB;AAC9C,gBAAI,CAACvH,MAAM,CAACuH,OAAD,CAAX,EAAsBA,OAAO,GAAG1E,SAAS,CAAC0E,OAAD,CAAnB,CADwB,CAG9C;;AACA,gBAAIjI,IAAJ,EAAU;AACR,kBAAIK,GAAG,GAAG,CAAC,KAAKE,QAAL,GAAgBP,IAAI,CAAC,OAAD,CAApB,GAAgCA,IAAI,CAAC,OAAD,CAArC,EACR,KAAKK,GADG,EAER,KAAKC,IAFG,EAGR2H,OAAO,CAAC5H,GAHA,EAIR4H,OAAO,CAAC3H,IAJA,CAAV;AAMA,qBAAOe,QAAQ,CAAChB,GAAD,EAAML,IAAI,CAAC,UAAD,CAAJ,EAAN,EAA0B,KAAKO,QAA/B,CAAf;AACD;;AACD,mBAAO,KAAKkE,GAAL,CAAS,KAAKF,GAAL,CAAS0D,OAAT,EAAkB5E,GAAlB,CAAsB4E,OAAtB,CAAT,CAAP;AACD,WAdD;AAgBA;AACL;AACA;AACA;AACA;AACA;;;AACKlE,UAAAA,aAAa,CAACoF,GAAd,GAAoBpF,aAAa,CAACmF,MAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKnF,UAAAA,aAAa,CAACW,GAAd,GAAoBX,aAAa,CAACmF,MAAlC;AAEA;AACL;AACA;AACA;AACA;;AACKnF,UAAAA,aAAa,CAAC+C,GAAd,GAAoB,SAASA,GAAT,GAAe;AACjC,mBAAOzF,QAAQ,CAAC,CAAC,KAAKhB,GAAP,EAAY,CAAC,KAAKC,IAAlB,EAAwB,KAAKC,QAA7B,CAAf;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACqF,iBAAd,GAAkC,SAASA,iBAAT,GAA6B;AAC7D,mBAAO,KAAK9I,IAAL,GAAYQ,IAAI,CAACC,KAAL,CAAW,KAAKT,IAAhB,CAAZ,GAAoCQ,IAAI,CAACC,KAAL,CAAW,KAAKV,GAAhB,IAAuB,EAAlE;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACK0D,UAAAA,aAAa,CAACsF,GAAd,GAAoBtF,aAAa,CAACqF,iBAAlC;AAEA;AACL;AACA;AACA;AACA;;AACKrF,UAAAA,aAAa,CAACuF,kBAAd,GAAmC,SAASA,kBAAT,GAA8B;AAC/D,mBAAO,KAAKjJ,GAAL,GAAWO,KAAK,CAAC,KAAKP,GAAN,CAAhB,GAA6BO,KAAK,CAAC,KAAKN,IAAN,CAAL,GAAmB,EAAvD;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACwF,GAAd,GAAoBxF,aAAa,CAACuF,kBAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKvF,UAAAA,aAAa,CAACyF,GAAd,GAAoB,SAASA,GAAT,CAAa7D,KAAb,EAAoB;AACtC,gBAAI,CAACjF,MAAM,CAACiF,KAAD,CAAX,EAAoBA,KAAK,GAAGpC,SAAS,CAACoC,KAAD,CAAjB;AACpB,mBAAOtE,QAAQ,CACb,KAAKhB,GAAL,GAAWsF,KAAK,CAACtF,GADJ,EAEb,KAAKC,IAAL,GAAYqF,KAAK,CAACrF,IAFL,EAGb,KAAKC,QAHQ,CAAf;AAKD,WAPD;AASA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAAC0F,EAAd,GAAmB,SAASA,EAAT,CAAY9D,KAAZ,EAAmB;AACpC,gBAAI,CAACjF,MAAM,CAACiF,KAAD,CAAX,EAAoBA,KAAK,GAAGpC,SAAS,CAACoC,KAAD,CAAjB;AACpB,mBAAOtE,QAAQ,CACb,KAAKhB,GAAL,GAAWsF,KAAK,CAACtF,GADJ,EAEb,KAAKC,IAAL,GAAYqF,KAAK,CAACrF,IAFL,EAGb,KAAKC,QAHQ,CAAf;AAKD,WAPD;AASA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAAC2F,GAAd,GAAoB,SAASA,GAAT,CAAa/D,KAAb,EAAoB;AACtC,gBAAI,CAACjF,MAAM,CAACiF,KAAD,CAAX,EAAoBA,KAAK,GAAGpC,SAAS,CAACoC,KAAD,CAAjB;AACpB,mBAAOtE,QAAQ,CACb,KAAKhB,GAAL,GAAWsF,KAAK,CAACtF,GADJ,EAEb,KAAKC,IAAL,GAAYqF,KAAK,CAACrF,IAFL,EAGb,KAAKC,QAHQ,CAAf;AAKD,WAPD;AASA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAAC4F,SAAd,GAA0B,SAASA,SAAT,CAAmBC,OAAnB,EAA4B;AACpD,gBAAIlJ,MAAM,CAACkJ,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,gBAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP,CAA3B,KACK,IAAIA,OAAO,GAAG,EAAd,EACH,OAAOvI,QAAQ,CACb,KAAKhB,GAAL,IAAYuJ,OADC,EAEZ,KAAKtJ,IAAL,IAAasJ,OAAd,GAA0B,KAAKvJ,GAAL,KAAc,KAAKuJ,OAFhC,EAGb,KAAKrJ,QAHQ,CAAf,CADG,KAMA,OAAOc,QAAQ,CAAC,CAAD,EAAI,KAAKhB,GAAL,IAAauJ,OAAO,GAAG,EAA3B,EAAgC,KAAKrJ,QAArC,CAAf;AACN,WAVD;AAYA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACuE,GAAd,GAAoBvE,aAAa,CAAC4F,SAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK5F,UAAAA,aAAa,CAAC8F,UAAd,GAA2B,SAASA,UAAT,CAAoBD,OAApB,EAA6B;AACtD,gBAAIlJ,MAAM,CAACkJ,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,gBAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP,CAA3B,KACK,IAAIA,OAAO,GAAG,EAAd,EACH,OAAOvI,QAAQ,CACZ,KAAKhB,GAAL,KAAauJ,OAAd,GAA0B,KAAKtJ,IAAL,IAAc,KAAKsJ,OADhC,EAEb,KAAKtJ,IAAL,IAAasJ,OAFA,EAGb,KAAKrJ,QAHQ,CAAf,CADG,KAOH,OAAOc,QAAQ,CACb,KAAKf,IAAL,IAAcsJ,OAAO,GAAG,EADX,EAEb,KAAKtJ,IAAL,IAAa,CAAb,GAAiB,CAAjB,GAAqB,CAAC,CAFT,EAGb,KAAKC,QAHQ,CAAf;AAKH,WAfD;AAiBA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACsE,GAAd,GAAoBtE,aAAa,CAAC8F,UAAlC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK9F,UAAAA,aAAa,CAAC+F,kBAAd,GAAmC,SAASA,kBAAT,CAA4BF,OAA5B,EAAqC;AACtE,gBAAIlJ,MAAM,CAACkJ,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,gBAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,gBAAIA,OAAO,GAAG,EAAd,EACE,OAAOvI,QAAQ,CACZ,KAAKhB,GAAL,KAAauJ,OAAd,GAA0B,KAAKtJ,IAAL,IAAc,KAAKsJ,OADhC,EAEb,KAAKtJ,IAAL,KAAcsJ,OAFD,EAGb,KAAKrJ,QAHQ,CAAf;AAKF,gBAAIqJ,OAAO,KAAK,EAAhB,EAAoB,OAAOvI,QAAQ,CAAC,KAAKf,IAAN,EAAY,CAAZ,EAAe,KAAKC,QAApB,CAAf;AACpB,mBAAOc,QAAQ,CAAC,KAAKf,IAAL,KAAesJ,OAAO,GAAG,EAA1B,EAA+B,CAA/B,EAAkC,KAAKrJ,QAAvC,CAAf;AACD,WAXD;AAaA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACyE,IAAd,GAAqBzE,aAAa,CAAC+F,kBAAnC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK/F,UAAAA,aAAa,CAACgG,KAAd,GAAsBhG,aAAa,CAAC+F,kBAApC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACK/F,UAAAA,aAAa,CAACiG,UAAd,GAA2B,SAASA,UAAT,CAAoBJ,OAApB,EAA6B;AACtD,gBAAIK,CAAJ;AACA,gBAAIvJ,MAAM,CAACkJ,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,gBAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,gBAAIA,OAAO,KAAK,EAAhB,EAAoB,OAAOvI,QAAQ,CAAC,KAAKf,IAAN,EAAY,KAAKD,GAAjB,EAAsB,KAAKE,QAA3B,CAAf;;AACpB,gBAAIqJ,OAAO,GAAG,EAAd,EAAkB;AAChBK,cAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,qBAAOvI,QAAQ,CACZ,KAAKhB,GAAL,IAAYuJ,OAAb,GAAyB,KAAKtJ,IAAL,KAAc2J,CAD1B,EAEZ,KAAK3J,IAAL,IAAasJ,OAAd,GAA0B,KAAKvJ,GAAL,KAAa4J,CAF1B,EAGb,KAAK1J,QAHQ,CAAf;AAKD;;AACDqJ,YAAAA,OAAO,IAAI,EAAX;AACAK,YAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,mBAAOvI,QAAQ,CACZ,KAAKf,IAAL,IAAasJ,OAAd,GAA0B,KAAKvJ,GAAL,KAAa4J,CAD1B,EAEZ,KAAK5J,GAAL,IAAYuJ,OAAb,GAAyB,KAAKtJ,IAAL,KAAc2J,CAF1B,EAGb,KAAK1J,QAHQ,CAAf;AAKD,WApBD;AAqBA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACmG,IAAd,GAAqBnG,aAAa,CAACiG,UAAnC;AAEA;AACL;AACA;AACA;AACA;AACA;;AACKjG,UAAAA,aAAa,CAACoG,WAAd,GAA4B,SAASA,WAAT,CAAqBP,OAArB,EAA8B;AACxD,gBAAIK,CAAJ;AACA,gBAAIvJ,MAAM,CAACkJ,OAAD,CAAV,EAAqBA,OAAO,GAAGA,OAAO,CAAC5F,KAAR,EAAV;AACrB,gBAAI,CAAC4F,OAAO,IAAI,EAAZ,MAAoB,CAAxB,EAA2B,OAAO,IAAP;AAC3B,gBAAIA,OAAO,KAAK,EAAhB,EAAoB,OAAOvI,QAAQ,CAAC,KAAKf,IAAN,EAAY,KAAKD,GAAjB,EAAsB,KAAKE,QAA3B,CAAf;;AACpB,gBAAIqJ,OAAO,GAAG,EAAd,EAAkB;AAChBK,cAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,qBAAOvI,QAAQ,CACZ,KAAKf,IAAL,IAAa2J,CAAd,GAAoB,KAAK5J,GAAL,KAAauJ,OADpB,EAEZ,KAAKvJ,GAAL,IAAY4J,CAAb,GAAmB,KAAK3J,IAAL,KAAcsJ,OAFpB,EAGb,KAAKrJ,QAHQ,CAAf;AAKD;;AACDqJ,YAAAA,OAAO,IAAI,EAAX;AACAK,YAAAA,CAAC,GAAG,KAAKL,OAAT;AACA,mBAAOvI,QAAQ,CACZ,KAAKhB,GAAL,IAAY4J,CAAb,GAAmB,KAAK3J,IAAL,KAAcsJ,OADpB,EAEZ,KAAKtJ,IAAL,IAAa2J,CAAd,GAAoB,KAAK5J,GAAL,KAAauJ,OAFpB,EAGb,KAAKrJ,QAHQ,CAAf;AAKD,WApBD;AAqBA;AACL;AACA;AACA;AACA;AACA;;;AACKwD,UAAAA,aAAa,CAACqG,IAAd,GAAqBrG,aAAa,CAACoG,WAAnC;AAEA;AACL;AACA;AACA;AACA;;AACKpG,UAAAA,aAAa,CAACsG,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,gBAAI,CAAC,KAAK9J,QAAV,EAAoB,OAAO,IAAP;AACpB,mBAAOc,QAAQ,CAAC,KAAKhB,GAAN,EAAW,KAAKC,IAAhB,EAAsB,KAAtB,CAAf;AACD,WAHD;AAKA;AACL;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACwE,UAAd,GAA2B,SAASA,UAAT,GAAsB;AAC/C,gBAAI,KAAKhI,QAAT,EAAmB,OAAO,IAAP;AACnB,mBAAOc,QAAQ,CAAC,KAAKhB,GAAN,EAAW,KAAKC,IAAhB,EAAsB,IAAtB,CAAf;AACD,WAHD;AAKA;AACL;AACA;AACA;AACA;AACA;;;AACKyD,UAAAA,aAAa,CAACuG,OAAd,GAAwB,SAASA,OAAT,CAAiBlE,EAAjB,EAAqB;AAC3C,mBAAOA,EAAE,GAAG,KAAKmE,SAAL,EAAH,GAAsB,KAAKC,SAAL,EAA/B;AACD,WAFD;AAIA;AACL;AACA;AACA;AACA;;;AACKzG,UAAAA,aAAa,CAACwG,SAAd,GAA0B,SAASA,SAAT,GAAqB;AAC7C,gBAAIE,EAAE,GAAG,KAAKnK,IAAd;AAAA,gBACEoK,EAAE,GAAG,KAAKrK,GADZ;AAEA,mBAAO,CACLqK,EAAE,GAAG,IADA,EAEJA,EAAE,KAAK,CAAR,GAAa,IAFR,EAGJA,EAAE,KAAK,EAAR,GAAc,IAHT,EAILA,EAAE,KAAK,EAJF,EAKLD,EAAE,GAAG,IALA,EAMJA,EAAE,KAAK,CAAR,GAAa,IANR,EAOJA,EAAE,KAAK,EAAR,GAAc,IAPT,EAQLA,EAAE,KAAK,EARF,CAAP;AAUD,WAbD;AAeA;AACL;AACA;AACA;AACA;;;AACK1G,UAAAA,aAAa,CAACyG,SAAd,GAA0B,SAASA,SAAT,GAAqB;AAC7C,gBAAIC,EAAE,GAAG,KAAKnK,IAAd;AAAA,gBACEoK,EAAE,GAAG,KAAKrK,GADZ;AAEA,mBAAO,CACLoK,EAAE,KAAK,EADF,EAEJA,EAAE,KAAK,EAAR,GAAc,IAFT,EAGJA,EAAE,KAAK,CAAR,GAAa,IAHR,EAILA,EAAE,GAAG,IAJA,EAKLC,EAAE,KAAK,EALF,EAMJA,EAAE,KAAK,EAAR,GAAc,IANT,EAOJA,EAAE,KAAK,CAAR,GAAa,IAPR,EAQLA,EAAE,GAAG,IARA,CAAP;AAUD,WAbD;AAeA;AACL;AACA;AACA;AACA;AACA;AACA;;;AACKjL,UAAAA,IAAI,CAACkL,SAAL,GAAiB,SAASA,SAAT,CAAmBC,KAAnB,EAA0BrK,QAA1B,EAAoC6F,EAApC,EAAwC;AACvD,mBAAOA,EAAE,GACL3G,IAAI,CAACoL,WAAL,CAAiBD,KAAjB,EAAwBrK,QAAxB,CADK,GAELd,IAAI,CAACqL,WAAL,CAAiBF,KAAjB,EAAwBrK,QAAxB,CAFJ;AAGD,WAJD;AAMA;AACL;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAACoL,WAAL,GAAmB,SAASA,WAAT,CAAqBD,KAArB,EAA4BrK,QAA5B,EAAsC;AACvD,mBAAO,IAAId,IAAJ,CACLmL,KAAK,CAAC,CAAD,CAAL,GAAYA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAxB,GAA8BA,KAAK,CAAC,CAAD,CAAL,IAAY,EAA1C,GAAiDA,KAAK,CAAC,CAAD,CAAL,IAAY,EADxD,EAELA,KAAK,CAAC,CAAD,CAAL,GAAYA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAxB,GAA8BA,KAAK,CAAC,CAAD,CAAL,IAAY,EAA1C,GAAiDA,KAAK,CAAC,CAAD,CAAL,IAAY,EAFxD,EAGLrK,QAHK,CAAP;AAKD,WAND;AAQA;AACL;AACA;AACA;AACA;AACA;;;AACKd,UAAAA,IAAI,CAACqL,WAAL,GAAmB,SAASA,WAAT,CAAqBF,KAArB,EAA4BrK,QAA5B,EAAsC;AACvD,mBAAO,IAAId,IAAJ,CACJmL,KAAK,CAAC,CAAD,CAAL,IAAY,EAAb,GAAoBA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAhC,GAAuCA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAnD,GAAwDA,KAAK,CAAC,CAAD,CADxD,EAEJA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAb,GAAoBA,KAAK,CAAC,CAAD,CAAL,IAAY,EAAhC,GAAuCA,KAAK,CAAC,CAAD,CAAL,IAAY,CAAnD,GAAwDA,KAAK,CAAC,CAAD,CAFxD,EAGLrK,QAHK,CAAP;AAKD,WAND,CAhhDkB,CAwhDlB;;;AACA,cAAI,OAAOwK,MAAP,KAAkB,UAAtB,EAAkC;AAChC;AACP;AACA;AACA;AACA;AACA;AACA;AACOtL,YAAAA,IAAI,CAACuL,UAAL,GAAkB,SAASA,UAAT,CAAoBjL,KAApB,EAA2BQ,QAA3B,EAAqC;AACrD,kBAAI0B,OAAO,GAAGgJ,MAAM,CAACF,MAAM,CAACG,MAAP,CAAc,EAAd,EAAkBnL,KAAlB,CAAD,CAApB;AACA,kBAAImC,QAAQ,GAAG+I,MAAM,CAACF,MAAM,CAACG,MAAP,CAAc,EAAd,EAAkBnL,KAAK,IAAIgL,MAAM,CAAC,EAAD,CAAjC,CAAD,CAArB;AACA,qBAAO1J,QAAQ,CAACY,OAAD,EAAUC,QAAV,EAAoB3B,QAApB,CAAf;AACD,aAJD,CARgC,CAchC;;;AACAd,YAAAA,IAAI,CAAC8D,SAAL,GAAiB,SAAS4H,mBAAT,CAA6BpL,KAA7B,EAAoCQ,QAApC,EAA8C;AAC7D,kBAAI,OAAOR,KAAP,KAAiB,QAArB,EAA+B,OAAON,IAAI,CAACuL,UAAL,CAAgBjL,KAAhB,EAAuBQ,QAAvB,CAAP;AAC/B,qBAAOgD,SAAS,CAACxD,KAAD,EAAQQ,QAAR,CAAhB;AACD,aAHD;AAKA;AACP;AACA;AACA;AACA;;;AACOwD,YAAAA,aAAa,CAACqH,QAAd,GAAyB,SAASA,QAAT,GAAoB;AAC3C,kBAAIC,SAAS,GAAGN,MAAM,CAAC,KAAK1K,GAAL,KAAa,CAAd,CAAtB;AACA,kBAAIiL,UAAU,GAAGP,MAAM,CAAC,KAAKxK,QAAL,GAAgB,KAAKD,IAAL,KAAc,CAA9B,GAAkC,KAAKA,IAAxC,CAAvB;AACA,qBAAQgL,UAAU,IAAIP,MAAM,CAAC,EAAD,CAArB,GAA6BM,SAApC;AACD,aAJD;AAKD;;AACD,cAAIE,QAAQ,GAAI3L,QAAQ,CAACL,OAAT,GAAmBE,IAAnC;AACD,SAnlDH,EAL0F,CA4lD3F;;;AAEA,2BAAA+L,WAAW,GAAGvM,MAAM,CAACF,OAArB;AAGC,OAjmDD,EAimDG,EAjmDH", "sourcesContent": ["import _cjsLoader from 'cce:/internal/ml/cjs-loader.mjs';\nlet _cjsExports;\nconst __cjsMetaURL = import.meta.url;\n_cjsLoader.define(__cjsMetaURL, function (exports, require, module, __filename, __dirname) {\n// #region ORIGINAL CODE\n\n\n // GENERATED FILE. DO NOT EDIT.\n (function (global, factory) {\n   function preferDefault(exports) {\n     return exports.default || exports;\n   }\n   if (typeof define === \"function\" && define.amd) {\n     define([], function () {\n       var exports = {};\n       factory(exports);\n       return preferDefault(exports);\n     });\n   } else if (typeof exports === \"object\") {\n     factory(exports);\n     if (typeof module === \"object\") module.exports = preferDefault(exports);\n   } else {\n     (function () {\n       var exports = {};\n       factory(exports);\n       global.Long = preferDefault(exports);\n     })();\n   }\n })(\n   typeof globalThis !== \"undefined\"\n     ? globalThis\n     : typeof self !== \"undefined\"\n       ? self\n       : this,\n   function (_exports) {\n     \"use strict\";\n\n     Object.defineProperty(_exports, \"__esModule\", {\n       value: true,\n     });\n     _exports.default = void 0;\n     /**\n      * @license\n      * Copyright 2009 The Closure Library Authors\n      * Copyright 2020 Daniel Wirtz / The long.js Authors.\n      *\n      * Licensed under the Apache License, Version 2.0 (the \"License\");\n      * you may not use this file except in compliance with the License.\n      * You may obtain a copy of the License at\n      *\n      *     http://www.apache.org/licenses/LICENSE-2.0\n      *\n      * Unless required by applicable law or agreed to in writing, software\n      * distributed under the License is distributed on an \"AS IS\" BASIS,\n      * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n      * See the License for the specific language governing permissions and\n      * limitations under the License.\n      *\n      * SPDX-License-Identifier: Apache-2.0\n      */\n\n     // WebAssembly optimizations to do native i64 multiplication and divide\n     var wasm = null;\n     try {\n       wasm = new WebAssembly.Instance(\n         new WebAssembly.Module(\n           new Uint8Array([\n             // \\0asm\n             0, 97, 115, 109,\n             // version 1\n             1, 0, 0, 0,\n             // section \"type\"\n             1, 13, 2,\n             // 0, () => i32\n             96, 0, 1, 127,\n             // 1, (i32, i32, i32, i32) => i32\n             96, 4, 127, 127, 127, 127, 1, 127,\n             // section \"function\"\n             3, 7, 6,\n             // 0, type 0\n             0,\n             // 1, type 1\n             1,\n             // 2, type 1\n             1,\n             // 3, type 1\n             1,\n             // 4, type 1\n             1,\n             // 5, type 1\n             1,\n             // section \"global\"\n             6, 6, 1,\n             // 0, \"high\", mutable i32\n             127, 1, 65, 0, 11,\n             // section \"export\"\n             7, 50, 6,\n             // 0, \"mul\"\n             3, 109, 117, 108, 0, 1,\n             // 1, \"div_s\"\n             5, 100, 105, 118, 95, 115, 0, 2,\n             // 2, \"div_u\"\n             5, 100, 105, 118, 95, 117, 0, 3,\n             // 3, \"rem_s\"\n             5, 114, 101, 109, 95, 115, 0, 4,\n             // 4, \"rem_u\"\n             5, 114, 101, 109, 95, 117, 0, 5,\n             // 5, \"get_high\"\n             8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n             // section \"code\"\n             10, 191, 1, 6,\n             // 0, \"get_high\"\n             4, 0, 35, 0, 11,\n             // 1, \"mul\"\n             36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n             32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0,\n             32, 4, 167, 11,\n             // 2, \"div_s\"\n             36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n             32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0,\n             32, 4, 167, 11,\n             // 3, \"div_u\"\n             36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n             32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0,\n             32, 4, 167, 11,\n             // 4, \"rem_s\"\n             36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n             32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0,\n             32, 4, 167, 11,\n             // 5, \"rem_u\"\n             36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n             32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0,\n             32, 4, 167, 11,\n           ]),\n         ),\n         {},\n       ).exports;\n     } catch {\n       // no wasm support :(\n     }\n\n     /**\n      * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n      *  See the from* functions below for more convenient ways of constructing Longs.\n      * @exports Long\n      * @class A Long class for representing a 64 bit two's-complement integer value.\n      * @param {number} low The low (signed) 32 bits of the long\n      * @param {number} high The high (signed) 32 bits of the long\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @constructor\n      */\n     function Long(low, high, unsigned) {\n       /**\n        * The low 32 bits as a signed value.\n        * @type {number}\n        */\n       this.low = low | 0;\n\n       /**\n        * The high 32 bits as a signed value.\n        * @type {number}\n        */\n       this.high = high | 0;\n\n       /**\n        * Whether unsigned or not.\n        * @type {boolean}\n        */\n       this.unsigned = !!unsigned;\n     }\n\n     // The internal representation of a long is the two given signed, 32-bit values.\n     // We use 32-bit pieces because these are the size of integers on which\n     // Javascript performs bit-operations.  For operations like addition and\n     // multiplication, we split each number into 16 bit pieces, which can easily be\n     // multiplied within Javascript's floating-point representation without overflow\n     // or change in sign.\n     //\n     // In the algorithms below, we frequently reduce the negative case to the\n     // positive case by negating the input(s) and then post-processing the result.\n     // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n     // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n     // a positive number, it overflows back into a negative).  Not handling this\n     // case would often result in infinite recursion.\n     //\n     // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n     // methods on which they depend.\n\n     /**\n      * An indicator used to reliably determine if an object is a Long or not.\n      * @type {boolean}\n      * @const\n      * @private\n      */\n     Long.prototype.__isLong__;\n     Object.defineProperty(Long.prototype, \"__isLong__\", {\n       value: true,\n     });\n\n     /**\n      * @function\n      * @param {*} obj Object\n      * @returns {boolean}\n      * @inner\n      */\n     function isLong(obj) {\n       return (obj && obj[\"__isLong__\"]) === true;\n     }\n\n     /**\n      * @function\n      * @param {*} value number\n      * @returns {number}\n      * @inner\n      */\n     function ctz32(value) {\n       var c = Math.clz32(value & -value);\n       return value ? 31 - c : c;\n     }\n\n     /**\n      * Tests if the specified object is a Long.\n      * @function\n      * @param {*} obj Object\n      * @returns {boolean}\n      */\n     Long.isLong = isLong;\n\n     /**\n      * A cache of the Long representations of small integer values.\n      * @type {!Object}\n      * @inner\n      */\n     var INT_CACHE = {};\n\n     /**\n      * A cache of the Long representations of small unsigned integer values.\n      * @type {!Object}\n      * @inner\n      */\n     var UINT_CACHE = {};\n\n     /**\n      * @param {number} value\n      * @param {boolean=} unsigned\n      * @returns {!Long}\n      * @inner\n      */\n     function fromInt(value, unsigned) {\n       var obj, cachedObj, cache;\n       if (unsigned) {\n         value >>>= 0;\n         if ((cache = 0 <= value && value < 256)) {\n           cachedObj = UINT_CACHE[value];\n           if (cachedObj) return cachedObj;\n         }\n         obj = fromBits(value, 0, true);\n         if (cache) UINT_CACHE[value] = obj;\n         return obj;\n       } else {\n         value |= 0;\n         if ((cache = -128 <= value && value < 128)) {\n           cachedObj = INT_CACHE[value];\n           if (cachedObj) return cachedObj;\n         }\n         obj = fromBits(value, value < 0 ? -1 : 0, false);\n         if (cache) INT_CACHE[value] = obj;\n         return obj;\n       }\n     }\n\n     /**\n      * Returns a Long representing the given 32 bit integer value.\n      * @function\n      * @param {number} value The 32 bit integer in question\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {!Long} The corresponding Long value\n      */\n     Long.fromInt = fromInt;\n\n     /**\n      * @param {number} value\n      * @param {boolean=} unsigned\n      * @returns {!Long}\n      * @inner\n      */\n     function fromNumber(value, unsigned) {\n       if (isNaN(value)) return unsigned ? UZERO : ZERO;\n       if (unsigned) {\n         if (value < 0) return UZERO;\n         if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n       } else {\n         if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n         if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n       }\n       if (value < 0) return fromNumber(-value, unsigned).neg();\n       return fromBits(\n         value % TWO_PWR_32_DBL | 0,\n         (value / TWO_PWR_32_DBL) | 0,\n         unsigned,\n       );\n     }\n\n     /**\n      * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n      * @function\n      * @param {number} value The number in question\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {!Long} The corresponding Long value\n      */\n     Long.fromNumber = fromNumber;\n\n     /**\n      * @param {number} lowBits\n      * @param {number} highBits\n      * @param {boolean=} unsigned\n      * @returns {!Long}\n      * @inner\n      */\n     function fromBits(lowBits, highBits, unsigned) {\n       return new Long(lowBits, highBits, unsigned);\n     }\n\n     /**\n      * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n      *  assumed to use 32 bits.\n      * @function\n      * @param {number} lowBits The low 32 bits\n      * @param {number} highBits The high 32 bits\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {!Long} The corresponding Long value\n      */\n     Long.fromBits = fromBits;\n\n     /**\n      * @function\n      * @param {number} base\n      * @param {number} exponent\n      * @returns {number}\n      * @inner\n      */\n     var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n     /**\n      * @param {string} str\n      * @param {(boolean|number)=} unsigned\n      * @param {number=} radix\n      * @returns {!Long}\n      * @inner\n      */\n     function fromString(str, unsigned, radix) {\n       if (str.length === 0) throw Error(\"empty string\");\n       if (typeof unsigned === \"number\") {\n         // For goog.math.long compatibility\n         radix = unsigned;\n         unsigned = false;\n       } else {\n         unsigned = !!unsigned;\n       }\n       if (\n         str === \"NaN\" ||\n         str === \"Infinity\" ||\n         str === \"+Infinity\" ||\n         str === \"-Infinity\"\n       )\n         return unsigned ? UZERO : ZERO;\n       radix = radix || 10;\n       if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n       var p;\n       if ((p = str.indexOf(\"-\")) > 0) throw Error(\"interior hyphen\");\n       else if (p === 0) {\n         return fromString(str.substring(1), unsigned, radix).neg();\n       }\n\n       // Do several (8) digits each time through the loop, so as to\n       // minimize the calls to the very expensive emulated div.\n       var radixToPower = fromNumber(pow_dbl(radix, 8));\n       var result = ZERO;\n       for (var i = 0; i < str.length; i += 8) {\n         var size = Math.min(8, str.length - i),\n           value = parseInt(str.substring(i, i + size), radix);\n         if (size < 8) {\n           var power = fromNumber(pow_dbl(radix, size));\n           result = result.mul(power).add(fromNumber(value));\n         } else {\n           result = result.mul(radixToPower);\n           result = result.add(fromNumber(value));\n         }\n       }\n       result.unsigned = unsigned;\n       return result;\n     }\n\n     /**\n      * Returns a Long representation of the given string, written using the specified radix.\n      * @function\n      * @param {string} str The textual representation of the Long\n      * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n      * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n      * @returns {!Long} The corresponding Long value\n      */\n     Long.fromString = fromString;\n\n     /**\n      * @function\n      * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n      * @param {boolean=} unsigned\n      * @returns {!Long}\n      * @inner\n      */\n     function fromValue(val, unsigned) {\n       if (typeof val === \"number\") return fromNumber(val, unsigned);\n       if (typeof val === \"string\") return fromString(val, unsigned);\n       // Throws for non-objects, converts non-instanceof Long:\n       return fromBits(\n         val.low,\n         val.high,\n         typeof unsigned === \"boolean\" ? unsigned : val.unsigned,\n       );\n     }\n\n     /**\n      * Converts the specified value to a Long using the appropriate from* function for its type.\n      * @function\n      * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {!Long}\n      */\n     Long.fromValue = fromValue;\n\n     // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n     // no runtime penalty for these.\n\n     /**\n      * @type {number}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_16_DBL = 1 << 16;\n\n     /**\n      * @type {number}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_24_DBL = 1 << 24;\n\n     /**\n      * @type {number}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n     /**\n      * @type {number}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n     /**\n      * @type {number}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n     /**\n      * @type {!Long}\n      * @const\n      * @inner\n      */\n     var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var ZERO = fromInt(0);\n\n     /**\n      * Signed zero.\n      * @type {!Long}\n      */\n     Long.ZERO = ZERO;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var UZERO = fromInt(0, true);\n\n     /**\n      * Unsigned zero.\n      * @type {!Long}\n      */\n     Long.UZERO = UZERO;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var ONE = fromInt(1);\n\n     /**\n      * Signed one.\n      * @type {!Long}\n      */\n     Long.ONE = ONE;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var UONE = fromInt(1, true);\n\n     /**\n      * Unsigned one.\n      * @type {!Long}\n      */\n     Long.UONE = UONE;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var NEG_ONE = fromInt(-1);\n\n     /**\n      * Signed negative one.\n      * @type {!Long}\n      */\n     Long.NEG_ONE = NEG_ONE;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var MAX_VALUE = fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n\n     /**\n      * Maximum signed value.\n      * @type {!Long}\n      */\n     Long.MAX_VALUE = MAX_VALUE;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var MAX_UNSIGNED_VALUE = fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n\n     /**\n      * Maximum unsigned value.\n      * @type {!Long}\n      */\n     Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n     /**\n      * @type {!Long}\n      * @inner\n      */\n     var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n\n     /**\n      * Minimum signed value.\n      * @type {!Long}\n      */\n     Long.MIN_VALUE = MIN_VALUE;\n\n     /**\n      * @alias Long.prototype\n      * @inner\n      */\n     var LongPrototype = Long.prototype;\n\n     /**\n      * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n      * @this {!Long}\n      * @returns {number}\n      */\n     LongPrototype.toInt = function toInt() {\n       return this.unsigned ? this.low >>> 0 : this.low;\n     };\n\n     /**\n      * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n      * @this {!Long}\n      * @returns {number}\n      */\n     LongPrototype.toNumber = function toNumber() {\n       if (this.unsigned)\n         return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n       return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n     };\n\n     /**\n      * Converts the Long to a string written in the specified radix.\n      * @this {!Long}\n      * @param {number=} radix Radix (2-36), defaults to 10\n      * @returns {string}\n      * @override\n      * @throws {RangeError} If `radix` is out of range\n      */\n     LongPrototype.toString = function toString(radix) {\n       radix = radix || 10;\n       if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n       if (this.isZero()) return \"0\";\n       if (this.isNegative()) {\n         // Unsigned Longs are never negative\n         if (this.eq(MIN_VALUE)) {\n           // We need to change the Long value before it can be negated, so we remove\n           // the bottom-most digit in this base and then recurse to do the rest.\n           var radixLong = fromNumber(radix),\n             div = this.div(radixLong),\n             rem1 = div.mul(radixLong).sub(this);\n           return div.toString(radix) + rem1.toInt().toString(radix);\n         } else return \"-\" + this.neg().toString(radix);\n       }\n\n       // Do several (6) digits each time through the loop, so as to\n       // minimize the calls to the very expensive emulated div.\n       var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n         rem = this;\n       var result = \"\";\n       while (true) {\n         var remDiv = rem.div(radixToPower),\n           intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n           digits = intval.toString(radix);\n         rem = remDiv;\n         if (rem.isZero()) return digits + result;\n         else {\n           while (digits.length < 6) digits = \"0\" + digits;\n           result = \"\" + digits + result;\n         }\n       }\n     };\n\n     /**\n      * Gets the high 32 bits as a signed integer.\n      * @this {!Long}\n      * @returns {number} Signed high bits\n      */\n     LongPrototype.getHighBits = function getHighBits() {\n       return this.high;\n     };\n\n     /**\n      * Gets the high 32 bits as an unsigned integer.\n      * @this {!Long}\n      * @returns {number} Unsigned high bits\n      */\n     LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n       return this.high >>> 0;\n     };\n\n     /**\n      * Gets the low 32 bits as a signed integer.\n      * @this {!Long}\n      * @returns {number} Signed low bits\n      */\n     LongPrototype.getLowBits = function getLowBits() {\n       return this.low;\n     };\n\n     /**\n      * Gets the low 32 bits as an unsigned integer.\n      * @this {!Long}\n      * @returns {number} Unsigned low bits\n      */\n     LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n       return this.low >>> 0;\n     };\n\n     /**\n      * Gets the number of bits needed to represent the absolute value of this Long.\n      * @this {!Long}\n      * @returns {number}\n      */\n     LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n       if (this.isNegative())\n         // Unsigned Longs are never negative\n         return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n       var val = this.high != 0 ? this.high : this.low;\n       for (var bit = 31; bit > 0; bit--) if ((val & (1 << bit)) != 0) break;\n       return this.high != 0 ? bit + 33 : bit + 1;\n     };\n\n     /**\n      * Tests if this Long can be safely represented as a JavaScript number.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isSafeInteger = function isSafeInteger() {\n       // 2^53-1 is the maximum safe value\n       var top11Bits = this.high >> 21;\n       // [0, 2^53-1]\n       if (!top11Bits) return true;\n       // > 2^53-1\n       if (this.unsigned) return false;\n       // [-2^53, -1] except -2^53\n       return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n     };\n\n     /**\n      * Tests if this Long's value equals zero.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isZero = function isZero() {\n       return this.high === 0 && this.low === 0;\n     };\n\n     /**\n      * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n      * @returns {boolean}\n      */\n     LongPrototype.eqz = LongPrototype.isZero;\n\n     /**\n      * Tests if this Long's value is negative.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isNegative = function isNegative() {\n       return !this.unsigned && this.high < 0;\n     };\n\n     /**\n      * Tests if this Long's value is positive or zero.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isPositive = function isPositive() {\n       return this.unsigned || this.high >= 0;\n     };\n\n     /**\n      * Tests if this Long's value is odd.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isOdd = function isOdd() {\n       return (this.low & 1) === 1;\n     };\n\n     /**\n      * Tests if this Long's value is even.\n      * @this {!Long}\n      * @returns {boolean}\n      */\n     LongPrototype.isEven = function isEven() {\n       return (this.low & 1) === 0;\n     };\n\n     /**\n      * Tests if this Long's value equals the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.equals = function equals(other) {\n       if (!isLong(other)) other = fromValue(other);\n       if (\n         this.unsigned !== other.unsigned &&\n         this.high >>> 31 === 1 &&\n         other.high >>> 31 === 1\n       )\n         return false;\n       return this.high === other.high && this.low === other.low;\n     };\n\n     /**\n      * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.eq = LongPrototype.equals;\n\n     /**\n      * Tests if this Long's value differs from the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.notEquals = function notEquals(other) {\n       return !this.eq(/* validates */ other);\n     };\n\n     /**\n      * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.neq = LongPrototype.notEquals;\n\n     /**\n      * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.ne = LongPrototype.notEquals;\n\n     /**\n      * Tests if this Long's value is less than the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.lessThan = function lessThan(other) {\n       return this.comp(/* validates */ other) < 0;\n     };\n\n     /**\n      * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.lt = LongPrototype.lessThan;\n\n     /**\n      * Tests if this Long's value is less than or equal the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n       return this.comp(/* validates */ other) <= 0;\n     };\n\n     /**\n      * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n     /**\n      * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.le = LongPrototype.lessThanOrEqual;\n\n     /**\n      * Tests if this Long's value is greater than the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.greaterThan = function greaterThan(other) {\n       return this.comp(/* validates */ other) > 0;\n     };\n\n     /**\n      * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.gt = LongPrototype.greaterThan;\n\n     /**\n      * Tests if this Long's value is greater than or equal the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n       return this.comp(/* validates */ other) >= 0;\n     };\n\n     /**\n      * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n     /**\n      * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {boolean}\n      */\n     LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n     /**\n      * Compares this Long's value with the specified's.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n      *  if the given one is greater\n      */\n     LongPrototype.compare = function compare(other) {\n       if (!isLong(other)) other = fromValue(other);\n       if (this.eq(other)) return 0;\n       var thisNeg = this.isNegative(),\n         otherNeg = other.isNegative();\n       if (thisNeg && !otherNeg) return -1;\n       if (!thisNeg && otherNeg) return 1;\n       // At this point the sign bits are the same\n       if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n       // Both are positive if at least one is unsigned\n       return other.high >>> 0 > this.high >>> 0 ||\n         (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n         ? -1\n         : 1;\n     };\n\n     /**\n      * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n      * @function\n      * @param {!Long|number|bigint|string} other Other value\n      * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n      *  if the given one is greater\n      */\n     LongPrototype.comp = LongPrototype.compare;\n\n     /**\n      * Negates this Long's value.\n      * @this {!Long}\n      * @returns {!Long} Negated Long\n      */\n     LongPrototype.negate = function negate() {\n       if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n       return this.not().add(ONE);\n     };\n\n     /**\n      * Negates this Long's value. This is an alias of {@link Long#negate}.\n      * @function\n      * @returns {!Long} Negated Long\n      */\n     LongPrototype.neg = LongPrototype.negate;\n\n     /**\n      * Returns the sum of this and the specified Long.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} addend Addend\n      * @returns {!Long} Sum\n      */\n     LongPrototype.add = function add(addend) {\n       if (!isLong(addend)) addend = fromValue(addend);\n\n       // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n       var a48 = this.high >>> 16;\n       var a32 = this.high & 0xffff;\n       var a16 = this.low >>> 16;\n       var a00 = this.low & 0xffff;\n       var b48 = addend.high >>> 16;\n       var b32 = addend.high & 0xffff;\n       var b16 = addend.low >>> 16;\n       var b00 = addend.low & 0xffff;\n       var c48 = 0,\n         c32 = 0,\n         c16 = 0,\n         c00 = 0;\n       c00 += a00 + b00;\n       c16 += c00 >>> 16;\n       c00 &= 0xffff;\n       c16 += a16 + b16;\n       c32 += c16 >>> 16;\n       c16 &= 0xffff;\n       c32 += a32 + b32;\n       c48 += c32 >>> 16;\n       c32 &= 0xffff;\n       c48 += a48 + b48;\n       c48 &= 0xffff;\n       return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n     };\n\n     /**\n      * Returns the difference of this and the specified Long.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} subtrahend Subtrahend\n      * @returns {!Long} Difference\n      */\n     LongPrototype.subtract = function subtract(subtrahend) {\n       if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n       return this.add(subtrahend.neg());\n     };\n\n     /**\n      * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n      * @function\n      * @param {!Long|number|bigint|string} subtrahend Subtrahend\n      * @returns {!Long} Difference\n      */\n     LongPrototype.sub = LongPrototype.subtract;\n\n     /**\n      * Returns the product of this and the specified Long.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} multiplier Multiplier\n      * @returns {!Long} Product\n      */\n     LongPrototype.multiply = function multiply(multiplier) {\n       if (this.isZero()) return this;\n       if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n\n       // use wasm support if present\n       if (wasm) {\n         var low = wasm[\"mul\"](\n           this.low,\n           this.high,\n           multiplier.low,\n           multiplier.high,\n         );\n         return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n       }\n       if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n       if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n       if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n       if (this.isNegative()) {\n         if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());\n         else return this.neg().mul(multiplier).neg();\n       } else if (multiplier.isNegative())\n         return this.mul(multiplier.neg()).neg();\n\n       // If both longs are small, use float multiplication\n       if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n         return fromNumber(\n           this.toNumber() * multiplier.toNumber(),\n           this.unsigned,\n         );\n\n       // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n       // We can skip products that would overflow.\n\n       var a48 = this.high >>> 16;\n       var a32 = this.high & 0xffff;\n       var a16 = this.low >>> 16;\n       var a00 = this.low & 0xffff;\n       var b48 = multiplier.high >>> 16;\n       var b32 = multiplier.high & 0xffff;\n       var b16 = multiplier.low >>> 16;\n       var b00 = multiplier.low & 0xffff;\n       var c48 = 0,\n         c32 = 0,\n         c16 = 0,\n         c00 = 0;\n       c00 += a00 * b00;\n       c16 += c00 >>> 16;\n       c00 &= 0xffff;\n       c16 += a16 * b00;\n       c32 += c16 >>> 16;\n       c16 &= 0xffff;\n       c16 += a00 * b16;\n       c32 += c16 >>> 16;\n       c16 &= 0xffff;\n       c32 += a32 * b00;\n       c48 += c32 >>> 16;\n       c32 &= 0xffff;\n       c32 += a16 * b16;\n       c48 += c32 >>> 16;\n       c32 &= 0xffff;\n       c32 += a00 * b32;\n       c48 += c32 >>> 16;\n       c32 &= 0xffff;\n       c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n       c48 &= 0xffff;\n       return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n     };\n\n     /**\n      * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n      * @function\n      * @param {!Long|number|bigint|string} multiplier Multiplier\n      * @returns {!Long} Product\n      */\n     LongPrototype.mul = LongPrototype.multiply;\n\n     /**\n      * Returns this Long divided by the specified. The result is signed if this Long is signed or\n      *  unsigned if this Long is unsigned.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} divisor Divisor\n      * @returns {!Long} Quotient\n      */\n     LongPrototype.divide = function divide(divisor) {\n       if (!isLong(divisor)) divisor = fromValue(divisor);\n       if (divisor.isZero()) throw Error(\"division by zero\");\n\n       // use wasm support if present\n       if (wasm) {\n         // guard against signed division overflow: the largest\n         // negative number / -1 would be 1 larger than the largest\n         // positive number, due to two's complement.\n         if (\n           !this.unsigned &&\n           this.high === -0x80000000 &&\n           divisor.low === -1 &&\n           divisor.high === -1\n         ) {\n           // be consistent with non-wasm code path\n           return this;\n         }\n         var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n           this.low,\n           this.high,\n           divisor.low,\n           divisor.high,\n         );\n         return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n       }\n       if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n       var approx, rem, res;\n       if (!this.unsigned) {\n         // This section is only relevant for signed longs and is derived from the\n         // closure library as a whole.\n         if (this.eq(MIN_VALUE)) {\n           if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n             return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n           else if (divisor.eq(MIN_VALUE)) return ONE;\n           else {\n             // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n             var halfThis = this.shr(1);\n             approx = halfThis.div(divisor).shl(1);\n             if (approx.eq(ZERO)) {\n               return divisor.isNegative() ? ONE : NEG_ONE;\n             } else {\n               rem = this.sub(divisor.mul(approx));\n               res = approx.add(rem.div(divisor));\n               return res;\n             }\n           }\n         } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n         if (this.isNegative()) {\n           if (divisor.isNegative()) return this.neg().div(divisor.neg());\n           return this.neg().div(divisor).neg();\n         } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n         res = ZERO;\n       } else {\n         // The algorithm below has not been made for unsigned longs. It's therefore\n         // required to take special care of the MSB prior to running it.\n         if (!divisor.unsigned) divisor = divisor.toUnsigned();\n         if (divisor.gt(this)) return UZERO;\n         if (divisor.gt(this.shru(1)))\n           // 15 >>> 1 = 7 ; with divisor = 8 ; true\n           return UONE;\n         res = UZERO;\n       }\n\n       // Repeat the following until the remainder is less than other:  find a\n       // floating-point that approximates remainder / other *from below*, add this\n       // into the result, and subtract it from the remainder.  It is critical that\n       // the approximate value is less than or equal to the real value so that the\n       // remainder never becomes negative.\n       rem = this;\n       while (rem.gte(divisor)) {\n         // Approximate the result of division. This may be a little greater or\n         // smaller than the actual value.\n         approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n         // We will tweak the approximate result by changing it in the 48-th digit or\n         // the smallest non-fractional digit, whichever is larger.\n         var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n           delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n           // Decrease the approximation until it is smaller than the remainder.  Note\n           // that if it is too large, the product overflows and is negative.\n           approxRes = fromNumber(approx),\n           approxRem = approxRes.mul(divisor);\n         while (approxRem.isNegative() || approxRem.gt(rem)) {\n           approx -= delta;\n           approxRes = fromNumber(approx, this.unsigned);\n           approxRem = approxRes.mul(divisor);\n         }\n\n         // We know the answer can't be zero... and actually, zero would cause\n         // infinite recursion since we would make no progress.\n         if (approxRes.isZero()) approxRes = ONE;\n         res = res.add(approxRes);\n         rem = rem.sub(approxRem);\n       }\n       return res;\n     };\n\n     /**\n      * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n      * @function\n      * @param {!Long|number|bigint|string} divisor Divisor\n      * @returns {!Long} Quotient\n      */\n     LongPrototype.div = LongPrototype.divide;\n\n     /**\n      * Returns this Long modulo the specified.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} divisor Divisor\n      * @returns {!Long} Remainder\n      */\n     LongPrototype.modulo = function modulo(divisor) {\n       if (!isLong(divisor)) divisor = fromValue(divisor);\n\n       // use wasm support if present\n       if (wasm) {\n         var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n           this.low,\n           this.high,\n           divisor.low,\n           divisor.high,\n         );\n         return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n       }\n       return this.sub(this.div(divisor).mul(divisor));\n     };\n\n     /**\n      * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n      * @function\n      * @param {!Long|number|bigint|string} divisor Divisor\n      * @returns {!Long} Remainder\n      */\n     LongPrototype.mod = LongPrototype.modulo;\n\n     /**\n      * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n      * @function\n      * @param {!Long|number|bigint|string} divisor Divisor\n      * @returns {!Long} Remainder\n      */\n     LongPrototype.rem = LongPrototype.modulo;\n\n     /**\n      * Returns the bitwise NOT of this Long.\n      * @this {!Long}\n      * @returns {!Long}\n      */\n     LongPrototype.not = function not() {\n       return fromBits(~this.low, ~this.high, this.unsigned);\n     };\n\n     /**\n      * Returns count leading zeros of this Long.\n      * @this {!Long}\n      * @returns {!number}\n      */\n     LongPrototype.countLeadingZeros = function countLeadingZeros() {\n       return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n     };\n\n     /**\n      * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n      * @function\n      * @param {!Long}\n      * @returns {!number}\n      */\n     LongPrototype.clz = LongPrototype.countLeadingZeros;\n\n     /**\n      * Returns count trailing zeros of this Long.\n      * @this {!Long}\n      * @returns {!number}\n      */\n     LongPrototype.countTrailingZeros = function countTrailingZeros() {\n       return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n     };\n\n     /**\n      * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n      * @function\n      * @param {!Long}\n      * @returns {!number}\n      */\n     LongPrototype.ctz = LongPrototype.countTrailingZeros;\n\n     /**\n      * Returns the bitwise AND of this Long and the specified.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other Long\n      * @returns {!Long}\n      */\n     LongPrototype.and = function and(other) {\n       if (!isLong(other)) other = fromValue(other);\n       return fromBits(\n         this.low & other.low,\n         this.high & other.high,\n         this.unsigned,\n       );\n     };\n\n     /**\n      * Returns the bitwise OR of this Long and the specified.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other Long\n      * @returns {!Long}\n      */\n     LongPrototype.or = function or(other) {\n       if (!isLong(other)) other = fromValue(other);\n       return fromBits(\n         this.low | other.low,\n         this.high | other.high,\n         this.unsigned,\n       );\n     };\n\n     /**\n      * Returns the bitwise XOR of this Long and the given one.\n      * @this {!Long}\n      * @param {!Long|number|bigint|string} other Other Long\n      * @returns {!Long}\n      */\n     LongPrototype.xor = function xor(other) {\n       if (!isLong(other)) other = fromValue(other);\n       return fromBits(\n         this.low ^ other.low,\n         this.high ^ other.high,\n         this.unsigned,\n       );\n     };\n\n     /**\n      * Returns this Long with bits shifted to the left by the given amount.\n      * @this {!Long}\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shiftLeft = function shiftLeft(numBits) {\n       if (isLong(numBits)) numBits = numBits.toInt();\n       if ((numBits &= 63) === 0) return this;\n       else if (numBits < 32)\n         return fromBits(\n           this.low << numBits,\n           (this.high << numBits) | (this.low >>> (32 - numBits)),\n           this.unsigned,\n         );\n       else return fromBits(0, this.low << (numBits - 32), this.unsigned);\n     };\n\n     /**\n      * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shl = LongPrototype.shiftLeft;\n\n     /**\n      * Returns this Long with bits arithmetically shifted to the right by the given amount.\n      * @this {!Long}\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shiftRight = function shiftRight(numBits) {\n       if (isLong(numBits)) numBits = numBits.toInt();\n       if ((numBits &= 63) === 0) return this;\n       else if (numBits < 32)\n         return fromBits(\n           (this.low >>> numBits) | (this.high << (32 - numBits)),\n           this.high >> numBits,\n           this.unsigned,\n         );\n       else\n         return fromBits(\n           this.high >> (numBits - 32),\n           this.high >= 0 ? 0 : -1,\n           this.unsigned,\n         );\n     };\n\n     /**\n      * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shr = LongPrototype.shiftRight;\n\n     /**\n      * Returns this Long with bits logically shifted to the right by the given amount.\n      * @this {!Long}\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n       if (isLong(numBits)) numBits = numBits.toInt();\n       if ((numBits &= 63) === 0) return this;\n       if (numBits < 32)\n         return fromBits(\n           (this.low >>> numBits) | (this.high << (32 - numBits)),\n           this.high >>> numBits,\n           this.unsigned,\n         );\n       if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n       return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n     };\n\n     /**\n      * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n     /**\n      * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Shifted Long\n      */\n     LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n     /**\n      * Returns this Long with bits rotated to the left by the given amount.\n      * @this {!Long}\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Rotated Long\n      */\n     LongPrototype.rotateLeft = function rotateLeft(numBits) {\n       var b;\n       if (isLong(numBits)) numBits = numBits.toInt();\n       if ((numBits &= 63) === 0) return this;\n       if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n       if (numBits < 32) {\n         b = 32 - numBits;\n         return fromBits(\n           (this.low << numBits) | (this.high >>> b),\n           (this.high << numBits) | (this.low >>> b),\n           this.unsigned,\n         );\n       }\n       numBits -= 32;\n       b = 32 - numBits;\n       return fromBits(\n         (this.high << numBits) | (this.low >>> b),\n         (this.low << numBits) | (this.high >>> b),\n         this.unsigned,\n       );\n     };\n     /**\n      * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Rotated Long\n      */\n     LongPrototype.rotl = LongPrototype.rotateLeft;\n\n     /**\n      * Returns this Long with bits rotated to the right by the given amount.\n      * @this {!Long}\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Rotated Long\n      */\n     LongPrototype.rotateRight = function rotateRight(numBits) {\n       var b;\n       if (isLong(numBits)) numBits = numBits.toInt();\n       if ((numBits &= 63) === 0) return this;\n       if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n       if (numBits < 32) {\n         b = 32 - numBits;\n         return fromBits(\n           (this.high << b) | (this.low >>> numBits),\n           (this.low << b) | (this.high >>> numBits),\n           this.unsigned,\n         );\n       }\n       numBits -= 32;\n       b = 32 - numBits;\n       return fromBits(\n         (this.low << b) | (this.high >>> numBits),\n         (this.high << b) | (this.low >>> numBits),\n         this.unsigned,\n       );\n     };\n     /**\n      * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n      * @function\n      * @param {number|!Long} numBits Number of bits\n      * @returns {!Long} Rotated Long\n      */\n     LongPrototype.rotr = LongPrototype.rotateRight;\n\n     /**\n      * Converts this Long to signed.\n      * @this {!Long}\n      * @returns {!Long} Signed long\n      */\n     LongPrototype.toSigned = function toSigned() {\n       if (!this.unsigned) return this;\n       return fromBits(this.low, this.high, false);\n     };\n\n     /**\n      * Converts this Long to unsigned.\n      * @this {!Long}\n      * @returns {!Long} Unsigned long\n      */\n     LongPrototype.toUnsigned = function toUnsigned() {\n       if (this.unsigned) return this;\n       return fromBits(this.low, this.high, true);\n     };\n\n     /**\n      * Converts this Long to its byte representation.\n      * @param {boolean=} le Whether little or big endian, defaults to big endian\n      * @this {!Long}\n      * @returns {!Array.<number>} Byte representation\n      */\n     LongPrototype.toBytes = function toBytes(le) {\n       return le ? this.toBytesLE() : this.toBytesBE();\n     };\n\n     /**\n      * Converts this Long to its little endian byte representation.\n      * @this {!Long}\n      * @returns {!Array.<number>} Little endian byte representation\n      */\n     LongPrototype.toBytesLE = function toBytesLE() {\n       var hi = this.high,\n         lo = this.low;\n       return [\n         lo & 0xff,\n         (lo >>> 8) & 0xff,\n         (lo >>> 16) & 0xff,\n         lo >>> 24,\n         hi & 0xff,\n         (hi >>> 8) & 0xff,\n         (hi >>> 16) & 0xff,\n         hi >>> 24,\n       ];\n     };\n\n     /**\n      * Converts this Long to its big endian byte representation.\n      * @this {!Long}\n      * @returns {!Array.<number>} Big endian byte representation\n      */\n     LongPrototype.toBytesBE = function toBytesBE() {\n       var hi = this.high,\n         lo = this.low;\n       return [\n         hi >>> 24,\n         (hi >>> 16) & 0xff,\n         (hi >>> 8) & 0xff,\n         hi & 0xff,\n         lo >>> 24,\n         (lo >>> 16) & 0xff,\n         (lo >>> 8) & 0xff,\n         lo & 0xff,\n       ];\n     };\n\n     /**\n      * Creates a Long from its byte representation.\n      * @param {!Array.<number>} bytes Byte representation\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @param {boolean=} le Whether little or big endian, defaults to big endian\n      * @returns {Long} The corresponding Long value\n      */\n     Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n       return le\n         ? Long.fromBytesLE(bytes, unsigned)\n         : Long.fromBytesBE(bytes, unsigned);\n     };\n\n     /**\n      * Creates a Long from its little endian byte representation.\n      * @param {!Array.<number>} bytes Little endian byte representation\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {Long} The corresponding Long value\n      */\n     Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n       return new Long(\n         bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24),\n         bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24),\n         unsigned,\n       );\n     };\n\n     /**\n      * Creates a Long from its big endian byte representation.\n      * @param {!Array.<number>} bytes Big endian byte representation\n      * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n      * @returns {Long} The corresponding Long value\n      */\n     Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n       return new Long(\n         (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7],\n         (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3],\n         unsigned,\n       );\n     };\n\n     // Support conversion to/from BigInt where available\n     if (typeof BigInt === \"function\") {\n       /**\n        * Returns a Long representing the given big integer.\n        * @function\n        * @param {number} value The big integer value\n        * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n        * @returns {!Long} The corresponding Long value\n        */\n       Long.fromBigInt = function fromBigInt(value, unsigned) {\n         var lowBits = Number(BigInt.asIntN(32, value));\n         var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n         return fromBits(lowBits, highBits, unsigned);\n       };\n\n       // Override\n       Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n         if (typeof value === \"bigint\") return Long.fromBigInt(value, unsigned);\n         return fromValue(value, unsigned);\n       };\n\n       /**\n        * Converts the Long to its big integer representation.\n        * @this {!Long}\n        * @returns {bigint}\n        */\n       LongPrototype.toBigInt = function toBigInt() {\n         var lowBigInt = BigInt(this.low >>> 0);\n         var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n         return (highBigInt << BigInt(32)) | lowBigInt;\n       };\n     }\n     var _default = (_exports.default = Long);\n   },\n );\n\n\n// #endregion ORIGINAL CODE\n\n_cjsExports = module.exports;\n\n\n}, {});\nexport { _cjsExports as default };\nexport { __cjsMetaURL }\n"]}