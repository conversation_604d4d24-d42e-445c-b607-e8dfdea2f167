{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/common/components/list/List.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "Enum", "EventHandler", "instantiate", "<PERSON><PERSON><PERSON><PERSON>", "Layout", "Node", "NodePool", "Prefab", "ScrollView", "Size", "tween", "UITransform", "Vec3", "Widget", "DEV", "ListItem", "ccclass", "property", "disallowMultiple", "menu", "executionOrder", "requireComponent", "TemplateType", "SlideType", "SelectedType", "List", "type", "tooltip", "visible", "templateType", "NODE", "PREFAB", "range", "slide", "_slideMode", "PAGE", "val", "slideMode", "NORMAL", "cyclic", "virtual", "lackCenter", "lackSlide", "selectedMode", "NONE", "SINGLE", "serializable", "_selectedId", "_lastSelectedId", "multSelected", "_forceUpdate", "_align", "_horizontalDir", "_verticalDir", "_startAxis", "_alignCalcType", "content", "_contentUt", "firstListId", "displayItemNum", "_updateDone", "_updateCounter", "_actualNumItems", "_cyclicNum", "_cyclicPos1", "_cyclicPos2", "_inited", "_scrollView", "_layout", "_resizeMode", "_topGap", "_rightGap", "_bottomGap", "_leftGap", "_columnGap", "_lineGap", "_colLineNum", "_lastDisplayData", "displayData", "_pool", "_itemTmp", "_itemTmpUt", "_needUpdateWidget", "_itemSize", "_sizeType", "_customSize", "frameCount", "_aniDelRuning", "_aniDelCB", "_aniDelItem", "_aniDelBeforePos", "_aniDelBeforeScale", "viewTop", "viewRight", "viewBottom", "viewLeft", "_doneAfterUpdate", "elasticTop", "elasticRight", "elasticBottom", "elasticLeft", "scrollToListId", "adhering", "_ad<PERSON><PERSON><PERSON><PERSON>", "nearestListId", "curPage<PERSON>um", "_beganPos", "_scrollPos", "_curScrollIsTouch", "_scrollToListId", "_scrollToEndTime", "_scrollToSo", "_lack", "_allItemSize", "_allItemSizeNoEdge", "_scrollItem", "_thisNodeUt", "_virtual", "_numItems", "_onScrolling", "updateRate", "_updateRate", "selectedId", "t", "item", "repeatEventSingle", "getItemByListId", "listItem", "getComponent", "selected", "lastItem", "selectedEvent", "emitEvents", "MULT", "bool", "sub", "indexOf", "push", "splice", "numItems", "checkInited", "console", "error", "_resizeContent", "frameByFrameRenderNum", "layout", "enabled", "_delRedundantItem", "len", "n", "_createOrUpdateItem2", "scrollView", "onLoad", "_init", "onDestroy", "destroy", "tmpNode", "clear", "onEnable", "_registerEvent", "position", "scale", "onDisable", "_unregisterEvent", "node", "on", "EventType", "TOUCH_START", "_onTouchStart", "_onTouchUp", "TOUCH_CANCEL", "_onTouchCancelled", "_onScrollBegan", "_onScrollEnded", "SIZE_CHANGED", "_onSizeChanged", "off", "name", "resizeMode", "startAxis", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "spacingX", "spacingY", "verticalDirection", "horizontalDirection", "setTemplateItem", "tmpPrefab", "ADHERING", "inertia", "_onMouseWheel", "_processAutoScrolling", "bind", "_startBounceBackIfNeeded", "Type", "HORIZONTAL", "HorizontalDirection", "LEFT_TO_RIGHT", "RIGHT_TO_LEFT", "VERTICAL", "VerticalDirection", "TOP_TO_BOTTOM", "BOTTOM_TO_TOP", "GRID", "AxisDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dt", "OUT_OF_BOUNDARY_BREAKING_FACTOR", "EPSILON", "ZERO", "quintEaseOut", "time", "sv", "isAutoScrollBrake", "brakingFactor", "percentage", "Math", "min", "clonedAutoScrollTargetDelta", "clone", "multiplyScalar", "clonedAutoScrollStartPosition", "add", "reachedEnd", "abs", "fireEvent", "SCROLL_ENG_WITH_THRESHOLD", "brakeOffsetPosition", "subtract", "set", "move<PERSON><PERSON><PERSON>", "outOfBoundary", "equals", "deltaMove", "SCROLLING", "SCROLL_ENDED", "ResizeMode", "CHILDREN", "cellSize", "itemUt", "width", "height", "com", "remove", "trimW", "floor", "trimH", "printLog", "result", "fixed", "_getFixedSize", "count", "lineNum", "ceil", "colNum", "totalSize", "spacing", "_cyclicAllItemSize", "_cycilcAllItemSizeNoEdge", "slideOffset", "targetWH", "ev", "scrollPos", "getPosition", "y", "x", "addVal", "contentPos", "z", "setPosition", "isAutoScrolling", "_calcViewPos", "vTop", "vRight", "vBottom", "vLeft", "itemPos", "curId", "endId", "breakFor", "_calcItemPos", "right", "left", "length", "bottom", "top", "ww", "hh", "id", "haveDataChange", "sort", "a", "b", "c", "_createOrUpdateItem", "_calcNearestItem", "itemX", "itemY", "cs", "offset", "anchorX", "anchorY", "colLine", "_calcExistItemPos", "ut", "pos", "data", "getItemPos", "listId", "parseInt", "to", "start", "adhere", "_page<PERSON><PERSON><PERSON>", "captureListeners", "isMe", "target", "itemNode", "_listId", "parent", "simulate", "_onItemAdaptive", "updateAll", "unschedule", "scrollTo", "max", "Date", "getTime", "curPos", "dis", "pageDistance", "canSkip", "timeInSecond", "prePage", "nextPage", "update", "canGet", "size", "get", "setContentSize", "_resetItemSize", "<PERSON><PERSON><PERSON><PERSON>", "widget", "updateAlignment", "setSiblingIndex", "children", "list", "renderEvent", "_updateListItem", "_updateItemPos", "listIdOrItem", "isNaN", "setMultSelected", "args", "Array", "isArray", "getMultSelected", "hasMultSelected", "updateItem", "_getOutsideItem", "find", "d", "arr", "<PERSON><PERSON><PERSON>d", "put", "m", "_delSingleItem", "removeFromParent", "aniDelItem", "callFunc", "aniType", "warn", "curLastId", "resetSelectedId", "showAni", "newId", "newData", "newCustomSize", "idNumber", "sec", "twe", "haveCB", "posData", "call", "overStress", "updateLayout", "targetX", "targetY", "viewPos", "comparePos", "runScroll", "scrollToOffset", "scheduleOnce", "center", "skipPage", "pageNum", "pageChangeEvent", "calcCustomSize", "temp", "Object", "keys"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAcC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACzLC,MAAAA,G,UAAAA,G;;AACFC,MAAAA,Q;;;;;;;AATP;AACA;AACA;AACA;AACA;AACA;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,gBAArB;AAAuCC,QAAAA,IAAvC;AAA6CC,QAAAA,cAA7C;AAA6DC,QAAAA;AAA7D,O,GAAkF1B,U;;;;AAKnF2B,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAKAC,MAAAA,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;QAAAA,S;;AAMAC,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;yBAYgBC,I,WALpBP,gBAAgB,E,UAChBC,IAAI,CAAC,MAAD,C,UACJE,gBAAgB,CAACb,UAAD,C,UAEhBY,cAAc,CAAC,CAAC,IAAF,C,UAGVH,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE1B,IAAI,CAACsB,YAAD,CAAZ;AAA4BK,QAAAA,OAAO,EAAEb,GAAG,IAAI;AAA5C,OAAD,C,UAGRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAErB,IADA;AAENsB,QAAAA,OAAO,EAAEb,GAAG,IAAI,QAFV;;AAGNc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,IAAqBP,YAAY,CAACQ,IAAzC;AAAgD;;AAHtD,OAAD,C,UAORb,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEnB,MADA;AAENoB,QAAAA,OAAO,EAAEb,GAAG,IAAI,QAFV;;AAGNc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,IAAqBP,YAAY,CAACS,MAAzC;AAAkD;;AAHxD,OAAD,C,UAORd,QAAQ,CAAC,EAAD,C,UAERA,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE1B,IAAI,CAACuB,SAAD,CADJ;AAENI,QAAAA,OAAO,EAAEb,GAAG,IAAI;AAFV,OAAD,C,WAWRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE7B,OADA;AAENmC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,EAAP,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,IAAI,QAHV;AAINmB,QAAAA,KAAK,EAAE,IAJD;;AAKNL,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKM,UAAL,IAAmBX,SAAS,CAACY,IAApC;AAA2C;;AALjD,OAAD,C,WASRlB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,IAAI,QAFV;;AAGNc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKM,UAAL,IAAmBX,SAAS,CAACY,IAApC;AAA2C;;AAHjD,OAAD,C,WAORlB,QAAQ,CAAC,EAAD,C,WAERA,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE9B,SADA;AAEN+B,QAAAA,OAAO,EAAEb,GAAG,IAAI;AAFV,OAAD,C,WAeRG,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,IAAI,SADV;;AAENc,QAAAA,OAAO,GAAG;AACN,cAAIQ,GAAY;AAAG;AAAoB,eAAKC,SAAL,IAAkBd,SAAS,CAACe,MAAnE;AACA,cAAI,CAACF,GAAL,EACI,KAAKG,MAAL,GAAc,KAAd;AACJ,iBAAOH,GAAP;AACH;;AAPK,OAAD,C,WAWRnB,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,IAAI,2CADV;;AAENc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKY,OAAZ;AAAsB;;AAF5B,OAAD,C,WAMRvB,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,IAAI,2BADV;;AAENc,QAAAA,OAAO,GAAG;AACN,cAAIQ,GAAY,GAAG,KAAKI,OAAL,IAAgB,CAAC,KAAKC,UAAzC;AACA,cAAI,CAACL,GAAL,EACI,KAAKM,SAAL,GAAiB,KAAjB;AACJ,iBAAON,GAAP;AACH;;AAPK,OAAD,C,WAWRnB,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE5B;AAAR,OAAD,C,WAERmB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE5B,SADA;AAENkC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,IAAI,sBAHV;AAINmB,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAeRhB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE5B,SADA;AAENkC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,IAAI,+BAHV;AAINmB,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAQRhB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,IAAI;AAFV,OAAD,C,WAMRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE1B,IAAI,CAACwB,YAAD,CADJ;AAENG,QAAAA,OAAO,EAAEb,GAAG,IAAI;AAFV,OAAD,C,WAMRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,IAAI,QAFV;;AAGNc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKe,YAAL,GAAoBnB,YAAY,CAACoB,IAAxC;AAA+C;;AAHrD,OAAD,C,WAMR3B,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,IAAI,YADV;;AAENc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKe,YAAL,IAAqBnB,YAAY,CAACqB,MAAzC;AAAkD;;AAFxD,OAAD,C,WAoFR5B,QAAQ,CAAC;AACN6B,QAAAA,YAAY,EAAE;AADR,OAAD,C,EAlOZ9B,O,gFAAD,MAMqBS,IANrB,SAMkC1B,SANlC,CAM4C;AAAA;AAAA;;AACxC;AADwC;;AAIxC;AAJwC;;AAWxC;AAXwC;;AAkBxC;AAlBwC;;AA+BxC;AA/BwC;;AAwCxC;AAxCwC;;AA+CxC;AA/CwC;;AAgExC;AAhEwC;;AA2ExC;AA3EwC;;AAiFxC;AAjFwC;;AA4FxC;AA5FwC;;AA6GxC;AA7GwC;;AAqHxC;AArHwC;;AA2HxC;AA3HwC;;AAiIxC;AAjIwC;;AAAA;;AA8IxC;AA9IwC,eA+IhCgD,WA/IgC,GA+IV,CAAC,CA/IS;AAAA,eAgJhCC,eAhJgC;AAAA,eAiJhCC,YAjJgC;AAAA,eA2MhCC,YA3MgC,GA2MR,KA3MQ;AAAA,eA4MhCC,MA5MgC;AAAA,eA6MhCC,cA7MgC;AAAA,eA8MhCC,YA9MgC;AAAA,eA+MhCC,UA/MgC;AAAA,eAgNhCC,cAhNgC;AAAA,eAiNjCC,OAjNiC;AAAA,eAkNhCC,UAlNgC;AAAA,eAmNhCC,WAnNgC;AAAA,eAoNjCC,cApNiC;AAAA,eAqNhCC,WArNgC,GAqNT,IArNS;AAAA,eAsNhCC,cAtNgC;AAAA,eAuNjCC,eAvNiC;AAAA,eAwNhCC,UAxNgC;AAAA,eAyNhCC,WAzNgC;AAAA,eA0NhCC,WA1NgC;;AA2NxC;AA3NwC;;AAAA,eAqRhCC,OArRgC,GAqRb,KArRa;AAAA,eAsRhCC,WAtRgC;AAAA,eA0RhCC,OA1RgC;AAAA,eA2RhCC,WA3RgC;AAAA,eA4RhCC,OA5RgC;AAAA,eA6RhCC,SA7RgC;AAAA,eA8RhCC,UA9RgC;AAAA,eA+RhCC,QA/RgC;AAAA,eAiShCC,UAjSgC;AAAA,eAkShCC,QAlSgC;AAAA,eAmShCC,WAnSgC;AAAA,eAqShCC,gBArSgC;AAAA,eAsSjCC,WAtSiC;AAAA,eAuShCC,KAvSgC;AAAA,eAyShCC,QAzSgC;AAAA,eA0ShCC,UA1SgC;AAAA,eA2ShCC,iBA3SgC,GA2SH,KA3SG;AAAA,eA4ShCC,SA5SgC;AAAA,eA6ShCC,SA7SgC;AAAA,eA+SjCC,WA/SiC;AAAA,eAiThCC,UAjTgC;AAAA,eAkThCC,aAlTgC,GAkTP,KAlTO;AAAA,eAmThCC,SAnTgC;AAAA,eAoThCC,WApTgC;AAAA,eAqThCC,gBArTgC;AAAA,eAsThCC,kBAtTgC;AAAA,eAuThCC,OAvTgC;AAAA,eAwThCC,SAxTgC;AAAA,eAyThCC,UAzTgC;AAAA,eA0ThCC,QA1TgC;AAAA,eA4ThCC,gBA5TgC,GA4TJ,KA5TI;AAAA,eA8ThCC,UA9TgC;AAAA,eA+ThCC,YA/TgC;AAAA,eAgUhCC,aAhUgC;AAAA,eAiUhCC,WAjUgC;AAAA,eAmUhCC,cAnUgC;AAAA,eAqUhCC,QArUgC,GAqUZ,KArUY;AAAA,eAuUhCC,gBAvUgC,GAuUJ,KAvUI;AAAA,eAwUhCC,aAxUgC;AAAA,eA0UjCC,UA1UiC,GA0UZ,CA1UY;AAAA,eA2UhCC,SA3UgC;AAAA,eA4UhCC,UA5UgC;AAAA,eA6UhCC,iBA7UgC;AA6UL;AA7UK,eA+UhCC,eA/UgC;AAAA,eAgVhCC,gBAhVgC;AAAA,eAiVhCC,WAjVgC;AAAA,eAmVhCC,KAnVgC;AAAA,eAoVhCC,YApVgC;AAAA,eAqVhCC,kBArVgC;AAAA,eAuVhCC,WAvVgC;AAuVf;AAvVe,eAyVhCC,WAzVgC;AAAA;;AAyB3B,YAAT/E,SAAS,CAACD,GAAD,EAAiB;AAC1B,eAAKF,UAAL,GAAkBE,GAAlB;AACH;;AACY,YAATC,SAAS,GAAG;AACZ,iBAAO,KAAKH,UAAZ;AACH;;AAwBU,YAAPM,OAAO,CAACJ,GAAD,EAAe;AACtB,cAAIA,GAAG,IAAI,IAAX,EACI,KAAKiF,QAAL,GAAgBjF,GAAhB;;AACJ,cAAI,CAACtB,GAAD,IAAQ,KAAKwG,SAAL,IAAkB,CAA9B,EAAiC;AAC7B,iBAAKC,YAAL;AACH;AACJ;;AACU,YAAP/E,OAAO,GAAG;AACV,iBAAO,KAAK6E,QAAZ;AACH;;AAsCa,YAAVG,UAAU,CAACpF,GAAD,EAAc;AACxB,cAAIA,GAAG,IAAI,CAAP,IAAYA,GAAG,IAAI,CAAvB,EAA0B;AACtB,iBAAKqF,WAAL,GAAmBrF,GAAnB;AACH;AACJ;;AACa,YAAVoF,UAAU,GAAG;AACb,iBAAO,KAAKC,WAAZ;AACH;;AAsCa,YAAVC,UAAU,CAACtF,GAAD,EAAc;AACxB,cAAIuF,CAAM,GAAG,IAAb;AACA,cAAIC,IAAJ;;AACA,kBAAQD,CAAC,CAAChF,YAAV;AACI,iBAAKnB,YAAY,CAACqB,MAAlB;AAA0B;AACtB,oBAAI,CAAC8E,CAAC,CAACE,iBAAH,IAAwBzF,GAAG,IAAIuF,CAAC,CAAC5E,WAArC,EACI;AACJ6E,gBAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkB1F,GAAlB,CAAP,CAHsB,CAItB;AACA;;AACA,oBAAI2F,QAAJ;AACA,oBAAIJ,CAAC,CAAC5E,WAAF,IAAiB,CAArB,EACI4E,CAAC,CAAC3E,eAAF,GAAoB2E,CAAC,CAAC5E,WAAtB,CADJ,KAEK;AACD4E,kBAAAA,CAAC,CAAC3E,eAAF,GAAoB,IAApB;AACJ2E,gBAAAA,CAAC,CAAC5E,WAAF,GAAgBX,GAAhB;;AACA,oBAAIwF,IAAJ,EAAU;AACNG,kBAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,2CAAX;AACAD,kBAAAA,QAAQ,CAACE,QAAT,GAAoB,IAApB;AACH;;AACD,oBAAIN,CAAC,CAAC3E,eAAF,IAAqB,CAArB,IAA0B2E,CAAC,CAAC3E,eAAF,IAAqB2E,CAAC,CAAC5E,WAArD,EAAkE;AAC9D,sBAAImF,QAAa,GAAGP,CAAC,CAACG,eAAF,CAAkBH,CAAC,CAAC3E,eAApB,CAApB;;AACA,sBAAIkF,QAAJ,EAAc;AACVA,oBAAAA,QAAQ,CAACF,YAAT;AAAA;AAAA,8CAAgCC,QAAhC,GAA2C,KAA3C;AACH;AACJ;;AACD,oBAAIN,CAAC,CAACQ,aAAN,EAAqB;AACjBlI,kBAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACQ,aAAH,CAAxB,EAA2CP,IAA3C,EAAiDxF,GAAG,GAAG,KAAK0B,eAA5D,EAA6E6D,CAAC,CAAC3E,eAAF,IAAqB,IAArB,GAA4B,IAA5B,GAAoC2E,CAAC,CAAC3E,eAAF,GAAoB,KAAKc,eAA1I;AACH;;AACD;AACH;;AACD,iBAAKtC,YAAY,CAAC6G,IAAlB;AAAwB;AACpBT,gBAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkB1F,GAAlB,CAAP;AACA,oBAAI,CAACwF,IAAL,EACI;AACJ,oBAAIG,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,yCAAf;AACA,oBAAIL,CAAC,CAAC5E,WAAF,IAAiB,CAArB,EACI4E,CAAC,CAAC3E,eAAF,GAAoB2E,CAAC,CAAC5E,WAAtB;AACJ4E,gBAAAA,CAAC,CAAC5E,WAAF,GAAgBX,GAAhB;AACA,oBAAIkG,IAAa,GAAG,CAACP,QAAQ,CAACE,QAA9B;AACAF,gBAAAA,QAAQ,CAACE,QAAT,GAAoBK,IAApB;AACA,oBAAIC,GAAW,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuBpG,GAAvB,CAAlB;;AACA,oBAAIkG,IAAI,IAAIC,GAAG,GAAG,CAAlB,EAAqB;AACjBZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAewF,IAAf,CAAoBrG,GAApB;AACH,iBAFD,MAEO,IAAI,CAACkG,IAAD,IAASC,GAAG,IAAI,CAApB,EAAuB;AAC1BZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH;;AACD,oBAAIZ,CAAC,CAACQ,aAAN,EAAqB;AACjBlI,kBAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACQ,aAAH,CAAxB,EAA2CP,IAA3C,EAAiDxF,GAAG,GAAG,KAAK0B,eAA5D,EAA6E6D,CAAC,CAAC3E,eAAF,IAAqB,IAArB,GAA4B,IAA5B,GAAoC2E,CAAC,CAAC3E,eAAF,GAAoB,KAAKc,eAA1I,EAA4JwE,IAA5J;AACH;;AACD;AACH;AAhDL;AAkDH;;AACa,YAAVZ,UAAU,GAAG;AACb,iBAAO,KAAK3E,WAAZ;AACH;;AAsBW,YAAR4F,QAAQ,CAACvG,GAAD,EAAc;AACtB,cAAIuF,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,CAAc,KAAd,CAAL,EACI;;AACJ,cAAIxG,GAAG,IAAI,IAAP,IAAeA,GAAG,GAAG,CAAzB,EAA4B;AACxByG,YAAAA,OAAO,CAACC,KAAR,CAAc,0BAAd,EAA0C1G,GAA1C;AACA;AACH;;AACDuF,UAAAA,CAAC,CAAC7D,eAAF,GAAoB6D,CAAC,CAACL,SAAF,GAAclF,GAAlC;AACAuF,UAAAA,CAAC,CAACzE,YAAF,GAAiB,IAAjB;;AAEA,cAAIyE,CAAC,CAACN,QAAN,EAAgB;AACZM,YAAAA,CAAC,CAACoB,cAAF;;AACA,gBAAIpB,CAAC,CAACpF,MAAN,EAAc;AACVoF,cAAAA,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC5D,UAAF,GAAe4D,CAAC,CAACL,SAA/B;AACH;;AACDK,YAAAA,CAAC,CAACJ,YAAF;;AACA,gBAAI,CAACI,CAAC,CAACqB,qBAAH,IAA4BrB,CAAC,CAACtF,SAAF,IAAed,SAAS,CAACY,IAAzD,EACIwF,CAAC,CAAClB,UAAF,GAAekB,CAAC,CAACnB,aAAjB;AACP,WARD,MAQO;AACH,gBAAImB,CAAC,CAACpF,MAAN,EAAc;AACVoF,cAAAA,CAAC,CAACoB,cAAF;;AACApB,cAAAA,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC5D,UAAF,GAAe4D,CAAC,CAACL,SAA/B;AACH;;AACD,gBAAI2B,MAAc,GAAGtB,CAAC,CAACnE,OAAF,CAAUwE,YAAV,CAAuB5H,MAAvB,CAArB;;AACA,gBAAI6I,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,OAAP,GAAiB,IAAjB;AACH;;AACDvB,YAAAA,CAAC,CAACwB,iBAAF;;AAEAxB,YAAAA,CAAC,CAACjE,WAAF,GAAgB,CAAhB;;AACA,gBAAIiE,CAAC,CAACqB,qBAAF,GAA0B,CAA9B,EAAiC;AAC7B;AACA,kBAAII,GAAW,GAAGzB,CAAC,CAACqB,qBAAF,GAA0BrB,CAAC,CAACL,SAA5B,GAAwCK,CAAC,CAACL,SAA1C,GAAsDK,CAAC,CAACqB,qBAA1E;;AACA,mBAAK,IAAIK,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,GAA5B,EAAiCC,CAAC,EAAlC,EAAsC;AAClC1B,gBAAAA,CAAC,CAAC2B,oBAAF,CAAuBD,CAAvB;AACH;;AACD,kBAAI1B,CAAC,CAACqB,qBAAF,GAA0BrB,CAAC,CAACL,SAAhC,EAA2C;AACvCK,gBAAAA,CAAC,CAAC9D,cAAF,GAAmB8D,CAAC,CAACqB,qBAArB;AACArB,gBAAAA,CAAC,CAAC/D,WAAF,GAAgB,KAAhB;AACH;AACJ,aAVD,MAUO;AACH,mBAAK,IAAIyF,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAG1B,CAAC,CAACL,SAA9B,EAAyC+B,CAAC,EAA1C,EAA8C;AAC1C1B,gBAAAA,CAAC,CAAC2B,oBAAF,CAAuBD,CAAvB;AACH;;AACD1B,cAAAA,CAAC,CAAChE,cAAF,GAAmBgE,CAAC,CAACL,SAArB;AACH;AACJ;AACJ;;AACW,YAARqB,QAAQ,GAAG;AACX,iBAAO,KAAK7E,eAAZ;AACH;;AAIa,YAAVyF,UAAU,GAAG;AACb,iBAAO,KAAKpF,WAAZ;AACH;;AAkED;AAEAqF,QAAAA,MAAM,GAAG;AACL,eAAKC,KAAL;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAI/B,CAAM,GAAG,IAAb;AACA,cAAIxH,OAAO,CAACwH,CAAC,CAAC3C,QAAH,CAAX,EACI2C,CAAC,CAAC3C,QAAF,CAAW2E,OAAX;AACJ,cAAIxJ,OAAO,CAACwH,CAAC,CAACiC,OAAH,CAAX,EACIjC,CAAC,CAACiC,OAAF,CAAUD,OAAV;AACJhC,UAAAA,CAAC,CAAC5C,KAAF,IAAW4C,CAAC,CAAC5C,KAAF,CAAQ8E,KAAR,EAAX;AACH;;AAEDC,QAAAA,QAAQ,GAAG;AACP;AACA,eAAKC,cAAL;;AACA,eAAKN,KAAL,GAHO,CAIP;;;AACA,cAAI,KAAKlE,aAAT,EAAwB;AACpB,iBAAKA,aAAL,GAAqB,KAArB;;AACA,gBAAI,KAAKE,WAAT,EAAsB;AAClB,kBAAI,KAAKC,gBAAT,EAA2B;AACvB,qBAAKD,WAAL,CAAiBuE,QAAjB,GAA4B,KAAKtE,gBAAjC;AACA,uBAAO,KAAKA,gBAAZ;AACH;;AACD,kBAAI,KAAKC,kBAAT,EAA6B;AACzB,qBAAKF,WAAL,CAAiBwE,KAAjB,GAAyB,KAAKtE,kBAA9B;AACA,uBAAO,KAAKA,kBAAZ;AACH;;AACD,qBAAO,KAAKF,WAAZ;AACH;;AACD,gBAAI,KAAKD,SAAT,EAAoB;AAChB,mBAAKA,SAAL;;AACA,qBAAO,KAAKA,SAAZ;AACH;AACJ;AACJ;;AAED0E,QAAAA,SAAS,GAAG;AACR;AACA,eAAKC,gBAAL;AACH,SAtYuC,CAuYxC;;;AACAJ,QAAAA,cAAc,GAAG;AACb,cAAIpC,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeC,WAAzB,EAAsC5C,CAAC,CAAC6C,aAAxC,EAAuD7C,CAAvD;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,UAAV,EAAsB1C,CAAC,CAAC8C,UAAxB,EAAoC9C,CAApC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeI,YAAzB,EAAuC/C,CAAC,CAACgD,iBAAzC,EAA4DhD,CAA5D;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,cAAV,EAA0B1C,CAAC,CAACiD,cAA5B,EAA4CjD,CAA5C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,cAAV,EAA0B1C,CAAC,CAACkD,cAA5B,EAA4ClD,CAA5C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,WAAV,EAAuB1C,CAAC,CAACJ,YAAzB,EAAuCI,CAAvC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeQ,YAAzB,EAAuCnD,CAAC,CAACoD,cAAzC,EAAyDpD,CAAzD;AACH,SAjZuC,CAkZxC;;;AACAwC,QAAAA,gBAAgB,GAAG;AACf,cAAIxC,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeC,WAA1B,EAAuC5C,CAAC,CAAC6C,aAAzC,EAAwD7C,CAAxD;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,UAAX,EAAuBrD,CAAC,CAAC8C,UAAzB,EAAqC9C,CAArC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeI,YAA1B,EAAwC/C,CAAC,CAACgD,iBAA1C,EAA6DhD,CAA7D;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,cAAX,EAA2BrD,CAAC,CAACiD,cAA7B,EAA6CjD,CAA7C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,cAAX,EAA2BrD,CAAC,CAACkD,cAA7B,EAA6ClD,CAA7C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,WAAX,EAAwBrD,CAAC,CAACJ,YAA1B,EAAwCI,CAAxC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeQ,YAA1B,EAAwCnD,CAAC,CAACoD,cAA1C,EAA0DpD,CAA1D;AACH,SA5ZuC,CA6ZxC;;;AACA8B,QAAAA,KAAK,GAAG;AACJ,cAAI9B,CAAM,GAAG,IAAb;AACA,cAAIA,CAAC,CAACzD,OAAN,EACI;AAEJyD,UAAAA,CAAC,CAACP,WAAF,GAAgBO,CAAC,CAACyC,IAAF,CAAOpC,YAAP,CAAoBrH,WAApB,CAAhB;AACAgH,UAAAA,CAAC,CAACxD,WAAF,GAAgBwD,CAAC,CAACyC,IAAF,CAAOpC,YAAP,CAAoBxH,UAApB,CAAhB;AAEAmH,UAAAA,CAAC,CAACnE,OAAF,GAAYmE,CAAC,CAACxD,WAAF,CAAcX,OAA1B;AACAmE,UAAAA,CAAC,CAAClE,UAAF,GAAekE,CAAC,CAACnE,OAAF,CAAUwE,YAAV,CAAuBrH,WAAvB,CAAf;;AACA,cAAI,CAACgH,CAAC,CAACnE,OAAP,EAAgB;AACZqF,YAAAA,OAAO,CAACC,KAAR,CAAcnB,CAAC,CAACyC,IAAF,CAAOa,IAAP,GAAc,8BAA5B;AACA;AACH;;AAEDtD,UAAAA,CAAC,CAACvD,OAAF,GAAYuD,CAAC,CAACnE,OAAF,CAAUwE,YAAV,CAAuB5H,MAAvB,CAAZ;AAEAuH,UAAAA,CAAC,CAACxE,MAAF,GAAWwE,CAAC,CAACvD,OAAF,CAAU1C,IAArB,CAjBI,CAiBuB;;AAC3BiG,UAAAA,CAAC,CAACtD,WAAF,GAAgBsD,CAAC,CAACvD,OAAF,CAAU8G,UAA1B,CAlBI,CAkBkC;;AACtCvD,UAAAA,CAAC,CAACrE,UAAF,GAAeqE,CAAC,CAACvD,OAAF,CAAU+G,SAAzB;AAEAxD,UAAAA,CAAC,CAACrD,OAAF,GAAYqD,CAAC,CAACvD,OAAF,CAAUgH,UAAtB,CArBI,CAqB8B;;AAClCzD,UAAAA,CAAC,CAACpD,SAAF,GAAcoD,CAAC,CAACvD,OAAF,CAAUiH,YAAxB,CAtBI,CAsBkC;;AACtC1D,UAAAA,CAAC,CAACnD,UAAF,GAAemD,CAAC,CAACvD,OAAF,CAAUkH,aAAzB,CAvBI,CAuBoC;;AACxC3D,UAAAA,CAAC,CAAClD,QAAF,GAAakD,CAAC,CAACvD,OAAF,CAAUmH,WAAvB,CAxBI,CAwBgC;;AAEpC5D,UAAAA,CAAC,CAACjD,UAAF,GAAeiD,CAAC,CAACvD,OAAF,CAAUoH,QAAzB,CA1BI,CA0B+B;;AACnC7D,UAAAA,CAAC,CAAChD,QAAF,GAAagD,CAAC,CAACvD,OAAF,CAAUqH,QAAvB,CA3BI,CA2B6B;;AAEjC9D,UAAAA,CAAC,CAAC/C,WAAF,CA7BI,CA6BW;;AAEf+C,UAAAA,CAAC,CAACtE,YAAF,GAAiBsE,CAAC,CAACvD,OAAF,CAAUsH,iBAA3B,CA/BI,CA+B0C;;AAC9C/D,UAAAA,CAAC,CAACvE,cAAF,GAAmBuE,CAAC,CAACvD,OAAF,CAAUuH,mBAA7B,CAhCI,CAgC8C;;AAElDhE,UAAAA,CAAC,CAACiE,eAAF,CAAkB1L,WAAW,CAACyH,CAAC,CAAC9F,YAAF,IAAkBP,YAAY,CAACS,MAA/B,GAAwC4F,CAAC,CAACkE,SAA1C,GAAsDlE,CAAC,CAACiC,OAAzD,CAA7B,EAlCI,CAoCJ;;AACA,cAAIjC,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA1B,IAAsCnE,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAApE,EAA0E;AACtEwF,YAAAA,CAAC,CAACxD,WAAF,CAAc4H,OAAd,GAAwB,KAAxB;;AACApE,YAAAA,CAAC,CAACxD,WAAF,CAAc6H,aAAd,GAA8B,YAAY;AACtC;AACH,aAFD;AAGH;;AACD,cAAI,CAACrE,CAAC,CAACnF,OAAP,EAAwB;AACpBmF,YAAAA,CAAC,CAAClF,UAAF,GAAe,KAAf;AAEJkF,UAAAA,CAAC,CAAC9C,gBAAF,GAAqB,EAArB,CA9CI,CA8CqB;;AACzB8C,UAAAA,CAAC,CAAC7C,WAAF,GAAgB,EAAhB,CA/CI,CA+CgB;;AACpB6C,UAAAA,CAAC,CAAC5C,KAAF,GAAU,IAAIzE,QAAJ,EAAV,CAhDI,CAgDyB;;AAC7BqH,UAAAA,CAAC,CAACzE,YAAF,GAAiB,KAAjB,CAjDI,CAiD4B;;AAChCyE,UAAAA,CAAC,CAAC9D,cAAF,GAAmB,CAAnB,CAlDI,CAkD4B;;AAChC8D,UAAAA,CAAC,CAAC/D,WAAF,GAAgB,IAAhB,CAnDI,CAmD4B;;AAEhC+D,UAAAA,CAAC,CAAClB,UAAF,GAAe,CAAf,CArDI,CAqD4B;;AAEhC,cAAIkB,CAAC,CAACpF,MAAF,IAAY,CAAhB,EAAmB;AACfoF,YAAAA,CAAC,CAACxD,WAAF,CAAc8H,qBAAd,GAAsC,KAAKA,qBAAL,CAA2BC,IAA3B,CAAgCvE,CAAhC,CAAtC;;AACAA,YAAAA,CAAC,CAACxD,WAAF,CAAcgI,wBAAd,GAAyC,YAAY;AACjD,qBAAO,KAAP;AACH,aAFD;AAGH;;AAED,kBAAQxE,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AAA6B;AACzB,wBAAQ1E,CAAC,CAACvE,cAAV;AACI,uBAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AACI5E,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,uBAAKnD,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AACI7E,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AACH;;AACD,iBAAKnD,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,wBAAQ9E,CAAC,CAACtE,YAAV;AACI,uBAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AACIhF,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,uBAAKnD,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AACIjF,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AACH;;AACD,iBAAKnD,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB,wBAAQlF,CAAC,CAACrE,UAAV;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,4BAAQ1E,CAAC,CAACtE,YAAV;AACI,2BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AACIhF,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,2BAAKnD,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AACIjF,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;;AACJ,uBAAKnD,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,4BAAQ9E,CAAC,CAACvE,cAAV;AACI,2BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AACI5E,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,2BAAKnD,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AACI7E,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AApBR;;AAsBA;AACH;AA/CL,WA9DI,CA+GJ;AACA;AACA;AACA;AACA;AACA;;;AACAoE,UAAAA,CAAC,CAACnE,OAAF,CAAUuJ,iBAAV;AACApF,UAAAA,CAAC,CAACzD,OAAF,GAAY,IAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACI+H,QAAAA,qBAAqB,CAACe,EAAD,EAAa;AAE9B;AACA,gBAAMC,+BAA+B,GAAG,IAAxC;AACA,gBAAMC,OAAO,GAAG,IAAhB;AACA,gBAAMC,IAAI,GAAG,IAAIvM,IAAJ,EAAb;;AACA,gBAAMwM,YAAY,GAAIC,IAAD,IAAkB;AACnCA,YAAAA,IAAI,IAAI,CAAR;AACA,mBAAQA,IAAI,GAAGA,IAAP,GAAcA,IAAd,GAAqBA,IAArB,GAA4BA,IAA5B,GAAmC,CAA3C;AACH,WAHD,CAN8B,CAU9B;;;AAEA,cAAIC,EAAc,GAAG,KAAKnJ,WAA1B;AAEA,gBAAMoJ,iBAAiB,GAAGD,EAAE,CAAC,6BAAD,CAAF,EAA1B;AACA,gBAAME,aAAa,GAAGD,iBAAiB,GAAGN,+BAAH,GAAqC,CAA5E;AACAK,UAAAA,EAAE,CAAC,4BAAD,CAAF,IAAoCN,EAAE,IAAI,IAAIQ,aAAR,CAAtC;AAEA,cAAIC,UAAU,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYL,EAAE,CAAC,4BAAD,CAAF,GAAmCA,EAAE,CAAC,sBAAD,CAAjD,CAAjB;;AACA,cAAIA,EAAE,CAAC,sBAAD,CAAN,EAAgC;AAC5BG,YAAAA,UAAU,GAAGL,YAAY,CAACK,UAAD,CAAzB;AACH;;AAED,gBAAMG,2BAA2B,GAAGN,EAAE,CAAC,wBAAD,CAAF,CAA6BO,KAA7B,EAApC;AACAD,UAAAA,2BAA2B,CAACE,cAA5B,CAA2CL,UAA3C;AACA,gBAAMM,6BAA6B,GAAGT,EAAE,CAAC,0BAAD,CAAF,CAA+BO,KAA/B,EAAtC;AACAE,UAAAA,6BAA6B,CAACC,GAA9B,CAAkCJ,2BAAlC;AACA,cAAIK,UAAU,GAAGP,IAAI,CAACQ,GAAL,CAAST,UAAU,GAAG,CAAtB,KAA4BP,OAA7C;AAEA,gBAAMiB,SAAS,GAAGT,IAAI,CAACQ,GAAL,CAAST,UAAU,GAAG,CAAtB,KAA4BH,EAAE,CAAC,2BAAD,CAAF,EAA9C;;AACA,cAAIa,SAAS,IAAI,CAACb,EAAE,CAAC,uCAAD,CAApB,EAA+D;AAC3DA,YAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqB8D,yBAA1C;AACAd,YAAAA,EAAE,CAAC,uCAAD,CAAF,GAA8C,IAA9C;AACH;;AAED,cAAIA,EAAE,CAAC,SAAD,CAAN,EAAmB;AACf,kBAAMe,mBAAmB,GAAGN,6BAA6B,CAACF,KAA9B,EAA5B;AACAQ,YAAAA,mBAAmB,CAACC,QAApB,CAA6BhB,EAAE,CAAC,iCAAD,CAA/B;;AACA,gBAAIC,iBAAJ,EAAuB;AACnBc,cAAAA,mBAAmB,CAACP,cAApB,CAAmCN,aAAnC;AACH;;AACDO,YAAAA,6BAA6B,CAACQ,GAA9B,CAAkCjB,EAAE,CAAC,iCAAD,CAApC;AACAS,YAAAA,6BAA6B,CAACC,GAA9B,CAAkCK,mBAAlC;AACH,WARD,MAQO;AACH,kBAAMG,SAAS,GAAGT,6BAA6B,CAACF,KAA9B,EAAlB;AACAW,YAAAA,SAAS,CAACF,QAAV,CAAmBhB,EAAE,CAAC,qBAAD,CAAF,EAAnB;AACA,kBAAMmB,aAAa,GAAGnB,EAAE,CAAC,0BAAD,CAAF,CAA+BkB,SAA/B,CAAtB;;AACA,gBAAI,CAACC,aAAa,CAACC,MAAd,CAAqBvB,IAArB,EAA2BD,OAA3B,CAAL,EAA0C;AACtCa,cAAAA,6BAA6B,CAACC,GAA9B,CAAkCS,aAAlC;AACAR,cAAAA,UAAU,GAAG,IAAb;AACH;AACJ;;AAED,cAAIA,UAAJ,EAAgB;AACZX,YAAAA,EAAE,CAAC,gBAAD,CAAF,GAAuB,KAAvB;AACH;;AAED,gBAAMqB,SAAS,GAAG,IAAI/N,IAAJ,CAASmN,6BAAT,CAAlB;AACAY,UAAAA,SAAS,CAACL,QAAV,CAAmBhB,EAAE,CAAC,qBAAD,CAAF,EAAnB;AACAA,UAAAA,EAAE,CAAC,aAAD,CAAF,CAAkBqB,SAAlB;AACArB,UAAAA,EAAE,CAAC,cAAD,CAAF,CAAmBqB,SAAnB,EAA8BV,UAA9B;AACAX,UAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqBsE,SAA1C;;AAEA,cAAI,CAACtB,EAAE,CAAC,gBAAD,CAAP,EAA2B;AACvBA,YAAAA,EAAE,CAAC,aAAD,CAAF,GAAoB,KAApB;AACAA,YAAAA,EAAE,CAAC,YAAD,CAAF,GAAmB,KAAnB;AACAA,YAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqBuE,YAA1C;AACH;AACJ,SA9lBuC,CA+lBxC;;;AACAjD,QAAAA,eAAe,CAAChE,IAAD,EAAY;AACvB,cAAI,CAACA,IAAL,EACI;AACJ,cAAID,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAAC3C,QAAF,GAAa4C,IAAb;AACAD,UAAAA,CAAC,CAAC1C,UAAF,GAAe2C,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAf;AAEA,cAAIgH,CAAC,CAACtD,WAAF,IAAiBjE,MAAM,CAAC0O,UAAP,CAAkBC,QAAvC,EACIpH,CAAC,CAACxC,SAAF,GAAcwC,CAAC,CAACvD,OAAF,CAAU4K,QAAxB,CADJ,KAEK;AACD,gBAAIC,MAAmB,GAAGrH,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAA1B;AACAgH,YAAAA,CAAC,CAACxC,SAAF,GAAc,IAAI1E,IAAJ,CAASwO,MAAM,CAACC,KAAhB,EAAuBD,MAAM,CAACE,MAA9B,CAAd;AACH,WAZsB,CAcvB;;AACA,cAAIC,GAAQ,GAAGxH,IAAI,CAACI,YAAL;AAAA;AAAA,mCAAf;AACA,cAAIqH,MAAM,GAAG,KAAb;AACA,cAAI,CAACD,GAAL,EACIC,MAAM,GAAG,IAAT,CAlBmB,CAmBvB;AACA;AACA;AACA;AACA;;AACA,cAAIA,MAAJ,EAAY;AACR1H,YAAAA,CAAC,CAAChF,YAAF,GAAiBnB,YAAY,CAACoB,IAA9B;AACH;;AACDwM,UAAAA,GAAG,GAAGxH,IAAI,CAACI,YAAL,CAAkBnH,MAAlB,CAAN;;AACA,cAAIuO,GAAG,IAAIA,GAAG,CAAClG,OAAf,EAAwB;AACpBvB,YAAAA,CAAC,CAACzC,iBAAF,GAAsB,IAAtB;AACH;;AACD,cAAIyC,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAAC6G,IAAnC,EACIV,CAAC,CAAC1E,YAAF,GAAiB,EAAjB;;AAEJ,kBAAQ0E,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI1E,cAAAA,CAAC,CAAC/C,WAAF,GAAgB,CAAhB;AACA+C,cAAAA,CAAC,CAACvC,SAAF,GAAc,KAAd;AACA;;AACJ,iBAAKhF,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AACI9E,cAAAA,CAAC,CAAC/C,WAAF,GAAgB,CAAhB;AACA+C,cAAAA,CAAC,CAACvC,SAAF,GAAc,IAAd;AACA;;AACJ,iBAAKhF,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AACI,sBAAQlF,CAAC,CAACrE,UAAV;AACI,qBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI;AACA,sBAAIiD,KAAa,GAAG3H,CAAC,CAAClE,UAAF,CAAayL,KAAb,GAAqBvH,CAAC,CAAClD,QAAvB,GAAkCkD,CAAC,CAACpD,SAAxD;AACAoD,kBAAAA,CAAC,CAAC/C,WAAF,GAAgB8I,IAAI,CAAC6B,KAAL,CAAW,CAACD,KAAK,GAAG3H,CAAC,CAACjD,UAAX,KAA0BiD,CAAC,CAACxC,SAAF,CAAY+J,KAAZ,GAAoBvH,CAAC,CAACjD,UAAhD,CAAX,CAAhB;AACAiD,kBAAAA,CAAC,CAACvC,SAAF,GAAc,IAAd;AACA;;AACJ,qBAAKhF,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI;AACA,sBAAI+C,KAAa,GAAG7H,CAAC,CAAClE,UAAF,CAAa0L,MAAb,GAAsBxH,CAAC,CAACrD,OAAxB,GAAkCqD,CAAC,CAACnD,UAAxD;AACAmD,kBAAAA,CAAC,CAAC/C,WAAF,GAAgB8I,IAAI,CAAC6B,KAAL,CAAW,CAACC,KAAK,GAAG7H,CAAC,CAAChD,QAAX,KAAwBgD,CAAC,CAACxC,SAAF,CAAYgK,MAAZ,GAAqBxH,CAAC,CAAChD,QAA/C,CAAX,CAAhB;AACAgD,kBAAAA,CAAC,CAACvC,SAAF,GAAc,KAAd;AACA;AAZR;;AAcA;AAxBR;AA0BH;AACD;AACJ;AACA;AACA;AACA;;;AACIwD,QAAAA,WAAW,CAAC6G,QAAiB,GAAG,IAArB,EAA2B;AAClC,cAAI,CAAC,KAAKvL,OAAV,EAAmB;AACf,gBAAIuL,QAAJ,EACI5G,OAAO,CAACC,KAAR,CAAc,oCAAd;AACJ,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH,SAzqBuC,CA0qBxC;;;AACAC,QAAAA,cAAc,GAAG;AACb,cAAIpB,CAAM,GAAG,IAAb;AACA,cAAI+H,MAAJ;;AAEA,kBAAQ/H,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AAA6B;AACzB,oBAAI1E,CAAC,CAACtC,WAAN,EAAmB;AACf,sBAAIsK,KAAU,GAAGhI,CAAC,CAACiI,aAAF,CAAgB,IAAhB,CAAjB;;AACAF,kBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAakL,KAAK,CAACvN,GAAnB,GAA0BuF,CAAC,CAACxC,SAAF,CAAY+J,KAAZ,IAAqBvH,CAAC,CAACL,SAAF,GAAcqI,KAAK,CAACE,KAAzC,CAA1B,GAA8ElI,CAAC,CAACjD,UAAF,IAAgBiD,CAAC,CAACL,SAAF,GAAc,CAA9B,CAA9E,GAAkHK,CAAC,CAACpD,SAA7H;AACH,iBAHD,MAGO;AACHmL,kBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAckD,CAAC,CAACxC,SAAF,CAAY+J,KAAZ,GAAoBvH,CAAC,CAACL,SAApC,GAAkDK,CAAC,CAACjD,UAAF,IAAgBiD,CAAC,CAACL,SAAF,GAAc,CAA9B,CAAlD,GAAsFK,CAAC,CAACpD,SAAjG;AACH;;AACD;AACH;;AACD,iBAAKnE,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,oBAAI9E,CAAC,CAACtC,WAAN,EAAmB;AACf,sBAAIsK,KAAU,GAAGhI,CAAC,CAACiI,aAAF,CAAgB,IAAhB,CAAjB;;AACAF,kBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAYqL,KAAK,CAACvN,GAAlB,GAAyBuF,CAAC,CAACxC,SAAF,CAAYgK,MAAZ,IAAsBxH,CAAC,CAACL,SAAF,GAAcqI,KAAK,CAACE,KAA1C,CAAzB,GAA8ElI,CAAC,CAAChD,QAAF,IAAcgD,CAAC,CAACL,SAAF,GAAc,CAA5B,CAA9E,GAAgHK,CAAC,CAACnD,UAA3H;AACH,iBAHD,MAGO;AACHkL,kBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAaqD,CAAC,CAACxC,SAAF,CAAYgK,MAAZ,GAAqBxH,CAAC,CAACL,SAApC,GAAkDK,CAAC,CAAChD,QAAF,IAAcgD,CAAC,CAACL,SAAF,GAAc,CAA5B,CAAlD,GAAoFK,CAAC,CAACnD,UAA/F;AACH;;AACD;AACH;;AACD,iBAAKpE,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB;AACA,oBAAIlF,CAAC,CAAClF,UAAN,EACIkF,CAAC,CAAClF,UAAF,GAAe,KAAf;;AACJ,wBAAQkF,CAAC,CAACrE,UAAV;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,wBAAIyD,OAAe,GAAGpC,IAAI,CAACqC,IAAL,CAAUpI,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC/C,WAA1B,CAAtB;AACA8K,oBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAaqD,CAAC,CAACxC,SAAF,CAAYgK,MAAZ,GAAqBW,OAAlC,GAA8CnI,CAAC,CAAChD,QAAF,IAAcmL,OAAO,GAAG,CAAxB,CAA9C,GAA4EnI,CAAC,CAACnD,UAAvF;AACA;;AACJ,uBAAKpE,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,wBAAIuD,MAAc,GAAGtC,IAAI,CAACqC,IAAL,CAAUpI,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC/C,WAA1B,CAArB;AACA8K,oBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAckD,CAAC,CAACxC,SAAF,CAAY+J,KAAZ,GAAoBc,MAAlC,GAA6CrI,CAAC,CAACjD,UAAF,IAAgBsL,MAAM,GAAG,CAAzB,CAA7C,GAA4ErI,CAAC,CAACpD,SAAvF;AACA;AARR;;AAUA;AACH;AAlCL;;AAqCA,cAAI0E,MAAc,GAAGtB,CAAC,CAACnE,OAAF,CAAUwE,YAAV,CAAuB5H,MAAvB,CAArB;AACA,cAAI6I,MAAJ,EACIA,MAAM,CAACC,OAAP,GAAiB,KAAjB;AAEJvB,UAAAA,CAAC,CAACV,YAAF,GAAiByI,MAAjB;AACA/H,UAAAA,CAAC,CAACT,kBAAF,GAAuBS,CAAC,CAACV,YAAF,IAAkBU,CAAC,CAACvC,SAAF,GAAeuC,CAAC,CAACrD,OAAF,GAAYqD,CAAC,CAACnD,UAA7B,GAA4CmD,CAAC,CAAClD,QAAF,GAAakD,CAAC,CAACpD,SAA7E,CAAvB;;AAEA,cAAIoD,CAAC,CAACpF,MAAN,EAAc;AACV,gBAAI0N,SAAiB,GAAItI,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAc+H,MAA5B,GAAqCxH,CAAC,CAACP,WAAF,CAAc8H,KAA5E;AAEAvH,YAAAA,CAAC,CAAC3D,WAAF,GAAgB,CAAhB;AACAiM,YAAAA,SAAS,IAAItI,CAAC,CAAC3D,WAAf;AACA2D,YAAAA,CAAC,CAAC5D,UAAF,GAAe2J,IAAI,CAACqC,IAAL,CAAUE,SAAS,GAAGtI,CAAC,CAACT,kBAAxB,IAA8C,CAA7D;AACA,gBAAIgJ,OAAe,GAAGvI,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAAChD,QAAhB,GAA2BgD,CAAC,CAACjD,UAAnD;AACAiD,YAAAA,CAAC,CAAC1D,WAAF,GAAgB0D,CAAC,CAAC3D,WAAF,GAAgB2D,CAAC,CAACT,kBAAlB,GAAuCgJ,OAAvD;AACAvI,YAAAA,CAAC,CAACwI,kBAAF,GAAuBxI,CAAC,CAACV,YAAF,GAAkBU,CAAC,CAACT,kBAAF,IAAwBS,CAAC,CAAC5D,UAAF,GAAe,CAAvC,CAAlB,GAAgEmM,OAAO,IAAIvI,CAAC,CAAC5D,UAAF,GAAe,CAAnB,CAA9F;AACA4D,YAAAA,CAAC,CAACyI,wBAAF,GAA6BzI,CAAC,CAACT,kBAAF,GAAuBS,CAAC,CAAC5D,UAAtD;AACA4D,YAAAA,CAAC,CAACyI,wBAAF,IAA8BF,OAAO,IAAIvI,CAAC,CAAC5D,UAAF,GAAe,CAAnB,CAArC,CAVU,CAWV;AACH;;AAED4D,UAAAA,CAAC,CAACX,KAAF,GAAU,CAACW,CAAC,CAACpF,MAAH,IAAaoF,CAAC,CAACV,YAAF,IAAkBU,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAc+H,MAA5B,GAAqCxH,CAAC,CAACP,WAAF,CAAc8H,KAArE,CAAvB;AACA,cAAImB,WAAmB,GAAI,CAAC,CAAC1I,CAAC,CAACX,KAAH,IAAY,CAACW,CAAC,CAAClF,UAAhB,KAA+BkF,CAAC,CAACjF,SAAlC,GAA+C,CAA/C,GAAmD,EAA7E;AAEA,cAAI4N,QAAgB,GAAG3I,CAAC,CAACX,KAAF,GAAW,CAACW,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAc+H,MAA5B,GAAqCxH,CAAC,CAACP,WAAF,CAAc8H,KAApD,IAA6DmB,WAAxE,GAAwF1I,CAAC,CAACpF,MAAF,GAAWoF,CAAC,CAACwI,kBAAb,GAAkCxI,CAAC,CAACV,YAAnJ;AACA,cAAIqJ,QAAQ,GAAG,CAAf,EACIA,QAAQ,GAAG,CAAX;;AAEJ,cAAI3I,CAAC,CAACvC,SAAN,EAAiB;AACbuC,YAAAA,CAAC,CAAClE,UAAF,CAAa0L,MAAb,GAAsBmB,QAAtB;AACH,WAFD,MAEO;AACH3I,YAAAA,CAAC,CAAClE,UAAF,CAAayL,KAAb,GAAqBoB,QAArB;AACH,WAzEY,CA2Eb;;AACH,SAvvBuC,CAyvBxC;;;AACA/I,QAAAA,YAAY,CAACgJ,EAAS,GAAG,IAAb,EAAmB;AAC3B,cAAI,KAAKjL,UAAL,IAAmB,IAAvB,EACI,KAAKA,UAAL,GAAkB,KAAKmC,WAAvB;;AACJ,cAAI,CAAC,KAAKvE,YAAN,IAAuBqN,EAAE,IAAIA,EAAE,CAAC7O,IAAH,IAAW,cAAxC,IAA2D,KAAK4D,UAAL,GAAkB,CAAjF,EAAoF;AAChF,iBAAKA,UAAL;AACA;AACH,WAHD,MAII,KAAKA,UAAL,GAAkB,KAAKmC,WAAvB;;AAEJ,cAAI,KAAKlC,aAAT,EACI,OAVuB,CAY3B;;AACA,cAAI,KAAKhD,MAAT,EAAiB;AACb,gBAAIiO,SAAc,GAAG,KAAKhN,OAAL,CAAaiN,WAAb,EAArB;AACAD,YAAAA,SAAS,GAAG,KAAKpL,SAAL,GAAiBoL,SAAS,CAACE,CAA3B,GAA+BF,SAAS,CAACG,CAArD;AAEA,gBAAIC,MAAM,GAAG,KAAK1J,kBAAL,IAA2B,KAAK9B,SAAL,GAAiB,KAAKT,QAAtB,GAAiC,KAAKD,UAAjE,CAAb;AACA,gBAAIsJ,GAAQ,GAAG,KAAK5I,SAAL,GAAiB,IAAIxE,IAAJ,CAAS,CAAT,EAAYgQ,MAAZ,EAAoB,CAApB,CAAjB,GAA0C,IAAIhQ,IAAJ,CAASgQ,MAAT,EAAiB,CAAjB,EAAoB,CAApB,CAAzD;AAEA,gBAAIC,UAAU,GAAG,KAAKrN,OAAL,CAAaiN,WAAb,EAAjB;;AAEA,oBAAQ,KAAKlN,cAAb;AACI,mBAAK,CAAL;AAAO;AACH,oBAAIiN,SAAS,GAAG,CAAC,KAAKxM,WAAtB,EAAmC;AAC/B6M,kBAAAA,UAAU,CAACtC,GAAX,CAAe,CAAC,KAAKtK,WAArB,EAAkC4M,UAAU,CAACH,CAA7C,EAAgDG,UAAU,CAACC,CAA3D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6CmK,QAA7C,CAAsDN,GAAtD,CAA/C;AACH,mBAL8B,CAM/B;AACA;AACA;;AACH,iBATD,MASO,IAAIwC,SAAS,GAAG,CAAC,KAAKvM,WAAtB,EAAmC;AACtC4M,kBAAAA,UAAU,CAACtC,GAAX,CAAe,CAAC,KAAKvK,WAArB,EAAkC6M,UAAU,CAACH,CAA7C,EAAgDG,UAAU,CAACC,CAA3D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6C6J,GAA7C,CAAiDA,GAAjD,CAA/C;AACH,mBALqC,CAMtC;AACA;AACA;;AACH;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIwC,SAAS,GAAG,KAAKxM,WAArB,EAAkC;AAC9B6M,kBAAAA,UAAU,CAACtC,GAAX,CAAe,KAAKtK,WAApB,EAAiC4M,UAAU,CAACH,CAA5C,EAA+CG,UAAU,CAACC,CAA1D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6C6J,GAA7C,CAAiDA,GAAjD,CAA/C;AACH;AACJ,iBAND,MAMO,IAAIwC,SAAS,GAAG,KAAKvM,WAArB,EAAkC;AACrC4M,kBAAAA,UAAU,CAACtC,GAAX,CAAe,KAAKvK,WAApB,EAAiC6M,UAAU,CAACH,CAA5C,EAA+CG,UAAU,CAACC,CAA1D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6CmK,QAA7C,CAAsDN,GAAtD,CAA/C;AACH;AACJ;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIwC,SAAS,GAAG,KAAKxM,WAArB,EAAkC;AAC9B6M,kBAAAA,UAAU,CAACtC,GAAX,CAAesC,UAAU,CAACF,CAA1B,EAA6B,KAAK1M,WAAlC,EAA+C4M,UAAU,CAACC,CAA1D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6C6J,GAA7C,CAAiDA,GAAjD,CAA/C;AACH;AACJ,iBAND,MAMO,IAAIwC,SAAS,GAAG,KAAKvM,WAArB,EAAkC;AACrC4M,kBAAAA,UAAU,CAACtC,GAAX,CAAesC,UAAU,CAACF,CAA1B,EAA6B,KAAK3M,WAAlC,EAA+C6M,UAAU,CAACC,CAA1D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6CmK,QAA7C,CAAsDN,GAAtD,CAA/C;AACH;AACJ;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIwC,SAAS,GAAG,CAAC,KAAKxM,WAAtB,EAAmC;AAC/B6M,kBAAAA,UAAU,CAACtC,GAAX,CAAesC,UAAU,CAACF,CAA1B,EAA6B,CAAC,KAAK1M,WAAnC,EAAgD4M,UAAU,CAACC,CAA3D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6CmK,QAA7C,CAAsDN,GAAtD,CAA/C;AACH;AACJ,iBAND,MAMO,IAAIwC,SAAS,GAAG,CAAC,KAAKvM,WAAtB,EAAmC;AACtC4M,kBAAAA,UAAU,CAACtC,GAAX,CAAesC,UAAU,CAACF,CAA1B,EAA6B,CAAC,KAAK3M,WAAnC,EAAgD6M,UAAU,CAACC,CAA3D;AACA,uBAAKtN,OAAL,CAAauN,WAAb,CAAyBF,UAAzB;;AACA,sBAAI,KAAK1M,WAAL,CAAiB6M,eAAjB,EAAJ,EAAwC;AACpC,yBAAK7M,WAAL,CAAiB,0BAAjB,IAA+C,KAAKA,WAAL,CAAiB,0BAAjB,EAA6C6J,GAA7C,CAAiDA,GAAjD,CAA/C;AACH;AACJ;;AACD;AAlER;AAoEH;;AAED,eAAKiD,YAAL;;AAEA,cAAIC,IAAJ,EAAkBC,MAAlB,EAAkCC,OAAlC,EAAmDC,KAAnD;;AACA,cAAI,KAAKjM,SAAT,EAAoB;AAChB8L,YAAAA,IAAI,GAAG,KAAKtL,OAAZ;AACAwL,YAAAA,OAAO,GAAG,KAAKtL,UAAf;AACH,WAHD,MAGO;AACHqL,YAAAA,MAAM,GAAG,KAAKtL,SAAd;AACAwL,YAAAA,KAAK,GAAG,KAAKtL,QAAb;AACH;;AAED,cAAI,KAAKsB,QAAT,EAAmB;AACf,iBAAKvC,WAAL,GAAmB,EAAnB;AACA,gBAAIwM,OAAJ;AAEA,gBAAIC,KAAa,GAAG,CAApB;AACA,gBAAIC,KAAa,GAAG,KAAKlK,SAAL,GAAiB,CAArC;;AAEA,gBAAI,KAAKjC,WAAT,EAAsB;AAClB,kBAAIoM,QAAiB,GAAG,KAAxB,CADkB,CAElB;;AACA,qBAAOF,KAAK,IAAIC,KAAT,IAAkB,CAACC,QAA1B,EAAoCF,KAAK,EAAzC,EAA6C;AACzCD,gBAAAA,OAAO,GAAG,KAAKI,YAAL,CAAkBH,KAAlB,CAAV;;AACA,wBAAQ,KAAKpO,MAAb;AACI,uBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI,wBAAIiF,OAAO,CAACK,KAAR,IAAiBN,KAAjB,IAA0BC,OAAO,CAACM,IAAR,IAAgBT,MAA9C,EAAsD;AAClD,2BAAKrM,WAAL,CAAiB2D,IAAjB,CAAsB6I,OAAtB;AACH,qBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAKzM,WAAL,CAAiB+M,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,sBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,uBAAKrR,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AACI,wBAAI6E,OAAO,CAACQ,MAAR,IAAkBZ,IAAlB,IAA0BI,OAAO,CAACS,GAAR,IAAeX,OAA7C,EAAsD;AAClD,2BAAKtM,WAAL,CAAiB2D,IAAjB,CAAsB6I,OAAtB;AACH,qBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAKzM,WAAL,CAAiB+M,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,sBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,uBAAKrR,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AACI,4BAAQ,KAAKvJ,UAAb;AACI,2BAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,4BAAIiF,OAAO,CAACQ,MAAR,IAAkBZ,IAAlB,IAA0BI,OAAO,CAACS,GAAR,IAAeX,OAA7C,EAAsD;AAClD,+BAAKtM,WAAL,CAAiB2D,IAAjB,CAAsB6I,OAAtB;AACH,yBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAKzM,WAAL,CAAiB+M,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,0BAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,2BAAKrR,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,4BAAI6E,OAAO,CAACK,KAAR,IAAiBN,KAAjB,IAA0BC,OAAO,CAACM,IAAR,IAAgBT,MAA9C,EAAsD;AAClD,+BAAKrM,WAAL,CAAiB2D,IAAjB,CAAsB6I,OAAtB;AACH,yBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAKzM,WAAL,CAAiB+M,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,0BAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;AAdR;;AAgBA;AAhCR;AAkCH;AACJ,aAxCD,MAwCO;AACH,kBAAIO,EAAU,GAAG,KAAK7M,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7C;AACA,kBAAIuN,EAAU,GAAG,KAAK9M,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9C;;AACA,sBAAQ,KAAKpB,cAAb;AACI,qBAAK,CAAL;AAAO;AACHgO,kBAAAA,KAAK,GAAG,CAACF,KAAK,GAAG,KAAK5M,QAAd,IAA0BuN,EAAlC;AACAR,kBAAAA,KAAK,GAAG,CAACL,MAAM,GAAG,KAAK1M,QAAf,IAA2BuN,EAAnC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHT,kBAAAA,KAAK,GAAG,CAAC,CAACJ,MAAD,GAAU,KAAK5M,SAAhB,IAA6ByN,EAArC;AACAR,kBAAAA,KAAK,GAAG,CAAC,CAACH,KAAD,GAAS,KAAK9M,SAAf,IAA4ByN,EAApC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHT,kBAAAA,KAAK,GAAG,CAAC,CAACL,IAAD,GAAQ,KAAK5M,OAAd,IAAyB2N,EAAjC;AACAT,kBAAAA,KAAK,GAAG,CAAC,CAACJ,OAAD,GAAW,KAAK9M,OAAjB,IAA4B2N,EAApC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHV,kBAAAA,KAAK,GAAG,CAACH,OAAO,GAAG,KAAK5M,UAAhB,IAA8ByN,EAAtC;AACAT,kBAAAA,KAAK,GAAG,CAACN,IAAI,GAAG,KAAK1M,UAAb,IAA2ByN,EAAnC;AACA;AAhBR;;AAkBAV,cAAAA,KAAK,GAAG7D,IAAI,CAAC6B,KAAL,CAAWgC,KAAX,IAAoB,KAAK3M,WAAjC;AACA4M,cAAAA,KAAK,GAAG9D,IAAI,CAACqC,IAAL,CAAUyB,KAAV,IAAmB,KAAK5M,WAAhC;AACA4M,cAAAA,KAAK;AACL,kBAAID,KAAK,GAAG,CAAZ,EACIA,KAAK,GAAG,CAAR;AACJ,kBAAIC,KAAK,IAAI,KAAKlK,SAAlB,EACIkK,KAAK,GAAG,KAAKlK,SAAL,GAAiB,CAAzB;;AACJ,qBAAOiK,KAAK,IAAIC,KAAhB,EAAuBD,KAAK,EAA5B,EAAgC;AAC5B,qBAAKzM,WAAL,CAAiB2D,IAAjB,CAAsB,KAAKiJ,YAAL,CAAkBH,KAAlB,CAAtB;AACH;AACJ;;AACD,iBAAKpI,iBAAL;;AACA,gBAAI,KAAKrE,WAAL,CAAiB+M,MAAjB,IAA2B,CAA3B,IAAgC,CAAC,KAAKvK,SAA1C,EAAqD;AAAE;AACnD,mBAAKzC,gBAAL,GAAwB,EAAxB;AACA;AACH;;AACD,iBAAKnB,WAAL,GAAmB,KAAKoB,WAAL,CAAiB,CAAjB,EAAoBoN,EAAvC;AACA,iBAAKvO,cAAL,GAAsB,KAAKmB,WAAL,CAAiB+M,MAAvC;AAEA,gBAAIzI,GAAW,GAAG,KAAKvE,gBAAL,CAAsBgN,MAAxC;AAEA,gBAAIM,cAAuB,GAAG,KAAKxO,cAAL,IAAuByF,GAArD;;AACA,gBAAI+I,cAAJ,EAAoB;AAChB;AACA,kBAAI,KAAKnJ,qBAAL,GAA6B,CAAjC,EAAoC;AAChC,qBAAKnE,gBAAL,CAAsBuN,IAAtB,CAA2B,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAAE,yBAAOD,CAAC,GAAGC,CAAX;AAAc,iBAArD;AACH,eAJe,CAKhB;;;AACAH,cAAAA,cAAc,GAAG,KAAKzO,WAAL,IAAoB,KAAKmB,gBAAL,CAAsB,CAAtB,CAApB,IAAgD,KAAKC,WAAL,CAAiB,KAAKnB,cAAL,GAAsB,CAAvC,EAA0CuO,EAA1C,IAAgD,KAAKrN,gBAAL,CAAsBuE,GAAG,GAAG,CAA5B,CAAjH;AACH;;AAED,gBAAI,KAAKlG,YAAL,IAAqBiP,cAAzB,EAAyC;AAAK;AAC1C,kBAAI,KAAKnJ,qBAAL,GAA6B,CAAjC,EAAoC;AAChC;AACA;AACA;AACA,oBAAI,KAAK1B,SAAL,GAAiB,CAArB,EAAwB;AACpB,sBAAI,CAAC,KAAK1D,WAAV,EAAuB;AACnB,yBAAKoC,gBAAL,GAAwB,IAAxB;AACH,mBAFD,MAEO;AACH,yBAAKnC,cAAL,GAAsB,CAAtB;AACH;;AACD,uBAAKD,WAAL,GAAmB,KAAnB;AACH,iBAPD,MAOO;AACH,uBAAKC,cAAL,GAAsB,CAAtB;AACA,uBAAKD,WAAL,GAAmB,IAAnB;AACH,iBAd+B,CAehC;;AACH,eAhBD,MAgBO;AACH;AACA,qBAAKiB,gBAAL,GAAwB,EAAxB,CAFG,CAGH;;AACA,qBAAK,IAAI0N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5O,cAAzB,EAAyC4O,CAAC,EAA1C,EAA8C;AAC1C,uBAAKC,mBAAL,CAAyB,KAAK1N,WAAL,CAAiByN,CAAjB,CAAzB;AACH;;AACD,qBAAKrP,YAAL,GAAoB,KAApB;AACH;AACJ;;AACD,iBAAKuP,gBAAL;AACH;AACJ,SAj+BuC,CAk+BxC;;;AACAxB,QAAAA,YAAY,GAAG;AACX,cAAIT,SAAc,GAAG,KAAKhN,OAAL,CAAaiN,WAAb,EAArB;;AACA,kBAAQ,KAAKlN,cAAb;AACI,iBAAK,CAAL;AAAO;AACH,mBAAK6C,WAAL,GAAmBoK,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkBH,SAAS,CAACG,CAA5B,GAAgC,CAAnD;AACA,mBAAK5K,QAAL,GAAgB,CAACyK,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAAlC,IAAuC,KAAKvK,WAA5D;AAEA,mBAAKP,SAAL,GAAiB,KAAKE,QAAL,GAAgB,KAAKqB,WAAL,CAAiB8H,KAAlD;AACA,mBAAKhJ,YAAL,GAAoB,KAAKL,SAAL,GAAiB,KAAKpC,UAAL,CAAgByL,KAAjC,GAAyCxB,IAAI,CAACQ,GAAL,CAAS,KAAKrI,SAAL,GAAiB,KAAKpC,UAAL,CAAgByL,KAA1C,CAAzC,GAA4F,CAAhH;AACA,mBAAKrJ,SAAL,IAAkB,KAAKK,YAAvB,CANJ,CAOI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKA,YAAL,GAAoBsK,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAArD;AACA,mBAAK9K,SAAL,GAAiB,CAAC2K,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAAlC,IAAuC,KAAKzK,YAA7D;AACA,mBAAKH,QAAL,GAAgB,KAAKF,SAAL,GAAiB,KAAKuB,WAAL,CAAiB8H,KAAlD;AACA,mBAAK9I,WAAL,GAAmB,KAAKL,QAAL,GAAgB,CAAC,KAAKtC,UAAL,CAAgByL,KAAjC,GAAyCxB,IAAI,CAACQ,GAAL,CAAS,KAAKnI,QAAL,GAAgB,KAAKtC,UAAL,CAAgByL,KAAzC,CAAzC,GAA2F,CAA9G;AACA,mBAAKnJ,QAAL,IAAiB,KAAKK,WAAtB,CALJ,CAMI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKH,UAAL,GAAkBuK,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkBhD,IAAI,CAACQ,GAAL,CAASsC,SAAS,CAACE,CAAnB,CAAlB,GAA0C,CAA5D;AACA,mBAAK9K,OAAL,GAAe,CAAC4K,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkB,CAACF,SAAS,CAACE,CAA7B,GAAiC,CAAlC,IAAuC,KAAKzK,UAA3D;AACA,mBAAKH,UAAL,GAAkB,KAAKF,OAAL,GAAe,KAAKwB,WAAL,CAAiB+H,MAAlD;AACA,mBAAKhJ,aAAL,GAAqB,KAAKL,UAAL,GAAkB,CAAC,KAAKrC,UAAL,CAAgB0L,MAAnC,GAA4CzB,IAAI,CAACQ,GAAL,CAAS,KAAKpI,UAAL,GAAkB,KAAKrC,UAAL,CAAgB0L,MAA3C,CAA5C,GAAiG,CAAtH;AACA,mBAAKrJ,UAAL,IAAmB,KAAKK,aAAxB,CALJ,CAMI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKA,aAAL,GAAqBqK,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkBhD,IAAI,CAACQ,GAAL,CAASsC,SAAS,CAACE,CAAnB,CAAlB,GAA0C,CAA/D;AACA,mBAAK5K,UAAL,GAAkB,CAAC0K,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkB,CAACF,SAAS,CAACE,CAA7B,GAAiC,CAAlC,IAAuC,KAAKvK,aAA9D;AACA,mBAAKP,OAAL,GAAe,KAAKE,UAAL,GAAkB,KAAKsB,WAAL,CAAiB+H,MAAlD;AACA,mBAAKlJ,UAAL,GAAkB,KAAKL,OAAL,GAAe,KAAKnC,UAAL,CAAgB0L,MAA/B,GAAwCzB,IAAI,CAACQ,GAAL,CAAS,KAAKtI,OAAL,GAAe,KAAKnC,UAAL,CAAgB0L,MAAxC,CAAxC,GAA0F,CAA5G;AACA,mBAAKvJ,OAAL,IAAgB,KAAKK,UAArB,CALJ,CAMI;;AACA;AAjCR;AAmCH,SAxgCuC,CAygCxC;;;AACAyL,QAAAA,YAAY,CAACQ,EAAD,EAAa;AACrB,cAAIhD,KAAJ,EAAmBC,MAAnB,EAAmC4C,GAAnC,EAAgDD,MAAhD,EAAgEF,IAAhE,EAA8ED,KAA9E,EAA6Fe,KAA7F,EAA4GC,KAA5G;;AACA,kBAAQ,KAAKxP,MAAb;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI,sBAAQ,KAAKjJ,cAAb;AACI,qBAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3C,wBAAI,KAAKlH,WAAT,EAAsB;AAClB,0BAAIsK,KAAU,GAAG,KAAKC,aAAL,CAAmBsC,EAAnB,CAAjB;;AACAN,sBAAAA,IAAI,GAAG,KAAKnN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,KAA4CwN,EAAE,GAAGvC,KAAK,CAACE,KAAvD,CAAjB,IAAmFF,KAAK,CAACvN,GAAN,GAAa,KAAKsC,UAAL,GAAkBiL,KAAK,CAACE,KAAxH,CAAP;AACA,0BAAI+C,EAAU,GAAG,KAAKvN,WAAL,CAAiB6M,EAAjB,CAAjB;AACAhD,sBAAAA,KAAK,GAAI0D,EAAE,GAAG,CAAL,GAASA,EAAT,GAAc,KAAKzN,SAAL,CAAe+J,KAAtC;AACH,qBALD,MAKO;AACH0C,sBAAAA,IAAI,GAAG,KAAKnN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,IAA2CwN,EAAnE;AACAhD,sBAAAA,KAAK,GAAG,KAAK/J,SAAL,CAAe+J,KAAvB;AACH;;AACD,wBAAI,KAAKzM,UAAT,EAAqB;AACjBmP,sBAAAA,IAAI,IAAI,KAAKnN,QAAb;AACA,0BAAIoO,MAAc,GAAI,KAAKpP,UAAL,CAAgByL,KAAhB,GAAwB,CAAzB,GAA+B,KAAKhI,kBAAL,GAA0B,CAA9E;AACA0K,sBAAAA,IAAI,IAAIiB,MAAR;AACH;;AACDlB,oBAAAA,KAAK,GAAGC,IAAI,GAAG1C,KAAf;AACA,2BAAO;AACHgD,sBAAAA,EAAE,EAAEA,EADD;AAEHN,sBAAAA,IAAI,EAAEA,IAFH;AAGHD,sBAAAA,KAAK,EAAEA,KAHJ;AAIHhB,sBAAAA,CAAC,EAAEiB,IAAI,GAAI,KAAK3M,UAAL,CAAgB6N,OAAhB,GAA0B5D,KAJlC;AAKHwB,sBAAAA,CAAC,EAAE,KAAK1L,QAAL,CAAc0L;AALd,qBAAP;AAOH;;AACD,qBAAKtQ,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3C,wBAAI,KAAKnH,WAAT,EAAsB;AAClB,0BAAIsK,KAAU,GAAG,KAAKC,aAAL,CAAmBsC,EAAnB,CAAjB;;AACAP,sBAAAA,KAAK,GAAG,CAAC,KAAKpN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,KAA4CwN,EAAE,GAAGvC,KAAK,CAACE,KAAvD,CAAnB,IAAqFF,KAAK,CAACvN,GAAN,GAAa,KAAKsC,UAAL,GAAkBiL,KAAK,CAACE,KAA1H,CAAR;AACA,0BAAI+C,EAAU,GAAG,KAAKvN,WAAL,CAAiB6M,EAAjB,CAAjB;AACAhD,sBAAAA,KAAK,GAAI0D,EAAE,GAAG,CAAL,GAASA,EAAT,GAAc,KAAKzN,SAAL,CAAe+J,KAAtC;AACH,qBALD,MAKO;AACHyC,sBAAAA,KAAK,GAAG,CAAC,KAAKpN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,IAA2CwN,EAAtE;AACAhD,sBAAAA,KAAK,GAAG,KAAK/J,SAAL,CAAe+J,KAAvB;AACH;;AACD,wBAAI,KAAKzM,UAAT,EAAqB;AACjBkP,sBAAAA,KAAK,IAAI,KAAKpN,SAAd;AACA,0BAAIsO,MAAc,GAAI,KAAKpP,UAAL,CAAgByL,KAAhB,GAAwB,CAAzB,GAA+B,KAAKhI,kBAAL,GAA0B,CAA9E;AACAyK,sBAAAA,KAAK,IAAIkB,MAAT;AACH;;AACDjB,oBAAAA,IAAI,GAAGD,KAAK,GAAGzC,KAAf;AACA,2BAAO;AACHgD,sBAAAA,EAAE,EAAEA,EADD;AAEHP,sBAAAA,KAAK,EAAEA,KAFJ;AAGHC,sBAAAA,IAAI,EAAEA,IAHH;AAIHjB,sBAAAA,CAAC,EAAEiB,IAAI,GAAI,KAAK3M,UAAL,CAAgB6N,OAAhB,GAA0B5D,KAJlC;AAKHwB,sBAAAA,CAAC,EAAE,KAAK1L,QAAL,CAAc0L;AALd,qBAAP;AAOH;AAhDL;;AAkDA;;AACJ,iBAAKtQ,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,wBAAQ,KAAKpJ,YAAb;AACI,uBAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzC,0BAAI,KAAKtH,WAAT,EAAsB;AAClB,4BAAIsK,KAAU,GAAG,KAAKC,aAAL,CAAmBsC,EAAnB,CAAjB;;AACAH,wBAAAA,GAAG,GAAG,CAAC,KAAKzN,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,KAA2CuN,EAAE,GAAGvC,KAAK,CAACE,KAAtD,CAAjB,IAAkFF,KAAK,CAACvN,GAAN,GAAa,KAAKuC,QAAL,GAAgBgL,KAAK,CAACE,KAArH,CAAN;AACA,4BAAI+C,EAAU,GAAG,KAAKvN,WAAL,CAAiB6M,EAAjB,CAAjB;AACA/C,wBAAAA,MAAM,GAAIyD,EAAE,GAAG,CAAL,GAASA,EAAT,GAAc,KAAKzN,SAAL,CAAegK,MAAvC;AACH,uBALD,MAKO;AACH4C,wBAAAA,GAAG,GAAG,CAAC,KAAKzN,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,IAA0CuN,EAAjE;AACA/C,wBAAAA,MAAM,GAAG,KAAKhK,SAAL,CAAegK,MAAxB;AACH;;AACD,0BAAI,KAAK1M,UAAT,EAAqB;AACjBsP,wBAAAA,GAAG,IAAI,KAAKzN,OAAZ;AACA,4BAAIuO,MAAc,GAAI,KAAKpP,UAAL,CAAgB0L,MAAhB,GAAyB,CAA1B,GAAgC,KAAKjI,kBAAL,GAA0B,CAA/E;AACA6K,wBAAAA,GAAG,IAAIc,MAAP;AACH;;AACDf,sBAAAA,MAAM,GAAGC,GAAG,GAAG5C,MAAf;AACA,6BAAO;AACH+C,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE,KAAK3L,QAAL,CAAc2L,CAJd;AAKHD,wBAAAA,CAAC,EAAEoB,MAAM,GAAI,KAAK7M,UAAL,CAAgB8N,OAAhB,GAA0B5D;AALpC,uBAAP;AAOH;;AACD,uBAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzC,0BAAI,KAAKvH,WAAT,EAAsB;AAClB,4BAAIsK,KAAU,GAAG,KAAKC,aAAL,CAAmBsC,EAAnB,CAAjB;;AACAJ,wBAAAA,MAAM,GAAG,KAAKtN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,KAA2CuN,EAAE,GAAGvC,KAAK,CAACE,KAAtD,CAAnB,IAAoFF,KAAK,CAACvN,GAAN,GAAa,KAAKuC,QAAL,GAAgBgL,KAAK,CAACE,KAAvH,CAAT;AACA,4BAAI+C,EAAU,GAAG,KAAKvN,WAAL,CAAiB6M,EAAjB,CAAjB;AACA/C,wBAAAA,MAAM,GAAIyD,EAAE,GAAG,CAAL,GAASA,EAAT,GAAc,KAAKzN,SAAL,CAAegK,MAAvC;AACH,uBALD,MAKO;AACH2C,wBAAAA,MAAM,GAAG,KAAKtN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,IAA0CuN,EAAtE;AACA/C,wBAAAA,MAAM,GAAG,KAAKhK,SAAL,CAAegK,MAAxB;AACH;;AACD,0BAAI,KAAK1M,UAAT,EAAqB;AACjBqP,wBAAAA,MAAM,IAAI,KAAKtN,UAAf;AACA,4BAAIqO,MAAc,GAAI,KAAKpP,UAAL,CAAgB0L,MAAhB,GAAyB,CAA1B,GAAgC,KAAKjI,kBAAL,GAA0B,CAA/E;AACA4K,wBAAAA,MAAM,IAAIe,MAAV;AACH;;AACDd,sBAAAA,GAAG,GAAGD,MAAM,GAAG3C,MAAf;AACA,6BAAO;AACH+C,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE,KAAK3L,QAAL,CAAc2L,CAJd;AAKHD,wBAAAA,CAAC,EAAEoB,MAAM,GAAI,KAAK7M,UAAL,CAAgB8N,OAAhB,GAA0B5D;AALpC,uBAAP;AAOA;AACH;AAjDL;AAmDH;;AACD,iBAAK/O,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB,oBAAImG,OAAe,GAAGtF,IAAI,CAAC6B,KAAL,CAAW2C,EAAE,GAAG,KAAKtN,WAArB,CAAtB;;AACA,wBAAQ,KAAKtB,UAAb;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AAAsC;AAClC,8BAAQ,KAAKhJ,YAAb;AACI,6BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzCoF,4BAAAA,GAAG,GAAG,CAAC,KAAKzN,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,IAA0CqO,OAAjE;AACAlB,4BAAAA,MAAM,GAAGC,GAAG,GAAG,KAAK5M,SAAL,CAAegK,MAA9B;AACAwD,4BAAAA,KAAK,GAAGb,MAAM,GAAI,KAAK7M,UAAL,CAAgB8N,OAAhB,GAA0B,KAAK5N,SAAL,CAAegK,MAA3D;AACA;AACH;;AACD,6BAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzCkF,4BAAAA,MAAM,GAAG,KAAKtN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAA9B,IAA0CqO,OAAtE;AACAjB,4BAAAA,GAAG,GAAGD,MAAM,GAAG,KAAK3M,SAAL,CAAegK,MAA9B;AACAwD,4BAAAA,KAAK,GAAGb,MAAM,GAAI,KAAK7M,UAAL,CAAgB8N,OAAhB,GAA0B,KAAK5N,SAAL,CAAegK,MAA3D;AACA;AACH;AAZL;;AAcAuD,sBAAAA,KAAK,GAAG,KAAKjO,QAAL,GAAkByN,EAAE,GAAG,KAAKtN,WAAX,IAA2B,KAAKO,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAAvD,CAAzB;;AACA,8BAAQ,KAAKtB,cAAb;AACI,6BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3CmG,4BAAAA,KAAK,IAAK,KAAKzN,UAAL,CAAgB6N,OAAhB,GAA0B,KAAK3N,SAAL,CAAe+J,KAAnD;AACAwD,4BAAAA,KAAK,IAAK,KAAKjP,UAAL,CAAgBqP,OAAhB,GAA0B,KAAKrP,UAAL,CAAgByL,KAApD;AACA;AACH;;AACD,6BAAK9O,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3CkG,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKzN,UAAL,CAAgB6N,OAArB,IAAgC,KAAK3N,SAAL,CAAe+J,KAAzD;AACAwD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKjP,UAAL,CAAgBqP,OAArB,IAAgC,KAAKrP,UAAL,CAAgByL,KAA1D;AACAwD,4BAAAA,KAAK,IAAI,CAAC,CAAV;AACA;AACH;AAXL;;AAaA,6BAAO;AACHR,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE+B,KAJA;AAKHhC,wBAAAA,CAAC,EAAEiC;AALA,uBAAP;AAOH;;AACD,uBAAKvS,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AAAoC;AAChC,8BAAQ,KAAKrJ,cAAb;AACI,6BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3CqF,4BAAAA,IAAI,GAAG,KAAKnN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,IAA2CsO,OAAnE;AACArB,4BAAAA,KAAK,GAAGC,IAAI,GAAG,KAAKzM,SAAL,CAAe+J,KAA9B;AACAwD,4BAAAA,KAAK,GAAGd,IAAI,GAAI,KAAK3M,UAAL,CAAgB6N,OAAhB,GAA0B,KAAK3N,SAAL,CAAe+J,KAAzD;AACAwD,4BAAAA,KAAK,IAAK,KAAKjP,UAAL,CAAgBqP,OAAhB,GAA0B,KAAKrP,UAAL,CAAgByL,KAApD;AACA;AACH;;AACD,6BAAK9O,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3CmF,4BAAAA,KAAK,GAAG,CAAC,KAAKpN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAe+J,KAAf,GAAuB,KAAKxK,UAA7B,IAA2CsO,OAAtE;AACApB,4BAAAA,IAAI,GAAGD,KAAK,GAAG,KAAKxM,SAAL,CAAe+J,KAA9B;AACAwD,4BAAAA,KAAK,GAAGd,IAAI,GAAI,KAAK3M,UAAL,CAAgB6N,OAAhB,GAA0B,KAAK3N,SAAL,CAAe+J,KAAzD;AACAwD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKjP,UAAL,CAAgBqP,OAArB,IAAgC,KAAKrP,UAAL,CAAgByL,KAA1D;AACA;AACH;AAdL;;AAgBAyD,sBAAAA,KAAK,GAAG,CAAC,KAAKrO,OAAN,GAAkB4N,EAAE,GAAG,KAAKtN,WAAX,IAA2B,KAAKO,SAAL,CAAegK,MAAf,GAAwB,KAAKxK,QAAxD,CAAzB;;AACA,8BAAQ,KAAKtB,YAAb;AACI,6BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzCgG,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAK1N,UAAL,CAAgB8N,OAArB,IAAgC,KAAK5N,SAAL,CAAegK,MAAzD;AACAwD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKlP,UAAL,CAAgBsP,OAArB,IAAgC,KAAKtP,UAAL,CAAgB0L,MAA1D;AACA;AACH;;AACD,6BAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzC+F,4BAAAA,KAAK,IAAM,KAAK1N,UAAL,CAAgB8N,OAAjB,GAA4B,KAAK5N,SAAL,CAAegK,MAArD;AACAwD,4BAAAA,KAAK,IAAK,KAAKlP,UAAL,CAAgBsP,OAAhB,GAA0B,KAAKtP,UAAL,CAAgB0L,MAApD;AACAwD,4BAAAA,KAAK,IAAI,CAAC,CAAV;AACA;AACH;AAXL;;AAaA,6BAAO;AACHT,wBAAAA,EAAE,EAAEA,EADD;AAEHN,wBAAAA,IAAI,EAAEA,IAFH;AAGHD,wBAAAA,KAAK,EAAEA,KAHJ;AAIHhB,wBAAAA,CAAC,EAAE+B,KAJA;AAKHhC,wBAAAA,CAAC,EAAEiC;AALA,uBAAP;AAOH;AA5EL;;AA8EA;AACH;AA3LL;AA6LH,SAzsCuC,CA0sCxC;;;AACAM,QAAAA,iBAAiB,CAACf,EAAD,EAAa;AAC1B,cAAItK,IAAS,GAAG,KAAKE,eAAL,CAAqBoK,EAArB,CAAhB;AACA,cAAI,CAACtK,IAAL,EACI,OAAO,IAAP;AACJ,cAAIsL,EAAe,GAAGtL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;AACA,cAAIwS,GAAS,GAAGvL,IAAI,CAAC6I,WAAL,EAAhB;AACA,cAAI2C,IAAS,GAAG;AACZlB,YAAAA,EAAE,EAAEA,EADQ;AAEZvB,YAAAA,CAAC,EAAEwC,GAAG,CAACxC,CAFK;AAGZD,YAAAA,CAAC,EAAEyC,GAAG,CAACzC;AAHK,WAAhB;;AAKA,cAAI,KAAKtL,SAAT,EAAoB;AAChBgO,YAAAA,IAAI,CAACrB,GAAL,GAAWoB,GAAG,CAACzC,CAAJ,GAASwC,EAAE,CAAC/D,MAAH,IAAa,IAAI+D,EAAE,CAACH,OAApB,CAApB;AACAK,YAAAA,IAAI,CAACtB,MAAL,GAAcqB,GAAG,CAACzC,CAAJ,GAASwC,EAAE,CAAC/D,MAAH,GAAY+D,EAAE,CAACH,OAAtC;AACH,WAHD,MAGO;AACHK,YAAAA,IAAI,CAACxB,IAAL,GAAYuB,GAAG,CAACxC,CAAJ,GAASuC,EAAE,CAAChE,KAAH,GAAWgE,EAAE,CAACJ,OAAnC;AACAM,YAAAA,IAAI,CAACzB,KAAL,GAAawB,GAAG,CAACxC,CAAJ,GAASuC,EAAE,CAAChE,KAAH,IAAY,IAAIgE,EAAE,CAACJ,OAAnB,CAAtB;AACH;;AACD,iBAAOM,IAAP;AACH,SA9tCuC,CA+tCxC;;;AACAC,QAAAA,UAAU,CAACnB,EAAD,EAAa;AACnB,cAAI,KAAK7K,QAAT,EACI,OAAO,KAAKqK,YAAL,CAAkBQ,EAAlB,CAAP,CADJ,KAEK;AACD,gBAAI,KAAKlJ,qBAAT,EACI,OAAO,KAAK0I,YAAL,CAAkBQ,EAAlB,CAAP,CADJ,KAGI,OAAO,KAAKe,iBAAL,CAAuBf,EAAvB,CAAP;AACP;AACJ,SAzuCuC,CA0uCxC;;;AACAtC,QAAAA,aAAa,CAAC0D,MAAD,EAAiB;AAC1B,cAAI,CAAC,KAAKjO,WAAV,EACI,OAAO,IAAP;AACJ,cAAIiO,MAAM,IAAI,IAAd,EACIA,MAAM,GAAG,KAAKhM,SAAd;AACJ,cAAIqI,KAAa,GAAG,CAApB;AACA,cAAIE,KAAa,GAAG,CAApB;;AACA,eAAK,IAAIqC,EAAT,IAAe,KAAK7M,WAApB,EAAiC;AAC7B,gBAAIkO,QAAQ,CAACrB,EAAD,CAAR,GAAeoB,MAAnB,EAA2B;AACvB3D,cAAAA,KAAK,IAAI,KAAKtK,WAAL,CAAiB6M,EAAjB,CAAT;AACArC,cAAAA,KAAK;AACR;AACJ;;AACD,iBAAO;AACHzN,YAAAA,GAAG,EAAEuN,KADF;AAEHE,YAAAA,KAAK,EAAEA;AAFJ,WAAP;AAIH,SA5vCuC,CA6vCxC;;;AACAjF,QAAAA,cAAc,GAAG;AACb,eAAKlE,SAAL,GAAiB,KAAKtB,SAAL,GAAiB,KAAKQ,OAAtB,GAAgC,KAAKG,QAAtD;AACH,SAhwCuC,CAiwCxC;;;AACA8E,QAAAA,cAAc,GAAG;AACb,cAAIlD,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACf,iBAAF,GAAsB,KAAtB;;AACA,cAAIe,CAAC,CAACtB,cAAF,IAAoB,IAAxB,EAA8B;AAC1B,gBAAIuB,IAAS,GAAGD,CAAC,CAACG,eAAF,CAAkBH,CAAC,CAACtB,cAApB,CAAhB;AACAsB,YAAAA,CAAC,CAACtB,cAAF,GAAmB,IAAnB;;AACA,gBAAIuB,IAAJ,EAAU;AACNlH,cAAAA,KAAK,CAACkH,IAAD,CAAL,CACK4L,EADL,CACQ,EADR,EACY;AAAEvJ,gBAAAA,KAAK,EAAE;AAAT,eADZ,EAEKuJ,EAFL,CAEQ,EAFR,EAEY;AAAEvJ,gBAAAA,KAAK,EAAE;AAAT,eAFZ,EAGKwJ,KAHL;AAIH;AACJ;;AACD9L,UAAAA,CAAC,CAACJ,YAAF;;AAEA,cAAII,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA1B,IACA,CAACnE,CAAC,CAACrB,QADP,EAEE;AACE;AACAqB,YAAAA,CAAC,CAAC+L,MAAF;AACH,WALD,MAKO,IAAI/L,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAf,IAAuBiB,CAAC,CAACf,iBAA7B,EAAgD;AAC5C,mBAAK+M,WAAL;AACH,aAFD,MAEO;AACHhM,cAAAA,CAAC,CAAC+L,MAAF;AACH;AACJ;AACJ,SA7xCuC,CA8xCxC;;;AACAlJ,QAAAA,aAAa,CAAC+F,EAAD,EAAKqD,gBAAL,EAAuB;AAChC,cAAI,KAAKzP,WAAL,CAAiB,qBAAjB,EAAwCoM,EAAxC,EAA4CqD,gBAA5C,CAAJ,EACI;AACJ,eAAKhN,iBAAL,GAAyB,IAAzB,CAHgC,CAIhC;;AACA,cAAIiN,IAAI,GAAGtD,EAAE,CAACuD,MAAH,KAAc,KAAK1J,IAA9B;;AACA,cAAI,CAACyJ,IAAL,EAAW;AACP,gBAAIE,QAAa,GAAGxD,EAAE,CAACuD,MAAvB;;AACA,mBAAOC,QAAQ,CAACC,OAAT,IAAoB,IAApB,IAA4BD,QAAQ,CAACE,MAA5C,EACIF,QAAQ,GAAGA,QAAQ,CAACE,MAApB;;AACJ,iBAAK9M,WAAL,GAAmB4M,QAAQ,CAACC,OAAT,IAAoB,IAApB,GAA2BD,QAA3B,GAAsCxD,EAAE,CAACuD,MAA5D;AACH;AACJ,SA3yCuC,CA4yCxC;;;AACArJ,QAAAA,UAAU,GAAG;AACT,cAAI9C,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAAChB,UAAF,GAAe,IAAf;;AACA,cAAIgB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA9B,EAAwC;AACpC,gBAAI,KAAKxF,QAAT,EACI,KAAKC,gBAAL,GAAwB,IAAxB;AACJoB,YAAAA,CAAC,CAAC+L,MAAF;AACH,WAJD,MAIO,IAAI/L,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAnB,EAAyB;AACrB,mBAAKiN,WAAL;AACH,aAFD,MAEO;AACHhM,cAAAA,CAAC,CAAC+L,MAAF;AACH;AACJ;;AACD,eAAKvM,WAAL,GAAmB,IAAnB;AACH;;AAEDwD,QAAAA,iBAAiB,CAAC4F,EAAD,EAAKqD,gBAAL,EAAuB;AACpC,cAAIjM,CAAC,GAAG,IAAR;AACA,cAAIA,CAAC,CAACxD,WAAF,CAAc,qBAAd,EAAqCoM,EAArC,EAAyCqD,gBAAzC,KAA8DrD,EAAE,CAAC2D,QAArE,EACI;AAEJvM,UAAAA,CAAC,CAAChB,UAAF,GAAe,IAAf;;AACA,cAAIgB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA9B,EAAwC;AACpC,gBAAInE,CAAC,CAACrB,QAAN,EACIqB,CAAC,CAACpB,gBAAF,GAAqB,IAArB;AACJoB,YAAAA,CAAC,CAAC+L,MAAF;AACH,WAJD,MAIO,IAAI/L,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAnB,EAAyB;AACrBiB,cAAAA,CAAC,CAACgM,WAAF;AACH,aAFD,MAEO;AACHhM,cAAAA,CAAC,CAAC+L,MAAF;AACH;AACJ;;AACD,eAAKvM,WAAL,GAAmB,IAAnB;AACH,SAh1CuC,CAi1CxC;;;AACA4D,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKnC,WAAL,CAAiB,KAAjB,CAAJ,EACI,KAAKrB,YAAL;AACP,SAr1CuC,CAs1CxC;;;AACA4M,QAAAA,eAAe,CAACvM,IAAD,EAAY;AACvB,cAAIsL,EAAe,GAAGtL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB,CADuB,CAEvB;;AACA,cACK,CAAC,KAAKyE,SAAN,IAAmB8N,EAAE,CAAChE,KAAH,IAAY,KAAK/J,SAAL,CAAe+J,KAA/C,IACI,KAAK9J,SAAL,IAAkB8N,EAAE,CAAC/D,MAAH,IAAa,KAAKhK,SAAL,CAAegK,MAFtD,EAGE;AACE,gBAAI,CAAC,KAAK9J,WAAV,EACI,KAAKA,WAAL,GAAmB,EAAnB;AACJ,gBAAIjD,GAAG,GAAG,KAAKgD,SAAL,GAAiB8N,EAAE,CAAC/D,MAApB,GAA6B+D,EAAE,CAAChE,KAA1C;;AACA,gBAAI,KAAK7J,WAAL,CAAiBuC,IAAI,CAACoM,OAAtB,KAAkC5R,GAAtC,EAA2C;AACvC,mBAAKiD,WAAL,CAAiBuC,IAAI,CAACoM,OAAtB,IAAiC5R,GAAjC;;AACA,mBAAK2G,cAAL,GAFuC,CAGvC;AACA;AACA;;;AACA,mBAAKqL,SAAL,GANuC,CAOvC;;AACA,kBAAI,KAAKvN,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,qBAAKF,UAAL,GAAkB,IAAlB;AACA,qBAAK0N,UAAL,CAAgB,KAAKtN,WAArB;AACA,qBAAKuN,QAAL,CAAc,KAAKzN,eAAnB,EAAoC6G,IAAI,CAAC6G,GAAL,CAAS,CAAT,EAAY,KAAKzN,gBAAL,GAA0B,IAAI0N,IAAJ,EAAD,CAAaC,OAAb,KAAyB,IAA9D,CAApC;AACH;AACJ;AACJ,WAxBsB,CAyBvB;;AACH,SAj3CuC,CAk3CxC;;;AACAd,QAAAA,WAAW,GAAG;AACV,cAAIhM,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACpF,MAAH,KAAcoF,CAAC,CAAC1B,UAAF,GAAe,CAAf,IAAoB0B,CAAC,CAACzB,YAAF,GAAiB,CAArC,IAA0CyB,CAAC,CAACxB,aAAF,GAAkB,CAA5D,IAAiEwB,CAAC,CAACvB,WAAF,GAAgB,CAA/F,CAAJ,EACI;AACJ,cAAIsO,MAAM,GAAG/M,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAAC/B,OAAhB,GAA0B+B,CAAC,CAAC5B,QAAzC;AACA,cAAI4O,GAAG,GAAG,CAAChN,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAc+H,MAA5B,GAAqCxH,CAAC,CAACP,WAAF,CAAc8H,KAApD,IAA6DvH,CAAC,CAACiN,YAAzE;AACA,cAAIC,OAAO,GAAGnH,IAAI,CAACQ,GAAL,CAASvG,CAAC,CAACjB,SAAF,GAAcgO,MAAvB,IAAiCC,GAA/C;;AACA,cAAIE,OAAJ,EAAa;AACT,gBAAIC,YAAY,GAAG,EAAnB;;AACA,oBAAQnN,CAAC,CAACpE,cAAV;AACI,mBAAK,CAAL,CADJ,CACW;;AACP,mBAAK,CAAL;AAAO;AACH,oBAAIoE,CAAC,CAACjB,SAAF,GAAcgO,MAAlB,EAA0B;AACtB/M,kBAAAA,CAAC,CAACoN,OAAF,CAAUD,YAAV,EADsB,CAEtB;AACH,iBAHD,MAGO;AACHnN,kBAAAA,CAAC,CAACqN,QAAF,CAAWF,YAAX,EADG,CAEH;AACH;;AACD;;AACJ,mBAAK,CAAL,CAXJ,CAWW;;AACP,mBAAK,CAAL;AAAO;AACH,oBAAInN,CAAC,CAACjB,SAAF,GAAcgO,MAAlB,EAA0B;AACtB/M,kBAAAA,CAAC,CAACoN,OAAF,CAAUD,YAAV;AACH,iBAFD,MAEO;AACHnN,kBAAAA,CAAC,CAACqN,QAAF,CAAWF,YAAX;AACH;;AACD;AAlBR;AAoBH,WAtBD,MAsBO,IAAInN,CAAC,CAAC1B,UAAF,IAAgB,CAAhB,IAAqB0B,CAAC,CAACzB,YAAF,IAAkB,CAAvC,IAA4CyB,CAAC,CAACxB,aAAF,IAAmB,CAA/D,IAAoEwB,CAAC,CAACvB,WAAF,IAAiB,CAAzF,EAA4F;AAC/FuB,YAAAA,CAAC,CAAC+L,MAAF;AACH;;AACD/L,UAAAA,CAAC,CAACjB,SAAF,GAAc,IAAd;AACH,SAp5CuC,CAq5CxC;;;AACAgN,QAAAA,MAAM,GAAG;AACL,cAAI/L,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAIjB,CAAC,CAAC1B,UAAF,GAAe,CAAf,IAAoB0B,CAAC,CAACzB,YAAF,GAAiB,CAArC,IAA0CyB,CAAC,CAACxB,aAAF,GAAkB,CAA5D,IAAiEwB,CAAC,CAACvB,WAAF,GAAgB,CAArF,EACI;AACJuB,UAAAA,CAAC,CAACrB,QAAF,GAAa,IAAb;;AACAqB,UAAAA,CAAC,CAAC8K,gBAAF;;AACA,cAAII,MAAc,GAAG,CAAClL,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACrD,OAAhB,GAA0BqD,CAAC,CAAClD,QAA7B,KAA0CkD,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAc+H,MAA5B,GAAqCxH,CAAC,CAACP,WAAF,CAAc8H,KAA7F,CAArB;AACA,cAAI4F,YAAoB,GAAG,EAA3B;AACAnN,UAAAA,CAAC,CAAC2M,QAAF,CAAW3M,CAAC,CAACnB,aAAb,EAA4BsO,YAA5B,EAA0CjC,MAA1C;AACH,SAj6CuC,CAk6CxC;;;AACAoC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKjM,qBAAL,IAA8B,CAA9B,IAAmC,KAAKpF,WAA5C,EACI,OAFC,CAGL;;AACA,cAAI,KAAKyD,QAAT,EAAmB;AACf,gBAAI+B,GAAW,GAAI,KAAKvF,cAAL,GAAsB,KAAKmF,qBAA5B,GAAqD,KAAKrF,cAA1D,GAA2E,KAAKA,cAAhF,GAAkG,KAAKE,cAAL,GAAsB,KAAKmF,qBAA/I;;AACA,iBAAK,IAAIK,CAAS,GAAG,KAAKxF,cAA1B,EAA0CwF,CAAC,GAAGD,GAA9C,EAAmDC,CAAC,EAApD,EAAwD;AACpD,kBAAI+J,IAAS,GAAG,KAAKtO,WAAL,CAAiBuE,CAAjB,CAAhB;;AACA,kBAAI+J,IAAJ,EAAU;AACN,qBAAKZ,mBAAL,CAAyBY,IAAzB;AACH;AACJ;;AAED,gBAAI,KAAKvP,cAAL,IAAuB,KAAKF,cAAL,GAAsB,CAAjD,EAAoD;AAAE;AAClD,kBAAI,KAAKqC,gBAAT,EAA2B;AACvB,qBAAKnC,cAAL,GAAsB,CAAtB;AACA,qBAAKD,WAAL,GAAmB,KAAnB,CAFuB,CAGvB;;AACA,qBAAKoC,gBAAL,GAAwB,KAAxB;AACH,eALD,MAKO;AACH,qBAAKpC,WAAL,GAAmB,IAAnB;;AACA,qBAAKuF,iBAAL;;AACA,qBAAKjG,YAAL,GAAoB,KAApB;;AACA,qBAAKuP,gBAAL;;AACA,oBAAI,KAAKpQ,SAAL,IAAkBd,SAAS,CAACY,IAAhC,EACI,KAAKsE,UAAL,GAAkB,KAAKD,aAAvB;AACP;AACJ,aAdD,MAcO;AACH,mBAAK3C,cAAL,IAAuB,KAAKmF,qBAA5B;AACH;AACJ,WA1BD,MA0BO;AACH,gBAAI,KAAKnF,cAAL,GAAsB,KAAKyD,SAA/B,EAA0C;AACtC,kBAAI8B,GAAW,GAAI,KAAKvF,cAAL,GAAsB,KAAKmF,qBAA5B,GAAqD,KAAK1B,SAA1D,GAAsE,KAAKA,SAA3E,GAAwF,KAAKzD,cAAL,GAAsB,KAAKmF,qBAArI;;AACA,mBAAK,IAAIK,CAAS,GAAG,KAAKxF,cAA1B,EAA0CwF,CAAC,GAAGD,GAA9C,EAAmDC,CAAC,EAApD,EAAwD;AACpD,qBAAKC,oBAAL,CAA0BD,CAA1B;AACH;;AACD,mBAAKxF,cAAL,IAAuB,KAAKmF,qBAA5B;AACH,aAND,MAMO;AACH,mBAAKpF,WAAL,GAAmB,IAAnB;;AACA,mBAAK6O,gBAAL;;AACA,kBAAI,KAAKpQ,SAAL,IAAkBd,SAAS,CAACY,IAAhC,EACI,KAAKsE,UAAL,GAAkB,KAAKD,aAAvB;AACP;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIgM,QAAAA,mBAAmB,CAACY,IAAD,EAAY;AAC3B,cAAIxL,IAAS,GAAG,KAAKE,eAAL,CAAqBsL,IAAI,CAAClB,EAA1B,CAAhB;;AACA,cAAI,CAACtK,IAAL,EAAW;AAAE;AACT,gBAAIsN,MAAe,GAAG,KAAKnQ,KAAL,CAAWoQ,IAAX,KAAoB,CAA1C;;AACA,gBAAID,MAAJ,EAAY;AACRtN,cAAAA,IAAI,GAAG,KAAK7C,KAAL,CAAWqQ,GAAX,EAAP,CADQ,CAER;AACH,aAHD,MAGO;AACHxN,cAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB,CADG,CAEH;AACH;;AACD,gBAAI,CAACkQ,MAAD,IAAW,CAAC/U,OAAO,CAACyH,IAAD,CAAvB,EAA+B;AAC3BA,cAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB;AACAkQ,cAAAA,MAAM,GAAG,KAAT;AACH;;AACD,gBAAItN,IAAI,CAACoM,OAAL,IAAgBZ,IAAI,CAAClB,EAAzB,EAA6B;AACzBtK,cAAAA,IAAI,CAACoM,OAAL,GAAeZ,IAAI,CAAClB,EAApB;AACA,kBAAIgB,EAAe,GAAGtL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;AACAuS,cAAAA,EAAE,CAACmC,cAAH,CAAkB,KAAKlQ,SAAvB;AACH;;AACDyC,YAAAA,IAAI,CAACmJ,WAAL,CAAiB,IAAInQ,IAAJ,CAASwS,IAAI,CAACzC,CAAd,EAAiByC,IAAI,CAAC1C,CAAtB,EAAyB,CAAzB,CAAjB;;AACA,iBAAK4E,cAAL,CAAoB1N,IAApB;;AACA,iBAAKpE,OAAL,CAAa+R,QAAb,CAAsB3N,IAAtB;;AACA,gBAAIsN,MAAM,IAAI,KAAKhQ,iBAAnB,EAAsC;AAClC,kBAAIsQ,MAAc,GAAG5N,IAAI,CAACI,YAAL,CAAkBnH,MAAlB,CAArB;AACA,kBAAI2U,MAAJ,EACIA,MAAM,CAACC,eAAP;AACP;;AACD7N,YAAAA,IAAI,CAAC8N,eAAL,CAAqB,KAAKlS,OAAL,CAAamS,QAAb,CAAsB9D,MAAtB,GAA+B,CAApD;AAEA,gBAAI9J,QAAkB,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAzB;AACAJ,YAAAA,IAAI,CAAC,UAAD,CAAJ,GAAmBG,QAAnB;;AACA,gBAAIA,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACuL,MAAT,GAAkBF,IAAI,CAAClB,EAAvB;AACAnK,cAAAA,QAAQ,CAAC6N,IAAT,GAAgB,IAAhB;;AACA7N,cAAAA,QAAQ,CAACgC,cAAT;AACH;;AACD,gBAAI,KAAK8L,WAAT,EAAsB;AAClB5V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAKyN,WAAN,CAAxB,EAA4CjO,IAA5C,EAAkDwL,IAAI,CAAClB,EAAL,GAAU,KAAKpO,eAAjE;AACH;AACJ,WAtCD,MAsCO,IAAI,KAAKZ,YAAL,IAAqB,KAAK2S,WAA9B,EAA2C;AAAE;AAChDjO,YAAAA,IAAI,CAACmJ,WAAL,CAAiB,IAAInQ,IAAJ,CAASwS,IAAI,CAACzC,CAAd,EAAiByC,IAAI,CAAC1C,CAAtB,EAAyB,CAAzB,CAAjB;;AACA,iBAAK4E,cAAL,CAAoB1N,IAApB,EAF8C,CAG9C;;;AACA,gBAAI,KAAKiO,WAAT,EAAsB;AAClB5V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAKyN,WAAN,CAAxB,EAA4CjO,IAA5C,EAAkDwL,IAAI,CAAClB,EAAL,GAAU,KAAKpO,eAAjE;AACH;AACJ;;AACD,eAAKwR,cAAL,CAAoB1N,IAApB;;AAEA,eAAKkO,eAAL,CAAqBlO,IAAI,CAAC,UAAD,CAAzB;;AACA,cAAI,KAAK/C,gBAAL,CAAsB2D,OAAtB,CAA8B4K,IAAI,CAAClB,EAAnC,IAAyC,CAA7C,EAAgD;AAC5C,iBAAKrN,gBAAL,CAAsB4D,IAAtB,CAA2B2K,IAAI,CAAClB,EAAhC;AACH;AACJ,SA1gDuC,CA2gDxC;;;AACA5I,QAAAA,oBAAoB,CAACgK,MAAD,EAAiB;AACjC,cAAI1L,IAAS,GAAG,KAAKpE,OAAL,CAAamS,QAAb,CAAsBrC,MAAtB,CAAhB;AACA,cAAIvL,QAAJ;;AACA,cAAI,CAACH,IAAL,EAAW;AAAE;AACTA,YAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB;AACA4C,YAAAA,IAAI,CAACoM,OAAL,GAAeV,MAAf;AACA,iBAAK9P,OAAL,CAAa+R,QAAb,CAAsB3N,IAAtB;AACAG,YAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAX;AACAJ,YAAAA,IAAI,CAAC,UAAD,CAAJ,GAAmBG,QAAnB;;AACA,gBAAIA,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACuL,MAAT,GAAkBA,MAAlB;AACAvL,cAAAA,QAAQ,CAAC6N,IAAT,GAAgB,IAAhB;;AACA7N,cAAAA,QAAQ,CAACgC,cAAT;AACH;;AACD,gBAAI,KAAK8L,WAAT,EAAsB;AAClB5V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAKyN,WAAN,CAAxB,EAA4CjO,IAA5C,EAAkD0L,MAAM,GAAG,KAAKxP,eAAhE;AACH;AACJ,WAdD,MAcO,IAAI,KAAKZ,YAAL,IAAqB,KAAK2S,WAA9B,EAA2C;AAAE;AAChDjO,YAAAA,IAAI,CAACoM,OAAL,GAAeV,MAAf;AACA,gBAAIvL,QAAJ,EACIA,QAAQ,CAACuL,MAAT,GAAkBA,MAAlB;;AACJ,gBAAI,KAAKuC,WAAT,EAAsB;AAClB5V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAKyN,WAAN,CAAxB,EAA4CjO,IAA5C,EAAkD0L,MAAM,GAAG,KAAKxP,eAAhE;AACH;AACJ;;AACD,eAAKgS,eAAL,CAAqB/N,QAArB;;AACA,cAAI,KAAKlD,gBAAL,CAAsB2D,OAAtB,CAA8B8K,MAA9B,IAAwC,CAA5C,EAA+C;AAC3C,iBAAKzO,gBAAL,CAAsB4D,IAAtB,CAA2B6K,MAA3B;AACH;AACJ;;AAEDwC,QAAAA,eAAe,CAAC/N,QAAD,EAAqB;AAChC,cAAI,CAACA,QAAL,EACI;;AACJ,cAAI,KAAKpF,YAAL,GAAoBnB,YAAY,CAACoB,IAArC,EAA2C;AACvC,gBAAIgF,IAAS,GAAGG,QAAQ,CAACqC,IAAzB;;AACA,oBAAQ,KAAKzH,YAAb;AACI,mBAAKnB,YAAY,CAACqB,MAAlB;AACIkF,gBAAAA,QAAQ,CAACE,QAAT,GAAoB,KAAKP,UAAL,IAAmBE,IAAI,CAACoM,OAA5C;AACA;;AACJ,mBAAKxS,YAAY,CAAC6G,IAAlB;AACIN,gBAAAA,QAAQ,CAACE,QAAT,GAAoB,KAAKhF,YAAL,CAAkBuF,OAAlB,CAA0BZ,IAAI,CAACoM,OAA/B,KAA2C,CAA/D;AACA;AANR;AAQH;AACJ,SAzjDuC,CA0jDxC;;;AACAsB,QAAAA,cAAc,CAAC1N,IAAD,EAAY;AACtB;AACA,cAAIuN,IAAJ;AACA,cAAIjC,EAAe,GAAGtL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;;AACA,cAAI,KAAK0E,WAAL,IAAoB,KAAKA,WAAL,CAAiBuC,IAAI,CAACoM,OAAtB,CAAxB,EAAwD;AACpDmB,YAAAA,IAAI,GAAG,KAAK9P,WAAL,CAAiBuC,IAAI,CAACoM,OAAtB,CAAP;AACH,WAFD,MAEO;AACH,gBAAI,KAAKpP,WAAL,GAAmB,CAAvB,EACIsO,EAAE,CAACmC,cAAH,CAAkB,KAAKlQ,SAAvB,EADJ,KAGIgQ,IAAI,GAAG,KAAK/P,SAAL,GAAiB,KAAKD,SAAL,CAAegK,MAAhC,GAAyC,KAAKhK,SAAL,CAAe+J,KAA/D;AACP;;AACD,cAAIiG,IAAJ,EAAU;AACN,gBAAI,KAAK/P,SAAT,EACI8N,EAAE,CAAC/D,MAAH,GAAYgG,IAAZ,CADJ,KAGIjC,EAAE,CAAChE,KAAH,GAAWiG,IAAX;AACP;AACJ;AACD;AACJ;AACA;AACA;;;AACIY,QAAAA,cAAc,CAACC,YAAD,EAAoB;AAC9B,cAAIpO,IAAS,GAAGqO,KAAK,CAACD,YAAD,CAAL,GAAsBA,YAAtB,GAAqC,KAAKlO,eAAL,CAAqBkO,YAArB,CAArD;AACA,cAAI7C,GAAQ,GAAG,KAAKE,UAAL,CAAgBzL,IAAI,CAACoM,OAArB,CAAf;AACApM,UAAAA,IAAI,CAACmJ,WAAL,CAAiBoC,GAAG,CAACxC,CAArB,EAAwBwC,GAAG,CAACzC,CAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIwF,QAAAA,eAAe,CAACC,IAAD,EAAY7N,IAAZ,EAA2B;AACtC,cAAIX,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;;AACJ,cAAI,CAACwN,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACtBA,YAAAA,IAAI,GAAG,CAACA,IAAD,CAAP;AACH;;AACD,cAAI7N,IAAI,IAAI,IAAZ,EAAkB;AACdX,YAAAA,CAAC,CAAC1E,YAAF,GAAiBkT,IAAjB;AACH,WAFD,MAEO;AACH,gBAAI7C,MAAJ,EAAoB/K,GAApB;;AACA,gBAAID,IAAJ,EAAU;AACN,mBAAK,IAAIe,CAAS,GAAG8M,IAAI,CAACtE,MAAL,GAAc,CAAnC,EAAsCxI,CAAC,IAAI,CAA3C,EAA8CA,CAAC,EAA/C,EAAmD;AAC/CiK,gBAAAA,MAAM,GAAG6C,IAAI,CAAC9M,CAAD,CAAb;AACAd,gBAAAA,GAAG,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB8K,MAAvB,CAAN;;AACA,oBAAI/K,GAAG,GAAG,CAAV,EAAa;AACTZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAewF,IAAf,CAAoB6K,MAApB;AACH;AACJ;AACJ,aARD,MAQO;AACH,mBAAK,IAAIjK,CAAS,GAAG8M,IAAI,CAACtE,MAAL,GAAc,CAAnC,EAAsCxI,CAAC,IAAI,CAA3C,EAA8CA,CAAC,EAA/C,EAAmD;AAC/CiK,gBAAAA,MAAM,GAAG6C,IAAI,CAAC9M,CAAD,CAAb;AACAd,gBAAAA,GAAG,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB8K,MAAvB,CAAN;;AACA,oBAAI/K,GAAG,IAAI,CAAX,EAAc;AACVZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH;AACJ;AACJ;AACJ;;AACDZ,UAAAA,CAAC,CAACzE,YAAF,GAAiB,IAAjB;;AACAyE,UAAAA,CAAC,CAACJ,YAAF;AACH;AACD;AACJ;AACA;AACA;;;AACI+O,QAAAA,eAAe,GAAG;AACd,iBAAO,KAAKrT,YAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIsT,QAAAA,eAAe,CAACjD,MAAD,EAAiB;AAC5B,iBAAO,KAAKrQ,YAAL,IAAqB,KAAKA,YAAL,CAAkBuF,OAAlB,CAA0B8K,MAA1B,KAAqC,CAAjE;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIkD,QAAAA,UAAU,CAACL,IAAD,EAAY;AAClB,cAAI,CAAC,KAAKvN,WAAL,EAAL,EACI;;AACJ,cAAI,CAACwN,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACtBA,YAAAA,IAAI,GAAG,CAACA,IAAD,CAAP;AACH;;AACD,eAAK,IAAI9M,CAAS,GAAG,CAAhB,EAAmBD,GAAW,GAAG+M,IAAI,CAACtE,MAA3C,EAAmDxI,CAAC,GAAGD,GAAvD,EAA4DC,CAAC,EAA7D,EAAiE;AAC7D,gBAAIiK,MAAc,GAAG6C,IAAI,CAAC9M,CAAD,CAAzB;AACA,gBAAIzB,IAAS,GAAG,KAAKE,eAAL,CAAqBwL,MAArB,CAAhB;AACA,gBAAI1L,IAAJ,EACI3H,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAKyN,WAAN,CAAxB,EAA4CjO,IAA5C,EAAkD0L,MAAM,GAAG,KAAKxP,eAAhE;AACP;AACJ;AACD;AACJ;AACA;;;AACIsQ,QAAAA,SAAS,GAAG;AACR,cAAI,CAAC,KAAKxL,WAAL,EAAL,EACI;AACJ,eAAKD,QAAL,GAAgB,KAAKA,QAArB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIb,QAAAA,eAAe,CAACwL,MAAD,EAAiB;AAC5B,cAAI,KAAK9P,OAAT,EAAkB;AACd,iBAAK,IAAI6F,CAAS,GAAG,KAAK7F,OAAL,CAAamS,QAAb,CAAsB9D,MAAtB,GAA+B,CAApD,EAAuDxI,CAAC,IAAI,CAA5D,EAA+DA,CAAC,EAAhE,EAAoE;AAChE,kBAAIzB,IAAS,GAAG,KAAKpE,OAAL,CAAamS,QAAb,CAAsBtM,CAAtB,CAAhB;AACA,kBAAIzB,IAAI,CAACoM,OAAL,IAAgBV,MAApB,EACI,OAAO1L,IAAP;AACP;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACI6O,QAAAA,eAAe,GAAG;AACd,cAAI7O,IAAJ;AACA,cAAI8H,MAAa,GAAG,EAApB;;AACA,eAAK,IAAIrG,CAAS,GAAG,KAAK7F,OAAL,CAAamS,QAAb,CAAsB9D,MAAtB,GAA+B,CAApD,EAAuDxI,CAAC,IAAI,CAA5D,EAA+DA,CAAC,EAAhE,EAAoE;AAChEzB,YAAAA,IAAI,GAAG,KAAKpE,OAAL,CAAamS,QAAb,CAAsBtM,CAAtB,CAAP;;AACA,gBAAI,CAAC,KAAKvE,WAAL,CAAiB4R,IAAjB,CAAsBC,CAAC,IAAIA,CAAC,CAACzE,EAAF,IAAQtK,IAAI,CAACoM,OAAxC,CAAL,EAAuD;AACnDtE,cAAAA,MAAM,CAACjH,IAAP,CAAYb,IAAZ;AACH;AACJ;;AACD,iBAAO8H,MAAP;AACH,SAjsDuC,CAksDxC;;;AACAvG,QAAAA,iBAAiB,GAAG;AAChB,cAAI,KAAK9B,QAAT,EAAmB;AACf,gBAAIuP,GAAU,GAAG,KAAKH,eAAL,EAAjB;;AACA,iBAAK,IAAIpN,CAAS,GAAGuN,GAAG,CAAC/E,MAAJ,GAAa,CAAlC,EAAqCxI,CAAC,IAAI,CAA1C,EAA6CA,CAAC,EAA9C,EAAkD;AAC9C,kBAAIzB,IAAS,GAAGgP,GAAG,CAACvN,CAAD,CAAnB;AACA,kBAAI,KAAKlC,WAAL,IAAoBS,IAAI,CAACoM,OAAL,IAAgB,KAAK7M,WAAL,CAAiB6M,OAAzD,EACI;AACJpM,cAAAA,IAAI,CAACiP,QAAL,GAAgB,IAAhB;;AACA,mBAAK9R,KAAL,CAAW+R,GAAX,CAAelP,IAAf;;AACA,mBAAK,IAAImP,CAAS,GAAG,KAAKlS,gBAAL,CAAsBgN,MAAtB,GAA+B,CAApD,EAAuDkF,CAAC,IAAI,CAA5D,EAA+DA,CAAC,EAAhE,EAAoE;AAChE,oBAAI,KAAKlS,gBAAL,CAAsBkS,CAAtB,KAA4BnP,IAAI,CAACoM,OAArC,EAA8C;AAC1C,uBAAKnP,gBAAL,CAAsB6D,MAAtB,CAA6BqO,CAA7B,EAAgC,CAAhC;;AACA;AACH;AACJ;AACJ,aAdc,CAef;;AACH,WAhBD,MAgBO;AACH,mBAAO,KAAKvT,OAAL,CAAamS,QAAb,CAAsB9D,MAAtB,GAA+B,KAAKvK,SAA3C,EAAsD;AAClD,mBAAK0P,cAAL,CAAoB,KAAKxT,OAAL,CAAamS,QAAb,CAAsB,KAAKnS,OAAL,CAAamS,QAAb,CAAsB9D,MAAtB,GAA+B,CAArD,CAApB;AACH;AACJ;AACJ,SAztDuC,CA0tDxC;;;AACAmF,QAAAA,cAAc,CAACpP,IAAD,EAAY;AACtB;AACAA,UAAAA,IAAI,CAACqP,gBAAL;AACA,cAAIrP,IAAI,CAAC+B,OAAT,EACI/B,IAAI,CAAC+B,OAAL;AACJ/B,UAAAA,IAAI,GAAG,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIsP,QAAAA,UAAU,CAAC5D,MAAD,EAAiB6D,QAAjB,EAAqCC,OAArC,EAAsD;AAC5D,cAAIzP,CAAM,GAAG,IAAb;AAEA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAD,IAAoBjB,CAAC,CAACpF,MAAtB,IAAgC,CAACoF,CAAC,CAACN,QAAvC,EACI,OAAOwB,OAAO,CAACC,KAAR,CAAc,4CAAd,CAAP;AAEJ,cAAI,CAACqO,QAAL,EACI,OAAOtO,OAAO,CAACC,KAAR,CAAc,oHAAd,CAAP;AAEJ,cAAInB,CAAC,CAACpC,aAAN,EACI,OAAOsD,OAAO,CAACwO,IAAR,CAAa,iDAAb,CAAP;AAEJ,cAAIzP,IAAS,GAAGD,CAAC,CAACG,eAAF,CAAkBwL,MAAlB,CAAhB;AACA,cAAIvL,QAAJ;;AACA,cAAI,CAACH,IAAL,EAAW;AACPuP,YAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA;AACH,WAHD,MAGO;AACHvL,YAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAX;AACH;;AACDL,UAAAA,CAAC,CAACpC,aAAF,GAAkB,IAAlB;AACAoC,UAAAA,CAAC,CAACnC,SAAF,GAAc2R,QAAd;AACAxP,UAAAA,CAAC,CAAClC,WAAF,GAAgBmC,IAAhB;AACAD,UAAAA,CAAC,CAACjC,gBAAF,GAAqBkC,IAAI,CAACoC,QAA1B;AACArC,UAAAA,CAAC,CAAChC,kBAAF,GAAuBiC,IAAI,CAACqC,KAA5B;AACA,cAAIqN,SAAiB,GAAG3P,CAAC,CAAC7C,WAAF,CAAc6C,CAAC,CAAC7C,WAAF,CAAc+M,MAAd,GAAuB,CAArC,EAAwCK,EAAhE;AACA,cAAIqF,eAAwB,GAAGxP,QAAQ,CAACE,QAAxC;AACAF,UAAAA,QAAQ,CAACyP,OAAT,CAAiBJ,OAAjB,EAA0B,MAAM;AAC5B;AACA,gBAAIK,KAAJ;;AACA,gBAAIH,SAAS,GAAG3P,CAAC,CAACL,SAAF,GAAc,CAA9B,EAAiC;AAC7BmQ,cAAAA,KAAK,GAAGH,SAAS,GAAG,CAApB;AACH;;AACD,gBAAIG,KAAK,IAAI,IAAb,EAAmB;AACf,kBAAIC,OAAY,GAAG/P,CAAC,CAAC+J,YAAF,CAAe+F,KAAf,CAAnB;;AACA9P,cAAAA,CAAC,CAAC7C,WAAF,CAAc2D,IAAd,CAAmBiP,OAAnB;AACA,kBAAI/P,CAAC,CAACN,QAAN,EACIM,CAAC,CAAC6K,mBAAF,CAAsBkF,OAAtB,EADJ,KAGI/P,CAAC,CAAC2B,oBAAF,CAAuBmO,KAAvB;AACP,aAPD,MAQI9P,CAAC,CAACL,SAAF;;AACJ,gBAAIK,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAACqB,MAAnC,EAA2C;AACvC,kBAAI0U,eAAJ,EAAqB;AACjB5P,gBAAAA,CAAC,CAAC5E,WAAF,GAAgB,CAAC,CAAjB;AACH,eAFD,MAEO,IAAI4E,CAAC,CAAC5E,WAAF,GAAgB,CAAhB,IAAqB,CAAzB,EAA4B;AAC/B4E,gBAAAA,CAAC,CAAC5E,WAAF;AACH;AACJ,aAND,MAMO,IAAI4E,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAAC6G,IAA/B,IAAuCV,CAAC,CAAC1E,YAAF,CAAe4O,MAA1D,EAAkE;AACrE,kBAAItJ,GAAW,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB8K,MAAvB,CAAlB;;AACA,kBAAI/K,GAAG,IAAI,CAAX,EAAc;AACVZ,gBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH,eAJoE,CAKrE;;;AACA,mBAAK,IAAIc,CAAS,GAAG1B,CAAC,CAAC1E,YAAF,CAAe4O,MAAf,GAAwB,CAA7C,EAAgDxI,CAAC,IAAI,CAArD,EAAwDA,CAAC,EAAzD,EAA6D;AACzD,oBAAI6I,EAAU,GAAGvK,CAAC,CAAC1E,YAAF,CAAeoG,CAAf,CAAjB;AACA,oBAAI6I,EAAE,IAAIoB,MAAV,EACI3L,CAAC,CAAC1E,YAAF,CAAeoG,CAAf;AACP;AACJ;;AACD,gBAAI1B,CAAC,CAACtC,WAAN,EAAmB;AACf,kBAAIsC,CAAC,CAACtC,WAAF,CAAciO,MAAd,CAAJ,EACI,OAAO3L,CAAC,CAACtC,WAAF,CAAciO,MAAd,CAAP;AACJ,kBAAIqE,aAAkB,GAAG,EAAzB;AACA,kBAAIxC,IAAJ;;AACA,mBAAK,IAAIjD,EAAT,IAAevK,CAAC,CAACtC,WAAjB,EAA8B;AAC1B8P,gBAAAA,IAAI,GAAGxN,CAAC,CAACtC,WAAF,CAAc6M,EAAd,CAAP;AACA,oBAAI0F,QAAgB,GAAGrE,QAAQ,CAACrB,EAAD,CAA/B;AACAyF,gBAAAA,aAAa,CAACC,QAAQ,IAAIA,QAAQ,IAAItE,MAAZ,GAAqB,CAArB,GAAyB,CAA7B,CAAT,CAAb,GAAyD6B,IAAzD;AACH;;AACDxN,cAAAA,CAAC,CAACtC,WAAF,GAAgBsS,aAAhB;AACH,aA5C2B,CA6C5B;;;AACA,gBAAIE,GAAW,GAAG,KAAlB;AACA,gBAAIC,GAAJ,EAAsBC,MAAtB;;AACA,iBAAK,IAAI1O,CAAS,GAAGoO,KAAK,IAAI,IAAT,GAAgBA,KAAhB,GAAwBH,SAA7C,EAAwDjO,CAAC,IAAIiK,MAAM,GAAG,CAAtE,EAAyEjK,CAAC,EAA1E,EAA8E;AAC1EzB,cAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkBuB,CAAlB,CAAP;;AACA,kBAAIzB,IAAJ,EAAU;AACN,oBAAIoQ,OAAY,GAAGrQ,CAAC,CAAC+J,YAAF,CAAerI,CAAC,GAAG,CAAnB,CAAnB;;AACAyO,gBAAAA,GAAG,GAAGpX,KAAK,CAACkH,IAAD,CAAL,CACD4L,EADC,CACEqE,GADF,EACO;AAAE7N,kBAAAA,QAAQ,EAAE,IAAIpJ,IAAJ,CAASoX,OAAO,CAACrH,CAAjB,EAAoBqH,OAAO,CAACtH,CAA5B,EAA+B,CAA/B;AAAZ,iBADP,CAAN;;AAGA,oBAAIrH,CAAC,IAAIiK,MAAM,GAAG,CAAlB,EAAqB;AACjByE,kBAAAA,MAAM,GAAG,IAAT;AACAD,kBAAAA,GAAG,CAACG,IAAJ,CAAS,MAAM;AACXtQ,oBAAAA,CAAC,CAACpC,aAAF,GAAkB,KAAlB;AACA4R,oBAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA,2BAAO3L,CAAC,CAACnC,SAAT;AACH,mBAJD;AAKH;;AACDsS,gBAAAA,GAAG,CAACrE,KAAJ;AACH;AACJ;;AACD,gBAAI,CAACsE,MAAL,EAAa;AACTpQ,cAAAA,CAAC,CAACpC,aAAF,GAAkB,KAAlB;AACA4R,cAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA3L,cAAAA,CAAC,CAACnC,SAAF,GAAc,IAAd;AACH;AACJ,WAvED,EAuEG,IAvEH;AAwEH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI8O,QAAAA,QAAQ,CAAChB,MAAD,EAAiBwB,YAAoB,GAAG,EAAxC,EAA4CjC,MAAc,GAAG,IAA7D,EAAmEqF,UAAmB,GAAG,KAAzF,EAAgG;AACpG,cAAIvQ,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,CAAc,KAAd,CAAL,EACI,OAHgG,CAIpG;;AACA,cAAIkM,YAAY,IAAI,IAApB,EAA4B;AACxBA,YAAAA,YAAY,GAAG,EAAf,CADJ,KAEK,IAAIA,YAAY,GAAG,CAAnB,EACDA,YAAY,GAAG,CAAf;AACJ,cAAIxB,MAAM,GAAG,CAAb,EACIA,MAAM,GAAG,CAAT,CADJ,KAEK,IAAIA,MAAM,IAAI3L,CAAC,CAACL,SAAhB,EACDgM,MAAM,GAAG3L,CAAC,CAACL,SAAF,GAAc,CAAvB,CAZgG,CAapG;;AACA,cAAI,CAACK,CAAC,CAACN,QAAH,IAAeM,CAAC,CAACvD,OAAjB,IAA4BuD,CAAC,CAACvD,OAAF,CAAU8E,OAA1C,EACIvB,CAAC,CAACvD,OAAF,CAAU+T,YAAV;AAEJ,cAAIhF,GAAG,GAAGxL,CAAC,CAAC0L,UAAF,CAAaC,MAAb,CAAV;;AACA,cAAI,CAACH,GAAL,EAAU;AACN,mBAAOrS,GAAG,IAAI+H,OAAO,CAACC,KAAR,CAAc,aAAd,EAA6BwK,MAA7B,CAAd;AACH;;AACD,cAAI8E,OAAJ,EAAqBC,OAArB;;AAEA,kBAAQ1Q,CAAC,CAACpE,cAAV;AACI,iBAAK,CAAL;AAAO;AACH6U,cAAAA,OAAO,GAAGjF,GAAG,CAACvB,IAAd;AACA,kBAAIiB,MAAM,IAAI,IAAd,EACIuF,OAAO,IAAIzQ,CAAC,CAACP,WAAF,CAAc8H,KAAd,GAAsB2D,MAAjC,CADJ,KAGIuF,OAAO,IAAIzQ,CAAC,CAAClD,QAAb;AACJ0O,cAAAA,GAAG,GAAG,IAAIvS,IAAJ,CAASwX,OAAT,EAAkB,CAAlB,EAAqB,CAArB,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHA,cAAAA,OAAO,GAAGjF,GAAG,CAACxB,KAAJ,GAAYhK,CAAC,CAACP,WAAF,CAAc8H,KAApC;AACA,kBAAI2D,MAAM,IAAI,IAAd,EACIuF,OAAO,IAAIzQ,CAAC,CAACP,WAAF,CAAc8H,KAAd,GAAsB2D,MAAjC,CADJ,KAGIuF,OAAO,IAAIzQ,CAAC,CAACpD,SAAb;AACJ4O,cAAAA,GAAG,GAAG,IAAIvS,IAAJ,CAASwX,OAAO,GAAGzQ,CAAC,CAAClE,UAAF,CAAayL,KAAhC,EAAuC,CAAvC,EAA0C,CAA1C,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHmJ,cAAAA,OAAO,GAAGlF,GAAG,CAACpB,GAAd;AACA,kBAAIc,MAAM,IAAI,IAAd,EACIwF,OAAO,IAAI1Q,CAAC,CAACP,WAAF,CAAc+H,MAAd,GAAuB0D,MAAlC,CADJ,KAGIwF,OAAO,IAAI1Q,CAAC,CAACrD,OAAb;AACJ6O,cAAAA,GAAG,GAAG,IAAIvS,IAAJ,CAAS,CAAT,EAAY,CAACyX,OAAb,EAAsB,CAAtB,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHA,cAAAA,OAAO,GAAGlF,GAAG,CAACrB,MAAJ,GAAanK,CAAC,CAACP,WAAF,CAAc+H,MAArC;AACA,kBAAI0D,MAAM,IAAI,IAAd,EACIwF,OAAO,IAAI1Q,CAAC,CAACP,WAAF,CAAc+H,MAAd,GAAuB0D,MAAlC,CADJ,KAGIwF,OAAO,IAAI1Q,CAAC,CAACnD,UAAb;AACJ2O,cAAAA,GAAG,GAAG,IAAIvS,IAAJ,CAAS,CAAT,EAAY,CAACyX,OAAD,GAAW1Q,CAAC,CAAClE,UAAF,CAAa0L,MAApC,EAA4C,CAA5C,CAAN;AACA;AAhCR;;AAkCA,cAAImJ,OAAY,GAAG3Q,CAAC,CAACnE,OAAF,CAAUiN,WAAV,EAAnB;AACA6H,UAAAA,OAAO,GAAG5K,IAAI,CAACQ,GAAL,CAASvG,CAAC,CAACvC,SAAF,GAAckT,OAAO,CAAC5H,CAAtB,GAA0B4H,OAAO,CAAC3H,CAA3C,CAAV;AAEA,cAAI4H,UAAU,GAAG5Q,CAAC,CAACvC,SAAF,GAAc+N,GAAG,CAACzC,CAAlB,GAAsByC,GAAG,CAACxC,CAA3C;AACA,cAAI6H,SAAS,GAAG9K,IAAI,CAACQ,GAAL,CAAS,CAACvG,CAAC,CAAChB,UAAF,IAAgB,IAAhB,GAAuBgB,CAAC,CAAChB,UAAzB,GAAsC2R,OAAvC,IAAkDC,UAA3D,IAAyE,EAAzF,CA7DoG,CA8DpG;AAEA;;AACA,cAAIC,SAAJ,EAAe;AACX7Q,YAAAA,CAAC,CAACxD,WAAF,CAAcsU,cAAd,CAA6BtF,GAA7B,EAAkC2B,YAAlC;;AACAnN,YAAAA,CAAC,CAACd,eAAF,GAAoByM,MAApB;AACA3L,YAAAA,CAAC,CAACb,gBAAF,GAAuB,IAAI0N,IAAJ,EAAD,CAAaC,OAAb,KAAyB,IAA1B,GAAkCK,YAAvD,CAHW,CAIX;;AACAnN,YAAAA,CAAC,CAACZ,WAAF,GAAgBY,CAAC,CAAC+Q,YAAF,CAAe,MAAM;AACjC,kBAAI,CAAC/Q,CAAC,CAACpB,gBAAP,EAAyB;AACrBoB,gBAAAA,CAAC,CAACrB,QAAF,GAAaqB,CAAC,CAACpB,gBAAF,GAAqB,KAAlC;AACH;;AACDoB,cAAAA,CAAC,CAAChB,UAAF,GACIgB,CAAC,CAACd,eAAF,GACAc,CAAC,CAACb,gBAAF,GACAa,CAAC,CAACZ,WAAF,GACA,IAJJ,CAJiC,CASjC;;AACA,kBAAImR,UAAJ,EAAgB;AACZ;AACA,oBAAItQ,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkBwL,MAAlB,CAAX;;AACA,oBAAI1L,IAAJ,EAAU;AACNlH,kBAAAA,KAAK,CAACkH,IAAD,CAAL,CACK4L,EADL,CACQ,EADR,EACY;AAAEvJ,oBAAAA,KAAK,EAAE;AAAT,mBADZ,EAEKuJ,EAFL,CAEQ,EAFR,EAEY;AAAEvJ,oBAAAA,KAAK,EAAE;AAAT,mBAFZ,EAGKwJ,KAHL;AAIH;AACJ;AACJ,aApBe,EAoBbqB,YAAY,GAAG,EApBF,CAAhB;;AAsBA,gBAAIA,YAAY,IAAI,CAApB,EAAuB;AACnBnN,cAAAA,CAAC,CAACJ,YAAF;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACIkL,QAAAA,gBAAgB,GAAG;AACf,cAAI9K,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACnB,aAAF,GAAkB,IAAlB;AACA,cAAI4M,IAAJ,EAAeuF,MAAf;AAEA,cAAIhR,CAAC,CAACN,QAAN,EACIM,CAAC,CAACsJ,YAAF;AAEJ,cAAIC,IAAJ,EAAkBC,MAAlB,EAAkCC,OAAlC,EAAmDC,KAAnD;AACAH,UAAAA,IAAI,GAAGvJ,CAAC,CAAC/B,OAAT;AACAuL,UAAAA,MAAM,GAAGxJ,CAAC,CAAC9B,SAAX;AACAuL,UAAAA,OAAO,GAAGzJ,CAAC,CAAC7B,UAAZ;AACAuL,UAAAA,KAAK,GAAG1J,CAAC,CAAC5B,QAAV;AAEA,cAAI0L,QAAiB,GAAG,KAAxB;;AACA,eAAK,IAAIpI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1B,CAAC,CAACnE,OAAF,CAAUmS,QAAV,CAAmB9D,MAAvB,IAAiC,CAACJ,QAAlD,EAA4DpI,CAAC,IAAI1B,CAAC,CAAC/C,WAAnE,EAAgF;AAC5EwO,YAAAA,IAAI,GAAGzL,CAAC,CAACN,QAAF,GAAaM,CAAC,CAAC7C,WAAF,CAAcuE,CAAd,CAAb,GAAgC1B,CAAC,CAACsL,iBAAF,CAAoB5J,CAApB,CAAvC;;AACA,gBAAI+J,IAAJ,EAAU;AACNuF,cAAAA,MAAM,GAAGhR,CAAC,CAACvC,SAAF,GAAe,CAACgO,IAAI,CAACrB,GAAL,GAAWqB,IAAI,CAACtB,MAAjB,IAA2B,CAA1C,GAAgD6G,MAAM,GAAG,CAACvF,IAAI,CAACxB,IAAL,GAAYwB,IAAI,CAACzB,KAAlB,IAA2B,CAA7F;;AACA,sBAAQhK,CAAC,CAACpE,cAAV;AACI,qBAAK,CAAL;AAAO;AACH,sBAAI6P,IAAI,CAACzB,KAAL,IAAcN,KAAlB,EAAyB;AACrB1J,oBAAAA,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACA,wBAAIb,KAAK,GAAGsH,MAAZ,EACIhR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ6M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACxB,IAAL,IAAaT,MAAjB,EAAyB;AACrBxJ,oBAAAA,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACA,wBAAIf,MAAM,GAAGwH,MAAb,EACIhR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ6M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACtB,MAAL,IAAeZ,IAAnB,EAAyB;AACrBvJ,oBAAAA,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACA,wBAAIhB,IAAI,GAAGyH,MAAX,EACIhR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ6M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACrB,GAAL,IAAYX,OAAhB,EAAyB;AACrBzJ,oBAAAA,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACA,wBAAId,OAAO,GAAGuH,MAAd,EACIhR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ6M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;AAhCR;AAkCH;AACJ,WAtDc,CAuDf;;;AACA2B,UAAAA,IAAI,GAAGzL,CAAC,CAACN,QAAF,GAAaM,CAAC,CAAC7C,WAAF,CAAc6C,CAAC,CAAChE,cAAF,GAAmB,CAAjC,CAAb,GAAmDgE,CAAC,CAACsL,iBAAF,CAAoBtL,CAAC,CAACL,SAAF,GAAc,CAAlC,CAA1D;;AACA,cAAI8L,IAAI,IAAIA,IAAI,CAAClB,EAAL,IAAWvK,CAAC,CAACL,SAAF,GAAc,CAArC,EAAwC;AACpCqR,YAAAA,MAAM,GAAGhR,CAAC,CAACvC,SAAF,GAAe,CAACgO,IAAI,CAACrB,GAAL,GAAWqB,IAAI,CAACtB,MAAjB,IAA2B,CAA1C,GAAgD6G,MAAM,GAAG,CAACvF,IAAI,CAACxB,IAAL,GAAYwB,IAAI,CAACzB,KAAlB,IAA2B,CAA7F;;AACA,oBAAQhK,CAAC,CAACpE,cAAV;AACI,mBAAK,CAAL;AAAO;AACH,oBAAI4N,MAAM,GAAGwH,MAAb,EACIhR,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIb,KAAK,GAAGsH,MAAZ,EACIhR,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAId,OAAO,GAAGuH,MAAd,EACIhR,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIhB,IAAI,GAAGyH,MAAX,EACIhR,CAAC,CAACnB,aAAF,GAAkB4M,IAAI,CAAClB,EAAvB;AACJ;AAhBR;AAkBH,WA7Ec,CA8Ef;;AACH,SApgEuC,CAqgExC;;;AACA6C,QAAAA,OAAO,CAACD,YAAoB,GAAG,EAAxB,EAA4B;AAC/B;AACA,cAAI,CAAC,KAAKlM,WAAL,EAAL,EACI;AACJ,eAAKgQ,QAAL,CAAc,KAAKnS,UAAL,GAAkB,CAAhC,EAAmCqO,YAAnC;AACH,SA3gEuC,CA4gExC;;;AACAE,QAAAA,QAAQ,CAACF,YAAoB,GAAG,EAAxB,EAA4B;AAChC;AACA,cAAI,CAAC,KAAKlM,WAAL,EAAL,EACI;AACJ,eAAKgQ,QAAL,CAAc,KAAKnS,UAAL,GAAkB,CAAhC,EAAmCqO,YAAnC;AACH,SAlhEuC,CAmhExC;;;AACA8D,QAAAA,QAAQ,CAACC,OAAD,EAAkB/D,YAAlB,EAAwC;AAC5C,cAAInN,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAIjB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EACI,OAAO0G,OAAO,CAACC,KAAR,CAAc,mEAAd,CAAP;AACJ,cAAI+P,OAAO,GAAG,CAAV,IAAeA,OAAO,IAAIlR,CAAC,CAACL,SAAhC,EACI;AACJ,cAAIK,CAAC,CAAClB,UAAF,IAAgBoS,OAApB,EACI,OATwC,CAU5C;;AACAlR,UAAAA,CAAC,CAAClB,UAAF,GAAeoS,OAAf;;AACA,cAAIlR,CAAC,CAACmR,eAAN,EAAuB;AACnB7Y,YAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACmR,eAAH,CAAxB,EAA6CD,OAA7C;AACH;;AACDlR,UAAAA,CAAC,CAAC2M,QAAF,CAAWuE,OAAX,EAAoB/D,YAApB;AACH,SApiEuC,CAqiExC;;;AACAiE,QAAAA,cAAc,CAACpQ,QAAD,EAAmB;AAC7B,cAAIhB,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAI,CAACjB,CAAC,CAAC3C,QAAP,EACI,OAAO6D,OAAO,CAACC,KAAR,CAAc,sBAAd,CAAP;AACJ,cAAI,CAACnB,CAAC,CAACkO,WAAP,EACI,OAAOhN,OAAO,CAACC,KAAR,CAAc,qBAAd,CAAP;AACJnB,UAAAA,CAAC,CAACtC,WAAF,GAAgB,EAAhB;AACA,cAAI2T,IAAS,GAAG9Y,WAAW,CAACyH,CAAC,CAAC3C,QAAH,CAA3B;AACA,cAAIkO,EAAe,GAAG8F,IAAI,CAAChR,YAAL,CAAkBrH,WAAlB,CAAtB;AACAgH,UAAAA,CAAC,CAACnE,OAAF,CAAU+R,QAAV,CAAmByD,IAAnB;;AACA,eAAK,IAAI3P,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGV,QAA5B,EAAsCU,CAAC,EAAvC,EAA2C;AACvCpJ,YAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACkO,WAAH,CAAxB,EAAyCmD,IAAzC,EAA+C3P,CAA/C;;AACA,gBAAI6J,EAAE,CAAC/D,MAAH,IAAaxH,CAAC,CAACxC,SAAF,CAAYgK,MAAzB,IAAmC+D,EAAE,CAAChE,KAAH,IAAYvH,CAAC,CAACxC,SAAF,CAAY+J,KAA/D,EAAsE;AAClEvH,cAAAA,CAAC,CAACtC,WAAF,CAAcgE,CAAd,IAAmB1B,CAAC,CAACvC,SAAF,GAAc8N,EAAE,CAAC/D,MAAjB,GAA0B+D,EAAE,CAAChE,KAAhD;AACH;AACJ;;AACD,cAAI,CAAC+J,MAAM,CAACC,IAAP,CAAYvR,CAAC,CAACtC,WAAd,EAA2BwM,MAAhC,EACIlK,CAAC,CAACtC,WAAF,GAAgB,IAAhB;AACJ2T,UAAAA,IAAI,CAAC/B,gBAAL;AACA,cAAI+B,IAAI,CAACrP,OAAT,EACIqP,IAAI,CAACrP,OAAL;AACJ,iBAAOhC,CAAC,CAACtC,WAAT;AACH;;AA9jEuC,O;;;;;iBAGH/D,YAAY,CAACQ,I;;;;;;;iBAOlC,I;;;;;;;iBAOI,I;;;;;;;iBAGYP,SAAS,CAACe,M;;;;;;;iBAmBZ,E;;;;;;;iBAOU,IAAIrC,YAAJ,E;;;;;;;iBAGZ,I;;;;;;;iBAyBH,K;;;;;;;iBAMI,K;;;;;;;iBAWD,K;;;;;;;iBAGE,C;;;;;;;iBAsBS,C;;;;;;;iBAMJ,IAAIA,YAAJ,E;;;;;;;iBAMCuB,YAAY,CAACoB,I;;;;;;;iBAOZ,IAAI3C,YAAJ,E;;;;;;;iBAKD,K;;;;;;;iBAmFR,C", "sourcesContent": ["/******************************************\n * <AUTHOR> <<EMAIL>>\n * @date 2020/12/9\n * @doc 列表组件.\n * @end\n ******************************************/\nconst { ccclass, property, disallowMultiple, menu, executionOrder, requireComponent } = _decorator;\nimport { _decorator, CCBoolean, CCFloat, CCInteger, Component, Enum, EventHandler, instantiate, isValid, Layout, Node, NodePool, Prefab, ScrollView, Size, tween, Tween, UITransform, Vec2, Vec3, Widget } from 'cc';\nimport { DEV } from 'cc/env';\nimport ListItem from './ListItem';\n\nenum TemplateType {\n    NODE = 1,\n    PREFAB = 2,\n}\n\nenum SlideType {\n    NORMAL = 1,//普通\n    ADHERING = 2,//粘附模式，将强制关闭滚动惯性\n    PAGE = 3,//页面模式，将强制关闭滚动惯性\n}\n\nenum SelectedType {\n    NONE = 0,\n    SINGLE = 1,//单选\n    MULT = 2,//多选\n}\n\n@ccclass\n@disallowMultiple()\n@menu('List')\n@requireComponent(ScrollView)\n//脚本生命周期回调的执行优先级。小于 0 的脚本将优先执行，大于 0 的脚本将最后执行。该优先级只对 onLoad, onEnable, start, update 和 lateUpdate 有效，对 onDisable 和 onDestroy 无效。\n@executionOrder(-5000)\nexport default class List extends Component {\n    //模板类型\n    @property({ type: Enum(TemplateType), tooltip: DEV && '模板类型', })\n    private templateType: TemplateType = TemplateType.NODE;\n    //模板Item（Node）\n    @property({\n        type: Node,\n        tooltip: DEV && '模板Item',\n        visible() { return this.templateType == TemplateType.NODE; }\n    })\n    tmpNode: Node = null;\n    //模板Item（Prefab）\n    @property({\n        type: Prefab,\n        tooltip: DEV && '模板Item',\n        visible() { return this.templateType == TemplateType.PREFAB; }\n    })\n    tmpPrefab: Prefab = null;\n    //滑动模式\n    @property({})\n    private _slideMode: SlideType = SlideType.NORMAL;\n    @property({\n        type: Enum(SlideType),\n        tooltip: DEV && '滑动模式'\n    })\n    set slideMode(val: SlideType) {\n        this._slideMode = val;\n    }\n    get slideMode() {\n        return this._slideMode;\n    }\n    //翻页作用距离\n    @property({\n        type: CCFloat,\n        range: [0, 1, .1],\n        tooltip: DEV && '翻页作用距离',\n        slide: true,\n        visible() { return this._slideMode == SlideType.PAGE; }\n    })\n    public pageDistance: number = .3;\n    //页面改变事件\n    @property({\n        type: EventHandler,\n        tooltip: DEV && '页面改变事件',\n        visible() { return this._slideMode == SlideType.PAGE; }\n    })\n    private pageChangeEvent: EventHandler = new EventHandler();\n    //是否为虚拟列表（动态列表）\n    @property({})\n    private _virtual: boolean = true;\n    @property({\n        type: CCBoolean,\n        tooltip: DEV && '是否为虚拟列表（动态列表）'\n    })\n    set virtual(val: boolean) {\n        if (val != null)\n            this._virtual = val;\n        if (!DEV && this._numItems != 0) {\n            this._onScrolling();\n        }\n    }\n    get virtual() {\n        return this._virtual;\n    }\n    //是否为循环列表\n    @property({\n        tooltip: DEV && '是否为循环列表',\n        visible() {\n            let val: boolean = /*this.virtual &&*/ this.slideMode == SlideType.NORMAL;\n            if (!val)\n                this.cyclic = false;\n            return val;\n        }\n    })\n    public cyclic: boolean = false;\n    //缺省居中\n    @property({\n        tooltip: DEV && 'Item数量不足以填满Content时，是否居中显示Item（不支持Grid布局）',\n        visible() { return this.virtual; }\n    })\n    public lackCenter: boolean = false;\n    //缺省可滑动\n    @property({\n        tooltip: DEV && 'Item数量不足以填满Content时，是否可滑动',\n        visible() {\n            let val: boolean = this.virtual && !this.lackCenter;\n            if (!val)\n                this.lackSlide = false;\n            return val;\n        }\n    })\n    public lackSlide: boolean = false;\n    //刷新频率\n    @property({ type: CCInteger })\n    private _updateRate: number = 0;\n    @property({\n        type: CCInteger,\n        range: [0, 6, 1],\n        tooltip: DEV && '刷新频率（值越大刷新频率越低、性能越高）',\n        slide: true,\n    })\n    set updateRate(val: number) {\n        if (val >= 0 && val <= 6) {\n            this._updateRate = val;\n        }\n    }\n    get updateRate() {\n        return this._updateRate;\n    }\n    //分帧渲染（每帧渲染的Item数量（<=0时关闭分帧渲染））\n    @property({\n        type: CCInteger,\n        range: [0, 12, 1],\n        tooltip: DEV && '逐帧渲染时，每帧渲染的Item数量（<=0时关闭分帧渲染）',\n        slide: true,\n    })\n    public frameByFrameRenderNum: number = 0;\n    //渲染事件（渲染器）\n    @property({\n        type: EventHandler,\n        tooltip: DEV && '渲染事件（渲染器）',\n    })\n    public renderEvent: EventHandler = new EventHandler();\n    //选择模式\n    @property({\n        type: Enum(SelectedType),\n        tooltip: DEV && '选择模式'\n    })\n    public selectedMode: SelectedType = SelectedType.NONE;\n    //触发选择事件\n    @property({\n        type: EventHandler,\n        tooltip: DEV && '触发选择事件',\n        visible() { return this.selectedMode > SelectedType.NONE; }\n    })\n    public selectedEvent: EventHandler = new EventHandler();\n    @property({\n        tooltip: DEV && '是否重复响应单选事件',\n        visible() { return this.selectedMode == SelectedType.SINGLE; }\n    })\n    public repeatEventSingle: boolean = false;\n\n    //当前选择id\n    private _selectedId: number = -1;\n    private _lastSelectedId: number;\n    private multSelected: number[];\n    set selectedId(val: number) {\n        let t: any = this;\n        let item: any;\n        switch (t.selectedMode) {\n            case SelectedType.SINGLE: {\n                if (!t.repeatEventSingle && val == t._selectedId)\n                    return;\n                item = t.getItemByListId(val);\n                // if (!item && val >= 0)\n                //     return;\n                let listItem: ListItem;\n                if (t._selectedId >= 0)\n                    t._lastSelectedId = t._selectedId;\n                else //如果＜0则取消选择，把_lastSelectedId也置空吧，如果以后有特殊需求再改吧。\n                    t._lastSelectedId = null;\n                t._selectedId = val;\n                if (item) {\n                    listItem = item.getComponent(ListItem);\n                    listItem.selected = true;\n                }\n                if (t._lastSelectedId >= 0 && t._lastSelectedId != t._selectedId) {\n                    let lastItem: any = t.getItemByListId(t._lastSelectedId);\n                    if (lastItem) {\n                        lastItem.getComponent(ListItem).selected = false;\n                    }\n                }\n                if (t.selectedEvent) {\n                    EventHandler.emitEvents([t.selectedEvent], item, val % this._actualNumItems, t._lastSelectedId == null ? null : (t._lastSelectedId % this._actualNumItems));\n                }\n                break;\n            }\n            case SelectedType.MULT: {\n                item = t.getItemByListId(val);\n                if (!item)\n                    return;\n                let listItem = item.getComponent(ListItem);\n                if (t._selectedId >= 0)\n                    t._lastSelectedId = t._selectedId;\n                t._selectedId = val;\n                let bool: boolean = !listItem.selected;\n                listItem.selected = bool;\n                let sub: number = t.multSelected.indexOf(val);\n                if (bool && sub < 0) {\n                    t.multSelected.push(val);\n                } else if (!bool && sub >= 0) {\n                    t.multSelected.splice(sub, 1);\n                }\n                if (t.selectedEvent) {\n                    EventHandler.emitEvents([t.selectedEvent], item, val % this._actualNumItems, t._lastSelectedId == null ? null : (t._lastSelectedId % this._actualNumItems), bool);\n                }\n                break;\n            }\n        }\n    }\n    get selectedId() {\n        return this._selectedId;\n    }\n    private _forceUpdate: boolean = false;\n    private _align: number;\n    private _horizontalDir: number;\n    private _verticalDir: number;\n    private _startAxis: number;\n    private _alignCalcType: number;\n    public content: Node;\n    private _contentUt: UITransform;\n    private firstListId: number;\n    public displayItemNum: number;\n    private _updateDone: boolean = true;\n    private _updateCounter: number;\n    public _actualNumItems: number;\n    private _cyclicNum: number;\n    private _cyclicPos1: number;\n    private _cyclicPos2: number;\n    //列表数量\n    @property({\n        serializable: false\n    })\n    private _numItems: number = 0;\n    set numItems(val: number) {\n        let t = this;\n        if (!t.checkInited(false))\n            return;\n        if (val == null || val < 0) {\n            console.error('numItems set the wrong::', val);\n            return;\n        }\n        t._actualNumItems = t._numItems = val;\n        t._forceUpdate = true;\n\n        if (t._virtual) {\n            t._resizeContent();\n            if (t.cyclic) {\n                t._numItems = t._cyclicNum * t._numItems;\n            }\n            t._onScrolling();\n            if (!t.frameByFrameRenderNum && t.slideMode == SlideType.PAGE)\n                t.curPageNum = t.nearestListId;\n        } else {\n            if (t.cyclic) {\n                t._resizeContent();\n                t._numItems = t._cyclicNum * t._numItems;\n            }\n            let layout: Layout = t.content.getComponent(Layout);\n            if (layout) {\n                layout.enabled = true;\n            }\n            t._delRedundantItem();\n\n            t.firstListId = 0;\n            if (t.frameByFrameRenderNum > 0) {\n                //先渲染几个出来\n                let len: number = t.frameByFrameRenderNum > t._numItems ? t._numItems : t.frameByFrameRenderNum;\n                for (let n: number = 0; n < len; n++) {\n                    t._createOrUpdateItem2(n);\n                }\n                if (t.frameByFrameRenderNum < t._numItems) {\n                    t._updateCounter = t.frameByFrameRenderNum;\n                    t._updateDone = false;\n                }\n            } else {\n                for (let n: number = 0; n < t._numItems; n++) {\n                    t._createOrUpdateItem2(n);\n                }\n                t.displayItemNum = t._numItems;\n            }\n        }\n    }\n    get numItems() {\n        return this._actualNumItems;\n    }\n\n    private _inited: boolean = false;\n    private _scrollView: ScrollView;\n    get scrollView() {\n        return this._scrollView;\n    }\n    private _layout: Layout;\n    private _resizeMode: number;\n    private _topGap: number;\n    private _rightGap: number;\n    private _bottomGap: number;\n    private _leftGap: number;\n\n    private _columnGap: number;\n    private _lineGap: number;\n    private _colLineNum: number;\n\n    private _lastDisplayData: number[];\n    public displayData: any[];\n    private _pool: NodePool;\n\n    private _itemTmp: any;\n    private _itemTmpUt: UITransform;\n    private _needUpdateWidget: boolean = false;\n    private _itemSize: Size;\n    private _sizeType: boolean;\n\n    public _customSize: any;\n\n    private frameCount: number;\n    private _aniDelRuning: boolean = false;\n    private _aniDelCB: Function;\n    private _aniDelItem: any;\n    private _aniDelBeforePos: Vec2;\n    private _aniDelBeforeScale: number;\n    private viewTop: number;\n    private viewRight: number;\n    private viewBottom: number;\n    private viewLeft: number;\n\n    private _doneAfterUpdate: boolean = false;\n\n    private elasticTop: number;\n    private elasticRight: number;\n    private elasticBottom: number;\n    private elasticLeft: number;\n\n    private scrollToListId: number;\n\n    private adhering: boolean = false;\n\n    private _adheringBarrier: boolean = false;\n    private nearestListId: number;\n\n    public curPageNum: number = 0;\n    private _beganPos: number;\n    private _scrollPos: number;\n    private _curScrollIsTouch: boolean;//当前滑动是否为手动\n\n    private _scrollToListId: number;\n    private _scrollToEndTime: number;\n    private _scrollToSo: any;\n\n    private _lack: boolean;\n    private _allItemSize: number;\n    private _allItemSizeNoEdge: number;\n\n    private _scrollItem: any;//当前控制 ScrollView 滚动的 Item\n\n    private _thisNodeUt: UITransform;\n\n    //----------------------------------------------------------------------------\n\n    onLoad() {\n        this._init();\n    }\n\n    onDestroy() {\n        let t: any = this;\n        if (isValid(t._itemTmp))\n            t._itemTmp.destroy();\n        if (isValid(t.tmpNode))\n            t.tmpNode.destroy();\n        t._pool && t._pool.clear();\n    }\n\n    onEnable() {\n        // if (!EDITOR) \n        this._registerEvent();\n        this._init();\n        // 处理重新显示后，有可能上一次的动画移除还未播放完毕，导致动画卡住的问题\n        if (this._aniDelRuning) {\n            this._aniDelRuning = false;\n            if (this._aniDelItem) {\n                if (this._aniDelBeforePos) {\n                    this._aniDelItem.position = this._aniDelBeforePos;\n                    delete this._aniDelBeforePos;\n                }\n                if (this._aniDelBeforeScale) {\n                    this._aniDelItem.scale = this._aniDelBeforeScale;\n                    delete this._aniDelBeforeScale;\n                }\n                delete this._aniDelItem;\n            }\n            if (this._aniDelCB) {\n                this._aniDelCB();\n                delete this._aniDelCB;\n            }\n        }\n    }\n\n    onDisable() {\n        // if (!EDITOR) \n        this._unregisterEvent();\n    }\n    //注册事件\n    _registerEvent() {\n        let t: any = this;\n        t.node.on(Node.EventType.TOUCH_START, t._onTouchStart, t);\n        t.node.on('touch-up', t._onTouchUp, t);\n        t.node.on(Node.EventType.TOUCH_CANCEL, t._onTouchCancelled, t);\n        t.node.on('scroll-began', t._onScrollBegan, t);\n        t.node.on('scroll-ended', t._onScrollEnded, t);\n        t.node.on('scrolling', t._onScrolling, t);\n        t.node.on(Node.EventType.SIZE_CHANGED, t._onSizeChanged, t);\n    }\n    //卸载事件\n    _unregisterEvent() {\n        let t: any = this;\n        t.node.off(Node.EventType.TOUCH_START, t._onTouchStart, t);\n        t.node.off('touch-up', t._onTouchUp, t);\n        t.node.off(Node.EventType.TOUCH_CANCEL, t._onTouchCancelled, t);\n        t.node.off('scroll-began', t._onScrollBegan, t);\n        t.node.off('scroll-ended', t._onScrollEnded, t);\n        t.node.off('scrolling', t._onScrolling, t);\n        t.node.off(Node.EventType.SIZE_CHANGED, t._onSizeChanged, t);\n    }\n    //初始化各种..\n    _init() {\n        let t: any = this;\n        if (t._inited)\n            return;\n\n        t._thisNodeUt = t.node.getComponent(UITransform);\n        t._scrollView = t.node.getComponent(ScrollView);\n\n        t.content = t._scrollView.content;\n        t._contentUt = t.content.getComponent(UITransform);\n        if (!t.content) {\n            console.error(t.node.name + \"'s ScrollView unset content!\");\n            return;\n        }\n\n        t._layout = t.content.getComponent(Layout);\n\n        t._align = t._layout.type; //排列模式\n        t._resizeMode = t._layout.resizeMode; //自适应模式\n        t._startAxis = t._layout.startAxis;\n\n        t._topGap = t._layout.paddingTop; //顶边距\n        t._rightGap = t._layout.paddingRight; //右边距\n        t._bottomGap = t._layout.paddingBottom; //底边距\n        t._leftGap = t._layout.paddingLeft; //左边距\n\n        t._columnGap = t._layout.spacingX; //列距\n        t._lineGap = t._layout.spacingY; //行距\n\n        t._colLineNum; //列数或行数（非GRID模式则=1，表示单列或单行）;\n\n        t._verticalDir = t._layout.verticalDirection; //垂直排列子节点的方向\n        t._horizontalDir = t._layout.horizontalDirection; //水平排列子节点的方向\n\n        t.setTemplateItem(instantiate(t.templateType == TemplateType.PREFAB ? t.tmpPrefab : t.tmpNode));\n\n        // 特定的滑动模式处理\n        if (t._slideMode == SlideType.ADHERING || t._slideMode == SlideType.PAGE) {\n            t._scrollView.inertia = false;\n            t._scrollView._onMouseWheel = function () {\n                return;\n            };\n        }\n        if (!t.virtual)         // lackCenter 仅支持 Virtual 模式\n            t.lackCenter = false;\n\n        t._lastDisplayData = []; //最后一次刷新的数据\n        t.displayData = []; //当前数据\n        t._pool = new NodePool();    //这是个池子..\n        t._forceUpdate = false;         //是否强制更新\n        t._updateCounter = 0;           //当前分帧渲染帧数\n        t._updateDone = true;           //分帧渲染是否完成\n\n        t.curPageNum = 0;               //当前页数\n\n        if (t.cyclic || 0) {\n            t._scrollView._processAutoScrolling = this._processAutoScrolling.bind(t);\n            t._scrollView._startBounceBackIfNeeded = function () {\n                return false;\n            }\n        }\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL: {\n                switch (t._horizontalDir) {\n                    case Layout.HorizontalDirection.LEFT_TO_RIGHT:\n                        t._alignCalcType = 1;\n                        break;\n                    case Layout.HorizontalDirection.RIGHT_TO_LEFT:\n                        t._alignCalcType = 2;\n                        break;\n                }\n                break;\n            }\n            case Layout.Type.VERTICAL: {\n                switch (t._verticalDir) {\n                    case Layout.VerticalDirection.TOP_TO_BOTTOM:\n                        t._alignCalcType = 3;\n                        break;\n                    case Layout.VerticalDirection.BOTTOM_TO_TOP:\n                        t._alignCalcType = 4;\n                        break;\n                }\n                break;\n            }\n            case Layout.Type.GRID: {\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        switch (t._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM:\n                                t._alignCalcType = 3;\n                                break;\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP:\n                                t._alignCalcType = 4;\n                                break;\n                        }\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        switch (t._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT:\n                                t._alignCalcType = 1;\n                                break;\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT:\n                                t._alignCalcType = 2;\n                                break;\n                        }\n                        break;\n                }\n                break;\n            }\n        }\n        // 清空 content\n        // t.content.children.forEach((child: Node) => {\n        //     child.removeFromParent();\n        //     if (child != t.tmpNode && child.isValid)\n        //         child.destroy();\n        // });\n        t.content.removeAllChildren();\n        t._inited = true;\n    }\n    /**\n     * 为了实现循环列表，必须覆写cc.ScrollView的某些函数\n     * @param {Number} dt\n     */\n    _processAutoScrolling(dt: number) {\n\n        // ------------- scroll-view 里定义的一些常量 -------------\n        const OUT_OF_BOUNDARY_BREAKING_FACTOR = 0.05;\n        const EPSILON = 1e-4;\n        const ZERO = new Vec3();\n        const quintEaseOut = (time: number) => {\n            time -= 1;\n            return (time * time * time * time * time + 1);\n        };\n        // ------------- scroll-view 里定义的一些常量 -------------\n\n        let sv: ScrollView = this._scrollView;\n\n        const isAutoScrollBrake = sv['_isNecessaryAutoScrollBrake']();\n        const brakingFactor = isAutoScrollBrake ? OUT_OF_BOUNDARY_BREAKING_FACTOR : 1;\n        sv['_autoScrollAccumulatedTime'] += dt * (1 / brakingFactor);\n\n        let percentage = Math.min(1, sv['_autoScrollAccumulatedTime'] / sv['_autoScrollTotalTime']);\n        if (sv['_autoScrollAttenuate']) {\n            percentage = quintEaseOut(percentage);\n        }\n\n        const clonedAutoScrollTargetDelta = sv['_autoScrollTargetDelta'].clone();\n        clonedAutoScrollTargetDelta.multiplyScalar(percentage);\n        const clonedAutoScrollStartPosition = sv['_autoScrollStartPosition'].clone();\n        clonedAutoScrollStartPosition.add(clonedAutoScrollTargetDelta);\n        let reachedEnd = Math.abs(percentage - 1) <= EPSILON;\n\n        const fireEvent = Math.abs(percentage - 1) <= sv['getScrollEndedEventTiming']();\n        if (fireEvent && !sv['_isScrollEndedWithThresholdEventFired']) {\n            sv['_dispatchEvent'](ScrollView.EventType.SCROLL_ENG_WITH_THRESHOLD);\n            sv['_isScrollEndedWithThresholdEventFired'] = true;\n        }\n\n        if (sv['elastic']) {\n            const brakeOffsetPosition = clonedAutoScrollStartPosition.clone();\n            brakeOffsetPosition.subtract(sv['_autoScrollBrakingStartPosition']);\n            if (isAutoScrollBrake) {\n                brakeOffsetPosition.multiplyScalar(brakingFactor);\n            }\n            clonedAutoScrollStartPosition.set(sv['_autoScrollBrakingStartPosition']);\n            clonedAutoScrollStartPosition.add(brakeOffsetPosition);\n        } else {\n            const moveDelta = clonedAutoScrollStartPosition.clone();\n            moveDelta.subtract(sv['_getContentPosition']());\n            const outOfBoundary = sv['_getHowMuchOutOfBoundary'](moveDelta);\n            if (!outOfBoundary.equals(ZERO, EPSILON)) {\n                clonedAutoScrollStartPosition.add(outOfBoundary);\n                reachedEnd = true;\n            }\n        }\n\n        if (reachedEnd) {\n            sv['_autoScrolling'] = false;\n        }\n\n        const deltaMove = new Vec3(clonedAutoScrollStartPosition);\n        deltaMove.subtract(sv['_getContentPosition']());\n        sv['_clampDelta'](deltaMove);\n        sv['_moveContent'](deltaMove, reachedEnd);\n        sv['_dispatchEvent'](ScrollView.EventType.SCROLLING);\n\n        if (!sv['_autoScrolling']) {\n            sv['_isBouncing'] = false;\n            sv['_scrolling'] = false;\n            sv['_dispatchEvent'](ScrollView.EventType.SCROLL_ENDED);\n        }\n    }\n    //设置模板Item\n    setTemplateItem(item: any) {\n        if (!item)\n            return;\n        let t: any = this;\n        t._itemTmp = item;\n        t._itemTmpUt = item.getComponent(UITransform);\n\n        if (t._resizeMode == Layout.ResizeMode.CHILDREN)\n            t._itemSize = t._layout.cellSize;\n        else {\n            let itemUt: UITransform = item.getComponent(UITransform);\n            t._itemSize = new Size(itemUt.width, itemUt.height);\n        }\n\n        //获取ListItem，如果没有就取消选择模式\n        let com: any = item.getComponent(ListItem);\n        let remove = false;\n        if (!com)\n            remove = true;\n        // if (com) {\n        //     if (!com._btnCom && !item.getComponent(cc.Button)) {\n        //         remove = true;\n        //     }\n        // }\n        if (remove) {\n            t.selectedMode = SelectedType.NONE;\n        }\n        com = item.getComponent(Widget);\n        if (com && com.enabled) {\n            t._needUpdateWidget = true;\n        }\n        if (t.selectedMode == SelectedType.MULT)\n            t.multSelected = [];\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL:\n                t._colLineNum = 1;\n                t._sizeType = false;\n                break;\n            case Layout.Type.VERTICAL:\n                t._colLineNum = 1;\n                t._sizeType = true;\n                break;\n            case Layout.Type.GRID:\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        //计算列数\n                        let trimW: number = t._contentUt.width - t._leftGap - t._rightGap;\n                        t._colLineNum = Math.floor((trimW + t._columnGap) / (t._itemSize.width + t._columnGap));\n                        t._sizeType = true;\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        //计算行数\n                        let trimH: number = t._contentUt.height - t._topGap - t._bottomGap;\n                        t._colLineNum = Math.floor((trimH + t._lineGap) / (t._itemSize.height + t._lineGap));\n                        t._sizeType = false;\n                        break;\n                }\n                break;\n        }\n    }\n    /**\n     * 检查是否初始化\n     * @param {Boolean} printLog 是否打印错误信息\n     * @returns\n     */\n    checkInited(printLog: boolean = true) {\n        if (!this._inited) {\n            if (printLog)\n                console.error('List initialization not completed!');\n            return false;\n        }\n        return true;\n    }\n    //禁用 Layout 组件，自行计算 Content Size\n    _resizeContent() {\n        let t: any = this;\n        let result: number;\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL: {\n                if (t._customSize) {\n                    let fixed: any = t._getFixedSize(null);\n                    result = t._leftGap + fixed.val + (t._itemSize.width * (t._numItems - fixed.count)) + (t._columnGap * (t._numItems - 1)) + t._rightGap;\n                } else {\n                    result = t._leftGap + (t._itemSize.width * t._numItems) + (t._columnGap * (t._numItems - 1)) + t._rightGap;\n                }\n                break;\n            }\n            case Layout.Type.VERTICAL: {\n                if (t._customSize) {\n                    let fixed: any = t._getFixedSize(null);\n                    result = t._topGap + fixed.val + (t._itemSize.height * (t._numItems - fixed.count)) + (t._lineGap * (t._numItems - 1)) + t._bottomGap;\n                } else {\n                    result = t._topGap + (t._itemSize.height * t._numItems) + (t._lineGap * (t._numItems - 1)) + t._bottomGap;\n                }\n                break;\n            }\n            case Layout.Type.GRID: {\n                //网格模式不支持居中\n                if (t.lackCenter)\n                    t.lackCenter = false;\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        let lineNum: number = Math.ceil(t._numItems / t._colLineNum);\n                        result = t._topGap + (t._itemSize.height * lineNum) + (t._lineGap * (lineNum - 1)) + t._bottomGap;\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        let colNum: number = Math.ceil(t._numItems / t._colLineNum);\n                        result = t._leftGap + (t._itemSize.width * colNum) + (t._columnGap * (colNum - 1)) + t._rightGap;\n                        break;\n                }\n                break;\n            }\n        }\n\n        let layout: Layout = t.content.getComponent(Layout);\n        if (layout)\n            layout.enabled = false;\n\n        t._allItemSize = result;\n        t._allItemSizeNoEdge = t._allItemSize - (t._sizeType ? (t._topGap + t._bottomGap) : (t._leftGap + t._rightGap));\n\n        if (t.cyclic) {\n            let totalSize: number = (t._sizeType ? t._thisNodeUt.height : t._thisNodeUt.width);\n\n            t._cyclicPos1 = 0;\n            totalSize -= t._cyclicPos1;\n            t._cyclicNum = Math.ceil(totalSize / t._allItemSizeNoEdge) + 1;\n            let spacing: number = t._sizeType ? t._lineGap : t._columnGap;\n            t._cyclicPos2 = t._cyclicPos1 + t._allItemSizeNoEdge + spacing;\n            t._cyclicAllItemSize = t._allItemSize + (t._allItemSizeNoEdge * (t._cyclicNum - 1)) + (spacing * (t._cyclicNum - 1));\n            t._cycilcAllItemSizeNoEdge = t._allItemSizeNoEdge * t._cyclicNum;\n            t._cycilcAllItemSizeNoEdge += spacing * (t._cyclicNum - 1);\n            // cc.log('_cyclicNum ->', t._cyclicNum, t._allItemSizeNoEdge, t._allItemSize, t._cyclicPos1, t._cyclicPos2);\n        }\n\n        t._lack = !t.cyclic && t._allItemSize < (t._sizeType ? t._thisNodeUt.height : t._thisNodeUt.width);\n        let slideOffset: number = ((!t._lack || !t.lackCenter) && t.lackSlide) ? 0 : .1;\n\n        let targetWH: number = t._lack ? ((t._sizeType ? t._thisNodeUt.height : t._thisNodeUt.width) - slideOffset) : (t.cyclic ? t._cyclicAllItemSize : t._allItemSize);\n        if (targetWH < 0)\n            targetWH = 0;\n\n        if (t._sizeType) {\n            t._contentUt.height = targetWH;\n        } else {\n            t._contentUt.width = targetWH;\n        }\n\n        // cc.log('_resizeContent()  numItems =', t._numItems, '，content =', t.content);\n    }\n\n    //滚动进行时...\n    _onScrolling(ev: Event = null) {\n        if (this.frameCount == null)\n            this.frameCount = this._updateRate;\n        if (!this._forceUpdate && (ev && ev.type != 'scroll-ended') && this.frameCount > 0) {\n            this.frameCount--;\n            return;\n        } else\n            this.frameCount = this._updateRate;\n\n        if (this._aniDelRuning)\n            return;\n\n        //循环列表处理\n        if (this.cyclic) {\n            let scrollPos: any = this.content.getPosition();\n            scrollPos = this._sizeType ? scrollPos.y : scrollPos.x;\n\n            let addVal = this._allItemSizeNoEdge + (this._sizeType ? this._lineGap : this._columnGap);\n            let add: any = this._sizeType ? new Vec3(0, addVal, 0) : new Vec3(addVal, 0, 0);\n\n            let contentPos = this.content.getPosition();\n\n            switch (this._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                    if (scrollPos > -this._cyclicPos1) {\n                        contentPos.set(-this._cyclicPos2, contentPos.y, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].subtract(add);\n                        }\n                        // if (this._beganPos) {\n                        //     this._beganPos += add;\n                        // }\n                    } else if (scrollPos < -this._cyclicPos2) {\n                        contentPos.set(-this._cyclicPos1, contentPos.y, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].add(add);\n                        }\n                        // if (this._beganPos) {\n                        //     this._beganPos -= add;\n                        // }\n                    }\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                    if (scrollPos < this._cyclicPos1) {\n                        contentPos.set(this._cyclicPos2, contentPos.y, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].add(add);\n                        }\n                    } else if (scrollPos > this._cyclicPos2) {\n                        contentPos.set(this._cyclicPos1, contentPos.y, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].subtract(add);\n                        }\n                    }\n                    break;\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (scrollPos < this._cyclicPos1) {\n                        contentPos.set(contentPos.x, this._cyclicPos2, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].add(add);\n                        }\n                    } else if (scrollPos > this._cyclicPos2) {\n                        contentPos.set(contentPos.x, this._cyclicPos1, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].subtract(add);\n                        }\n                    }\n                    break;\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (scrollPos > -this._cyclicPos1) {\n                        contentPos.set(contentPos.x, -this._cyclicPos2, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].subtract(add);\n                        }\n                    } else if (scrollPos < -this._cyclicPos2) {\n                        contentPos.set(contentPos.x, -this._cyclicPos1, contentPos.z);\n                        this.content.setPosition(contentPos);\n                        if (this._scrollView.isAutoScrolling()) {\n                            this._scrollView['_autoScrollStartPosition'] = this._scrollView['_autoScrollStartPosition'].add(add);\n                        }\n                    }\n                    break;\n            }\n        }\n\n        this._calcViewPos();\n\n        let vTop: number, vRight: number, vBottom: number, vLeft: number;\n        if (this._sizeType) {\n            vTop = this.viewTop;\n            vBottom = this.viewBottom;\n        } else {\n            vRight = this.viewRight;\n            vLeft = this.viewLeft;\n        }\n\n        if (this._virtual) {\n            this.displayData = [];\n            let itemPos: any;\n\n            let curId: number = 0;\n            let endId: number = this._numItems - 1;\n\n            if (this._customSize) {\n                let breakFor: boolean = false;\n                //如果该item的位置在可视区域内，就推入displayData\n                for (; curId <= endId && !breakFor; curId++) {\n                    itemPos = this._calcItemPos(curId);\n                    switch (this._align) {\n                        case Layout.Type.HORIZONTAL:\n                            if (itemPos.right >= vLeft && itemPos.left <= vRight) {\n                                this.displayData.push(itemPos);\n                            } else if (curId != 0 && this.displayData.length > 0) {\n                                breakFor = true;\n                            }\n                            break;\n                        case Layout.Type.VERTICAL:\n                            if (itemPos.bottom <= vTop && itemPos.top >= vBottom) {\n                                this.displayData.push(itemPos);\n                            } else if (curId != 0 && this.displayData.length > 0) {\n                                breakFor = true;\n                            }\n                            break;\n                        case Layout.Type.GRID:\n                            switch (this._startAxis) {\n                                case Layout.AxisDirection.HORIZONTAL:\n                                    if (itemPos.bottom <= vTop && itemPos.top >= vBottom) {\n                                        this.displayData.push(itemPos);\n                                    } else if (curId != 0 && this.displayData.length > 0) {\n                                        breakFor = true;\n                                    }\n                                    break;\n                                case Layout.AxisDirection.VERTICAL:\n                                    if (itemPos.right >= vLeft && itemPos.left <= vRight) {\n                                        this.displayData.push(itemPos);\n                                    } else if (curId != 0 && this.displayData.length > 0) {\n                                        breakFor = true;\n                                    }\n                                    break;\n                            }\n                            break;\n                    }\n                }\n            } else {\n                let ww: number = this._itemSize.width + this._columnGap;\n                let hh: number = this._itemSize.height + this._lineGap;\n                switch (this._alignCalcType) {\n                    case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                        curId = (vLeft - this._leftGap) / ww;\n                        endId = (vRight - this._leftGap) / ww;\n                        break;\n                    case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                        curId = (-vRight - this._rightGap) / ww;\n                        endId = (-vLeft - this._rightGap) / ww;\n                        break;\n                    case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                        curId = (-vTop - this._topGap) / hh;\n                        endId = (-vBottom - this._topGap) / hh;\n                        break;\n                    case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                        curId = (vBottom - this._bottomGap) / hh;\n                        endId = (vTop - this._bottomGap) / hh;\n                        break;\n                }\n                curId = Math.floor(curId) * this._colLineNum;\n                endId = Math.ceil(endId) * this._colLineNum;\n                endId--;\n                if (curId < 0)\n                    curId = 0;\n                if (endId >= this._numItems)\n                    endId = this._numItems - 1;\n                for (; curId <= endId; curId++) {\n                    this.displayData.push(this._calcItemPos(curId));\n                }\n            }\n            this._delRedundantItem();\n            if (this.displayData.length <= 0 || !this._numItems) { //if none, delete all.\n                this._lastDisplayData = [];\n                return;\n            }\n            this.firstListId = this.displayData[0].id;\n            this.displayItemNum = this.displayData.length;\n\n            let len: number = this._lastDisplayData.length;\n\n            let haveDataChange: boolean = this.displayItemNum != len;\n            if (haveDataChange) {\n                // 如果是逐帧渲染，需要排序\n                if (this.frameByFrameRenderNum > 0) {\n                    this._lastDisplayData.sort((a, b) => { return a - b });\n                }\n                // 因List的显示数据是有序的，所以只需要判断数组长度是否相等，以及头、尾两个元素是否相等即可。\n                haveDataChange = this.firstListId != this._lastDisplayData[0] || this.displayData[this.displayItemNum - 1].id != this._lastDisplayData[len - 1];\n            }\n\n            if (this._forceUpdate || haveDataChange) {    //如果是强制更新\n                if (this.frameByFrameRenderNum > 0) {\n                    // if (this._updateDone) {\n                    // this._lastDisplayData = [];\n                    //逐帧渲染\n                    if (this._numItems > 0) {\n                        if (!this._updateDone) {\n                            this._doneAfterUpdate = true;\n                        } else {\n                            this._updateCounter = 0;\n                        }\n                        this._updateDone = false;\n                    } else {\n                        this._updateCounter = 0;\n                        this._updateDone = true;\n                    }\n                    // }\n                } else {\n                    //直接渲染\n                    this._lastDisplayData = [];\n                    // cc.log('List Display Data II::', this.displayData);\n                    for (let c = 0; c < this.displayItemNum; c++) {\n                        this._createOrUpdateItem(this.displayData[c]);\n                    }\n                    this._forceUpdate = false;\n                }\n            }\n            this._calcNearestItem();\n        }\n    }\n    //计算可视范围\n    _calcViewPos() {\n        let scrollPos: any = this.content.getPosition();\n        switch (this._alignCalcType) {\n            case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                this.elasticLeft = scrollPos.x > 0 ? scrollPos.x : 0;\n                this.viewLeft = (scrollPos.x < 0 ? -scrollPos.x : 0) - this.elasticLeft;\n\n                this.viewRight = this.viewLeft + this._thisNodeUt.width;\n                this.elasticRight = this.viewRight > this._contentUt.width ? Math.abs(this.viewRight - this._contentUt.width) : 0;\n                this.viewRight += this.elasticRight;\n                // cc.log(this.elasticLeft, this.elasticRight, this.viewLeft, this.viewRight);\n                break;\n            case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                this.elasticRight = scrollPos.x < 0 ? -scrollPos.x : 0;\n                this.viewRight = (scrollPos.x > 0 ? -scrollPos.x : 0) + this.elasticRight;\n                this.viewLeft = this.viewRight - this._thisNodeUt.width;\n                this.elasticLeft = this.viewLeft < -this._contentUt.width ? Math.abs(this.viewLeft + this._contentUt.width) : 0;\n                this.viewLeft -= this.elasticLeft;\n                // cc.log(this.elasticLeft, this.elasticRight, this.viewLeft, this.viewRight);\n                break;\n            case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                this.elasticTop = scrollPos.y < 0 ? Math.abs(scrollPos.y) : 0;\n                this.viewTop = (scrollPos.y > 0 ? -scrollPos.y : 0) + this.elasticTop;\n                this.viewBottom = this.viewTop - this._thisNodeUt.height;\n                this.elasticBottom = this.viewBottom < -this._contentUt.height ? Math.abs(this.viewBottom + this._contentUt.height) : 0;\n                this.viewBottom += this.elasticBottom;\n                // cc.log(this.elasticTop, this.elasticBottom, this.viewTop, this.viewBottom);\n                break;\n            case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                this.elasticBottom = scrollPos.y > 0 ? Math.abs(scrollPos.y) : 0;\n                this.viewBottom = (scrollPos.y < 0 ? -scrollPos.y : 0) - this.elasticBottom;\n                this.viewTop = this.viewBottom + this._thisNodeUt.height;\n                this.elasticTop = this.viewTop > this._contentUt.height ? Math.abs(this.viewTop - this._contentUt.height) : 0;\n                this.viewTop -= this.elasticTop;\n                // cc.log(this.elasticTop, this.elasticBottom, this.viewTop, this.viewBottom);\n                break;\n        }\n    }\n    //计算位置 根据id\n    _calcItemPos(id: number) {\n        let width: number, height: number, top: number, bottom: number, left: number, right: number, itemX: number, itemY: number;\n        switch (this._align) {\n            case Layout.Type.HORIZONTAL:\n                switch (this._horizontalDir) {\n                    case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            left = this._leftGap + ((this._itemSize.width + this._columnGap) * (id - fixed.count)) + (fixed.val + (this._columnGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            width = (cs > 0 ? cs : this._itemSize.width);\n                        } else {\n                            left = this._leftGap + ((this._itemSize.width + this._columnGap) * id);\n                            width = this._itemSize.width;\n                        }\n                        if (this.lackCenter) {\n                            left -= this._leftGap;\n                            let offset: number = (this._contentUt.width / 2) - (this._allItemSizeNoEdge / 2);\n                            left += offset;\n                        }\n                        right = left + width;\n                        return {\n                            id: id,\n                            left: left,\n                            right: right,\n                            x: left + (this._itemTmpUt.anchorX * width),\n                            y: this._itemTmp.y,\n                        };\n                    }\n                    case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            right = -this._rightGap - ((this._itemSize.width + this._columnGap) * (id - fixed.count)) - (fixed.val + (this._columnGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            width = (cs > 0 ? cs : this._itemSize.width);\n                        } else {\n                            right = -this._rightGap - ((this._itemSize.width + this._columnGap) * id);\n                            width = this._itemSize.width;\n                        }\n                        if (this.lackCenter) {\n                            right += this._rightGap;\n                            let offset: number = (this._contentUt.width / 2) - (this._allItemSizeNoEdge / 2);\n                            right -= offset;\n                        }\n                        left = right - width;\n                        return {\n                            id: id,\n                            right: right,\n                            left: left,\n                            x: left + (this._itemTmpUt.anchorX * width),\n                            y: this._itemTmp.y,\n                        };\n                    }\n                }\n                break;\n            case Layout.Type.VERTICAL: {\n                switch (this._verticalDir) {\n                    case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            top = -this._topGap - ((this._itemSize.height + this._lineGap) * (id - fixed.count)) - (fixed.val + (this._lineGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            height = (cs > 0 ? cs : this._itemSize.height);\n                        } else {\n                            top = -this._topGap - ((this._itemSize.height + this._lineGap) * id);\n                            height = this._itemSize.height;\n                        }\n                        if (this.lackCenter) {\n                            top += this._topGap;\n                            let offset: number = (this._contentUt.height / 2) - (this._allItemSizeNoEdge / 2);\n                            top -= offset;\n                        }\n                        bottom = top - height;\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: this._itemTmp.x,\n                            y: bottom + (this._itemTmpUt.anchorY * height),\n                        };\n                    }\n                    case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            bottom = this._bottomGap + ((this._itemSize.height + this._lineGap) * (id - fixed.count)) + (fixed.val + (this._lineGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            height = (cs > 0 ? cs : this._itemSize.height);\n                        } else {\n                            bottom = this._bottomGap + ((this._itemSize.height + this._lineGap) * id);\n                            height = this._itemSize.height;\n                        }\n                        if (this.lackCenter) {\n                            bottom -= this._bottomGap;\n                            let offset: number = (this._contentUt.height / 2) - (this._allItemSizeNoEdge / 2);\n                            bottom += offset;\n                        }\n                        top = bottom + height;\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: this._itemTmp.x,\n                            y: bottom + (this._itemTmpUt.anchorY * height),\n                        };\n                        break;\n                    }\n                }\n            }\n            case Layout.Type.GRID: {\n                let colLine: number = Math.floor(id / this._colLineNum);\n                switch (this._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL: {\n                        switch (this._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                                top = -this._topGap - ((this._itemSize.height + this._lineGap) * colLine);\n                                bottom = top - this._itemSize.height;\n                                itemY = bottom + (this._itemTmpUt.anchorY * this._itemSize.height);\n                                break;\n                            }\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                                bottom = this._bottomGap + ((this._itemSize.height + this._lineGap) * colLine);\n                                top = bottom + this._itemSize.height;\n                                itemY = bottom + (this._itemTmpUt.anchorY * this._itemSize.height);\n                                break;\n                            }\n                        }\n                        itemX = this._leftGap + ((id % this._colLineNum) * (this._itemSize.width + this._columnGap));\n                        switch (this._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                                itemX += (this._itemTmpUt.anchorX * this._itemSize.width);\n                                itemX -= (this._contentUt.anchorX * this._contentUt.width);\n                                break;\n                            }\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                                itemX += ((1 - this._itemTmpUt.anchorX) * this._itemSize.width);\n                                itemX -= ((1 - this._contentUt.anchorX) * this._contentUt.width);\n                                itemX *= -1;\n                                break;\n                            }\n                        }\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: itemX,\n                            y: itemY,\n                        };\n                    }\n                    case Layout.AxisDirection.VERTICAL: {\n                        switch (this._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                                left = this._leftGap + ((this._itemSize.width + this._columnGap) * colLine);\n                                right = left + this._itemSize.width;\n                                itemX = left + (this._itemTmpUt.anchorX * this._itemSize.width);\n                                itemX -= (this._contentUt.anchorX * this._contentUt.width);\n                                break;\n                            }\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                                right = -this._rightGap - ((this._itemSize.width + this._columnGap) * colLine);\n                                left = right - this._itemSize.width;\n                                itemX = left + (this._itemTmpUt.anchorX * this._itemSize.width);\n                                itemX += ((1 - this._contentUt.anchorX) * this._contentUt.width);\n                                break;\n                            }\n                        }\n                        itemY = -this._topGap - ((id % this._colLineNum) * (this._itemSize.height + this._lineGap));\n                        switch (this._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                                itemY -= ((1 - this._itemTmpUt.anchorY) * this._itemSize.height);\n                                itemY += ((1 - this._contentUt.anchorY) * this._contentUt.height);\n                                break;\n                            }\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                                itemY -= ((this._itemTmpUt.anchorY) * this._itemSize.height);\n                                itemY += (this._contentUt.anchorY * this._contentUt.height);\n                                itemY *= -1;\n                                break;\n                            }\n                        }\n                        return {\n                            id: id,\n                            left: left,\n                            right: right,\n                            x: itemX,\n                            y: itemY,\n                        };\n                    }\n                }\n                break;\n            }\n        }\n    }\n    //计算已存在的Item的位置\n    _calcExistItemPos(id: number) {\n        let item: any = this.getItemByListId(id);\n        if (!item)\n            return null;\n        let ut: UITransform = item.getComponent(UITransform);\n        let pos: Vec3 = item.getPosition();\n        let data: any = {\n            id: id,\n            x: pos.x,\n            y: pos.y,\n        }\n        if (this._sizeType) {\n            data.top = pos.y + (ut.height * (1 - ut.anchorY));\n            data.bottom = pos.y - (ut.height * ut.anchorY);\n        } else {\n            data.left = pos.x - (ut.width * ut.anchorX);\n            data.right = pos.x + (ut.width * (1 - ut.anchorX));\n        }\n        return data;\n    }\n    //获取Item位置\n    getItemPos(id: number) {\n        if (this._virtual)\n            return this._calcItemPos(id);\n        else {\n            if (this.frameByFrameRenderNum)\n                return this._calcItemPos(id);\n            else\n                return this._calcExistItemPos(id);\n        }\n    }\n    //获取固定尺寸\n    _getFixedSize(listId: number) {\n        if (!this._customSize)\n            return null;\n        if (listId == null)\n            listId = this._numItems;\n        let fixed: number = 0;\n        let count: number = 0;\n        for (let id in this._customSize) {\n            if (parseInt(id) < listId) {\n                fixed += this._customSize[id];\n                count++;\n            }\n        }\n        return {\n            val: fixed,\n            count: count,\n        }\n    }\n    //滚动结束时..\n    _onScrollBegan() {\n        this._beganPos = this._sizeType ? this.viewTop : this.viewLeft;\n    }\n    //滚动结束时..\n    _onScrollEnded() {\n        let t: any = this;\n        t._curScrollIsTouch = false;\n        if (t.scrollToListId != null) {\n            let item: any = t.getItemByListId(t.scrollToListId);\n            t.scrollToListId = null;\n            if (item) {\n                tween(item)\n                    .to(.1, { scale: 1.06 })\n                    .to(.1, { scale: 1 })\n                    .start();\n            }\n        }\n        t._onScrolling();\n\n        if (t._slideMode == SlideType.ADHERING &&\n            !t.adhering\n        ) {\n            //cc.log(t.adhering, t._scrollView.isAutoScrolling(), t._scrollView.isScrolling());\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null && t._curScrollIsTouch) {\n                this._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n    }\n    // 触摸时\n    _onTouchStart(ev, captureListeners) {\n        if (this._scrollView['_hasNestedViewGroup'](ev, captureListeners))\n            return;\n        this._curScrollIsTouch = true;\n        //let isMe = ev.eventPhase === Event.AT_TARGET && ev.target === this.node;\n        let isMe = ev.target === this.node;\n        if (!isMe) {\n            let itemNode: any = ev.target;\n            while (itemNode._listId == null && itemNode.parent)\n                itemNode = itemNode.parent;\n            this._scrollItem = itemNode._listId != null ? itemNode : ev.target;\n        }\n    }\n    //触摸抬起时..\n    _onTouchUp() {\n        let t: any = this;\n        t._scrollPos = null;\n        if (t._slideMode == SlideType.ADHERING) {\n            if (this.adhering)\n                this._adheringBarrier = true;\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null) {\n                this._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n        this._scrollItem = null;\n    }\n\n    _onTouchCancelled(ev, captureListeners) {\n        let t = this;\n        if (t._scrollView['_hasNestedViewGroup'](ev, captureListeners) || ev.simulate)\n            return;\n\n        t._scrollPos = null;\n        if (t._slideMode == SlideType.ADHERING) {\n            if (t.adhering)\n                t._adheringBarrier = true;\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null) {\n                t._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n        this._scrollItem = null;\n    }\n    //当尺寸改变\n    _onSizeChanged() {\n        if (this.checkInited(false))\n            this._onScrolling();\n    }\n    //当Item自适应\n    _onItemAdaptive(item: any) {\n        let ut: UITransform = item.getComponent(UITransform);\n        // if (this.checkInited(false)) {\n        if (\n            (!this._sizeType && ut.width != this._itemSize.width)\n            || (this._sizeType && ut.height != this._itemSize.height)\n        ) {\n            if (!this._customSize)\n                this._customSize = {};\n            let val = this._sizeType ? ut.height : ut.width;\n            if (this._customSize[item._listId] != val) {\n                this._customSize[item._listId] = val;\n                this._resizeContent();\n                // this.content.children.forEach((child: Node) => {\n                //     this._updateItemPos(child);\n                // });\n                this.updateAll();\n                // 如果当前正在运行 scrollTo，肯定会不准确，在这里做修正\n                if (this._scrollToListId != null) {\n                    this._scrollPos = null;\n                    this.unschedule(this._scrollToSo);\n                    this.scrollTo(this._scrollToListId, Math.max(0, this._scrollToEndTime - ((new Date()).getTime() / 1000)));\n                }\n            }\n        }\n        // }\n    }\n    //PAGE粘附\n    _pageAdhere() {\n        let t = this;\n        if (!t.cyclic && (t.elasticTop > 0 || t.elasticRight > 0 || t.elasticBottom > 0 || t.elasticLeft > 0))\n            return;\n        let curPos = t._sizeType ? t.viewTop : t.viewLeft;\n        let dis = (t._sizeType ? t._thisNodeUt.height : t._thisNodeUt.width) * t.pageDistance;\n        let canSkip = Math.abs(t._beganPos - curPos) > dis;\n        if (canSkip) {\n            let timeInSecond = .5;\n            switch (t._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (t._beganPos > curPos) {\n                        t.prePage(timeInSecond);\n                        // cc.log('_pageAdhere   PPPPPPPPPPPPPPP');\n                    } else {\n                        t.nextPage(timeInSecond);\n                        // cc.log('_pageAdhere   NNNNNNNNNNNNNNN');\n                    }\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (t._beganPos < curPos) {\n                        t.prePage(timeInSecond);\n                    } else {\n                        t.nextPage(timeInSecond);\n                    }\n                    break;\n            }\n        } else if (t.elasticTop <= 0 && t.elasticRight <= 0 && t.elasticBottom <= 0 && t.elasticLeft <= 0) {\n            t.adhere();\n        }\n        t._beganPos = null;\n    }\n    //粘附\n    adhere() {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (t.elasticTop > 0 || t.elasticRight > 0 || t.elasticBottom > 0 || t.elasticLeft > 0)\n            return;\n        t.adhering = true;\n        t._calcNearestItem();\n        let offset: number = (t._sizeType ? t._topGap : t._leftGap) / (t._sizeType ? t._thisNodeUt.height : t._thisNodeUt.width);\n        let timeInSecond: number = .7;\n        t.scrollTo(t.nearestListId, timeInSecond, offset);\n    }\n    //Update..\n    update() {\n        if (this.frameByFrameRenderNum <= 0 || this._updateDone)\n            return;\n        // cc.log(this.displayData.length, this._updateCounter, this.displayData[this._updateCounter]);\n        if (this._virtual) {\n            let len: number = (this._updateCounter + this.frameByFrameRenderNum) > this.displayItemNum ? this.displayItemNum : (this._updateCounter + this.frameByFrameRenderNum);\n            for (let n: number = this._updateCounter; n < len; n++) {\n                let data: any = this.displayData[n];\n                if (data) {\n                    this._createOrUpdateItem(data);\n                }\n            }\n\n            if (this._updateCounter >= this.displayItemNum - 1) { //最后一个\n                if (this._doneAfterUpdate) {\n                    this._updateCounter = 0;\n                    this._updateDone = false;\n                    // if (!this._scrollView.isScrolling())\n                    this._doneAfterUpdate = false;\n                } else {\n                    this._updateDone = true;\n                    this._delRedundantItem();\n                    this._forceUpdate = false;\n                    this._calcNearestItem();\n                    if (this.slideMode == SlideType.PAGE)\n                        this.curPageNum = this.nearestListId;\n                }\n            } else {\n                this._updateCounter += this.frameByFrameRenderNum;\n            }\n        } else {\n            if (this._updateCounter < this._numItems) {\n                let len: number = (this._updateCounter + this.frameByFrameRenderNum) > this._numItems ? this._numItems : (this._updateCounter + this.frameByFrameRenderNum);\n                for (let n: number = this._updateCounter; n < len; n++) {\n                    this._createOrUpdateItem2(n);\n                }\n                this._updateCounter += this.frameByFrameRenderNum;\n            } else {\n                this._updateDone = true;\n                this._calcNearestItem();\n                if (this.slideMode == SlideType.PAGE)\n                    this.curPageNum = this.nearestListId;\n            }\n        }\n    }\n    /**\n     * 创建或更新Item（虚拟列表用）\n     * @param {Object} data 数据\n     */\n    _createOrUpdateItem(data: any) {\n        let item: any = this.getItemByListId(data.id);\n        if (!item) { //如果不存在\n            let canGet: boolean = this._pool.size() > 0;\n            if (canGet) {\n                item = this._pool.get();\n                // cc.log('从池中取出::   旧id =', item['_listId'], '，新id =', data.id, item);\n            } else {\n                item = instantiate(this._itemTmp);\n                // cc.log('新建::', data.id, item);\n            }\n            if (!canGet || !isValid(item)) {\n                item = instantiate(this._itemTmp);\n                canGet = false;\n            }\n            if (item._listId != data.id) {\n                item._listId = data.id;\n                let ut: UITransform = item.getComponent(UITransform);\n                ut.setContentSize(this._itemSize);\n            }\n            item.setPosition(new Vec3(data.x, data.y, 0));\n            this._resetItemSize(item);\n            this.content.addChild(item);\n            if (canGet && this._needUpdateWidget) {\n                let widget: Widget = item.getComponent(Widget);\n                if (widget)\n                    widget.updateAlignment();\n            }\n            item.setSiblingIndex(this.content.children.length - 1);\n\n            let listItem: ListItem = item.getComponent(ListItem);\n            item['listItem'] = listItem;\n            if (listItem) {\n                listItem.listId = data.id;\n                listItem.list = this;\n                listItem._registerEvent();\n            }\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, data.id % this._actualNumItems);\n            }\n        } else if (this._forceUpdate && this.renderEvent) { //强制更新\n            item.setPosition(new Vec3(data.x, data.y, 0));\n            this._resetItemSize(item);\n            // cc.log('ADD::', data.id, item);\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, data.id % this._actualNumItems);\n            }\n        }\n        this._resetItemSize(item);\n\n        this._updateListItem(item['listItem']);\n        if (this._lastDisplayData.indexOf(data.id) < 0) {\n            this._lastDisplayData.push(data.id);\n        }\n    }\n    //创建或更新Item（非虚拟列表用）\n    _createOrUpdateItem2(listId: number) {\n        let item: any = this.content.children[listId];\n        let listItem: ListItem;\n        if (!item) { //如果不存在\n            item = instantiate(this._itemTmp);\n            item._listId = listId;\n            this.content.addChild(item);\n            listItem = item.getComponent(ListItem);\n            item['listItem'] = listItem;\n            if (listItem) {\n                listItem.listId = listId;\n                listItem.list = this;\n                listItem._registerEvent();\n            }\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n            }\n        } else if (this._forceUpdate && this.renderEvent) { //强制更新\n            item._listId = listId;\n            if (listItem)\n                listItem.listId = listId;\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n            }\n        }\n        this._updateListItem(listItem);\n        if (this._lastDisplayData.indexOf(listId) < 0) {\n            this._lastDisplayData.push(listId);\n        }\n    }\n\n    _updateListItem(listItem: ListItem) {\n        if (!listItem)\n            return;\n        if (this.selectedMode > SelectedType.NONE) {\n            let item: any = listItem.node;\n            switch (this.selectedMode) {\n                case SelectedType.SINGLE:\n                    listItem.selected = this.selectedId == item._listId;\n                    break;\n                case SelectedType.MULT:\n                    listItem.selected = this.multSelected.indexOf(item._listId) >= 0;\n                    break;\n            }\n        }\n    }\n    //仅虚拟列表用\n    _resetItemSize(item: any) {\n        return;\n        let size: number;\n        let ut: UITransform = item.getComponent(UITransform);\n        if (this._customSize && this._customSize[item._listId]) {\n            size = this._customSize[item._listId];\n        } else {\n            if (this._colLineNum > 1)\n                ut.setContentSize(this._itemSize);\n            else\n                size = this._sizeType ? this._itemSize.height : this._itemSize.width;\n        }\n        if (size) {\n            if (this._sizeType)\n                ut.height = size;\n            else\n                ut.width = size;\n        }\n    }\n    /**\n     * 更新Item位置\n     * @param {Number||Node} listIdOrItem\n     */\n    _updateItemPos(listIdOrItem: any) {\n        let item: any = isNaN(listIdOrItem) ? listIdOrItem : this.getItemByListId(listIdOrItem);\n        let pos: any = this.getItemPos(item._listId);\n        item.setPosition(pos.x, pos.y);\n    }\n    /**\n     * 设置多选\n     * @param {Array} args 可以是单个listId，也可是个listId数组\n     * @param {Boolean} bool 值，如果为null的话，则直接用args覆盖\n     */\n    setMultSelected(args: any, bool: boolean) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (!Array.isArray(args)) {\n            args = [args];\n        }\n        if (bool == null) {\n            t.multSelected = args;\n        } else {\n            let listId: number, sub: number;\n            if (bool) {\n                for (let n: number = args.length - 1; n >= 0; n--) {\n                    listId = args[n];\n                    sub = t.multSelected.indexOf(listId);\n                    if (sub < 0) {\n                        t.multSelected.push(listId);\n                    }\n                }\n            } else {\n                for (let n: number = args.length - 1; n >= 0; n--) {\n                    listId = args[n];\n                    sub = t.multSelected.indexOf(listId);\n                    if (sub >= 0) {\n                        t.multSelected.splice(sub, 1);\n                    }\n                }\n            }\n        }\n        t._forceUpdate = true;\n        t._onScrolling();\n    }\n    /**\n     * 获取多选数据\n     * @returns\n     */\n    getMultSelected() {\n        return this.multSelected;\n    }\n    /**\n     * 多选是否有选择\n     * @param {number} listId 索引\n     * @returns\n     */\n    hasMultSelected(listId: number) {\n        return this.multSelected && this.multSelected.indexOf(listId) >= 0;\n    }\n    /**\n     * 更新指定的Item\n     * @param {Array} args 单个listId，或者数组\n     * @returns\n     */\n    updateItem(args: any) {\n        if (!this.checkInited())\n            return;\n        if (!Array.isArray(args)) {\n            args = [args];\n        }\n        for (let n: number = 0, len: number = args.length; n < len; n++) {\n            let listId: number = args[n];\n            let item: any = this.getItemByListId(listId);\n            if (item)\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n        }\n    }\n    /**\n     * 更新全部\n     */\n    updateAll() {\n        if (!this.checkInited())\n            return;\n        this.numItems = this.numItems;\n    }\n    /**\n     * 根据ListID获取Item\n     * @param {Number} listId\n     * @returns\n     */\n    getItemByListId(listId: number) {\n        if (this.content) {\n            for (let n: number = this.content.children.length - 1; n >= 0; n--) {\n                let item: any = this.content.children[n];\n                if (item._listId == listId)\n                    return item;\n            }\n        }\n    }\n    /**\n     * 获取在显示区域外的Item\n     * @returns\n     */\n    _getOutsideItem() {\n        let item: any;\n        let result: any[] = [];\n        for (let n: number = this.content.children.length - 1; n >= 0; n--) {\n            item = this.content.children[n];\n            if (!this.displayData.find(d => d.id == item._listId)) {\n                result.push(item);\n            }\n        }\n        return result;\n    }\n    //删除显示区域以外的Item\n    _delRedundantItem() {\n        if (this._virtual) {\n            let arr: any[] = this._getOutsideItem();\n            for (let n: number = arr.length - 1; n >= 0; n--) {\n                let item: any = arr[n];\n                if (this._scrollItem && item._listId == this._scrollItem._listId)\n                    continue;\n                item.isCached = true;\n                this._pool.put(item);\n                for (let m: number = this._lastDisplayData.length - 1; m >= 0; m--) {\n                    if (this._lastDisplayData[m] == item._listId) {\n                        this._lastDisplayData.splice(m, 1);\n                        break;\n                    }\n                }\n            }\n            // cc.log('存入::', str, '    pool.length =', this._pool.length);\n        } else {\n            while (this.content.children.length > this._numItems) {\n                this._delSingleItem(this.content.children[this.content.children.length - 1]);\n            }\n        }\n    }\n    //删除单个Item\n    _delSingleItem(item: any) {\n        // cc.log('DEL::', item['_listId'], item);\n        item.removeFromParent();\n        if (item.destroy)\n            item.destroy();\n        item = null;\n    }\n    /** \n     * 动效删除Item（此方法只适用于虚拟列表，即_virtual=true）\n     * 一定要在回调函数里重新设置新的numItems进行刷新，毕竟本List是靠数据驱动的。\n     */\n    aniDelItem(listId: number, callFunc: Function, aniType: number) {\n        let t: any = this;\n\n        if (!t.checkInited() || t.cyclic || !t._virtual)\n            return console.error('This function is not allowed to be called!');\n\n        if (!callFunc)\n            return console.error('CallFunc are not allowed to be NULL, You need to delete the corresponding index in the data array in the CallFunc!');\n\n        if (t._aniDelRuning)\n            return console.warn('Please wait for the current deletion to finish!');\n\n        let item: any = t.getItemByListId(listId);\n        let listItem: ListItem;\n        if (!item) {\n            callFunc(listId);\n            return;\n        } else {\n            listItem = item.getComponent(ListItem);\n        }\n        t._aniDelRuning = true;\n        t._aniDelCB = callFunc;\n        t._aniDelItem = item;\n        t._aniDelBeforePos = item.position;\n        t._aniDelBeforeScale = item.scale;\n        let curLastId: number = t.displayData[t.displayData.length - 1].id;\n        let resetSelectedId: boolean = listItem.selected;\n        listItem.showAni(aniType, () => {\n            //判断有没有下一个，如果有的话，创建粗来\n            let newId: number;\n            if (curLastId < t._numItems - 2) {\n                newId = curLastId + 1;\n            }\n            if (newId != null) {\n                let newData: any = t._calcItemPos(newId);\n                t.displayData.push(newData);\n                if (t._virtual)\n                    t._createOrUpdateItem(newData);\n                else\n                    t._createOrUpdateItem2(newId);\n            } else\n                t._numItems--;\n            if (t.selectedMode == SelectedType.SINGLE) {\n                if (resetSelectedId) {\n                    t._selectedId = -1;\n                } else if (t._selectedId - 1 >= 0) {\n                    t._selectedId--;\n                }\n            } else if (t.selectedMode == SelectedType.MULT && t.multSelected.length) {\n                let sub: number = t.multSelected.indexOf(listId);\n                if (sub >= 0) {\n                    t.multSelected.splice(sub, 1);\n                }\n                //多选的数据，在其后的全部减一\n                for (let n: number = t.multSelected.length - 1; n >= 0; n--) {\n                    let id: number = t.multSelected[n];\n                    if (id >= listId)\n                        t.multSelected[n]--;\n                }\n            }\n            if (t._customSize) {\n                if (t._customSize[listId])\n                    delete t._customSize[listId];\n                let newCustomSize: any = {};\n                let size: number;\n                for (let id in t._customSize) {\n                    size = t._customSize[id];\n                    let idNumber: number = parseInt(id);\n                    newCustomSize[idNumber - (idNumber >= listId ? 1 : 0)] = size;\n                }\n                t._customSize = newCustomSize;\n            }\n            //后面的Item向前怼的动效\n            let sec: number = .2333;\n            let twe: Tween<Node>, haveCB: boolean;\n            for (let n: number = newId != null ? newId : curLastId; n >= listId + 1; n--) {\n                item = t.getItemByListId(n);\n                if (item) {\n                    let posData: any = t._calcItemPos(n - 1);\n                    twe = tween(item)\n                        .to(sec, { position: new Vec3(posData.x, posData.y, 0) });\n\n                    if (n <= listId + 1) {\n                        haveCB = true;\n                        twe.call(() => {\n                            t._aniDelRuning = false;\n                            callFunc(listId);\n                            delete t._aniDelCB;\n                        });\n                    }\n                    twe.start();\n                }\n            }\n            if (!haveCB) {\n                t._aniDelRuning = false;\n                callFunc(listId);\n                t._aniDelCB = null;\n            }\n        }, true);\n    }\n    /**\n     * 滚动到..\n     * @param {Number} listId 索引（如果<0，则滚到首个Item位置，如果>=_numItems，则滚到最末Item位置）\n     * @param {Number} timeInSecond 时间\n     * @param {Number} offset 索引目标位置偏移，0-1\n     * @param {Boolean} overStress 滚动后是否强调该Item（这只是个实验功能）\n     */\n    scrollTo(listId: number, timeInSecond: number = .5, offset: number = null, overStress: boolean = false) {\n        let t = this;\n        if (!t.checkInited(false))\n            return;\n        // t._scrollView.stopAutoScroll();\n        if (timeInSecond == null)   //默认0.5\n            timeInSecond = .5;\n        else if (timeInSecond < 0)\n            timeInSecond = 0;\n        if (listId < 0)\n            listId = 0;\n        else if (listId >= t._numItems)\n            listId = t._numItems - 1;\n        // 以防设置了numItems之后layout的尺寸还未更新\n        if (!t._virtual && t._layout && t._layout.enabled)\n            t._layout.updateLayout();\n\n        let pos = t.getItemPos(listId);\n        if (!pos) {\n            return DEV && console.error('pos is null', listId);\n        }\n        let targetX: number, targetY: number;\n\n        switch (t._alignCalcType) {\n            case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                targetX = pos.left;\n                if (offset != null)\n                    targetX -= t._thisNodeUt.width * offset;\n                else\n                    targetX -= t._leftGap;\n                pos = new Vec3(targetX, 0, 0);\n                break;\n            case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                targetX = pos.right - t._thisNodeUt.width;\n                if (offset != null)\n                    targetX += t._thisNodeUt.width * offset;\n                else\n                    targetX += t._rightGap;\n                pos = new Vec3(targetX + t._contentUt.width, 0, 0);\n                break;\n            case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                targetY = pos.top;\n                if (offset != null)\n                    targetY += t._thisNodeUt.height * offset;\n                else\n                    targetY += t._topGap;\n                pos = new Vec3(0, -targetY, 0);\n                break;\n            case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                targetY = pos.bottom + t._thisNodeUt.height;\n                if (offset != null)\n                    targetY -= t._thisNodeUt.height * offset;\n                else\n                    targetY -= t._bottomGap;\n                pos = new Vec3(0, -targetY + t._contentUt.height, 0);\n                break;\n        }\n        let viewPos: any = t.content.getPosition();\n        viewPos = Math.abs(t._sizeType ? viewPos.y : viewPos.x);\n\n        let comparePos = t._sizeType ? pos.y : pos.x;\n        let runScroll = Math.abs((t._scrollPos != null ? t._scrollPos : viewPos) - comparePos) > .5;\n        // cc.log(runScroll, t._scrollPos, viewPos, comparePos)\n\n        // t._scrollView.stopAutoScroll();\n        if (runScroll) {\n            t._scrollView.scrollToOffset(pos, timeInSecond);\n            t._scrollToListId = listId;\n            t._scrollToEndTime = ((new Date()).getTime() / 1000) + timeInSecond;\n            // cc.log(listId, t.content.width, t.content.getPosition(), pos);\n            t._scrollToSo = t.scheduleOnce(() => {\n                if (!t._adheringBarrier) {\n                    t.adhering = t._adheringBarrier = false;\n                }\n                t._scrollPos =\n                    t._scrollToListId =\n                    t._scrollToEndTime =\n                    t._scrollToSo =\n                    null;\n                //cc.log('2222222222', t._adheringBarrier)\n                if (overStress) {\n                    // t.scrollToListId = listId;\n                    let item = t.getItemByListId(listId);\n                    if (item) {\n                        tween(item)\n                            .to(.1, { scale: 1.05 })\n                            .to(.1, { scale: 1 })\n                            .start();\n                    }\n                }\n            }, timeInSecond + .1);\n\n            if (timeInSecond <= 0) {\n                t._onScrolling();\n            }\n        }\n    }\n    /**\n     * 计算当前滚动窗最近的Item\n     */\n    _calcNearestItem() {\n        let t: any = this;\n        t.nearestListId = null;\n        let data: any, center: number;\n\n        if (t._virtual)\n            t._calcViewPos();\n\n        let vTop: number, vRight: number, vBottom: number, vLeft: number;\n        vTop = t.viewTop;\n        vRight = t.viewRight;\n        vBottom = t.viewBottom;\n        vLeft = t.viewLeft;\n\n        let breakFor: boolean = false;\n        for (let n = 0; n < t.content.children.length && !breakFor; n += t._colLineNum) {\n            data = t._virtual ? t.displayData[n] : t._calcExistItemPos(n);\n            if (data) {\n                center = t._sizeType ? ((data.top + data.bottom) / 2) : (center = (data.left + data.right) / 2);\n                switch (t._alignCalcType) {\n                    case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                        if (data.right >= vLeft) {\n                            t.nearestListId = data.id;\n                            if (vLeft > center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                        if (data.left <= vRight) {\n                            t.nearestListId = data.id;\n                            if (vRight < center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                        if (data.bottom <= vTop) {\n                            t.nearestListId = data.id;\n                            if (vTop < center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                        if (data.top >= vBottom) {\n                            t.nearestListId = data.id;\n                            if (vBottom > center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                }\n            }\n        }\n        //判断最后一个Item。。。（哎，这些判断真心恶心，判断了前面的还要判断最后一个。。。一开始呢，就只有一个布局（单列布局），那时候代码才三百行，后来就想着完善啊，艹..这坑真深，现在这行数都一千五了= =||）\n        data = t._virtual ? t.displayData[t.displayItemNum - 1] : t._calcExistItemPos(t._numItems - 1);\n        if (data && data.id == t._numItems - 1) {\n            center = t._sizeType ? ((data.top + data.bottom) / 2) : (center = (data.left + data.right) / 2);\n            switch (t._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                    if (vRight > center)\n                        t.nearestListId = data.id;\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                    if (vLeft < center)\n                        t.nearestListId = data.id;\n                    break;\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (vBottom < center)\n                        t.nearestListId = data.id;\n                    break;\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (vTop > center)\n                        t.nearestListId = data.id;\n                    break;\n            }\n        }\n        // cc.log('t.nearestListId =', t.nearestListId);\n    }\n    //上一页\n    prePage(timeInSecond: number = .5) {\n        // cc.log('👈');\n        if (!this.checkInited())\n            return;\n        this.skipPage(this.curPageNum - 1, timeInSecond);\n    }\n    //下一页\n    nextPage(timeInSecond: number = .5) {\n        // cc.log('👉');\n        if (!this.checkInited())\n            return;\n        this.skipPage(this.curPageNum + 1, timeInSecond);\n    }\n    //跳转到第几页\n    skipPage(pageNum: number, timeInSecond: number) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (t._slideMode != SlideType.PAGE)\n            return console.error('This function is not allowed to be called, Must SlideMode = PAGE!');\n        if (pageNum < 0 || pageNum >= t._numItems)\n            return;\n        if (t.curPageNum == pageNum)\n            return;\n        // cc.log(pageNum);\n        t.curPageNum = pageNum;\n        if (t.pageChangeEvent) {\n            EventHandler.emitEvents([t.pageChangeEvent], pageNum);\n        }\n        t.scrollTo(pageNum, timeInSecond);\n    }\n    //计算 CustomSize（这个函数还是保留吧，某些罕见的情况的确还是需要手动计算customSize的）\n    calcCustomSize(numItems: number) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (!t._itemTmp)\n            return console.error('Unset template item!');\n        if (!t.renderEvent)\n            return console.error('Unset Render-Event!');\n        t._customSize = {};\n        let temp: any = instantiate(t._itemTmp);\n        let ut: UITransform = temp.getComponent(UITransform);\n        t.content.addChild(temp);\n        for (let n: number = 0; n < numItems; n++) {\n            EventHandler.emitEvents([t.renderEvent], temp, n);\n            if (ut.height != t._itemSize.height || ut.width != t._itemSize.width) {\n                t._customSize[n] = t._sizeType ? ut.height : ut.width;\n            }\n        }\n        if (!Object.keys(t._customSize).length)\n            t._customSize = null;\n        temp.removeFromParent();\n        if (temp.destroy)\n            temp.destroy();\n        return t._customSize;\n    }\n}"]}