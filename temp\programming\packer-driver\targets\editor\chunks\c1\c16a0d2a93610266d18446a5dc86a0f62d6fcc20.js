System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, EmitterConditionData, BulletConditionData, EmitterActionData, BulletActionData, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _class4, _class5, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _crd, ccclass, property, eConditionGroupOp, EmitterGroupData, BulletGroupData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEmitterConditionData(extras) {
    _reporterNs.report("EmitterConditionData", "./EventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletConditionData(extras) {
    _reporterNs.report("BulletConditionData", "./EventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterActionData(extras) {
    _reporterNs.report("EmitterActionData", "./EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletActionData(extras) {
    _reporterNs.report("BulletActionData", "./EventActionData", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
    }, function (_unresolved_2) {
      EmitterConditionData = _unresolved_2.EmitterConditionData;
      BulletConditionData = _unresolved_2.BulletConditionData;
    }, function (_unresolved_3) {
      EmitterActionData = _unresolved_3.EmitterActionData;
      BulletActionData = _unresolved_3.BulletActionData;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0977cZFFJJPY5RB+3QtfUBc", "EventGroupData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eConditionGroupOp", eConditionGroupOp = /*#__PURE__*/function (eConditionGroupOp) {
        eConditionGroupOp[eConditionGroupOp["And"] = 0] = "And";
        eConditionGroupOp[eConditionGroupOp["Or"] = 1] = "Or";
        return eConditionGroupOp;
      }({}));
      /**
       * 发射器事件数据
       * 所有时间相关的，单位都是秒(s)
       */


      _export("EmitterGroupData", EmitterGroupData = (_dec = ccclass("EmitterGroupData"), _dec2 = property({
        displayName: '事件组名称'
      }), _dec3 = property({
        type: _crd && EmitterConditionData === void 0 ? (_reportPossibleCrUseOfEmitterConditionData({
          error: Error()
        }), EmitterConditionData) : EmitterConditionData,
        displayName: '条件A'
      }), _dec4 = property({
        type: Enum(eConditionGroupOp),
        displayName: '条件组合操作'
      }), _dec5 = property({
        type: _crd && EmitterConditionData === void 0 ? (_reportPossibleCrUseOfEmitterConditionData({
          error: Error()
        }), EmitterConditionData) : EmitterConditionData,
        displayName: '条件B'
      }), _dec6 = property({
        displayName: '是否重复触发'
      }), _dec7 = property({
        type: [_crd && EmitterActionData === void 0 ? (_reportPossibleCrUseOfEmitterActionData({
          error: Error()
        }), EmitterActionData) : EmitterActionData],
        displayName: '行为列表'
      }), _dec(_class = (_class2 = class EmitterGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor, this);

          _initializerDefineProperty(this, "conditionA", _descriptor2, this);

          _initializerDefineProperty(this, "groupOp", _descriptor3, this);

          // 条件组合操作: 与/或
          _initializerDefineProperty(this, "conditionB", _descriptor4, this);

          _initializerDefineProperty(this, "isRepeat", _descriptor5, this);

          _initializerDefineProperty(this, "actions", _descriptor6, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "name", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "conditionA", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "groupOp", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eConditionGroupOp.And;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "conditionB", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "isRepeat", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "actions", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _export("BulletGroupData", BulletGroupData = (_dec8 = ccclass("BulletGroupData"), _dec9 = property({
        displayName: '事件组名称'
      }), _dec10 = property({
        type: _crd && BulletConditionData === void 0 ? (_reportPossibleCrUseOfBulletConditionData({
          error: Error()
        }), BulletConditionData) : BulletConditionData,
        displayName: '条件A'
      }), _dec11 = property({
        type: Enum(eConditionGroupOp),
        displayName: '条件组合操作'
      }), _dec12 = property({
        type: _crd && BulletConditionData === void 0 ? (_reportPossibleCrUseOfBulletConditionData({
          error: Error()
        }), BulletConditionData) : BulletConditionData,
        displayName: '条件B'
      }), _dec13 = property({
        displayName: '是否重复触发'
      }), _dec14 = property({
        type: [_crd && BulletActionData === void 0 ? (_reportPossibleCrUseOfBulletActionData({
          error: Error()
        }), BulletActionData) : BulletActionData],
        displayName: '行为列表'
      }), _dec8(_class4 = (_class5 = class BulletGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor7, this);

          _initializerDefineProperty(this, "conditionA", _descriptor8, this);

          _initializerDefineProperty(this, "groupOp", _descriptor9, this);

          // 条件组合操作: 与/或
          _initializerDefineProperty(this, "conditionB", _descriptor10, this);

          _initializerDefineProperty(this, "isRepeat", _descriptor11, this);

          _initializerDefineProperty(this, "actions", _descriptor12, this);
        }

      }, (_descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "name", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return '';
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "conditionA", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "groupOp", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eConditionGroupOp.And;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "conditionB", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "isRepeat", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class5.prototype, "actions", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c16a0d2a93610266d18446a5dc86a0f62d6fcc20.js.map