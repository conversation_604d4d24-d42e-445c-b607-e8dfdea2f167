System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, LevelDataEventTrigger, LevelDataEventTriggerType, LevelDataEventTriggerWave, _crd;

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "./LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "./LevelDataEventTrigger", _context.meta, extras);
  }

  _export("LevelDataEventTriggerWave", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      LevelDataEventTrigger = _unresolved_2.LevelDataEventTrigger;
      LevelDataEventTriggerType = _unresolved_2.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "79f93J9ZHpJhoBfHQa2Lb3c", "LevelDataEventTriggerWave", undefined);

      _export("LevelDataEventTriggerWave", LevelDataEventTriggerWave = class LevelDataEventTriggerWave extends (_crd && LevelDataEventTrigger === void 0 ? (_reportPossibleCrUseOfLevelDataEventTrigger({
        error: Error()
      }), LevelDataEventTrigger) : LevelDataEventTrigger) {
        constructor() {
          super((_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave);
          this.waveUUID = "";
          this.planeID = -1;
          this.params = {};
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=679fe68101a08f2a7b1a2271f7f6cea841d5a9fa.js.map