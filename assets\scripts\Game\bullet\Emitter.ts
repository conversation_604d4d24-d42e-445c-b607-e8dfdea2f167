import { _decorator, misc, instantiate, Node, Prefab, Component } from 'cc';
import { EDITOR } from 'cc/env';
import { Bullet } from './Bullet';
import { EmitterData } from '../data/EmitterData';
import { ObjectPool } from './ObjectPool';
import { BulletSystem } from './BulletSystem';
import { eEmitterActionType } from '../data/EventActionData';
const { ccclass, executeInEditMode, property, playOnFocus } = _decorator;
const { degreesToRadians, radiansToDegrees } = misc;

export enum eEmitterStatus {
    None, Prewarm, Emitting, LoopEndReached, Completed
}

@ccclass('Emitter')
@executeInEditMode
@playOnFocus
export class Emitter extends Component {

    static kBulletNameInEditor:string = "_bullet_";

    @property({type: Prefab, displayName: "Bullet Prefab"})
    bulletPrefab: Prefab = null;

    @property({type: EmitterData, displayName: "Emitter Data"})
    emitterData: EmitterData = null;

    // 以下属性重新定义作为可修改的属性
    public isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射
    public isPreWarm : boolean = false;       // 是否预热
    public isLoop : boolean = true;           // 是否循环

    public initialDelay : number = 0.0;       // 初始延迟
    public preWarmDuration : number = 0.0;    // 预热持续时长

    public emitDuration : number = 1.0;       // 发射器持续时间
    public emitInterval : number = 1.0;       // 发射间隔
    public emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)
    public loopInterval : number = 0.0;       // 循环间隔

    public perEmitCount : number = 1;         // 单次发射数量
    public perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔
    public perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移

    public angle : number = -90;              // 发射角度: -90朝下
    public count : number = 1;                // 发射条数(弹道数量)
    public arc   : number = 60;               // 发射范围(弧度范围)
    public radius : number = 1.0;             // 发射半径

    public updateInEditor : boolean = false; // 是否在编辑器中更新
    protected _isActive: boolean = false;
    protected _status: eEmitterStatus = eEmitterStatus.None;
    protected _statusElapsedTime: number = 0;
    protected _totalElapsedTime: number = 0;
    protected _isEmitting: boolean = false;
    protected _nextEmitTime: number = 0;

    // Per-emit timing tracking
    protected _perEmitStartTime: number = 0;
    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];

    get isActive(): boolean {
        return this._isActive;
    }

    get status(): eEmitterStatus {
        return this._status;
    }

    get isEmitting(): boolean {
        return this._isEmitting;
    }

    get statusElapsedTime(): number {
        return this._statusElapsedTime;
    }

    get totalElapsedTime(): number {
        return this._totalElapsedTime;
    }

    protected start() : void {
        BulletSystem.onCreateEmitter(this);
        this._isActive = true;
        this.resetProperties();
    }

    protected update(dt : number): void {
        if (EDITOR && this.updateInEditor) {
            this.tick(dt);
            BulletSystem.tickBullets(dt);
            BulletSystem.tickActionRunners(dt);
        }
    }

    public resetInEditor() {
        this.updateInEditor = true;
    }

    public onFocusInEditor() {
        this.updateInEditor = true;
        this.resetProperties();
    }

    public onLostFocusInEditor() {
        this.updateInEditor = false;
        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {
            BulletSystem.destroyAllBullets()
        }
    }

    // reset properties from emitterData
    protected resetProperties() {
        this.isOnlyInScreen = this.emitterData.isOnlyInScreen;
        this.isPreWarm = this.emitterData.isPreWarm;
        this.isLoop = this.emitterData.isLoop;
        this.initialDelay = this.emitterData.initialDelay;
        this.preWarmDuration = this.emitterData.preWarmDuration;
        this.emitDuration = this.emitterData.emitDuration;
        this.emitInterval = this.emitterData.emitInterval;
        this.emitPower = this.emitterData.emitPower;
        this.loopInterval = this.emitterData.loopInterval;
        this.perEmitCount = this.emitterData.perEmitCount;
        this.perEmitInterval = this.emitterData.perEmitInterval;
        this.perEmitOffsetX = this.emitterData.perEmitOffsetX;
        this.angle = this.emitterData.angle;
        this.count = this.emitterData.count;
        this.arc = this.emitterData.arc;
        this.radius = this.emitterData.radius;
    }
    /**
     * public apis
     */
    changeStatus(status: eEmitterStatus) {
        this._status = status;
        this._statusElapsedTime = 0;
        // Clear per-emit queue when changing status
        this._perEmitBulletQueue = [];
    }

    protected scheduleNextEmit() {
        // Schedule the next emit after emitInterval
        this._nextEmitTime = this._statusElapsedTime + this.emitInterval;
    }

    protected startEmitting() {
        this._isEmitting = true;
        // 下一次update时触发发射
        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
    }
    
    protected stopEmitting() {
        this._isEmitting = false;
        // Clear any scheduled per-emit bullets
        this.unscheduleAllCallbacks();
        // Clear the per-emit bullet queue
        this._perEmitBulletQueue = [];
    }

    protected canEmit(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    protected emit(): void {
        if (this.perEmitInterval > 0) {
            // Calculate the base time for this emit cycle
            const currentEmitBaseTime = this._statusElapsedTime;
            console.log(`Starting emit cycle at time ${currentEmitBaseTime.toFixed(3)}, queue length before: ${this._perEmitBulletQueue.length}`);

            // Queue all bullets with their target emission times for this cycle
            for (let i = 0; i < this.count; i++) {
                for (let j = 0; j < this.perEmitCount; j++) {
                    const targetTime = currentEmitBaseTime + (this.perEmitInterval * j);
                    this._perEmitBulletQueue.push({
                        index: i,
                        perEmitIndex: j,
                        targetTime: targetTime
                    });
                    console.log(`Queued bullet ${i}-${j} for time ${targetTime.toFixed(3)}`);
                }
            }

            // Sort by target time to ensure proper order
            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);
            console.log(`Queue length after: ${this._perEmitBulletQueue.length}`);
        }
        else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count; i++) {
                for (let j = 0; j < this.perEmitCount; j++) {
                    this.emitSingle(i, j);
                }
            }
        }
    }

    protected processPerEmitQueue(): void {
        // Process bullets that should be emitted based on current time
        // Use a small tolerance to handle floating point precision issues
        const timeTolerance = 0.001; // 1ms tolerance

        while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0];

            // Check if it's time to emit this bullet (with tolerance)
            if (this._statusElapsedTime >= (nextBullet.targetTime - timeTolerance)) {
                // Remove from queue and emit
                this._perEmitBulletQueue.shift();
                console.log(`Emitting bullet at time ${this._statusElapsedTime.toFixed(3)}, target was ${nextBullet.targetTime.toFixed(3)}, diff: ${(this._statusElapsedTime - nextBullet.targetTime).toFixed(3)}`);
                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
                // No more bullets ready to emit yet
                break;
            }
        }
    }

    protected tryEmit(): boolean {
        if (this.canEmit()) {
            this.emit();
            return true;
        }
        return false;
    }

    protected emitSingle(index:number, perEmitIndex: number) {
        console.log("emit a bullet");
        const direction = this.getSpawnDirection(index);
        const position = this.getSpawnPosition(index, perEmitIndex);
        this.createBullet(direction, position);
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 计算发射方向
        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;
        const radian = degreesToRadians(this.angle + angleOffset);
        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }

    /**
     * Get the spawn position for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {
        // add perEmitOffsetX by perEmitIndex
        const perEmitOffsetX = (this.perEmitCount > 1 ? (this.perEmitOffsetX / (this.perEmitCount - 1)) * perEmitIndex - this.perEmitOffsetX / 2 : 0);
        if (this.radius <= 0) {
            return { x: perEmitOffsetX, y: 0 };
        }

        const direction = this.getSpawnDirection(index);
        return {
            x: direction.x * this.radius + perEmitOffsetX,
            y: direction.y * this.radius
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {
        if (!this.bulletPrefab) {
            console.warn("EmitterArc: No bullet prefab assigned");
            return null;
        }

        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);
        if (!bulletNode) {
            console.error("EmitterArc: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        const bullet = bulletNode.getComponent(Bullet);
        if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
        }

        if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
        }

        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bulletNode.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );

        BulletSystem.onCreateBullet(bullet);
        
        // Post set bullet properties
        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));
        bullet.mover.speed *= this.emitPower;
        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));

        return bulletNode;
    }

    public applyAction(actType: eEmitterActionType, actValue: number) {
        switch (actType) {
            case eEmitterActionType.Emitter_Active:
                this._isActive = actValue === 1 ? true : false;
                break;
            case eEmitterActionType.Emitter_InitialDelay:
                this.initialDelay = actValue;
                break;
            case eEmitterActionType.Emitter_Prewarm:
                this.isPreWarm = actValue === 1 ? true : false;
                break;
            case eEmitterActionType.Emitter_PrewarmDuration:
                this.preWarmDuration = actValue;
                break;
            case eEmitterActionType.Emitter_Duration:
                this.emitDuration = actValue;
                break;
            case eEmitterActionType.Emitter_ElapsedTime:
                this._statusElapsedTime = actValue;
                break;
            case eEmitterActionType.Emitter_Loop:
                this.isLoop = actValue === 1 ? true : false;
                break;
            case eEmitterActionType.Emitter_LoopInterval:
                this.loopInterval = actValue;
                break;
            case eEmitterActionType.Emitter_PerEmitInterval:
                this.perEmitInterval = actValue;
                break;
            case eEmitterActionType.Emitter_PerEmitCount:
                this.perEmitCount = actValue;
                break;
            case eEmitterActionType.Emitter_PerEmitOffsetX:
                this.perEmitOffsetX = actValue;
                break;
            case eEmitterActionType.Emitter_Angle:
                this.angle = actValue;
                break;
            case eEmitterActionType.Emitter_Count:
                this.count = actValue;
                break;
            // TODO: 补充更多的行为实现
            default: break;
        }
    }
    
    /**
     * Return true if this.node is in screen
     */
    protected isInScreen() : boolean {
        // TODO: Get mainCamera.containsNode(this.node)
        return true;
    }

    public tick(deltaTime: number): void {
        if (!this._isActive) {
            return;
        }

        this._statusElapsedTime += deltaTime;
        this._totalElapsedTime += deltaTime;

        switch (this._status)
        {
            case eEmitterStatus.None:
                this.updateStatusNone();
                break;
            case eEmitterStatus.Prewarm:
                this.updateStatusPrewarm();
                break;
            case eEmitterStatus.Emitting:
                this.updateStatusEmitting(deltaTime);
                break;
            case eEmitterStatus.LoopEndReached:
                this.updateStatusLoopEndReached();
                break;
            case eEmitterStatus.Completed:
                this.updateStatusCompleted();
                break;
            default:
                break;
        }
    }

    protected updateStatusNone() {
        if (this._statusElapsedTime >= this.initialDelay) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusPrewarm() {
        if (!this.isPreWarm)
            this.changeStatus(eEmitterStatus.Emitting);
        else {
            if (this._statusElapsedTime >= this.preWarmDuration) {
                this.changeStatus(eEmitterStatus.Emitting);
            }
        }
    }

    protected updateStatusEmitting(deltaTime: number) {
        if (this._statusElapsedTime > this.emitDuration) {
            this.stopEmitting();
            if (this.isLoop)
                this.changeStatus(eEmitterStatus.LoopEndReached);
            else
                this.changeStatus(eEmitterStatus.Completed);
            return;
        }

        // Start emitting if not already started
        if (!this._isEmitting) {
            this.startEmitting();
        }
        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {
            // Check if it's time for the next emit
            this.tryEmit();
            this.scheduleNextEmit();
        }

        // Process per-emit bullet queue based on precise timing
        this.processPerEmitQueue();
    }

    protected updateStatusLoopEndReached() {
        if (this._statusElapsedTime >= this.loopInterval) {
            this.changeStatus(eEmitterStatus.Emitting);
        }
    }

    protected updateStatusCompleted() {
        // Do nothing or cleanup if needed
    }
}
