{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts"], "names": ["PropertyValue", "_decorator", "ccclass", "property"], "mappings": ";;;8EASaA,a;;;;;;;;;AATJC,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U,GAE9B;;+BAMaD,a,GAAN,MAAMA,aAAN,CAAoB,E", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n// Expression wrapper\r\ninterface Expression {\r\n    raw: string;             // the original expression text\r\n    compiled?: Function;     // compiled JS function\r\n}\r\n\r\nexport class PropertyValue {\r\n\r\n}"]}