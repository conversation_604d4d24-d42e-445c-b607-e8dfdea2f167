{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts"], "names": ["EnemyWave", "TrackGroup", "WaveLootData", "Tools", "enemyGroupID", "groupInterval", "type", "enemyID", "enemyInterval", "posDX", "posDY", "enemyNum", "bSetStartPos", "startPosX", "startPosY", "trackGroups", "liveParam", "exp", "rotateSpeed", "firstShootDelay", "loadJson", "data", "delay", "planeType", "planeId", "interval", "num", "rotatioSpeed", "point", "stringToPoint", "offsetPos", "x", "y", "hasOwnProperty", "startPos", "stringToNumber", "pos", "length", "ways", "track", "split", "way", "trackGroup", "push", "types", "trackParams", "i", "FirstShootDelay", "fromLevelWave", "wave", "enemyWave", "planeID", "map", "group", "loopNum", "formIndex", "trackIDs", "tracks", "id", "speeds", "speed", "accelerates", "accelerate", "trackIntervals", "Interval", "parts", "header", "part", "values", "enemys", "lootId", "parseInt"], "mappings": ";;;sFAQaA,S,EAwGAC,U,EAmCAC,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjJJC,MAAAA,K,iBAAAA,K;;;;;;;;;AAGT;AACA;AACA;2BACaH,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACnBI,YADmB,GACI,CADJ;AAAA,eAEnBC,aAFmB,GAEK,CAFL;AAAA,eAGnBC,IAHmB,GAGJ,CAHI;AAAA,eAInBC,OAJmB,GAID,CAJC;AAAA,eAKnBC,aALmB,GAKK,CALL;AAAA,eAMnBC,KANmB,GAMH,CANG;AAAA,eAOnBC,KAPmB,GAOH,CAPG;AAAA,eAQnBC,QARmB,GAQA,CARA;AAAA,eASnBC,YATmB,GASK,KATL;AAAA,eAUnBC,SAVmB,GAUC,CAVD;AAAA,eAWnBC,SAXmB,GAWC,CAXD;AAAA,eAYnBC,WAZmB,GAYS,EAZT;AAAA,eAanBC,SAbmB,GAaG,EAbH;AAAA,eAcnBC,GAdmB,GAcL,CAdK;AAenB;AACA;AAhBmB,eAiBnBC,WAjBmB,GAiBG,CAjBH;AAAA,eAkBnBC,eAlBmB,GAkBS,EAlBT;AAAA;;AAoBnB;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,eAAKjB,YAAL,GAAoBiB,IAAI,CAACjB,YAAzB;AACA,eAAKC,aAAL,GAAqBgB,IAAI,CAACC,KAA1B;AACA,eAAKhB,IAAL,GAAYe,IAAI,CAACE,SAAjB;AACA,eAAKhB,OAAL,GAAec,IAAI,CAACG,OAApB;AACA,eAAKhB,aAAL,GAAqBa,IAAI,CAACI,QAA1B;AACA,eAAKd,QAAL,GAAgBU,IAAI,CAACK,GAArB;AACA,eAAKR,WAAL,GAAmBG,IAAI,CAACM,YAAxB;AAGA,gBAAMC,KAAK,GAAG;AAAA;AAAA,8BAAMC,aAAN,CAAoBR,IAAI,CAACS,SAAzB,EAAoC,GAApC,CAAd;AACA,eAAKrB,KAAL,GAAamB,KAAK,CAACG,CAAnB;AACA,eAAKrB,KAAL,GAAakB,KAAK,CAACI,CAAnB;;AAEA,cAAIX,IAAI,CAACY,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,kBAAMC,QAAQ,GAAG;AAAA;AAAA,gCAAMC,cAAN,CAAqBd,IAAI,CAACe,GAA1B,EAA+B,GAA/B,CAAjB;;AACA,gBAAIF,QAAQ,CAACG,MAAT,KAAoB,CAAxB,EAA2B;AACvB,mBAAKxB,SAAL,GAAiBqB,QAAQ,CAAC,CAAD,CAAzB;AACA,mBAAKpB,SAAL,GAAiBoB,QAAQ,CAAC,CAAD,CAAzB;AACA,mBAAKtB,YAAL,GAAoB,IAApB;AACH,aAJD,MAIO;AACH,mBAAKA,YAAL,GAAoB,KAApB;AACH;AACJ;;AAGD,cAAIS,IAAI,CAACY,cAAL,CAAoB,OAApB,CAAJ,EAAkC;AAC9B,kBAAMK,IAAI,GAAGjB,IAAI,CAACkB,KAAL,CAAWC,KAAX,CAAiB,GAAjB,CAAb;;AACA,iBAAK,MAAMC,GAAX,IAAkBH,IAAlB,EAAwB;AACpB,kBAAIG,GAAG,KAAK,EAAR,IAAcA,GAAG,CAACD,KAAJ,CAAU,GAAV,EAAeH,MAAf,GAAwB,CAA1C,EAA6C;AACzC,sBAAMK,UAAU,GAAG,IAAIzC,UAAJ,EAAnB;AACAyC,gBAAAA,UAAU,CAACtB,QAAX,CAAoBqB,GAApB;AACA,qBAAK1B,WAAL,CAAiB4B,IAAjB,CAAsBD,UAAtB;AACH;AACJ;AACJ;;AACD,cAAIrB,IAAI,CAACY,cAAL,CAAoB,aAApB,CAAJ,EAAwC;AACpC,kBAAMW,KAAK,GAAG;AAAA;AAAA,gCAAMT,cAAN,CAAqBd,IAAI,CAACwB,WAA1B,EAAuC,GAAvC,CAAd;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAACP,MAA1B,EAAkCS,CAAC,EAAnC,EAAuC;AACnC,kBAAI,KAAK/B,WAAL,CAAiBsB,MAAjB,GAA0BS,CAA9B,EAAiC;AAC7B,qBAAK/B,WAAL,CAAiB+B,CAAjB,EAAoBxC,IAApB,GAA2BsC,KAAK,CAACE,CAAD,CAAhC;AACH;AACJ;AACJ;;AACD,cAAIzB,IAAI,CAACY,cAAL,CAAoB,iBAApB,KAA0CZ,IAAI,CAAC0B,eAAL,KAAyB,EAAvE,EAA2E;AACvE,iBAAK5B,eAAL,GAAuB;AAAA;AAAA,gCAAMgB,cAAN,CAAqBd,IAAI,CAAC0B,eAA1B,EAA2C,GAA3C,CAAvB;AACH;AACJ;;AACmB,eAAbC,aAAa,CAACC,IAAD,EAAab,GAAb,EAAwB;AACxC,gBAAMc,SAAS,GAAG,IAAIlD,SAAJ,EAAlB;AACAkD,UAAAA,SAAS,CAAC9C,YAAV,GAAyB6C,IAAI,CAAC7C,YAA9B;AACA8C,UAAAA,SAAS,CAAC7C,aAAV,GAA0B4C,IAAI,CAAC3B,KAA/B;AACA4B,UAAAA,SAAS,CAAC5C,IAAV,GAAiB2C,IAAI,CAAC1B,SAAtB;AACA2B,UAAAA,SAAS,CAAC3C,OAAV,GAAoB0C,IAAI,CAACE,OAAzB;AACAD,UAAAA,SAAS,CAAC1C,aAAV,GAA0ByC,IAAI,CAACxB,QAA/B;AACAyB,UAAAA,SAAS,CAACzC,KAAV,GAAkB2B,GAAG,CAACL,CAAtB;AACAmB,UAAAA,SAAS,CAACxC,KAAV,GAAkB0B,GAAG,CAACJ,CAAtB;AACAkB,UAAAA,SAAS,CAACvC,QAAV,GAAqBsC,IAAI,CAACvB,GAA1B;AACAwB,UAAAA,SAAS,CAACtC,YAAV,GAAyB,IAAzB;AACAsC,UAAAA,SAAS,CAACrC,SAAV,GAAsBoC,IAAI,CAACf,QAAL,CAAcH,CAApC;AACAmB,UAAAA,SAAS,CAACpC,SAAV,GAAsBmC,IAAI,CAACf,QAAL,CAAcF,CAApC;AACAkB,UAAAA,SAAS,CAACnC,WAAV,GAAwBkC,IAAI,CAAClC,WAAL,CAAiBqC,GAAjB,CAAqBC,KAAK,IAAI;AAClD,kBAAMX,UAAU,GAAG,IAAIzC,UAAJ,EAAnB;AACAyC,YAAAA,UAAU,CAACY,OAAX,GAAqBD,KAAK,CAACC,OAA3B;AACAZ,YAAAA,UAAU,CAACa,SAAX,GAAuBF,KAAK,CAACE,SAA7B;AACAb,YAAAA,UAAU,CAACc,QAAX,GAAsBH,KAAK,CAACI,MAAN,CAAaL,GAAb,CAAiBb,KAAK,IAAIA,KAAK,CAACmB,EAAhC,CAAtB;AACAhB,YAAAA,UAAU,CAACiB,MAAX,GAAoBN,KAAK,CAACI,MAAN,CAAaL,GAAb,CAAiBb,KAAK,IAAIA,KAAK,CAACqB,KAAhC,CAApB;AACAlB,YAAAA,UAAU,CAACmB,WAAX,GAAyBR,KAAK,CAACI,MAAN,CAAaL,GAAb,CAAiBb,KAAK,IAAIA,KAAK,CAACuB,UAAhC,CAAzB;AACApB,YAAAA,UAAU,CAACqB,cAAX,GAA4BV,KAAK,CAACI,MAAN,CAAaL,GAAb,CAAiBb,KAAK,IAAIA,KAAK,CAACyB,QAAhC,CAA5B;AACAtB,YAAAA,UAAU,CAACpC,IAAX,GAAkB+C,KAAK,CAAC/C,IAAxB;AACA,mBAAOoC,UAAP;AACH,WAVuB,CAAxB;AAWAQ,UAAAA,SAAS,CAAC/B,eAAV,GAA4B8B,IAAI,CAAC9B,eAAjC;AACA,iBAAO+B,SAAP;AACH;;AAlGkB,O;AAqGvB;AACA;AACA;;;4BACajD,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eACpBK,IADoB,GACL,CADK;AAAA,eAEpBgD,OAFoB,GAEF,CAFE;AAAA,eAGpBC,SAHoB,GAGA,CAHA;AAAA,eAIpBC,QAJoB,GAIC,EAJD;AAAA,eAKpBG,MALoB,GAKD,EALC;AAAA,eAMpBE,WANoB,GAMI,EANJ;AAAA,eAOpBE,cAPoB,GAOO,EAPP;AAAA;;AASpB;AACJ;AACA;AACA;AACI3C,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAM4C,KAAK,GAAG5C,IAAI,CAACmB,KAAL,CAAW,GAAX,CAAd;;AACA,cAAIyB,KAAK,CAAC5B,MAAN,GAAe,CAAnB,EAAsB;AAClB,kBAAM6B,MAAM,GAAG;AAAA;AAAA,gCAAM/B,cAAN,CAAqB8B,KAAK,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAf;AACA,iBAAKX,OAAL,GAAeY,MAAM,CAAC,CAAD,CAArB;AACA,iBAAKX,SAAL,GAAiBW,MAAM,CAAC,CAAD,CAAvB;AACH;;AACD,eAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmB,KAAK,CAAC5B,MAA1B,EAAkCS,CAAC,EAAnC,EAAuC;AACnC,kBAAMqB,IAAI,GAAGF,KAAK,CAACnB,CAAD,CAAlB;;AACA,gBAAIqB,IAAI,KAAK,EAAb,EAAiB;AACb,oBAAMC,MAAM,GAAG;AAAA;AAAA,kCAAMjC,cAAN,CAAqBgC,IAArB,EAA2B,GAA3B,CAAf;AACA,mBAAKX,QAAL,CAAcb,IAAd,CAAmByB,MAAM,CAAC,CAAD,CAAzB;AACA,mBAAKT,MAAL,CAAYhB,IAAZ,CAAiByB,MAAM,CAAC,CAAD,CAAvB;AACA,mBAAKL,cAAL,CAAoBpB,IAApB,CAAyByB,MAAM,CAAC,CAAD,CAA/B;AACH;AACJ;AACJ;;AA7BmB,O;AAgCxB;AACA;AACA;;;8BACalE,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACtBmE,MADsB,GACH,EADG;AAAA,eAEtBC,MAFsB,GAEL,CAFK;AAAA;;AAItB;AACJ;AACA;AACA;AACIlD,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,gBAAM4C,KAAK,GAAG5C,IAAI,CAACmB,KAAL,CAAW,GAAX,CAAd;;AACA,cAAIyB,KAAK,CAAC5B,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAKgC,MAAL,GAAc;AAAA;AAAA,gCAAMlC,cAAN,CAAqB8B,KAAK,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAd;AACA,iBAAKK,MAAL,GAAcC,QAAQ,CAACN,KAAK,CAAC,CAAD,CAAN,CAAtB;AACH;AACJ;;AAdqB,O", "sourcesContent": ["import { Vec2 } from \"cc\";\r\nimport { Wave as tbWave } from \"../../AutoGen/Luban/schema\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n/**\r\n * 敌人波次类\r\n */\r\nexport class EnemyWave {\r\n    enemyGroupID: number = 0;\r\n    groupInterval: number = 0;\r\n    type: number = 0;\r\n    enemyID: number = 0;\r\n    enemyInterval: number = 0;\r\n    posDX: number = 0;\r\n    posDY: number = 0;\r\n    enemyNum: number = 0;\r\n    bSetStartPos: boolean = false;\r\n    startPosX: number = 0;\r\n    startPosY: number = 0;\r\n    trackGroups: TrackGroup[] = [];\r\n    liveParam: number[] = [];\r\n    exp: number = 0;\r\n    // normalLoot: WaveLootData | null = null;\r\n    // randomLoot: WaveLootData | null = null;\r\n    rotateSpeed: number = 0;\r\n    firstShootDelay: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载波次信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: tbWave): void {\r\n        this.enemyGroupID = data.enemyGroupID;\r\n        this.groupInterval = data.delay;\r\n        this.type = data.planeType;\r\n        this.enemyID = data.planeId;\r\n        this.enemyInterval = data.interval;\r\n        this.enemyNum = data.num;\r\n        this.rotateSpeed = data.rotatioSpeed;\r\n\r\n\r\n        const point = Tools.stringToPoint(data.offsetPos, \",\");\r\n        this.posDX = point.x;\r\n        this.posDY = point.y;\r\n\r\n        if (data.hasOwnProperty(\"pos\")) {\r\n            const startPos = Tools.stringToNumber(data.pos, \",\");\r\n            if (startPos.length === 2) {\r\n                this.startPosX = startPos[0];\r\n                this.startPosY = startPos[1];\r\n                this.bSetStartPos = true;\r\n            } else {\r\n                this.bSetStartPos = false;\r\n            }\r\n        }\r\n\r\n\r\n        if (data.hasOwnProperty(\"track\")) {\r\n            const ways = data.track.split(\"#\");\r\n            for (const way of ways) {\r\n                if (way !== \"\" && way.split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(way);\r\n                    this.trackGroups.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"trackParams\")) {\r\n            const types = Tools.stringToNumber(data.trackParams, \",\");\r\n            for (let i = 0; i < types.length; i++) {\r\n                if (this.trackGroups.length > i) {\r\n                    this.trackGroups[i].type = types[i];\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"FirstShootDelay\") && data.FirstShootDelay !== \"\") {\r\n            this.firstShootDelay = Tools.stringToNumber(data.FirstShootDelay, \",\");\r\n        }\r\n    }\r\n    static fromLevelWave(wave: Wave, pos: Vec2) {\r\n        const enemyWave = new EnemyWave();\r\n        enemyWave.enemyGroupID = wave.enemyGroupID;\r\n        enemyWave.groupInterval = wave.delay;\r\n        enemyWave.type = wave.planeType;\r\n        enemyWave.enemyID = wave.planeID;\r\n        enemyWave.enemyInterval = wave.interval\r\n        enemyWave.posDX = pos.x\r\n        enemyWave.posDY = pos.y\r\n        enemyWave.enemyNum = wave.num;\r\n        enemyWave.bSetStartPos = true\r\n        enemyWave.startPosX = wave.startPos.x\r\n        enemyWave.startPosY = wave.startPos.y\r\n        enemyWave.trackGroups = wave.trackGroups.map(group => {\r\n            const trackGroup = new TrackGroup();\r\n            trackGroup.loopNum = group.loopNum;\r\n            trackGroup.formIndex = group.formIndex;\r\n            trackGroup.trackIDs = group.tracks.map(track => track.id);\r\n            trackGroup.speeds = group.tracks.map(track => track.speed);\r\n            trackGroup.accelerates = group.tracks.map(track => track.accelerate);\r\n            trackGroup.trackIntervals = group.tracks.map(track => track.Interval);\r\n            trackGroup.type = group.type;\r\n            return trackGroup;\r\n        })\r\n        enemyWave.firstShootDelay = wave.firstShootDelay;\r\n        return enemyWave;\r\n    }\r\n}\r\n\r\n/**\r\n * 轨迹组类\r\n */\r\nexport class TrackGroup {\r\n    type: number = 0;\r\n    loopNum: number = 0;\r\n    formIndex: number = 0;\r\n    trackIDs: number[] = [];\r\n    speeds: number[] = [];\r\n    accelerates: number[] = [];\r\n    trackIntervals: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载轨迹组信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\";\");\r\n        if (parts.length > 0) {\r\n            const header = Tools.stringToNumber(parts[0], \",\");\r\n            this.loopNum = header[0];\r\n            this.formIndex = header[1];\r\n        }\r\n        for (let i = 1; i < parts.length; i++) {\r\n            const part = parts[i];\r\n            if (part !== \"\") {\r\n                const values = Tools.stringToNumber(part, \",\");\r\n                this.trackIDs.push(values[0]);\r\n                this.speeds.push(values[1]);\r\n                this.trackIntervals.push(values[2]);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 波次掉落数据类\r\n */\r\nexport class WaveLootData {\r\n    enemys: number[] = [];\r\n    lootId: number = 0;\r\n\r\n    /**\r\n     * 从 JSON 数据加载掉落信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\";\");\r\n        if (parts.length > 1) {\r\n            this.enemys = Tools.stringToNumber(parts[0], \",\");\r\n            this.lootId = parseInt(parts[1]);\r\n        }\r\n    }\r\n}"]}