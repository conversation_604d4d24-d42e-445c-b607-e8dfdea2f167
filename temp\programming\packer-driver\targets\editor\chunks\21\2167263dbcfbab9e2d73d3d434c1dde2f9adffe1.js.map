{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts"], "names": ["_decorator", "CCFloat", "Prefab", "CCString", "assetManager", "CCInteger", "LevelEditorElemUI", "ccclass", "property", "executeInEditMode", "LevelEditorWaveParam", "LevelEditorWaveUI", "initByLevelData", "data", "planeID", "params", "k", "param", "name", "value", "push", "waveUUID", "loadAny", "uuid", "err", "prefab", "console", "error", "wavePrefab", "fillLevelData", "for<PERSON>ach"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,O,OAAAA,O;AAAqCC,MAAAA,M,OAAAA,M;AAA4CC,MAAAA,Q,OAAAA,Q;AAA4BC,MAAAA,Y,OAAAA,Y;AAAuFC,MAAAA,S,OAAAA,S;;AAGtNC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CT,U;;sCAGpCU,oB,WADZH,OAAO,CAAC,sBAAD,C,UAEHC,QAAQ,CAACL,QAAD,C,UAERK,QAAQ,CAACP,OAAD,C,2BAJb,MACaS,oBADb,CACkC;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAER,E;;;;;;;iBAEC,C;;;;mCAKdC,iB,YAFZJ,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAACN,MAAD,C,UAERM,QAAQ,CAACH,SAAD,C,UAGRG,QAAQ,CAAC,CAACE,oBAAD,CAAD,C,6CARb,MAEaC,iBAFb;AAAA;AAAA,kDAEyD;AAAA;AAAA;;AAAA;;AAAA;;AAKrD;AALqD;AAAA;;AAS9CC,QAAAA,eAAe,CAACC,IAAD,EAAsB;AACxC,gBAAMD,eAAN,CAAsBC,IAAtB;AACA,eAAKC,OAAL,GAAeD,IAAI,CAACC,OAApB;AACA,eAAKC,MAAL,GAAc,EAAd;;AACA,cAAIF,IAAI,CAACE,MAAT,EAAiB;AACb,iBAAK,IAAIC,CAAT,IAAcH,IAAI,CAACE,MAAnB,EAA2B;AACvB,kBAAIE,KAAK,GAAG,IAAIP,oBAAJ,EAAZ;AACAO,cAAAA,KAAK,CAACC,IAAN,GAAaF,CAAb;AACAC,cAAAA,KAAK,CAACE,KAAN,GAAcN,IAAI,CAACE,MAAL,CAAYC,CAAZ,CAAd;AACA,mBAAKD,MAAL,CAAYK,IAAZ,CAAiBH,KAAjB;AACH;AACJ;;AACD,cAAIJ,IAAI,CAACQ,QAAL,IAAiB,EAArB,EAAyB;AACrBjB,YAAAA,YAAY,CAACkB,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACV,IAAI,CAACQ;AAAX,aAArB,EAA2C,CAACG,GAAD,EAAMC,MAAN,KAAwB;AAC/D,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,yDAAd,EAAyEH,GAAzE;AACA;AACH;;AACD,mBAAKI,UAAL,GAAkBH,MAAlB;AACH,aAND;AAOH;AACJ;;AAEMI,QAAAA,aAAa,CAAChB,IAAD,EAAsB;AAAA;;AACtC,gBAAMgB,aAAN,CAAoBhB,IAApB;AACAA,UAAAA,IAAI,CAACC,OAAL,GAAe,KAAKA,OAApB;AACAD,UAAAA,IAAI,CAACQ,QAAL,gDAAgB,KAAKO,UAArB,qBAAgB,iBAAiBL,IAAjC,oCAAyC,EAAzC;AACAV,UAAAA,IAAI,CAACE,MAAL,GAAc,EAAd;AACA,eAAKA,MAAL,CAAYe,OAAZ,CAAqBb,KAAD,IAAW;AAC3BJ,YAAAA,IAAI,CAACE,MAAL,CAAYE,KAAK,CAACC,IAAlB,IAA0BD,KAAK,CAACE,KAAhC;AACH,WAFD;AAGH;;AAxCoD,O;;;;;iBAElB,I;;;;;;;iBAEV,C;;;;;;;iBAGe,E", "sourcesContent": ["import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger } from 'cc';\r\nimport {LevelDataWave} from '../../scripts/leveldata/leveldata';\r\n\r\nimport { LevelEditorElemUI } from './LevelEditorElemUI';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('LevelEditorWaveParam')\r\nexport class LevelEditorWaveParam {\r\n    @property(CCString)\r\n    public name: string = \"\";\r\n    @property(CCFloat)\r\n    public value: number = 0;\r\n}\r\n\r\n@ccclass('LevelEditorWaveUI')\r\n@executeInEditMode()\r\nexport class LevelEditorWaveUI extends LevelEditorElemUI {\r\n    @property(Prefab)\r\n    public wavePrefab: Prefab | null = null;\r\n    @property(CCInteger)\r\n    public planeID: number = 0;\r\n    // map property name to value\r\n    @property([LevelEditorWaveParam])\r\n    public params: LevelEditorWaveParam[] = [];\r\n\r\n    public initByLevelData(data: LevelDataWave) {\r\n        super.initByLevelData(data);\r\n        this.planeID = data.planeID;\r\n        this.params = [];\r\n        if (data.params) {\r\n            for (let k in data.params) {\r\n                var param = new LevelEditorWaveParam()\r\n                param.name = k\r\n                param.value = data.params[k]\r\n                this.params.push(param);\r\n            }\r\n        }\r\n        if (data.waveUUID != \"\") {\r\n            assetManager.loadAny({uuid:data.waveUUID}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load wave prefab err\", err);\r\n                    return\r\n                }\r\n                this.wavePrefab = prefab;\r\n            })\r\n        }\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataWave) {\r\n        super.fillLevelData(data)\r\n        data.planeID = this.planeID;\r\n        data.waveUUID = this.wavePrefab?.uuid ?? \"\";\r\n        data.params = {};\r\n        this.params.forEach((param) => {\r\n            data.params[param.name] = param.value;\r\n        })\r\n    }\r\n}"]}