System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, color, Color, v2, Vec2, <PERSON>ls, EnemyCollider, TrackGroup, BossBaseData, BossData, BossAttackPointData, BossAttackActionData, UnitData, BossUnitData, BossBulletData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyCollider(extras) {
    _reporterNs.report("EnemyCollider", "./EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "./EnemyWave", _context.meta, extras);
  }

  _export({
    BossBaseData: void 0,
    BossD<PERSON>: void 0,
    BossAttackPointData: void 0,
    BossAttackActionData: void 0,
    UnitData: void 0,
    BossUnitData: void 0,
    BossBulletData: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      color = _cc.color;
      Color = _cc.Color;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }, function (_unresolved_3) {
      EnemyCollider = _unresolved_3.EnemyCollider;
    }, function (_unresolved_4) {
      TrackGroup = _unresolved_4.TrackGroup;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e97fb5d9LZCSauRdnwyXl3r", "BossData", undefined);

      __checkObsolete__(['color', 'Color', 'v2', 'Vec2']);

      /**
       * Boss 基础数据类
       */
      _export("BossBaseData", BossBaseData = class BossBaseData {
        constructor() {
          this.id = 0;
          this.atlas = [];
          this.exp = 0;
          this.collideArr = [];
          this.attack = 0;
          this.collideAttack = 0;
          this.transformAudio = "";
          this.blastParam = [];
          this.blastShake = [];
          this.appearParam = [];
          this.onlyLoot = [];
          this.lootArr = [];
          this.lootParam0 = [];
          this.lootParam1 = [];
        }

        /**
         * 从 JSON 数据加载 Boss 基础数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("id")) this.id = parseInt(data.id);
          if (data.hasOwnProperty("atlas")) this.atlas = data.atlas.split(";");
          if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
          if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;

          if (data.hasOwnProperty("cs") && data.cs !== "") {
            const csArray = data.cs.split(";");

            for (const cs of csArray) {
              if (cs !== "") this.collideArr.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(cs, ","));
            }
          }

          if (data.hasOwnProperty("bla") && data.bla !== "") {
            const blaArray = data.bla.split(";");

            for (const bla of blaArray) {
              if (bla !== "") this.blastParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(bla, ","));
            }
          }

          if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");

            for (const sk of skArray) {
              if (sk !== "") this.blastShake.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToPoint(sk, ","));
            }
          }

          if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
          if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);
          if (data.hasOwnProperty("app")) this.appearParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.app, ",");
          if (data.hasOwnProperty("fl") && data.fl !== "") this.onlyLoot = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.fl, ",");
          if (data.hasOwnProperty("loot")) this.lootArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.loot, ",");
          if (data.hasOwnProperty("lp0")) this.lootParam0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lp0, ",");
          if (data.hasOwnProperty("lp1")) this.lootParam1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.lp1, ",");
        }

      });
      /**
       * Boss 数据类
       */


      _export("BossData", BossData = class BossData extends BossBaseData {
        constructor(...args) {
          super(...args);
          this.subId = 0;
          this.units = [];
          this.unitsOrder = [];
          this.colliders = [];
          this.hpParam = [];
          this.appearParam = [];
          this.transformAudio = "";
          this.blastType = 0;
          this.bombHurt = 0;
          this.leave = 0;
          this.nextBoss = [];
          this.wayPointXs = [];
          this.wayPointYs = [];
          this.wayPointIntervals = [];
          this.speeds = [];
          this.attackIntervals = [];
          this.snakeParam = [];
          this.trackGroups = [];
          this.attackActions = [];
          this.attackPoints = [];
          this.dieFallDelay = 0;
          this.blastCount = 0;
          this.va = void 0;
          this.dashTrack = [];
          this.freeTrackArr = void 0;
          this.enemyId = void 0;
          this.enemyRotate = void 0;
          this.enemyPos = [];
          this.enemyTrackGroup1 = [];
          this.enemyTrackGroup2 = [];
        }

        /**
         * 从 JSON 数据加载 Boss 数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("bId")) this.id = parseInt(data.bId);
          if (data.hasOwnProperty("sId")) this.subId = parseInt(data.sId);
          if (data.hasOwnProperty("us")) this.units = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.us, ",");
          if (data.hasOwnProperty("rid") && data.rid !== "") this.nextBoss = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.rid, ",");
          if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
          if (data.hasOwnProperty("leave")) this.leave = parseInt(data.leave);
          if (data.hasOwnProperty("va")) this.va = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.va, ",");

          if (data.hasOwnProperty("ua")) {
            const uaArray = data.ua.split(";");

            for (const ua of uaArray) {
              if (ua !== "") this.unitsOrder.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(ua, ","));
            }
          }

          if (data.hasOwnProperty("cos")) {
            const cosArray = data.cos.split(";");

            for (const cos of cosArray) {
              const collider = new (_crd && EnemyCollider === void 0 ? (_reportPossibleCrUseOfEnemyCollider({
                error: Error()
              }), EnemyCollider) : EnemyCollider)();
              collider.loadJson(cos);
              this.colliders.push(collider);
            }
          }

          if (data.hasOwnProperty("hpp")) this.hpParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hpp, ",");
          if (data.hasOwnProperty("app")) this.appearParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.app, ",");
          if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;
          if (data.hasOwnProperty("bla")) this.blastType = Number(data.bla);
          if (data.hasOwnProperty("bh")) this.bombHurt = Number(data.bh);
          if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
          if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);

          if (data.hasOwnProperty("dh")) {
            const dhArray = data.dh.split(";");

            for (const dh of dhArray) {
              if (dh !== "") this.dashTrack.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(dh, ","));
            }
          }

          if (data.hasOwnProperty("ea") && data.ea !== "") {
            this.freeTrackArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.ea, ",");
          }

          if (data.hasOwnProperty("way") && data.way !== "") {
            const wayArray = data.way.split(";");

            for (const way of wayArray) {
              if (way !== "") {
                const point = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(way, ",");
                this.wayPointXs.push(point.x);
                this.wayPointYs.push(point.y);
              }
            }
          }

          if (data.hasOwnProperty("wi") && data.wi !== "") {
            this.wayPointIntervals = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.wi, ",");
          }

          if (data.hasOwnProperty("sp") && data.sp !== "") {
            this.speeds = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.sp, ",");
          }

          if (data.hasOwnProperty("ai") && data.ai !== "") {
            this.attackIntervals = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.ai, ",");
          }

          if (data.hasOwnProperty("ra") && data.ra !== "") {
            const raArray = data.ra.split(";");

            for (let i = 0; i < raArray.length; i++) {
              if (raArray[i] !== "") {
                const attackAction = new BossAttackActionData();
                attackAction.loadJson(raArray[i]);
                this.attackActions.push(attackAction);
              }
            }
          } // 解析攻击点数据


          let attackPointIndex = 0;

          while (true) {
            const attackPointKey = "a" + attackPointIndex++;
            if (!data.hasOwnProperty(attackPointKey) || data[attackPointKey] === "") break;
            const attackPointData = new BossAttackPointData();
            attackPointData.loadJson(data[attackPointKey]);
            this.attackPoints.push(attackPointData);
          } // 解析爆炸参数


          if (data.hasOwnProperty("blp") && data.blp !== "") {
            const blpArray = data.blp.split(";");

            for (let i = 0; i < blpArray.length; i++) {
              if (blpArray[i] !== "") {
                this.blastParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(blpArray[i], ","));
                this.blastCount++;
              }
            }
          } // 解析爆炸震动参数


          if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");

            for (let i = 0; i < skArray.length; i++) {
              if (skArray[i] !== "") {
                this.blastShake.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(skArray[i], ","));
              }
            }
          } // 解析死亡掉落延迟


          if (data.hasOwnProperty("ft")) {
            this.dieFallDelay = Number(data.ft);
          } // 解析唯一掉落


          if (data.hasOwnProperty("fl") && data.fl !== "") {
            this.onlyLoot = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.fl, ",");
          } // 解析掉落数组


          if (data.hasOwnProperty("loot")) {
            this.lootArr = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.loot, ",");
          } // 解析掉落参数 0


          if (data.hasOwnProperty("lp0")) {
            this.lootParam0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.lp0, ",");
          } // 解析掉落参数 1


          if (data.hasOwnProperty("lp1")) {
            this.lootParam1 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.lp1, ",");
          } // 解析敌人 ID


          if (data.hasOwnProperty("eid")) {
            this.enemyId = Number(data.eid);
          } // 解析敌人旋转角度


          if (data.hasOwnProperty("erotate")) {
            this.enemyRotate = Number(data.erotate);
          } // 解析敌人位置


          if (data.hasOwnProperty("epos")) {
            const eposArray = data.epos.split("#");

            for (let i = 0; i < eposArray.length; i++) {
              const position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(eposArray[i], ",");

              if (position.length === 2) {
                this.enemyPos.push(v2(position[0], position[1]));
              }
            }
          } // 解析敌人轨迹组 1


          if (data.hasOwnProperty("etrack1")) {
            const etrack1Array = data.etrack1.split("#");

            for (let i = 0; i < etrack1Array.length; i++) {
              if (etrack1Array[i] !== "" && etrack1Array[i].split(";").length > 1) {
                const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
                  error: Error()
                }), TrackGroup) : TrackGroup)();
                trackGroup.loadJson(etrack1Array[i]);
                this.enemyTrackGroup1.push(trackGroup);
              }
            }
          } // 解析敌人轨迹组 2


          if (data.hasOwnProperty("etrack2")) {
            const etrack2Array = data.etrack2.split("#");

            for (let i = 0; i < etrack2Array.length; i++) {
              if (etrack2Array[i] !== "" && etrack2Array[i].split(";").length > 1) {
                const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
                  error: Error()
                }), TrackGroup) : TrackGroup)();
                trackGroup.loadJson(etrack2Array[i]);
                this.enemyTrackGroup2.push(trackGroup);
              }
            }
          }
        }

      });
      /**
       * Boss 攻击点数据类
       */


      _export("BossAttackPointData", BossAttackPointData = class BossAttackPointData {
        constructor() {
          this.bAvailable = true;
          this.atkType = 0;
          this.atkUnitId = 0;
          this.atkAnim = [];
          this.x = 0;
          this.y = 0;
          this.shootInterval = [];
          this.bulletIDs = [];
          this.bulletNums = [];
          this.bulletIntervals = [];
          this.bulletAttackRates = [];
          this.attackOverDelay = [];
          this.waveIds = [];
        }

        /**
         * 从 JSON 数据加载攻击点数据
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = data.split("#");

          if (parts.length < 3) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("BossAttackPointData error:", data);
            return;
          }

          this.atkType = parseInt(parts[0]);
          this.atkUnitId = parseInt(parts[1]);
          const animParts = parts[2].split(";");

          for (const anim of animParts) {
            if (anim !== "") {
              this.atkAnim.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(anim, ","));
            }
          }

          switch (this.atkType) {
            case 0:
              // 普通攻击
              const attackParts = parts[3].split(";");

              try {
                if (attackParts.length <= 1) {
                  this.bAvailable = false;
                  return;
                }

                const position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(attackParts[0], ",");
                this.x = position.x;
                this.y = position.y;

                for (let i = 1; i < attackParts.length; i++) {
                  if (attackParts[i] !== "") {
                    const attackData = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).stringToNumber(attackParts[i], ",");
                    this.shootInterval.push(attackData[0]);
                    this.bulletIDs.push(attackData[1]);
                    this.bulletNums.push(attackData[2]);
                    this.bulletIntervals.push(attackData[3]);
                    this.bulletAttackRates.push(attackData[4] / 100);
                    this.attackOverDelay.push(attackData[5]);
                  }
                }
              } catch (error) {
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).error("BossAttackPointData error:", data);
              }

              break;

            case 1:
              // 波次攻击
              const waveParts = parts[3].split(";");

              try {
                if (waveParts.length <= 1) {
                  this.bAvailable = false;
                  return;
                }

                const wavePosition = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToPoint(waveParts[0], ",");
                this.x = wavePosition.x;
                this.y = wavePosition.y;
                this.waveIds = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(waveParts[1], ",");

                if (waveParts.length > 2) {
                  this.attackOverDelay.push(parseInt(waveParts[2]));
                }
              } catch (error) {
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).error("BossAttackPointData error:", data);
              }

              break;

            default:
              (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).error("Unknown attack type:", this.atkType);
              break;
          }
        }

      });
      /**
       * Boss 攻击动作数据类
       */


      _export("BossAttackActionData", BossAttackActionData = class BossAttackActionData {
        constructor() {
          this.bAtkMove = false;
          // 是否移动攻击
          this.atkActId = 0;
          // 攻击动作 ID
          this.atkPointId = [];
        }

        // 攻击点 ID 列表

        /**
         * 从 JSON 数据加载攻击动作数据
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data, ",");

          try {
            if (parts.length > 1) {
              this.bAtkMove = parts[0] === 1;
              this.atkActId = parts[1];

              for (let i = 2; i < parts.length; i++) {
                this.atkPointId.push(parts[i]);
              }
            }
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("BossAttackActionData error:", data);
          }
        }

      });
      /**
       * 单位数据类
       */


      _export("UnitData", UnitData = class UnitData {
        constructor() {
          this.id = 0;
          this.uId = 0;
          this.type = 0;
          this.anim = "";
          this.img = [];
          this.imgPao = [];
          this.dam = "";
          this.pos = Vec2.ZERO;
          this.hurtColor = Color.RED;
          this.turn = [];
          this.hp = 0;
          this.hpParam = [];
          this.hpStage = [];
          this.collider = null;
          this.score = 0;
          this.blastParam = [];
          this.blastShake = [];
          this.blastSmoke = [];
          this.mixArr = [];
          this.param0 = [];
        }

        /**
         * 从 JSON 数据加载单位数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("id")) this.id = parseInt(data.id);
          if (data.hasOwnProperty("uId")) this.uId = parseInt(data.uId);
          if (data.hasOwnProperty("am")) this.anim = data.am;
          if (data.hasOwnProperty("im") && data.im !== "") this.img = data.im.split(",");
          if (data.hasOwnProperty("imp") && data.imp !== "") this.imgPao = data.imp.split(",");
          if (data.hasOwnProperty("dam")) this.dam = data.dam;
          if (data.hasOwnProperty("hp")) this.hp = parseInt(data.hp);
          if (data.hasOwnProperty("pos")) this.pos = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToPoint(data.pos, ",");
          if (data.hasOwnProperty("hpp")) this.hpParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hpp, ",");
          if (data.hasOwnProperty("hs")) this.hpStage = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hs, ",");

          if (data.hasOwnProperty("col")) {
            this.collider = new (_crd && EnemyCollider === void 0 ? (_reportPossibleCrUseOfEnemyCollider({
              error: Error()
            }), EnemyCollider) : EnemyCollider)();
            this.collider.loadJson(data.col);
          }

          if (data.hasOwnProperty("sco")) this.score = parseInt(data.sco);

          if (data.hasOwnProperty("hc")) {
            const colorData = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.hc, ",");

            if (colorData.length >= 3) {
              this.hurtColor = color(colorData[0], colorData[1], colorData[2]);
            }
          }

          if (data.hasOwnProperty("turn")) this.turn = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.turn, ",");

          if (data.hasOwnProperty("bla")) {
            const blaArray = data.bla.split("#");

            for (const bla of blaArray) {
              if (bla !== "") {
                const params = bla.split(";");
                const parsedParams = params.map(param => (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(param, ","));
                this.blastParam.push(parsedParams);
              }
            }
          }

          if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split("#");

            for (const sk of skArray) {
              if (sk !== "") {
                const params = sk.split(";");
                const parsedParams = params.map(param => (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(param, ","));
                this.blastShake.push(parsedParams);
              }
            }
          }

          if (data.hasOwnProperty("so")) {
            const soArray = data.so.split("#");

            for (const so of soArray) {
              if (so !== "") {
                const params = so.split(";");
                const parsedParams = params.map(param => (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).stringToNumber(param, ","));
                this.blastSmoke.push(parsedParams);
              }
            }
          }

          if (data.hasOwnProperty("mix")) {
            const mixArray = data.mix.split(";");

            for (const mix of mixArray) {
              if (mix !== "") {
                const parsedMix = mix.split(",");
                this.mixArr.push(parsedMix);
              }
            }
          }

          if (data.hasOwnProperty("v0") && data.v0 !== "") {
            this.param0 = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.v0, ",");
          }
        }

      });
      /**
       * Boss 单位数据类
       */


      _export("BossUnitData", BossUnitData = class BossUnitData {
        constructor() {
          this.id = 0;
          this.uId = 0;
          this.type = 0;
          this.image = "";
          this.damageImage = [];
          this.damagePos = Vec2.ZERO;
          this.uiParam = [];
          this.attr = "";
          this.hp = 0;
          this.hpParam = [];
          this.collider = [];
          this.recoil = false;
          this.hpFire = 0;
          this.hpFireParam = [];
          this.blastParam = [];
          this.blastShake = [];
          this.atkPos = [];
        }

        /**
         * 从 JSON 数据加载 Boss 单位数据
         * @param data JSON 数据
         */
        loadJson(data) {
          if (data.hasOwnProperty("id")) this.id = parseInt(data.id);
          if (data.hasOwnProperty("uId")) this.uId = parseInt(data.uId);
          if (data.hasOwnProperty("type")) this.type = parseInt(data.type);
          if (data.hasOwnProperty("img")) this.image = data.img;
          if (data.hasOwnProperty("dmg") && data.dmg !== "") this.damageImage = data.dmg.split(";");
          if (data.hasOwnProperty("dp") && data.dp !== "") this.damagePos = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToPoint(data.dp, ",");
          if (data.hasOwnProperty("ui") && data.ui !== "") this.uiParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.ui, ",");
          if (data.hasOwnProperty("attr")) this.attr = data.attr;
          if (data.hasOwnProperty("hp")) this.hp = parseInt(data.hp);
          if (data.hasOwnProperty("hpp")) this.hpParam = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.hpp, ",");
          if (data.hasOwnProperty("col")) this.collider = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.col, ",");
          if (data.hasOwnProperty("aa")) this.recoil = parseInt(data.aa) === 1;
          if (data.hasOwnProperty("fhp")) this.hpFire = parseInt(data.fhp);

          if (data.hasOwnProperty("fire")) {
            const fireArray = data.fire.split(";");

            for (const fire of fireArray) {
              if (fire !== "") this.hpFireParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(fire, ","));
            }
          }

          if (data.hasOwnProperty("bla")) {
            const blaArray = data.bla.split(";");

            for (const bla of blaArray) {
              if (bla !== "") this.blastParam.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(bla, ","));
            }
          }

          if (data.hasOwnProperty("sk")) this.blastShake = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToNumber(data.sk, ",");
          let atkIndex = 0;

          while (true) {
            const atkKey = "ap" + atkIndex++;
            if (!data.hasOwnProperty(atkKey) || data[atkKey] === "") break;
            this.atkPos.push((_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToPoint(data[atkKey], ","));
          }
        }

      });
      /**
       * Boss 子弹数据类
       */


      _export("BossBulletData", BossBulletData = class BossBulletData {
        constructor() {
          this.soundId = 0;
          this.shootInterval = [];
          this.bulletIDs = [];
          this.bulletNums = [];
          this.bulletIntervals = [];
          this.bulletAttackRates = [];
          this.attackOverDelay = [];
        }

        /**
         * 从 JSON 数据加载子弹数据
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = data.split(";");
          this.soundId = parseInt(parts[0]);

          for (let i = 1; i < parts.length; i++) {
            if (parts[i] !== "") {
              const bulletData = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(parts[i], ",");
              this.shootInterval.push(bulletData[0]);
              this.bulletIDs.push(bulletData[1]);
              this.bulletNums.push(bulletData[2]);
              this.bulletIntervals.push(bulletData[3]);
              this.bulletAttackRates.push(bulletData[4] / 100);
              this.attackOverDelay.push(bulletData[5]);
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a8b012d3e059b916f7a5fedf461dffc4fdf64c9e.js.map