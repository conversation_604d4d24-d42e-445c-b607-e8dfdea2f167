import { JsonAsset, NodePool, Prefab, resources, Sprite, SpriteAtlas, SpriteFrame, Node, instantiate, Vec2, Vec3 } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameIns } from "../GameIns";
import { Tools } from "../utils/Tools";
import { TrackData } from "../data/TrackData";
import { GameConst } from "../const/GameConst";
import { EnemyPlaneData, EnemyUIData } from "../data/EnemyData";
import { GameFunc } from "../GameFunc";
import EnemyPlane from "../ui/plane/enemy/EnemyPlane";
import GameEnum from "../const/GameEnum";
import BattleLayer from "../ui/layer/BattleLayer";
import ResourceList from "../const/GameResourceList";
import { TrackGroup } from "../data/EnemyWave";
import { MyApp } from "../../MyApp";
import GameResourceList from "../const/GameResourceList";


export class EnemyManager extends SingletonBase<EnemyManager> {

    _loadFinish = false;
    _mainStage = -1;
    _subStage = -1;
    _uiDataMap = new Map();
    _planeAnimMap = new Map<string, Prefab>();
    _normalCount = 0;
    _loadTotal = 0;
    _loadCount = 0;
    _trackDatas: Map<number, TrackData> = new Map();
    _planeDataMap = new Map();
    enemyAtlas = null;
    _pfPlane = null;
    _planePool: Node[] = [];
    _planeArr = [];
    _willDeadPlane = [];
    _animPlanePoolMap = new Map();
    _mapEnemyPool = new NodePool();
    missileAtlas = null;
    /**
     * 是否加载完成
     */
    public get loadFinish(): boolean {
        return this._loadFinish;
    }

    constructor() {
        super();
        this.initConfig();
    }

    initConfig() {
        let tracks = MyApp.lubanTables.TbTrack.getDataList();
        for (let track of tracks) {
            const trackData = new TrackData();
            trackData.loadJson(track);
            this._trackDatas.set(trackData.trackID, trackData);
        }


        let enemyUIDatas = MyApp.lubanTables.TbEnemyUI.getDataList();
        for (let data of enemyUIDatas) {
            const uiData = new EnemyUIData();
            uiData.loadJson(data);
            this._uiDataMap.set(uiData.id, uiData);
        }

        let planeList = MyApp.lubanTables.TbEnemy.getDataList();
        for (let plane of planeList) {
            const planeData = new EnemyPlaneData();
            planeData.loadJson(plane);
            this._planeDataMap.set(planeData.id, planeData);
        }
    }



    /**
     * 预加载资源
     * @param stage 当前关卡
     */
    public preLoad(stage: number) {

        // 加载敌机资源
        const enemyResources = [
            GameResourceList.EnemyPlane
        ];


        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(enemyResources, Prefab, async () => {
            this._pfPlane = await MyApp.resMgr.loadAsync(GameResourceList.EnemyPlane, Prefab);
            GameIns.battleManager.checkLoadFinish();
        });

        // 加载子弹图集
        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(GameResourceList.atlas_enemyBullet,SpriteAtlas, (error,atlas) => {
            this.missileAtlas = atlas;
            GameIns.battleManager.checkLoadFinish();
        });



        // 加载敌机图集
        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(ResourceList[`atlas_package_enemy${stage}`],SpriteAtlas, (error,atlas) => {
            this.enemyAtlas = atlas;
            GameIns.battleManager.checkLoadFinish();
        });

        for (let k in this._uiDataMap) {
            let uiData = this._uiDataMap[k];
            if (uiData.isAm) {
                GameIns.battleManager.addLoadCount(1);
                MyApp.resMgr.load("Game/"+uiData.image, Prefab, (error,prefab) => {
                    this._planeAnimMap.set(uiData.image, prefab);
                    GameIns.battleManager.checkLoadFinish();
                });
            }
        }
    }

    /**
     * 初始化敌人管理器
     * @param mainStage 主关卡
     * @param subStage 子关卡
     */
    initBattle(mainStage: number, subStage: number) {
        console.warn("stage", mainStage, subStage);
        if (this._mainStage !== mainStage) {
            this._mainStage = mainStage;
            this._subStage = subStage;

            if (this._planePool.length === 0) {
                const poolSize = 20;
                for (let i = 0; i < poolSize; i++) {
                    const plane = this.createNewPlane();
                    this._planePool.push(plane);
                }
            }
        } else {
            this._subStage = subStage;
        }
    }

    /**
 * 创建新的敌机节点
 * @param uiId 敌机 UI ID
 */
    createNewPlane(uiId: number = 0): Node {
        const node = instantiate(this._pfPlane);
        node.name = "plane";
        node.y = 1000;

        const plane:EnemyPlane = node.getComponent(EnemyPlane);
        plane.preLoad();

        if (uiId > 0) {
            const uiData = this.getEnemyUIData(uiId);
            plane.preLoadUI(uiData);
            node.y = 1000;
        } else {
            plane.preLoadUI(null);
        }

        return node;
    }

    /**
     * 获取敌机 UI 数据
     * @param id 敌机 UI ID
     */
    getEnemyUIData(id: number): EnemyUIData {
        return this._uiDataMap.get(id);
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        for (let i = 0; i < this._planeArr.length; i++) {
            const plane = this._planeArr[i];
            if (plane.removeAble) {
                this.removePlaneForIndex(i);
                i--;
            } else {
                if (plane.isDead) {
                    if (plane.type === GameEnum.EnemyType.Turret || plane.type === GameEnum.EnemyType.Ship) {
                        this._willDeadPlane.push(plane);
                        this._planeArr.splice(i, 1);
                        i--;
                        continue;
                    }
                }
                plane.updateGameLogic(deltaTime);
            }
        }

        for (let i = 0; i < this._willDeadPlane.length; i++) {
            const plane = this._willDeadPlane[i];
            if (plane.removeAble) {
                this.removePlaneForIndex(i, true);
                i--;
            } else {
                plane.updateGameLogic(deltaTime);
            }
        }
    }

    /**
* 重置主关卡
*/
    mainReset() {
        this.subReset();

        this._mainStage = -1;
        this._subStage = -1;

        // 清理飞机池
        for (const plane of this._planePool) {
            plane.destroy();
        }
        this._planePool.splice(0);


        // 清理即将死亡的飞机
        for (const plane of this._willDeadPlane) {
            if (plane && plane.node) {
                plane.node.destroy();
            }
        }
        this._willDeadPlane = [];


        // // 清理动画飞机池
        // this._animPlanePoolMap.forEach((pool) => {
        //     for (const node of pool) {
        //         node.destroy();
        //     }
        // });
        // this._animPlanePoolMap.clear();
    }

    /**
     * 重置子关卡
     */
    subReset() {
        let EnemyType = GameEnum.EnemyType;
        for (const plane of this._planeArr) {
            switch (plane.type) {
                case EnemyType.Normal:
                    plane.willRemove();
                    if (plane.sceneLayer < 0) {
                        plane.node.y = 1000;
                        const pool = plane.getUIData()?.isAm
                            ? this._animPlanePoolMap.get(plane.getUIData().id) || []
                            : this._planePool;
                        pool.push(plane.node);
                    } else {
                        this._mapEnemyPool.put(plane.node);
                    }
                    break;
            }
            plane.node.removeFromParent(false);
        }
        this._planeArr.splice(0);
    }


    /**
     * 清理敌人管理器
     */
    clear() {

    }
    /**
     * 获取敌机角色
     * @param uiId 敌机 UI ID
     */
    async getPlaneRole(uiId: string): Promise<Prefab> {
        let prefab = this._planeAnimMap.get(uiId);
        if (!prefab) {
            prefab = await MyApp.resMgr.loadAsync("Game/prefabs/"+uiId, Prefab);
            this._planeAnimMap.set(uiId, prefab);
        }
        return prefab;
    }

    /**
     * 检查敌人是否全部消灭
     */
    isEnemyOver(): boolean {
        return this._planeArr.length === 0;
    }

    /**
     * 获取普通敌机数量
     */
    getNormalPlaneCount(): number {
        return this._normalCount;
    }

    /**
     * 检查敌机是否为动画类型
     * @param id 敌机 ID
     */
    isEnemyAnim(id: number): boolean {
        const planeData = this.getPlaneData(id);
        const uiData = this.getEnemyUIData(planeData.uiId);
        return uiData?.isAm || false;
    }


    /**
     * 设置敌机的精灵帧
     * @param sprite 精灵组件
     * @param frameName 精灵帧名称
     */
    setPlaneFrame(sprite: Sprite, frameName: string) {
        if (sprite && frameName !== "" && this.enemyAtlas) {
            sprite.spriteFrame = this.enemyAtlas.getSpriteFrame(frameName);
            sprite.sizeMode = Sprite.SizeMode.RAW;
        }
    }

    /**
     * 获取敌机的精灵帧
     * @param frameName 精灵帧名称
     */
    getPlaneFrame(frameName: string): SpriteFrame | null {
        return this.enemyAtlas ? this.enemyAtlas.getSpriteFrame(frameName) : null;
    }

    /**
     * 根据轨迹 ID 获取轨迹数据
     * @param trackId 轨迹 ID
     */
    getTrackDataForID(trackId: number): TrackData | null {
        let trackData: TrackData | null = null;
        try {
            trackData = this._trackDatas.get(trackId) || null;
        } catch (error) {
            Tools.error(`getTrackData error: ${trackId}`, error);
        }
        return trackData;
    }

    /**
     * 获取所有敌机
     */
    get planes(): EnemyPlane[] {
        return this._planeArr;
    }

    /**
     * 移除所有存活的敌机
     */
    removeAllAlivePlane() {
        for (const plane of this._planeArr) {
            if (!plane.isDead) {
                plane.willRemove();
            }
        }
    }

    /**
     * 添加敌机到管理器
     * @param plane 敌机对象
     */
    pushPlane(plane: EnemyPlane) {
        if (!Tools.arrContain(this._planeArr, plane)) {
            this._planeArr.push(plane);
        }
    }

    /**
     * 根据索引移除敌机
     * @param index 索引
     * @param isDead 是否为死亡敌机
     */
    removePlaneForIndex(index: number, isDead: boolean = false) {
        if (isDead) {
            this._willRemovePlane(this._willDeadPlane[index]);
            this._willDeadPlane.splice(index, 1);
        } else {
            this._willRemovePlane(this._planeArr[index]);
            this._planeArr.splice(index, 1);
        }
    }

    //     /**
    //      * 移除指定敌机
    //      * @param plane 敌机对象
    //      * @param isDead 是否为死亡敌机
    //      */
    //     removePlane(plane: EnemyPlane, isDead: boolean = false) {
    //         this._willRemovePlane(plane);
    //         if (isDead) {
    //             Tools.arrRemove(this._willDeadPlane, plane);
    //         } else {
    //             Tools.arrRemove(this._planeArr, plane);
    //         }
    //     }

    /**
     * 处理即将移除的敌机
     * @param plane 敌机对象
     */
    _willRemovePlane(plane: EnemyPlane) {
        let EnemyType = GameEnum.EnemyType;
        switch (plane.type) {
            case EnemyType.Normal:
                if (plane.sceneLayer < 0) {
                    plane.node.y = 1000;
                    this._normalCount--;
                    const pool = plane.getUIData()?.isAm
                        ? this._animPlanePoolMap.get(plane.getUIData().id) || []
                        : this._planePool;
                    pool.push(plane.node);
                } else {
                    plane.node.x = 1000;
                    this._mapEnemyPool.put(plane.node);
                }
                break;
        }
        plane.node.removeFromParent();
    }

    /**
     * 根据敌机 ID 获取敌机数据
     * @param id 敌机 ID
     */
    getPlaneData(id: number): EnemyPlaneData {
        const data = this._planeDataMap.get(id);
        if (!data) {
            Tools.error(`getEnemyData error: ${id}`);
        }
        return data!;
    }

    /**
     * 添加敌机
     * @param id 敌机 ID
     */
    async addPlane(id: number) {
        try {
            const planeData = this.getPlaneData(id);
            let node: Node = null;

            if (this.isEnemyAnim(id)) {
                let pool = this._animPlanePoolMap.get(planeData.uiId);
                if (!pool) {
                    pool = [];
                    this._animPlanePoolMap.set(planeData.uiId, pool);
                }
                node = pool.pop() || this.createNewPlane(planeData.uiId);
            } else {
                node = this._planePool.pop() || this.createNewPlane();
            }

            BattleLayer.me.addEnemy(node);
            const plane = node.getComponent(EnemyPlane);
            await plane.init(planeData);
            plane.new_uuid = GameFunc.uuid;
            plane.setScaleType(this.getEnemyRangeType(id));
            this.pushPlane(plane);

            // if (GameIns.battleManager.shadowAble(GameIns.gameDataManager.curMainStage)) {
            //     const uiData = this.getEnemyUIData(planeData.uiId);
            //     plane.shadowComp = ShadowManager.addShadow(uiData.image + "y", node, v2(99, -165));
            // }

            this._normalCount++;
            return plane;
        } catch (error) {
            return null;
        }
    }

    /**
     * 检查敌人死亡炸弹
     * @param position 炸弹位置
     */
    checkEnemyDieBomb(position: Vec3) {
        // if (this._dieBombAtk > 0) {
        //     const enemies = GameFunc.getRangeEnemys(position, this._dieBombRadius);
        //     enemies.forEach((enemy) => {
        //         if (!enemy.isDead) {
        //             enemy.hurt(this._dieBombAtk);
        //         }
        //     });
        //     EnemyEffectLayer.me.playDieBombAnim(position);
        // }
    }

    /**
     * 获取敌机的范围类型
     * @param id 敌机 ID
     */
    getEnemyRangeType(id: number): number {
        if ((id >= 1000 && id <= 1999) || (id >= 4000 && id <= 4999)) {
            return 0; // 小型
        } else if ((id >= 2000 && id <= 2999) || (id >= 5000 && id <= 6999)) {
            return 1; // 中型
        } else if (id >= 3000 && id <= 3999) {
            return 2; // 大型
        }
        return -1; // 未知
    }

    /**
     * 添加护盾
     */
    addShield() {
        // let shieldNode = this._shieldPool.pop();
        // if (!shieldNode) {
        //     shieldNode = instantiate(this._pfShield);
        //     shieldNode.getComponent(EnemyShield).preLoad();
        // }
        // const shield = shieldNode.getComponent(EnemyShield);
        // this._shieldArr.push(shield);
        // return shield;
    }

    /**
     * 移除护盾
     * @param shield 要移除的护盾
     */
    removeShield(shield) {
        // shield.node.parent = null;
        // Tools.arrRemove(this._shieldArr, shield);
        // this._shieldPool.push(shield.node);
    }

    // /**
    //  * 获取所有护盾
    //  */
    // get shieldArr(): EnemyShield[] {
    //     return this._shieldArr;
    // }
}