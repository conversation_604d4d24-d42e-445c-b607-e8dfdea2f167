System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, Sprite, tween, Enum, Vec2, sp, v3, UITransform, Tween, size, Bullet, MainPlane, GameEnum, BossHurt, Tools, GameIns, FBoxCollider, ColliderGroupType, _dec, _class, _class2, _crd, ccclass, property, BossUnit;

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../mainPlane/MainPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossHurt(extras) {
    _reporterNs.report("BossHurt", "./BossHurt", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      Enum = _cc.Enum;
      Vec2 = _cc.Vec2;
      sp = _cc.sp;
      v3 = _cc.v3;
      UITransform = _cc.UITransform;
      Tween = _cc.Tween;
      size = _cc.size;
    }, function (_unresolved_2) {
      Bullet = _unresolved_2.default;
    }, function (_unresolved_3) {
      MainPlane = _unresolved_3.MainPlane;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.default;
    }, function (_unresolved_5) {
      BossHurt = _unresolved_5.default;
    }, function (_unresolved_6) {
      Tools = _unresolved_6.Tools;
    }, function (_unresolved_7) {
      GameIns = _unresolved_7.GameIns;
    }, function (_unresolved_8) {
      FBoxCollider = _unresolved_8.default;
    }, function (_unresolved_9) {
      ColliderGroupType = _unresolved_9.ColliderGroupType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c8e6e/aeORKIrkBuJI4ifK0", "BossUnit", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Sprite', 'tween', 'Color', 'Enum', 'Vec2', 'sp', 'v2', 'v3', 'UITransform', 'Tween', 'size']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossUnit = (_dec = ccclass('BossUnit'), _dec(_class = (_class2 = class BossUnit extends (_crd && BossHurt === void 0 ? (_reportPossibleCrUseOfBossHurt({
        error: Error()
      }), BossHurt) : BossHurt) {
        constructor(...args) {
          super(...args);
          this._data = null;
          this._bossPlane = null;
          this._collideComp = void 0;
          this._unitType = 0;
          this._curHp = 0;
          this._maxHp = 0;
          this._hpBar = null;
          this._hpWhite = null;
          this._hpWhiteTween = null;
          this.defence = 0;
          this._hpStage = 0;
          this._hpStageIndex = 0;
          this._action = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal;
          this._damaged = false;
          this._damageable = false;
          this._bSmoking = false;
          this._whiteNode = null;
          this._winkCount = 0;
          this._bWinkWhite = false;
          this._winkAct = null;
          this._skel = null;
          this._curAnim = '';
          this._skelCallMap = new Map();
          this._skelEventCallMap = new Map();
          this._smokeSkelPool = [];
          this._smokePosArr = [];
          this._smokeSkelArr = [];
          this._smokeBoneArr = [];
        }

        onLoad() {// 初始化逻辑
        }

        init(data, bossPlane) {
          this.reset();
          this.type = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType.BossUnit;
          this._data = data;
          this._bossPlane = bossPlane;
          this.initData();

          this._initUI();

          this._refreshHpBar();

          this.setSkin(0);

          if (this._curHp > 0) {
            this._unitType = 1;
            this.isDead = false;

            this._initCollide();

            this._checkHpStage();
          } else {
            this._unitType = 0;
            this.isDead = true;
          }
        }

        reset() {
          this._curAnim = '';
          this._hpStage = 0;
          this._hpStageIndex = 0;
          this._action = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal;
          this.removeSmoke();
        }

        initData() {
          this._curHp = this._data.hp;
          this._maxHp = this._curHp;
        }

        _initCollide() {
          const colliderData = this._data.collider;
          this._collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);

          this._collideComp.init(this, size(colliderData.width, colliderData.height)); // 初始化碰撞组件


          this._collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL;
          this._collideComp.isEnable = false;
        }

        _initUI() {
          this.node.setPosition(this._data.pos.x, this._data.pos.y);

          switch (this._data.type) {
            case 0:
              if (!this._skel) {
                const skelNode = new Node("skelNode");
                this.node.addChild(skelNode); // this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel)

                this._skel = skelNode.addComponent(sp.Skeleton);
                this._skel.skeletonData = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).bossManager.skelDataMap.get(this._data.anim);
                this._skel.premultipliedAlpha = false;

                this._bossPlane.addSpine(this._skel);

                this._data.mixArr.forEach(mix => {
                  try {
                    this._skel.setMix(mix[0], mix[1], 0.5);
                  } catch (error) {
                    (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                      error: Error()
                    }), Tools) : Tools).log('Boss unit mix error:', mix);
                  }
                });

                this._skel.setCompleteListener(trackEntry => {
                  const animationName = trackEntry.animation ? trackEntry.animation.name : '';

                  this._skelCallMap.forEach((callback, key) => {
                    if (animationName === key && callback) {
                      callback();
                    }
                  });
                });

                this._skel.setEventListener((trackEntry, event) => {
                  const eventName = event.data.name;

                  this._skelEventCallMap.forEach((callback, key) => {
                    if (eventName === key && callback) {
                      callback();
                    }
                  });
                });
              } // this._winkAct = new Tween()
              //     .to(0, { color: new Color(this._data.hurtColor.getR(), this._data.hurtColor.getG(), this._data.hurtColor.getB()) })
              //     .to(0.1, { color: Color.WHITE });


              break;

            case 1:
              const skelNode = new Node();
              skelNode.addComponent(UITransform);
              this.node.addChild(skelNode);
              this.node.setSiblingIndex(BossUnit.UnitZIndex.Skel); // this._winkAct = tween()
              //     .to(0, { opacity: 180 })
              //     .to(3 * GameConst.ActionFrameTime, { opacity: 0 });

              break;
          }

          if (this._data.hpParam[0] !== 0) {
            let hpNode = this._bossPlane.node.getChildByName(`blood${this._data.uId}`);

            if (!hpNode) {
              hpNode = new Node();
              hpNode.addComponent(UITransform);
              hpNode.name = `blood${this._data.uId}`;
              hpNode.parent = this._bossPlane.node;
              hpNode.setSiblingIndex(10);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.setBossFrame(hpNode.addComponent(Sprite), 'hp_0');
              const whiteNode = new Node("whiteNode");
              whiteNode.addComponent(UITransform);
              hpNode.addChild(whiteNode);
              this._hpWhite = whiteNode.addComponent(Sprite);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.setBossFrame(this._hpWhite, 'hp_2');
              this._hpWhite.type = Sprite.Type.FILLED;
              this._hpWhite.fillType = Sprite.FillType.HORIZONTAL;
              this._hpWhite.fillRange = 1;
              const barNode = new Node("barNode");
              barNode.addComponent(UITransform);
              hpNode.addChild(barNode);
              this._hpBar = barNode.addComponent(Sprite);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.setBossFrame(this._hpBar, 'hp_1');
              this._hpBar.type = Sprite.Type.FILLED;
              this._hpBar.fillType = Sprite.FillType.HORIZONTAL;
              this._hpBar.fillRange = 1;
            }

            let posX = this._data.hpParam[1] + this.node.position.x;
            let posY = this._data.hpParam[2] + this.node.position.y;
            let scaleX = this._data.hpParam[3];
            hpNode.setPosition(posX, posY);
            hpNode.setScale(scaleX * hpNode.getScale().x, hpNode.getScale().y);
            hpNode.active = false;
          }
        }

        updateGameLogic(deltaTime) {
          this._smokeSkelArr.forEach((smoke, index) => {
            const boneName = this._smokeBoneArr[index];

            const bone = this._skel.findBone(boneName);

            if (bone) {
              let pos = this._smokePosArr[index].add(new Vec2(bone.worldX, bone.worldY));

              smoke.node.setPosition(pos.x, pos.y);
            }
          });

          if (!this.isDead) {
            if (this._bWinkWhite) {
              this._winkCount++;

              if (this._winkCount > 8) {
                this._winkCount = 0;
                this._bWinkWhite = false;
              }
            }

            this.m_comps.forEach(comp => {
              comp.update(deltaTime);
            });
          }
        }

        onCollide(collider) {
          if (!this.isDead && this.damageable) {
            if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
              error: Error()
            }), Bullet) : Bullet)) {
              let damage = collider.entity.getAttack(this);

              try {
                (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
              } catch (error) {
                console.error(error);
              }

              damage = Math.max(damage / 10, damage - this.defence);
              this.hurt(damage);
            } else if (collider.entity instanceof (_crd && MainPlane === void 0 ? (_reportPossibleCrUseOfMainPlane({
              error: Error()
            }), MainPlane) : MainPlane)) {// Handle collision with the main plane
            }
          }
        }

        hurt(damage) {
          if (this.isDead || !this.damageable) {
            return false;
          }

          this.hpChange(-damage);

          this._checkHp();

          if (!this.isDead) {
            this._winkWhite();
          }

          return true;
        }

        hpChange(amount) {
          let change = amount;
          let newHp = this._curHp + amount;

          if (newHp < 0) {
            change = -this._curHp;
          }

          this._curHp = newHp;

          if (this._curHp < 0) {
            this._curHp = 0;
          }

          if (this._bossPlane) {
            this._bossPlane.hpChange(change);
          }

          this._refreshHpBar();
        }

        _checkHp() {
          if (this._curHp <= this._hpStage) {
            this._damaged = true;

            this._playStageAnim(this._hpStageIndex + 1);

            this._hpStageIndex++;

            this._checkHpStage();

            if (this._hpStage < 0) {
              this._die();
            }
          }
        }

        _checkHpStage() {
          if (this._hpStageIndex < this._data.hpStage.length) {
            this._hpStage = this._data.hpStage[this._hpStageIndex];
          } else {
            this._hpStage = -1;
          }
        }

        _die() {
          this.isDead = true;

          try {
            if (this._hpBar) {
              this._hpBar.node.parent.active = false;
            }
          } catch (error) {
            console.error(error);
          }

          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          }

          if (this._collideComp) {
            this._collideComp.isEnable = false;
          }

          this._playDieAnim();

          if (this._bossPlane) {
            this._bossPlane.unitDestroyed(this);
          }
        }

        _refreshHpBar() {
          if (this._hpBar) {
            if (this._hpWhiteTween) {
              this._hpWhiteTween.stop();

              this._hpWhiteTween = null;
            }

            this._hpBar.fillRange = this._curHp / this._maxHp;
            const fillDifference = Math.abs(this._hpWhite.fillRange - this._hpBar.fillRange);
            this._hpWhiteTween = new Tween(this._hpWhite).to(fillDifference, {
              fillRange: this._hpBar.fillRange
            }).call(() => {
              this._hpWhiteTween = null;
            }).start();
          }
        }

        playSkel(animationName, loop, callback = null) {
          if (!this._skel || this._curAnim === animationName) {
            return false;
          }

          this._curAnim = animationName;

          this._skelCallMap.set(animationName, callback);

          this._skel.setAnimation(0, animationName, loop);

          return true;
        }

        setEventCallback(eventName, callback) {
          this._skelEventCallMap.set(eventName, callback);
        }

        setSkin(skinIndex) {
          if (this._skel) {
            this._skel.setSkin(`s${this._bossPlane.formIndex + 1}_${skinIndex}`);
          }
        }

        setAction(action) {
          if (!this.isDead) {
            this._action = action;
          }
        }

        _winkWhite() {// if (!this._bWinkWhite && this._action < 2) { // Assuming 2 is the threshold for attack actions
          //     this._bWinkWhite = true;
          //     if (this._skel && this._skel.node) {
          //         this._winkAct.clone(this._skel.node).start();
          //     }
          // }
        }

        _playShakeAnim() {
          const actionFrameTime = 0.016; // Assuming 60 FPS

          tween(this.node).to(actionFrameTime, {
            position: v3(11, -16),
            angle: -1
          }).to(2 * actionFrameTime, {
            position: v3(7, 2),
            angle: 1
          }).to(2 * actionFrameTime, {
            position: v3(20, -11),
            angle: 0
          }).to(2 * actionFrameTime, {
            position: v3(28, 5)
          }).to(2 * actionFrameTime, {
            position: v3(13, -7)
          }).to(actionFrameTime, {
            position: v3(17, 1)
          }).to(actionFrameTime, {
            position: v3(4, -8)
          }).to(actionFrameTime, {
            position: v3(14, 2)
          }).to(actionFrameTime, {
            position: v3(-1, -6)
          }).to(actionFrameTime, {
            position: v3(5, 4)
          }).to(actionFrameTime, {
            position: v3(-3, -7)
          }).to(actionFrameTime, {
            position: v3(2, 1)
          }).to(actionFrameTime, {
            position: v3(0, 0)
          }).start();
        }

        hideSmoke() {
          for (const smoke of this._smokeSkelArr) {
            tween(smoke.node).to(5 * 0.016, {
              opacity: 0
            }) // Assuming 60 FPS
            .start();
          }
        }

        removeSmoke() {
          for (const smoke of this._smokeSkelArr) {
            smoke.node.active = false;

            this._smokeSkelPool.push(smoke);

            this._bossPlane.removeSpine(smoke);
          }

          this._smokeSkelArr.length = 0;
          this._smokePosArr.length = 0;
          this._smokeBoneArr.length = 0;
        }

        get id() {
          return this._data.id;
        }

        get unitId() {
          return this._data.uId;
        }

        isBody() {
          return this._unitType === 0;
        }

        getUnitType() {
          return this._data.type;
        }

        setPropertyRate(rates) {
          if (rates.length > 2) {
            this._curHp *= rates[0];
            this._maxHp = this._curHp;
            this.attack *= rates[1];
            this._collideAtk *= rates[2];
          }
        }

        get damageable() {
          return this._damageable;
        }

        set damageable(value) {
          this._damageable = value;
        }

        get bossEntity() {
          return this._bossPlane;
        }

        get curHp() {
          return this._curHp;
        }

        get maxHp() {
          return this._maxHp;
        }

        _playStageAnim(stageIndex) {
          this._onStageAnimEnd();

          this.setSkin(stageIndex);
        }

        _onStageAnimEnd() {
          try {
            if (this._bossPlane) {
              this._bossPlane.unitDestroyAnimEnd(this);
            }

            if (this.isDead) {
              this.playSkel(`idle${this._bossPlane.formIndex + 1}`, true);
            }
          } catch (error) {
            console.error(error);
          }
        }

        _getSmokeAnim() {
          let smokeAnim = this._smokeSkelPool.pop();

          if (!smokeAnim) {
            const smokeNode = new Node();
            smokeNode.addComponent(UITransform);
            this.node.addChild(smokeNode);
            smokeAnim = smokeNode.addComponent(sp.Skeleton);
            smokeAnim.skeletonData = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.smokeSkelData;
            smokeAnim.premultipliedAlpha = false;

            this._smokeSkelPool.push(smokeAnim);
          }

          smokeAnim.node.active = true;
          return smokeAnim;
        }

        _playDieAnim() {
          if (this._data.id >= 200 && this._data.id < 250) {
            return;
          }

          this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);
        }

        setCollideAble(isEnabled) {
          if (!this.isBody() && this._collideComp) {
            this.active = isEnabled;
            this._collideComp.isEnable = isEnabled;

            try {
              if (this._hpBar) {
                this._hpBar.node.parent.active = isEnabled;
              }
            } catch (error) {
              console.error("Error setting collide ability:", error);
            }
          }
        }

        getCollideAble() {
          return this._collideComp ? this._collideComp.isEnable : false;
        }

      }, _class2.UnitZIndex = Enum({
        SmokeBottom: -10,
        Skel: -9,
        SmokeTop: -8
      }), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1c9b50f204ca398abc6070f3ea138dda5f0bd096.js.map