{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts"], "names": ["_decorator", "CCFloat", "Component", "JsonAsset", "CCBoolean", "CCString", "assetManager", "log", "LevelData", "LevelEditorBaseUI", "ccclass", "property", "executeInEditMode", "LevelEditorUI", "type", "range", "slide", "visible", "group", "name", "displayName", "_levelPrefab", "baseCom", "_levelElapsedTime", "levelPrefab", "value", "uuid", "levelData", "fromJSON", "json", "node", "getComponent", "initByLevelData", "levelPrefabUUID", "loadAny", "err", "asset", "save", "JSON", "stringify", "data", "genLevelData", "Editor", "Message", "send", "new", "newName", "filePath", "jsonString", "p", "request", "then", "res", "levelElapsedTime", "start", "checkLevelPrebab", "update", "deltaTime", "tick", "progress", "fillLevelData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAkDC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAA4BC,MAAAA,Y,OAAAA,Y;AAAoCC,MAAAA,G,OAAAA,G;;AAE7JC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAFH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;+BAMpCa,a,WAFZH,OAAO,CAAC,eAAD,C,UACPE,iBAAiB,E,UAGbD,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACb,OADC;AAENc,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,IAAP,CAFD;AAGNC,QAAAA,KAAK,EAAE,IAHD;AAINC,QAAAA,OAAO,EAAE;AAJH,OAAD,C,UAYRN,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACX;AADC,OAAD,C,UAcRQ,QAAQ,CAACN,QAAD,C,UAmBRM,QAAQ,CAACP,SAAD,C,UAkBRO,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAC;AAACC,UAAAA,IAAI,EAAC;AAAN,SADA;AACcL,QAAAA,IAAI,EAACT,QADnB;AAC6Be,QAAAA,WAAW,EAAC;AADzC,OAAD,C,UAIRT,QAAQ,CAAC;AACNO,QAAAA,KAAK,EAAC;AAACC,UAAAA,IAAI,EAAC;AAAN,SADA;AACcL,QAAAA,IAAI,EAACV;AADnB,OAAD,C,0CAvEb,MAEaS,aAFb,SAEmCX,SAFnC,CAE6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAYjCmB,YAZiC,GAYA,IAZA;;AAAA;;AAAA,eAiGjCC,OAjGiC,GAiGL,IAjGK;AAAA,eAmGzCC,iBAnGyC,GAmGb,CAnGa;AAAA;;AAiBnB,YAAXC,WAAW,CAACC,KAAD,EAA0B;AAAA;;AAC5C,cAAI,4BAAKJ,YAAL,wCAAmBK,IAAnB,MAA2BD,KAA3B,oBAA2BA,KAAK,CAAEC,IAAlC,CAAJ,EAA4C;AACxC,iBAAKL,YAAL,GAAoBI,KAApB;AACA,gBAAIE,SAAS,GAAG;AAAA;AAAA,wCAAUC,QAAV,CAAmBH,KAAnB,oBAAmBA,KAAK,CAAEI,IAA1B,CAAhB;AACA,iBAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,wDAA6DC,eAA7D,CAA6EL,SAA7E;AACH;AACJ;;AACqB,YAAXH,WAAW,GAAc;AAChC,iBAAO,KAAKH,YAAZ;AACH;;AAGyB,YAAfY,eAAe,CAACR,KAAD,EAAgB;AACtClB,UAAAA,GAAG,CAAC,mCAAD,EAAsCkB,KAAtC,CAAH;;AACA,cAAIA,KAAK,IAAI,IAAT,IAAiBA,KAAK,IAAI,EAA9B,EAAkC;AAC9B,iBAAKD,WAAL,GAAmB,IAAnB;AACA;AACH;;AACDlB,UAAAA,YAAY,CAAC4B,OAAb,CAAqB;AAACR,YAAAA,IAAI,EAACD;AAAN,WAArB,EAAmC,CAACU,GAAD,EAAMC,KAAN,KAAgB;AAC/C,gBAAID,GAAJ,EAAS;AACL5B,cAAAA,GAAG,CAAC,+CAAD,EAAkD4B,GAAlD,CAAH;AACA;AACH;;AACD,iBAAKX,WAAL,GAAmBY,KAAnB;AACH,WAND;AAOH;;AACyB,YAAfH,eAAe,GAAY;AAAA;;AAClC,wCAAO,KAAKZ,YAAZ,qBAAO,oBAAmBK,IAA1B;AACH;;AAGc,YAAJW,IAAI,GAAE;AACb,iBAAO,KAAP;AACH;;AACc,YAAJA,IAAI,CAACZ,KAAD,EAAQ;AACnBlB,UAAAA,GAAG,CAAC,oBAAD,CAAH;;AACA,cAAI,CAACkB,KAAL,EAAY;AACR;AACH;;AACD,cAAI,KAAKJ,YAAL,IAAqB,IAAzB,EAA+B;AAC3B;AACH;;AACDd,UAAAA,GAAG,CAAE,SAAQ+B,IAAI,CAACC,SAAL,CAAe,KAAKlB,YAAL,CAAkBQ,IAAjC,CAAuC,GAAjD,CAAH;AACA,cAAIW,IAAI,GAAG,KAAKC,YAAL,EAAX,CATmB,CAUnB;;AACAC,UAAAA,MAAM,CAACC,OAAP,CAAeC,IAAf,CAAoB,UAApB,EAAgC,YAAhC,EAA8C,KAAKvB,YAAL,CAAkBK,IAAhE,EAAsEY,IAAI,CAACC,SAAL,CAAeC,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAAtE;AACAjC,UAAAA,GAAG,CAAC,4BAAD,CAAH;AACH;;AAQa,YAAHsC,GAAG,GAAE;AACZ,iBAAO,KAAP;AACH;;AACa,YAAHA,GAAG,CAACpB,KAAD,EAAQ;AAClB,cAAIA,KAAJ,EAAW;AACPlB,YAAAA,GAAG,CAAE,aAAY,KAAKuC,OAAQ,EAA3B,CAAH;AACH;;AAED,gBAAMC,QAAQ,GAAI,oCAAmC,KAAKD,OAAQ,OAAlE;AACA,cAAInB,SAAS,GAAG;AAAA;AAAA,uCAAhB;AACA,gBAAMqB,UAAU,GAAGV,IAAI,CAACC,SAAL,CAAeZ,SAAf,EAA0B,IAA1B,EAAgC,CAAhC,CAAnB,CAPkB,CASlB;;AACA,cAAIsB,CAAC,GAAGP,MAAM,CAACC,OAAP,CAAeO,OAAf,CAAuB,UAAvB,EAAmC,cAAnC,EAAmDH,QAAnD,EAA6DC,UAA7D,CAAR;AACAC,UAAAA,CAAC,CAACE,IAAF,CAAQC,GAAD,IAAS;AACZ9C,YAAAA,YAAY,CAAC4B,OAAb,CAAqB;AAACR,cAAAA,IAAI,EAAC0B,GAAG,CAAC1B;AAAV,aAArB,EAAsC,CAACS,GAAD,EAAMC,KAAN,KAAgB;AAClD,kBAAID,GAAJ,EAAS;AACL5B,gBAAAA,GAAG,CAAC4B,GAAD,CAAH;AACH,eAFD,MAEO;AACH,qBAAKX,WAAL,GAAmBY,KAAnB;AACH;AACJ,aAND;AAOH,WARD;AASH;;AAK0B,YAAhBiB,gBAAgB,GAAW;AAClC,iBAAO,KAAK9B,iBAAZ;AACH;;AAED+B,QAAAA,KAAK,GAAQ;AACT/C,UAAAA,GAAG,CAAC,eAAD,CAAH;AACA,eAAKe,OAAL,GAAe,KAAKQ,IAAL,CAAUC,YAAV;AAAA;AAAA,qDAAf;AACA,eAAKP,WAAL,GAAmB,IAAnB;AACH;;AACO+B,QAAAA,gBAAgB,GAAU;AAC9B,cAAI,KAAKlC,YAAL,IAAqB,IAAzB,EAA+B;AAC3B;AACH;AACJ;;AACDmC,QAAAA,MAAM,CAACC,SAAD,EAA2B;AAC7B,eAAKC,IAAL;AACH;;AACOA,QAAAA,IAAI,GAAQ;AAChB,cAAI,KAAKrC,YAAL,IAAqB,IAAzB,EAA+B;AAC3B;AACH;;AACD,eAAKC,OAAL,CAAaoC,IAAb,CAAkB,KAAKC,QAAvB;AACH;;AAEOlB,QAAAA,YAAY,GAAe;AAC/B,cAAID,IAAI,GAAG;AAAA;AAAA,uCAAX;AACA,eAAKV,IAAL,CAAUC,YAAV;AAAA;AAAA,sDAA6D6B,aAA7D,CAA2EpB,IAA3E;AACA,iBAAOA,IAAP;AACH;;AAhIwC,O;;;;;iBAQtB,C;;gFAClB7B,Q;;;;;iBACO,E;;;;;;;iBA0DgB,E", "sourcesContent": ["import { _decorator, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Canvas, log, math } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LevelData } from '../../scripts/leveldata/leveldata';\r\nimport { LevelEditorBaseUI } from './LevelEditorBaseUI';\r\n\r\n@ccclass('LevelEditorUI')\r\n@executeInEditMode()\r\nexport class LevelEditorUI extends Component {\r\n\r\n    @property({\r\n        type:CCFloat,\r\n        range: [0, 1, 0.01],\r\n        slide: true,\r\n        visible: true,\r\n    })\r\n    progress: number = 0;\r\n    @property\r\n    label = \"\"\r\n\r\n    private _levelPrefab: JsonAsset | null = null;\r\n\r\n    @property({\r\n        type:JsonAsset,\r\n    })\r\n    public set levelPrefab(value: JsonAsset | null) {\r\n        if (this._levelPrefab?.uuid != value?.uuid) {\r\n            this._levelPrefab = value;\r\n            var levelData = LevelData.fromJSON(value?.json);\r\n            this.node.getComponent<LevelEditorBaseUI>(LevelEditorBaseUI).initByLevelData(levelData);\r\n        }\r\n    }\r\n    public get levelPrefab() : JsonAsset{\r\n        return this._levelPrefab;\r\n    }\r\n\r\n    @property(CCString) \r\n    public set levelPrefabUUID(value: string) {\r\n        log(\"LevelEditorUI set levelPrefabUUID\", value)\r\n        if (value == null || value == \"\") {\r\n            this.levelPrefab = null;\r\n            return;\r\n        }\r\n        assetManager.loadAny({uuid:value}, (err, asset) => {\r\n            if (err) {\r\n                log(\"LevelEditor set levelPrefabUUID but load err:\", err);\r\n                return\r\n            }\r\n            this.levelPrefab = asset;\r\n        });\r\n    }\r\n    public get levelPrefabUUID() : string {\r\n        return this._levelPrefab?.uuid;\r\n    }\r\n\r\n    @property(CCBoolean)\r\n    public get save(){\r\n        return false;\r\n    }\r\n    public set save(value) {\r\n        log(\"LevelEditorUI save\")\r\n        if (!value) {\r\n            return;\r\n        }\r\n        if (this._levelPrefab == null) {\r\n            return;\r\n        }\r\n        log(`json:[${JSON.stringify(this._levelPrefab.json)}]`);\r\n        var data = this.genLevelData();\r\n        //@ts-ignore\r\n        Editor.Message.send('asset-db', 'save-asset', this._levelPrefab.uuid, JSON.stringify(data, null, 2));\r\n        log(\"LevelEditorUI save success\")\r\n    }\r\n    @property({\r\n        group:{name:\"new\"}, type:CCString, displayName:\"Name\"\r\n    })\r\n    public newName:string = \"\";\r\n    @property({\r\n        group:{name:\"new\"}, type:CCBoolean,\r\n    })\r\n    public get new(){\r\n        return false;\r\n    }\r\n    public set new(value) {\r\n        if (value) {\r\n            log(`Level new ${this.newName}`);\r\n        }\r\n\r\n        const filePath = `db://assets/resources/Game/level/${this.newName}.json`;\r\n        var levelData = new LevelData();\r\n        const jsonString = JSON.stringify(levelData, null, 2);\r\n    \r\n        //@ts-ignore\r\n        var p = Editor.Message.request('asset-db', 'create-asset', filePath, jsonString);\r\n        p.then((res) => {\r\n            assetManager.loadAny({uuid:res.uuid}, (err, asset) => {\r\n                if (err) {\r\n                    log(err);\r\n                } else {\r\n                    this.levelPrefab = asset;\r\n                }\r\n            });\r\n        });\r\n    }\r\n    \r\n    private baseCom:LevelEditorBaseUI = null;\r\n\r\n    _levelElapsedTime: number = 0;\r\n    public get levelElapsedTime(): number {\r\n        return this._levelElapsedTime;\r\n    }\r\n\r\n    start():void {\r\n        log(\"LevelUI start\")\r\n        this.baseCom = this.node.getComponent<LevelEditorBaseUI>(LevelEditorBaseUI);\r\n        this.levelPrefab = null;\r\n    }\r\n    private checkLevelPrebab() : void {\r\n        if (this._levelPrefab == null) {\r\n            return;\r\n        }\r\n    }\r\n    update(deltaTime: number) : void {\r\n        this.tick();\r\n    }\r\n    private tick():void {\r\n        if (this._levelPrefab == null) {\r\n            return;\r\n        }\r\n        this.baseCom.tick(this.progress);\r\n    }\r\n\r\n    private genLevelData() : LevelData {\r\n        var data = new LevelData();\r\n        this.node.getComponent<LevelEditorBaseUI>(LevelEditorBaseUI).fillLevelData(data);\r\n        return data;\r\n    }\r\n\r\n}\r\n"]}