{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts"], "names": ["_decorator", "Node", "UITransform", "v2", "Tools", "EnemyComponent", "AttackPoint", "ccclass", "property", "EnemyShootComponent", "_target", "_atkNode", "_atkPointsPool", "_shootAble", "_nextAble", "bulletTurn", "_bShooting", "_shootInterval", "_shootTime", "_shootCount", "_attackNum", "_attackArrIndex", "_attackArrOver", "_attackPointArr", "_attackPoints", "_firstShootDelay", "_isFirstShoot", "_atkOverCall", "_atkStartCall", "init", "target", "atkNode", "shootData", "reset", "setShootData", "_initAtkPoints", "attackInterval", "attackNum", "attackPointArr", "setFirstShoot<PERSON>elay", "delay", "getFirstShootDelay", "setIsShooting", "isShooting", "setNextAble", "nextAble", "startShoot", "stopShoot", "splice", "setAtkStartCall", "callback", "setAtkOverCall", "updateGameLogic", "deltaTime", "active", "sceneLayer", "isPlaneOutScreen", "node", "position", "x", "y", "_updateShoot", "_updateNextShoot", "length", "i", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "angle", "push", "shootPrepare", "setNextShootAtOnce", "allOver", "point", "isAttackOver", "_setNextShootArr", "_isShootLoop", "_getShootLoop", "setNextShoot", "attackPoints", "atkPoint", "initForEnemy", "log"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AAC1CC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,c;;AACAC,MAAAA,W;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAGTS,mB,WADpBF,OAAO,CAAC,qBAAD,C,gBAAR,MACqBE,mBADrB;AAAA;AAAA,4CACgE;AAAA;AAAA;AAAA,eAC5DC,OAD4D,GAC7C,IAD6C;AAAA,eAE5DC,QAF4D,GAE3C,IAF2C;AAAA,eAG5DC,cAH4D,GAG5B,EAH4B;AAAA,eAI5DC,UAJ4D,GAItC,KAJsC;AAAA,eAK5DC,SAL4D,GAKvC,KALuC;AAAA,eAM5DC,UAN4D,GAMtC,KANsC;AAAA,eAO5DC,UAP4D,GAOtC,KAPsC;AAAA,eAQ5DC,cAR4D,GAQnC,CARmC;AAAA,eAS5DC,UAT4D,GASvC,CATuC;AAAA,eAU5DC,WAV4D,GAUtC,CAVsC;AAAA,eAW5DC,UAX4D,GAWvC,CAXuC;AAAA,eAY5DC,eAZ4D,GAYlC,CAZkC;AAAA,eAa5DC,cAb4D,GAalC,IAbkC;AAAA,eAc5DC,eAd4D,GAcnC,EAdmC;AAAA,eAe5DC,aAf4D,GAe7B,EAf6B;AAAA,eAgB5DC,gBAhB4D,GAgBjC,CAAC,CAhBgC;AAAA,eAiB5DC,aAjB4D,GAiBnC,IAjBmC;AAAA,eAkB5DC,YAlB4D,GAkBnC,IAlBmC;AAAA,eAmB5DC,aAnB4D,GAmBlC,IAnBkC;AAAA;;AAqB5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,MAAD,EAAcC,OAAd,EAA6BC,SAA7B,EAA6CjB,UAAmB,GAAG,KAAnE,EAA0E;AAC1E,eAAKkB,KAAL;AACA,eAAKlB,UAAL,GAAkBA,UAAlB;AACA,eAAKL,OAAL,GAAeoB,MAAf;AACA,eAAKnB,QAAL,GAAgBoB,OAAhB;AACA,eAAKG,YAAL,CAAkBF,SAAlB;;AACA,eAAKG,cAAL;AACH;AAED;AACJ;AACA;;;AACIF,QAAAA,KAAK,GAAG;AACJ,eAAKnB,SAAL,GAAiB,KAAjB;AACA,eAAKE,UAAL,GAAkB,KAAlB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,cAAL,GAAsB,IAAtB;AACA,eAAKC,eAAL,GAAuB,EAAvB;AACA,eAAKG,aAAL,GAAqB,IAArB;AACA,eAAKD,gBAAL,GAAwB,CAAC,CAAzB;AACA,eAAKD,aAAL,GAAqB,EAArB;AACA,eAAKI,aAAL,GAAqB,IAArB;AACA,eAAKD,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,YAAY,CAACF,SAAD,EAAiB;AACzB,cAAIA,SAAJ,EAAe;AACX,iBAAKnB,UAAL,GAAkB,IAAlB;AACA,iBAAKQ,eAAL,GAAuB,CAAvB;AACA,iBAAKJ,cAAL,GAAsBe,SAAS,CAACI,cAAhC;AACA,iBAAKhB,UAAL,GAAkBY,SAAS,CAACK,SAA5B;AACA,iBAAKd,eAAL,GAAuBS,SAAS,CAACM,cAAjC;AACH,WAND,MAMO;AACH,iBAAKzB,UAAL,GAAkB,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI0B,QAAAA,kBAAkB,CAACC,KAAD,EAAgB;AAC9B,eAAKf,gBAAL,GAAwBe,KAAxB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,kBAAkB,GAAG;AACjB,iBAAO,KAAKhB,gBAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,aAAa,CAACC,UAAD,EAAsB;AAC/B,eAAK3B,UAAL,GAAkB2B,UAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,CAACC,QAAD,EAAoB;AAC3B,eAAK/B,SAAL,GAAiB+B,QAAjB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAK9B,UAAL,GAAkB,IAAlB;AACH;AAED;AACJ;AACA;;;AACI+B,QAAAA,SAAS,GAAG;AACR,eAAKzB,cAAL,GAAsB,IAAtB;;AACA,eAAKE,aAAL,CAAmBwB,MAAnB,CAA0B,CAA1B;;AACA,eAAK9B,UAAL,GAAkB,CAAlB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACI4B,QAAAA,eAAe,CAACC,QAAD,EAAqB;AAChC,eAAKtB,aAAL,GAAqBsB,QAArB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACD,QAAD,EAAqB;AAC/B,eAAKvB,YAAL,GAAoBuB,QAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,KAAKC,MAAL,IAAe,KAAKzC,UAAxB,EAAoC;AAChC,gBAAI,KAAKH,OAAL,CAAa6C,UAAb,GAA0B,CAA1B,IAA+B;AAAA;AAAA,gCAAMC,gBAAN,CAAuBrD,EAAE,CAAC,KAAKsD,IAAL,CAAUC,QAAV,CAAmBC,CAApB,EAAuB,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAA1C,CAAzB,CAAnC,EAA2G;AACvG;AACH;;AACD,iBAAKC,YAAL,CAAkBR,SAAlB;;AACA,gBAAI,KAAKvC,SAAT,EAAoB;AAChB,mBAAKgD,gBAAL,CAAsBT,SAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIlB,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKvB,cAAL,CAAoBmD,MAApB,KAA+B,CAAnC,EAAsC;AAClC,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,oBAAMP,IAAI,GAAG,IAAIxD,IAAJ,EAAb;AACAwD,cAAAA,IAAI,CAACQ,YAAL,CAAkB/D,WAAlB;;AACA,mBAAKS,QAAL,CAAcuD,QAAd,CAAuBT,IAAvB;;AACAA,cAAAA,IAAI,CAACU,KAAL,GAAa,CAAC,GAAd;;AACA,mBAAKvD,cAAL,CAAoBwD,IAApB,CAAyBX,IAAI,CAACQ,YAAL;AAAA;AAAA,6CAAzB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,YAAY,GAAG;AACX,eAAKnD,UAAL,GAAkB,CAAlB;;AACA,cAAI,KAAKU,aAAT,EAAwB;AACpB,iBAAKA,aAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACI0C,QAAAA,kBAAkB,GAAG;AACjB,cAAI,CAAC,KAAK5C,aAAN,IAAuB,KAAKD,gBAAL,GAAwB,CAAnD,EAAsD;AAClD,iBAAKP,UAAL,GAAkB,KAAKD,cAAvB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACsB,cAAZ4C,YAAY,CAACR,SAAD,EAAoB;AAClC,cAAI,KAAKrC,UAAT,EAAqB;AACjB,gBAAIuD,OAAO,GAAG,IAAd;;AACA,iBAAK,MAAMC,KAAX,IAAoB,KAAKhD,aAAzB,EAAwC;AACpC,oBAAMgD,KAAK,CAACpB,eAAN,CAAsBC,SAAtB,CAAN;;AACA,kBAAI,CAACmB,KAAK,CAACC,YAAN,EAAL,EAA2B;AACvBF,gBAAAA,OAAO,GAAG,KAAV;AACH;AACJ;;AACD,gBAAIA,OAAJ,EAAa;AACT,mBAAKrD,UAAL,GAAkB,CAAlB;AACA,mBAAKF,UAAL,GAAkB,KAAlB;AACA,mBAAKM,cAAL,GAAsB,IAAtB;AACA,mBAAKD,eAAL;;AACA,kBAAI,KAAKA,eAAL,GAAuB,KAAKE,eAAL,CAAqBwC,MAAhD,EAAwD;AACpD,qBAAKW,gBAAL;;AACA,qBAAKL,YAAL;AACH,eAHD,MAGO,IAAI,KAAK1C,YAAT,EAAuB;AAC1B,qBAAKA,YAAL;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACImC,QAAAA,gBAAgB,CAACT,SAAD,EAAoB;AAChC,cAAI,CAAC,KAAKrC,UAAN,IAAoB,KAAKM,cAA7B,EAA6C;AACzC,iBAAKJ,UAAL,IAAmBmC,SAAnB;;AACA,gBAAI,KAAKsB,YAAL,MAAuB,KAAKxD,WAAL,GAAmB,KAAKyD,aAAL,EAA9C,EAAoE;AAChE,kBAAI,KAAKlD,aAAL,IAAsB,KAAKD,gBAAL,IAAyB,CAAnD,EAAsD;AAClD,oBAAI,KAAKP,UAAL,GAAkB,KAAKO,gBAA3B,EAA6C;AACzC,uBAAKoD,YAAL;AACH;AACJ,eAJD,MAIO,IAAI,KAAK3D,UAAL,GAAkB,KAAKD,cAA3B,EAA2C;AAC9C,qBAAK4D,YAAL;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACIH,QAAAA,gBAAgB,GAAG;AACf,eAAKpD,cAAL,GAAsB,KAAtB;;AACA,eAAKE,aAAL,CAAmBwB,MAAnB,CAA0B,CAA1B;;AAEA,gBAAM8B,YAAY,GAAG,KAAKvD,eAAL,CAAqB,KAAKF,eAA1B,CAArB;;AACA,cAAIyD,YAAY,IAAIA,YAAY,CAACf,MAAb,GAAsB,CAA1C,EAA6C;AACzC,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGc,YAAY,CAACf,MAAjC,EAAyCC,CAAC,EAA1C,EAA8C;AAC1C,kBAAIe,QAAQ,GAAG,KAAKnE,cAAL,CAAoBoD,CAApB,CAAf;;AACA,kBAAI,CAACe,QAAL,EAAe;AACX,sBAAMtB,IAAI,GAAG,IAAIxD,IAAJ,EAAb;AACAwD,gBAAAA,IAAI,CAACQ,YAAL,CAAkB/D,WAAlB;AACAuD,gBAAAA,IAAI,CAACU,KAAL,GAAa,CAAC,GAAd;;AACA,qBAAKxD,QAAL,CAAcuD,QAAd,CAAuBT,IAAvB;;AACAsB,gBAAAA,QAAQ,GAAGtB,IAAI,CAACQ,YAAL;AAAA;AAAA,+CAAX;;AACA,qBAAKrD,cAAL,CAAoBwD,IAApB,CAAyBW,QAAzB;AACH;;AACDA,cAAAA,QAAQ,CAACC,YAAT,CAAsBF,YAAY,CAACd,CAAD,CAAlC,EAAuC,KAAKtD,OAA5C;AACAqE,cAAAA,QAAQ,CAAChE,UAAT,GAAsB,KAAKA,UAA3B;;AACA,mBAAKS,aAAL,CAAmB4C,IAAnB,CAAwBW,QAAxB;AACH;;AACD,iBAAKrD,aAAL,GAAqB,KAArB;AACH,WAhBD,MAgBO;AACH;AAAA;AAAA,gCAAMuD,GAAN,CAAU,aAAV;AACH;AACJ;AAED;AACJ;AACA;;;AACIJ,QAAAA,YAAY,GAAG;AACX,eAAK1D,WAAL;AACA,eAAKE,eAAL,GAAuB,CAAvB;;AACA,eAAKqD,gBAAL;;AACA,eAAKL,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAKvD,UAAL,KAAoB,CAAC,CAA5B;AACH;AAED;AACJ;AACA;AACA;;;AACIwD,QAAAA,aAAa,GAAG;AACZ,iBAAO,KAAKxD,UAAZ;AACH;;AAhS2D,O", "sourcesContent": ["import { _decorator, Component, Node, UITransform, v2 } from 'cc';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport EnemyComponent from './EnemyComponent';\r\nimport AttackPoint from '../../base/AttackPoint';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyShootComponent')\r\nexport default class EnemyShootComponent extends EnemyComponent {\r\n    _target: any = null;\r\n    _atkNode: Node = null;\r\n    _atkPointsPool: AttackPoint[] = [];\r\n    _shootAble: boolean = false;\r\n    _nextAble: boolean = false;\r\n    bulletTurn: boolean = false;\r\n    _bShooting: boolean = false;\r\n    _shootInterval: number = 0;\r\n    _shootTime: number = 0;\r\n    _shootCount: number = 0;\r\n    _attackNum: number = 0;\r\n    _attackArrIndex: number = 0;\r\n    _attackArrOver: boolean = true;\r\n    _attackPointArr: any[] = [];\r\n    _attackPoints: AttackPoint[] = [];\r\n    _firstShootDelay: number = -1;\r\n    _isFirstShoot: boolean = true;\r\n    _atkOverCall: Function = null;\r\n    _atkStartCall: Function = null;\r\n\r\n    /**\r\n     * 初始化射击组件\r\n     * @param target 目标对象\r\n     * @param atkNode 攻击节点\r\n     * @param shootData 射击数据\r\n     * @param bulletTurn 是否允许子弹转向\r\n     */\r\n    init(target: any, atkNode: Node, shootData: any, bulletTurn: boolean = false) {\r\n        this.reset();\r\n        this.bulletTurn = bulletTurn;\r\n        this._target = target;\r\n        this._atkNode = atkNode;\r\n        this.setShootData(shootData);\r\n        this._initAtkPoints();\r\n    }\r\n\r\n    /**\r\n     * 重置射击组件\r\n     */\r\n    reset() {\r\n        this._nextAble = false;\r\n        this._bShooting = false;\r\n        this._shootInterval = 0;\r\n        this._shootTime = 0;\r\n        this._shootCount = 0;\r\n        this._attackNum = 0;\r\n        this._attackArrIndex = 0;\r\n        this._attackArrOver = true;\r\n        this._attackPointArr = [];\r\n        this._isFirstShoot = true;\r\n        this._firstShootDelay = -1;\r\n        this._attackPoints = [];\r\n        this._atkStartCall = null;\r\n        this._atkOverCall = null;\r\n    }\r\n\r\n    /**\r\n     * 设置射击数据\r\n     * @param shootData 射击数据\r\n     */\r\n    setShootData(shootData: any) {\r\n        if (shootData) {\r\n            this._shootAble = true;\r\n            this._attackArrIndex = 0;\r\n            this._shootInterval = shootData.attackInterval;\r\n            this._attackNum = shootData.attackNum;\r\n            this._attackPointArr = shootData.attackPointArr;\r\n        } else {\r\n            this._shootAble = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置首次射击延迟\r\n     * @param delay 延迟时间\r\n     */\r\n    setFirstShootDelay(delay: number) {\r\n        this._firstShootDelay = delay;\r\n    }\r\n\r\n    /**\r\n     * 获取首次射击延迟\r\n     * @returns {number} 首次射击延迟\r\n     */\r\n    getFirstShootDelay() {\r\n        return this._firstShootDelay;\r\n    }\r\n\r\n    /**\r\n     * 设置是否正在射击\r\n     * @param isShooting 是否射击\r\n     */\r\n    setIsShooting(isShooting: boolean) {\r\n        this._bShooting = isShooting;\r\n    }\r\n\r\n    /**\r\n     * 设置是否可以进行下一次射击\r\n     * @param nextAble 是否可以进行下一次射击\r\n     */\r\n    setNextAble(nextAble: boolean) {\r\n        this._nextAble = nextAble;\r\n    }\r\n\r\n    /**\r\n     * 开始射击\r\n     */\r\n    startShoot() {\r\n        this._bShooting = true;\r\n    }\r\n\r\n    /**\r\n     * 停止射击\r\n     */\r\n    stopShoot() {\r\n        this._attackArrOver = true;\r\n        this._attackPoints.splice(0);\r\n        this._shootTime = 0;\r\n        this._attackArrIndex = 0;\r\n    }\r\n\r\n    /**\r\n     * 设置攻击开始的回调\r\n     * @param callback 回调函数\r\n     */\r\n    setAtkStartCall(callback: Function) {\r\n        this._atkStartCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置攻击结束的回调\r\n     * @param callback 回调函数\r\n     */\r\n    setAtkOverCall(callback: Function) {\r\n        this._atkOverCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (this.active && this._shootAble) {\r\n            if (this._target.sceneLayer < 0 && Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n                return;\r\n            }\r\n            this._updateShoot(deltaTime);\r\n            if (this._nextAble) {\r\n                this._updateNextShoot(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化攻击点\r\n     */\r\n    _initAtkPoints() {\r\n        if (this._atkPointsPool.length === 0) {\r\n            for (let i = 0; i < 2; i++) {\r\n                const node = new Node();\r\n                node.addComponent(UITransform);\r\n                this._atkNode.addChild(node);\r\n                node.angle = -180;\r\n                this._atkPointsPool.push(node.addComponent(AttackPoint));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备射击\r\n     */\r\n    shootPrepare() {\r\n        this._shootTime = 0;\r\n        if (this._atkStartCall) {\r\n            this._atkStartCall();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 立即进行下一次射击\r\n     */\r\n    setNextShootAtOnce() {\r\n        if (!this._isFirstShoot || this._firstShootDelay < 0) {\r\n            this._shootTime = this._shootInterval;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    async _updateShoot(deltaTime: number) {\r\n        if (this._bShooting) {\r\n            let allOver = true;\r\n            for (const point of this._attackPoints) {\r\n                await point.updateGameLogic(deltaTime);\r\n                if (!point.isAttackOver()) {\r\n                    allOver = false;\r\n                }\r\n            }\r\n            if (allOver) {\r\n                this._shootTime = 0;\r\n                this._bShooting = false;\r\n                this._attackArrOver = true;\r\n                this._attackArrIndex++;\r\n                if (this._attackArrIndex < this._attackPointArr.length) {\r\n                    this._setNextShootArr();\r\n                    this.shootPrepare();\r\n                } else if (this._atkOverCall) {\r\n                    this._atkOverCall();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新下一次射击逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    _updateNextShoot(deltaTime: number) {\r\n        if (!this._bShooting && this._attackArrOver) {\r\n            this._shootTime += deltaTime;\r\n            if (this._isShootLoop() || this._shootCount < this._getShootLoop()) {\r\n                if (this._isFirstShoot && this._firstShootDelay >= 0) {\r\n                    if (this._shootTime > this._firstShootDelay) {\r\n                        this.setNextShoot();\r\n                    }\r\n                } else if (this._shootTime > this._shootInterval) {\r\n                    this.setNextShoot();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置下一次射击的攻击点数组\r\n     */\r\n    _setNextShootArr() {\r\n        this._attackArrOver = false;\r\n        this._attackPoints.splice(0);\r\n\r\n        const attackPoints = this._attackPointArr[this._attackArrIndex];\r\n        if (attackPoints && attackPoints.length > 0) {\r\n            for (let i = 0; i < attackPoints.length; i++) {\r\n                let atkPoint = this._atkPointsPool[i];\r\n                if (!atkPoint) {\r\n                    const node = new Node();\r\n                    node.addComponent(UITransform);\r\n                    node.angle = -180;\r\n                    this._atkNode.addChild(node);\r\n                    atkPoint = node.addComponent(AttackPoint);\r\n                    this._atkPointsPool.push(atkPoint);\r\n                }\r\n                atkPoint.initForEnemy(attackPoints[i], this._target);\r\n                atkPoint.bulletTurn = this.bulletTurn;\r\n                this._attackPoints.push(atkPoint);\r\n            }\r\n            this._isFirstShoot = false;\r\n        } else {\r\n            Tools.log('shoot error');\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 立即进行下一次射击\r\n     */\r\n    setNextShoot() {\r\n        this._shootCount++;\r\n        this._attackArrIndex = 0;\r\n        this._setNextShootArr();\r\n        this.shootPrepare();\r\n    }\r\n\r\n    /**\r\n     * 是否为循环射击\r\n     * @returns {boolean} 是否循环\r\n     */\r\n    _isShootLoop() {\r\n        return this._attackNum === -1;\r\n    }\r\n\r\n    /**\r\n     * 获取射击循环次数\r\n     * @returns {number} 循环次数\r\n     */\r\n    _getShootLoop() {\r\n        return this._attackNum;\r\n    }\r\n}"]}