import { JsonAsset, resources } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameConst } from "../const/GameConst";
import { StageData} from "../data/StageData";
import { GameIns } from "../GameIns";
import { MyApp } from "../../MyApp";

export class StageManager extends SingletonBase<StageManager> {

    _allStageDataArr:StageData[] = [];
    _curStageDataArr:StageData[] = [];

    constructor() {
        super();
        this.initConfig();
    }

    initConfig(){
        let stages = MyApp.lubanTables.TbStage.getDataList();
        for (let data of stages) {
            const stage = new StageData();
            stage.loadJson(data);
            this._allStageDataArr.push(stage);
        }
    }

    initBattle(mainID, subID) {
        this._curStageDataArr.splice(0);
        this._allStageDataArr.forEach(stage => {
            if (stage.mainStage === mainID && stage.subStage === subID) {
                this._curStageDataArr.push(stage);
            }
        });
    }

    getBossTips() {
        return "";
    }

    gameStart() {
        GameIns.waveManager.setEnemyActions(this._curStageDataArr);
    }
}