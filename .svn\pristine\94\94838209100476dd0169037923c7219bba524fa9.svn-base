import { error, JsonAsset, resources, Vec2 } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameConst } from "../const/GameConst";
import { EnemyWave } from "../data/EnemyWave";
import { GameIns } from "../GameIns";
import { Tools } from "../utils/Tools";
import BattleLayer from "../ui/layer/BattleLayer";
import GameMapRun from "../ui/map/GameMapRun";
import GameEnum from "../const/GameEnum";
import BossBase from "../ui/plane/boss/BossBase";
import { MyApp } from "../../MyApp";
import { StageData } from "../data/StageData";
import EnemyPlane from "../ui/plane/enemy/EnemyPlane";
import { Wave } from "../wave/Wave";


export default class WaveManager extends SingletonBase<WaveManager> {
    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();
    private _enemyOver: boolean = false;
    private _enemyActions: StageData[] | null = null;
    private _bEnemyCreateAble: boolean = false;
    private _bEnemyNorCreateAble: boolean = false;
    private _waveActionArr: any[] = [];
    private _waveArr: EnemyWave[] = [];
    private _waveNumArr: number[] = [];
    private _waveTimeArr: number[] = [];
    private _enemyCreateTime: number = 0;
    private _enemyActionIndex: number = 0;

    private _waveIndex: number = 0;
    private _waveCreateTime: number = 0;
    private _waveIndexOver: number[] = [];
    private _curEnemyAction: any | null = null;

    private _bossCreateDelay: number = 0;
    private _bossCreateTime: number = 0;
    private _bossToAddArr: any[] = [];
    private _bShowBossWarning: boolean = false;


    get enemyCreateAble(): boolean {
        return this._bEnemyCreateAble;
    }

    set enemyCreateAble(value: boolean) {
        this._bEnemyCreateAble = value;
    }

    constructor() {
        super();
        this.initConfig();
    }

    initConfig() {
        let waveDatas = MyApp.lubanTables.TbWave.getDataList();
        for (let waveData of waveDatas) {
            const wave = new EnemyWave();
            wave.loadJson(waveData);
            const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];
            group.push(wave);
            this._waveNorDatasMap.set(wave.enemyGroupID, group);
        }
    }

    mainReset(): void {
        this.reset();
    }

    reset(): void {
        this._enemyOver = false;
        this._enemyActions = [];
        this._enemyActionIndex = 0;
        this._enemyCreateTime = 0;
        this._bEnemyCreateAble = false;
        this._bEnemyNorCreateAble = false;
        this._waveIndex = 0;
        this._waveCreateTime = 0;
        this._waveIndexOver.splice(0);
        this._curEnemyAction = null;
        this._waveArr = [];
        this._waveActionArr = [];
        this._waveNumArr = [];
        this._waveTimeArr = [];
        this._bShowBossWarning = false;
        this._bossCreateTime = 0;
    }

    setEnemyActions(actions: StageData[]): void {
        this._enemyActions = actions;
    }

    gameStart(): void {
        this._bEnemyCreateAble = true;
        this._bEnemyNorCreateAble = true;
        this._waveArr = [];
        this._waveActionArr = [];
        this._waveNumArr = [];
        this._waveTimeArr = [];
    }

    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {
        return this._waveNorDatasMap.get(groupID);
    }


    /**
 * 更新游戏逻辑
 * @param deltaTime 每帧的时间增量
 */
    updateGameLogic(deltaTime: number): void {
        this._updateCurAction(deltaTime);
        this._updateEnemy(deltaTime);
        this._updateBoss(deltaTime);
    }

    /**
     * 更新当前敌人行为
     * @param deltaTime 每帧的时间增量
     */
    private _updateCurAction(deltaTime: number): void {
        if (!this._enemyOver) {
            if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {
                this._enemyOver = true;
                error("enemy over");
            } else if (this.enemyCreateAble && !this._curEnemyAction) {
                const action = this._enemyActions![this._enemyActionIndex];
                switch (action.type) {
                    case 0:
                        this._enemyCreateTime += deltaTime;
                        if (
                            this._enemyCreateTime >= action.enemyNorInterval ||
                            (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)
                        ) {
                            this._curEnemyAction = action;
                        }
                        break;

                    case 1:
                        if (
                            this._waveArr.length === 0 &&
                            GameIns.enemyManager.getNormalPlaneCount() === 0
                        ) {
                            this._enemyCreateTime += deltaTime;
                            if (this._enemyCreateTime >= action.enemyNorInterval) {
                                this._curEnemyAction = action;
                            }
                        }
                        break;

                    default:
                        if (action.type >= 100) {
                            console.warn("Boss stage", action.type, action.enemyNorIDs[0]);
                            this._bossCreateDelay = action.enemyNorInterval;
                            GameIns.bossManager.loadBossRes(action.type, action.enemyNorIDs[0]);
                            this._bossToAddArr.push(action);
                            this._enemyActionIndex++;
                        }
                }
            }
        }
    }

    /**
     * 更新敌人逻辑
     * @param deltaTime 每帧的时间增量
     */
    private _updateEnemy(deltaTime: number): void {
        // if (this.isShipStage && GameIns.enemyManager.isEnemyShipDead()) {
        //     this._waveArr.splice(0);
        //     this._enemyOver = true;
        // }

        this._updateEnemyCreate(deltaTime);

        if (this._curEnemyAction) {
            if (!this._updateNorEnemys(deltaTime)) {
                this._curEnemyAction = null;
                this._enemyActionIndex++;
                this._enemyCreateTime = 0;
            }
        }
    }

    addWaveByLevel(wave: Wave, pos: Vec2) {
        const enemyWave = EnemyWave.fromLevelWave(wave, pos);
        this._waveArr.push(enemyWave)
        this._waveNumArr.push(0)
        this._waveTimeArr.push(0)
        this._waveActionArr.push(this._curEnemyAction)
    }

    /**
     * 更新敌人生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    private async _updateEnemyCreate(deltaTime: number): Promise<void> {
        for (let i = 0; i < this._waveArr.length; i++) {
            const wave = this._waveArr[i];
            this._waveTimeArr[i] += deltaTime;
            const currentEnemyCount = this._waveNumArr[i];
            let posX = GameConst.EnemyPos.x;
            let posY = GameConst.EnemyPos.y;

            if (wave.bSetStartPos) {
                posX += wave.startPosX;
                posY += wave.startPosY;
            }

            const expPerEnemy = Math.floor(wave.exp / wave.enemyNum);

            for (let j = currentEnemyCount; j < wave.enemyNum; j++) {
                if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {
                    this._waveNumArr[i]++;
                    let enemy:EnemyPlane;
                    const enemyPosX = posX + wave.posDX * (j + 1);
                    const enemyPosY = posY + wave.posDY * (j + 1);

                    switch (wave.type) {
                        case 0:
                            enemy = await GameIns.enemyManager.addPlane(wave.enemyID);
                            if (enemy) {
                                if (j < wave.firstShootDelay.length) {
                                    enemy.setFirstShootDelay(wave.firstShootDelay[j]);
                                }
                                enemy.setStandByTime(0);
                                enemy.setExp(expPerEnemy);
                                enemy.initPropertyRate(this._waveActionArr[i].enemyNorRate);
                                enemy.initTrack(
                                    wave.trackGroups,
                                    wave.liveParam,
                                    enemyPosX,
                                    enemyPosY,
                                    wave.rotateSpeed
                                );
                                // if (
                                //     wave.normalLoot &&
                                //     Tools.arrContain(wave.normalLoot.enemys, j + 1)
                                // ) {
                                //     enemy.addLoot(
                                //         GameIns.lootManager.getLootData(wave.normalLoot.lootId)
                                //     );
                                // }
                            }
                            break;
                    }
                }
            }

            if (wave.enemyNum <= this._waveNumArr[i]) {
                this._waveArr.splice(i, 1);
                this._waveNumArr.splice(i, 1);
                this._waveTimeArr.splice(i, 1);
                this._waveActionArr.splice(i, 1);
                i--;
            }
        }
    }

    /**
     * 更新普通敌人生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    private _updateNorEnemys(deltaTime: number): boolean {
        if (this._bEnemyNorCreateAble) {
            if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {
                this._waveIndex = 0;
                return false;
            }

            const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];
            this._waveCreateTime += deltaTime;

            const waveDatas = this.getNorWaveDatas(waveID);
            for (let i = 0; i < waveDatas!.length; i++) {
                const wave = waveDatas![i];
                if (
                    !Tools.arrContain(this._waveIndexOver, i) &&
                    this._waveCreateTime >= wave.groupInterval
                ) {
                    this._waveArr.push(wave);
                    this._waveNumArr.push(0);
                    this._waveTimeArr.push(0);
                    this._waveActionArr.push(this._curEnemyAction);
                    this._waveIndexOver.push(i);
                }
            }

            if (this._waveIndexOver.length >= waveDatas!.length) {
                this._waveIndexOver.splice(0);
                this._waveCreateTime = 0;
                this._waveIndex++;
            }
        }

        return true;
    }

    /**
     * 更新 Boss 生成逻辑
     * @param deltaTime 每帧的时间增量
     */
    private _updateBoss(deltaTime: number): void {
        if (
            this._bossToAddArr.length > 0 &&
            GameIns.enemyManager.isEnemyOver() &&
            (this._bShowBossWarning ||
                (this._bShowBossWarning = true, GameIns.battleManager.bossWillEnter()))
        ) {
            this._bossCreateTime += deltaTime;
            if (
                this._bossCreateTime > this._bossCreateDelay &&
                GameIns.bossManager.bossResFinish
            ) {
                const bossData = this._bossToAddArr[0];
                const boss = GameIns.bossManager.addBoss(
                    bossData.type,
                    bossData.enemyNorIDs[0]
                );
                if (boss instanceof BossBase) {
                    // if (GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {
                    //     boss.setPropertyRate(BossBattleManager.getPropertyRate());
                    // } else {
                    boss.setPropertyRate(bossData.enemyNorRate);
                    // }
                    boss.setTip(GameIns.stageManager.getBossTips());
                }
                this._bossToAddArr.splice(0, 1);
            }
        }
    }
    /**
     * 检查敌人是否全部结束
     * @returns 是否所有敌人都已结束
     */
    isEnemyOver(): boolean {
        return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;
    }

    //     /**
    //      * 设置当前波次的 ID（未实现具体逻辑）
    //      * @param waveID 波次 ID
    //      */
    //     setWaveID(waveID: number): void {
    //         // 该方法目前未实现具体逻辑
    //     }
}