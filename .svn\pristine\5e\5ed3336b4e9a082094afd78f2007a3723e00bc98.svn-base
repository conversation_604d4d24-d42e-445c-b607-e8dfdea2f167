import { _decorator, Component, Node, UITransform, tween, Vec3, easing, Label } from 'cc';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
const { ccclass, property } = _decorator;

@ccclass('WheelSpinnerUI')
export class WheelSpinnerUI extends Component {
    @property({ type: Node })
    private wheel: Node = null; // 转盘节点

    @property
    private segments: number = 7; // 转盘分块数量

    @property(ButtonPlus)
    button: ButtonPlus = null;

    @property(Label)
    angleLabel: Label = null;

    private isSpinning: boolean = false; // 是否正在转动
    protected onLoad(): void {
        this.button.addClick(this.onButtonClick, this);
    }
    // 开始转动
    public spin(targetIndex: number): void {
        if (this.isSpinning) return;
        this.isSpinning = true;

        // 计算目标角度（360度除以分块数量，乘以目标索引）
        const anglePerSegment = 360 / this.segments;
        const targetAngle = 360 * 5 + anglePerSegment * targetIndex; // 多转5圈

        this.angleLabel.string = `${anglePerSegment.toFixed(2)}°`;

        // 使用 Tween 实现缓动动画
        tween(this.wheel)
            .to(0.5, { angle: 360 * 2 }, { easing: easing.quadOut }) // 快速转动
            .to(2, { angle: targetAngle }, { easing: easing.quadIn }) // 缓慢停止
            .call(() => {
                this.isSpinning = false;
                console.log(`停止在第 ${targetIndex + 1} 格`);
            })
            .start();
    }
    // 开始转动（顺时针）
    public spin2(targetIndex: number): void {
        if (this.isSpinning) return;
        this.isSpinning = true;
        // 重置角度为 0
        this.wheel.angle = 0;
        // 计算目标角度（顺时针为负方向）
        const anglePerSegment = 360 / this.segments;
        const targetAngle = -360 * 15 - anglePerSegment * targetIndex; // 多转几圈（顺时针）

        this.angleLabel.string = `${targetAngle.toFixed(2)}°`;

        // 使用 Tween 实现缓动动画
        tween(this.wheel)
            .to(3.5, { angle: targetAngle }, { easing: easing.cubicOut })
            .call(() => {
                this.isSpinning = false;
                console.log(`停止在第 ${targetIndex + 1} 格`);
            })
            .start();
    }
    // 测试用：点击按钮触发转动
    private onButtonClick(): void {
        const randomIndex = Math.floor(Math.random() * this.segments); // 随机选择一个格子
        this.spin2(randomIndex);
    }
}


