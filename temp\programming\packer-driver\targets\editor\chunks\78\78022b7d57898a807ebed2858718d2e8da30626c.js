System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, GameConst, GameIns, GameEnum, _dec, _class, _crd, ccclass, property, NodeMove;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "930adZcVoBM7oecGU3pPBGh", "NodeMove", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Sprite', 'tween', 'isValid', 'UITransform', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", NodeMove = (_dec = ccclass('NodeMove'), _dec(_class = class NodeMove extends Component {
        constructor(...args) {
          super(...args);
          this.moveSpeed = 0;
          // 垂直移动速度
          this.sideswaySpeed = 0;
          // 水平移动速度
          this.layer = 0;
        }

        // 节点所属图层

        /**
         * 设置节点的移动数据
         * @param xSpeed 水平移动速度
         * @param ySpeed 垂直移动速度
         * @param layer 节点所属图层
         */
        setData(xSpeed, ySpeed, layer) {
          this.sideswaySpeed = xSpeed;
          this.moveSpeed = ySpeed;
          this.layer = layer;
        }
        /**
         * 每帧更新节点的位置
         * @param deltaTime 时间增量
         */


        update(deltaTime) {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).GameAble) {
            return;
          } // 限制 deltaTime 的最大值


          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
          }

          const gameState = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState;

          if (gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle || gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Sortie || gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Ready || gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.WillOver || gameState === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Over) {
            // 更新节点位置
            let posX = this.node.position.x - deltaTime * this.moveSpeed;
            let posY = this.node.position.y + deltaTime * this.sideswaySpeed;
            this.node.setPosition(posX, posY); // 检查节点是否超出视图范围

            /*const layerData = GameMapRun.instance.LayerData.get(this.layer);
            if (this.node.y + this.node.getComponent(UITransform).height < layerData.ViewBot) {
                // const enemyComponent = this.getComponent(EnemyBuild);
                // if (enemyComponent) {
                //     EnemyManager.EnemyMgr.removePlane(enemyComponent);
                // }
            } else if (
                this.node.position.x + this.node.getComponent(UITransform).width < -view.getVisibleSize().width / 2 - 500 ||
                this.node.position.x > view.getVisibleSize().width / 2 + 500
            ) {
                // 节点超出屏幕宽度范围
            }*/
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=78022b7d57898a807ebed2858718d2e8da30626c.js.map