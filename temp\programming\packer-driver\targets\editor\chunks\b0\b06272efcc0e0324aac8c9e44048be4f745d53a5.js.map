{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/BuidingUI.ts"], "names": ["_decorator", "Component", "Label", "resources", "Sprite", "SpriteFrame", "ButtonPlus", "EventMgr", "MainEvent", "ccclass", "property", "BuidingUI", "index", "onLoad", "getComponent", "addClick", "onClick", "onDestroy", "emit", "BattleItemClick", "title", "string", "setNewFrame", "imageUrl", "load", "err", "spriteFrame", "sprt", "setTitle", "onRenderItem", "item"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACjDC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U,GAE9B;;2BAEaW,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACR,KAAD,C,UAGRQ,QAAQ,CAACN,MAAD,C,2BANb,MACaO,SADb,SAC+BV,SAD/B,CACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQ7BW,KAR6B,GAQhB,CARgB;AAAA;;AAU3BC,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA8BC,QAA9B,CAAuC,KAAKC,OAA5C,EAAqD,IAArD;AACH;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAEOD,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASE,IAAT,CAAc;AAAA;AAAA,sCAAUC,eAAxB,EAAyC,KAAKP,KAA9C,EAAqD,KAAKQ,KAAL,CAAWC,MAAhE;AACH;;AAEMC,QAAAA,WAAW,CAACC,QAAD,EAAmB;AACjCpB,UAAAA,SAAS,CAACqB,IAAV,CAAgB,iCAAgCD,QAAS,EAAzD,EAA4DlB,WAA5D,EAAyE,CAACoB,GAAD,EAAMC,WAAN,KAAsB;AAC3F,iBAAKC,IAAL,CAAUD,WAAV,GAAwBA,WAAxB;AACH,WAFD;AAGH;;AAEME,QAAAA,QAAQ,CAAChB,KAAD,EAAaQ,KAAb,EAA4B;AACvC,eAAKR,KAAL,GAAaA,KAAb;AACA,eAAKQ,KAAL,CAAWC,MAAX,GAAoBD,KAApB;AAEH,SAhCoC,CAkCrC;;;AACAS,QAAAA,YAAY,CAACC,IAAD,EAAyB,CAEpC;;AArCoC,O;;;;;iBAGtB,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Label, resources, Sprite, SpriteFrame } from \"cc\";\r\nimport { ButtonPlus } from \"../common/components/button/ButtonPlus\";\r\nimport { EventMgr } from \"../../event/EventManager\";\r\nimport { MainEvent } from \"./MainEvent\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n//参考 BagItem\r\n@ccclass('BuidingUI')\r\nexport class BuidingUI extends Component {\r\n\r\n    @property(Label)\r\n    title: Label = null;\r\n\r\n    @property(Sprite)\r\n    sprt: Sprite = null;\r\n\r\n    private index: any = 0;\r\n\r\n    protected onLoad(): void {\r\n        this.getComponent(ButtonPlus).addClick(this.onClick, this)\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n    private onClick() {\r\n        EventMgr.emit(MainEvent.BattleItemClick, this.index, this.title.string);\r\n    }\r\n\r\n    public setNewFrame(imageUrl: string) {\r\n        resources.load(`Game/texture/common/itemImage/${imageUrl}`, SpriteFrame, (err, spriteFrame) => {\r\n            this.sprt.spriteFrame = spriteFrame;\r\n        });\r\n    }\r\n\r\n    public setTitle(index: any, title: string) {\r\n        this.index = index;\r\n        this.title.string = title;\r\n\r\n    }\r\n\r\n    //cell的容器去手动调用\r\n    onRenderItem(item: { name: string }) {\r\n\r\n    }\r\n}"]}