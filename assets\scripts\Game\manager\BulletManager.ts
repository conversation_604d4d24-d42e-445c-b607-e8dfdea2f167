import { instantiate, isValid, log, MotionStreak, Prefab, resources, SpriteAtlas, Texture2D } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameIns } from "../GameIns";
import { GameFunc } from "../GameFunc";
import Bullet from "../ui/bullet/Bullet";
import EnemyBase from "../ui/plane/enemy/EnemyBase";
import BossBase from "../ui/plane/boss/BossBase";
import GameResourceList from "../const/GameResourceList";
import { MyApp } from "../../MyApp";


export class BulletManager extends SingletonBase<BulletManager>{

    _preloadFinish = false;
    _mainStage = 0;
    mainBulletAtlas = null;
    enemyBulletAtlas = null;
    enemyComAtlas = null;
    m_unUseBullets = new Map();
    selfBullets = new Map();
    enemyBullets = new Map();
    m_nodeCatch = [];
    _bulletCount = 120;
    _testIds = []

    preLoad(stage = 0) {
        GameIns.battleManager.addLoadCount(1);;
        const spriteAtlases = [GameResourceList.atlas_enemyBullet, GameResourceList.atlas_mainBullet];
        MyApp.resMgr.load(spriteAtlases, SpriteAtlas, (error,atlas:SpriteAtlas[]) => {
            this.enemyComAtlas = atlas[0];
            this.mainBulletAtlas = atlas[1];
            GameIns.battleManager.checkLoadFinish();;
        });

        GameIns.battleManager.addLoadCount(1);
        const prefabs = [
            GameResourceList.Bullet
        ];

        MyApp.resMgr.load(prefabs, Prefab, () => {
            GameIns.battleManager.checkLoadFinish();;
        });

        if (stage > 0) {
            GameIns.battleManager.addLoadCount(1);;
            MyApp.resMgr.load(GameResourceList.atlas_enemyBullet1, SpriteAtlas,(error,atlas) => {
                this.enemyBulletAtlas = atlas;
                GameIns.battleManager.checkLoadFinish();;
            });
        }
    }

    clear() {
        if (this.enemyBulletAtlas) {
            MyApp.resMgr.releaseAssetByForce(this.enemyBulletAtlas);
            this.enemyBulletAtlas = null;
        }
    }

    async battleInit() {
        if (!this.m_unUseBullets.get("e1")) {
            const bullets = [];
            for (let i = 0; i < 150; i++) {
                const bullet =  await this.createNewBullet(1, true);
                if (bullet.node && isValid(bullet)) {
                    bullets.push(bullet);
                }
            }
            this.m_unUseBullets.set("e1", bullets);
        }
        this.removeAll();
        if (this.m_nodeCatch.length < this._bulletCount) {
            await GameIns.prefabManager.createFrame(
                Bullet,
                this._bulletCount - this.m_nodeCatch.length,
                this.m_nodeCatch,
                10
            );
        }
    }

    getConfig(bulletID) {
        if (!this._testIds.includes(bulletID)) {
            console.log("getBullet", bulletID);
            this._testIds.push(bulletID);
        }
        return MyApp.lubanTables.TbBullet.get(bulletID);
    }

    /**
     * 获取子弹实例
     * @param {number} bulletID 子弹ID
     * @param {boolean} isEnemy 是否为敌方子弹
     * @returns {Bullet} 子弹实例
     */
    async getBullet(bulletID, isEnemy) {
        try {
            const config = this.getConfig(bulletID);
            if (!config) return null;

            const saveType = this.getSaveType(config, isEnemy);
            let unusedBullets = this.m_unUseBullets.get(saveType);
            let bullet:Bullet = null;

            if (unusedBullets && unusedBullets.length > 0) {
                bullet = unusedBullets.pop();
                if (isValid(bullet) && isValid(bullet.node)) {
                    bullet.create(bulletID);
                    bullet.enemy = isEnemy;
                } else {
                    log("bullet not valid", bulletID, saveType);
                    bullet = await this.createNewBullet(config.bustyle, isEnemy);
                    bullet.bulletID = bulletID;
                    bullet.enemy = isEnemy;
                    bullet.create(bulletID);
                }
            } else {
                bullet = await this.createNewBullet(config.bustyle, isEnemy);
                bullet.bulletID = bulletID;
                bullet.enemy = isEnemy;
                bullet.create(bulletID);
            }

            bullet.new_uuid = GameFunc.uuid;

            if (bullet.enemy) {
                if (this.enemyBullets.has(bullet.bulletID)) {
                    this.enemyBullets.get(bullet.bulletID).push(bullet);
                } else {
                    this.enemyBullets.set(bullet.bulletID, [bullet]);
                }
            } else {
                if (this.selfBullets.has(bullet.bulletID)) {
                    this.selfBullets.get(bullet.bulletID).push(bullet);
                } else {
                    this.selfBullets.set(bullet.bulletID, [bullet]);
                }
            }

            bullet.refresh();
            bullet.node.setPosition(0, 0);

            return bullet;
        } catch (error) {
            const config = this.getConfig(bulletID);
            const saveType = this.getSaveType(config, isEnemy);
            log("getBullet error", 0, bulletID, isEnemy, config ? config.bustyle : 0, saveType);
            return null;
        }
    }

    /**
     * 移除子弹
     * @param {Bullet} bullet 子弹实例
     * @param {boolean} removeFromEntity 是否从实体中移除
     */
    removeBullet(bullet, removeFromEntity = true) {
        let bulletList;
        let index;

        if (bullet.enemy) {
            bulletList = this.enemyBullets.get(bullet.bulletID);
            if (bulletList) {
                index = bulletList.indexOf(bullet);
                if (index >= 0) {
                    bulletList.splice(index, 1);
                    this.remove(bullet);
                }
            }
            if (removeFromEntity && bullet.m_mainEntity) {
                if (bullet.m_mainEntity instanceof EnemyBase || bullet.m_mainEntity instanceof BossBase) {
                    bullet.m_mainEntity.removeBullet(bullet);
                }
            }
        } else {
            bulletList = this.selfBullets.get(bullet.bulletID);
            if (bulletList) {
                index = bulletList.indexOf(bullet);
                if (index >= 0) {
                    bulletList.splice(index, 1);
                    this.remove(bullet);
                } else {
                    log("b11 11111");
                }
            }
        }
    }

    /**
     * 移除子弹并回收
     * @param {Bullet} bullet 子弹实例
     * @param {boolean} forceDestroy 是否强制销毁
     */
    remove(bullet:Bullet, forceDestroy = false) {
        bullet.removeAllComp();
        if (bullet.node) {
            if (bullet.getType() !== 41) {
                bullet.node.parent = null;
                bullet.node.setScale(bullet.node.getScale().x,1);
            }
            const config = this.getConfig(bullet.bulletID);
            const saveType = this.getSaveType(config, bullet.enemy);

            if (this.m_unUseBullets.has(saveType)) {
                if (isValid(bullet)) {
                    this.m_unUseBullets.get(saveType).push(bullet);
                }
            } else {
                this.m_unUseBullets.set(saveType, [bullet]);
            }
        } else if (isValid(bullet)) {
            bullet.destroy();
        }
    }
    /**

    /**
     * 移除所有敌方子弹
     * @param {boolean} dieRemove 是否调用子弹的死亡移除逻辑
     */
    removeEnemyBullets(dieRemove = false) {
        if (dieRemove) {
            this.enemyBullets.forEach((bulletList) => {
                let count = 0;
                while (bulletList.length > 0 && count < 9999) {
                    bulletList[0].dieRemove();
                    count++;
                }
            });
        } else {
            this.enemyBullets.forEach((bulletList) => {
                bulletList.forEach((bullet) => {
                    this.remove(bullet, true);
                });
            });
        }
    }

    /**
     * 移除所有子弹
     * @param {boolean} destroyAll 是否销毁所有子弹
     * @param {boolean} forceDestroy 是否强制销毁
     */
    removeAll(destroyAll = false, forceDestroy = false) {
        this.removeEnemyBullets();

        this.selfBullets.forEach((bulletList) => {
            bulletList.forEach((bullet) => {
                this.remove(bullet, true);
            });
        });

        this.m_unUseBullets.forEach((bulletList) => {
            bulletList.forEach((bullet) => {
                try {
                    if (!destroyAll && this.m_nodeCatch.length < this._bulletCount) {
                        if (isValid(bullet)) {
                            bullet.node.parent = null;
                            this.m_nodeCatch.push(bullet);
                        }
                    } else {
                        bullet.node.destroy();
                    }
                } catch (error) {
                    log("bullet removeAll error");
                }
            });
        });

        if (destroyAll) {
            this.m_nodeCatch.forEach((node) => {
                if (node.node) node.node.destroy();
            });
            this.m_nodeCatch = [];
        }

        this.m_unUseBullets.clear();
        this.enemyBullets.clear();
        this.selfBullets.clear();
        // this.putAllStreak();
    }
    /**
     * 创建新的子弹实例
     * @param {number} bulletType 子弹类型
     * @param {boolean} isEnemy 是否为敌方子弹
     * @returns {Bullet} 子弹实例
     */
    async createNewBullet(bulletType:number, isEnemy:boolean):Promise<Bullet> {
        let bullet:Bullet = null;

        switch (bulletType) {
            default: // 默认子弹
                if (this.m_nodeCatch.length > 0) {
                    bullet = this.m_nodeCatch.pop();
                    if (!isValid(bullet)) {
                        bullet = await GameIns.prefabManager.createComponent(Bullet) as Bullet;
                    }
                } else {
                    bullet = await GameIns.prefabManager.createComponent(Bullet) as Bullet;
                }
        }

        return bullet;
    }

    /**
     * 获取子弹的保存类型
     * @param {Object} config 子弹配置
     * @param {boolean} isEnemy 是否为敌方子弹
     * @returns {string} 保存类型
     */
    getSaveType(config, isEnemy) {
        if (isEnemy) {
            if (config.bustyle === 23) return "e23";
            if (config.bustyle === 26) return "e26";
            if (config.bustyle === 39) return "e39";
            return "e1";
        } else {
            if (config.bustyle === 27 || config.bustyle === 28) return "f27";
            if (config.bustyle === 23) return "f23";
            if (config.bustyle === 24) return "f24";
            if (config.bustyle === 41) return "f41";
            return `f${config.id}`;
        }
    }
}