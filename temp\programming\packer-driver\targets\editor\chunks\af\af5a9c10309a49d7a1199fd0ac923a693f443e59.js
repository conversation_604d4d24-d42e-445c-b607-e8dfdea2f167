System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, CCFloat, LevelEditorBaseUI, _dec, _dec2, _dec3, _class, _class2, _crd, ccclass, property, executeInEditMode, LevelEditorElemUI;

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfLevelEditorBaseUI(extras) {
    _reporterNs.report("LevelEditorBaseUI", "./LevelEditorBaseUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataElem(extras) {
    _reporterNs.report("LevelDataElem", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
      CCFloat = _cc.CCFloat;
    }, function (_unresolved_2) {
      LevelEditorBaseUI = _unresolved_2.LevelEditorBaseUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5ee394EpEZENIRzqxl/v/Ci", "LevelEditorElemUI", undefined);

      __checkObsolete__(['_decorator', 'CCInteger', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'Asset', 'resources', 'assetManager', 'AssetManager', 'Sprite', 'SpriteFrame', 'SpriteAtlas', 'math', 'instantiate', 'Vec2', 'CCFloat']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelEditorElemUI", LevelEditorElemUI = (_dec = ccclass('LevelEditorElemUI'), _dec2 = executeInEditMode(), _dec3 = property(CCFloat), _dec(_class = _dec2(_class = (_class2 = class LevelEditorElemUI extends Component {
        constructor(...args) {
          super(...args);
          this.elemID = "";
        }

        get time() {
          var _this$node$parent;

          const layerNode = (_this$node$parent = this.node.parent) == null ? void 0 : _this$node$parent.parent;

          if (!layerNode) {
            return 0;
          }

          const rootNode = layerNode.parent.parent;

          if (!rootNode) {
            return 0;
          }

          const baseUI = rootNode.getComponent(_crd && LevelEditorBaseUI === void 0 ? (_reportPossibleCrUseOfLevelEditorBaseUI({
            error: Error()
          }), LevelEditorBaseUI) : LevelEditorBaseUI);

          if (!baseUI) {
            return 0;
          }

          const layer = baseUI.floorLayers.find(layer => layer.node == layerNode) || baseUI.skyLayers.find(layer => layer.node == layerNode);

          if (!layer) {
            return 0;
          }

          return this.node.position.y / layer.speed;
        }

        onLoad() {
          if (this.elemID == "") {
            this.elemID = this.uuid;
          }
        }

        initByLevelData(data) {
          this.node.setPosition(data.position.x, data.position.y);
          this.elemID = data.elemID;
          this.node.name = data.name;
        }

        fillLevelData(data) {
          data.position = new Vec2(this.node.position.x, this.node.position.y);
          data.elemID = this.elemID;
          data.name = this.node.name;
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "time", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "time"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=af5a9c10309a49d7a1199fd0ac923a693f443e59.js.map