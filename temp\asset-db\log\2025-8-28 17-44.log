2025-8-28 17:44:02-debug: start **** info
2025-8-28 17:44:03-log: Cannot access game frame or container.
2025-8-28 17:44:03-debug: asset-db:require-engine-code (407ms)
2025-8-28 17:44:03-log: meshopt wasm decoder initialized
2025-8-28 17:44:03-log: [bullet]:bullet wasm lib loaded.
2025-8-28 17:44:03-log: [box2d]:box2d wasm lib loaded.
2025-8-28 17:44:03-log: Cocos Creator v3.8.6
2025-8-28 17:44:03-log: Forward render pipeline initialized.
2025-8-28 17:44:03-log: Using legacy pipeline
2025-8-28 17:44:03-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.88MB, end 80.07MB, increase: 49.19MB
2025-8-28 17:44:03-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.97MB, end 84.29MB, increase: 3.32MB
2025-8-28 17:44:04-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.09MB, end 287.70MB, increase: 207.61MB
2025-8-28 17:44:04-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.31MB, end 288.96MB, increase: 204.64MB
2025-8-28 17:44:04-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.82MB, end 287.67MB, increase: 206.85MB
2025-8-28 17:44:04-debug: run package(harmonyos-next) handler(enable) start
2025-8-28 17:44:04-debug: run package(harmonyos-next) handler(enable) success!
2025-8-28 17:44:04-debug: run package(honor-mini-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(huawei-agc) handler(enable) success!
2025-8-28 17:44:04-debug: run package(honor-mini-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(huawei-agc) handler(enable) start
2025-8-28 17:44:04-debug: run package(huawei-quick-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(ios) handler(enable) start
2025-8-28 17:44:04-debug: run package(linux) handler(enable) start
2025-8-28 17:44:04-debug: run package(ios) handler(enable) success!
2025-8-28 17:44:04-debug: run package(linux) handler(enable) success!
2025-8-28 17:44:04-debug: run package(huawei-quick-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(mac) handler(enable) start
2025-8-28 17:44:04-debug: run package(migu-mini-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(mac) handler(enable) success!
2025-8-28 17:44:04-debug: run package(migu-mini-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(native) handler(enable) success!
2025-8-28 17:44:04-debug: run package(oppo-mini-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(native) handler(enable) start
2025-8-28 17:44:04-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(ohos) handler(enable) start
2025-8-28 17:44:04-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-28 17:44:04-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-28 17:44:04-debug: run package(ohos) handler(enable) success!
2025-8-28 17:44:04-debug: run package(taobao-mini-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(web-desktop) handler(enable) success!
2025-8-28 17:44:04-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(web-mobile) handler(enable) start
2025-8-28 17:44:04-debug: run package(web-desktop) handler(enable) start
2025-8-28 17:44:04-debug: run package(web-mobile) handler(enable) success!
2025-8-28 17:44:04-debug: run package(vivo-mini-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(wechatgame) handler(enable) success!
2025-8-28 17:44:04-debug: run package(wechatprogram) handler(enable) start
2025-8-28 17:44:04-debug: run package(wechatprogram) handler(enable) success!
2025-8-28 17:44:04-debug: run package(windows) handler(enable) start
2025-8-28 17:44:04-debug: run package(wechatgame) handler(enable) start
2025-8-28 17:44:04-debug: run package(windows) handler(enable) success!
2025-8-28 17:44:04-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-28 17:44:04-debug: run package(cocos-service) handler(enable) success!
2025-8-28 17:44:04-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-28 17:44:04-debug: run package(im-plugin) handler(enable) start
2025-8-28 17:44:04-debug: run package(cocos-service) handler(enable) start
2025-8-28 17:44:04-debug: run package(im-plugin) handler(enable) success!
2025-8-28 17:44:04-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-28 17:44:04-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-28 17:44:04-debug: run package(level-editor) handler(enable) start
2025-8-28 17:44:04-debug: asset-db:worker-init: initPlugin (1031ms)
2025-8-28 17:44:04-debug: run package(level-editor) handler(enable) success!
2025-8-28 17:44:04-debug: [Assets Memory track]: asset-db:worker-init start:30.87MB, end 289.86MB, increase: 258.99MB
2025-8-28 17:44:04-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-28 17:44:04-debug: Run asset db hook programming:beforePreStart success!
2025-8-28 17:44:04-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-28 17:44:04-debug: Run asset db hook programming:beforePreStart ...
2025-8-28 17:44:04-debug: run package(level-editor-extension) handler(enable) start
2025-8-28 17:44:04-debug: run package(level-editor-extension) handler(enable) success!
2025-8-28 17:44:04-debug: run package(localization-editor) handler(enable) start
2025-8-28 17:44:04-debug: run package(localization-editor) handler(enable) success!
2025-8-28 17:44:04-debug: asset-db:worker-init (1629ms)
2025-8-28 17:44:04-debug: asset-db-hook-programming-beforePreStart (125ms)
2025-8-28 17:44:04-debug: asset-db-hook-engine-extends-beforePreStart (124ms)
2025-8-28 17:44:04-debug: run package(placeholder) handler(enable) start
2025-8-28 17:44:04-debug: run package(placeholder) handler(enable) success!
2025-8-28 17:44:04-debug: Preimport db internal success
2025-8-28 17:44:04-debug: Preimport db assets success
2025-8-28 17:44:04-debug: Run asset db hook programming:afterPreStart ...
2025-8-28 17:44:04-debug: starting packer-driver...
2025-8-28 17:44:08-debug: initialize scripting environment...
2025-8-28 17:44:08-debug: [[Executor]] prepare before lock
2025-8-28 17:44:09-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-28 17:44:09-debug: [[Executor]] prepare after unlock
2025-8-28 17:44:09-debug: Run asset db hook programming:afterPreStart success!
2025-8-28 17:44:09-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-28 17:44:09-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-28 17:44:09-debug: Start up the 'internal' database...
2025-8-28 17:44:09-debug: asset-db:worker-effect-data-processing (226ms)
2025-8-28 17:44:09-debug: asset-db-hook-programming-afterPreStart (4599ms)
2025-8-28 17:44:09-debug: asset-db-hook-engine-extends-afterPreStart (226ms)
2025-8-28 17:44:09-debug: Start up the 'assets' database...
2025-8-28 17:44:09-debug: asset-db:worker-startup-database[internal] (4894ms)
2025-8-28 17:44:09-debug: [Assets Memory track]: asset-db:worker-init: startup start:177.42MB, end 193.54MB, increase: 16.13MB
2025-8-28 17:44:09-debug: lazy register asset handler directory
2025-8-28 17:44:09-debug: lazy register asset handler text
2025-8-28 17:44:09-debug: lazy register asset handler *
2025-8-28 17:44:09-debug: lazy register asset handler json
2025-8-28 17:44:09-debug: lazy register asset handler spine-data
2025-8-28 17:44:09-debug: lazy register asset handler dragonbones
2025-8-28 17:44:09-debug: lazy register asset handler javascript
2025-8-28 17:44:09-debug: lazy register asset handler dragonbones-atlas
2025-8-28 17:44:09-debug: lazy register asset handler terrain
2025-8-28 17:44:09-debug: lazy register asset handler scene
2025-8-28 17:44:09-debug: lazy register asset handler typescript
2025-8-28 17:44:09-debug: lazy register asset handler prefab
2025-8-28 17:44:09-debug: lazy register asset handler sprite-frame
2025-8-28 17:44:09-debug: lazy register asset handler tiled-map
2025-8-28 17:44:09-debug: lazy register asset handler sign-image
2025-8-28 17:44:09-debug: lazy register asset handler buffer
2025-8-28 17:44:09-debug: lazy register asset handler texture
2025-8-28 17:44:09-debug: lazy register asset handler image
2025-8-28 17:44:09-debug: lazy register asset handler texture-cube
2025-8-28 17:44:09-debug: lazy register asset handler erp-texture-cube
2025-8-28 17:44:09-debug: lazy register asset handler texture-cube-face
2025-8-28 17:44:09-debug: lazy register asset handler alpha-image
2025-8-28 17:44:09-debug: lazy register asset handler rt-sprite-frame
2025-8-28 17:44:09-debug: lazy register asset handler render-texture
2025-8-28 17:44:09-debug: lazy register asset handler gltf-mesh
2025-8-28 17:44:09-debug: lazy register asset handler gltf-animation
2025-8-28 17:44:09-debug: lazy register asset handler gltf-scene
2025-8-28 17:44:09-debug: lazy register asset handler gltf-skeleton
2025-8-28 17:44:09-debug: lazy register asset handler gltf-embeded-image
2025-8-28 17:44:09-debug: lazy register asset handler gltf
2025-8-28 17:44:09-debug: lazy register asset handler material
2025-8-28 17:44:09-debug: lazy register asset handler gltf-material
2025-8-28 17:44:09-debug: lazy register asset handler physics-material
2025-8-28 17:44:09-debug: lazy register asset handler effect-header
2025-8-28 17:44:09-debug: lazy register asset handler fbx
2025-8-28 17:44:09-debug: lazy register asset handler audio-clip
2025-8-28 17:44:09-debug: lazy register asset handler animation-clip
2025-8-28 17:44:09-debug: lazy register asset handler effect
2025-8-28 17:44:09-debug: lazy register asset handler animation-graph
2025-8-28 17:44:09-debug: lazy register asset handler animation-graph-variant
2025-8-28 17:44:09-debug: lazy register asset handler bitmap-font
2025-8-28 17:44:09-debug: lazy register asset handler animation-mask
2025-8-28 17:44:09-debug: lazy register asset handler sprite-atlas
2025-8-28 17:44:09-debug: lazy register asset handler particle
2025-8-28 17:44:09-debug: lazy register asset handler auto-atlas
2025-8-28 17:44:09-debug: lazy register asset handler ttf-font
2025-8-28 17:44:09-debug: lazy register asset handler label-atlas
2025-8-28 17:44:09-debug: lazy register asset handler render-stage
2025-8-28 17:44:09-debug: lazy register asset handler render-pipeline
2025-8-28 17:44:09-debug: lazy register asset handler render-flow
2025-8-28 17:44:09-debug: lazy register asset handler instantiation-material
2025-8-28 17:44:09-debug: lazy register asset handler instantiation-skeleton
2025-8-28 17:44:09-debug: lazy register asset handler instantiation-mesh
2025-8-28 17:44:09-debug: lazy register asset handler instantiation-animation
2025-8-28 17:44:09-debug: lazy register asset handler video-clip
2025-8-28 17:44:09-debug: asset-db:worker-startup-database[assets] (4810ms)
2025-8-28 17:44:09-debug: asset-db:ready (8067ms)
2025-8-28 17:44:09-debug: asset-db:start-database (4976ms)
2025-8-28 17:44:09-debug: init worker message success
2025-8-28 17:44:09-debug: fix the bug of updateDefaultUserData
2025-8-28 17:44:09-debug: programming:execute-script (3ms)
2025-8-28 17:44:09-debug: [Build Memory track]: builder:worker-init start:191.98MB, end 210.22MB, increase: 18.24MB
2025-8-28 17:44:09-debug: builder:worker-init (306ms)
2025-8-28 17:47:38-debug: refresh db internal success
2025-8-28 17:47:38-debug: refresh db assets success
2025-8-28 17:47:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:47:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:47:38-debug: asset-db:refresh-all-database (140ms)
2025-8-28 17:47:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 17:47:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:47:40-debug: refresh db internal success
2025-8-28 17:47:40-debug: refresh db assets success
2025-8-28 17:47:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:47:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:47:40-debug: asset-db:refresh-all-database (128ms)
2025-8-28 17:47:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:57:44-debug: refresh db internal success
2025-8-28 17:57:44-debug: refresh db assets success
2025-8-28 17:57:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:57:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:57:44-debug: asset-db:refresh-all-database (127ms)
2025-8-28 17:59:46-debug: refresh db internal success
2025-8-28 17:59:46-debug: refresh db assets success
2025-8-28 17:59:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:59:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:59:46-debug: asset-db:refresh-all-database (105ms)
2025-8-28 17:59:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-28 17:59:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:59:48-debug: refresh db internal success
2025-8-28 17:59:48-debug: refresh db assets success
2025-8-28 17:59:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 17:59:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 17:59:48-debug: asset-db:refresh-all-database (105ms)
2025-8-28 17:59:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-28 17:59:52-debug: Query all assets info in project
2025-8-28 17:59:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:59:52-debug: Skip compress image, progress: 0%
2025-8-28 17:59:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:59:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:59:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:59:52-debug:   Number of other assets: 2032
2025-8-28 17:59:52-debug:   Number of all scripts: 261
2025-8-28 17:59:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:59:52-debug:   Number of all scenes: 8
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ---- (22ms)
2025-8-28 17:59:52-log: run build task Query asset bundle success in 22 ms√, progress: 5%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:215.20MB, end 214.73MB, increase: -487.71KB
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:59:52-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:214.76MB, end 215.04MB, increase: 282.51KB
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:59:52-debug: [Build Memory track]: Sort some build options to settings.json start:215.07MB, end 215.09MB, increase: 19.65KB
2025-8-28 17:59:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:59:52-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:59:52-debug: [Build Memory track]: Fill script data to settings start:215.13MB, end 215.16MB, increase: 27.98KB
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ---- (3ms)
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in 3 ms√, progress: 15%
2025-8-28 17:59:52-debug: [Build Memory track]: Sort some build options to settings.json start:215.19MB, end 215.49MB, increase: 311.50KB
2025-8-28 17:59:52-debug: Query all assets info in project
2025-8-28 17:59:52-debug: Query all assets info in project
2025-8-28 17:59:52-debug: Query all assets info in project
2025-8-28 17:59:52-debug: Query all assets info in project
2025-8-28 17:59:52-debug: Skip compress image, progress: 0%
2025-8-28 17:59:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:59:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:59:52-debug: Skip compress image, progress: 0%
2025-8-28 17:59:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:59:52-debug: Skip compress image, progress: 0%
2025-8-28 17:59:52-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 17:59:52-debug: Skip compress image, progress: 0%
2025-8-28 17:59:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:59:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:59:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:59:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:59:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:59:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:59:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:59:52-debug: Init all bundles start..., progress: 0%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Num of bundles: 3..., progress: 0%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 0%
2025-8-28 17:59:52-debug: Init bundle root assets start..., progress: 0%
2025-8-28 17:59:52-debug:   Number of all scripts: 261
2025-8-28 17:59:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:59:52-debug:   Number of all scenes: 8
2025-8-28 17:59:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:59:52-debug:   Number of other assets: 2032
2025-8-28 17:59:52-debug:   Number of all scenes: 8
2025-8-28 17:59:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:59:52-debug:   Number of all scripts: 261
2025-8-28 17:59:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:215.17MB, end 219.79MB, increase: 4.62MB
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ---- (19ms)
2025-8-28 17:59:52-log: run build task Query asset bundle success in 19 ms√, progress: 5%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug:   Number of other assets: 2032
2025-8-28 17:59:52-log: run build task Query asset bundle success in √, progress: 5%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:219.82MB, end 219.84MB, increase: 14.57KB
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:59:52-debug:   Number of other assets: 2032
2025-8-28 17:59:52-debug:   Number of all scenes: 8
2025-8-28 17:59:52-debug:   Number of all scripts: 261
2025-8-28 17:59:52-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 17:59:52-debug:   Number of all scenes: 8
2025-8-28 17:59:52-debug:   Number of all scripts: 261
2025-8-28 17:59:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:59:52-debug: Init bundle root assets success..., progress: 0%
2025-8-28 17:59:52-debug:   Number of other assets: 2032
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ---- (49ms)
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:219.87MB, end 207.74MB, increase: -12421.93KB
2025-8-28 17:59:52-log: run build task Query asset bundle success in 49 ms√, progress: 5%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-log: run build task Query asset bundle success in √, progress: 5%
2025-8-28 17:59:52-debug: Query asset bundle start, progress: 5%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:207.78MB, end 207.80MB, increase: 21.57KB
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ----
2025-8-28 17:59:52-log: run build task Query asset bundle success in 2 ms√, progress: 10%
2025-8-28 17:59:52-debug: [Build Memory track]: Query asset bundle start:207.84MB, end 208.85MB, increase: 1.01MB
2025-8-28 17:59:52-debug: // ---- build task Query asset bundle ---- (2ms)
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-log: run build task Query asset bundle success in √, progress: 10%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-log: run build task Query asset bundle success in √, progress: 10%
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 17:59:52-log: run build task Query asset bundle success in √, progress: 10%
2025-8-28 17:59:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:59:52-debug: [Build Memory track]: Sort some build options to settings.json start:209.02MB, end 209.04MB, increase: 15.98KB
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 17:59:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 17:59:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 12%
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 17:59:52-debug: Fill script data to settings start, progress: 12%
2025-8-28 17:59:52-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:59:52-debug: [Build Memory track]: Fill script data to settings start:209.24MB, end 209.25MB, increase: 15.35KB
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ----
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-debug: // ---- build task Fill script data to settings ---- (1ms)
2025-8-28 17:59:52-log: run build task Fill script data to settings success in 1 ms√, progress: 13%
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 17:59:52-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 17:59:52-debug: // ---- build task Sort some build options to settings.json ---- (6ms)
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in 6 ms√, progress: 15%
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 17:59:52-debug: [Build Memory track]: Sort some build options to settings.json start:209.42MB, end 210.52MB, increase: 1.10MB
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 17:59:52-log: run build task Sort some build options to settings.json success in √, progress: 15%
2025-8-28 18:08:04-debug: refresh db internal success
2025-8-28 18:08:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-8-28 18:08:04-debug: refresh db assets success
2025-8-28 18:08:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 18:08:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 18:08:04-debug: asset-db:refresh-all-database (143ms)
2025-8-28 18:08:05-debug: Query all assets info in project
2025-8-28 18:08:05-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-28 18:08:05-debug: Skip compress image, progress: 0%
2025-8-28 18:08:05-debug: Init all bundles start..., progress: 0%
2025-8-28 18:08:05-debug: Num of bundles: 3..., progress: 0%
2025-8-28 18:08:05-debug: Query asset bundle start, progress: 0%
2025-8-28 18:08:05-debug: // ---- build task Query asset bundle ----
2025-8-28 18:08:05-debug: Init bundle root assets start..., progress: 0%
2025-8-28 18:08:05-debug:   Number of other assets: 2032
2025-8-28 18:08:05-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-28 18:08:05-debug:   Number of all scenes: 8
2025-8-28 18:08:05-debug:   Number of all scripts: 261
2025-8-28 18:08:05-debug: Init bundle root assets success..., progress: 0%
2025-8-28 18:08:06-debug: // ---- build task Query asset bundle ---- (27ms)
2025-8-28 18:08:06-debug: [Build Memory track]: Query asset bundle start:211.68MB, end 212.48MB, increase: 817.78KB
2025-8-28 18:08:06-log: run build task Query asset bundle success in 27 ms√, progress: 5%
2025-8-28 18:08:06-debug: // ---- build task Query asset bundle ----
2025-8-28 18:08:06-debug: Query asset bundle start, progress: 5%
2025-8-28 18:08:06-debug: // ---- build task Query asset bundle ---- (1ms)
2025-8-28 18:08:06-debug: [Build Memory track]: Query asset bundle start:212.51MB, end 212.79MB, increase: 285.54KB
2025-8-28 18:08:06-debug: Sort some build options to settings.json start, progress: 10%
2025-8-28 18:08:06-log: run build task Query asset bundle success in 1 ms√, progress: 10%
2025-8-28 18:08:06-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 18:08:06-debug: Fill script data to settings start, progress: 12%
2025-8-28 18:08:06-debug: [Build Memory track]: Sort some build options to settings.json start:212.82MB, end 212.84MB, increase: 19.16KB
2025-8-28 18:08:06-log: run build task Sort some build options to settings.json success in √, progress: 12%
2025-8-28 18:08:06-debug: // ---- build task Fill script data to settings ----
2025-8-28 18:08:06-debug: [Build Memory track]: Fill script data to settings start:212.88MB, end 212.89MB, increase: 19.04KB
2025-8-28 18:08:06-debug: Sort some build options to settings.json start, progress: 13%
2025-8-28 18:08:06-log: run build task Fill script data to settings success in √, progress: 13%
2025-8-28 18:08:06-debug: // ---- build task Sort some build options to settings.json ----
2025-8-28 18:08:06-debug: // ---- build task Sort some build options to settings.json ---- (1ms)
2025-8-28 18:08:06-log: run build task Sort some build options to settings.json success in 1 ms√, progress: 15%
2025-8-28 18:08:06-debug: [Build Memory track]: Sort some build options to settings.json start:212.93MB, end 213.22MB, increase: 296.42KB
2025-8-28 18:10:01-debug: refresh db internal success
2025-8-28 18:10:01-debug: refresh db assets success
2025-8-28 18:10:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-28 18:10:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-28 18:10:01-debug: asset-db:refresh-all-database (140ms)
2025-8-28 18:10:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
