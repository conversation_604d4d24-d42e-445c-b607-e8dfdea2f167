{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts"], "names": ["BattleManager", "SingletonBase", "GameConst", "GameEnum", "GameIns", "ExchangeMap", "GameMapRun", "UIMgr", "LoadingUI", "_percent", "gameType", "GameType", "Common", "initBattleEnd", "gameStart", "animSpeed", "_gameTime", "mainStage", "subStage", "_loadFinish", "_loadTotal", "_loadCount", "mainReset", "enemyManager", "boss<PERSON><PERSON><PERSON>", "waveManager", "mainPlaneManager", "instance", "reset", "clear", "bulletManager", "hurtEffectManager", "gameRuleManager", "subReset", "hurtTotal", "checkLoadFinish", "loadingUI", "get", "updateProgress", "initBattle", "closeUI", "addLoadCount", "count", "startLoading", "gameSortie", "gameResManager", "preload", "preLoad", "initData", "me", "maskTextureInit", "gameDataManager", "resetBattleData", "removeAll", "stageManager", "mainPlane", "mapEndChange", "planeIn", "onPlaneIn", "beginBattle", "begine", "update", "dt", "GameAble", "isGameOver", "planeManager", "enemyTarget", "isInBattle", "isGameWillOver", "updateGameLogic", "battleDie", "gamePause", "battleFail", "gameMainUI", "showGameResult", "hpBar", "active", "endBattle", "battleSucc", "stopFire", "gameOver", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "bossWillEnter", "bossFightStart", "fireEnable", "moveAble", "getRatio", "isGameType", "e"], "mappings": ";;;sHAUaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACAC,MAAAA,U;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;+BAEIR,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AAAA;AAAA;AAAA,eAE5DS,QAF4D,GAEjD,CAFiD;AAAA,eAG5DC,QAH4D,GAGjD;AAAA;AAAA,oCAASC,QAAT,CAAkBC,MAH+B;AAAA,eAI5DC,aAJ4D,GAI5C,KAJ4C;AAAA,eAK5DC,SAL4D,GAKhD,KALgD;AAAA,eAM5DC,SAN4D,GAMhD,CANgD;AAAA,eAO5DC,SAP4D,GAOhD,CAPgD;AAAA,eAS5DC,SAT4D,GAShD,CATgD;AAAA,eAU5DC,QAV4D,GAUjD,CAViD;AAAA,eAY5DC,WAZ4D,GAY9C,KAZ8C;AAAA,eAa5DC,UAb4D,GAa/C,CAb+C;AAAA,eAc5DC,UAd4D,GAc/C,CAd+C;AAAA;;AAiB5DC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,SAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,SAApB;AACA;AAAA;AAAA,kCAAQG,WAAR,CAAoBH,SAApB;AACA;AAAA;AAAA,kCAAQI,gBAAR,CAAyBJ,SAAzB;AACA;AAAA;AAAA,wCAAWK,QAAX,CAAoBC,KAApB;AACA;AAAA;AAAA,wCAAWD,QAAX,CAAoBE,KAApB;AACA;AAAA;AAAA,kCAAQC,aAAR,CAAsBD,KAAtB;AACA;AAAA;AAAA,kCAAQE,iBAAR,CAA0BF,KAA1B;AACA;AAAA;AAAA,kCAAQG,eAAR,CAAwBJ,KAAxB;AACH;;AAEDK,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQD,eAAR,CAAwBJ,KAAxB;AACA;AAAA;AAAA,kCAAQH,WAAR,CAAoBG,KAApB;AACA;AAAA;AAAA,kCAAQL,YAAR,CAAqBU,QAArB;AACA;AAAA;AAAA,kCAAQT,WAAR,CAAoBS,QAApB;AACA;AAAA;AAAA,kCAAQP,gBAAR,CAAyBQ,SAAzB,GAAqC,CAArC;AACA,eAAKxB,QAAL,GAAgB;AAAA;AAAA,oCAASC,QAAT,CAAkBC,MAAlC;AACH;AAED;AACJ;AACA;;;AACIuB,QAAAA,eAAe,GAAG;AACd,eAAKd,UAAL;AACA,cAAIe,SAAS,GAAG;AAAA;AAAA,8BAAMC,GAAN;AAAA;AAAA,qCAAhB;AACAD,UAAAA,SAAS,CAACE,cAAV,CAAyB,KAAKjB,UAAL,GAAkB,KAAKD,UAAhD;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,iBAAKmB,UAAL;AACA;AAAA;AAAA,gCAAMC,OAAN;AAAA;AAAA;AACH;AAEJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKtB,UAAL,IAAmBsB,KAAnB;AACH;;AAEDC,QAAAA,YAAY,CAAC1B,SAAD,EAAmBC,QAAnB,EAAoC;AAC5C,eAAKD,SAAL,GAAiBA,SAAjB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AAEA;AAAA;AAAA,kCAAQc,eAAR,CAAwBY,UAAxB;AAEA;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,OAAvB;AACA;AAAA;AAAA,kCAAQpB,gBAAR,CAAyBoB,OAAzB;AACA;AAAA;AAAA,kCAAQhB,aAAR,CAAsBiB,OAAtB,CAA8B9B,SAA9B,EAR4C,CAQH;;AACzC;AAAA;AAAA,kCAAQc,iBAAR,CAA0BgB,OAA1B,GAT4C,CASR;;AACpC;AAAA;AAAA,wCAAWpB,QAAX,CAAoBqB,QAApB,CAA6B/B,SAA7B,EAV4C,CAUJ;;AACxC;AAAA;AAAA,0CAAYgC,EAAZ,CAAeC,eAAf;AACA;AAAA;AAAA,kCAAQ3B,YAAR,CAAqBwB,OAArB,CAA6B9B,SAA7B,EAZ4C,CAYJ;;AACxC;AAAA;AAAA,kCAAQO,WAAR,CAAoBuB,OAApB,GAb4C,CAad;AACjC;;AAIDR,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQY,eAAR,CAAwBC,eAAxB;AACA;AAAA;AAAA,kCAAQtB,aAAR,CAAsBuB,SAAtB;AAEA;AAAA;AAAA,wCAAW1B,QAAX,CAAoBY,UAApB;AACA;AAAA;AAAA,kCAAQe,YAAR,CAAqBf,UAArB,CAAgC,KAAKtB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQK,YAAR,CAAqBgB,UAArB,CAAgC,KAAKtB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQQ,gBAAR,CAAyB6B,SAAzB,CAAmChB,UAAnC;AACA;AAAA;AAAA,wCAAWZ,QAAX,CAAoB6B,YAApB;AACA;AAAA;AAAA,kCAAQ9B,gBAAR,CAAyB6B,SAAzB,CAAmCE,OAAnC;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,eAAK7C,aAAL,GAAqB,IAArB;AACA,eAAK8C,WAAL;AACH;;AAEDA,QAAAA,WAAW,GAAG;AACV,cAAI,KAAK9C,aAAL,IAAsB,CAAC,KAAKC,SAAhC,EAA2C;AACvC,iBAAKA,SAAL,GAAiB,IAAjB;AAEA;AAAA;AAAA,oCAAQwC,YAAR,CAAqBxC,SAArB;AACA;AAAA;AAAA,oCAAQW,WAAR,CAAoBX,SAApB;AACA;AAAA;AAAA,oCAAQkB,eAAR,CAAwBlB,SAAxB;AAEA;AAAA;AAAA,oCAAQY,gBAAR,CAAyB6B,SAAzB,CAAmCK,MAAnC,CAA0C,IAA1C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAY;AACd,cAAI,CAAC;AAAA;AAAA,sCAAUC,QAAf,EAAyB;AACrB;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQ/B,eAAR,CAAwBgC,UAAxB,EAAJ,EAA0C;AACtC,gBAAI;AAAA;AAAA,oCAAQC,YAAZ,EAA0B;AACtB;AAAA;AAAA,sCAAQA,YAAR,CAAqBC,WAArB,GAAmC,IAAnC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQlC,eAAR,CAAwBmC,UAAxB,MAAwC;AAAA;AAAA,kCAAQnC,eAAR,CAAwBoC,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQH,YAAR,CAAqBJ,MAArB,CAA4BC,EAA5B;AACA;AAAA;AAAA,oCAAQ9B,eAAR,CAAwBqC,eAAxB,CAAwCP,EAAxC;AACA;AAAA;AAAA,oCAAQrC,WAAR,CAAoB4C,eAApB,CAAoCP,EAApC;AACA;AAAA;AAAA,oCAAQvC,YAAR,CAAqB8C,eAArB,CAAqCP,EAArC;AACA;AAAA;AAAA,oCAAQtC,WAAR,CAAoB6C,eAApB,CAAoCP,EAApC;AACA,iBAAK9C,SAAL,IAAkB8C,EAAlB;AACH,WAPD,MAOO,IAAI;AAAA;AAAA,kCAAQG,YAAZ,EAA0B;AAC7B;AAAA;AAAA,oCAAQA,YAAR,CAAqBC,WAArB,GAAmC,IAAnC;AACH;AACJ;AAED;AACJ;AACA;;;AACmB,cAATI,SAAS,GAAG;AACd;AACA;AAAA;AAAA,kCAAQtC,eAAR,CAAwBuC,SAAxB;AACH,SAzI2D,CA2I5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQC,UAAR,CAAmBC,cAAnB,CAAkC,KAAlC;AACA;AAAA;AAAA,kCAAQhD,gBAAR,CAAyB6B,SAAzB,CAAmCoB,KAAnC,CAAyCC,MAAzC,GAAkD,KAAlD;AACA,eAAKC,SAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQpD,gBAAR,CAAyB6B,SAAzB,CAAmCoB,KAAnC,CAAyCC,MAAzC,GAAkD,KAAlD;AACA;AAAA;AAAA,kCAAQlD,gBAAR,CAAyB6B,SAAzB,CAAmCwB,QAAnC;AACA;AAAA;AAAA,kCAAQN,UAAR,CAAmBC,cAAnB,CAAkC,IAAlC;AACA,eAAKG,SAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQ7C,eAAR,CAAwBgD,QAAxB;AAEAC,UAAAA,UAAU,CAAC,MAAM;AACb;AAAA;AAAA,oCAAQnD,aAAR,CAAsBuB,SAAtB,CAAgC,KAAhC,EAAuC,IAAvC;AACH,WAFS,EAEP,IAFO,CAAV;AAIA,eAAKvC,SAAL,GAAiB,KAAjB;AACA,eAAKD,aAAL,GAAqB,KAArB;AACH;AAGD;AACJ;AACA;AACA;;;AACIqE,QAAAA,gBAAgB,CAACC,QAAD,EAAmB,CAC/B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQ3D,gBAAR,CAAyB4D,UAAzB,GAAsC,IAAtC;AACA;AAAA;AAAA,kCAAQ5D,gBAAR,CAAyB6D,QAAzB,GAAoC,IAApC;AAEA;AAAA;AAAA,kCAAQ/D,WAAR,CAAoB6D,cAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,QAAQ,GAAG;AACP,iBAAO,QAAP,CADO,CACU;AACpB;;AAEDC,QAAAA,UAAU,CAACC,CAAD,EAAa;AACnB,iBAAO,KAAKhF,QAAL,IAAiBgF,CAAxB;AACH;;AA3O2D,O", "sourcesContent": ["\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport ExchangeMap from \"../ui/base/ExchangeMap\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport { UIMgr } from \"../../ui/UIMgr\";\r\nimport { LoadingUI } from \"../../ui/LoadingUI\";\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    _percent = 0;\r\n    gameType = GameEnum.GameType.Common;\r\n    initBattleEnd = false;\r\n    gameStart = false;\r\n    animSpeed = 1;\r\n    _gameTime = 0;\r\n\r\n    mainStage = 0;\r\n    subStage = 0;\r\n\r\n    _loadFinish = false;\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n\r\n\r\n    mainReset() {\r\n        GameIns.enemyManager.mainReset();\r\n        GameIns.bossManager.mainReset();\r\n        GameIns.waveManager.mainReset();\r\n        GameIns.mainPlaneManager.mainReset();\r\n        GameMapRun.instance.reset();\r\n        GameMapRun.instance.clear();\r\n        GameIns.bulletManager.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameRuleManager.reset();\r\n    }\r\n\r\n    subReset() {\r\n        GameIns.gameRuleManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n        GameIns.mainPlaneManager.hurtTotal = 0;\r\n        this.gameType = GameEnum.GameType.Common;\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        let loadingUI = UIMgr.get(LoadingUI)\r\n        loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            this.initBattle();\r\n            UIMgr.closeUI(LoadingUI)\r\n        }\r\n\r\n    }\r\n\r\n    addLoadCount(count :number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading(mainStage:number, subStage:number) {\r\n        this.mainStage = mainStage;\r\n        this.subStage = subStage;\r\n\r\n        GameIns.gameRuleManager.gameSortie();\r\n\r\n        GameIns.gameResManager.preload();\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.bulletManager.preLoad(mainStage);//子弹资源\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameMapRun.instance.initData(mainStage);//地图背景初始化\r\n        ExchangeMap.me.maskTextureInit();\r\n        GameIns.enemyManager.preLoad(mainStage);//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n\r\n\r\n    initBattle() {\r\n        GameIns.gameDataManager.resetBattleData();\r\n        GameIns.bulletManager.removeAll();\r\n\r\n        GameMapRun.instance.initBattle();\r\n        GameIns.stageManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.enemyManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.mainPlaneManager.mainPlane.initBattle();\r\n        GameMapRun.instance.mapEndChange();\r\n        GameIns.mainPlaneManager.mainPlane.planeIn();\r\n    }\r\n\r\n    onPlaneIn() {\r\n        this.initBattleEnd = true;\r\n        this.beginBattle();\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.gameStart) {\r\n            this.gameStart = true;\r\n\r\n            GameIns.stageManager.gameStart();\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameRuleManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane.begine(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    update(dt:number) {\r\n        if (!GameConst.GameAble) {\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isGameOver()) {\r\n            if (GameIns.planeManager) {\r\n                GameIns.planeManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.planeManager.update(dt);\r\n            GameIns.gameRuleManager.updateGameLogic(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n            this._gameTime += dt;\r\n        } else if (GameIns.planeManager) {\r\n            GameIns.planeManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 战斗失败逻辑\r\n     */\r\n    async battleDie() {\r\n        // GameFunc.addDialog(ReplayUI.default);\r\n        GameIns.gameRuleManager.gamePause();\r\n    }\r\n\r\n    //     /**\r\n    //      * 战斗复活逻辑\r\n    //      */\r\n    //     relifeBattle() {\r\n    //         GameIns.eventManager.emit(GameEvent.MainRelife);\r\n    //         GameIns.gameRuleManager.gameResume();\r\n    //     }\r\n\r\n    /**\r\n     * 战斗失败结算\r\n     */\r\n    battleFail() {\r\n        GameIns.gameMainUI.showGameResult(false);\r\n        GameIns.mainPlaneManager.mainPlane.hpBar.active = false;\r\n        this.endBattle();\r\n    }\r\n\r\n    /**\r\n     * 战斗胜利逻辑\r\n     */\r\n    battleSucc() {\r\n        GameIns.mainPlaneManager.mainPlane.hpBar.active = false;\r\n        GameIns.mainPlaneManager.mainPlane.stopFire();\r\n        GameIns.gameMainUI.showGameResult(true);\r\n        this.endBattle();\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        GameIns.gameRuleManager.gameOver();\r\n\r\n        setTimeout(() => {\r\n            GameIns.bulletManager.removeAll(false, true);\r\n        }, 1000);\r\n\r\n        this.gameStart = false;\r\n        this.initBattleEnd = false;\r\n    }\r\n\r\n\r\n    /**\r\n     * Boss切换完成\r\n     * @param {string} bossName Boss名称\r\n     */\r\n    bossChangeFinish(bossName: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        //        GameIns.mainPlaneManager.fireEnable = false;\r\n        //        GameIns.mainPlaneManager.moveAble = false;\r\n        //         WinePlaneManager.default.me.pauseBattle();\r\n\r\n        //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);\r\n        //         if (inGameUI) {\r\n        //             inGameUI.hideUI();\r\n        //         }\r\n\r\n        //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        //         if (bossEnterDialog) {\r\n        //             if (!bossEnterDialog.node.parent) {\r\n        //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);\r\n        //             }\r\n        //             bossEnterDialog.node.active = true;\r\n        //             bossEnterDialog.play();\r\n        //         }\r\n\r\n        //         GameIns.audioManager.playbg(\"bg_3\");\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.fireEnable = true;\r\n        GameIns.mainPlaneManager.moveAble = true;\r\n\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕比例\r\n     * @returns {number} 屏幕比例\r\n     */\r\n    getRatio() {\r\n        return 0.666667; // 固定比例值\r\n    }\r\n\r\n    isGameType(e : number) {\r\n        return this.gameType == e;\r\n    }\r\n}"]}