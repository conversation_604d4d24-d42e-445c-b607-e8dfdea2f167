System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, UITransform, Vec3, Entity, BattleLayer, Tools, GameIns, AimCircleScreen, CircleZoomScreen, LoftScreen, AimSingleLineScreen, _dec, _class, _crd, ccclass, property, AttackPoint;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "./Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAimCircleScreen(extras) {
    _reporterNs.report("AimCircleScreen", "../bulletDanmu/AimCircleScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCircleZoomScreen(extras) {
    _reporterNs.report("CircleZoomScreen", "../bulletDanmu/CircleZoomScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoftScreen(extras) {
    _reporterNs.report("LoftScreen", "../bulletDanmu/LoftScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAimSingleLineScreen(extras) {
    _reporterNs.report("AimSingleLineScreen", "../bulletDanmu/AimSingleLineScreen", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      UITransform = _cc.UITransform;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      Entity = _unresolved_2.default;
    }, function (_unresolved_3) {
      BattleLayer = _unresolved_3.default;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      AimCircleScreen = _unresolved_6.default;
    }, function (_unresolved_7) {
      CircleZoomScreen = _unresolved_7.CircleZoomScreen;
    }, function (_unresolved_8) {
      LoftScreen = _unresolved_8.default;
    }, function (_unresolved_9) {
      AimSingleLineScreen = _unresolved_9.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a30f3i5c99AI5v4HjPti5Hr", "AttackPoint", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Vec2', 'misc', 'UITransform', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", AttackPoint = (_dec = ccclass('AttackPoint'), _dec(_class = class AttackPoint extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor(...args) {
          super(...args);
          this._bapData = null;
          this._owner = null;
          this._atkType = 0;
          this._bulletEntity = null;
          this._isBoss = false;
          this._bulletNum = 0;
          this._bulletInterval = 0;
          this._bulletAttack = 0;
          this._bulletCount = 0;
          this._bulletCreateTime = 0;
          this._bShootStart = false;
          this._bAttackOver = false;
          this._attackOverCount = 0;
          this._attackOverTime = 0;
          this._shootInterval = 0;
          this._shootPauseTime = 0;
          this._bIndex = 0;
          this._bNum = 0;
          this._targetType = 0;
          this.m_danmuConfig = null;
          this._bulletID = 0;
          this.m_screen = null;
          this.bulletTurn = false;
          this._soundAble = false;
          this._soundId = 0;
          this._soundTime = 0;
          this._soundDuration = 0;
          this._waveDatas = [];
          this._waveTime = [];
          this._wavedNum = [];
          this._waveIndexOver = [];
          this._waveDelayDuration = 0;
          this._waveDelay = 0;
        }

        initForBoss(data, owner) {
          this._targetType = 1;
          this._bapData = data;
          this._owner = owner;
          this._isBoss = true;
          this._bulletEntity = owner;
          this.bulletTurn = false;
          this._bIndex = 0;
          this._atkType = this._bapData.atkType;

          switch (this._atkType) {
            case 0:
              this._bNum = data.bulletIDs.length;
              break;

            case 1:
              this._bNum = data.waveIds.length;
              break;
          }

          this.node.setPosition(data.x, data.y);
          this.reset();

          this._refreshNextShoot();
        }
        /**
         * 初始化 Boss 单位的攻击点
         * @param {Object} data 攻击点数据
         * @param {Object} owner 拥有者（Boss 实体）
         */


        initForBossUnit(data, owner) {
          this._targetType = 1; // 设置目标类型为 Boss

          this._bapData = data; // 存储攻击点数据

          this._owner = owner; // 设置拥有者

          this._isBoss = true; // 标记为 Boss

          this.bulletTurn = false; // 子弹是否转向

          this._bIndex = 0; // 当前攻击序列索引

          this._atkType = this._bapData.atkType; // 攻击类型
          // 根据攻击类型设置攻击序列数量

          switch (this._atkType) {
            case 0:
              // 子弹攻击
              this._bNum = data.bulletIDs.length;
              break;

            case 1:
              // 波次攻击
              this._bNum = data.waveIds.length;
              break;
          } // 设置攻击点的位置


          this.node.setPosition(data.x, data.y); // 重置状态并刷新下一次攻击

          this.reset();

          this._refreshNextShoot();
        }

        initForEnemy(data, owner) {
          this.reset();
          this._isBoss = false;
          this._targetType = 0;
          this._owner = owner;
          this._bulletEntity = owner;
          this._bIndex = 0;
          this._bNum = 1;
          this._atkType = 0;
          this._shootPauseTime = 0;
          this._shootInterval = data.shootInterval;
          this._bulletNum = data.bulletNum;
          this._bulletInterval = data.bulletInterval;
          this._bulletAttack = data.bulletAttackRate * this._owner.attack;
          this._soundId = data.soundId;
          this._soundDuration = data.soundDelay;
          this._soundAble = this._soundId > 0;
          this.node.setPosition(data.x, data.y);

          this._setBullet(data.bulletID);
        }

        reset() {
          this._shootPauseTime = 0;
          this._bulletCount = 0;
          this._bulletCreateTime = 0;
          this._bAttackOver = false;
          this.bulletTurn = false;
          this._attackOverCount = 0;
          this._attackOverTime = 0;
          this._soundAble = false;
          this._soundId = 0;
          this._soundTime = 0;
        }

        _refreshNextShoot() {
          this._shootPauseTime = 0;

          switch (this._atkType) {
            case 0:
              this._shootInterval = this._bapData.shootInterval[this._bIndex];
              this._bulletNum = this._bapData.bulletNums[this._bIndex];
              this._bulletInterval = this._bapData.bulletIntervals[this._bIndex];
              this._bulletAttack = this._bapData.bulletAttackRates[this._bIndex] * this._owner.attack;
              this._attackOverTime = this._bapData.attackOverDelay[this._bIndex];

              this._setBullet(this._bapData.bulletIDs[this._bIndex]);

              this._bulletCreateTime = this._bulletInterval;
              break;

            case 1:
              // this._waveDatas = WaveManager.getNorWaveDatas(this._bapData.waveIds[this._bIndex]);
              // this._waveTime = Array(this._waveDatas.length).fill(0);
              // this._wavedNum = Array(this._waveDatas.length).fill(0);
              // this._waveIndexOver = [];
              // this._waveDelayDuration = this._bapData.attackOverDelay[0];
              // this._waveDelay = 0;
              break;
          }
        }

        _setBullet(bulletID) {
          this._bulletID = bulletID;
          this.m_danmuConfig = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.getConfig(bulletID);
          this.m_screen = this.getScreenComp();
          this.m_screen.init(this);
          this.m_screen.setBulletState({
            attack: this._bulletAttack
          }, this._bulletEntity);
        }

        shoot() {
          this.m_screen.toFire(this._bulletCount);
        }

        async updateGameLogic(deltaTime) {
          if (!this.isAttackOver()) {
            switch (this._atkType) {
              case 0:
                this._updateShoot(deltaTime);

                break;

              case 1:
                await this._updateCreateEnemys(deltaTime);
                break;
            }
          }
        }

        _updateShoot(deltaTime) {
          this._bShootStart = false;
          this._shootPauseTime += deltaTime;

          if (this._shootPauseTime >= this._shootInterval) {
            if (this._bulletCount === 0) {
              this._bShootStart = true;
            }

            if (this._soundAble) {
              this._soundTime += deltaTime;

              if (this._soundTime >= this._soundDuration) {
                const soundKey = `e${this._soundId}`; // if (GameIns.enemyManager.getSoundAble(soundKey)) {
                //     FrameWork.audioManager.playEffect(soundKey);
                // }

                this._soundAble = false;
              }
            }

            if (this._bulletCount < this._bulletNum) {
              this._bulletCreateTime += deltaTime;

              if (this._bulletCreateTime >= this._bulletInterval) {
                this.shoot();
                this._bulletCreateTime -= this._bulletInterval;
                this._bulletCount++;
              }
            } else {
              this._attackOverCount += deltaTime;

              if (this._attackOverCount >= this._attackOverTime) {
                this._bIndex++;

                if (this._bIndex >= this._bNum) {
                  this.setAttackOver(true);
                } else {
                  this.reset();

                  this._refreshNextShoot();
                }
              }
            }
          }
        }

        async _updateCreateEnemys(deltaTime) {
          this._shootPauseTime += deltaTime;

          for (let i = 0; i < this._waveDatas.length; i++) {
            const waveData = this._waveDatas[i];

            if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).arrContain(this._waveIndexOver, i) && this._shootPauseTime >= waveData.groupInterval) {
              this._waveTime[i] += deltaTime;

              while (this._wavedNum[i] < waveData.enemyNum && this._waveTime[i] >= waveData.enemyInterval) {
                this._waveTime[i] = 0;
                const worldPos = this.node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                let enemy;

                if (waveData.enemyID >= 10000) {// const localPos = BattleLayer.me.enemyBulletLayer.convertToNodeSpaceAR(worldPos);
                  // enemy = GameIns.enemyManager.addMissile(waveData.enemyID);
                  // if (enemy) {
                  //     enemy.initTracks(waveData.trackGroups, localPos.x, localPos.y);
                  // }
                } else {
                  const localPos = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
                    error: Error()
                  }), BattleLayer) : BattleLayer).me.enemyPlaneLayer.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                  enemy = await (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).enemyManager.addPlane(waveData.enemyID);

                  if (enemy) {
                    enemy.initTrack(waveData.trackGroups, waveData.liveParam, localPos.x, localPos.y);
                    enemy.startBattle();
                  }
                }

                this._wavedNum[i]++;
              }

              if (this._wavedNum[i] === waveData.enemyNum) {
                this._waveIndexOver.push(i);
              }
            }
          }

          if (this._waveIndexOver.length >= this._waveDatas.length) {
            if (this._bIndex < this._bNum) {
              this._bIndex++;

              if (this._bIndex < this._bNum) {
                this._refreshNextShoot();
              }
            } else {
              this._waveDelay += deltaTime;

              if (this._waveDelay >= this._waveDelayDuration) {
                this.setAttackOver(true);
              }
            }
          }
        }

        getScreenComp() {
          switch (this.m_danmuConfig.bustyle) {
            case 1:
              return new (_crd && AimSingleLineScreen === void 0 ? (_reportPossibleCrUseOfAimSingleLineScreen({
                error: Error()
              }), AimSingleLineScreen) : AimSingleLineScreen)(this._bulletID, true);
            // 瞄准单线弹幕，子弹沿直线发射并瞄准目标

            case 3:
              return new (_crd && LoftScreen === void 0 ? (_reportPossibleCrUseOfLoftScreen({
                error: Error()
              }), LoftScreen) : LoftScreen)(this._bulletID, true);
            // 抛物线弹幕，子弹以抛物线轨迹发射

            case 11:
            case 51:
            case 52:
            case 56:
              return new (_crd && AimCircleScreen === void 0 ? (_reportPossibleCrUseOfAimCircleScreen({
                error: Error()
              }), AimCircleScreen) : AimCircleScreen)(this._bulletID, true);
            // 瞄准圆形弹幕，子弹以圆形方式发射并瞄准目标

            case 25:
              return new (_crd && CircleZoomScreen === void 0 ? (_reportPossibleCrUseOfCircleZoomScreen({
                error: Error()
              }), CircleZoomScreen) : CircleZoomScreen)(this._bulletID, true);
            // 圆形缩放弹幕，子弹以圆形方式发射并逐渐缩放

            default:
              return null;
            // 未匹配到任何弹幕类型时返回 null
          }
        }

        setAttackOver(isOver) {
          this._bAttackOver = isOver;
        }

        isAttackOver() {
          return this._bAttackOver;
        }

        getOwnEntity() {
          return this._owner;
        }

        getAtkAnims() {
          return this._bapData.atkAnim;
        }

        getAtkUnitId() {
          return this._bapData.atkUnitId;
        }

        getAttackPointAngle() {// return this.bulletTurn
          //     ? misc.radiansToDegrees(Vec2.UP.signAngle(this.node.position))
          //     : 0;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b302cfdf59b026fe4b62062b65f2eee5db871d6c.js.map