{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts"], "names": ["EnemyManager", "NodePool", "Prefab", "Sprite", "SpriteAtlas", "instantiate", "SingletonBase", "GameIns", "Tools", "TrackData", "EnemyPlaneData", "EnemyUIData", "GameFunc", "EnemyPlane", "GameEnum", "BattleLayer", "ResourceList", "MyApp", "GameResourceList", "loadFinish", "_loadFinish", "constructor", "_mainStage", "_subStage", "_uiDataMap", "Map", "_planeAnimMap", "_normalCount", "_loadTotal", "_loadCount", "_trackDatas", "_planeDataMap", "enemyAtlas", "_pfPlane", "_planePool", "_planeArr", "_willDeadPlane", "_animPlanePoolMap", "_mapEnemyPool", "missileAtlas", "initConfig", "tracks", "lubanTables", "TbTrack", "getDataList", "track", "trackData", "loadJson", "set", "trackID", "enemyUIDatas", "TbEnemyUI", "data", "uiData", "id", "planeList", "TbEnemy", "plane", "planeData", "preLoad", "stage", "enemyResources", "battleManager", "addLoadCount", "resMgr", "load", "loadAsync", "checkLoadFinish", "atlas_enemyBullet", "error", "atlas", "k", "isAm", "image", "prefab", "initBattle", "mainStage", "subStage", "console", "warn", "length", "poolSize", "i", "createNewPlane", "push", "uiId", "node", "name", "y", "getComponent", "getEnemyUIData", "preLoadUI", "get", "updateGameLogic", "deltaTime", "removeAble", "removePlaneForIndex", "isDead", "type", "EnemyType", "<PERSON><PERSON><PERSON>", "Ship", "splice", "mainReset", "subReset", "destroy", "Normal", "will<PERSON><PERSON><PERSON>", "sceneLayer", "pool", "getUIData", "put", "removeFromParent", "clear", "getPlaneRole", "isEnemyOver", "getNormalPlaneCount", "isEnemyAnim", "getPlaneData", "setPlaneFrame", "sprite", "frameName", "spriteFrame", "getSpriteFrame", "sizeMode", "SizeMode", "RAW", "getPlaneFrame", "getTrackDataForID", "trackId", "planes", "removeAllAlivePlane", "pushPlane", "arrC<PERSON>ain", "index", "_willRemovePlane", "x", "addPlane", "pop", "me", "addEnemy", "init", "new_uuid", "uuid", "setScaleType", "getEnemyRangeType", "checkEnemyDieBomb", "position", "addShield", "removeShield", "shield"], "mappings": ";;;8RAiBaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBOC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAmBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAgCC,MAAAA,W,OAAAA,W;;AAChFC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AAEAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,W,iBAAAA,W;;AAChBC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,U;;AACAC,MAAAA,Q;;AACAC,MAAAA,W;;AACAC,MAAAA,Y;;AAEEC,MAAAA,K,kBAAAA,K;;AACFC,MAAAA,gB;;;;;;;;;8BAGMlB,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAoB1D;AACJ;AACA;AACyB,YAAVmB,UAAU,GAAY;AAC7B,iBAAO,KAAKC,WAAZ;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV;AADU,eAzBdD,WAyBc,GAzBA,KAyBA;AAAA,eAxBdE,UAwBc,GAxBD,CAAC,CAwBA;AAAA,eAvBdC,SAuBc,GAvBF,CAAC,CAuBC;AAAA,eAtBdC,UAsBc,GAtBD,IAAIC,GAAJ,EAsBC;AAAA,eArBdC,aAqBc,GArBE,IAAID,GAAJ,EAqBF;AAAA,eApBdE,YAoBc,GApBC,CAoBD;AAAA,eAnBdC,UAmBc,GAnBD,CAmBC;AAAA,eAlBdC,UAkBc,GAlBD,CAkBC;AAAA,eAjBdC,WAiBc,GAjBwB,IAAIL,GAAJ,EAiBxB;AAAA,eAhBdM,aAgBc,GAhBE,IAAIN,GAAJ,EAgBF;AAAA,eAfdO,UAec,GAfD,IAeC;AAAA,eAddC,QAcc,GAdH,IAcG;AAAA,eAbdC,UAac,GAbO,EAaP;AAAA,eAZdC,SAYc,GAZF,EAYE;AAAA,eAXdC,cAWc,GAXG,EAWH;AAAA,eAVdC,iBAUc,GAVM,IAAIZ,GAAJ,EAUN;AAAA,eATda,aASc,GATE,IAAIrC,QAAJ,EASF;AAAA,eARdsC,YAQc,GARC,IAQD;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT,cAAIC,MAAM,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,WAA1B,EAAb;;AACA,eAAK,IAAIC,KAAT,IAAkBJ,MAAlB,EAA0B;AACtB,kBAAMK,SAAS,GAAG;AAAA;AAAA,yCAAlB;AACAA,YAAAA,SAAS,CAACC,QAAV,CAAmBF,KAAnB;;AACA,iBAAKf,WAAL,CAAiBkB,GAAjB,CAAqBF,SAAS,CAACG,OAA/B,EAAwCH,SAAxC;AACH;;AAGD,cAAII,YAAY,GAAG;AAAA;AAAA,8BAAMR,WAAN,CAAkBS,SAAlB,CAA4BP,WAA5B,EAAnB;;AACA,eAAK,IAAIQ,IAAT,IAAiBF,YAAjB,EAA+B;AAC3B,kBAAMG,MAAM,GAAG;AAAA;AAAA,6CAAf;AACAA,YAAAA,MAAM,CAACN,QAAP,CAAgBK,IAAhB;;AACA,iBAAK5B,UAAL,CAAgBwB,GAAhB,CAAoBK,MAAM,CAACC,EAA3B,EAA+BD,MAA/B;AACH;;AAED,cAAIE,SAAS,GAAG;AAAA;AAAA,8BAAMb,WAAN,CAAkBc,OAAlB,CAA0BZ,WAA1B,EAAhB;;AACA,eAAK,IAAIa,KAAT,IAAkBF,SAAlB,EAA6B;AACzB,kBAAMG,SAAS,GAAG;AAAA;AAAA,mDAAlB;AACAA,YAAAA,SAAS,CAACX,QAAV,CAAmBU,KAAnB;;AACA,iBAAK1B,aAAL,CAAmBiB,GAAnB,CAAuBU,SAAS,CAACJ,EAAjC,EAAqCI,SAArC;AACH;AACJ;AAID;AACJ;AACA;AACA;;;AACWC,QAAAA,OAAO,CAACC,KAAD,EAAgB;AAE1B;AACA,gBAAMC,cAAc,GAAG,CACnB;AAAA;AAAA,oDAAiBhD,UADE,CAAvB;AAKA;AAAA;AAAA,kCAAQiD,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkBJ,cAAlB,EAAkC3D,MAAlC,EAA0C,YAAY;AAClD,iBAAK+B,QAAL,GAAgB,MAAM;AAAA;AAAA,gCAAM+B,MAAN,CAAaE,SAAb,CAAuB;AAAA;AAAA,sDAAiBrD,UAAxC,EAAoDX,MAApD,CAAtB;AACA;AAAA;AAAA,oCAAQ4D,aAAR,CAAsBK,eAAtB;AACH,WAHD,EAT0B,CAc1B;;AACA;AAAA;AAAA,kCAAQL,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,oDAAiBG,iBAAnC,EAAqDhE,WAArD,EAAkE,CAACiE,KAAD,EAAOC,KAAP,KAAiB;AAC/E,iBAAK/B,YAAL,GAAoB+B,KAApB;AACA;AAAA;AAAA,oCAAQR,aAAR,CAAsBK,eAAtB;AACH,WAHD,EAhB0B,CAuB1B;;AACA;AAAA;AAAA,kCAAQL,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,4CAAc,sBAAqBL,KAAM,EAAzC,CAAlB,EAA8DxD,WAA9D,EAA2E,CAACiE,KAAD,EAAOC,KAAP,KAAiB;AACxF,iBAAKtC,UAAL,GAAkBsC,KAAlB;AACA;AAAA;AAAA,oCAAQR,aAAR,CAAsBK,eAAtB;AACH,WAHD;;AAKA,eAAK,IAAII,CAAT,IAAc,KAAK/C,UAAnB,EAA+B;AAC3B,gBAAI6B,MAAM,GAAG,KAAK7B,UAAL,CAAgB+C,CAAhB,CAAb;;AACA,gBAAIlB,MAAM,CAACmB,IAAX,EAAiB;AACb;AAAA;AAAA,sCAAQV,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,kCAAMC,MAAN,CAAaC,IAAb,CAAkB,UAAQZ,MAAM,CAACoB,KAAjC,EAAwCvE,MAAxC,EAAgD,CAACmE,KAAD,EAAOK,MAAP,KAAkB;AAC9D,qBAAKhD,aAAL,CAAmBsB,GAAnB,CAAuBK,MAAM,CAACoB,KAA9B,EAAqCC,MAArC;;AACA;AAAA;AAAA,wCAAQZ,aAAR,CAAsBK,eAAtB;AACH,eAHD;AAIH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIQ,QAAAA,UAAU,CAACC,SAAD,EAAoBC,QAApB,EAAsC;AAC5CC,UAAAA,OAAO,CAACC,IAAR,CAAa,OAAb,EAAsBH,SAAtB,EAAiCC,QAAjC;;AACA,cAAI,KAAKvD,UAAL,KAAoBsD,SAAxB,EAAmC;AAC/B,iBAAKtD,UAAL,GAAkBsD,SAAlB;AACA,iBAAKrD,SAAL,GAAiBsD,QAAjB;;AAEA,gBAAI,KAAK3C,UAAL,CAAgB8C,MAAhB,KAA2B,CAA/B,EAAkC;AAC9B,oBAAMC,QAAQ,GAAG,EAAjB;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,QAApB,EAA8BC,CAAC,EAA/B,EAAmC;AAC/B,sBAAMzB,KAAK,GAAG,KAAK0B,cAAL,EAAd;;AACA,qBAAKjD,UAAL,CAAgBkD,IAAhB,CAAqB3B,KAArB;AACH;AACJ;AACJ,WAXD,MAWO;AACH,iBAAKlC,SAAL,GAAiBsD,QAAjB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,cAAc,CAACE,IAAY,GAAG,CAAhB,EAAyB;AACnC,gBAAMC,IAAI,GAAGjF,WAAW,CAAC,KAAK4B,QAAN,CAAxB;AACAqD,UAAAA,IAAI,CAACC,IAAL,GAAY,OAAZ;AACAD,UAAAA,IAAI,CAACE,CAAL,GAAS,IAAT;AAEA,gBAAM/B,KAAgB,GAAG6B,IAAI,CAACG,YAAL;AAAA;AAAA,uCAAzB;AACAhC,UAAAA,KAAK,CAACE,OAAN;;AAEA,cAAI0B,IAAI,GAAG,CAAX,EAAc;AACV,kBAAMhC,MAAM,GAAG,KAAKqC,cAAL,CAAoBL,IAApB,CAAf;AACA5B,YAAAA,KAAK,CAACkC,SAAN,CAAgBtC,MAAhB;AACAiC,YAAAA,IAAI,CAACE,CAAL,GAAS,IAAT;AACH,WAJD,MAIO;AACH/B,YAAAA,KAAK,CAACkC,SAAN,CAAgB,IAAhB;AACH;;AAED,iBAAOL,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,cAAc,CAACpC,EAAD,EAA0B;AACpC,iBAAO,KAAK9B,UAAL,CAAgBoE,GAAhB,CAAoBtC,EAApB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIuC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/C,SAAL,CAAe6C,MAAnC,EAA2CE,CAAC,EAA5C,EAAgD;AAC5C,kBAAMzB,KAAK,GAAG,KAAKtB,SAAL,CAAe+C,CAAf,CAAd;;AACA,gBAAIzB,KAAK,CAACsC,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBd,CAAzB;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACH,kBAAIzB,KAAK,CAACwC,MAAV,EAAkB;AACd,oBAAIxC,KAAK,CAACyC,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBC,MAAlC,IAA4C3C,KAAK,CAACyC,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBE,IAAlF,EAAwF;AACpF,uBAAKjE,cAAL,CAAoBgD,IAApB,CAAyB3B,KAAzB;;AACA,uBAAKtB,SAAL,CAAemE,MAAf,CAAsBpB,CAAtB,EAAyB,CAAzB;;AACAA,kBAAAA,CAAC;AACD;AACH;AACJ;;AACDzB,cAAAA,KAAK,CAACoC,eAAN,CAAsBC,SAAtB;AACH;AACJ;;AAED,eAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9C,cAAL,CAAoB4C,MAAxC,EAAgDE,CAAC,EAAjD,EAAqD;AACjD,kBAAMzB,KAAK,GAAG,KAAKrB,cAAL,CAAoB8C,CAApB,CAAd;;AACA,gBAAIzB,KAAK,CAACsC,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBd,CAAzB,EAA4B,IAA5B;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHzB,cAAAA,KAAK,CAACoC,eAAN,CAAsBC,SAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIS,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL;AAEA,eAAKlF,UAAL,GAAkB,CAAC,CAAnB;AACA,eAAKC,SAAL,GAAiB,CAAC,CAAlB,CAJQ,CAMR;;AACA,eAAK,MAAMkC,KAAX,IAAoB,KAAKvB,UAAzB,EAAqC;AACjCuB,YAAAA,KAAK,CAACgD,OAAN;AACH;;AACD,eAAKvE,UAAL,CAAgBoE,MAAhB,CAAuB,CAAvB,EAVQ,CAaR;;;AACA,eAAK,MAAM7C,KAAX,IAAoB,KAAKrB,cAAzB,EAAyC;AACrC,gBAAIqB,KAAK,IAAIA,KAAK,CAAC6B,IAAnB,EAAyB;AACrB7B,cAAAA,KAAK,CAAC6B,IAAN,CAAWmB,OAAX;AACH;AACJ;;AACD,eAAKrE,cAAL,GAAsB,EAAtB,CAnBQ,CAsBR;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIoE,QAAAA,QAAQ,GAAG;AACP,cAAIL,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,eAAK,MAAM1C,KAAX,IAAoB,KAAKtB,SAAzB,EAAoC;AAChC,oBAAQsB,KAAK,CAACyC,IAAd;AACI,mBAAKC,SAAS,CAACO,MAAf;AACIjD,gBAAAA,KAAK,CAACkD,UAAN;;AACA,oBAAIlD,KAAK,CAACmD,UAAN,GAAmB,CAAvB,EAA0B;AAAA;;AACtBnD,kBAAAA,KAAK,CAAC6B,IAAN,CAAWE,CAAX,GAAe,IAAf;AACA,wBAAMqB,IAAI,GAAG,oBAAApD,KAAK,CAACqD,SAAN,gCAAmBtC,IAAnB,GACP,KAAKnC,iBAAL,CAAuBuD,GAAvB,CAA2BnC,KAAK,CAACqD,SAAN,GAAkBxD,EAA7C,KAAoD,EAD7C,GAEP,KAAKpB,UAFX;AAGA2E,kBAAAA,IAAI,CAACzB,IAAL,CAAU3B,KAAK,CAAC6B,IAAhB;AACH,iBAND,MAMO;AACH,uBAAKhD,aAAL,CAAmByE,GAAnB,CAAuBtD,KAAK,CAAC6B,IAA7B;AACH;;AACD;AAZR;;AAcA7B,YAAAA,KAAK,CAAC6B,IAAN,CAAW0B,gBAAX,CAA4B,KAA5B;AACH;;AACD,eAAK7E,SAAL,CAAemE,MAAf,CAAsB,CAAtB;AACH;AAGD;AACJ;AACA;;;AACIW,QAAAA,KAAK,GAAG,CAEP;AACD;AACJ;AACA;AACA;;;AACsB,cAAZC,YAAY,CAAC7B,IAAD,EAAgC;AAC9C,cAAIX,MAAM,GAAG,KAAKhD,aAAL,CAAmBkE,GAAnB,CAAuBP,IAAvB,CAAb;;AACA,cAAI,CAACX,MAAL,EAAa;AACTA,YAAAA,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMV,MAAN,CAAaE,SAAb,CAAuB,kBAAgBmB,IAAvC,EAA6CnF,MAA7C,CAAf;;AACA,iBAAKwB,aAAL,CAAmBsB,GAAnB,CAAuBqC,IAAvB,EAA6BX,MAA7B;AACH;;AACD,iBAAOA,MAAP;AACH;AAED;AACJ;AACA;;;AACIyC,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKhF,SAAL,CAAe6C,MAAf,KAA0B,CAAjC;AACH;AAED;AACJ;AACA;;;AACIoC,QAAAA,mBAAmB,GAAW;AAC1B,iBAAO,KAAKzF,YAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACI0F,QAAAA,WAAW,CAAC/D,EAAD,EAAsB;AAC7B,gBAAMI,SAAS,GAAG,KAAK4D,YAAL,CAAkBhE,EAAlB,CAAlB;AACA,gBAAMD,MAAM,GAAG,KAAKqC,cAAL,CAAoBhC,SAAS,CAAC2B,IAA9B,CAAf;AACA,iBAAO,CAAAhC,MAAM,QAAN,YAAAA,MAAM,CAAEmB,IAAR,KAAgB,KAAvB;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACI+C,QAAAA,aAAa,CAACC,MAAD,EAAiBC,SAAjB,EAAoC;AAC7C,cAAID,MAAM,IAAIC,SAAS,KAAK,EAAxB,IAA8B,KAAKzF,UAAvC,EAAmD;AAC/CwF,YAAAA,MAAM,CAACE,WAAP,GAAqB,KAAK1F,UAAL,CAAgB2F,cAAhB,CAA+BF,SAA/B,CAArB;AACAD,YAAAA,MAAM,CAACI,QAAP,GAAkBzH,MAAM,CAAC0H,QAAP,CAAgBC,GAAlC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,aAAa,CAACN,SAAD,EAAwC;AACjD,iBAAO,KAAKzF,UAAL,GAAkB,KAAKA,UAAL,CAAgB2F,cAAhB,CAA+BF,SAA/B,CAAlB,GAA8D,IAArE;AACH;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,iBAAiB,CAACC,OAAD,EAAoC;AACjD,cAAInF,SAA2B,GAAG,IAAlC;;AACA,cAAI;AACAA,YAAAA,SAAS,GAAG,KAAKhB,WAAL,CAAiB8D,GAAjB,CAAqBqC,OAArB,KAAiC,IAA7C;AACH,WAFD,CAEE,OAAO5D,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAa,uBAAsB4D,OAAQ,EAA3C,EAA8C5D,KAA9C;AACH;;AACD,iBAAOvB,SAAP;AACH;AAED;AACJ;AACA;;;AACc,YAANoF,MAAM,GAAiB;AACvB,iBAAO,KAAK/F,SAAZ;AACH;AAED;AACJ;AACA;;;AACIgG,QAAAA,mBAAmB,GAAG;AAClB,eAAK,MAAM1E,KAAX,IAAoB,KAAKtB,SAAzB,EAAoC;AAChC,gBAAI,CAACsB,KAAK,CAACwC,MAAX,EAAmB;AACfxC,cAAAA,KAAK,CAACkD,UAAN;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,SAAS,CAAC3E,KAAD,EAAoB;AACzB,cAAI,CAAC;AAAA;AAAA,8BAAM4E,UAAN,CAAiB,KAAKlG,SAAtB,EAAiCsB,KAAjC,CAAL,EAA8C;AAC1C,iBAAKtB,SAAL,CAAeiD,IAAf,CAAoB3B,KAApB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIuC,QAAAA,mBAAmB,CAACsC,KAAD,EAAgBrC,MAAe,GAAG,KAAlC,EAAyC;AACxD,cAAIA,MAAJ,EAAY;AACR,iBAAKsC,gBAAL,CAAsB,KAAKnG,cAAL,CAAoBkG,KAApB,CAAtB;;AACA,iBAAKlG,cAAL,CAAoBkE,MAApB,CAA2BgC,KAA3B,EAAkC,CAAlC;AACH,WAHD,MAGO;AACH,iBAAKC,gBAAL,CAAsB,KAAKpG,SAAL,CAAemG,KAAf,CAAtB;;AACA,iBAAKnG,SAAL,CAAemE,MAAf,CAAsBgC,KAAtB,EAA6B,CAA7B;AACH;AACJ,SAnXyD,CAqX1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAAC9E,KAAD,EAAoB;AAChC,cAAI0C,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,kBAAQ1C,KAAK,CAACyC,IAAd;AACI,iBAAKC,SAAS,CAACO,MAAf;AACI,kBAAIjD,KAAK,CAACmD,UAAN,GAAmB,CAAvB,EAA0B;AAAA;;AACtBnD,gBAAAA,KAAK,CAAC6B,IAAN,CAAWE,CAAX,GAAe,IAAf;AACA,qBAAK7D,YAAL;AACA,sBAAMkF,IAAI,GAAG,qBAAApD,KAAK,CAACqD,SAAN,iCAAmBtC,IAAnB,GACP,KAAKnC,iBAAL,CAAuBuD,GAAvB,CAA2BnC,KAAK,CAACqD,SAAN,GAAkBxD,EAA7C,KAAoD,EAD7C,GAEP,KAAKpB,UAFX;AAGA2E,gBAAAA,IAAI,CAACzB,IAAL,CAAU3B,KAAK,CAAC6B,IAAhB;AACH,eAPD,MAOO;AACH7B,gBAAAA,KAAK,CAAC6B,IAAN,CAAWkD,CAAX,GAAe,IAAf;;AACA,qBAAKlG,aAAL,CAAmByE,GAAnB,CAAuBtD,KAAK,CAAC6B,IAA7B;AACH;;AACD;AAbR;;AAeA7B,UAAAA,KAAK,CAAC6B,IAAN,CAAW0B,gBAAX;AACH;AAED;AACJ;AACA;AACA;;;AACIM,QAAAA,YAAY,CAAChE,EAAD,EAA6B;AACrC,gBAAMF,IAAI,GAAG,KAAKrB,aAAL,CAAmB6D,GAAnB,CAAuBtC,EAAvB,CAAb;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AAAA;AAAA,gCAAMiB,KAAN,CAAa,uBAAsBf,EAAG,EAAtC;AACH;;AACD,iBAAOF,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACkB,cAARqF,QAAQ,CAACnF,EAAD,EAAa;AACvB,cAAI;AACA,kBAAMI,SAAS,GAAG,KAAK4D,YAAL,CAAkBhE,EAAlB,CAAlB;AACA,gBAAIgC,IAAU,GAAG,IAAjB;;AAEA,gBAAI,KAAK+B,WAAL,CAAiB/D,EAAjB,CAAJ,EAA0B;AACtB,kBAAIuD,IAAI,GAAG,KAAKxE,iBAAL,CAAuBuD,GAAvB,CAA2BlC,SAAS,CAAC2B,IAArC,CAAX;;AACA,kBAAI,CAACwB,IAAL,EAAW;AACPA,gBAAAA,IAAI,GAAG,EAAP;;AACA,qBAAKxE,iBAAL,CAAuBW,GAAvB,CAA2BU,SAAS,CAAC2B,IAArC,EAA2CwB,IAA3C;AACH;;AACDvB,cAAAA,IAAI,GAAGuB,IAAI,CAAC6B,GAAL,MAAc,KAAKvD,cAAL,CAAoBzB,SAAS,CAAC2B,IAA9B,CAArB;AACH,aAPD,MAOO;AACHC,cAAAA,IAAI,GAAG,KAAKpD,UAAL,CAAgBwG,GAAhB,MAAyB,KAAKvD,cAAL,EAAhC;AACH;;AAED;AAAA;AAAA,4CAAYwD,EAAZ,CAAeC,QAAf,CAAwBtD,IAAxB;AACA,kBAAM7B,KAAK,GAAG6B,IAAI,CAACG,YAAL;AAAA;AAAA,yCAAd;AACA,kBAAMhC,KAAK,CAACoF,IAAN,CAAWnF,SAAX,CAAN;AACAD,YAAAA,KAAK,CAACqF,QAAN,GAAiB;AAAA;AAAA,sCAASC,IAA1B;AACAtF,YAAAA,KAAK,CAACuF,YAAN,CAAmB,KAAKC,iBAAL,CAAuB3F,EAAvB,CAAnB;AACA,iBAAK8E,SAAL,CAAe3E,KAAf,EApBA,CAsBA;AACA;AACA;AACA;;AAEA,iBAAK9B,YAAL;AACA,mBAAO8B,KAAP;AACH,WA7BD,CA6BE,OAAOY,KAAP,EAAc;AACZ,mBAAO,IAAP;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACI6E,QAAAA,iBAAiB,CAACC,QAAD,EAAiB,CAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIF,QAAAA,iBAAiB,CAAC3F,EAAD,EAAqB;AAClC,cAAKA,EAAE,IAAI,IAAN,IAAcA,EAAE,IAAI,IAArB,IAA+BA,EAAE,IAAI,IAAN,IAAcA,EAAE,IAAI,IAAvD,EAA8D;AAC1D,mBAAO,CAAP,CAD0D,CAChD;AACb,WAFD,MAEO,IAAKA,EAAE,IAAI,IAAN,IAAcA,EAAE,IAAI,IAArB,IAA+BA,EAAE,IAAI,IAAN,IAAcA,EAAE,IAAI,IAAvD,EAA8D;AACjE,mBAAO,CAAP,CADiE,CACvD;AACb,WAFM,MAEA,IAAIA,EAAE,IAAI,IAAN,IAAcA,EAAE,IAAI,IAAxB,EAA8B;AACjC,mBAAO,CAAP,CADiC,CACvB;AACb;;AACD,iBAAO,CAAC,CAAR,CARkC,CAQvB;AACd;AAED;AACJ;AACA;;;AACI8F,QAAAA,SAAS,GAAG,CACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,MAAD,EAAS,CACjB;AACA;AACA;AACH,SAngByD,CAqgB1D;AACA;AACA;AACA;AACA;AACA;;;AA1gB0D,O", "sourcesContent": ["import { JsonAsset, NodePool, Prefab, resources, Sprite, SpriteAtlas, SpriteFrame, Node, instantiate, Vec2, Vec3 } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { TrackData } from \"../data/TrackData\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyPlaneData, EnemyUIData } from \"../data/EnemyData\";\r\nimport { GameFunc } from \"../GameFunc\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport ResourceList from \"../const/GameResourceList\";\r\nimport { TrackGroup } from \"../data/EnemyWave\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\n\r\n\r\nexport class EnemyManager extends SingletonBase<EnemyManager> {\r\n\r\n    _loadFinish = false;\r\n    _mainStage = -1;\r\n    _subStage = -1;\r\n    _uiDataMap = new Map();\r\n    _planeAnimMap = new Map<string, Prefab>();\r\n    _normalCount = 0;\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n    _trackDatas: Map<number, TrackData> = new Map();\r\n    _planeDataMap = new Map();\r\n    enemyAtlas = null;\r\n    _pfPlane = null;\r\n    _planePool: Node[] = [];\r\n    _planeArr = [];\r\n    _willDeadPlane = [];\r\n    _animPlanePoolMap = new Map();\r\n    _mapEnemyPool = new NodePool();\r\n    missileAtlas = null;\r\n    /**\r\n     * 是否加载完成\r\n     */\r\n    public get loadFinish(): boolean {\r\n        return this._loadFinish;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig() {\r\n        let tracks = MyApp.lubanTables.TbTrack.getDataList();\r\n        for (let track of tracks) {\r\n            const trackData = new TrackData();\r\n            trackData.loadJson(track);\r\n            this._trackDatas.set(trackData.trackID, trackData);\r\n        }\r\n\r\n\r\n        let enemyUIDatas = MyApp.lubanTables.TbEnemyUI.getDataList();\r\n        for (let data of enemyUIDatas) {\r\n            const uiData = new EnemyUIData();\r\n            uiData.loadJson(data);\r\n            this._uiDataMap.set(uiData.id, uiData);\r\n        }\r\n\r\n        let planeList = MyApp.lubanTables.TbEnemy.getDataList();\r\n        for (let plane of planeList) {\r\n            const planeData = new EnemyPlaneData();\r\n            planeData.loadJson(plane);\r\n            this._planeDataMap.set(planeData.id, planeData);\r\n        }\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 预加载资源\r\n     * @param stage 当前关卡\r\n     */\r\n    public preLoad(stage: number) {\r\n\r\n        // 加载敌机资源\r\n        const enemyResources = [\r\n            GameResourceList.EnemyPlane\r\n        ];\r\n\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(enemyResources, Prefab, async () => {\r\n            this._pfPlane = await MyApp.resMgr.loadAsync(GameResourceList.EnemyPlane, Prefab);\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n\r\n        // 加载子弹图集\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.atlas_enemyBullet,SpriteAtlas, (error,atlas) => {\r\n            this.missileAtlas = atlas;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n\r\n\r\n\r\n        // 加载敌机图集\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(ResourceList[`atlas_package_enemy${stage}`],SpriteAtlas, (error,atlas) => {\r\n            this.enemyAtlas = atlas;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n\r\n        for (let k in this._uiDataMap) {\r\n            let uiData = this._uiDataMap[k];\r\n            if (uiData.isAm) {\r\n                GameIns.battleManager.addLoadCount(1);\r\n                MyApp.resMgr.load(\"Game/\"+uiData.image, Prefab, (error,prefab) => {\r\n                    this._planeAnimMap.set(uiData.image, prefab);\r\n                    GameIns.battleManager.checkLoadFinish();\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化敌人管理器\r\n     * @param mainStage 主关卡\r\n     * @param subStage 子关卡\r\n     */\r\n    initBattle(mainStage: number, subStage: number) {\r\n        console.warn(\"stage\", mainStage, subStage);\r\n        if (this._mainStage !== mainStage) {\r\n            this._mainStage = mainStage;\r\n            this._subStage = subStage;\r\n\r\n            if (this._planePool.length === 0) {\r\n                const poolSize = 20;\r\n                for (let i = 0; i < poolSize; i++) {\r\n                    const plane = this.createNewPlane();\r\n                    this._planePool.push(plane);\r\n                }\r\n            }\r\n        } else {\r\n            this._subStage = subStage;\r\n        }\r\n    }\r\n\r\n    /**\r\n * 创建新的敌机节点\r\n * @param uiId 敌机 UI ID\r\n */\r\n    createNewPlane(uiId: number = 0): Node {\r\n        const node = instantiate(this._pfPlane);\r\n        node.name = \"plane\";\r\n        node.y = 1000;\r\n\r\n        const plane:EnemyPlane = node.getComponent(EnemyPlane);\r\n        plane.preLoad();\r\n\r\n        if (uiId > 0) {\r\n            const uiData = this.getEnemyUIData(uiId);\r\n            plane.preLoadUI(uiData);\r\n            node.y = 1000;\r\n        } else {\r\n            plane.preLoadUI(null);\r\n        }\r\n\r\n        return node;\r\n    }\r\n\r\n    /**\r\n     * 获取敌机 UI 数据\r\n     * @param id 敌机 UI ID\r\n     */\r\n    getEnemyUIData(id: number): EnemyUIData {\r\n        return this._uiDataMap.get(id);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._planeArr.length; i++) {\r\n            const plane = this._planeArr[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i);\r\n                i--;\r\n            } else {\r\n                if (plane.isDead) {\r\n                    if (plane.type === GameEnum.EnemyType.Turret || plane.type === GameEnum.EnemyType.Ship) {\r\n                        this._willDeadPlane.push(plane);\r\n                        this._planeArr.splice(i, 1);\r\n                        i--;\r\n                        continue;\r\n                    }\r\n                }\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._willDeadPlane.length; i++) {\r\n            const plane = this._willDeadPlane[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i, true);\r\n                i--;\r\n            } else {\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n* 重置主关卡\r\n*/\r\n    mainReset() {\r\n        this.subReset();\r\n\r\n        this._mainStage = -1;\r\n        this._subStage = -1;\r\n\r\n        // 清理飞机池\r\n        for (const plane of this._planePool) {\r\n            plane.destroy();\r\n        }\r\n        this._planePool.splice(0);\r\n\r\n\r\n        // 清理即将死亡的飞机\r\n        for (const plane of this._willDeadPlane) {\r\n            if (plane && plane.node) {\r\n                plane.node.destroy();\r\n            }\r\n        }\r\n        this._willDeadPlane = [];\r\n\r\n\r\n        // // 清理动画飞机池\r\n        // this._animPlanePoolMap.forEach((pool) => {\r\n        //     for (const node of pool) {\r\n        //         node.destroy();\r\n        //     }\r\n        // });\r\n        // this._animPlanePoolMap.clear();\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        for (const plane of this._planeArr) {\r\n            switch (plane.type) {\r\n                case EnemyType.Normal:\r\n                    plane.willRemove();\r\n                    if (plane.sceneLayer < 0) {\r\n                        plane.node.y = 1000;\r\n                        const pool = plane.getUIData()?.isAm\r\n                            ? this._animPlanePoolMap.get(plane.getUIData().id) || []\r\n                            : this._planePool;\r\n                        pool.push(plane.node);\r\n                    } else {\r\n                        this._mapEnemyPool.put(plane.node);\r\n                    }\r\n                    break;\r\n            }\r\n            plane.node.removeFromParent(false);\r\n        }\r\n        this._planeArr.splice(0);\r\n    }\r\n\r\n\r\n    /**\r\n     * 清理敌人管理器\r\n     */\r\n    clear() {\r\n\r\n    }\r\n    /**\r\n     * 获取敌机角色\r\n     * @param uiId 敌机 UI ID\r\n     */\r\n    async getPlaneRole(uiId: string): Promise<Prefab> {\r\n        let prefab = this._planeAnimMap.get(uiId);\r\n        if (!prefab) {\r\n            prefab = await MyApp.resMgr.loadAsync(\"Game/prefabs/\"+uiId, Prefab);\r\n            this._planeAnimMap.set(uiId, prefab);\r\n        }\r\n        return prefab;\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否全部消灭\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._planeArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 获取普通敌机数量\r\n     */\r\n    getNormalPlaneCount(): number {\r\n        return this._normalCount;\r\n    }\r\n\r\n    /**\r\n     * 检查敌机是否为动画类型\r\n     * @param id 敌机 ID\r\n     */\r\n    isEnemyAnim(id: number): boolean {\r\n        const planeData = this.getPlaneData(id);\r\n        const uiData = this.getEnemyUIData(planeData.uiId);\r\n        return uiData?.isAm || false;\r\n    }\r\n\r\n\r\n    /**\r\n     * 设置敌机的精灵帧\r\n     * @param sprite 精灵组件\r\n     * @param frameName 精灵帧名称\r\n     */\r\n    setPlaneFrame(sprite: Sprite, frameName: string) {\r\n        if (sprite && frameName !== \"\" && this.enemyAtlas) {\r\n            sprite.spriteFrame = this.enemyAtlas.getSpriteFrame(frameName);\r\n            sprite.sizeMode = Sprite.SizeMode.RAW;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取敌机的精灵帧\r\n     * @param frameName 精灵帧名称\r\n     */\r\n    getPlaneFrame(frameName: string): SpriteFrame | null {\r\n        return this.enemyAtlas ? this.enemyAtlas.getSpriteFrame(frameName) : null;\r\n    }\r\n\r\n    /**\r\n     * 根据轨迹 ID 获取轨迹数据\r\n     * @param trackId 轨迹 ID\r\n     */\r\n    getTrackDataForID(trackId: number): TrackData | null {\r\n        let trackData: TrackData | null = null;\r\n        try {\r\n            trackData = this._trackDatas.get(trackId) || null;\r\n        } catch (error) {\r\n            Tools.error(`getTrackData error: ${trackId}`, error);\r\n        }\r\n        return trackData;\r\n    }\r\n\r\n    /**\r\n     * 获取所有敌机\r\n     */\r\n    get planes(): EnemyPlane[] {\r\n        return this._planeArr;\r\n    }\r\n\r\n    /**\r\n     * 移除所有存活的敌机\r\n     */\r\n    removeAllAlivePlane() {\r\n        for (const plane of this._planeArr) {\r\n            if (!plane.isDead) {\r\n                plane.willRemove();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加敌机到管理器\r\n     * @param plane 敌机对象\r\n     */\r\n    pushPlane(plane: EnemyPlane) {\r\n        if (!Tools.arrContain(this._planeArr, plane)) {\r\n            this._planeArr.push(plane);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 根据索引移除敌机\r\n     * @param index 索引\r\n     * @param isDead 是否为死亡敌机\r\n     */\r\n    removePlaneForIndex(index: number, isDead: boolean = false) {\r\n        if (isDead) {\r\n            this._willRemovePlane(this._willDeadPlane[index]);\r\n            this._willDeadPlane.splice(index, 1);\r\n        } else {\r\n            this._willRemovePlane(this._planeArr[index]);\r\n            this._planeArr.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    //     /**\r\n    //      * 移除指定敌机\r\n    //      * @param plane 敌机对象\r\n    //      * @param isDead 是否为死亡敌机\r\n    //      */\r\n    //     removePlane(plane: EnemyPlane, isDead: boolean = false) {\r\n    //         this._willRemovePlane(plane);\r\n    //         if (isDead) {\r\n    //             Tools.arrRemove(this._willDeadPlane, plane);\r\n    //         } else {\r\n    //             Tools.arrRemove(this._planeArr, plane);\r\n    //         }\r\n    //     }\r\n\r\n    /**\r\n     * 处理即将移除的敌机\r\n     * @param plane 敌机对象\r\n     */\r\n    _willRemovePlane(plane: EnemyPlane) {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        switch (plane.type) {\r\n            case EnemyType.Normal:\r\n                if (plane.sceneLayer < 0) {\r\n                    plane.node.y = 1000;\r\n                    this._normalCount--;\r\n                    const pool = plane.getUIData()?.isAm\r\n                        ? this._animPlanePoolMap.get(plane.getUIData().id) || []\r\n                        : this._planePool;\r\n                    pool.push(plane.node);\r\n                } else {\r\n                    plane.node.x = 1000;\r\n                    this._mapEnemyPool.put(plane.node);\r\n                }\r\n                break;\r\n        }\r\n        plane.node.removeFromParent();\r\n    }\r\n\r\n    /**\r\n     * 根据敌机 ID 获取敌机数据\r\n     * @param id 敌机 ID\r\n     */\r\n    getPlaneData(id: number): EnemyPlaneData {\r\n        const data = this._planeDataMap.get(id);\r\n        if (!data) {\r\n            Tools.error(`getEnemyData error: ${id}`);\r\n        }\r\n        return data!;\r\n    }\r\n\r\n    /**\r\n     * 添加敌机\r\n     * @param id 敌机 ID\r\n     */\r\n    async addPlane(id: number) {\r\n        try {\r\n            const planeData = this.getPlaneData(id);\r\n            let node: Node = null;\r\n\r\n            if (this.isEnemyAnim(id)) {\r\n                let pool = this._animPlanePoolMap.get(planeData.uiId);\r\n                if (!pool) {\r\n                    pool = [];\r\n                    this._animPlanePoolMap.set(planeData.uiId, pool);\r\n                }\r\n                node = pool.pop() || this.createNewPlane(planeData.uiId);\r\n            } else {\r\n                node = this._planePool.pop() || this.createNewPlane();\r\n            }\r\n\r\n            BattleLayer.me.addEnemy(node);\r\n            const plane = node.getComponent(EnemyPlane);\r\n            await plane.init(planeData);\r\n            plane.new_uuid = GameFunc.uuid;\r\n            plane.setScaleType(this.getEnemyRangeType(id));\r\n            this.pushPlane(plane);\r\n\r\n            // if (GameIns.battleManager.shadowAble(GameIns.gameDataManager.curMainStage)) {\r\n            //     const uiData = this.getEnemyUIData(planeData.uiId);\r\n            //     plane.shadowComp = ShadowManager.addShadow(uiData.image + \"y\", node, v2(99, -165));\r\n            // }\r\n\r\n            this._normalCount++;\r\n            return plane;\r\n        } catch (error) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查敌人死亡炸弹\r\n     * @param position 炸弹位置\r\n     */\r\n    checkEnemyDieBomb(position: Vec3) {\r\n        // if (this._dieBombAtk > 0) {\r\n        //     const enemies = GameFunc.getRangeEnemys(position, this._dieBombRadius);\r\n        //     enemies.forEach((enemy) => {\r\n        //         if (!enemy.isDead) {\r\n        //             enemy.hurt(this._dieBombAtk);\r\n        //         }\r\n        //     });\r\n        //     EnemyEffectLayer.me.playDieBombAnim(position);\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 获取敌机的范围类型\r\n     * @param id 敌机 ID\r\n     */\r\n    getEnemyRangeType(id: number): number {\r\n        if ((id >= 1000 && id <= 1999) || (id >= 4000 && id <= 4999)) {\r\n            return 0; // 小型\r\n        } else if ((id >= 2000 && id <= 2999) || (id >= 5000 && id <= 6999)) {\r\n            return 1; // 中型\r\n        } else if (id >= 3000 && id <= 3999) {\r\n            return 2; // 大型\r\n        }\r\n        return -1; // 未知\r\n    }\r\n\r\n    /**\r\n     * 添加护盾\r\n     */\r\n    addShield() {\r\n        // let shieldNode = this._shieldPool.pop();\r\n        // if (!shieldNode) {\r\n        //     shieldNode = instantiate(this._pfShield);\r\n        //     shieldNode.getComponent(EnemyShield).preLoad();\r\n        // }\r\n        // const shield = shieldNode.getComponent(EnemyShield);\r\n        // this._shieldArr.push(shield);\r\n        // return shield;\r\n    }\r\n\r\n    /**\r\n     * 移除护盾\r\n     * @param shield 要移除的护盾\r\n     */\r\n    removeShield(shield) {\r\n        // shield.node.parent = null;\r\n        // Tools.arrRemove(this._shieldArr, shield);\r\n        // this._shieldPool.push(shield.node);\r\n    }\r\n\r\n    // /**\r\n    //  * 获取所有护盾\r\n    //  */\r\n    // get shieldArr(): EnemyShield[] {\r\n    //     return this._shieldArr;\r\n    // }\r\n}"]}