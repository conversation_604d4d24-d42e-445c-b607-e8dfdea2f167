
export let GameEnum = {

    /**
 * 游戏类型
 */
    GameType: {
        Common: 0,
        Expedition: 1,
        Gold: 2,
        Boss: 3,
    },

    /**
     * 游戏状态
     */
    GameState: {
        Idle: 0,
        Ready: 1,
        Sortie: 2,
        Battle: 3,
        Pause: 4,
        WillOver: 5,
        Over: 6,
        BossIn: 7,
    },

    /**
     * 敌人行为类型
     */
    EnemyAction: {
        Sneak: 0,// 潜行行为
        GoUp: 1,//上浮
        Track: 2,// 跟踪行为
        Transform: 3,// 变形行为
        AttackPrepare: 4,// 准备攻击行为
        AttackIng: 5,// 攻击中行为
        AttackOver: 6,// 攻击结束行为
        Leave: 7,//离开
    },

    /**
     * 敌人类型
     */
    EnemyType: {
        Normal: 0,
        Missile: 1,
        Turret: 2,
        Ligature: 3,
        LigatureLine: 4,
        LigatureUnit: 5,
        Build: 6,
        Ship: 7,
        ShipHeart: 8,
        Train: 9,
        ParkourItem: 10,
        GoldShip: 11,
        GoldBox: 12,
        BossLigature: 13,
        BossUnit: 10001,
        BossNormal: 20001,
        BossSnake: 21001,
        BossUFO: 22001,
    },
    /**
     * 敌人碰撞等级
     */
    EnemyCollideLevel: {
        None: 0,
        MainBullet: 1,
        Main: 2,
    },
    /**
     * 敌人销毁类型
     */
    EnemyDestroyType: {
        Die: 0,
        Leave: 1,
        TrackOver: 2,
        TimeOver: 3,
    },

    /**
     * Boss 行为类型
     */
    BossAction: {
        Normal: 0,
        Appear: 1,
        Transform: 2,
        AttackPrepare: 3,
        AttackIng: 4,
        AttackOver: 5,
        Switch: 6,
        Blast: 7,
    },

    /**
     * 敌人 Buff 类型
     */
    EnemyBuff: {
        Ice: 1,
        Fire: 2,
        Treat: 100,
    },

    /**
     * 敌人属性类型
     */
    EnemyAttr: {
        Doctor: 1,
        Shield: 2,
    },
}

export default GameEnum;