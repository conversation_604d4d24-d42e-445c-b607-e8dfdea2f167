System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, NodePool, Color, instantiate, Sprite, view, GameIns, ColliderType, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, ColliderTest;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "./Game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "./Game/collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderType(extras) {
    _reporterNs.report("ColliderType", "./Game/collider-system/FCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      NodePool = _cc.NodePool;
      Color = _cc.Color;
      instantiate = _cc.instantiate;
      Sprite = _cc.Sprite;
      view = _cc.view;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      ColliderType = _unresolved_3.ColliderType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "adebfSrTY5G2JWPpqP8/yig", "ColliderTest", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Prefab', 'Node', 'NodePool', 'Color', 'instantiate', 'Sprite', 'view', 'v3', 'Vec3', 'Graphics']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", ColliderTest = (_dec = ccclass('ColliderTest'), _dec2 = property([Node]), _dec3 = property(Node), _dec(_class = (_class2 = class ColliderTest extends Component {
        constructor(...args) {
          super(...args);
          this.nodeCount = 1000;

          _initializerDefineProperty(this, "testPrefabs", _descriptor, this);

          _initializerDefineProperty(this, "parentNode", _descriptor2, this);

          this.pool = void 0;
          this.width = void 0;
          this.height = void 0;
          this.halfWidth = void 0;
          this.halfHeight = void 0;
          this._list = [];
        }

        start() {
          this.height = view.getVisibleSize().height;
          this.width = view.getVisibleSize().width;
          this.halfWidth = this.width * 0.5;
          this.halfHeight = this.height * 0.5;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.enable = true;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.setGlobalColliderEnterCall((colliderA, colliderB) => {
            this.setChangeColor(colliderA, Color.RED);
            this.setChangeColor(colliderB, Color.RED);
          });
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.setGlobalColliderExitCall((colliderA, colliderB) => {
            this.setChangeColor(colliderA, Color.WHITE);
            this.setChangeColor(colliderB, Color.WHITE);
          });
          this.pool = new NodePool();

          for (let i = 0; i < this.nodeCount; i++) {
            let index = Math.floor(i / 2);
            this.createNode();
          }
        }

        setChangeColor(collider, color) {
          if (collider.type == (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Polygon) {
            let nodes = collider.node.children;

            for (let i = 0; i < nodes.length; i++) {
              nodes[i].getComponent(Sprite).color = color;
            }
          } else {
            collider.node.getComponent(Sprite).color = color;
          }
        }

        random(min, max) {
          return Math.floor(Math.random() * (max - min)) + min;
        }

        createNode() {
          let node = this.pool.get();
          let index = Math.floor(Math.random() * this.testPrefabs.length);
          if (!node) node = instantiate(this.testPrefabs[index]);
          node.parent = this.parentNode;
          node.active = true;
          let x = (Math.random() - 0.5) * this.width;
          let y = (Math.random() - 0.5) * this.height;
          node.setPosition(x, y);

          this._list.push(node);

          node["vx"] = Math.random() * 3 - 1.5;
          node["vy"] = Math.random() * 3 - 1.5;
        }

        update(dt) {
          for (let i = 0; i < this._list.length; i++) {
            let node = this._list[i];
            let x = node.position.x + node["vx"];
            let y = node.position.y + node["vy"];
            node.setPosition(x, y);

            if (node.position.x > this.halfWidth || node.position.x < -this.halfWidth) {
              node["vx"] *= -1;
            }

            if (node.position.y > this.halfHeight || node.position.y < -this.halfHeight) {
              node["vy"] *= -1;
            }
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.update(dt);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "testPrefabs", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "parentNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3eace4b22f5cfe11f307600b0d784ff618e67a92.js.map