System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, NodePool, instantiate, Prefab, SingletonBase, MyApp, _dec, _class, _crd, ccclass, PrefabManager;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      NodePool = _cc.NodePool;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "238cfBaJ1RM1rYH/uLvhviP", "PrefabManager", undefined);

      __checkObsolete__(['_decorator', 'NodePool', 'instantiate', 'Prefab', 'Node']);

      ({
        ccclass
      } = _decorator);

      _export("default", PrefabManager = (_dec = ccclass('PrefabManager'), _dec(_class = class PrefabManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.m_pool = new Map();
        }

        // 存储所有预制体对象池

        /**
         * 预加载指定的预制体
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Promise<void>}
         */
        async preload(prefabClass) {
          if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
          }

          await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.preloadAsync(prefabClass.PrefabName);
        }
        /**
         * 创建预制体实例
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Node} 预制体实例
         */


        async _create(prefabClass) {
          var node = null;

          if (prefabClass || prefabClass.PrefabName) {
            const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync("Game/prefabs/" + prefabClass.PrefabName, Prefab);

            if (prefab == null) {
              console.log(`create prefab[${prefabClass.PrefabName}] but prefab is null`);
            } else {
              node = instantiate(prefab);
            }
          }

          return node;
        }
        /**
         * 异步创建预制体实例
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Promise<Node>} 预制体实例
         */


        async create(prefabClass) {
          return this._create(prefabClass);
        }
        /**
         * 同步创建预制体实例
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Node} 预制体实例
         */


        createSync(prefabClass) {
          return this._create(prefabClass);
        }
        /**
         * 异步创建预制体组件
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Promise<Component>} 预制体组件
         */


        async createComponent(prefabClass) {
          const node = await this.create(prefabClass);
          return node.getComponent(prefabClass);
        }
        /**
         * 创建指定数量的预制体实例（逐帧创建）
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @param {number} count 需要创建的数量
         * @param {Array} result 存储创建结果的数组
         * @param {number} batch 每帧创建的数量
         * @returns {Promise<void>}
         */


        async createFrame(prefabClass, count, result, batch = 10) {
          for (let i = 0; i < batch; i++) {
            const node = await this._create(prefabClass);

            if (!node) {
              return;
            }

            result.push(node.getComponent(prefabClass));

            if (result.length >= count) {
              return;
            }
          }

          if (result.length < count) {
            setTimeout(() => {
              this.createFrame(prefabClass, count, result, batch);
            }, 0);
          }
        }
        /**
         * 创建指定数量的预制体实例（通过名称逐帧创建）
         * @param {string} prefabName 预制体名称
         * @param {number} count 需要创建的数量
         * @param {Array} result 存储创建结果的数组
         * @param {number} batch 每帧创建的数量
         * @returns {Promise<void>}
         */


        async createFrameByName(prefabName, count, result, batch = 10) {
          for (let i = 0; i < batch; i++) {
            const node = await this.create({
              PrefabName: prefabName
            });

            if (!node) {
              return;
            }

            result.push(node);

            if (result.length >= count) {
              return;
            }
          }

          if (result.length < count) {
            setTimeout(() => {
              this.createFrameByName(prefabName, count, result, batch);
            }, 0);
          }
        }
        /**
         * 获取对象池中的预制体实例
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @returns {Component} 预制体组件
         */


        get(prefabClass) {
          if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
          }

          const pool = this.m_pool.get(prefabClass.PrefabName);
          const node = pool && pool.size() > 0 ? pool.get() : this._create(prefabClass);
          return node.getComponent(prefabClass);
        }
        /**
         * 创建对象池
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @param {number} size 对象池的初始大小
         * @returns {Promise<void>}
         */


        async createPool(prefabClass, size = 1) {
          await this.preload(prefabClass);

          if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
          }

          const pool = new NodePool();
          this.m_pool.set(prefabClass.PrefabName, pool);

          for (let i = 0; i < size; i++) {
            const node = await this._create(prefabClass);
            pool.put(node);
          }
        }
        /**
         * 添加预制体实例到对象池
         * @param {Object} prefabClass 包含 PrefabName 的类
         */


        add(prefabClass) {
          if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
          }

          const pool = this.m_pool.get(prefabClass.PrefabName);

          if (!pool) {
            return null;
          }

          const node = this._create(prefabClass);

          pool.put(node);
        }
        /**
         * 将预制体实例放回对象池
         * @param {Object} prefabClass 包含 PrefabName 的类
         * @param {Node} node 需要放回的节点
         */


        put(prefabClass, node) {
          if (!prefabClass.PrefabName) {
            throw new Error('Please set static var PrefabName');
          }

          const pool = this.m_pool.get(prefabClass.PrefabName);

          if (!pool) {
            return null;
          }

          pool.put(node);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=37c88a200ef74a6377ba47cf0f3a5bc4a1dc7639.js.map