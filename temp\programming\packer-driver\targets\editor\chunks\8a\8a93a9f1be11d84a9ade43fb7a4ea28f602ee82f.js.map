{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts"], "names": ["WaveManager", "error", "SingletonBase", "GameConst", "EnemyWave", "GameIns", "Tools", "BossBase", "MyApp", "enemyCreateAble", "_bEnemyCreateAble", "value", "constructor", "_waveNorDatasMap", "Map", "_enemyOver", "_enemyActions", "_bEnemyNorCreateAble", "_waveActionArr", "_waveArr", "_waveNumArr", "_waveTimeArr", "_enemyCreateTime", "_enemyActionIndex", "_waveIndex", "_waveCreateTime", "_waveIndexOver", "_curEnemyAction", "_boss<PERSON><PERSON><PERSON><PERSON><PERSON>", "_bossCreateTime", "_bossToAddArr", "_bShowBossWarning", "initConfig", "waveDatas", "lubanTables", "TbWave", "getDataList", "waveData", "wave", "loadJson", "group", "get", "enemyGroupID", "push", "set", "mainReset", "reset", "splice", "setEnemyActions", "actions", "gameStart", "getNorWaveDatas", "groupID", "updateGameLogic", "deltaTime", "_updateCurAction", "_updateEnemy", "_updateBoss", "length", "action", "type", "enemyNorInterval", "enemyManager", "getNormalPlaneCount", "console", "warn", "enemyNorIDs", "boss<PERSON><PERSON><PERSON>", "loadBossRes", "_updateEnemyCreate", "_updateNorEnemys", "addWaveByLevel", "pos", "enemyWave", "fromLevelWave", "i", "currentEnemyCount", "posX", "EnemyPos", "x", "posY", "y", "bSetStartPos", "startPosX", "startPosY", "expPerEnemy", "Math", "floor", "exp", "enemyNum", "j", "enemyInterval", "enemy", "enemyPosX", "posDX", "enemyPosY", "posDY", "addPlane", "enemyID", "firstShootDelay", "setFirstShoot<PERSON>elay", "setStandByTime", "setExp", "initPropertyRate", "enemyNorRate", "initTrack", "trackGroups", "liveParam", "rotateSpeed", "waveID", "arrC<PERSON>ain", "groupInterval", "isEnemyOver", "battleManager", "bossWillEnter", "boss<PERSON><PERSON><PERSON><PERSON>sh", "bossData", "boss", "addBoss", "setPropertyRate", "setTip", "stageManager", "getBossTips"], "mappings": ";;;4JAgBqBA,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBZC,MAAAA,K,OAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AAIFC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;yBAMYR,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AAwB7C,YAAfS,eAAe,GAAY;AAC3B,iBAAO,KAAKC,iBAAZ;AACH;;AAEkB,YAAfD,eAAe,CAACE,KAAD,EAAiB;AAChC,eAAKD,iBAAL,GAAyBC,KAAzB;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV;AADU,eA/BNC,gBA+BM,GA/BuC,IAAIC,GAAJ,EA+BvC;AAAA,eA9BNC,UA8BM,GA9BgB,KA8BhB;AAAA,eA7BNC,aA6BM,GA7B8B,IA6B9B;AAAA,eA5BNN,iBA4BM,GA5BuB,KA4BvB;AAAA,eA3BNO,oBA2BM,GA3B0B,KA2B1B;AAAA,eA1BNC,cA0BM,GA1BkB,EA0BlB;AAAA,eAzBNC,QAyBM,GAzBkB,EAyBlB;AAAA,eAxBNC,WAwBM,GAxBkB,EAwBlB;AAAA,eAvBNC,YAuBM,GAvBmB,EAuBnB;AAAA,eAtBNC,gBAsBM,GAtBqB,CAsBrB;AAAA,eArBNC,iBAqBM,GArBsB,CAqBtB;AAAA,eAnBNC,UAmBM,GAnBe,CAmBf;AAAA,eAlBNC,eAkBM,GAlBoB,CAkBpB;AAAA,eAjBNC,cAiBM,GAjBqB,EAiBrB;AAAA,eAhBNC,eAgBM,GAhBwB,IAgBxB;AAAA,eAdNC,gBAcM,GAdqB,CAcrB;AAAA,eAbNC,eAaM,GAboB,CAapB;AAAA,eAZNC,aAYM,GAZiB,EAYjB;AAAA,eAXNC,iBAWM,GAXuB,KAWvB;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT,cAAIC,SAAS,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,WAAzB,EAAhB;;AACA,eAAK,IAAIC,QAAT,IAAqBJ,SAArB,EAAgC;AAC5B,kBAAMK,IAAI,GAAG;AAAA;AAAA,yCAAb;AACAA,YAAAA,IAAI,CAACC,QAAL,CAAcF,QAAd;AACA,kBAAMG,KAAK,GAAG,KAAK3B,gBAAL,CAAsB4B,GAAtB,CAA0BH,IAAI,CAACI,YAA/B,KAAgD,EAA9D;AACAF,YAAAA,KAAK,CAACG,IAAN,CAAWL,IAAX;;AACA,iBAAKzB,gBAAL,CAAsB+B,GAAtB,CAA0BN,IAAI,CAACI,YAA/B,EAA6CF,KAA7C;AACH;AACJ;;AAEDK,QAAAA,SAAS,GAAS;AACd,eAAKC,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAS;AACV,eAAK/B,UAAL,GAAkB,KAAlB;AACA,eAAKC,aAAL,GAAqB,EAArB;AACA,eAAKO,iBAAL,GAAyB,CAAzB;AACA,eAAKD,gBAAL,GAAwB,CAAxB;AACA,eAAKZ,iBAAL,GAAyB,KAAzB;AACA,eAAKO,oBAAL,GAA4B,KAA5B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAvB;;AACA,eAAKC,cAAL,CAAoBqB,MAApB,CAA2B,CAA3B;;AACA,eAAKpB,eAAL,GAAuB,IAAvB;AACA,eAAKR,QAAL,GAAgB,EAAhB;AACA,eAAKD,cAAL,GAAsB,EAAtB;AACA,eAAKE,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACA,eAAKU,iBAAL,GAAyB,KAAzB;AACA,eAAKF,eAAL,GAAuB,CAAvB;AACH;;AAEDmB,QAAAA,eAAe,CAACC,OAAD,EAA6B;AACxC,eAAKjC,aAAL,GAAqBiC,OAArB;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd,eAAKxC,iBAAL,GAAyB,IAAzB;AACA,eAAKO,oBAAL,GAA4B,IAA5B;AACA,eAAKE,QAAL,GAAgB,EAAhB;AACA,eAAKD,cAAL,GAAsB,EAAtB;AACA,eAAKE,WAAL,GAAmB,EAAnB;AACA,eAAKC,YAAL,GAAoB,EAApB;AACH;;AAED8B,QAAAA,eAAe,CAACC,OAAD,EAA2C;AACtD,iBAAO,KAAKvC,gBAAL,CAAsB4B,GAAtB,CAA0BW,OAA1B,CAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,SAAD,EAA0B;AACrC,eAAKC,gBAAL,CAAsBD,SAAtB;;AACA,eAAKE,YAAL,CAAkBF,SAAlB;;AACA,eAAKG,WAAL,CAAiBH,SAAjB;AACH;AAED;AACJ;AACA;AACA;;;AACYC,QAAAA,gBAAgB,CAACD,SAAD,EAA0B;AAC9C,cAAI,CAAC,KAAKvC,UAAV,EAAsB;AAAA;;AAClB,gBAAI,KAAKQ,iBAAL,KAA2B,6BAAKP,aAAL,yCAAoB0C,MAApB,KAA8B,CAAzD,CAAJ,EAAiE;AAC7D,mBAAK3C,UAAL,GAAkB,IAAlB;AACAd,cAAAA,KAAK,CAAC,YAAD,CAAL;AACH,aAHD,MAGO,IAAI,KAAKQ,eAAL,IAAwB,CAAC,KAAKkB,eAAlC,EAAmD;AACtD,oBAAMgC,MAAM,GAAG,KAAK3C,aAAL,CAAoB,KAAKO,iBAAzB,CAAf;;AACA,sBAAQoC,MAAM,CAACC,IAAf;AACI,qBAAK,CAAL;AACI,uBAAKtC,gBAAL,IAAyBgC,SAAzB;;AACA,sBACI,KAAKhC,gBAAL,IAAyBqC,MAAM,CAACE,gBAAhC,IACC,KAAK1C,QAAL,CAAcuC,MAAd,KAAyB,CAAzB,IAA8B;AAAA;AAAA,0CAAQI,YAAR,CAAqBC,mBAArB,OAA+C,CAFlF,EAGE;AACE,yBAAKpC,eAAL,GAAuBgC,MAAvB;AACH;;AACD;;AAEJ,qBAAK,CAAL;AACI,sBACI,KAAKxC,QAAL,CAAcuC,MAAd,KAAyB,CAAzB,IACA;AAAA;AAAA,0CAAQI,YAAR,CAAqBC,mBAArB,OAA+C,CAFnD,EAGE;AACE,yBAAKzC,gBAAL,IAAyBgC,SAAzB;;AACA,wBAAI,KAAKhC,gBAAL,IAAyBqC,MAAM,CAACE,gBAApC,EAAsD;AAClD,2BAAKlC,eAAL,GAAuBgC,MAAvB;AACH;AACJ;;AACD;;AAEJ;AACI,sBAAIA,MAAM,CAACC,IAAP,IAAe,GAAnB,EAAwB;AACpBI,oBAAAA,OAAO,CAACC,IAAR,CAAa,YAAb,EAA2BN,MAAM,CAACC,IAAlC,EAAwCD,MAAM,CAACO,WAAP,CAAmB,CAAnB,CAAxC;AACA,yBAAKtC,gBAAL,GAAwB+B,MAAM,CAACE,gBAA/B;AACA;AAAA;AAAA,4CAAQM,WAAR,CAAoBC,WAApB,CAAgCT,MAAM,CAACC,IAAvC,EAA6CD,MAAM,CAACO,WAAP,CAAmB,CAAnB,CAA7C;;AACA,yBAAKpC,aAAL,CAAmBa,IAAnB,CAAwBgB,MAAxB;;AACA,yBAAKpC,iBAAL;AACH;;AA9BT;AAgCH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACYiC,QAAAA,YAAY,CAACF,SAAD,EAA0B;AAC1C;AACA;AACA;AACA;AAEA,eAAKe,kBAAL,CAAwBf,SAAxB;;AAEA,cAAI,KAAK3B,eAAT,EAA0B;AACtB,gBAAI,CAAC,KAAK2C,gBAAL,CAAsBhB,SAAtB,CAAL,EAAuC;AACnC,mBAAK3B,eAAL,GAAuB,IAAvB;AACA,mBAAKJ,iBAAL;AACA,mBAAKD,gBAAL,GAAwB,CAAxB;AACH;AACJ;AACJ;;AAEDiD,QAAAA,cAAc,CAACjC,IAAD,EAAakC,GAAb,EAAwB;AAClC,gBAAMC,SAAS,GAAG;AAAA;AAAA,sCAAUC,aAAV,CAAwBpC,IAAxB,EAA8BkC,GAA9B,CAAlB;;AACA,eAAKrD,QAAL,CAAcwB,IAAd,CAAmB8B,SAAnB;;AACA,eAAKrD,WAAL,CAAiBuB,IAAjB,CAAsB,CAAtB;;AACA,eAAKtB,YAAL,CAAkBsB,IAAlB,CAAuB,CAAvB;;AACA,eAAKzB,cAAL,CAAoByB,IAApB,CAAyB,KAAKhB,eAA9B;AACH;AAED;AACJ;AACA;AACA;;;AACoC,cAAlB0C,kBAAkB,CAACf,SAAD,EAAmC;AAC/D,eAAK,IAAIqB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxD,QAAL,CAAcuC,MAAlC,EAA0CiB,CAAC,EAA3C,EAA+C;AAC3C,kBAAMrC,IAAI,GAAG,KAAKnB,QAAL,CAAcwD,CAAd,CAAb;AACA,iBAAKtD,YAAL,CAAkBsD,CAAlB,KAAwBrB,SAAxB;AACA,kBAAMsB,iBAAiB,GAAG,KAAKxD,WAAL,CAAiBuD,CAAjB,CAA1B;AACA,gBAAIE,IAAI,GAAG;AAAA;AAAA,wCAAUC,QAAV,CAAmBC,CAA9B;AACA,gBAAIC,IAAI,GAAG;AAAA;AAAA,wCAAUF,QAAV,CAAmBG,CAA9B;;AAEA,gBAAI3C,IAAI,CAAC4C,YAAT,EAAuB;AACnBL,cAAAA,IAAI,IAAIvC,IAAI,CAAC6C,SAAb;AACAH,cAAAA,IAAI,IAAI1C,IAAI,CAAC8C,SAAb;AACH;;AAED,kBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWjD,IAAI,CAACkD,GAAL,GAAWlD,IAAI,CAACmD,QAA3B,CAApB;;AAEA,iBAAK,IAAIC,CAAC,GAAGd,iBAAb,EAAgCc,CAAC,GAAGpD,IAAI,CAACmD,QAAzC,EAAmDC,CAAC,EAApD,EAAwD;AACpD,kBAAIpD,IAAI,CAACqD,aAAL,IAAsBD,CAAC,GAAG,CAA1B,IAA+B,KAAKrE,YAAL,CAAkBsD,CAAlB,CAAnC,EAAyD;AACrD,qBAAKvD,WAAL,CAAiBuD,CAAjB;AACA,oBAAIiB,KAAJ;AACA,sBAAMC,SAAS,GAAGhB,IAAI,GAAGvC,IAAI,CAACwD,KAAL,IAAcJ,CAAC,GAAG,CAAlB,CAAzB;AACA,sBAAMK,SAAS,GAAGf,IAAI,GAAG1C,IAAI,CAAC0D,KAAL,IAAcN,CAAC,GAAG,CAAlB,CAAzB;;AAEA,wBAAQpD,IAAI,CAACsB,IAAb;AACI,uBAAK,CAAL;AACIgC,oBAAAA,KAAK,GAAG,MAAM;AAAA;AAAA,4CAAQ9B,YAAR,CAAqBmC,QAArB,CAA8B3D,IAAI,CAAC4D,OAAnC,CAAd;;AACA,wBAAIN,KAAJ,EAAW;AACP,0BAAIF,CAAC,GAAGpD,IAAI,CAAC6D,eAAL,CAAqBzC,MAA7B,EAAqC;AACjCkC,wBAAAA,KAAK,CAACQ,kBAAN,CAAyB9D,IAAI,CAAC6D,eAAL,CAAqBT,CAArB,CAAzB;AACH;;AACDE,sBAAAA,KAAK,CAACS,cAAN,CAAqB,CAArB;AACAT,sBAAAA,KAAK,CAACU,MAAN,CAAajB,WAAb;AACAO,sBAAAA,KAAK,CAACW,gBAAN,CAAuB,KAAKrF,cAAL,CAAoByD,CAApB,EAAuB6B,YAA9C;AACAZ,sBAAAA,KAAK,CAACa,SAAN,CACInE,IAAI,CAACoE,WADT,EAEIpE,IAAI,CAACqE,SAFT,EAGId,SAHJ,EAIIE,SAJJ,EAKIzD,IAAI,CAACsE,WALT,EAPO,CAcP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AACD;AA1BR;AA4BH;AACJ;;AAED,gBAAItE,IAAI,CAACmD,QAAL,IAAiB,KAAKrE,WAAL,CAAiBuD,CAAjB,CAArB,EAA0C;AACtC,mBAAKxD,QAAL,CAAc4B,MAAd,CAAqB4B,CAArB,EAAwB,CAAxB;;AACA,mBAAKvD,WAAL,CAAiB2B,MAAjB,CAAwB4B,CAAxB,EAA2B,CAA3B;;AACA,mBAAKtD,YAAL,CAAkB0B,MAAlB,CAAyB4B,CAAzB,EAA4B,CAA5B;;AACA,mBAAKzD,cAAL,CAAoB6B,MAApB,CAA2B4B,CAA3B,EAA8B,CAA9B;;AACAA,cAAAA,CAAC;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACYL,QAAAA,gBAAgB,CAAChB,SAAD,EAA6B;AACjD,cAAI,KAAKrC,oBAAT,EAA+B;AAC3B,gBAAI,KAAKO,UAAL,IAAmB,KAAKG,eAAL,CAAsBuC,WAAtB,CAAkCR,MAAzD,EAAiE;AAC7D,mBAAKlC,UAAL,GAAkB,CAAlB;AACA,qBAAO,KAAP;AACH;;AAED,kBAAMqF,MAAM,GAAG,KAAKlF,eAAL,CAAsBuC,WAAtB,CAAkC,KAAK1C,UAAvC,CAAf;AACA,iBAAKC,eAAL,IAAwB6B,SAAxB;AAEA,kBAAMrB,SAAS,GAAG,KAAKkB,eAAL,CAAqB0D,MAArB,CAAlB;;AACA,iBAAK,IAAIlC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1C,SAAS,CAAEyB,MAA/B,EAAuCiB,CAAC,EAAxC,EAA4C;AACxC,oBAAMrC,IAAI,GAAGL,SAAS,CAAE0C,CAAF,CAAtB;;AACA,kBACI,CAAC;AAAA;AAAA,kCAAMmC,UAAN,CAAiB,KAAKpF,cAAtB,EAAsCiD,CAAtC,CAAD,IACA,KAAKlD,eAAL,IAAwBa,IAAI,CAACyE,aAFjC,EAGE;AACE,qBAAK5F,QAAL,CAAcwB,IAAd,CAAmBL,IAAnB;;AACA,qBAAKlB,WAAL,CAAiBuB,IAAjB,CAAsB,CAAtB;;AACA,qBAAKtB,YAAL,CAAkBsB,IAAlB,CAAuB,CAAvB;;AACA,qBAAKzB,cAAL,CAAoByB,IAApB,CAAyB,KAAKhB,eAA9B;;AACA,qBAAKD,cAAL,CAAoBiB,IAApB,CAAyBgC,CAAzB;AACH;AACJ;;AAED,gBAAI,KAAKjD,cAAL,CAAoBgC,MAApB,IAA8BzB,SAAS,CAAEyB,MAA7C,EAAqD;AACjD,mBAAKhC,cAAL,CAAoBqB,MAApB,CAA2B,CAA3B;;AACA,mBAAKtB,eAAL,GAAuB,CAAvB;AACA,mBAAKD,UAAL;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACYiC,QAAAA,WAAW,CAACH,SAAD,EAA0B;AACzC,cACI,KAAKxB,aAAL,CAAmB4B,MAAnB,GAA4B,CAA5B,IACA;AAAA;AAAA,kCAAQI,YAAR,CAAqBkD,WAArB,EADA,KAEC,KAAKjF,iBAAL,KACI,KAAKA,iBAAL,GAAyB,IAAzB,EAA+B;AAAA;AAAA,kCAAQkF,aAAR,CAAsBC,aAAtB,EADnC,CAFD,CADJ,EAKE;AACE,iBAAKrF,eAAL,IAAwByB,SAAxB;;AACA,gBACI,KAAKzB,eAAL,GAAuB,KAAKD,gBAA5B,IACA;AAAA;AAAA,oCAAQuC,WAAR,CAAoBgD,aAFxB,EAGE;AACE,oBAAMC,QAAQ,GAAG,KAAKtF,aAAL,CAAmB,CAAnB,CAAjB;AACA,oBAAMuF,IAAI,GAAG;AAAA;AAAA,sCAAQlD,WAAR,CAAoBmD,OAApB,CACTF,QAAQ,CAACxD,IADA,EAETwD,QAAQ,CAAClD,WAAT,CAAqB,CAArB,CAFS,CAAb;;AAIA,kBAAImD,IAAI;AAAA;AAAA,uCAAR,EAA8B;AAC1B;AACA;AACA;AACAA,gBAAAA,IAAI,CAACE,eAAL,CAAqBH,QAAQ,CAACZ,YAA9B,EAJ0B,CAK1B;;AACAa,gBAAAA,IAAI,CAACG,MAAL,CAAY;AAAA;AAAA,wCAAQC,YAAR,CAAqBC,WAArB,EAAZ;AACH;;AACD,mBAAK5F,aAAL,CAAmBiB,MAAnB,CAA0B,CAA1B,EAA6B,CAA7B;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIiE,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAKjG,UAAL,IAAmB,KAAKI,QAAL,CAAcuC,MAAd,KAAyB,CAA5C,IAAiD,KAAK5B,aAAL,CAAmB4B,MAAnB,KAA8B,CAAtF;AACH,SAhU+D,CAkUhE;AACA;AACA;AACA;AACA;AACA;AACA;;;AAxUgE,O", "sourcesContent": ["import { error, JsonAsset, resources, Vec2 } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport { EnemyWave } from \"../data/EnemyWave\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport BossBase from \"../ui/plane/boss/BossBase\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { StageData } from \"../data/StageData\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n\r\nexport default class WaveManager extends SingletonBase<WaveManager> {\r\n    private _waveNorDatasMap: Map<number, EnemyWave[]> = new Map();\r\n    private _enemyOver: boolean = false;\r\n    private _enemyActions: StageData[] | null = null;\r\n    private _bEnemyCreateAble: boolean = false;\r\n    private _bEnemyNorCreateAble: boolean = false;\r\n    private _waveActionArr: any[] = [];\r\n    private _waveArr: EnemyWave[] = [];\r\n    private _waveNumArr: number[] = [];\r\n    private _waveTimeArr: number[] = [];\r\n    private _enemyCreateTime: number = 0;\r\n    private _enemyActionIndex: number = 0;\r\n\r\n    private _waveIndex: number = 0;\r\n    private _waveCreateTime: number = 0;\r\n    private _waveIndexOver: number[] = [];\r\n    private _curEnemyAction: any | null = null;\r\n\r\n    private _bossCreateDelay: number = 0;\r\n    private _bossCreateTime: number = 0;\r\n    private _bossToAddArr: any[] = [];\r\n    private _bShowBossWarning: boolean = false;\r\n\r\n\r\n    get enemyCreateAble(): boolean {\r\n        return this._bEnemyCreateAble;\r\n    }\r\n\r\n    set enemyCreateAble(value: boolean) {\r\n        this._bEnemyCreateAble = value;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig() {\r\n        let waveDatas = MyApp.lubanTables.TbWave.getDataList();\r\n        for (let waveData of waveDatas) {\r\n            const wave = new EnemyWave();\r\n            wave.loadJson(waveData);\r\n            const group = this._waveNorDatasMap.get(wave.enemyGroupID) || [];\r\n            group.push(wave);\r\n            this._waveNorDatasMap.set(wave.enemyGroupID, group);\r\n        }\r\n    }\r\n\r\n    mainReset(): void {\r\n        this.reset();\r\n    }\r\n\r\n    reset(): void {\r\n        this._enemyOver = false;\r\n        this._enemyActions = [];\r\n        this._enemyActionIndex = 0;\r\n        this._enemyCreateTime = 0;\r\n        this._bEnemyCreateAble = false;\r\n        this._bEnemyNorCreateAble = false;\r\n        this._waveIndex = 0;\r\n        this._waveCreateTime = 0;\r\n        this._waveIndexOver.splice(0);\r\n        this._curEnemyAction = null;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n        this._bShowBossWarning = false;\r\n        this._bossCreateTime = 0;\r\n    }\r\n\r\n    setEnemyActions(actions: StageData[]): void {\r\n        this._enemyActions = actions;\r\n    }\r\n\r\n    gameStart(): void {\r\n        this._bEnemyCreateAble = true;\r\n        this._bEnemyNorCreateAble = true;\r\n        this._waveArr = [];\r\n        this._waveActionArr = [];\r\n        this._waveNumArr = [];\r\n        this._waveTimeArr = [];\r\n    }\r\n\r\n    getNorWaveDatas(groupID: number): EnemyWave[] | undefined {\r\n        return this._waveNorDatasMap.get(groupID);\r\n    }\r\n\r\n\r\n    /**\r\n * 更新游戏逻辑\r\n * @param deltaTime 每帧的时间增量\r\n */\r\n    updateGameLogic(deltaTime: number): void {\r\n        this._updateCurAction(deltaTime);\r\n        this._updateEnemy(deltaTime);\r\n        this._updateBoss(deltaTime);\r\n    }\r\n\r\n    /**\r\n     * 更新当前敌人行为\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateCurAction(deltaTime: number): void {\r\n        if (!this._enemyOver) {\r\n            if (this._enemyActionIndex >= (this._enemyActions?.length || 0)) {\r\n                this._enemyOver = true;\r\n                error(\"enemy over\");\r\n            } else if (this.enemyCreateAble && !this._curEnemyAction) {\r\n                const action = this._enemyActions![this._enemyActionIndex];\r\n                switch (action.type) {\r\n                    case 0:\r\n                        this._enemyCreateTime += deltaTime;\r\n                        if (\r\n                            this._enemyCreateTime >= action.enemyNorInterval ||\r\n                            (this._waveArr.length === 0 && GameIns.enemyManager.getNormalPlaneCount() === 0)\r\n                        ) {\r\n                            this._curEnemyAction = action;\r\n                        }\r\n                        break;\r\n\r\n                    case 1:\r\n                        if (\r\n                            this._waveArr.length === 0 &&\r\n                            GameIns.enemyManager.getNormalPlaneCount() === 0\r\n                        ) {\r\n                            this._enemyCreateTime += deltaTime;\r\n                            if (this._enemyCreateTime >= action.enemyNorInterval) {\r\n                                this._curEnemyAction = action;\r\n                            }\r\n                        }\r\n                        break;\r\n\r\n                    default:\r\n                        if (action.type >= 100) {\r\n                            console.warn(\"Boss stage\", action.type, action.enemyNorIDs[0]);\r\n                            this._bossCreateDelay = action.enemyNorInterval;\r\n                            GameIns.bossManager.loadBossRes(action.type, action.enemyNorIDs[0]);\r\n                            this._bossToAddArr.push(action);\r\n                            this._enemyActionIndex++;\r\n                        }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新敌人逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateEnemy(deltaTime: number): void {\r\n        // if (this.isShipStage && GameIns.enemyManager.isEnemyShipDead()) {\r\n        //     this._waveArr.splice(0);\r\n        //     this._enemyOver = true;\r\n        // }\r\n\r\n        this._updateEnemyCreate(deltaTime);\r\n\r\n        if (this._curEnemyAction) {\r\n            if (!this._updateNorEnemys(deltaTime)) {\r\n                this._curEnemyAction = null;\r\n                this._enemyActionIndex++;\r\n                this._enemyCreateTime = 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    addWaveByLevel(wave: Wave, pos: Vec2) {\r\n        const enemyWave = EnemyWave.fromLevelWave(wave, pos);\r\n        this._waveArr.push(enemyWave)\r\n        this._waveNumArr.push(0)\r\n        this._waveTimeArr.push(0)\r\n        this._waveActionArr.push(this._curEnemyAction)\r\n    }\r\n\r\n    /**\r\n     * 更新敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private async _updateEnemyCreate(deltaTime: number): Promise<void> {\r\n        for (let i = 0; i < this._waveArr.length; i++) {\r\n            const wave = this._waveArr[i];\r\n            this._waveTimeArr[i] += deltaTime;\r\n            const currentEnemyCount = this._waveNumArr[i];\r\n            let posX = GameConst.EnemyPos.x;\r\n            let posY = GameConst.EnemyPos.y;\r\n\r\n            if (wave.bSetStartPos) {\r\n                posX += wave.startPosX;\r\n                posY += wave.startPosY;\r\n            }\r\n\r\n            const expPerEnemy = Math.floor(wave.exp / wave.enemyNum);\r\n\r\n            for (let j = currentEnemyCount; j < wave.enemyNum; j++) {\r\n                if (wave.enemyInterval * (j + 1) < this._waveTimeArr[i]) {\r\n                    this._waveNumArr[i]++;\r\n                    let enemy:EnemyPlane;\r\n                    const enemyPosX = posX + wave.posDX * (j + 1);\r\n                    const enemyPosY = posY + wave.posDY * (j + 1);\r\n\r\n                    switch (wave.type) {\r\n                        case 0:\r\n                            enemy = await GameIns.enemyManager.addPlane(wave.enemyID);\r\n                            if (enemy) {\r\n                                if (j < wave.firstShootDelay.length) {\r\n                                    enemy.setFirstShootDelay(wave.firstShootDelay[j]);\r\n                                }\r\n                                enemy.setStandByTime(0);\r\n                                enemy.setExp(expPerEnemy);\r\n                                enemy.initPropertyRate(this._waveActionArr[i].enemyNorRate);\r\n                                enemy.initTrack(\r\n                                    wave.trackGroups,\r\n                                    wave.liveParam,\r\n                                    enemyPosX,\r\n                                    enemyPosY,\r\n                                    wave.rotateSpeed\r\n                                );\r\n                                // if (\r\n                                //     wave.normalLoot &&\r\n                                //     Tools.arrContain(wave.normalLoot.enemys, j + 1)\r\n                                // ) {\r\n                                //     enemy.addLoot(\r\n                                //         GameIns.lootManager.getLootData(wave.normalLoot.lootId)\r\n                                //     );\r\n                                // }\r\n                            }\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (wave.enemyNum <= this._waveNumArr[i]) {\r\n                this._waveArr.splice(i, 1);\r\n                this._waveNumArr.splice(i, 1);\r\n                this._waveTimeArr.splice(i, 1);\r\n                this._waveActionArr.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新普通敌人生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateNorEnemys(deltaTime: number): boolean {\r\n        if (this._bEnemyNorCreateAble) {\r\n            if (this._waveIndex >= this._curEnemyAction!.enemyNorIDs.length) {\r\n                this._waveIndex = 0;\r\n                return false;\r\n            }\r\n\r\n            const waveID = this._curEnemyAction!.enemyNorIDs[this._waveIndex];\r\n            this._waveCreateTime += deltaTime;\r\n\r\n            const waveDatas = this.getNorWaveDatas(waveID);\r\n            for (let i = 0; i < waveDatas!.length; i++) {\r\n                const wave = waveDatas![i];\r\n                if (\r\n                    !Tools.arrContain(this._waveIndexOver, i) &&\r\n                    this._waveCreateTime >= wave.groupInterval\r\n                ) {\r\n                    this._waveArr.push(wave);\r\n                    this._waveNumArr.push(0);\r\n                    this._waveTimeArr.push(0);\r\n                    this._waveActionArr.push(this._curEnemyAction);\r\n                    this._waveIndexOver.push(i);\r\n                }\r\n            }\r\n\r\n            if (this._waveIndexOver.length >= waveDatas!.length) {\r\n                this._waveIndexOver.splice(0);\r\n                this._waveCreateTime = 0;\r\n                this._waveIndex++;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 更新 Boss 生成逻辑\r\n     * @param deltaTime 每帧的时间增量\r\n     */\r\n    private _updateBoss(deltaTime: number): void {\r\n        if (\r\n            this._bossToAddArr.length > 0 &&\r\n            GameIns.enemyManager.isEnemyOver() &&\r\n            (this._bShowBossWarning ||\r\n                (this._bShowBossWarning = true, GameIns.battleManager.bossWillEnter()))\r\n        ) {\r\n            this._bossCreateTime += deltaTime;\r\n            if (\r\n                this._bossCreateTime > this._bossCreateDelay &&\r\n                GameIns.bossManager.bossResFinish\r\n            ) {\r\n                const bossData = this._bossToAddArr[0];\r\n                const boss = GameIns.bossManager.addBoss(\r\n                    bossData.type,\r\n                    bossData.enemyNorIDs[0]\r\n                );\r\n                if (boss instanceof BossBase) {\r\n                    // if (GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {\r\n                    //     boss.setPropertyRate(BossBattleManager.getPropertyRate());\r\n                    // } else {\r\n                    boss.setPropertyRate(bossData.enemyNorRate);\r\n                    // }\r\n                    boss.setTip(GameIns.stageManager.getBossTips());\r\n                }\r\n                this._bossToAddArr.splice(0, 1);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * 检查敌人是否全部结束\r\n     * @returns 是否所有敌人都已结束\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._enemyOver && this._waveArr.length === 0 && this._bossToAddArr.length === 0;\r\n    }\r\n\r\n    //     /**\r\n    //      * 设置当前波次的 ID（未实现具体逻辑）\r\n    //      * @param waveID 波次 ID\r\n    //      */\r\n    //     setWaveID(waveID: number): void {\r\n    //         // 该方法目前未实现具体逻辑\r\n    //     }\r\n}"]}