System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, resources, Sprite, SpriteFrame, ButtonPlus, EventMgr, MainEvent, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, BuidingUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainEvent(extras) {
    _reporterNs.report("MainEvent", "./MainEvent", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      resources = _cc.resources;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      MainEvent = _unresolved_4.MainEvent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "40a0dCYqPJAh7Xe/xEz8MOw", "BuidingUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'resources', 'Sprite', 'SpriteFrame']);

      ({
        ccclass,
        property
      } = _decorator); //参考 BagItem

      _export("BuidingUI", BuidingUI = (_dec = ccclass('BuidingUI'), _dec2 = property(Label), _dec3 = property(Sprite), _dec(_class = (_class2 = class BuidingUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "title", _descriptor, this);

          _initializerDefineProperty(this, "sprt", _descriptor2, this);

          this.index = 0;
        }

        onLoad() {
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onClick, this);
        }

        onDestroy() {}

        onClick() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && MainEvent === void 0 ? (_reportPossibleCrUseOfMainEvent({
            error: Error()
          }), MainEvent) : MainEvent).BattleItemClick, this.index, this.title.string);
        }

        setNewFrame(imageUrl) {
          resources.load(`Game/texture/common/itemImage/${imageUrl}`, SpriteFrame, (err, spriteFrame) => {
            this.sprt.spriteFrame = spriteFrame;
          });
        }

        setTitle(index, title) {
          this.index = index;
          this.title.string = title;
        } //cell的容器去手动调用


        onRenderItem(item) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "title", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "sprt", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b06272efcc0e0324aac8c9e44048be4f745d53a5.js.map