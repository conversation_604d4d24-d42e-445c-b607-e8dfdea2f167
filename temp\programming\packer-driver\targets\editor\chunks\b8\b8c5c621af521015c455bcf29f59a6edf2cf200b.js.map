{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Data/bag/Bag.ts"], "names": ["Bag", "csproto", "EventMgr", "MyApp", "logError", "DataEvent", "items", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ITEM_LIST", "onGetItemListMsg", "bind", "CS_CMD_UPDATE_ITEM", "onUpdateItemMsg", "refreshItems", "sendMessage", "get_item_list", "emit", "ItemsRefresh", "msg", "updateItems", "body", "update_item", "reason", "comm", "ITEM_UPDATE_REASON", "ITEM_UPDATE_REASON_ADD", "for<PERSON>ach", "element", "getItemByGuid", "guid", "push", "ITEM_UPDATE_REASON_REMOVE", "idx", "findIndex", "v", "eq", "splice", "ITEM_UPDATE_REASON_CHANGE", "map", "item", "find", "update"], "mappings": ";;;6EAOaA,G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANNC,MAAAA,O;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;qBAEIL,G,GAAN,MAAMA,GAAN,CAA2B;AAAA;AAC9B;AAD8B,eAE9BM,KAF8B,GAEA,EAFA;AAAA;;AAIvBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,gBAAL,CAAsBC,IAAtB,CAA2B,IAA3B,CAArE;AACA;AAAA;AAAA,8BAAMN,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBI,kBAA/C,EAAmE,KAAKC,eAAL,CAAqBF,IAArB,CAA0B,IAA1B,CAAnE;AACA,eAAKG,YAAL;AACH,SAR6B,CAU9B;;;AACAA,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,8BAAMT,MAAN,CAAaU,WAAb,CAAyB;AAAA;AAAA,kCAAQR,EAAR,CAAWC,MAAX,CAAkBC,oBAA3C,EAAiE;AAAEO,YAAAA,aAAa,EAAE;AAAjB,WAAjE;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,sCAAUC,YAAxB;AACH,SAd6B,CAgB9B;;;AACAL,QAAAA,eAAe,CAACM,GAAD,EAA0B;AACrC,gBAAMC,WAAW,GAAGD,GAAG,CAACE,IAAJ,CAASC,WAAT,CAAqBnB,KAAzC;;AACA,kBAAQgB,GAAG,CAACE,IAAJ,CAASC,WAAT,CAAqBC,MAA7B;AACI,iBAAK;AAAA;AAAA,oCAAQC,IAAR,CAAaC,kBAAb,CAAgCC,sBAArC;AACIN,cAAAA,WAAW,CAACO,OAAZ,CAAoBC,OAAO,IAAI;AAC3B,oBAAI,KAAKC,aAAL,CAAmBD,OAAO,CAACE,IAA3B,CAAJ,EAAsC;AAClC;AAAA;AAAA,4CAAS,SAAT,EAAqB,aAAYF,OAAO,CAACE,IAAK,gBAA9C;AACA;AACH;;AACD,qBAAK3B,KAAL,CAAW4B,IAAX,CAAgBH,OAAhB;AACH,eAND;AAOA;;AACJ,iBAAK;AAAA;AAAA,oCAAQJ,IAAR,CAAaC,kBAAb,CAAgCO,yBAArC;AACIZ,cAAAA,WAAW,CAACO,OAAZ,CAAoBC,OAAO,IAAI;AAC3B,sBAAMK,GAAG,GAAG,KAAK9B,KAAL,CAAW+B,SAAX,CAAqBC,CAAC,IAAI;AAClC,yBAAOA,CAAC,CAACL,IAAF,CAAOM,EAAP,CAAUR,OAAO,CAACE,IAAlB,CAAP;AACH,iBAFW,CAAZ;;AAGA,oBAAIG,GAAG,GAAG,CAAV,EAAa;AACT;AAAA;AAAA,4CAAS,SAAT,EAAqB,aAAYL,OAAO,CAACE,IAAK,YAA9C;AACA;AACH;;AACD,qBAAK3B,KAAL,CAAWkC,MAAX,CAAkBJ,GAAlB,EAAuB,CAAvB;AACH,eATD;AAUA;;AACJ,iBAAK;AAAA;AAAA,oCAAQT,IAAR,CAAaC,kBAAb,CAAgCa,yBAArC;AACI,mBAAKnC,KAAL,GAAa,KAAKA,KAAL,CAAWoC,GAAX,CAAeJ,CAAC,IAAI;AAC7B,sBAAMK,IAAI,GAAGpB,WAAW,CAACqB,IAAZ,CAAiBD,IAAI,IAAIA,IAAI,CAACV,IAAL,CAAUM,EAAV,CAAaD,CAAC,CAACL,IAAf,CAAzB,CAAb;AACA,uBAAOU,IAAI,GAAGA,IAAH,GAAUL,CAArB;AACH,eAHY,CAAb;AAIA;;AACJ;AACI;AAAA;AAAA,wCAAS,SAAT,EAAqB,8BAA6BhB,GAAG,CAACE,IAAJ,CAASC,WAAT,CAAqBC,MAAO,EAA9E;AACA;AA9BR;;AAgCA;AAAA;AAAA,oCAASN,IAAT,CAAc;AAAA;AAAA,sCAAUC,YAAxB;AACH;;AAEDW,QAAAA,aAAa,CAACC,IAAD,EAAwC;AACjD,iBAAO,KAAK3B,KAAL,CAAWsC,IAAX,CAAgBN,CAAC,IAAIA,CAAC,CAACL,IAAF,CAAOM,EAAP,CAAUN,IAAV,CAArB,KAAyC,IAAhD;AACH;;AAEMpB,QAAAA,gBAAgB,CAACS,GAAD,EAAgC;AACnD,eAAKhB,KAAL,GAAagB,GAAG,CAACE,IAAJ,CAASL,aAAT,CAAuBb,KAAvB,IAAgC,EAA7C;AACH;;AAEMuC,QAAAA,MAAM,GAAS,CACrB;;AA/D6B,O", "sourcesContent": ["import Long from 'long';\nimport csproto from '../../AutoGen/PB/cs_proto.js';\nimport { EventMgr } from '../../event/EventManager';\nimport { MyApp } from '../../MyApp';\nimport { logError } from '../../Utils/Logger';\nimport { DataEvent } from '../DataEvent';\nimport { IData } from \"../DataManager\";\nexport class Bag implements IData {\n    // 物品槽位\n    items: csproto.cs.ICSItem[] = [];\n\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, this.onGetItemListMsg.bind(this))\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_UPDATE_ITEM, this.onUpdateItemMsg.bind(this))\n        this.refreshItems();\n    }\n\n    // 刷新物品 \n    refreshItems() {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ITEM_LIST, { get_item_list: {} })\n        EventMgr.emit(DataEvent.ItemsRefresh)\n    }\n\n    //物品推送\n    onUpdateItemMsg(msg: csproto.cs.IS2CMsg) {\n        const updateItems = msg.body.update_item.items\n        switch (msg.body.update_item.reason) {\n            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_ADD:\n                updateItems.forEach(element => {\n                    if (this.getItemByGuid(element.guid)) {\n                        logError(\"PlaneUI\", `item guid ${element.guid} already exist`)\n                        return\n                    }\n                    this.items.push(element)\n                });\n                break;\n            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_REMOVE:\n                updateItems.forEach(element => {\n                    const idx = this.items.findIndex(v => {\n                        return v.guid.eq(element.guid)\n                    })\n                    if (idx < 0) {\n                        logError(\"PlaneUI\", `item guid ${element.guid} not exist`)\n                        return\n                    }\n                    this.items.splice(idx, 1)\n                });\n                break;\n            case csproto.comm.ITEM_UPDATE_REASON.ITEM_UPDATE_REASON_CHANGE:\n                this.items = this.items.map(v => {\n                    const item = updateItems.find(item => item.guid.eq(v.guid))\n                    return item ? item : v\n                })\n                break;\n            default:\n                logError(\"PlaneUI\", `unknown item update reason ${msg.body.update_item.reason}`)\n                break;\n        }\n        EventMgr.emit(DataEvent.ItemsRefresh)\n    }\n\n    getItemByGuid(guid: Long): csproto.cs.ICSItem | null {\n        return this.items.find(v => v.guid.eq(guid)) || null\n    }\n\n    public onGetItemListMsg(msg: csproto.cs.IS2CMsg): void {\n        this.items = msg.body.get_item_list.items || []\n    }\n\n    public update(): void {\n    }\n}\n"]}