System.register(["__unresolved_0", "cc", "long", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Long, csproto, IMgr, logDebug, logError, logInfo, logWarn, LoginInfo, _dec, _class2, _crd, ccclass, NetStatus, NetMgr;

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "../Utils/Logger", _context.meta, extras);
  }

  _export("LoginInfo", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      IMgr = _unresolved_3.IMgr;
    }, function (_unresolved_4) {
      logDebug = _unresolved_4.logDebug;
      logError = _unresolved_4.logError;
      logInfo = _unresolved_4.logInfo;
      logWarn = _unresolved_4.logWarn;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d7f90yKiiBA+ZpKmPMVdOF6", "NetMgr", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("NetStatus", NetStatus = /*#__PURE__*/function (NetStatus) {
        NetStatus[NetStatus["NotConnect"] = 0] = "NotConnect";
        NetStatus[NetStatus["Connecting"] = 1] = "Connecting";
        NetStatus[NetStatus["ServerPassed"] = 2] = "ServerPassed";
        NetStatus[NetStatus["Disconnected"] = 3] = "Disconnected";
        NetStatus[NetStatus["Connected"] = 4] = "Connected";
        return NetStatus;
      }({}));

      _export("LoginInfo", LoginInfo = class LoginInfo {
        constructor() {
          this.accountType = void 0;
          this.code = void 0;
          this.serverAddr = void 0;
        }

      });

      _export("NetMgr", NetMgr = (_dec = ccclass("NetMgr"), _dec(_class2 = class NetMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor(...args) {
          super(...args);
          this._websocket = null;
          this._status = NetStatus.NotConnect;
          this.loginInfo = null;
          this._reconnectAttempts = 0;
          this._maxReconnectAttempts = 5;
          this._reconnectDelay = 3000;
          // 3 seconds
          this._heartbeatInterval = 3000;
          // 30 seconds
          this._heartbeatTimer = 0;
          this._lastHeartbeatTime = 0;
          this._messageHandlers = new Map();
          this._messageQueue = [];
          this._currentSeq = 1;
          this.registerInInitHandlerBound = new Map();
        }

        initRegistered(msgId, handler) {
          handler = handler.bind(this);
          this.registerInInitHandlerBound.set(msgId, handler);
          this.registerHandler(msgId, handler);
        }

        uninitRegistered() {
          this.registerInInitHandlerBound.forEach((handler, msgId) => {
            this.unregisterHandler(msgId, handler);
          });
          this.registerInInitHandlerBound.clear();
        }

        init() {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Network manager initialized");
          this.initRegistered((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_SESSION, this.onGetSession);
          this.initRegistered((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_HEARTBEAT, this.onHeartbeat);
          this.initRegistered((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole);
        }

        unInit() {
          this.uninitRegistered();
          this.disconnect();

          this._messageHandlers.clear();

          this._messageQueue.length = 0;
          super.unInit();
        }

        onUpdate(dt) {
          this._heartbeatTimer += dt * 1000; // Send heartbeat

          if (this._status === NetStatus.Connected && this._heartbeatTimer >= this._heartbeatInterval) {
            this.sendHeartbeat();
            this._heartbeatTimer = 0;
          } // Check connection timeout


          if (this._status === NetStatus.Connected && Date.now() - this._lastHeartbeatTime > this._heartbeatInterval * 2) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "Connection timeout, attempting to reconnect");
            this.handleDisconnection();
          }
        }
        /**
         * Connect to server
         * @param url WebSocket server URL
         */


        connect() {
          if (this._status === NetStatus.Connecting || this._status === NetStatus.Connected) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "Already connecting or connected");
            return;
          }

          this._status = NetStatus.Connecting;
          this._reconnectAttempts = 0;
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `Connecting to ${this.loginInfo.serverAddr}`);
          this.createWebSocket();
        }

        login(info) {
          this.loginInfo = info;
          this.connect();
        }
        /**
         * Disconnect from server
         */


        disconnect() {
          if (this._websocket) {
            this._websocket.close();

            this._websocket = null;
          }

          this._status = NetStatus.Disconnected;
          this._reconnectAttempts = 0;
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Disconnected from server");
        }
        /**
         * Get current connection status
         */


        getStatus() {
          return this._status;
        }
        /**
         * Check if connected
         */


        isConnected() {
          return this._status === NetStatus.Connected;
        }
        /**
         * Create WebSocket connection
         */


        createWebSocket() {
          try {
            this._websocket = new WebSocket(this.loginInfo.serverAddr);
            this._websocket.binaryType = 'arraybuffer';
            this._websocket.onopen = this.onWebSocketOpen.bind(this);
            this._websocket.onmessage = this.onWebSocketMessage.bind(this);
            this._websocket.onclose = this.onWebSocketClose.bind(this);
            this._websocket.onerror = this.onWebSocketError.bind(this);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to create WebSocket: ${err}`);
            this.handleDisconnection();
          }
        }
        /**
         * WebSocket open event handler
         */


        onWebSocketOpen(_event) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "WebSocket connected");
          this._status = NetStatus.Connected;
          this._reconnectAttempts = 0;
          this._lastHeartbeatTime = Date.now();
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_SESSION, {
            get_session: {
              account_type: this.loginInfo.accountType,
              // 账号类型
              platform: (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).cs.PLATFORM.PLATFORM_EDITOR,
              // 平台类型
              code: this.loginInfo.code,
              // 账号名
              version: 1 // 版本号

            }
          });
        }
        /**
         * WebSocket message event handler
         */


        onWebSocketMessage(event) {
          try {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `WebSocket message received ${event}`);
            const buffer = new Uint8Array(event.data);
            this.handleMessage(buffer);
            this._lastHeartbeatTime = Date.now();
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to handle message: ${err}`);
          }
        }
        /**
         * WebSocket close event handler
         */


        onWebSocketClose(event) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `WebSocket closed: ${event.code} - ${event.reason}`);
          this.handleDisconnection();
        }
        /**
         * WebSocket error event handler
         */


        onWebSocketError(_event) {
          (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
            error: Error()
          }), logError) : logError)("NetMgr", "WebSocket error occurred");
          this.handleDisconnection();
        }
        /**
         * Handle disconnection and attempt reconnection
         */


        handleDisconnection() {
          if (this._websocket) {
            this._websocket.close();

            this._websocket = null;
          }

          this._status = NetStatus.NotConnect; // Attempt reconnection if not manually disconnected

          if (this._reconnectAttempts < this._maxReconnectAttempts) {
            this._reconnectAttempts++;
            (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
              error: Error()
            }), logInfo) : logInfo)("NetMgr", `Attempting reconnection ${this._reconnectAttempts}/${this._maxReconnectAttempts}`);
            setTimeout(() => {
              if (this.loginInfo.serverAddr && this._status !== NetStatus.Disconnected) {
                this.connect();
              }
            }, this._reconnectDelay);
          } else {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", "Max reconnection attempts reached");
            this._status = NetStatus.Disconnected;
          }
        }
        /**
         * Process queued messages
         */


        processMessageQueue() {
          while (this._messageQueue.length > 0 && this.isConnected()) {
            const message = this._messageQueue.shift();

            if (message) {
              this.sendRawMessage(message);
            }
          }
        }
        /**
         * Handle incoming message
         */


        handleMessage(buffer) {
          try {
            // Parse message header (assuming first 4 bytes are message ID)
            var msg = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.S2CMsg.decode(buffer);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `Received message ${JSON.stringify(msg)}`);
            this.dispatchMessage(msg);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to parse message: ${err}`);
          }
        }
        /**
         * Dispatch message to registered handlers
         */


        dispatchMessage(msg) {
          const handlers = this._messageHandlers.get(msg.cmd);

          if (handlers && handlers.length > 0) {
            try {
              handlers.forEach(handler => {
                try {
                  handler(msg);
                } catch (err) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)("NetMgr", `Handler error for msgId ${msg.cmd}: ${err.stack}`);
                }
              });
            } catch (err) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("NetMgr", `Failed to decode message ${msg.cmd}: ${err.stack}`);
            }
          } else {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `No handler registered for msgId: ${msg.cmd}}`);
          }
        }
        /**
         * Decode protobuf message based on message ID
         */


        decodeProtobufMessage(_msgId, data) {
          // This is a simplified example - you would need to map msgId to specific protobuf types
          // For now, return the raw data
          return data;
        }
        /**
         * Register message handler
         */


        registerHandler(msgId, handler) {
          if (!this._messageHandlers.has(msgId)) {
            this._messageHandlers.set(msgId, []);
          }

          this._messageHandlers.get(msgId).push(handler);

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `Registered handler for msgId: ${msgId}`);
        }
        /**
         * Unregister message handler
         */


        unregisterHandler(msgId, handler) {
          const handlers = this._messageHandlers.get(msgId);

          if (handlers) {
            const index = handlers.indexOf(handler);

            if (index !== -1) {
              handlers.splice(index, 1);
              (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                error: Error()
              }), logInfo) : logInfo)("NetMgr", `Unregistered handler for msgId: ${msgId}`); // Clean up empty handler arrays to prevent memory leaks

              if (handlers.length === 0) {
                this._messageHandlers.delete(msgId);

                (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                  error: Error()
                }), logInfo) : logInfo)("NetMgr", `Removed empty handler array for msgId: ${msgId}`);
              }
            } else {
              (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
                error: Error()
              }), logWarn) : logWarn)("NetMgr", `Handler not found for msgId: ${msgId}`);
            }
          } else {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `No handlers registered for msgId: ${msgId}`);
          }
        }
        /**
         * Send protobuf message
         */


        sendMessage(msgId, message) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `sendMessage ${msgId} ${JSON.stringify((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsgBody.create(message))}`);

          try {
            // Encode protobuf message
            const netMessage = this.encodeProtobufMessage(msgId, message);

            if (this.isConnected()) {
              this.sendRawMessage(netMessage);
            } else {
              // Queue message if not connected
              this._messageQueue.push(netMessage);

              (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
                error: Error()
              }), logInfo) : logInfo)("NetMgr", `Queued message ${msgId} (not connected)`);
            }
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to send message ${msgId}: ${err}`);
          }
        }
        /**
         * Send raw message over WebSocket
         */


        sendRawMessage(message) {
          if (!this._websocket || this._websocket.readyState !== WebSocket.OPEN) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "WebSocket not ready for sending");
            return;
          }

          try {
            this._websocket.send(message.data);

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("NetMgr", `Sent message ${message.msgId}, size: ${message.data.byteLength}`);
          } catch (err) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("NetMgr", `Failed to send raw message: ${err}`);
          }
        }
        /**
         * Encode protobuf message
         */


        encodeProtobufMessage(_msgId, message) {
          // This is a simplified example - you would need to map msgId to specific protobuf types
          // For now, if message is already Uint8Array, return it; otherwise encode as ClientData
          if (message instanceof Uint8Array) {
            const netMessage = {
              msgId: _msgId,
              seq: this._currentSeq++,
              data: message
            };
            return netMessage;
          }

          var msg = new (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsg();
          msg.cmd = _msgId;
          msg.seq = this._currentSeq++;
          msg.body = message;
          const clientData = (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.C2SMsg.encode(msg).finish();
          const netMessage = {
            msgId: _msgId,
            seq: this._currentSeq++,
            data: clientData
          };
          return netMessage;
        }
        /**
         * Send heartbeat message
         */


        sendHeartbeat() {
          // Send a simple heartbeat message (you can define a specific heartbeat message type)
          const heartbeatData = {
            heartbeat: {
              clent_time: (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
                error: Error()
              }), Long) : Long).fromNumber(Date.now()),
              // 客户端时间
              is_fighting: 0 // 是否战斗中

            }
          };
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_HEARTBEAT, heartbeatData);
        }
        /**
         * Clear all message handlers
         */


        clearAllHandlers() {
          this._messageHandlers.clear();

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", "Cleared all message handlers");
        }
        /**
         * Get message queue length
         */


        getQueueLength() {
          return this._messageQueue.length;
        }

        onHeartbeat(msg) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `onHeartbeat ${msg}`);
        }

        onGetSession(msg) {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("NetMgr", `onGetSession ${msg}`);

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetSession failed ${msg.ret_code}`);
            return;
          }

          var sessionRsp = msg.body.get_session;

          if (!sessionRsp) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetSession data is null");
            return;
          }

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("NetMgr", `onGetSession ${sessionRsp.openid}:${sessionRsp.uin_list}`);
          this.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, {
            get_role: {
              uin: (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
                error: Error()
              }), Long) : Long).ZERO,
              area_id: 0
            }
          });
        }

        onGetRole(msg) {
          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", `onGetRole failed ${msg.ret_code}`);
            return;
          }

          var roleRsp = msg.body.get_role;

          if (!roleRsp) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("NetMgr", "onGetRole data is null");
            return;
          }
        }

        disableReconnect() {
          this._reconnectAttempts = this._maxReconnectAttempts;
        }

      }) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=adac3babafbcaaff82b91cc080fe88b86548f97a.js.map