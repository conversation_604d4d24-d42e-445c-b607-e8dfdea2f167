System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, v2, mat4, Mat4, Vec2, rect, Graphics, director, Node, view, Color, ColliderGroupType, ColliderType, Intersection, QuadTree, FColliderManager, _crd, tempVec2, tempMat4, tempArr, collisionState, ColliderTriggerFuncs, CollisionType, CollisionMatrix;

  function obbApplyMatrix(rect, mat4, out_bl, out_tl, out_tr, out_br) {
    let x = rect.x;
    let y = rect.y;
    let width = rect.width;
    let height = rect.height; // 初始化一个长度为16的数组来存储矩阵元素

    let mat4m = new Array(16); // 或者使用 Float32Array(16) 如果你需要TypedArray
    // 将矩阵数据提取到数组中

    Mat4.toArray(mat4m, mat4);
    let m00 = mat4m[0],
        m01 = mat4m[1],
        m04 = mat4m[4],
        m05 = mat4m[5];
    let m12 = mat4m[12],
        m13 = mat4m[13];
    let tx = m00 * x + m04 * y + m12;
    let ty = m01 * x + m05 * y + m13;
    let xa = m00 * width;
    let xb = m01 * width;
    let yc = m04 * height;
    let yd = m05 * height;
    out_tl.x = tx;
    out_tl.y = ty;
    out_tr.x = xa + tx;
    out_tr.y = xb + ty;
    out_bl.x = yc + tx;
    out_bl.y = yd + ty;
    out_br.x = xa + yc + tx;
    out_br.y = xb + yd + ty;
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "./FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCircleCollider(extras) {
    _reporterNs.report("FCircleCollider", "./FCircleCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderType(extras) {
    _reporterNs.report("ColliderType", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFPolygonCollider(extras) {
    _reporterNs.report("FPolygonCollider", "./FPolygonCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIntersection(extras) {
    _reporterNs.report("Intersection", "./Intersection", _context.meta, extras);
  }

  function _reportPossibleCrUseOfQuadTree(extras) {
    _reporterNs.report("QuadTree", "./QuadTree", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
      mat4 = _cc.mat4;
      Mat4 = _cc.Mat4;
      Vec2 = _cc.Vec2;
      rect = _cc.rect;
      Graphics = _cc.Graphics;
      director = _cc.director;
      Node = _cc.Node;
      view = _cc.view;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      ColliderGroupType = _unresolved_2.ColliderGroupType;
      ColliderType = _unresolved_2.ColliderType;
    }, function (_unresolved_3) {
      Intersection = _unresolved_3.Intersection;
    }, function (_unresolved_4) {
      QuadTree = _unresolved_4.QuadTree;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5f0cc1FenRA16TtOTuamahq", "FColliderManager", undefined);

      __checkObsolete__(['_decorator', 'v2', 'mat4', 'Rect', 'Mat4', 'Vec2', 'rect', 'Graphics', 'director', 'Scheduler', 'game', 'Node', 'view', 'Color', 'Director', 'log']);

      tempVec2 = v2();
      tempMat4 = mat4();
      tempArr = [];
      collisionState = 0;

      _export("ColliderTriggerFuncs", ColliderTriggerFuncs = []);

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Circle | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Circle] = (self, other) => {
        let selfCircle = self;
        let otherCircle = other;
        return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
          error: Error()
        }), Intersection) : Intersection).circleCircle(selfCircle, otherCircle);
      };

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Circle | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Box] = (self, other) => {
        let selfCircle = self;
        let otherBox = other;
        return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
          error: Error()
        }), Intersection) : Intersection).polygonCircle(otherBox.worldPoints, selfCircle);
      };

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Circle | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Polygon] = (self, other) => {
        let selfCircle = self;
        let otherPolygon = other;
        return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
          error: Error()
        }), Intersection) : Intersection).polygonCircle(otherPolygon.worldPoints, selfCircle);
      };

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Box | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Box] = (self, other) => {
        let selfBox = self;
        let otherBox = other;

        if (selfBox.node.angle === 0 && otherBox.node.angle == 0) {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).rectRect(selfBox.aabb, otherBox.aabb);
        } else {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
        }
      };

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Box | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Polygon] = (self, other) => {
        let selfBox = self;
        let otherBox = other;

        if (!otherBox.isConvex) {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);
        } else {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
        }
      };

      ColliderTriggerFuncs[(_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Polygon | (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
        error: Error()
      }), ColliderType) : ColliderType).Polygon] = (self, other) => {
        let selfBox = self;
        let otherBox = other;

        if (!otherBox.isConvex || !selfBox.isConvex) {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).polygonPolygon(selfBox.worldPoints, otherBox.worldPoints);
        } else {
          return (_crd && Intersection === void 0 ? (_reportPossibleCrUseOfIntersection({
            error: Error()
          }), Intersection) : Intersection).satPolygonPolygon(selfBox.worldPoints, otherBox.worldPoints, selfBox.worldEdge, otherBox.worldEdge);
        }
      };

      CollisionType = /*#__PURE__*/function (CollisionType) {
        CollisionType["onEnter"] = "onCollisionEnter";
        CollisionType["onStay"] = "onCollisionStay";
        CollisionType["onExit"] = "onCollisionExit";
        return CollisionType;
      }(CollisionType || {});

      CollisionMatrix = {
        [`${(_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
          error: Error()
        }), ColliderGroupType) : ColliderGroupType).BULLET_SELF}_${(_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
          error: Error()
        }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL}`]: true,
        [`${(_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
          error: Error()
        }), ColliderGroupType) : ColliderGroupType).BULLET_ENEMY}_${(_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
          error: Error()
        }), ColliderGroupType) : ColliderGroupType).PLAYER}`]: true
      };

      _export("default", FColliderManager = class FColliderManager {
        static get instance() {
          if (!this._instance) {
            this._instance = new FColliderManager();
          }

          return this._instance;
        }

        constructor() {
          this._onCollisionEnter = null;
          this._onCollisionStay = null;
          this._onCollisionExit = null;
          this._tree = null;
          this._frameId = 0;
          //是否要重新建树
          this._treeDirty = true;
          this._maxDepth = 6;
          this._maxChildren = 12;
          this._treeRect = rect(0, 0, view.getVisibleSize().width, view.getVisibleSize().height);
          this._enable = false;
          this.collisionPairs = new Map();
          this._colliders = [];
          this._enableDebugDraw = false;
          this._enableQuadTreeDraw = false;
          this._debugDrawer = null;
          this._frameId = 0;
          this._tree = new (_crd && QuadTree === void 0 ? (_reportPossibleCrUseOfQuadTree({
            error: Error()
          }), QuadTree) : QuadTree)(this._treeRect, 0, this._maxDepth, this._maxChildren);
        }

        get maxDepth() {
          return this._maxDepth;
        }

        set maxDepth(value) {
          if (value != this._maxDepth) {
            this._maxDepth = value;
            this._treeDirty = true;
          }
        }

        get maxChildren() {
          return this._maxChildren;
        }

        set maxChildren(value) {
          if (this._maxChildren != value) {
            this._maxChildren = value;
            this._treeDirty = true;
          }
        }

        get treeRect() {
          return this._treeRect;
        } //设置四叉树大小


        set treeRect(value) {
          if (this._treeRect) {
            if (this._treeRect.equals(value)) return;
          }

          this._treeRect.set(value);

          this._treeDirty = false;
        }

        get enable() {
          return this._enable;
        }

        set enable(value) {
          this._enable = value;

          if (value) {// director.getScheduler().enableForTarget(this);
            // director.getScheduler().scheduleUpdate(this, Scheduler.PRIORITY_NON_SYSTEM, false);
            // director.on(Director.EVENT_BEFORE_UPDATE, this.update, this);
          }
        }

        addCollider(collider) {
          let colliders = this._colliders;
          this.initCollider(collider);
          colliders.push(collider);
        }

        removeCollider(collider) {
          for (let i = this._colliders.length - 1; i >= 0; i--) {
            let c = this._colliders[i];

            if (collider.colliderId === c.colliderId) {
              this._colliders.splice(i, 1);
            }
          }
        }

        initCollider(collider) {
          collider.initCollider();
        }

        setGlobalColliderEnterCall(func) {
          this._onCollisionEnter = func;
        }

        setGlobalColliderStayCall(func) {
          this._onCollisionStay = func;
        }

        setGlobalColliderExitCall(func) {
          this._onCollisionExit = func;
        }

        updateCollider(c) {
          c.node.getWorldMatrix(tempMat4);

          if (c.type === (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Box) {
            let collider = c;
            let size = collider.size;
            collider.aabb.x = collider.offset.x - size.width / 2;
            collider.aabb.y = collider.offset.y - size.height / 2;
            collider.aabb.width = size.width;
            collider.aabb.height = size.height;
            let wps = collider.worldPoints;
            let wp0 = wps[0],
                wp1 = wps[1],
                wp2 = wps[2],
                wp3 = wps[3];
            obbApplyMatrix(collider.aabb, tempMat4, wp0, wp1, wp2, wp3);
            let minx = Math.min(wp0.x, wp1.x, wp2.x, wp3.x);
            let miny = Math.min(wp0.y, wp1.y, wp2.y, wp3.y);
            let maxx = Math.max(wp0.x, wp1.x, wp2.x, wp3.x);
            let maxy = Math.max(wp0.y, wp1.y, wp2.y, wp3.y);
            let worldEdge = collider.worldEdge;

            for (let i = 0, l = wps.length; i < l; i++) {
              if (!worldEdge[i]) worldEdge[i] = v2();
              Vec2.subtract(worldEdge[i], wps[(i + 1) % l], wps[i]);
            }

            collider.aabb.x = minx;
            collider.aabb.y = miny;
            collider.aabb.width = maxx - minx;
            collider.aabb.height = maxy - miny;
          } else if (c.type == (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Circle) {
            let collider = c;
            Vec2.transformMat4(tempVec2, collider.offset, tempMat4);
            collider.worldPosition.x = tempVec2.x;
            collider.worldPosition.y = tempVec2.y;
            let mm = new Array(16); // Create an array to hold the matrix data

            Mat4.toArray(mm, tempMat4); // Extract the matrix data into the array

            let tempx = mm[12],
                tempy = mm[13];
            mm[12] = mm[13] = 0;
            Mat4.fromArray(tempMat4, mm);
            tempVec2.x = collider.radius;
            tempVec2.y = 0;
            Vec2.transformMat4(tempVec2, tempVec2, tempMat4);
            let d = Math.sqrt(tempVec2.x * tempVec2.x + tempVec2.y * tempVec2.y);
            collider.worldRadius = d;
            collider.aabb.x = collider.worldPosition.x - d;
            collider.aabb.y = collider.worldPosition.y - d;
            collider.aabb.width = d * 2;
            collider.aabb.height = d * 2;
            mm[12] = tempx;
            mm[13] = tempy;
          } else if (c.type == (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Polygon) {
            let collider = c;
            let points = collider.points;
            let worldPoints = collider.worldPoints;
            let worldEdge = collider.worldEdge;
            worldPoints.length = points.length;
            let minx = Number.MAX_SAFE_INTEGER,
                miny = Number.MAX_SAFE_INTEGER,
                maxx = -Number.MAX_SAFE_INTEGER,
                maxy = -Number.MAX_SAFE_INTEGER;

            for (let i = 0, l = points.length; i < l; i++) {
              if (!worldPoints[i]) {
                worldPoints[i] = v2();
              }

              tempVec2.x = points[i].x + collider.offset.x;
              tempVec2.y = points[i].y + collider.offset.y;
              Vec2.transformMat4(tempVec2, tempVec2, tempMat4);
              let x = tempVec2.x;
              let y = tempVec2.y;
              worldPoints[i].set(tempVec2);
              if (x > maxx) maxx = x;
              if (x < minx) minx = x;
              if (y > maxy) maxy = y;
              if (y < miny) miny = y;
            }

            if (c.isConvex) {
              for (let i = 0, l = worldPoints.length; i < l; i++) {
                if (!worldEdge[i]) worldEdge[i] = v2();
                Vec2.subtract(worldEdge[i], worldPoints[(i + 1) % l], worldPoints[i]);
              }
            }

            collider.aabb.x = minx;
            collider.aabb.y = miny;
            collider.aabb.width = maxx - minx;
            collider.aabb.height = maxy - miny;
          }
        }

        shouldCollide(c1, c2) {
          if (c1.groupType === (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).DEFAULT || c2.groupType === (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).DEFAULT) {
            return true;
          }

          return CollisionMatrix[`${c1.groupType}_${c2.groupType}`] || CollisionMatrix[`${c2.groupType}_${c1.groupType}`];
        }

        isOutOfScreen(aabb) {
          const visibleSize = view.getVisibleSize();
          const screenLeft = -200;
          const screenRight = visibleSize.width + 200;
          const screenTop = visibleSize.height + 200;
          const screenBottom = -200; // 判断是否超出屏幕边界

          return aabb.x + aabb.width < screenLeft || // 超出屏幕左边
          aabb.x > screenRight || // 超出屏幕右边
          aabb.y + aabb.height < screenBottom || // 超出屏幕下边
          aabb.y > screenTop // 超出屏幕上边
          ;
        }

        update(dt) {
          if (!this.enable) return; // console.time("碰撞");

          this.oneTest(dt); // console.timeEnd("碰撞");
        }
        /**
         * 1.清空四叉树
         * 2.更新Collider
         * 3.插入四叉树
         * 4.筛选重复碰撞组(暂无)
         * 5.碰撞检测
         * @param dt 
         * @returns 
         */


        oneTest(dt) {
          let timeNow = Date.now();
          this._frameId++;

          if (this._treeDirty) {
            this._tree = new (_crd && QuadTree === void 0 ? (_reportPossibleCrUseOfQuadTree({
              error: Error()
            }), QuadTree) : QuadTree)(this._treeRect, 0, this._maxDepth, this._maxChildren);
            this._treeDirty = false;
          }

          this._tree.clear();

          for (let i = this._colliders.length - 1; i >= 0; i--) {
            let collider = this._colliders[i];

            if (!collider || !collider.isValid) {
              this._colliders.splice(i, 1);

              continue;
            }

            if (!collider.isEnable) {
              continue;
            }

            this.updateCollider(this._colliders[i]);

            if (this.isOutOfScreen(collider.aabb)) {
              var _collider$entity;

              (_collider$entity = collider.entity) == null || _collider$entity.onOutScreen == null || _collider$entity.onOutScreen();
              continue;
            }

            this._tree.insert(this._colliders[i]);
          }

          tempArr.length = 0;

          this._tree.getAllNeedTestColliders(tempArr);

          for (let k = 0, klen = tempArr.length; k < klen; k++) {
            let _colliders = tempArr[k];

            for (let i = 0, len = _colliders.length; i < len; i++) {
              let icollider = _colliders[i];

              for (let j = i + 1; j < len; j++) {
                let jcollider = _colliders[j];
                if (!this.shouldCollide(icollider, jcollider)) continue;

                if (icollider.type < jcollider.type) {
                  collisionState = ColliderTriggerFuncs[icollider.type | jcollider.type](icollider, jcollider);
                } else {
                  collisionState = ColliderTriggerFuncs[jcollider.type | icollider.type](jcollider, icollider);
                }
                /*
                switch (icollider.type) {
                    case ColliderType.Circle:
                        if (jcollider.type === ColliderType.Circle) {
                            collisionState = Intersection.circleCircle(icollider as FCircleCollider, jcollider as FCircleCollider) ? 1 : 0;
                        } else if (jcollider.type === ColliderType.Box || jcollider.type === ColliderType.Polygon) {
                            collisionState = Intersection.polygonCircle((jcollider as FPolygonCollider).worldPoints, icollider as FCircleCollider) ? 1 : 0;
                        }
                        break;
                    case ColliderType.Box:
                        if (jcollider.type === ColliderType.Circle) {
                            collisionState = Intersection.polygonCircle((icollider as FPolygonCollider).worldPoints, jcollider as FCircleCollider) ? 1 : 0;
                        } else if (jcollider.type === ColliderType.Box) {
                            if (icollider.node.angle === 0 && jcollider.node.angle === 0) {
                                collisionState = Intersection.rectRect(icollider.aabb, jcollider.aabb) ? 1 : 0;
                            } else {
                                collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;
                            }
                        }
                        else if (jcollider.type === ColliderType.Polygon) {
                            if (!jcollider.isConvex) {
                                collisionState = Intersection.polygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints) ? 1 : 0;
                            }
                            else {
                                collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;
                            }
                        }
                        break;
                    case ColliderType.Polygon:
                        if (jcollider.type === ColliderType.Circle) {
                            collisionState = Intersection.polygonCircle((icollider as FPolygonCollider).worldPoints, jcollider as FCircleCollider) ? 1 : 0;
                        } else if (icollider.isConvex && jcollider.isConvex) {
                            collisionState = Intersection.satPolygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints, (icollider as FPolygonCollider).worldEdge, (jcollider as FPolygonCollider).worldEdge) ? 1 : 0;
                        } else {
                            collisionState = Intersection.polygonPolygon((icollider as FPolygonCollider).worldPoints, (jcollider as FPolygonCollider).worldPoints) ? 1 : 0;
                        }
                        break;
                }*/


                if (collisionState) {
                  let id = 0,
                      aid = icollider.colliderId,
                      bid = jcollider.colliderId;

                  if (aid < bid) {
                    id = aid;
                    aid = bid;
                    bid = id;
                  }

                  id = (aid * (aid + 1) >> 1) + bid - 1; //NOTE: 康拓配对函数

                  let pairs = this.collisionPairs;
                  let data = pairs.get(id);

                  if (data !== undefined) {
                    data.frameId = this._frameId;
                    data.state = CollisionType.onStay;
                  } else {
                    data = {
                      id: id,
                      colliderA: icollider,
                      colliderB: jcollider,
                      frameId: this._frameId,
                      state: CollisionType.onEnter
                    };
                    pairs.set(id, data);
                  }

                  this._doCollide(icollider, jcollider, data.state); // this._doCollide(jcollider, icollider, data.state);

                }
              }
            }
          }

          this.endTrigger();
          this.drawColliders();
          this.drawQuadTree(); // log("每帧碰撞检测耗时:",Date.now() - timeNow + "毫秒");
        }

        endTrigger() {
          const curFrameId = this._frameId;
          let len = this.collisionPairs.size;
          let entries = this.collisionPairs.values();
          let waitToDelete = [];

          for (let i = 0; i < len; i++) {
            const value = entries.next().value;
            let icollider = value.colliderA;
            let jcollider = value.colliderB;

            if (value.frameId !== curFrameId || !jcollider.node || !icollider.node || !jcollider.node.isValid || !icollider.node.isValid) {
              this._doCollide(icollider, jcollider, CollisionType.onExit); // this._doCollide(jcollider, icollider, CollisionType.onExit);


              waitToDelete.push(value.id);
            }
          }

          len = waitToDelete.length - 1;

          while (len >= 0) {
            this.collisionPairs.delete(waitToDelete[len]);
            len--;
          }

          waitToDelete.length = 0;
        }
        /**
         *更新两个碰撞体之前的碰撞信息,延迟到发生碰撞才创建
         *
         * @private
         * @param {*} collider1
         * @param {*} collider2
         * @memberof _ColliderManager
         */


        _doCollide(collider1, collider2, type) {
          if (!collider1 || !collider1.node) return; //@ts-ignore

          let comps1 = collider1.node._components;
          let comp;

          for (let i = 0, l = comps1.length; i < l; i++) {
            comp = comps1[i];

            if (comp[type]) {
              comp[type](collider2, collider1);
            }
          }

          if (this._onCollisionEnter && type === CollisionType.onEnter) {
            var _this$_onCollisionEnt;

            (_this$_onCollisionEnt = this._onCollisionEnter) == null || _this$_onCollisionEnt.call(this, collider1, collider2);
          } else if (this._onCollisionStay && type === CollisionType.onStay) {
            var _this$_onCollisionSta;

            (_this$_onCollisionSta = this._onCollisionStay) == null || _this$_onCollisionSta.call(this, collider1, collider2);
          } else if (this._onCollisionExit && type === CollisionType.onExit) {
            var _this$_onCollisionExi;

            (_this$_onCollisionExi = this._onCollisionExit) == null || _this$_onCollisionExi.call(this, collider1, collider2);
          }
        }

        get enableDebugDraw() {
          return this._enableDebugDraw;
        }

        set enableDebugDraw(value) {
          if (value && !this._enableDebugDraw) {
            this._checkDebugDrawValid();

            this._debugDrawer.node.active = true;
          } else if (!value && this._enableDebugDraw) {
            this._debugDrawer.clear();

            this._debugDrawer.node.active = false;
          }

          this._enableDebugDraw = value;
        }

        get enableQuadTreeDraw() {
          return this._enableQuadTreeDraw;
        }

        set enableQuadTreeDraw(value) {
          if (value && !this._enableQuadTreeDraw) {
            this._checkDebugDrawValid();

            this._debugDrawer.node.active = true;
          } else if (!value && this._enableQuadTreeDraw) {
            this._debugDrawer.clear();

            this._debugDrawer.node.active = false;
          }

          this._enableQuadTreeDraw = value;
        }

        _checkDebugDrawValid() {
          if (!this._debugDrawer || !this._debugDrawer.isValid) {
            let node = new Node('FCOLLISION_MANAGER_DEBUG_DRAW'); // node.setSli;

            director.addPersistRootNode(node);
            this._debugDrawer = node.addComponent(Graphics);
            this._debugDrawer.lineWidth = 5;
          }
        }

        drawColliders() {
          if (!this._enableDebugDraw) {
            return;
          }

          this._checkDebugDrawValid();

          let debugDrawer = this._debugDrawer;
          debugDrawer.clear();
          let colliders = this._colliders;

          for (let i = 0, l = colliders.length; i < l; i++) {
            let collider = colliders[i];
            debugDrawer.strokeColor = Color.RED;

            if (collider.type === (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
              error: Error()
            }), ColliderType) : ColliderType).Box || collider.type === (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
              error: Error()
            }), ColliderType) : ColliderType).Polygon) {
              //@ts-ignore
              let ps = collider.worldPoints;

              if (ps.length > 0) {
                Vec2.set(tempVec2, ps[0].x, ps[0].y);
                debugDrawer.moveTo(tempVec2.x, tempVec2.y);

                for (let j = 1; j < ps.length; j++) {
                  Vec2.set(tempVec2, ps[j].x, ps[j].y);
                  debugDrawer.lineTo(tempVec2.x, tempVec2.y);
                }

                debugDrawer.close();
                debugDrawer.stroke();
              }
            } else if (collider.type === (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
              error: Error()
            }), ColliderType) : ColliderType).Circle) {
              //@ts-ignore
              debugDrawer.circle(collider.worldPosition.x, collider.worldPosition.y, collider.worldRadius); // console.log(collider.worldPosition.toString(),collider.worldRadius)

              debugDrawer.stroke();
            }
          }
        }

        drawQuadTree() {
          if (!this._enableQuadTreeDraw) {
            return;
          }

          this._checkDebugDrawValid();

          let debugDrawer = this._debugDrawer;
          if (!this._enableDebugDraw) debugDrawer.clear();

          this._tree.render(debugDrawer);
        }

      });

      FColliderManager._instance = null;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=898e9a970c208b2eba37c6f0a52fd4fd95d8573a.js.map