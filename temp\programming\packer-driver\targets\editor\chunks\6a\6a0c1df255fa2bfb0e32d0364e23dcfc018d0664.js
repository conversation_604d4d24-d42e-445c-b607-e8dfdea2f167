System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, Prefab, CCString, assetManager, CCInteger, AudioClip, LevelEditorWaveParam, LevelEditorCondition, LevelEditorElemUI, LevelDataEventTriggerType, LevelDataEventTriggerLog, newTrigger, LevelDataEventCondtionType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _dec8, _dec9, _dec10, _dec11, _class4, _class5, _descriptor, _descriptor2, _crd, ccclass, property, executeInEditMode, LevelEditorEventTrigger, LevelEditorEventUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorWaveParam(extras) {
    _reporterNs.report("LevelEditorWaveParam", "./LevelEditorWaveUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorCondition(extras) {
    _reporterNs.report("LevelEditorCondition", "./LevelEditorCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorElemUI(extras) {
    _reporterNs.report("LevelEditorElemUI", "./LevelEditorElemUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTrigger(extras) {
    _reporterNs.report("LevelDataEventTrigger", "../../scripts/leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "../../scripts/leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "../../scripts/leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerAudio(extras) {
    _reporterNs.report("LevelDataEventTriggerAudio", "../../scripts/leveldata/trigger/LevelDataEventTriggerAudio", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "../../scripts/leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfnewTrigger(extras) {
    _reporterNs.report("newTrigger", "../../scripts/leveldata/trigger/newTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionType(extras) {
    _reporterNs.report("LevelDataEventCondtionType", "../../scripts/leveldata/condition/LevelDataEventCondtion", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventCondtionWave(extras) {
    _reporterNs.report("LevelDataEventCondtionWave", "../../scripts/leveldata/condition/LevelDataEventCondtionWave", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
      Prefab = _cc.Prefab;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      CCInteger = _cc.CCInteger;
      AudioClip = _cc.AudioClip;
    }, function (_unresolved_2) {
      LevelEditorWaveParam = _unresolved_2.LevelEditorWaveParam;
    }, function (_unresolved_3) {
      LevelEditorCondition = _unresolved_3.LevelEditorCondition;
    }, function (_unresolved_4) {
      LevelEditorElemUI = _unresolved_4.LevelEditorElemUI;
    }, function (_unresolved_5) {
      LevelDataEventTriggerType = _unresolved_5.LevelDataEventTriggerType;
    }, function (_unresolved_6) {
      LevelDataEventTriggerLog = _unresolved_6.LevelDataEventTriggerLog;
    }, function (_unresolved_7) {
      newTrigger = _unresolved_7.newTrigger;
    }, function (_unresolved_8) {
      LevelDataEventCondtionType = _unresolved_8.LevelDataEventCondtionType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9fa8bQG+gZMmpqlZsjy9rl1", "LevelEditorEventUI", undefined);

      __checkObsolete__(['_decorator', 'Enum', 'CCFloat', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'Asset', 'resources', 'assetManager', 'AssetManager', 'Sprite', 'SpriteFrame', 'SpriteAtlas', 'math', 'instantiate', 'Vec2', 'CCInteger', 'AudioClip']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelEditorEventTrigger", LevelEditorEventTrigger = (_dec = ccclass('LevelEditorEventTrigger'), _dec2 = property({
        type: Enum(_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
          error: Error()
        }), LevelDataEventTriggerType) : LevelDataEventTriggerType)
      }), _dec3 = property({
        type: CCString,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log;
        }

      }), _dec4 = property({
        type: AudioClip,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio;
        }

      }), _dec5 = property({
        type: Prefab,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec6 = property({
        type: CCInteger,

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec7 = property({
        type: [_crd && LevelEditorWaveParam === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveParam({
          error: Error()
        }), LevelEditorWaveParam) : LevelEditorWaveParam],

        visible() {
          return this.type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
            error: Error()
          }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave;
        }

      }), _dec(_class = (_class2 = class LevelEditorEventTrigger {
        constructor() {
          this._index = 0;
          this.data = new (_crd && LevelDataEventTriggerLog === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerLog({
            error: Error()
          }), LevelDataEventTriggerLog) : LevelDataEventTriggerLog)();
          this._audio = null;
          this._wave = null;
          this._params = [];
        }

        get type() {
          return this.data._type;
        }

        set type(value) {
          if (this.data._type != value) {
            this.data = (_crd && newTrigger === void 0 ? (_reportPossibleCrUseOfnewTrigger({
              error: Error()
            }), newTrigger) : newTrigger)({
              _type: value
            });
          }
        }

        get message() {
          return this.data.message;
        }

        set message(value) {
          this.data.message = value;
        }

        get audio() {
          return this._audio;
        }

        set audio(value) {
          this._audio = value;

          if (value) {
            this.data.audioUUID = value.uuid;
          } else {
            this.data.audioUUID = "";
          }
        }

        get wave() {
          return this._wave;
        }

        set wave(value) {
          this._wave = value;

          if (value) {
            this.data.waveUUID = value.uuid;
          } else {
            this.data.waveUUID = "";
          }
        }

        get planeID() {
          return this.data.planeID;
        }

        set planeID(value) {
          this.data.planeID = value;
        }

        get params() {
          return this._params;
        }

        set params(value) {
          this._params = value;
          let waveTrigger = this.data;
          waveTrigger.params = {};

          for (let p of this._params) {
            waveTrigger.params[p.name] = p.value;
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "type", [_dec2], Object.getOwnPropertyDescriptor(_class2.prototype, "type"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "message", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "message"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "audio", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "audio"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "wave", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "wave"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "planeID", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "planeID"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "params", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "params"), _class2.prototype)), _class2)) || _class));

      _export("LevelEditorEventUI", LevelEditorEventUI = (_dec8 = ccclass('LevelEditorEventUI'), _dec9 = executeInEditMode(), _dec10 = property([_crd && LevelEditorCondition === void 0 ? (_reportPossibleCrUseOfLevelEditorCondition({
        error: Error()
      }), LevelEditorCondition) : LevelEditorCondition]), _dec11 = property([LevelEditorEventTrigger]), _dec8(_class4 = _dec9(_class4 = (_class5 = class LevelEditorEventUI extends (_crd && LevelEditorElemUI === void 0 ? (_reportPossibleCrUseOfLevelEditorElemUI({
        error: Error()
      }), LevelEditorElemUI) : LevelEditorElemUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "conditions", _descriptor, this);

          _initializerDefineProperty(this, "triggers", _descriptor2, this);
        }

        update(dt) {
          for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;

            if (cond.type == (_crd && LevelDataEventCondtionType === void 0 ? (_reportPossibleCrUseOfLevelDataEventCondtionType({
              error: Error()
            }), LevelDataEventCondtionType) : LevelDataEventCondtionType).Wave && cond.data.targetElemID != "" && cond._targetElem == null) {
              const elems = this.node.scene.getComponentsInChildren(_crd && LevelEditorElemUI === void 0 ? (_reportPossibleCrUseOfLevelEditorElemUI({
                error: Error()
              }), LevelEditorElemUI) : LevelEditorElemUI);

              for (let elem of elems) {
                if (elem.elemID == cond.data.targetElemID) {
                  cond._targetElem = elem;
                  break;
                }
              }
            }
          }
        }

        initByLevelData(data) {
          super.initByLevelData(data);

          if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
              const condition = new (_crd && LevelEditorCondition === void 0 ? (_reportPossibleCrUseOfLevelEditorCondition({
                error: Error()
              }), LevelEditorCondition) : LevelEditorCondition)();
              condition._index = i;
              condition.data = data.conditions[i];
              this.conditions.push(condition);
            }
          }

          if (data.triggers) {
            for (let i = 0; i < data.triggers.length; i++) {
              const trigger = new LevelEditorEventTrigger();
              trigger._index = i;
              trigger.data = data.triggers[i];
              this.triggers.push(trigger);

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio) {
                let uuid = trigger.data.audioUUID;
                assetManager.loadAny({
                  uuid: uuid
                }, (err, audio) => {
                  if (err) {
                    console.error("LevelEditorEventUI initByLevelData load audio err", err);
                    return;
                  }

                  trigger._audio = audio;
                });
              }

              if (trigger.data._type == (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                error: Error()
              }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave) {
                let waveTrigger = trigger.data;
                let uuid = waveTrigger.waveUUID;
                assetManager.loadAny({
                  uuid: uuid
                }, (err, prefab) => {
                  if (err) {
                    console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                    return;
                  }

                  trigger._wave = prefab;
                });
                trigger._params = [];

                for (let k in waveTrigger.params) {
                  let param = new (_crd && LevelEditorWaveParam === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveParam({
                    error: Error()
                  }), LevelEditorWaveParam) : LevelEditorWaveParam)();
                  param.name = k;
                  param.value = waveTrigger.params[k];

                  trigger._params.push(param);
                }
              }
            }
          }
        }

        fillLevelData(data) {
          super.fillLevelData(data);
          data.conditions = [];
          this.conditions.forEach(cond => {
            if (cond != null) {
              data.conditions.push(cond.data);
            }
          });
          this.triggers.forEach(trigger => {
            if (trigger != null) {
              data.triggers.push(trigger.data);
            }
          });
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class5.prototype, "conditions", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class5.prototype, "triggers", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6a0c1df255fa2bfb0e32d0364e23dcfc018d0664.js.map