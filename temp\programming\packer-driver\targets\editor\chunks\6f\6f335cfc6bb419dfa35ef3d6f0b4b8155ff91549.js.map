{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "assetManager", "Component", "instantiate", "Node", "Vec2", "MyApp", "GameIns", "Wave", "LevelDataEventTriggerType", "ccclass", "TerrainsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelLayerUI", "backgrounds", "_offSetY", "terrainsNode", "dynamicNode", "waves", "events", "enableEvents", "onLoad", "initByLevelData", "data", "offSetY", "node", "setPosition", "_getOrAddNode", "console", "log", "terrains", "for<PERSON>ach", "terrain", "loadAny", "uuid", "err", "prefab", "error", "terrainNode", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "tick", "deltaTime", "speed", "prePosY", "getPosition", "length", "wave", "shift", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "waveUUID", "load", "waveComp", "getComponent", "waveManager", "addWaveByLevel", "event", "push", "i", "condResult", "cond", "conditions", "splice", "trigger", "triggers", "_type", "Log", "message", "Audio", "waveTriger", "node_parent", "name", "getChildByName"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,I,OAAAA,I;;AAKhEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,I,iBAAAA,I;;AAEAC,MAAAA,yB,iBAAAA,yB;;;;;;;;;OAIH;AAAEC,QAAAA;AAAF,O,GAAcV,U;AAEdW,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;8BAGTC,Y,WADZL,OAAO,CAAC,cAAD,C,gBAAR,MACaK,YADb,SACkCb,SADlC,CAC4C;AAAA;AAAA;AAAA,eACjCc,WADiC,GACT,EADS;AAAA,eAEhCC,QAFgC,GAEb,CAFa;AAEV;AAFU,eAIhCC,YAJgC,GAIN,IAJM;AAAA,eAKhCC,WALgC,GAKP,IALO;AAAA,eAMhCC,KANgC,GAMP,EANO;AAAA,eAOhCC,MAPgC,GAOL,EAPK;AAAA,eAQhCC,YARgC,GAQC,EARD;AAAA;;AAUxCC,QAAAA,MAAM,GAAS,CAEd;;AAEMC,QAAAA,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAA6C;AAAA;;AAC/D,eAAKT,QAAL,GAAgBS,OAAhB;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBF,OAAzB,EAAkC,CAAlC;AAEA,eAAKR,YAAL,GAAoB,KAAKW,aAAL,CAAmB,KAAKF,IAAxB,EAA8BhB,gBAA9B,CAApB;AACA,eAAKQ,WAAL,GAAmB,KAAKU,aAAL,CAAmB,KAAKF,IAAxB,EAA8Bf,eAA9B,CAAnB;AAEAkB,UAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA2B,kBAA3B;AACA,eAAKf,WAAL,GAAmB,EAAnB;AAEA,4BAAAS,IAAI,CAACO,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChCjC,YAAAA,YAAY,CAACkC,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,aAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,cAAd,EAA6B,0CAA7B,EAAyEF,GAAzE;AACA;AACH;;AAED,kBAAIG,WAAW,GAAGrC,WAAW,CAACmC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACZ,WAAZ,CAAwBM,OAAO,CAACO,QAAR,CAAiBC,CAAzC,EAA4CR,OAAO,CAACO,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAH,cAAAA,WAAW,CAACI,QAAZ,CAAqBV,OAAO,CAACW,KAAR,CAAcH,CAAnC,EAAsCR,OAAO,CAACW,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAH,cAAAA,WAAW,CAACM,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCZ,OAAO,CAACa,QAA/C;AACA,mBAAK7B,YAAL,CAAkB8B,QAAlB,CAA2BR,WAA3B;AACH,aAXD;AAYH,WAbD;AAcA,eAAKpB,KAAL,GAAa,CAAC,GAAGK,IAAI,CAACL,KAAT,CAAb;AACA,eAAKA,KAAL,CAAW6B,IAAX,CAAgB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACT,QAAF,CAAWE,CAAX,GAAeQ,CAAC,CAACV,QAAF,CAAWE,CAApD;AACA,eAAKtB,MAAL,GAAc,CAAC,GAAGI,IAAI,CAACJ,MAAT,CAAd;AACA,eAAKA,MAAL,CAAY4B,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACT,QAAF,CAAWE,CAAX,GAAeQ,CAAC,CAACV,QAAF,CAAWE,CAArD;AACH;;AAEMS,QAAAA,IAAI,CAACC,SAAD,EAAoBC,KAApB,EAAuC;AAC9C,gBAAMC,OAAO,GAAG,KAAK5B,IAAL,CAAU6B,WAAV,GAAwBb,CAAxC;AACA,eAAKhB,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyB2B,OAAO,GAAGF,SAAS,GAAGC,KAA/C,EAAsD,CAAtD;;AAEA,iBAAM,KAAKlC,KAAL,CAAWqC,MAAX,GAAoB,CAApB,IAAyB,KAAKrC,KAAL,CAAW,CAAX,EAAcqB,QAAd,CAAuBE,CAAvB,GAA2B,KAAKhB,IAAL,CAAU6B,WAAV,GAAwBb,CAAlF,EAAqF;AACjF,kBAAMe,IAAI,GAAG,KAAKtC,KAAL,CAAW,CAAX,CAAb;AACA,iBAAKA,KAAL,CAAWuC,KAAX;AACA,kBAAMC,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0DL,IAAI,CAACM,QAA/D,CAAb;AACA;AAAA;AAAA,gCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACvB,GAAD,EAAMC,MAAN,KAAwB;AAC5C,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,cAAd,EAA6B,4BAA7B,EAA2DF,GAA3D;AACA;AACH;;AACD,oBAAM6B,QAAQ,GAAG/D,WAAW,CAACmC,MAAD,CAAX,CAAoB6B,YAApB;AAAA;AAAA,+BAAjB;AACA;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA6CR,IAAI,CAACjB,QAAlD;AACH,aAPD;AAQH;;AACD,iBAAM,KAAKpB,MAAL,CAAYoC,MAAZ,GAAqB,CAArB,IAA0B,KAAKpC,MAAL,CAAY,CAAZ,EAAeoB,QAAf,CAAwBE,CAAxB,GAA4B,KAAKhB,IAAL,CAAU6B,WAAV,GAAwBb,CAApF,EAAuF;AACnF,kBAAM2B,KAAK,GAAG,KAAKjD,MAAL,CAAY,CAAZ,CAAd;AACA,iBAAKA,MAAL,CAAYsC,KAAZ;AACA,iBAAKrC,YAAL,CAAkBiD,IAAlB,CAAuBD,KAAvB;AACH;;AACD,eAAK,IAAIE,CAAC,GAAG,KAAKlD,YAAL,CAAkBmC,MAAlB,GAAyB,CAAtC,EAAyCe,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,kBAAMF,KAAK,GAAG,KAAKhD,YAAL,CAAkBkD,CAAlB,CAAd;AACA,gBAAIC,UAAU,GAAG,IAAjB;;AACA,iBAAK,IAAIC,IAAT,IAAiBJ,KAAK,CAACK,UAAvB,EAAmC,CAClC;;AACD,gBAAIF,UAAJ,EAAgB;AACZ,mBAAKnD,YAAL,CAAkBsD,MAAlB,CAAyBJ,CAAzB,EAA4B,CAA5B;;AACA,mBAAK,IAAIK,OAAT,IAAoBP,KAAK,CAACQ,QAA1B,EAAoC;AAChC,wBAAOD,OAAO,CAACE,KAAf;AACI,uBAAK;AAAA;AAAA,8EAA0BC,GAA/B;AACIlD,oBAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,aAA5B,EAA4C8C,OAAD,CAAsCI,OAAjF;AACA;;AACJ,uBAAK;AAAA;AAAA,8EAA0BC,KAA/B;AACI;;AACJ,uBAAK;AAAA;AAAA,8EAA0B1E,IAA/B;AACI,0BAAM2E,UAAU,GAAGN,OAAnB;AACA,0BAAMjB,IAAI,GAAG;AAAA;AAAA,wCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,wCAAMD,MAAN,CAAaE,iBAAvC,EAA0DoB,UAAU,CAACnB,QAArE,CAAb;AACA;AAAA;AAAA,wCAAMH,MAAN,CAAaI,IAAb,CAAkBL,IAAlB,EAAwB,CAACvB,GAAD,EAAMC,MAAN,KAAwB;AAC5C,0BAAID,GAAJ,EAAS;AACLP,wBAAAA,OAAO,CAACS,KAAR,CAAc,cAAd,EAA6B,4BAA7B,EAA2DF,GAA3D;AACA;AACH;;AACD,4BAAM6B,QAAQ,GAAG/D,WAAW,CAACmC,MAAD,CAAX,CAAoB6B,YAApB;AAAA;AAAA,uCAAjB;AACA;AAAA;AAAA,8CAAQC,WAAR,CAAoBC,cAApB,CAAmCH,QAAnC,EAA6C,IAAI7D,IAAJ,CAASiE,KAAK,CAAC7B,QAAN,CAAeC,CAAxB,EAA2B,KAAKf,IAAL,CAAUc,QAAV,CAAmBE,CAA9C,CAA7C;AACH,qBAPD;AAQA;AAjBR;AAmBH;AACJ;AACJ;AACJ;;AAEOd,QAAAA,aAAa,CAACuD,WAAD,EAAoBC,IAApB,EAAwC;AACzD,cAAI1D,IAAI,GAAGyD,WAAW,CAACE,cAAZ,CAA2BD,IAA3B,CAAX;;AACA,cAAI1D,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIvB,IAAJ,CAASiF,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACpC,QAAZ,CAAqBrB,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAzGuC,O", "sourcesContent": ["import { _decorator, assetManager, Component, instantiate, Node, Prefab, Vec2 } from \"cc\";\r\nimport { LevelDataEvent, LevelDataLayer, LevelDataWave } from \"../../../leveldata/leveldata\";\r\nimport { LevelWaveUI } from \"./LevelWaveUI\";\r\nimport { LevelEventUI } from \"./LevelEventUI\";\r\nimport WaveManager from \"../../manager/WaveManager\";\r\nimport { MyApp } from \"../../../MyApp\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport { Wave } from \"../../wave/Wave\";\r\nimport { LevelDataEventCondtionType } from \"../../../leveldata/condition/LevelDataEventCondtion\";\r\nimport { LevelDataEventTriggerType } from \"../../../leveldata/trigger/LevelDataEventTrigger\";\r\nimport { LevelDataEventTriggerLog } from \"../../../leveldata/trigger/LevelDataEventTriggerLog\";\r\nimport { LevelDataEventTriggerWave } from \"../../../leveldata/trigger/LevelDataEventTriggerWave\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends Component {\r\n    public backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n\r\n    private terrainsNode: Node|null = null;\r\n    private dynamicNode: Node|null = null;\r\n    private waves: LevelDataWave[] = [];\r\n    private events: LevelDataEvent[] = [];\r\n    private enableEvents: LevelDataEvent[] = [];\r\n\r\n    onLoad(): void {\r\n        \r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer, offSetY: number):void {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n        \r\n        this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);\r\n        \r\n        console.log('LevelLayerUI',\" initByLevelData\");\r\n        this.backgrounds = [];\r\n\r\n        data.terrains?.forEach((terrain) => {\r\n            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI',\" initByLevelData load terrain prefab err\", err);\r\n                    return;\r\n                } \r\n                \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode.addChild(terrainNode);                \r\n            });\r\n        });\r\n        this.waves = [...data.waves]\r\n        this.waves.sort((a, b) => a.position.y - b.position.y);\r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n    }\r\n\r\n    public tick(deltaTime: number, speed:number):void {\r\n        const prePosY = this.node.getPosition().y;\r\n        this.node.setPosition(0, prePosY - deltaTime * speed, 0);\r\n\r\n        while(this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {\r\n            const wave = this.waves[0];\r\n            this.waves.shift();\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, wave.waveUUID)\r\n            MyApp.resMgr.load(path, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error('LevelLayerUI',\" tick load wave prefab err\", err);\r\n                    return;\r\n                } \r\n                const waveComp = instantiate(prefab).getComponent(Wave)\r\n                GameIns.waveManager.addWaveByLevel(waveComp, wave.position);\r\n            });\r\n        }\r\n        while(this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {\r\n            const event = this.events[0];\r\n            this.events.shift();\r\n            this.enableEvents.push(event);\r\n        }\r\n        for (let i = this.enableEvents.length-1; i >= 0; i--) {\r\n            const event = this.enableEvents[i];\r\n            let condResult = true\r\n            for (let cond of event.conditions) {\r\n            }\r\n            if (condResult) {\r\n                this.enableEvents.splice(i, 1);\r\n                for (let trigger of event.triggers) {\r\n                    switch(trigger._type) {\r\n                        case LevelDataEventTriggerType.Log:\r\n                            console.log(\"LevelLayerUI\", \"trigger log\", (trigger as LevelDataEventTriggerLog).message);\r\n                            break;\r\n                        case LevelDataEventTriggerType.Audio:\r\n                            break;\r\n                        case LevelDataEventTriggerType.Wave:\r\n                            const waveTriger = trigger as LevelDataEventTriggerWave;\r\n                            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveTriger.waveUUID)\r\n                            MyApp.resMgr.load(path, (err, prefab:Prefab) => {\r\n                                if (err) {\r\n                                    console.error('LevelLayerUI',\" tick load wave prefab err\", err);\r\n                                    return;\r\n                                } \r\n                                const waveComp = instantiate(prefab).getComponent(Wave)\r\n                                GameIns.waveManager.addWaveByLevel(waveComp, new Vec2(event.position.x, this.node.position.y));\r\n                            });\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n}"]}