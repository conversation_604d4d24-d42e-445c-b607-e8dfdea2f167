System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v2, Size, size, BoxCollider2D, FCollider, ColliderType, GameConst, _dec, _dec2, _dec3, _class, _class2, _descriptor, _crd, ccclass, property, menu, FBoxCollider;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderType(extras) {
    _reporterNs.report("ColliderType", "./FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v2 = _cc.v2;
      Size = _cc.Size;
      size = _cc.size;
      BoxCollider2D = _cc.BoxCollider2D;
    }, function (_unresolved_2) {
      FCollider = _unresolved_2.default;
      ColliderType = _unresolved_2.ColliderType;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c976eGmvO5AnJ3rMG4DYuIx", "FBoxCollider", undefined);

      __checkObsolete__(['_decorator', 'Vec2', 'v2', 'Size', 'size', 'BoxCollider2D']);

      ({
        ccclass,
        property,
        menu
      } = _decorator);

      _export("default", FBoxCollider = (_dec = ccclass('FBoxCollider'), _dec2 = menu("碰撞组件Ex/FBoxCollider"), _dec3 = property(Size), _dec(_class = _dec2(_class = (_class2 = class FBoxCollider extends (_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
        error: Error()
      }), FCollider) : FCollider) {
        constructor(...args) {
          super(...args);
          this.worldPoints = [v2(), v2(), v2(), v2()];
          this.worldEdge = [];
          this.isConvex = true;

          _initializerDefineProperty(this, "_size", _descriptor, this);
        }

        get type() {
          return (_crd && ColliderType === void 0 ? (_reportPossibleCrUseOfColliderType({
            error: Error()
          }), ColliderType) : ColliderType).Box;
        }

        get size() {
          return this._size;
        }

        set size(value) {
          this._size.width = value.width < 0 ? 0 : value.width;
          this._size.height = value.height < 0 ? 0 : value.height;
        }

        onLoad() {
          let collider = this.node.getComponent(BoxCollider2D);

          if (collider) {
            this.size = collider.size;
            this.offset = v2(collider.offset.x, collider.offset.y);
          }
        }

        init(entity, size = null, offset = v2(0, 0)) {
          this.initBaseData(entity, offset);

          if (size) {
            this.size = size;
          }
        }

        draw() {
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ColliderDraw) {
            return;
          }

          let collider = this.node.getComponent(BoxCollider2D);

          if (!collider) {
            collider = this.node.addComponent(BoxCollider2D);
            collider.size = this.size;
            collider.offset.x = this.offset.x;
            collider.offset.y = this.offset.y;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "_size", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return size(100, 100);
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "size", [property], Object.getOwnPropertyDescriptor(_class2.prototype, "size"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cea6a6d613b6bc1eec9af5cdc3fa896c399dd402.js.map