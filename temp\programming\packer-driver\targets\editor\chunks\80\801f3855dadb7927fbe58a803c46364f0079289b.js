System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, NodePool, sp, SpriteAtlas, Node, SingletonBase, GameIns, GameConst, Tools, BossData, UnitData, BattleLayer, BossEntity, GameFunc, MyApp, GameResourceList, BossManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossBase(extras) {
    _reporterNs.report("BossBase", "../ui/plane/boss/BossBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossData(extras) {
    _reporterNs.report("BossData", "../data/BossData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossUnitData(extras) {
    _reporterNs.report("BossUnitData", "../data/BossData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUnitData(extras) {
    _reporterNs.report("UnitData", "../data/BossData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossEntity(extras) {
    _reporterNs.report("BossEntity", "../ui/plane/boss/BossEntity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  _export("BossManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      NodePool = _cc.NodePool;
      sp = _cc.sp;
      SpriteAtlas = _cc.SpriteAtlas;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }, function (_unresolved_5) {
      Tools = _unresolved_5.Tools;
    }, function (_unresolved_6) {
      BossData = _unresolved_6.BossData;
      UnitData = _unresolved_6.UnitData;
    }, function (_unresolved_7) {
      BattleLayer = _unresolved_7.default;
    }, function (_unresolved_8) {
      BossEntity = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameFunc = _unresolved_9.GameFunc;
    }, function (_unresolved_10) {
      MyApp = _unresolved_10.MyApp;
    }, function (_unresolved_11) {
      GameResourceList = _unresolved_11.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "369abrLRSFI4qwLaw5SnC7H", "BossManager", undefined);

      __checkObsolete__(['JsonAsset', 'NodePool', 'resources', 'sp', 'Sprite', 'SpriteAtlas', 'Node']);

      _export("BossManager", BossManager = class BossManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super();
          this._bossDatas = new Map();
          this._unitDatas = new Map();
          this._bossUnitDatas = new Map();
          this._bossDataMap = new Map();
          this.bossAtlasMap = new Map();
          this.unitAtlas = null;
          this._bossArr = [];
          this.fireParticle = null;
          this.skelDataMap = new Map();
          this.bossResFinish = false;
          this._fireParticlePool = new NodePool();
          this._isBossWarning = false;
          this._smokeSkelData = null;
          this._mainStage = -1;
          this._subStage = -1;
          this.m_bossResStage = 0;
          this.initConfig();
        }

        initConfig() {
          let bossTbDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbBoss.getDataList();

          for (let bossTbData of bossTbDatas) {
            const bossData = new (_crd && BossData === void 0 ? (_reportPossibleCrUseOfBossData({
              error: Error()
            }), BossData) : BossData)();
            bossData.loadJson(bossTbData);

            let bossList = this._bossDatas.get(bossData.id);

            if (!bossList) {
              bossList = [];

              this._bossDatas.set(bossData.id, bossList);
            }

            bossList.push(bossData);
          }

          let unitTbDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbUnit.getDataList();

          for (let unitTbData of unitTbDatas) {
            const unitData = new (_crd && UnitData === void 0 ? (_reportPossibleCrUseOfUnitData({
              error: Error()
            }), UnitData) : UnitData)();
            unitData.loadJson(unitTbData);

            this._unitDatas.set(unitData.id, unitData);
          }
        }

        async preLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).spine_boss_smoke, sp.SkeletonData, (error, data) => {
            this._smokeSkelData = data;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          this.unitAtlas = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_boss_unit, SpriteAtlas);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.checkLoadFinish();
        }

        mainReset() {
          this.subReset();
          this._mainStage = -1;

          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).Cache) {
            if (this._smokeSkelData) {
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.releaseAssetByForce(this._smokeSkelData);
              this._smokeSkelData = null;
            }

            if (this.unitAtlas) {
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.releaseAssetByForce(this.unitAtlas);
              this.unitAtlas = null;
            }
          }
        }
        /**
         * 重置子关卡
         */


        subReset() {
          for (const boss of this._bossArr) {
            boss.willDestroy();
            boss.node.parent = null;
            setTimeout(() => {
              boss.node.destroy();
            }, 1000);
          }

          this._bossArr = []; // 清理骨骼动画资源

          this.skelDataMap.forEach((data, key) => {
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.releaseAssetByForce(data);
          });
          this.skelDataMap.clear(); // 清理图集资源

          this.bossAtlasMap.forEach((atlas, key) => {
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.releaseAssetByForce(atlas);
          });
          this.bossAtlasMap.clear();
        }
        /**
         * 加载 Boss 资源
         * @param bossType Boss 类型
         * @param bossId Boss ID
         * @param callback 回调函数
         * @param isPreload 是否为预加载
         */


        async loadBossRes(bossType, bossId, callback = null, isPreload = false) {
          try {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).warn("Loading Boss Resources", bossType, bossId);

            if (!isPreload) {
              this.bossResFinish = false; // GameFunc.showUILoadingForDelay(5);
            }

            switch (bossType) {
              case 100:
                // 加载普通 Boss
                const bossDatas = this.getBossDatas(bossId);

                for (const bossData of bossDatas) {
                  for (const unitId of bossData.units) {
                    const unitData = this.getUnitData(unitId);

                    if (unitData.anim) {
                      const skelData = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.loadAsync("Game/spine/" + unitData.anim, sp.SkeletonData);

                      if (!skelData) {
                        if (!isPreload) {
                          // GameFunc.showUILoadErr(() => {
                          this.loadBossRes(bossType, bossId, callback); // });
                        }

                        return false;
                      }

                      if (!isPreload) {
                        this.skelDataMap.set(unitData.anim, skelData);
                      }
                    }
                  }
                }

                break;

              default:
                (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).error("Unknown Boss Type:", bossType);
                return false;
            }

            if (!isPreload) {
              this.bossResFinish = true; // GameFunc.hideUILoading();
            }

            if (callback) {
              callback();
            }

            return true;
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error("Error loading Boss resources:", error);

            if (!isPreload) {
              // GameFunc.showUILoadErr(() => {
              this.loadBossRes(bossType, bossId, callback); // });
            }

            return false;
          }
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (let i = 0; i < this._bossArr.length; i++) {
            const boss = this._bossArr[i];

            if (boss.removeAble) {
              this.removeBoss(boss);
              i--;
            } else {
              boss.updateGameLogic(deltaTime);
            }
          }
        }
        /**
         * 开始 Boss 战斗
         */


        bossFightStart() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              boss.startBattle();
              break;
            }
          }
        }
        /**
         * 添加 Boss
         * @param bossType Boss 类型
         * @param bossId Boss ID
         */


        addBoss(bossType, bossId) {
          switch (bossType) {
            case 100:
              const bossDatas = this.getBossDatas(bossId);
              return this._createBoss(bossDatas);

            default:
              (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).error("Unknown Boss Type:", bossType);
              return null;
          }
        }
        /**
         * 移除 Boss
         * @param boss 要移除的 Boss
         */


        removeBoss(boss) {
          boss.node.y = 1000;
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrRemove(this._bossArr, boss);
          boss.node.parent = null;
          boss.node.destroy();
        }
        /**
         * 获取 Boss 图集
         * @param atlasName 图集名称
         */


        async getBossAtlas(atlasName) {
          let atlas = this.bossAtlasMap.get(atlasName);

          if (!atlas) {
            atlas = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync(atlasName, SpriteAtlas);

            if (atlas) {
              this.bossAtlasMap.set(atlasName, atlas);
            }
          }

          return atlas;
        }
        /**
         * 设置 Boss 的精灵帧
         * @param sprite 精灵组件
         * @param frameName 精灵帧名称
         */


        setBossFrame(sprite, frameName) {
          if (sprite && this.unitAtlas) {
            sprite.spriteFrame = this.unitAtlas.getSpriteFrame(frameName);
          }
        }
        /**
         * 显示 Boss 警告
         */


        showBossWarning() {
          this._isBossWarning = true;
        }
        /**
         * 获取所有 Boss
         */


        get bosses() {
          return this._bossArr;
        }
        /**
         * 检查是否所有 Boss 已结束
         */


        isBossOver() {
          return this._bossArr.length === 0;
        }
        /**
         * 检查是否所有 Boss 已死亡
         */


        isBossDead() {
          for (const boss of this._bossArr) {
            if (!boss.isDead) {
              return false;
            }
          }

          return true;
        }
        /**
         * 获取烟雾骨骼数据
         */


        get smokeSkelData() {
          return this._smokeSkelData;
        }
        /**
         * 获取单位数据
         * @param unitId 单位 ID
         */


        getUnitData(unitId) {
          return this._unitDatas.get(unitId);
        }
        /**
         * 获取 Boss 数据
         * @param bossId Boss ID
         */


        getBossDatas(bossId) {
          return this._bossDatas.get(bossId);
        }
        /**
         * 根据 ID 创建 Boss
         * @param bossId Boss ID
         */


        createBossById(bossId) {
          const bossDatas = this.getBossDatas(bossId);

          if (bossDatas) {
            return this._createBoss(bossDatas);
          }

          return null;
        }
        /**
         * 创建普通 Boss
         * @param bossDatas Boss 数据
         */


        _createBoss(bossDatas) {
          const node = new Node("boss");
          (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
            error: Error()
          }), BattleLayer) : BattleLayer).me.addEnemy(node);
          const boss = node.addComponent(_crd && BossEntity === void 0 ? (_reportPossibleCrUseOfBossEntity({
            error: Error()
          }), BossEntity) : BossEntity);
          boss.init(bossDatas);
          boss.new_uuid = (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
            error: Error()
          }), GameFunc) : GameFunc).uuid;

          this._bossArr.push(boss);

          return boss;
        }
        /**
         * 暂停所有 Boss
         */


        pauseBoss() {
          for (const boss of this._bossArr) {
            if (!boss.isDead && boss.pause) {
              boss.pause();
            }
          }
        }
        /**
         * 恢复所有 Boss
         */


        resumeBoss() {
          for (const boss of this._bossArr) {
            if (!boss.isDead && boss.resume) {
              boss.resume();
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=801f3855dadb7927fbe58a803c46364f0079289b.js.map