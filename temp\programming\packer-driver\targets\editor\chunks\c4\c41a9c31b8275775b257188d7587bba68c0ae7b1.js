System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, assetManager, instantiate, Vec2, LevelDataEvent, LevelDataWave, LevelEditorUtils, LevelEditorWaveUI, LevelEditorEventUI, _dec, _dec2, _class, _crd, ccclass, property, executeInEditMode, TerrainsNodeName, DynamicNodeName, WaveNodeName, EventNodeName, LevelEditorLayerUI;

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataWave(extras) {
    _reporterNs.report("LevelDataWave", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorWaveUI(extras) {
    _reporterNs.report("LevelEditorWaveUI", "./LevelEditorWaveUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorEventUI(extras) {
    _reporterNs.report("LevelEditorEventUI", "./LevelEditorEventUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      LevelDataEvent = _unresolved_2.LevelDataEvent;
      LevelDataWave = _unresolved_2.LevelDataWave;
    }, function (_unresolved_3) {
      LevelEditorUtils = _unresolved_3.LevelEditorUtils;
    }, function (_unresolved_4) {
      LevelEditorWaveUI = _unresolved_4.LevelEditorWaveUI;
    }, function (_unresolved_5) {
      LevelEditorEventUI = _unresolved_5.LevelEditorEventUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "92d5epe+XRMm6NUwiCSJBKR", "LevelEditorLayerUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'Asset', 'resources', 'assetManager', 'AssetManager', 'Sprite', 'SpriteFrame', 'SpriteAtlas', 'math', 'instantiate', 'Vec2', 'CCInteger']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      WaveNodeName = "waves";
      EventNodeName = "events";

      _export("LevelEditorLayerUI", LevelEditorLayerUI = (_dec = ccclass('LevelEditorLayerUI'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class LevelEditorLayerUI extends Component {
        constructor(...args) {
          super(...args);
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.wavesNode = null;
          this.eventsNode = null;
        }

        onLoad() {
          this.terrainsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, TerrainsNodeName);
          this.dynamicNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, DynamicNodeName);
          this.wavesNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, WaveNodeName);
          this.eventsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, EventNodeName);
        }

        initByLevelData(data) {
          var _data$terrains, _data$waves, _data$events;

          console.log("LevelEditorLayerUI initByLevelData");

          if (!data) {
            return;
          }

          (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
            assetManager.loadAny({
              uuid: terrain.uuid
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
              terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
              this.terrainsNode.addChild(terrainNode);
            });
          });
          (_data$waves = data.waves) == null || _data$waves.forEach(wave => {
            var node = new Node();
            var waveUIComp = node.addComponent(_crd && LevelEditorWaveUI === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveUI({
              error: Error()
            }), LevelEditorWaveUI) : LevelEditorWaveUI);
            waveUIComp.initByLevelData(wave);
            this.wavesNode.addChild(node);
          });
          (_data$events = data.events) == null || _data$events.forEach(event => {
            var node = new Node();
            var eventUIComp = node.addComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
              error: Error()
            }), LevelEditorEventUI) : LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode.addChild(node);
          });
        }

        fillLevelData(data) {
          console.log("LevelEditorLayerUI fillLevelData");
          data.terrains = [];
          this.terrainsNode.children.forEach(terrainNode => {
            data.terrains.push({
              // @ts-ignore
              uuid: terrainNode._prefab.asset._uuid,
              position: new Vec2(terrainNode.position.x, terrainNode.position.y),
              scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),
              rotation: terrainNode.rotation.z
            });
          });
          data.waves = [];
          this.wavesNode.children.forEach(waveNode => {
            var wave = new (_crd && LevelDataWave === void 0 ? (_reportPossibleCrUseOfLevelDataWave({
              error: Error()
            }), LevelDataWave) : LevelDataWave)();
            var waveUIComp = waveNode.getComponent(_crd && LevelEditorWaveUI === void 0 ? (_reportPossibleCrUseOfLevelEditorWaveUI({
              error: Error()
            }), LevelEditorWaveUI) : LevelEditorWaveUI);
            waveUIComp.fillLevelData(wave);
            data.waves.push(wave);
          });
          data.events = [];
          this.eventsNode.children.forEach(eventNode => {
            var event = new (_crd && LevelDataEvent === void 0 ? (_reportPossibleCrUseOfLevelDataEvent({
              error: Error()
            }), LevelDataEvent) : LevelDataEvent)();
            var eventUIComp = eventNode.getComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
              error: Error()
            }), LevelEditorEventUI) : LevelEditorEventUI);
            eventUIComp.fillLevelData(event);
            data.events.push(event);
          });
        }

        tick(progress, totalTime, speed) {
          this.node.setPosition(0, -progress * totalTime * speed, 0);
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c41a9c31b8275775b257188d7587bba68c0ae7b1.js.map