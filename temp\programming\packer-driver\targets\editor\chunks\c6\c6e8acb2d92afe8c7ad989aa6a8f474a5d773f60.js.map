{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/gm/GmUI.ts"], "names": ["_decorator", "EditBox", "Label", "Node", "csproto", "DataMgr", "MyApp", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ButtonPlus", "DropDown", "List", "GmButtonUI", "ccclass", "property", "GmUI", "_inputNodeList", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getUIOption", "isClickBgHideUI", "onLoad", "inputParentNode", "children", "for<PERSON>ach", "v", "push", "getComponentInChildren", "sendBtn", "addClick", "onSendBtnClick", "clearBtn", "onClearBtnClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GM", "onGmMsg", "bind", "logLabel", "string", "res", "log", "body", "gm", "text", "onCmdBtnRender", "item", "idx", "cmdInfo", "getCmdBtnListByTabID", "tabDropDown", "<PERSON><PERSON><PERSON>", "label", "cfg", "name", "onCmdBtnClick", "filter", "cmd", "placeholder", "desc", "onDropDownOptionRender", "nd", "optKey", "tabName", "onDropDownOptionClick", "cmdBtnList", "numItems", "length", "msg", "Date", "toLocaleString", "args", "target", "onSendClick", "gmStr", "sendMessage", "gm_str", "onShow", "tabIDList", "init", "onHide", "openUI", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC9BC,MAAAA,O;;AAEEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACEC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;sBAEjBgB,I,WADZF,OAAO,CAAC,MAAD,C,UASHC,QAAQ;AAAA;AAAA,+B,UAERA,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACb,KAAD,C,UAERa,QAAQ;AAAA;AAAA,mC,2BAnBb,MACaC,IADb;AAAA;AAAA,4BACiC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAqBrBC,cArBqB,GAqBO,EArBP;AAAA;;AACT,eAANC,MAAM,GAAW;AAAE,iBAAO,YAAP;AAAsB;;AACjC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC/B,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AACHC,YAAAA,eAAe,EAAE;AADd,WAAP;AAGH;;AAgBSC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL,CAAqBC,QAArB,CAA8BC,OAA9B,CAAsCC,CAAC,IAAI;AACvC,iBAAKV,cAAL,CAAoBW,IAApB,CAAyBD,CAAC,CAACE,sBAAF,CAAyB5B,OAAzB,CAAzB;AACH,WAFD;AAGA,eAAK6B,OAAL,CAAaC,QAAb,CAAsB,KAAKC,cAA3B,EAA2C,IAA3C;AACA,eAAKC,QAAL,CAAcF,QAAd,CAAuB,KAAKG,eAA5B,EAA6C,IAA7C;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,SAA/C,EAA0D,KAAKC,OAAL,CAAaC,IAAb,CAAkB,IAAlB,CAA1D;AACH;;AAEOP,QAAAA,eAAe,GAAG;AACtB,eAAKQ,QAAL,CAAcC,MAAd,GAAuB,EAAvB;AACH;;AAEOH,QAAAA,OAAO,CAACI,GAAD,EAA0B;AACrC,eAAKC,GAAL,CAAU,SAAQD,GAAG,CAACE,IAAJ,CAASC,EAAT,CAAYC,IAAK,EAAnC;AACH;;AAEOC,QAAAA,cAAc,CAACC,IAAD,EAAaC,GAAb,EAA0B;AAC5C,gBAAMC,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAiBC,WAAjD,EAA8DJ,GAA9D,CAAhB;AACA,gBAAMK,KAAK,GAAGN,IAAI,CAACrB,sBAAL,CAA4B3B,KAA5B,CAAd;AACAsD,UAAAA,KAAK,CAACb,MAAN,GAAeS,OAAO,CAACK,GAAR,CAAYC,IAA3B;AACH;;AAEOC,QAAAA,aAAa,CAACT,IAAD,EAAaC,GAAb,EAA0B;AAC3C,gBAAMC,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAiBC,WAAjD,EAA8DK,MAA9D,CAAqEjC,CAAC,IAAIA,CAAC,CAAC8B,GAAF,CAAMC,IAAN,KAAe,EAAzF,EAA6FP,GAA7F,CAAhB;AACA,eAAKlC,cAAL,CAAoB,CAApB,EAAuB0B,MAAvB,GAAgCS,OAAO,CAACK,GAAR,CAAYI,GAA5C;AACA,eAAK5C,cAAL,CAAoB,CAApB,EAAuB6C,WAAvB,GAAqCV,OAAO,CAACK,GAAR,CAAYM,IAAjD;AACH;;AAEOC,QAAAA,sBAAsB,CAACC,EAAD,EAAWC,MAAX,EAAgC;AAC1D,gBAAMT,GAAG,GAAG;AAAA;AAAA,kCAAQV,EAAR,CAAWM,oBAAX,CAAgCa,MAAhC,CAAZ;AACAD,UAAAA,EAAE,CAACpC,sBAAH,CAA0B3B,KAA1B,EAAiCyC,MAAjC,GAA0Cc,GAAG,CAAC,CAAD,CAAH,CAAOA,GAAP,CAAWU,OAArD;AACH;;AAEOC,QAAAA,qBAAqB,CAACF,MAAD,EAAsB;AAC/C,gBAAMT,GAAG,GAAG;AAAA;AAAA,kCAAQV,EAAR,CAAWM,oBAAX,CAAgCa,MAAhC,EAAwCN,MAAxC,CAA+CjC,CAAC,IAAIA,CAAC,CAAC8B,GAAF,CAAMC,IAAN,KAAe,EAAnE,CAAZ;AACA,eAAKW,UAAL,CAAgBC,QAAhB,GAA2Bb,GAAG,CAACc,MAA/B;AACH;;AAEO1B,QAAAA,GAAG,CAAC2B,GAAD,EAAc;AACrB,eAAK9B,QAAL,CAAcC,MAAd,IAAyB,IAAG,IAAI8B,IAAJ,GAAWC,cAAX,EAA4B,KAAIF,GAAI,IAAhE;AACH;;AAEOxC,QAAAA,cAAc,GAAG;AACrB,eAAKf,cAAL,CAAoBS,OAApB,CAA4BC,CAAC,IAAI;AAC7B,gBAAIA,CAAC,CAACgB,MAAF,IAAYhB,CAAC,CAACmC,WAAlB,EAA+B;AAC3BnC,cAAAA,CAAC,CAACgB,MAAF,GAAW,EAAX;AACH;AACJ,WAJD;;AAKA,gBAAMkB,GAAG,GAAG,KAAK5C,cAAL,CAAoB,CAApB,EAAuB0B,MAAnC;AACA,gBAAMgC,IAAI,GAAG,KAAK1D,cAAL,CAAoB,CAApB,EAAuB0B,MAApC;AACA,cAAIiC,MAAM,GAAG,KAAK3D,cAAL,CAAoB,CAApB,EAAuB0B,MAApC;;AACA,cAAIiC,MAAM,KAAK,EAAf,EAAmB;AACfA,YAAAA,MAAM,GAAI,YAAWA,MAAO,GAA5B;AACH;;AACD,gBAAMxB,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAiBC,WAAjD,EAA8DK,MAA9D,CAAqEjC,CAAC,IAAIA,CAAC,CAAC8B,GAAF,CAAMI,GAAN,KAAcA,GAAxF,EAA6F,CAA7F,CAAhB;;AACA,cAAIT,OAAJ,YAAIA,OAAO,CAAEyB,WAAb,EAA0B;AACtB,kBAAMjC,GAAG,GAAGQ,OAAO,CAACyB,WAAR,CAAoBF,IAApB,CAAZ;AACA,iBAAK9B,GAAL,CAAU,IAAGgB,GAAI,WAAUjB,GAAI,EAA/B;AACH,WAHD,MAGO;AACH,kBAAMkC,KAAK,GAAGjB,GAAG,GAAG,GAAN,GAAYe,MAAZ,GAAqB,GAArB,GAA2BD,IAAzC;AACA;AAAA;AAAA,gCAAMxC,MAAN,CAAa4C,WAAb,CAAyB;AAAA;AAAA,oCAAQ1C,EAAR,CAAWC,MAAX,CAAkBC,SAA3C,EAAsD;AAClDQ,cAAAA,EAAE,EAAE;AAAEiC,gBAAAA,MAAM,EAAEF;AAAV;AAD8C,aAAtD;AAGA,iBAAKjC,GAAL,CAAU,IAAGgB,GAAI,WAAUiB,KAAM,EAAjC;AACH;AACJ,SAzF4B,CA2F7B;;;AACOG,QAAAA,MAAM,CAAC,GAAGN,IAAJ,EAAgC;AACzC,gBAAMO,SAAS,GAAG;AAAA;AAAA,kCAAQnC,EAAR,CAAWmC,SAA7B;AACA,eAAK5B,WAAL,CAAiB6B,IAAjB,CAAsBD,SAAtB,EAAiC,KAAKlB,sBAAL,CAA4BvB,IAA5B,CAAiC,IAAjC,CAAjC,EAAyE,KAAK2B,qBAAL,CAA2B3B,IAA3B,CAAgC,IAAhC,CAAzE;AACA,eAAK4B,UAAL,CAAgBC,QAAhB,GAA2B;AAAA;AAAA,kCAAQvB,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAiBC,WAAjD,EAA8DgB,MAAzF;AACA,eAAKrC,eAAL;AACA;AACH;;AAED;AACOkD,QAAAA,MAAM,CAAC,GAAGT,IAAJ,EAAgC;AACzC;AAAA;AAAA,8BAAMU,MAAN;AAAA;AAAA;AACA;AACH;;AACD;AACOC,QAAAA,OAAO,CAAC,GAAGX,IAAJ,EAAgC;AAC1C;AAAA;AAAA,8BAAMU,MAAN;AAAA;AAAA;AACA;AACH;;AA7G4B,O;;;;;iBASL,I;;;;;;;iBAEL,I;;;;;;;iBAEG,I;;;;;;;iBAEE,I;;;;;;;iBAEN,I;;;;;;;iBAEK,I", "sourcesContent": ["import { _decorator, EditBox, Label, Node } from 'cc';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { res } from '../../AutoGen/Luban/schema';\nimport { DataMgr } from '../../Data/DataManager';\nimport { MyApp } from '../../MyApp';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from '../UIMgr';\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\nimport { DropDown } from '../common/components/dropdown/DropDown';\nimport List from '../common/components/list/List';\nimport { GmButtonUI } from './GmButtonUI';\n\nconst { ccclass, property } = _decorator;\n@ccclass('GmUI')\nexport class GmUI extends BaseUI {\n    public static getUrl(): string { return \"ui/gm/GmUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getUIOption(): UIOpt {\n        return {\n            isClickBgHideUI: true,\n        }\n    }\n    @property(DropDown)\n    tabDropDown: DropDown = null;\n    @property(List)\n    cmdBtnList: List = null;\n    @property(ButtonPlus)\n    sendBtn: ButtonPlus = null;\n    @property(Node)\n    inputParentNode: Node = null;\n    @property(Label)\n    logLabel: Label = null;\n    @property(ButtonPlus)\n    clearBtn: ButtonPlus = null;\n\n    private _inputNodeList: EditBox[] = [];\n\n    protected onLoad(): void {\n        this.inputParentNode.children.forEach(v => {\n            this._inputNodeList.push(v.getComponentInChildren(EditBox))\n        })\n        this.sendBtn.addClick(this.onSendBtnClick, this)\n        this.clearBtn.addClick(this.onClearBtnClick, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GM, this.onGmMsg.bind(this))\n    }\n\n    private onClearBtnClick() {\n        this.logLabel.string = \"\"\n    }\n\n    private onGmMsg(res: csproto.cs.IS2CMsg) {\n        this.log(`接收 => ${res.body.gm.text}`)\n    }\n\n    private onCmdBtnRender(item: Node, idx: number) {\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey)[idx]\n        const label = item.getComponentInChildren(Label)\n        label.string = cmdInfo.cfg.name\n    }\n\n    private onCmdBtnClick(item: Node, idx: number) {\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.name !== \"\")[idx]\n        this._inputNodeList[0].string = cmdInfo.cfg.cmd\n        this._inputNodeList[1].placeholder = cmdInfo.cfg.desc\n    }\n\n    private onDropDownOptionRender(nd: Node, optKey: res.GMTabID) {\n        const cfg = DataMgr.gm.getCmdBtnListByTabID(optKey)\n        nd.getComponentInChildren(Label).string = cfg[0].cfg.tabName\n    }\n\n    private onDropDownOptionClick(optKey: res.GMTabID) {\n        const cfg = DataMgr.gm.getCmdBtnListByTabID(optKey).filter(v => v.cfg.name !== \"\")\n        this.cmdBtnList.numItems = cfg.length\n    }\n\n    private log(msg: string) {\n        this.logLabel.string += `[${new Date().toLocaleString()}] ${msg}\\n`\n    }\n\n    private onSendBtnClick() {\n        this._inputNodeList.forEach(v => {\n            if (v.string == v.placeholder) {\n                v.string = \"\"\n            }\n        })\n        const cmd = this._inputNodeList[0].string\n        const args = this._inputNodeList[1].string\n        let target = this._inputNodeList[2].string\n        if (target !== \"\") {\n            target = `[destuin ${target}]`\n        }\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.cmd === cmd)[0]\n        if (cmdInfo?.onSendClick) {\n            const res = cmdInfo.onSendClick(args)\n            this.log(`[${cmd}] 发送 => ${res}`)\n        } else {\n            const gmStr = cmd + \" \" + target + \" \" + args\n            MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GM, {\n                gm: { gm_str: gmStr }\n            })\n            this.log(`[${cmd}] 发送 => ${gmStr}`)\n        }\n    }\n\n    // 显示 UI 的方法，需要子类实现\n    public onShow(...args: any[]): Promise<void> {\n        const tabIDList = DataMgr.gm.tabIDList\n        this.tabDropDown.init(tabIDList, this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this))\n        this.cmdBtnList.numItems = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).length\n        this.onClearBtnClick()\n        return\n    };\n\n    // 隐藏 UI 的方法，需要子类实现\n    public onHide(...args: any[]): Promise<void> {\n        UIMgr.openUI(GmButtonUI)\n        return\n    };\n    // 关闭 UI 的方法，需要子类实现\n    public onClose(...args: any[]): Promise<void> {\n        UIMgr.openUI(GmButtonUI)\n        return\n    };\n}\n\n"]}