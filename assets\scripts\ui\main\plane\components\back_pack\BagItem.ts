import { _decorator, Component, Label, Node } from "cc";
import csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';
import { DataMgr } from "db://assets/scripts/Data/DataManager";
import { EventMgr } from "db://assets/scripts/event/EventManager";
import { MyApp } from "db://assets/scripts/MyApp";
import { ButtonPlus } from "../../../../common/components/button/ButtonPlus";
import { PlaneUIEvent } from "../../PlaneEvent";
import { TabStatus } from "../../PlaneTypes";
const { ccclass, property } = _decorator;

@ccclass('BagItem')
export class BagItem extends Component {
    @property(Node)
    selectedIcon: Node = null;
    @property(Node)
    mask: Node = null;

    private _planeEquipInfo: csproto.cs.ICSItem = null;
    private _tabStatus: TabStatus = TabStatus.None;

    protected onLoad(): void {
        this.getComponent(ButtonPlus).addClick(this.onClick, this)
    }

    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    private onClick() {
        if (!this._planeEquipInfo) return
        if (this._tabStatus == TabStatus.Merge) {
            if (this.mask.active && !this.selectedIcon) {
                return
            }
            if (!this.mask.active && DataMgr.equip.eqCombine.isFull()) {
                return
            }
        }
        EventMgr.emit(PlaneUIEvent.BagItemClick, this._planeEquipInfo)
    }

    private updateMergeStatus() {
        if (DataMgr.equip.eqCombine.size() > 0) {
            const info = DataMgr.equip.eqCombine.getByGuid(this._planeEquipInfo.guid)
            if (info) {
                this.selectedIcon.active = true;
                this.mask.active = true;
            } else {
                this.selectedIcon.active = false;
                this.mask.active = !DataMgr.equip.eqCombine.isCanCombineWith(this._planeEquipInfo)
            }
        } else {
            this.selectedIcon.active = false;
            this.mask.active = false;
        }
    }

    renderPlanePart(planeEquipInfo: csproto.cs.ICSItem, tabStatus: TabStatus) {
        this._tabStatus = tabStatus;
        this._planeEquipInfo = planeEquipInfo;
        this.selectedIcon.active = false;
        this.mask.active = false;
        if (tabStatus == TabStatus.Merge) {
            this.updateMergeStatus()
        }
        const itemCfg = MyApp.lubanTables.TbEquip.get(this._planeEquipInfo.item_id)
        this.getComponentInChildren(Label).string = itemCfg?.name + `(品质:${itemCfg?.quality})`

    }

    onRenderItem(item: csproto.cs.ICSItem) {
        this.selectedIcon.active = false;
        this.mask.active = false;
        this.getComponentInChildren(Label).string = MyApp.lubanTables.TbItem.get(item.item_id)?.name;
        this._planeEquipInfo = null;
    }
}