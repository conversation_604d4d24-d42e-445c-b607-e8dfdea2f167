import { _decorator, Component, Sprite } from 'cc';
import csproto from './AutoGen/PB/cs_proto.js';
import { MyApp } from './MyApp';
import { DevLoginUI } from './ui/DevLoginUI';
import { BattleUI } from './ui/main/BattleUI';
import { BottomUI } from './ui/main/BottomUI';
import { TopUI } from './ui/main/TopUI';
import { UIMgr } from './ui/UIMgr';
import { logError, logInfo } from './Utils/Logger';

import { WECHAT } from 'cc/env';


const { ccclass, property } = _decorator;

@ccclass('MainUI')
export class MainUI extends Component {
    onLoad(): void {
    }

    async start() {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff)
        if (DevLoginUI.needLogin) {
            if (WECHAT) {
                MyApp.platformSDK.login((err, info) => {
                    if (err) {
                        logError("MainUI", `login failed ${err}`);
                        return;
                    }
                    MyApp.netMgr.login(info);
                })
            } else {
                await UIMgr.openUI(DevLoginUI)
            }
        }
        await UIMgr.openUI(BattleUI)
        await UIMgr.openUI(BottomUI)
        await UIMgr.openUI(TopUI)

        // TODO 为了显示临时背景图，去掉各个UI的背景图 by binbin
        if (false) {
            let disableAllSprite = (uiClass) => {
                let ui = UIMgr.get(uiClass)
                let sprites = ui.node.getComponentsInChildren(Sprite)
                sprites.forEach((sprite) => {
                    sprite.enabled = false
                })
            }
            disableAllSprite(BattleUI)
            disableAllSprite(BottomUI)
            disableAllSprite(TopUI)
        }
    }

    protected onDestroy(): void {
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff)
    }
    onKickOff(msg: csproto.cs.IS2CMsg) {
        logInfo("MainUI", "onKickOff")
        MyApp.netMgr.disableReconnect()
        if (WECHAT) {
        } else {
            UIMgr.openUI(DevLoginUI)
        }
    }

    update(deltaTime: number) {

    }
}

