{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js"], "names": ["_cjs<PERSON><PERSON><PERSON>", "_req", "__cjsMetaURL", "_req0", "url", "define", "exports", "require", "module", "__filename", "__dirname", "<PERSON>", "$protobuf", "$util", "util", "configure", "_cjsExports"], "mappings": ";;;;;;;;;AAAOA,MAAAA,U;;AACkBC,MAAAA,I,SAAhBC,Y;;AACgBC,MAAAA,K,wBAAhBD,Y;;;8BAEHA,Y,GAAe,cAAYE,G;;AACjCJ,MAAAA,UAAU,CAACK,MAAX,CAAkBH,YAAlB,EAAgC,UAAUI,OAAV,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoCC,UAApC,EAAgDC,SAAhD,EAA2D;AAC3F;AAGC;;AAEA,YAAIC,IAAI,GAAGJ,OAAO,CAAC,MAAD,CAAlB;;AACA,YAAIK,SAAS,GAAGL,OAAO,CAAC,uBAAD,CAAvB;;AACA,YAAIM,KAAK,GAAGD,SAAS,CAACE,IAAtB;AACAD,QAAAA,KAAK,CAACF,IAAN,GAAaA,IAAb;AACAC,QAAAA,SAAS,CAACG,SAAV,GAV0F,CAa3F;;AAEA,2BAAAC,WAAW,GAAGR,MAAM,CAACF,OAArB;AAGC,OAlBD,EAkBG,OAAO;AACR,gBAAQL,IADA;AAER,iCAAyBE;AAFjB,OAAP,CAlBH", "sourcesContent": ["import _cjsLoader from 'cce:/internal/ml/cjs-loader.mjs';\nimport { __cjsMetaURL as _req} from 'long';\nimport { __cjsMetaURL as _req0} from 'protobufjs/minimal.js';\nlet _cjsExports;\nconst __cjsMetaURL = import.meta.url;\n_cjsLoader.define(__cjsMetaURL, function (exports, require, module, __filename, __dirname) {\n// #region ORIGINAL CODE\n\n\n \"use strict\";\r\n\r\n var Long = require(\"long\");\r\n var $protobuf = require(\"protobufjs/minimal.js\");\r\n var $util = $protobuf.util;\r\n $util.Long = Long;\r\n $protobuf.configure();\r\n\n\n// #endregion ORIGINAL CODE\n\n_cjsExports = module.exports;\n\n\n}, () => ({\n  'long': _req,\n  'protobufjs/minimal.js': _req0,\n}));\nexport { _cjsExports as default };\nexport { __cjsMetaURL }\n"]}