{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts"], "names": ["_decorator", "Enum", "Prefab", "CCString", "assetManager", "CCInteger", "AudioClip", "LevelEditorWaveParam", "LevelEditorCondition", "LevelEditorElemUI", "LevelDataEventTriggerType", "LevelDataEventTriggerLog", "newTrigger", "LevelDataEventCondtionType", "ccclass", "property", "executeInEditMode", "LevelEditorEventTrigger", "type", "visible", "Log", "Audio", "Wave", "_index", "data", "_audio", "_wave", "_params", "_type", "value", "message", "audio", "audioUUID", "uuid", "wave", "waveUUID", "planeID", "params", "waveTrigger", "p", "name", "LevelEditorEventUI", "update", "dt", "i", "conditions", "length", "cond", "targetElemID", "_targetElem", "elems", "node", "scene", "getComponentsInChildren", "elem", "elemID", "initByLevelData", "condition", "push", "triggers", "trigger", "loadAny", "err", "console", "error", "prefab", "k", "param", "fillLevelData", "for<PERSON>ach"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAA2CC,MAAAA,M,OAAAA,M;AAA4CC,MAAAA,Q,OAAAA,Q;AAA4BC,MAAAA,Y,OAAAA,Y;AAAuFC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAEjOC,MAAAA,oB,iBAAAA,oB;;AACAC,MAAAA,oB,iBAAAA,oB;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEuBC,MAAAA,yB,iBAAAA,yB;;AACvBC,MAAAA,wB,iBAAAA,wB;;AAGAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2ChB,U;;yCAUpCiB,uB,WADZH,OAAO,CAAC,yBAAD,C,UAKHC,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACjB,IAAI;AAAA;AAAA;AADH,OAAD,C,UAYRc,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEf,QADA;;AAENgB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BE,GAA9C;AACH;;AAJK,OAAD,C,UAcRL,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEZ,SADA;;AAENa,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BG,KAA9C;AACH;;AAJK,OAAD,C,UAmBRN,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEhB,MADA;;AAENiB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,UAkBRP,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEb,SADA;;AAENc,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,UAcRP,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAE;AAAA;AAAA,yDADA;;AAENC,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BI,IAA9C;AACH;;AAJK,OAAD,C,2BAlFb,MACaL,uBADb,CACoC;AAAA;AAAA,eACzBM,MADyB,GAChB,CADgB;AAAA,eAEzBC,IAFyB,GAEM;AAAA;AAAA,qEAFN;AAAA,eA6BzBC,MA7ByB,GA6BL,IA7BK;AAAA,eAgDzBC,KAhDyB,GAgDT,IAhDS;AAAA,eAgFzBC,OAhFyB,GAgFS,EAhFT;AAAA;;AAOjB,YAAJT,IAAI,GAA6B;AACxC,iBAAO,KAAKM,IAAL,CAAUI,KAAjB;AACH;;AACc,YAAJV,IAAI,CAACW,KAAD,EAAmC;AAC9C,cAAI,KAAKL,IAAL,CAAUI,KAAV,IAAmBC,KAAvB,EAA8B;AAC1B,iBAAKL,IAAL,GAAY;AAAA;AAAA,0CAAW;AAACI,cAAAA,KAAK,EAAEC;AAAR,aAAX,CAAZ;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKN,IAAN,CAAwCM,OAA/C;AACH;;AACiB,YAAPA,OAAO,CAACD,KAAD,EAAgB;AAC7B,eAAKL,IAAN,CAAwCM,OAAxC,GAAkDD,KAAlD;AACH;;AASe,YAALE,KAAK,GAAc;AAC1B,iBAAO,KAAKN,MAAZ;AACH;;AACe,YAALM,KAAK,CAACF,KAAD,EAAmB;AAC/B,eAAKJ,MAAL,GAAcI,KAAd;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKL,IAAN,CAA0CQ,SAA1C,GAAsDH,KAAK,CAACI,IAA5D;AACH,WAFD,MAEO;AACF,iBAAKT,IAAN,CAA0CQ,SAA1C,GAAsD,EAAtD;AACH;AACJ;;AASc,YAAJE,IAAI,GAAW;AACtB,iBAAO,KAAKR,KAAZ;AACH;;AACc,YAAJQ,IAAI,CAACL,KAAD,EAAgB;AAC3B,eAAKH,KAAL,GAAaG,KAAb;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKL,IAAN,CAAyCW,QAAzC,GAAoDN,KAAK,CAACI,IAA1D;AACH,WAFD,MAEO;AACF,iBAAKT,IAAN,CAAyCW,QAAzC,GAAoD,EAApD;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKZ,IAAN,CAAyCY,OAAhD;AACH;;AACiB,YAAPA,OAAO,CAACP,KAAD,EAAgB;AAC7B,eAAKL,IAAN,CAAyCY,OAAzC,GAAmDP,KAAnD;AACH;;AASgB,YAANQ,MAAM,GAA2B;AACxC,iBAAO,KAAKV,OAAZ;AACH;;AACgB,YAANU,MAAM,CAACR,KAAD,EAAgC;AAC7C,eAAKF,OAAL,GAAeE,KAAf;AACA,cAAIS,WAAW,GAAG,KAAKd,IAAvB;AACAc,UAAAA,WAAW,CAACD,MAAZ,GAAqB,EAArB;;AACA,eAAK,IAAIE,CAAT,IAAc,KAAKZ,OAAnB,EAA4B;AACxBW,YAAAA,WAAW,CAACD,MAAZ,CAAmBE,CAAC,CAACC,IAArB,IAA6BD,CAAC,CAACV,KAA/B;AACH;AACJ;;AAjG+B,O;;oCAuGvBY,kB,YAFZ3B,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,WAEbD,QAAQ,CAAC;AAAA;AAAA,uDAAD,C,WAERA,QAAQ,CAAC,CAACE,uBAAD,CAAD,C,6CALb,MAEawB,kBAFb;AAAA;AAAA,kDAE0D;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAM/CC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,kBAAMG,IAAI,GAAG,KAAKF,UAAL,CAAgBD,CAAhB,CAAb;AACAG,YAAAA,IAAI,CAACxB,MAAL,GAAcqB,CAAd;;AACA,gBAAIG,IAAI,CAAC7B,IAAL,IAAa;AAAA;AAAA,0EAA2BI,IAAxC,IACIyB,IAAI,CAACvB,IAAN,CAA0CwB,YAA1C,IAA0D,EAD7D,IAEGD,IAAI,CAACE,WAAL,IAAoB,IAF3B,EAEiC;AAC7B,oBAAMC,KAAK,GAAG,KAAKC,IAAL,CAAUC,KAAV,CAAgBC,uBAAhB;AAAA;AAAA,yDAAd;;AACA,mBAAK,IAAIC,IAAT,IAAiBJ,KAAjB,EAAwB;AACpB,oBAAII,IAAI,CAACC,MAAL,IAAgBR,IAAI,CAACvB,IAAN,CAA0CwB,YAA7D,EAA2E;AACvED,kBAAAA,IAAI,CAACE,WAAL,GAAmBK,IAAnB;AACA;AACH;AACJ;AACJ;AACJ;AACJ;;AAEME,QAAAA,eAAe,CAAChC,IAAD,EAAuB;AACzC,gBAAMgC,eAAN,CAAsBhC,IAAtB;;AACA,cAAIA,IAAI,CAACqB,UAAT,EAAqB;AACjB,iBAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,IAAI,CAACqB,UAAL,CAAgBC,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,oBAAMa,SAAS,GAAG;AAAA;AAAA,iEAAlB;AACAA,cAAAA,SAAS,CAAClC,MAAV,GAAmBqB,CAAnB;AACAa,cAAAA,SAAS,CAACjC,IAAV,GAAiBA,IAAI,CAACqB,UAAL,CAAgBD,CAAhB,CAAjB;AACA,mBAAKC,UAAL,CAAgBa,IAAhB,CAAqBD,SAArB;AACH;AACJ;;AACD,cAAIjC,IAAI,CAACmC,QAAT,EAAmB;AACf,iBAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,IAAI,CAACmC,QAAL,CAAcb,MAAlC,EAA0CF,CAAC,EAA3C,EAA+C;AAC3C,oBAAMgB,OAAO,GAAG,IAAI3C,uBAAJ,EAAhB;AACA2C,cAAAA,OAAO,CAACrC,MAAR,GAAiBqB,CAAjB;AACAgB,cAAAA,OAAO,CAACpC,IAAR,GAAeA,IAAI,CAACmC,QAAL,CAAcf,CAAd,CAAf;AACA,mBAAKe,QAAL,CAAcD,IAAd,CAAmBE,OAAnB;;AACA,kBAAIA,OAAO,CAACpC,IAAR,CAAaI,KAAb,IAAsB;AAAA;AAAA,0EAA0BP,KAApD,EAA2D;AACvD,oBAAIY,IAAI,GAAI2B,OAAO,CAACpC,IAAT,CAA6CQ,SAAxD;AACA5B,gBAAAA,YAAY,CAACyD,OAAb,CAAqB;AAAC5B,kBAAAA,IAAI,EAACA;AAAN,iBAArB,EAAkC,CAAC6B,GAAD,EAAM/B,KAAN,KAA0B;AACxD,sBAAI+B,GAAJ,EAAS;AACLC,oBAAAA,OAAO,CAACC,KAAR,CAAc,mDAAd,EAAmEF,GAAnE;AACA;AACH;;AACDF,kBAAAA,OAAO,CAACnC,MAAR,GAAiBM,KAAjB;AACH,iBAND;AAOH;;AACD,kBAAI6B,OAAO,CAACpC,IAAR,CAAaI,KAAb,IAAsB;AAAA;AAAA,0EAA0BN,IAApD,EAA0D;AACtD,oBAAIgB,WAAW,GAAGsB,OAAO,CAACpC,IAA1B;AACA,oBAAIS,IAAI,GAAGK,WAAW,CAACH,QAAvB;AACA/B,gBAAAA,YAAY,CAACyD,OAAb,CAAqB;AAAC5B,kBAAAA,IAAI,EAACA;AAAN,iBAArB,EAAkC,CAAC6B,GAAD,EAAMG,MAAN,KAAwB;AACtD,sBAAIH,GAAJ,EAAS;AACLC,oBAAAA,OAAO,CAACC,KAAR,CAAc,yDAAd,EAAyEF,GAAzE;AACA;AACH;;AACDF,kBAAAA,OAAO,CAAClC,KAAR,GAAgBuC,MAAhB;AACH,iBAND;AAOAL,gBAAAA,OAAO,CAACjC,OAAR,GAAkB,EAAlB;;AACA,qBAAK,IAAIuC,CAAT,IAAc5B,WAAW,CAACD,MAA1B,EAAkC;AAC9B,sBAAI8B,KAAK,GAAG;AAAA;AAAA,qEAAZ;AACAA,kBAAAA,KAAK,CAAC3B,IAAN,GAAa0B,CAAb;AACAC,kBAAAA,KAAK,CAACtC,KAAN,GAAcS,WAAW,CAACD,MAAZ,CAAmB6B,CAAnB,CAAd;;AACAN,kBAAAA,OAAO,CAACjC,OAAR,CAAgB+B,IAAhB,CAAqBS,KAArB;AACH;AACJ;AACJ;AACJ;AACJ;;AAEMC,QAAAA,aAAa,CAAC5C,IAAD,EAAuB;AACvC,gBAAM4C,aAAN,CAAoB5C,IAApB;AACAA,UAAAA,IAAI,CAACqB,UAAL,GAAkB,EAAlB;AACA,eAAKA,UAAL,CAAgBwB,OAAhB,CAAyBtB,IAAD,IAAU;AAC9B,gBAAIA,IAAI,IAAI,IAAZ,EAAkB;AACdvB,cAAAA,IAAI,CAACqB,UAAL,CAAgBa,IAAhB,CAAqBX,IAAI,CAACvB,IAA1B;AACH;AACJ,WAJD;AAKA,eAAKmC,QAAL,CAAcU,OAAd,CAAuBT,OAAD,IAAa;AAC/B,gBAAIA,OAAO,IAAI,IAAf,EAAqB;AACjBpC,cAAAA,IAAI,CAACmC,QAAL,CAAcD,IAAd,CAAmBE,OAAO,CAACpC,IAA3B;AACH;AACJ,WAJD;AAKH;;AArFqD,O;;;;;iBAEV,E;;;;;;;iBAEC,E", "sourcesContent": ["import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip } from 'cc';\r\nimport { LevelDataEvent } from '../../scripts/leveldata/leveldata';\r\nimport { LevelEditorWaveParam } from './LevelEditorWaveUI';\r\nimport { LevelEditorCondition } from './LevelEditorCondition';\r\nimport { LevelEditorElemUI } from './LevelEditorElemUI';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from '../../scripts/leveldata/trigger/LevelDataEventTrigger';\r\nimport { LevelDataEventTriggerLog } from '../../scripts/leveldata/trigger/LevelDataEventTriggerLog';\r\nimport { LevelDataEventTriggerAudio } from '../../scripts/leveldata/trigger/LevelDataEventTriggerAudio';\r\nimport { LevelDataEventTriggerWave } from '../../scripts/leveldata/trigger/LevelDataEventTriggerWave';\r\nimport { newTrigger } from '../../scripts/leveldata/trigger/newTrigger';\r\nimport { LevelDataEventCondtionType } from '../../scripts/leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionWave } from '../../scripts/leveldata/condition/LevelDataEventCondtionWave';\r\n\r\n@ccclass('LevelEditorEventTrigger')\r\nexport class LevelEditorEventTrigger{\r\n    public _index = 0;\r\n    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventTriggerType),\r\n    })\r\n    public get type(): LevelDataEventTriggerType{\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventTriggerType) {\r\n        if (this.data._type != value) {\r\n            this.data = newTrigger({_type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCString,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Log ;\r\n        }\r\n    })\r\n    public get message(): string {\r\n        return (this.data as LevelDataEventTriggerLog).message;\r\n    }\r\n    public set message(value: string) {\r\n        (this.data as LevelDataEventTriggerLog).message = value;\r\n    }\r\n\r\n    public _audio: AudioClip = null;\r\n    @property({\r\n        type :AudioClip,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Audio;\r\n        }\r\n    })\r\n    public get audio(): AudioClip {\r\n        return this._audio;\r\n    }\r\n    public set audio(value: AudioClip) {\r\n        this._audio = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = \"\";\r\n        }\r\n    }\r\n\r\n    public _wave: Prefab = null;\r\n    @property({\r\n        type: Prefab,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get wave(): Prefab {\r\n        return this._wave;\r\n    }\r\n    public set wave(value: Prefab) {\r\n        this._wave = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerWave).waveUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerWave).waveUUID = \"\";\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCInteger,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get planeID(): number {\r\n        return (this.data as LevelDataEventTriggerWave).planeID;\r\n    }\r\n    public set planeID(value: number) {\r\n        (this.data as LevelDataEventTriggerWave).planeID = value;\r\n    }\r\n\r\n    public _params: LevelEditorWaveParam[] = [];\r\n    @property({\r\n        type: [LevelEditorWaveParam],\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get params(): LevelEditorWaveParam[] {\r\n        return this._params;\r\n    }\r\n    public set params(value: LevelEditorWaveParam[]) {\r\n        this._params = value;\r\n        let waveTrigger = this.data as LevelDataEventTriggerWave;\r\n        waveTrigger.params = {};\r\n        for (let p of this._params) {\r\n            waveTrigger.params[p.name] = p.value;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n@ccclass('LevelEditorEventUI')\r\n@executeInEditMode()\r\nexport class LevelEditorEventUI extends LevelEditorElemUI {\r\n    @property([LevelEditorCondition])\r\n    public conditions: LevelEditorCondition[] = [];\r\n    @property([LevelEditorEventTrigger])\r\n    public triggers: LevelEditorEventTrigger[] = [];\r\n\r\n    public update(dt: number): void {\r\n        for (let i = 0; i < this.conditions.length; i++) {\r\n            const cond = this.conditions[i];\r\n            cond._index = i;\r\n            if (cond.type == LevelDataEventCondtionType.Wave \r\n                && (cond.data as LevelDataEventCondtionWave).targetElemID != \"\" \r\n                && cond._targetElem == null) {\r\n                const elems = this.node.scene.getComponentsInChildren(LevelEditorElemUI);\r\n                for (let elem of elems) {\r\n                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {\r\n                        cond._targetElem = elem;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataEvent) {\r\n        super.initByLevelData(data)\r\n        if (data.conditions) {\r\n            for (let i = 0; i < data.conditions.length; i++) {\r\n                const condition = new LevelEditorCondition();\r\n                condition._index = i;\r\n                condition.data = data.conditions[i];\r\n                this.conditions.push(condition);\r\n            }\r\n        }\r\n        if (data.triggers) {\r\n            for (let i = 0; i < data.triggers.length; i++) {\r\n                const trigger = new LevelEditorEventTrigger();\r\n                trigger._index = i;\r\n                trigger.data = data.triggers[i];\r\n                this.triggers.push(trigger);\r\n                if (trigger.data._type == LevelDataEventTriggerType.Audio) {\r\n                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;\r\n                    assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {\r\n                        if (err) {\r\n                            console.error(\"LevelEditorEventUI initByLevelData load audio err\", err);\r\n                            return;\r\n                        }\r\n                        trigger._audio = audio;\r\n                    });\r\n                }\r\n                if (trigger.data._type == LevelDataEventTriggerType.Wave) {\r\n                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;\r\n                    let uuid = waveTrigger.waveUUID;\r\n                    assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {\r\n                        if (err) {\r\n                            console.error(\"LevelEditorEventUI initByLevelData load wave prefab err\", err);\r\n                            return;\r\n                        }\r\n                        trigger._wave = prefab;\r\n                    });\r\n                    trigger._params = []\r\n                    for (let k in waveTrigger.params) {\r\n                        let param = new LevelEditorWaveParam();\r\n                        param.name = k;\r\n                        param.value = waveTrigger.params[k];\r\n                        trigger._params.push(param);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataEvent) {\r\n        super.fillLevelData(data)\r\n        data.conditions = []\r\n        this.conditions.forEach((cond) => {\r\n            if (cond != null) {\r\n                data.conditions.push(cond.data);\r\n            }\r\n        })\r\n        this.triggers.forEach((trigger) => {\r\n            if (trigger != null) {\r\n                data.triggers.push(trigger.data);\r\n            }\r\n        })\r\n    }\r\n}"]}