{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/EventActionData.ts"], "names": ["EmitterActionMapping", "_decorator", "Enum", "ccclass", "property", "eEmitterActionType", "eBulletActionType", "eEasing", "type", "name", "EmitterActionData", "displayName", "Linear", "BulletActionData"], "mappings": ";;;oFAqGaA,oB;;;;;;;;;;;;;;;AArGJC,MAAAA,U,OAAAA,U;AAAqCC,MAAAA,I,OAAAA,I;;;;;;;;;OACxC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;AAE9B;AACA;AACA;AACA;;oCACYI,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;cA8CZ;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;mCACYC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;yBAqBAC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;;sCAMCP,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eACvBQ,IADuB;AAAA,eAEvBC,IAFuB;AAAA;;AAAA,O;AAMlC;AACA;AACA;AACA;;;mCAEaC,iB,WADZP,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEN,IAAI,CAACG,kBAAD,CAAZ;AAAkCM,QAAAA,WAAW,EAAE;AAA/C,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRP,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEN,IAAI,CAACK,OAAD,CAAZ;AAAuBI,QAAAA,WAAW,EAAE;AAApC,OAAD,C,4BApBb,MACaD,iBADb,CAC+B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAiBJ;AAjBI;AAAA;;AAAA,O;;;;;;;;;;iBAKN,K;;;;;;;iBAGD,C;;;;;;;iBAGA,C;;;;;;;iBAGE,K;;;;;;;iBAGF,C;;;;;;;iBAGDH,OAAO,CAACK,M;;;AAG/B;AACA;AACA;AACA;;;kCAEaC,gB,YADZV,OAAO,CAAC,kBAAD,C,WAEHC,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEN,IAAI,CAACI,iBAAD,CAAZ;AAAiCK,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,WAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEI,QAAAA,IAAI,EAAEN,IAAI,CAACK,OAAD,CAAZ;AAAuBI,QAAAA,WAAW,EAAE;AAApC,OAAD,C,6BApBb,MACaE,gBADb,CAC8B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAiBH;AAjBG;AAAA;;AAAA,O;;;;;;;;;;iBAKL,K;;;;;;;iBAGD,C;;;;;;;iBAGA,C;;;;;;;iBAGE,K;;;;;;;iBAGF,C;;;;;;;iBAGDN,OAAO,CAACK,M", "sourcesContent": ["import { _decorator, error, v2, Vec2, Prefab, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是发射器的行为\r\n */\r\nexport enum eEmitterActionType {\r\n    Emitter_Active = 1,         // 发射器是否启用\r\n    Emitter_InitialDelay,       // 发射器当前的初始延迟\r\n    Emitter_Prewarm,            // 发射器是否启用预热\r\n    Emitter_PrewarmDuration,    // 发射器预热的持续时间\r\n    Emitter_Duration,           // 发射器配置的持续时间\r\n    Emitter_ElapsedTime,        // 发射器已运行的时间\r\n    Emitter_Loop,               // 发射器是否循环\r\n    Emitter_LoopInterval,       // 发射器循环的间隔时间\r\n\r\n    Emitter_PerEmitInterval,   // 发射器开火间隔\r\n    Emitter_PerEmitCount,      // 发射器开火次数\r\n    Emitter_PerEmitOffsetX,    // 发射器开火偏移\r\n\r\n    Emitter_Angle,             // 发射器弹道角度\r\n    Emitter_Count,             // 发射器弹道数量\r\n\r\n    Bullet_Duration,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_ColorA,\r\n    Bullet_FaceMovingDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n    \r\n    Unit_Life,\r\n    Unit_LifePercent,\r\n    Unit_PosX,\r\n    Unit_PosY,\r\n    Unit_Speed,\r\n    Unit_SpeedAngle,\r\n    Unit_Acceleration,\r\n    Unit_AccelerationAngle,\r\n}\r\n\r\n// const map:[] = {\r\n//     {eEmitter_Active, \"active\", ValueType.Boolean },\r\n// }\r\n\r\n// class PropertyContainer {\r\n//     [key: string]: any;\r\n// }\r\n\r\n// class Emitter {\r\n//     container: PropertyContainer;\r\n\r\n//     init(): void {\r\n//         this.container = new PropertyContainer();\r\n//         this.container[\"active\"] = true;\r\n//     }\r\n// }\r\n\r\n/**\r\n * ActionType对应要修改的属性\r\n * 以下是子弹的行为\r\n */\r\nexport enum eBulletActionType {\r\n    Bullet_Duration = 100,\r\n    Bullet_ElapsedTime,\r\n    Bullet_PosX,\r\n    Bullet_PosY,\r\n    Bullet_Damage,\r\n    Bullet_Speed,\r\n    Bullet_SpeedAngle,\r\n    Bullet_Acceleration,\r\n    Bullet_AccelerationAngle,\r\n    Bullet_Scale,\r\n    Bullet_ColorR,\r\n    Bullet_ColorG,\r\n    Bullet_ColorB,\r\n    Bullet_ColorA,\r\n    Bullet_FaceMovingDir,\r\n    Bullet_TrackingTarget,\r\n    Bullet_Destructive,\r\n    Bullet_DestructiveOnHit,\r\n}\r\n\r\nexport enum eEasing {\r\n    Linear,\r\n    InSine, OutSine, InOutSine,\r\n    InQuad, OutQuad, InOutQuad\r\n}\r\n\r\nexport class EmitterActionMapping {\r\n    public type: eEmitterActionType;\r\n    public name: string;\r\n\r\n}\r\n\r\n/**\r\n * 发射器行为数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"EmitterActionData\")\r\nexport class EmitterActionData {\r\n    @property({ type: Enum(eEmitterActionType), displayName: '行为类型' })\r\n    actionType : eEmitterActionType;\r\n\r\n    @property({ displayName: '是否随机' })\r\n    isRandom : boolean = false;\r\n\r\n    @property({ displayName: '最小值' })\r\n    minValue : number = 0;\r\n\r\n    @property({ displayName: '最大值' })\r\n    maxValue : number = 0;\r\n\r\n    @property({ displayName: 'bool值' })\r\n    boolValue : boolean = false;\r\n\r\n    @property({ displayName: '持续时间' })\r\n    duration : number = 0; // 持续时间: 0表示立即执行\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    easing : eEasing = eEasing.Linear;\r\n}\r\n\r\n/**\r\n * 子弹行为数据\r\n * 所有时间相关的，单位都是秒(s)\r\n */\r\n@ccclass(\"BulletActionData\")\r\nexport class BulletActionData {\r\n    @property({ type: Enum(eBulletActionType), displayName: '行为类型' })\r\n    actionType : eBulletActionType;\r\n\r\n    @property({ displayName: '是否随机' })\r\n    isRandom : boolean = false;\r\n\r\n    @property({ displayName: '最小值' })\r\n    minValue : number = 0;\r\n\r\n    @property({ displayName: '最大值' })\r\n    maxValue : number = 0;\r\n\r\n    @property({ displayName: 'bool值' })\r\n    boolValue : boolean = false;\r\n\r\n    @property({ displayName: '持续时间' })\r\n    duration : number = 0; // 持续时间: 0表示立即执行\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    easing : eEasing = eEasing.Linear;\r\n}"]}