import { SingletonBase } from "../core/base/SingletonBase";
import { GameDataManager } from "./manager/GameDataManager";
import { MainPlaneManager } from "./manager/MainPlaneManager";
import { BattleManager } from "./manager/BattleManager";
import { BossManager } from "./manager/BossManager";
import { BulletManager } from "./manager/BulletManager";
import { EnemyManager } from "./manager/EnemyManager";
import { GameRuleManager } from "./manager/GameRuleManager";
import { HurtEffectManager } from "./manager/HurtEffectManager";
import { PlaneManager } from "./manager/PlaneManager";
import { StageManager } from "./manager/StageManager";;
import GameResManager from "./manager/GameResManager";
import { SceneManager } from "./manager/SceneManager";
import WaveManager from "./manager/WaveManager";
import { GameMain } from "./scenes/GameMain";
import PrefabManager from "./manager/PrefabManager";
import FColliderManager from "./collider-system/FColliderManager";

class _GameIns extends SingletonBase<_GameIns> {
    get prefabManager() { return PrefabManager.getInstance<PrefabManager>(PrefabManager); }
    get battleManager() { return BattleManager.getInstance<BattleManager>(BattleManager); }
    get bossManager() { return BossManager.getInstance<BossManager>(BossManager); }
    get bulletManager() { return BulletManager.getInstance<BulletManager>(BulletManager); }
    get enemyManager() { return EnemyManager.getInstance<EnemyManager>(EnemyManager); }
    get gameDataManager() { return GameDataManager.getInstance<GameDataManager>(GameDataManager); }
    get gameRuleManager() { return GameRuleManager.getInstance<GameRuleManager>(GameRuleManager); }
    get hurtEffectManager() { return HurtEffectManager.getInstance<HurtEffectManager>(HurtEffectManager); }
    get mainPlaneManager() { return MainPlaneManager.getInstance<MainPlaneManager>(MainPlaneManager); }
    get planeManager() { return PlaneManager.getInstance<PlaneManager>(PlaneManager); }
    get stageManager() { return StageManager.getInstance<StageManager>(StageManager); }
    get gameResManager() { return GameResManager.getInstance<GameResManager>(GameResManager); }
    get sceneManager() { return SceneManager.getInstance<SceneManager>(SceneManager); }
    get waveManager() { return WaveManager.getInstance<WaveManager>(WaveManager); }
    get fColliderManager() { return FColliderManager.instance; }

    gameMainUI:GameMain = null;
}



export const GameIns: _GameIns = _GameIns.getInstance<_GameIns>(_GameIns);