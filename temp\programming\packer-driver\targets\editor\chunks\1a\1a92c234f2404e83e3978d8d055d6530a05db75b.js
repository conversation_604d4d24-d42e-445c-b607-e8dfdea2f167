System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, NodePool, Prefab, Sprite, SpriteAtlas, instantiate, SingletonBase, GameIns, Tools, TrackData, EnemyPlaneData, EnemyUIData, GameFunc, EnemyPlane, GameEnum, BattleLayer, ResourceList, MyApp, GameResourceList, EnemyManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackData(extras) {
    _reporterNs.report("TrackData", "../data/TrackData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneData(extras) {
    _reporterNs.report("EnemyPlaneData", "../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyUIData(extras) {
    _reporterNs.report("EnemyUIData", "../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFunc(extras) {
    _reporterNs.report("GameFunc", "../GameFunc", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResourceList(extras) {
    _reporterNs.report("ResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  _export("EnemyManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      NodePool = _cc.NodePool;
      Prefab = _cc.Prefab;
      Sprite = _cc.Sprite;
      SpriteAtlas = _cc.SpriteAtlas;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      TrackData = _unresolved_5.TrackData;
    }, function (_unresolved_6) {
      EnemyPlaneData = _unresolved_6.EnemyPlaneData;
      EnemyUIData = _unresolved_6.EnemyUIData;
    }, function (_unresolved_7) {
      GameFunc = _unresolved_7.GameFunc;
    }, function (_unresolved_8) {
      EnemyPlane = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameEnum = _unresolved_9.default;
    }, function (_unresolved_10) {
      BattleLayer = _unresolved_10.default;
    }, function (_unresolved_11) {
      ResourceList = _unresolved_11.default;
    }, function (_unresolved_12) {
      MyApp = _unresolved_12.MyApp;
    }, function (_unresolved_13) {
      GameResourceList = _unresolved_13.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7a8acsCga9NJbCkCaR42MV3", "EnemyManager", undefined);

      __checkObsolete__(['JsonAsset', 'NodePool', 'Prefab', 'resources', 'Sprite', 'SpriteAtlas', 'SpriteFrame', 'Node', 'instantiate', 'Vec2', 'Vec3']);

      _export("EnemyManager", EnemyManager = class EnemyManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        /**
         * 是否加载完成
         */
        get loadFinish() {
          return this._loadFinish;
        }

        constructor() {
          super();
          this._loadFinish = false;
          this._mainStage = -1;
          this._subStage = -1;
          this._uiDataMap = new Map();
          this._planeAnimMap = new Map();
          this._normalCount = 0;
          this._loadTotal = 0;
          this._loadCount = 0;
          this._trackDatas = new Map();
          this._planeDataMap = new Map();
          this.enemyAtlas = null;
          this._pfPlane = null;
          this._planePool = [];
          this._planeArr = [];
          this._willDeadPlane = [];
          this._animPlanePoolMap = new Map();
          this._mapEnemyPool = new NodePool();
          this.missileAtlas = null;
          this.initConfig();
        }

        initConfig() {
          let tracks = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbTrack.getDataList();

          for (let track of tracks) {
            const trackData = new (_crd && TrackData === void 0 ? (_reportPossibleCrUseOfTrackData({
              error: Error()
            }), TrackData) : TrackData)();
            trackData.loadJson(track);

            this._trackDatas.set(trackData.trackID, trackData);
          }

          let enemyUIDatas = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEnemyUI.getDataList();

          for (let data of enemyUIDatas) {
            const uiData = new (_crd && EnemyUIData === void 0 ? (_reportPossibleCrUseOfEnemyUIData({
              error: Error()
            }), EnemyUIData) : EnemyUIData)();
            uiData.loadJson(data);

            this._uiDataMap.set(uiData.id, uiData);
          }

          let planeList = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEnemy.getDataList();

          for (let plane of planeList) {
            const planeData = new (_crd && EnemyPlaneData === void 0 ? (_reportPossibleCrUseOfEnemyPlaneData({
              error: Error()
            }), EnemyPlaneData) : EnemyPlaneData)();
            planeData.loadJson(plane);

            this._planeDataMap.set(planeData.id, planeData);
          }
        }
        /**
         * 预加载资源
         * @param stage 当前关卡
         */


        preLoad(stage) {
          // 加载敌机资源
          const enemyResources = [(_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EnemyPlane];
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load(enemyResources, Prefab, async () => {
            this._pfPlane = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).EnemyPlane, Prefab);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          }); // 加载子弹图集

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_enemyBullet, SpriteAtlas, (error, atlas) => {
            this.missileAtlas = atlas;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          }); // 加载敌机图集

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load((_crd && ResourceList === void 0 ? (_reportPossibleCrUseOfResourceList({
            error: Error()
          }), ResourceList) : ResourceList)[`atlas_package_enemy${stage}`], SpriteAtlas, (error, atlas) => {
            this.enemyAtlas = atlas;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });

          for (let k in this._uiDataMap) {
            let uiData = this._uiDataMap[k];

            if (uiData.isAm) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.addLoadCount(1);
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.load("Game/" + uiData.image, Prefab, (error, prefab) => {
                this._planeAnimMap.set(uiData.image, prefab);

                (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager.checkLoadFinish();
              });
            }
          }
        }
        /**
         * 初始化敌人管理器
         * @param mainStage 主关卡
         * @param subStage 子关卡
         */


        initBattle(mainStage, subStage) {
          console.warn("stage", mainStage, subStage);

          if (this._mainStage !== mainStage) {
            this._mainStage = mainStage;
            this._subStage = subStage;

            if (this._planePool.length === 0) {
              const poolSize = 20;

              for (let i = 0; i < poolSize; i++) {
                const plane = this.createNewPlane();

                this._planePool.push(plane);
              }
            }
          } else {
            this._subStage = subStage;
          }
        }
        /**
        * 创建新的敌机节点
        * @param uiId 敌机 UI ID
        */


        createNewPlane(uiId = 0) {
          const node = instantiate(this._pfPlane);
          node.name = "plane";
          node.y = 1000;
          const plane = node.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
            error: Error()
          }), EnemyPlane) : EnemyPlane);
          plane.preLoad();

          if (uiId > 0) {
            const uiData = this.getEnemyUIData(uiId);
            plane.preLoadUI(uiData);
            node.y = 1000;
          } else {
            plane.preLoadUI(null);
          }

          return node;
        }
        /**
         * 获取敌机 UI 数据
         * @param id 敌机 UI ID
         */


        getEnemyUIData(id) {
          return this._uiDataMap.get(id);
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          for (let i = 0; i < this._planeArr.length; i++) {
            const plane = this._planeArr[i];

            if (plane.removeAble) {
              this.removePlaneForIndex(i);
              i--;
            } else {
              if (plane.isDead) {
                if (plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Turret || plane.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyType.Ship) {
                  this._willDeadPlane.push(plane);

                  this._planeArr.splice(i, 1);

                  i--;
                  continue;
                }
              }

              plane.updateGameLogic(deltaTime);
            }
          }

          for (let i = 0; i < this._willDeadPlane.length; i++) {
            const plane = this._willDeadPlane[i];

            if (plane.removeAble) {
              this.removePlaneForIndex(i, true);
              i--;
            } else {
              plane.updateGameLogic(deltaTime);
            }
          }
        }
        /**
        * 重置主关卡
        */


        mainReset() {
          this.subReset();
          this._mainStage = -1;
          this._subStage = -1; // 清理飞机池

          for (const plane of this._planePool) {
            plane.destroy();
          }

          this._planePool.splice(0); // 清理即将死亡的飞机


          for (const plane of this._willDeadPlane) {
            if (plane && plane.node) {
              plane.node.destroy();
            }
          }

          this._willDeadPlane = []; // // 清理动画飞机池
          // this._animPlanePoolMap.forEach((pool) => {
          //     for (const node of pool) {
          //         node.destroy();
          //     }
          // });
          // this._animPlanePoolMap.clear();
        }
        /**
         * 重置子关卡
         */


        subReset() {
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          for (const plane of this._planeArr) {
            switch (plane.type) {
              case EnemyType.Normal:
                plane.willRemove();

                if (plane.sceneLayer < 0) {
                  var _plane$getUIData;

                  plane.node.y = 1000;
                  const pool = (_plane$getUIData = plane.getUIData()) != null && _plane$getUIData.isAm ? this._animPlanePoolMap.get(plane.getUIData().id) || [] : this._planePool;
                  pool.push(plane.node);
                } else {
                  this._mapEnemyPool.put(plane.node);
                }

                break;
            }

            plane.node.removeFromParent(false);
          }

          this._planeArr.splice(0);
        }
        /**
         * 清理敌人管理器
         */


        clear() {}
        /**
         * 获取敌机角色
         * @param uiId 敌机 UI ID
         */


        async getPlaneRole(uiId) {
          let prefab = this._planeAnimMap.get(uiId);

          if (!prefab) {
            prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync("Game/prefabs/" + uiId, Prefab);

            this._planeAnimMap.set(uiId, prefab);
          }

          return prefab;
        }
        /**
         * 检查敌人是否全部消灭
         */


        isEnemyOver() {
          return this._planeArr.length === 0;
        }
        /**
         * 获取普通敌机数量
         */


        getNormalPlaneCount() {
          return this._normalCount;
        }
        /**
         * 检查敌机是否为动画类型
         * @param id 敌机 ID
         */


        isEnemyAnim(id) {
          const planeData = this.getPlaneData(id);
          const uiData = this.getEnemyUIData(planeData.uiId);
          return (uiData == null ? void 0 : uiData.isAm) || false;
        }
        /**
         * 设置敌机的精灵帧
         * @param sprite 精灵组件
         * @param frameName 精灵帧名称
         */


        setPlaneFrame(sprite, frameName) {
          if (sprite && frameName !== "" && this.enemyAtlas) {
            sprite.spriteFrame = this.enemyAtlas.getSpriteFrame(frameName);
            sprite.sizeMode = Sprite.SizeMode.RAW;
          }
        }
        /**
         * 获取敌机的精灵帧
         * @param frameName 精灵帧名称
         */


        getPlaneFrame(frameName) {
          return this.enemyAtlas ? this.enemyAtlas.getSpriteFrame(frameName) : null;
        }
        /**
         * 根据轨迹 ID 获取轨迹数据
         * @param trackId 轨迹 ID
         */


        getTrackDataForID(trackId) {
          let trackData = null;

          try {
            trackData = this._trackDatas.get(trackId) || null;
          } catch (error) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error(`getTrackData error: ${trackId}`, error);
          }

          return trackData;
        }
        /**
         * 获取所有敌机
         */


        get planes() {
          return this._planeArr;
        }
        /**
         * 移除所有存活的敌机
         */


        removeAllAlivePlane() {
          for (const plane of this._planeArr) {
            if (!plane.isDead) {
              plane.willRemove();
            }
          }
        }
        /**
         * 添加敌机到管理器
         * @param plane 敌机对象
         */


        pushPlane(plane) {
          if (!(_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).arrContain(this._planeArr, plane)) {
            this._planeArr.push(plane);
          }
        }
        /**
         * 根据索引移除敌机
         * @param index 索引
         * @param isDead 是否为死亡敌机
         */


        removePlaneForIndex(index, isDead = false) {
          if (isDead) {
            this._willRemovePlane(this._willDeadPlane[index]);

            this._willDeadPlane.splice(index, 1);
          } else {
            this._willRemovePlane(this._planeArr[index]);

            this._planeArr.splice(index, 1);
          }
        } //     /**
        //      * 移除指定敌机
        //      * @param plane 敌机对象
        //      * @param isDead 是否为死亡敌机
        //      */
        //     removePlane(plane: EnemyPlane, isDead: boolean = false) {
        //         this._willRemovePlane(plane);
        //         if (isDead) {
        //             Tools.arrRemove(this._willDeadPlane, plane);
        //         } else {
        //             Tools.arrRemove(this._planeArr, plane);
        //         }
        //     }

        /**
         * 处理即将移除的敌机
         * @param plane 敌机对象
         */


        _willRemovePlane(plane) {
          let EnemyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyType;

          switch (plane.type) {
            case EnemyType.Normal:
              if (plane.sceneLayer < 0) {
                var _plane$getUIData2;

                plane.node.y = 1000;
                this._normalCount--;
                const pool = (_plane$getUIData2 = plane.getUIData()) != null && _plane$getUIData2.isAm ? this._animPlanePoolMap.get(plane.getUIData().id) || [] : this._planePool;
                pool.push(plane.node);
              } else {
                plane.node.x = 1000;

                this._mapEnemyPool.put(plane.node);
              }

              break;
          }

          plane.node.removeFromParent();
        }
        /**
         * 根据敌机 ID 获取敌机数据
         * @param id 敌机 ID
         */


        getPlaneData(id) {
          const data = this._planeDataMap.get(id);

          if (!data) {
            (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).error(`getEnemyData error: ${id}`);
          }

          return data;
        }
        /**
         * 添加敌机
         * @param id 敌机 ID
         */


        async addPlane(id) {
          try {
            const planeData = this.getPlaneData(id);
            let node = null;

            if (this.isEnemyAnim(id)) {
              let pool = this._animPlanePoolMap.get(planeData.uiId);

              if (!pool) {
                pool = [];

                this._animPlanePoolMap.set(planeData.uiId, pool);
              }

              node = pool.pop() || this.createNewPlane(planeData.uiId);
            } else {
              node = this._planePool.pop() || this.createNewPlane();
            }

            (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me.addEnemy(node);
            const plane = node.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
              error: Error()
            }), EnemyPlane) : EnemyPlane);
            await plane.init(planeData);
            plane.new_uuid = (_crd && GameFunc === void 0 ? (_reportPossibleCrUseOfGameFunc({
              error: Error()
            }), GameFunc) : GameFunc).uuid;
            plane.setScaleType(this.getEnemyRangeType(id));
            this.pushPlane(plane); // if (GameIns.battleManager.shadowAble(GameIns.gameDataManager.curMainStage)) {
            //     const uiData = this.getEnemyUIData(planeData.uiId);
            //     plane.shadowComp = ShadowManager.addShadow(uiData.image + "y", node, v2(99, -165));
            // }

            this._normalCount++;
            return plane;
          } catch (error) {
            return null;
          }
        }
        /**
         * 检查敌人死亡炸弹
         * @param position 炸弹位置
         */


        checkEnemyDieBomb(position) {// if (this._dieBombAtk > 0) {
          //     const enemies = GameFunc.getRangeEnemys(position, this._dieBombRadius);
          //     enemies.forEach((enemy) => {
          //         if (!enemy.isDead) {
          //             enemy.hurt(this._dieBombAtk);
          //         }
          //     });
          //     EnemyEffectLayer.me.playDieBombAnim(position);
          // }
        }
        /**
         * 获取敌机的范围类型
         * @param id 敌机 ID
         */


        getEnemyRangeType(id) {
          if (id >= 1000 && id <= 1999 || id >= 4000 && id <= 4999) {
            return 0; // 小型
          } else if (id >= 2000 && id <= 2999 || id >= 5000 && id <= 6999) {
            return 1; // 中型
          } else if (id >= 3000 && id <= 3999) {
            return 2; // 大型
          }

          return -1; // 未知
        }
        /**
         * 添加护盾
         */


        addShield() {// let shieldNode = this._shieldPool.pop();
          // if (!shieldNode) {
          //     shieldNode = instantiate(this._pfShield);
          //     shieldNode.getComponent(EnemyShield).preLoad();
          // }
          // const shield = shieldNode.getComponent(EnemyShield);
          // this._shieldArr.push(shield);
          // return shield;
        }
        /**
         * 移除护盾
         * @param shield 要移除的护盾
         */


        removeShield(shield) {// shield.node.parent = null;
          // Tools.arrRemove(this._shieldArr, shield);
          // this._shieldPool.push(shield.node);
        } // /**
        //  * 获取所有护盾
        //  */
        // get shieldArr(): EnemyShield[] {
        //     return this._shieldArr;
        // }


      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1a92c234f2404e83e3978d8d055d6530a05db75b.js.map