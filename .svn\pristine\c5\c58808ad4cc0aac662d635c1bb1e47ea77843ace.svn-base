import { _decorator, Component, Node, Prefab, NodePool, Vec3, sp, SpriteFrame, instantiate, UIOpacity, UITransform } from 'cc';


const { ccclass, property } = _decorator;

@ccclass('EnemyEffectLayer')
export default class EnemyEffectLayer extends Component {
    static me: EnemyEffectLayer;

    hurtEffectLayer: Node = null;
    hurtNumLayer: Node = null;

    onLoad() {
        EnemyEffectLayer.me = this;
    }

    start() {
        this.hurtEffectLayer = new Node();
        this.hurtEffectLayer.addComponent(UITransform);
        this.hurtEffectLayer.parent = this.node;
        this.hurtEffectLayer.setPosition(0, 0);

        this.hurtNumLayer = new Node();
        this.hurtNumLayer.addComponent(UITransform);
        this.hurtNumLayer.parent = this.node;
        this.hurtNumLayer.setPosition(0, 0);
    }
}