import { director, Font, game, instantiate, Label, NodePool, ParticleSystem2D, Prefab, SpriteAtlas, Tween, UITransform, v3, Vec3, warn } from "cc";
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameIns } from "../GameIns";
import GameEnum from "../const/GameEnum";
import { Tools } from "../utils/Tools";
import { GameConst } from "../const/GameConst";
import ImageSequence from "../ui/base/ImageSequence";
import UIAnimMethods from "../ui/base/UIAnimMethods";
import PfFrameAnim from "../ui/base/PfFrameAnim";
import ResourceList from "../const/GameResourceList";
import GameResourceList from "../const/GameResourceList";
import EnemyEffectLayer from "../ui/layer/EnemyEffectLayer";
import FCollider from "../collider-system/FCollider";
import { MyApp } from "../../MyApp";



export class HurtEffectManager extends SingletonBase<HurtEffectManager> {

    coolTime = 5;
    ratio = 0.8;

    hurtNum: Prefab = null;
    hurtEffect: Prefab = null;

    m_hurtEffects = new Map();
    m_hurtAtlas = null;
    m_spriteFrames = [];
    m_spriteCounts = [];
    m_spriteNameAndCounts = new Map();
    m_nameAndImgs = new Map();
    m_hurtParticlePF = new Map();
    m_hurtParticles = new Map();
    m_hurtNums = new Map();
    m_hurtFont = new Map();

    m_bulletDieAnim = new NodePool();

    /**
     * 预加载资源
     */
    preLoad() {
        this.m_hurtParticles.clear();
        this.m_hurtParticlePF.clear();
        this.m_bulletDieAnim.clear();
        this.m_spriteNameAndCounts.clear();
        this.m_nameAndImgs.clear();

        if (!this.m_hurtAtlas) {
            GameIns.battleManager.addLoadCount(1);
            MyApp.resMgr.load(GameResourceList.atlas_hurtEffects,SpriteAtlas, (error,atlas) => {
                this.m_hurtAtlas = atlas;
                this.m_spriteFrames = this.m_hurtAtlas.getSpriteFrames();

                const uniqueNames = [];
                for (let i = 1; i < this.m_spriteFrames.length; i++) {
                    const name = this.m_spriteFrames[i].name.split("_")[0];
                    if (!Tools.arrContain(uniqueNames, name)) {
                        uniqueNames.push(name);
                    }
                }

                this.m_spriteCounts = [];
                for (const name of uniqueNames) {
                    let count = 0;
                    for (const frame of this.m_spriteFrames) {
                        if (name === frame.name.split("_")[0]) {
                            count++;
                        }
                    }
                    this.m_spriteNameAndCounts.set(name, count);
                }

                this.m_hurtEffects.clear();
                this.m_spriteNameAndCounts.forEach((count, name) => {
                    const frames = [];
                    for (let i = 1; i <= count; i++) {
                        frames.push(this.m_hurtAtlas.getSpriteFrame(`${name}_${i}`));
                    }

                    const effectInstances = [];
                    const effect = instantiate(this.hurtEffect).getComponent(ImageSequence);
                    effect.setData(frames);
                    effectInstances.push(effect);

                    this.m_hurtEffects.set(name, effectInstances);
                    this.m_nameAndImgs.set(name, frames);
                });

                GameIns.battleManager.checkLoadFinish();
            });
        }

        if (!this.hurtEffect) {
            GameIns.battleManager.addLoadCount(1);
            MyApp.resMgr.load(GameResourceList.HurtEffect,Prefab, (error,prefab) => {
                this.hurtEffect = prefab;
                GameIns.battleManager.checkLoadFinish();
            });
        }

        if (!this.hurtNum) {
            GameIns.battleManager.addLoadCount(1);
            MyApp.resMgr.load(GameResourceList.HurtNum, Prefab, (error,prefab) => {
                this.hurtNum = prefab;
                GameIns.battleManager.checkLoadFinish();
            });
        }

        if (!this.m_hurtFont) {
            this.m_hurtFont = new Map();
        }

        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.loadDir(GameResourceList.font_hurtNum, Font, (error,fonts) => {
            fonts.forEach((font) => {
                if (font) {
                    this.m_hurtFont.set(font.name, font);
                }
            });

            this.m_hurtNums.clear();
            this.m_hurtFont.forEach((font, name) => {
                const instances = [];
                for (let i = 0; i < 3; i++) {
                    const labelNode = instantiate(this.hurtNum);
                    const label = labelNode.getComponent(Label);
                    label.string = "";
                    label.font = font;
                    instances.push(label);
                }
                this.m_hurtNums.set(name, instances);
            });

            GameIns.battleManager.checkLoadFinish();
        });

        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(GameResourceList.Hurt0, Prefab,(error,prefab) => {
            this.m_hurtParticlePF.set("default", prefab);
            const particles = [];
            this.m_hurtParticles.set("default", particles);

            for (let i = 0; i < 10; i++) {
                const particleNode = instantiate(prefab);
                particles.push(particleNode.getComponent(ParticleSystem2D));
            }

            GameIns.battleManager.checkLoadFinish();
        });
    }


    /**
     * 清理资源
     */
    clear() {
        if (this.m_hurtAtlas && !GameConst.Cache) {
            MyApp.resMgr.releaseAssetByForce(this.m_hurtAtlas);
            this.m_hurtAtlas = null;
        }

        Tools.clearMapForCompArr(this.m_hurtEffects);
        this.m_spriteFrames = [];
        this.m_spriteCounts = [];
        this.m_spriteNameAndCounts.clear();
        this.m_nameAndImgs.clear();
        this.m_hurtParticlePF.clear();

        this.m_hurtParticles.forEach((particles) => {
            for (const particle of particles) {
                if (particle && particle.node) {
                    particle.node.destroy();
                }
            }
        });

        this.m_hurtParticles.clear();
        Tools.clearMapForCompArr(this.m_hurtNums);
        this.m_bulletDieAnim.clear();
    }

    createHurtEffect(pos: Vec3, particleType, scale = 1) {
        const particle = this.getHurtParticle(particleType) as ParticleSystem2D;
        if (particle) {
            pos = EnemyEffectLayer.me.hurtEffectLayer.getComponent(UITransform).convertToNodeSpaceAR(pos);
            particle.node.parent = EnemyEffectLayer.me.hurtEffectLayer;
            particle.node.setPosition(pos);
            particle.node.setScale(scale, scale);
            particle.node.active = true;

            if (particle.stopped) {
                particle.resetSystem();
            }

            particle.scheduleOnce(() => {
                if (particle && particle.node) {
                    particle.stopSystem();
                    this.pushHurtParticle(particle, particleType);
                }
            }, 0.12);
        }
    }

    pushHurtParticle(particle, particleType = "default") {
        setTimeout(() => {
            if (particle && particle.node) {
                if (!this.m_hurtParticles.has(particleType)) {
                    particleType = "default";
                }

                const particles = this.m_hurtParticles.get(particleType);
                if (particles) {
                    particles.push(particle);
                    particle.node.active = false;
                } else {
                    particle.node.destroy();
                }
            }
        }, 200);
    }

    /**
     * 获取伤害粒子
     * @param {string} particleType 粒子类型
     * @returns {ParticleSystem2D|null} 粒子系统
     */
    getHurtParticle(particleType = "default") {

        if (!this.m_hurtParticles.has(particleType)) {
            particleType = "default";
        }

        const particles = this.m_hurtParticles.get(particleType);
        if (particles && particles.length > 0) {
            const particle = particles.shift();
            if (particle && particle.node) {
                particle.node.active = true;
                return particle;
            }
        }

        const prefab = this.m_hurtParticlePF.get(particleType);
        if (prefab) {
            const newParticle = instantiate(prefab).getComponent(ParticleSystem2D);
            return newParticle;
        }

        return null;
    }
    /**
     * 播放子弹死亡动画
     * @param {Vec2} position 动画位置
     */
    playBulletDieAnim(position) {
        if (Tools.isPlaneOutScreen(position)) return;

        let animNode = this.m_bulletDieAnim.get();
        if (!animNode) {
            animNode = instantiate(GameIns.gameResManager.frameAnim);
            const animComponent = animNode.getComponent(PfFrameAnim);
            animComponent.init(
                GameIns.bulletManager.enemyComAtlas,
                "die_",
                7,
                GameConst.ActionFrameTime,
                () => {
                    animComponent.stop();
                    this.m_bulletDieAnim.put(animNode);
                }
            );
        }

        animNode.parent = EnemyEffectLayer.me.hurtEffectLayer;
        animNode.setPosition(position.x, position.y);

        const animComponent = animNode.getComponent(PfFrameAnim);
        if (animComponent) {
            animComponent.reset();
        }
    }

    /**
     * 根据类型创建伤害数字
     * @param {Object} collider 碰撞体
     * @param {Object} entity 实体对象
     * @param {number} damage 伤害值
     * @param {Object} offset 偏移量
     */
    createHurtNumByType(position:Vec3, damage:number, isCirt:boolean = false) {
        if (damage <= 0) return;

        const fontType = isCirt ? "yellowHurtNum" : "whiteHurtNum";
        const lab: Label = this.GetHurtNumsByCount(fontType, damage);

        if (lab && EnemyEffectLayer.me.hurtNumLayer) {
            lab.node.parent = EnemyEffectLayer.me.hurtNumLayer;
            lab.node.setPosition(position.x, position.y - 30);

            this.startHurtAni(lab, fontType);
        }
    }

    GetHurtNumsByCount(fontType, damage) {
        let hurtNum = null;
        const pool = this.m_hurtNums.get(fontType);

        if (pool) {
            if (pool.length > 0) {
                hurtNum = pool.pop();
            } else {
                hurtNum = instantiate(this.hurtNum).getComponent(Label);
                hurtNum.font = this.m_hurtFont.get(fontType);
            }
        }

        hurtNum.node.opacity = 255;
        hurtNum.node.active = true;
        hurtNum.string = Math.ceil(damage).toString();

        return hurtNum;
    }

    startHurtAni(hurtNum, fontType) {
        const ratio = this.ratio;
        Tween.stopAllByTarget(hurtNum.node);

        let tween: Tween = null;
        switch (fontType) {
            case "whiteHurtNum":
                hurtNum.node.setScale(0.15, 0.15);
                tween = new Tween(hurtNum.node)
                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.53 * ratio, 1.53 * ratio) })
                    .to(UIAnimMethods.fromTo(6, 11), { scale: v3(0.47 * ratio, 0.47 * ratio) })
                    .to(UIAnimMethods.fromTo(11, 32), { position: v3(hurtNum.node.x, hurtNum.node.y + 13), scale: v3(0.47 * ratio, 0.47 * ratio) });
                break;

            case "yellowHurtNum":
                hurtNum.node.setScale(0.16, 0.16);
                tween = new Tween(hurtNum.node)
                    .to(UIAnimMethods.fromTo(1, 6), { scale: v3(1.75 * ratio, 1.75 * ratio) })
                    .to(UIAnimMethods.fromTo(6, 9), { scale: v3(0.44 * ratio, 0.44 * ratio) })
                    .to(UIAnimMethods.fromTo(9, 12), { scale: v3(0.52 * ratio, 0.52 * ratio) })
                    .to(UIAnimMethods.fromTo(12, 31), { position: v3(hurtNum.node.x, hurtNum.node.y + 21), scale: v3(0.52 * ratio, 0.52 * ratio) });
                break;

            default:
                warn("Unknown font type in createHurtNumInTarget");
        }

        tween.call(() => {
            this.pushHurtNums(fontType, hurtNum);
        }).start();
    }

    pushHurtNums(fontType, hurtNum) {
        if (hurtNum && hurtNum.node) {
            hurtNum.string = "";
            hurtNum.node.active = false;

            const pool = this.m_hurtNums.get(fontType);
            if (pool) {
                pool.push(hurtNum);
            } else {
                hurtNum.node.destroy();
            }
        }
    }
}