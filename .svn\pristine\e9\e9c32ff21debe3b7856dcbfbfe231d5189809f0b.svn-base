import { _decorator, Button, Label, Node, Sprite } from 'cc';
import { EventMgr } from '../../../event/EventManager';
import { BaseUI, UILayer, UIMgr } from '../../UIMgr';
import { BattleUI } from '../BattleUI';
const { ccclass, property } = _decorator;

@ccclass('DialogueUI')
export class DialogueUI extends BaseUI {

    @property(Node)
    nodeLeft: Node;
    @property(Sprite)
    characterImageLeft: Sprite;
    @property(Label)
    characterNameLeft: Label;

    @property(Node)
    nodeRight: Node;
    @property(Sprite)
    characterImageRight: Sprite;
    @property(Label)
    characterNameRight: Label;
    @property(Label)
    dialogueContent: Label;

    @property(Button)
    btnClick: Button;

    //data
    dialogueID: number = 0;
    dialogueContentList: string[] = [];
    dialogueIndex: number = 0;

    public static getUrl(): string { return "ui/main/dialogue/DialogueUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    protected onLoad(): void {
        const randomDialogues = [
            "111",
            "222",
            "333",
            "444"
        ];
        for (let i = 0; i < randomDialogues.length; i++) {
            this.dialogueContentList.push(randomDialogues[i]);
        }
        this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];
        this.btnClick.node.on('click', this.onClick, this);
        this.nodeRight.active = false;
    }
    async onClick() {
        this.dialogueIndex++;
        if (this.dialogueIndex >= this.dialogueContentList.length) {
            this.dialogueIndex = 0;
            this.btnClick.node.active = false;
            UIMgr.closeUI(DialogueUI);
            await UIMgr.openUI(BattleUI);
            return;
        }
        if (this.dialogueIndex % 2 === 0) {
            this.nodeRight.active = false;
            this.nodeLeft.active = true;
        } else {
            this.nodeLeft.active = false;
            this.nodeRight.active = true;
        }
        this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];
    }

    async onShow(dialogueID: number): Promise<void> {
        this.dialogueID = dialogueID;
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }
}


