System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Tween, Animation, GameConst, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, EnemyAnim;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Tween = _cc.Tween;
      Animation = _cc.Animation;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f58cdzE3ixFqZxBBa7lc+jH", "EnemyAnim", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Tween', 'Node', 'Animation']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyAnim = (_dec = ccclass('EnemyAnim'), _dec2 = property(Animation), _dec(_class = (_class2 = class EnemyAnim extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "anim", _descriptor, this);

          this._animCallMap = new Map();
          this._animEventCallMap = new Map();
          this._tailFireArr = [];
          this._curAnim = "";
        }

        /**
         * 加载时的初始化
         */
        onLoad() {// this.anim.on("finished", this.onFinished, this);
        }
        /**
         * 初始化动画
         * @param tailFireData 尾焰数据
         */


        init(tailFireData) {
          this._initTailFire(tailFireData);
        }
        /**
         * 初始化尾焰动画
         * @param tailFireData 尾焰数据
         */


        _initTailFire(tailFireData) {
          const frameTime = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime;
          let fireIndex = 0;
          let dataIndex = 0;

          while (true) {
            const fireNode = this.node.getChildByName(`fire${fireIndex}`);
            if (!fireNode) break;

            for (let i = 0; i < fireNode.children.length; i++) {
              const child = fireNode.children[i];

              if (child.name !== "icon") {
                Tween.stopAllByTarget(child);
                const fireData = tailFireData[dataIndex++];

                if (fireData) {// const scaleSequence = sequence(
                  //     scaleTo(frameTime * fireData[5], fireData[6]),
                  //     scaleTo(frameTime * fireData[5], 1)
                  // );
                  // child.runAction(repeatForever(scaleSequence));
                }
              }
            }

            fireIndex++;
          }

          for (let i = fireIndex; i < this._tailFireArr.length; i++) {
            const tailFire = this._tailFireArr[i];
            Tween.stopAllByTarget(tailFire);
            tailFire.active = false;
          }
        }
        /**
         * 动画播放完成的回调
         * @param event 动画事件
         */


        onFinished(event) {
          this._animCallMap.forEach((callback, animName) => {
            if (this._curAnim === animName && callback) {
              callback();
            }
          });
        }
        /**
         * 射击动画事件
         */


        onShoot() {
          const callback = this._animEventCallMap.get("shoot");

          if (callback) {
            callback();
          }
        }
        /**
         * 护盾动画事件
         */


        onShield() {
          const callback = this._animEventCallMap.get("shield");

          if (callback) {
            callback();
          }
        }
        /**
         * 播放动画
         * @param animName 动画名称
         * @param callback 动画完成后的回调
         */


        playAnim(animName, callback) {
          // this._curAnim = animName;
          // this.anim.play();
          // this._animCallMap.set(animName, callback);
          callback == null || callback();
        }
        /**
         * 设置动画事件回调
         * @param eventName 动画事件名称
         * @param callback 回调函数
         */


        setAnimEventCall(eventName, callback) {
          this._animEventCallMap.set(eventName, callback);
        }
        /**
         * 暂停动画
         */


        pauseAnim() {
          this.anim.pause();
        }
        /**
         * 恢复动画
         */


        resumeAnim() {// this.anim.resume();
        }
        /**
         * 停止动画
         */


        stopAnim() {// this.anim.stop();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "anim", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=83da60e8869e6d4b54338a01c7d420aa0db78a6a.js.map