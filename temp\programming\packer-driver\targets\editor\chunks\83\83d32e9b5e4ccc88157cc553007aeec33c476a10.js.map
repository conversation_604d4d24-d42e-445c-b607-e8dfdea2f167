{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/dialogue/DialogueUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Label", "Node", "Sprite", "EventMgr", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BattleUI", "ccclass", "property", "DialogueUI", "dialogueID", "dialogueContentList", "dialogueIndex", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onLoad", "randomDialogues", "i", "length", "push", "dialogueContent", "string", "btnClick", "node", "on", "onClick", "nodeRight", "active", "closeUI", "openUI", "nodeLeft", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACjCC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;4BAGjBY,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ,CAACP,MAAD,C,UAERO,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACR,IAAD,C,UAERQ,QAAQ,CAACP,MAAD,C,UAERO,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ,CAACV,MAAD,C,2BAnBb,MACaW,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAqBnC;AArBmC,eAsBnCC,UAtBmC,GAsBd,CAtBc;AAAA,eAuBnCC,mBAvBmC,GAuBH,EAvBG;AAAA,eAwBnCC,aAxBmC,GAwBX,CAxBW;AAAA;;AA0Bf,eAANC,MAAM,GAAW;AAAE,iBAAO,6BAAP;AAAuC;;AAClD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AAClDC,QAAAA,MAAM,GAAS;AACrB,gBAAMC,eAAe,GAAG,CACpB,KADoB,EAEpB,KAFoB,EAGpB,KAHoB,EAIpB,KAJoB,CAAxB;;AAMA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,eAAe,CAACE,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,iBAAKP,mBAAL,CAAyBS,IAAzB,CAA8BH,eAAe,CAACC,CAAD,CAA7C;AACH;;AACD,eAAKG,eAAL,CAAqBC,MAArB,GAA8B,KAAKX,mBAAL,CAAyB,KAAKC,aAA9B,CAA9B;AACA,eAAKW,QAAL,CAAcC,IAAd,CAAmBC,EAAnB,CAAsB,OAAtB,EAA+B,KAAKC,OAApC,EAA6C,IAA7C;AACA,eAAKC,SAAL,CAAeC,MAAf,GAAwB,KAAxB;AACH;;AACY,cAAPF,OAAO,GAAG;AACZ,eAAKd,aAAL;;AACA,cAAI,KAAKA,aAAL,IAAsB,KAAKD,mBAAL,CAAyBQ,MAAnD,EAA2D;AACvD,iBAAKP,aAAL,GAAqB,CAArB;AACA,iBAAKW,QAAL,CAAcC,IAAd,CAAmBI,MAAnB,GAA4B,KAA5B;AACA;AAAA;AAAA,gCAAMC,OAAN,CAAcpB,UAAd;AACA,kBAAM;AAAA;AAAA,gCAAMqB,MAAN;AAAA;AAAA,qCAAN;AACA;AACH;;AACD,cAAI,KAAKlB,aAAL,GAAqB,CAArB,KAA2B,CAA/B,EAAkC;AAC9B,iBAAKe,SAAL,CAAeC,MAAf,GAAwB,KAAxB;AACA,iBAAKG,QAAL,CAAcH,MAAd,GAAuB,IAAvB;AACH,WAHD,MAGO;AACH,iBAAKG,QAAL,CAAcH,MAAd,GAAuB,KAAvB;AACA,iBAAKD,SAAL,CAAeC,MAAf,GAAwB,IAAxB;AACH;;AACD,eAAKP,eAAL,CAAqBC,MAArB,GAA8B,KAAKX,mBAAL,CAAyB,KAAKC,aAA9B,CAA9B;AACH;;AAEW,cAANoB,MAAM,CAACtB,UAAD,EAAoC;AAC5C,eAAKA,UAAL,GAAkBA,UAAlB;AACH;;AACW,cAANuB,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAtEkC,O", "sourcesContent": ["import { _decorator, Button, Label, Node, Sprite } from 'cc';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { BaseUI, UILayer, UIMgr } from '../../UIMgr';\r\nimport { BattleUI } from '../BattleUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('DialogueUI')\r\nexport class DialogueUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    nodeLeft: Node;\r\n    @property(Sprite)\r\n    characterImageLeft: Sprite;\r\n    @property(Label)\r\n    characterNameLeft: Label;\r\n\r\n    @property(Node)\r\n    nodeRight: Node;\r\n    @property(Sprite)\r\n    characterImageRight: Sprite;\r\n    @property(Label)\r\n    characterNameRight: Label;\r\n    @property(Label)\r\n    dialogueContent: Label;\r\n\r\n    @property(Button)\r\n    btnClick: Button;\r\n\r\n    //data\r\n    dialogueID: number = 0;\r\n    dialogueContentList: string[] = [];\r\n    dialogueIndex: number = 0;\r\n\r\n    public static getUrl(): string { return \"ui/main/dialogue/DialogueUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    protected onLoad(): void {\r\n        const randomDialogues = [\r\n            \"111\",\r\n            \"222\",\r\n            \"333\",\r\n            \"444\"\r\n        ];\r\n        for (let i = 0; i < randomDialogues.length; i++) {\r\n            this.dialogueContentList.push(randomDialogues[i]);\r\n        }\r\n        this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];\r\n        this.btnClick.node.on('click', this.onClick, this);\r\n        this.nodeRight.active = false;\r\n    }\r\n    async onClick() {\r\n        this.dialogueIndex++;\r\n        if (this.dialogueIndex >= this.dialogueContentList.length) {\r\n            this.dialogueIndex = 0;\r\n            this.btnClick.node.active = false;\r\n            UIMgr.closeUI(DialogueUI);\r\n            await UIMgr.openUI(BattleUI);\r\n            return;\r\n        }\r\n        if (this.dialogueIndex % 2 === 0) {\r\n            this.nodeRight.active = false;\r\n            this.nodeLeft.active = true;\r\n        } else {\r\n            this.nodeLeft.active = false;\r\n            this.nodeRight.active = true;\r\n        }\r\n        this.dialogueContent.string = this.dialogueContentList[this.dialogueIndex];\r\n    }\r\n\r\n    async onShow(dialogueID: number): Promise<void> {\r\n        this.dialogueID = dialogueID;\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}\r\n\r\n\r\n"]}