{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts"], "names": ["PlaneManager", "SingletonBase", "GameIns", "BattleLayer", "GameConst", "GameEnum", "Tools", "FCollider", "enemyTarget", "enemyCollider", "frienPlane", "m_planeTable", "enemyTargetPos", "x", "y", "clear", "for<PERSON>ach", "plane", "node", "destroy", "getConfig", "id", "getRecorder", "addFriendPlane", "me", "push", "removeFriendPlane", "index", "indexOf", "splice", "parent", "<PERSON><PERSON><PERSON><PERSON>", "point1", "point2", "Math", "sqrt", "getRandomTargetEnemyOrderByHp", "maxTargets", "enemies", "bosses", "viewHeight", "ViewHeight", "processedIds", "EnemyType", "enemyManager", "planes", "enemy", "type", "Normal", "<PERSON><PERSON><PERSON>", "collider", "getComp", "screenPos", "getScreenPos", "enabled", "entity", "active", "arrC<PERSON>ain", "new_uuid", "sort", "a", "b", "curHp", "allTargets", "concat", "min", "length", "getRandomTargetEnemy", "targets", "normalCollider", "normalScreenPos", "turretCollider", "turretScreenPos", "randomIndex", "floor", "random", "getTargetEnemy", "mainPlanePos", "mainPlaneManager", "mainPlane", "position", "closestDistance", "Infinity", "closestEnemy", "closestCollider", "isDead", "collideAble", "enemyPos", "distance", "update", "deltaTime", "GameAble"], "mappings": ";;;2JAUaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACEC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,S;;;;;;;;;8BAEMP,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAAA;AAAA;AAAA,eAC1DQ,WAD0D,GACvC,IADuC;AAAA,eAE1DC,aAF0D,GAE/B,IAF+B;AAAA,eAG1DC,UAH0D,GAGtC,EAHsC;AAAA,eAI1DC,YAJ0D;AAAA,eAK1DC,cAL0D,GAKzC;AAAEC,YAAAA,CAAC,EAAE,CAAL;AAAQC,YAAAA,CAAC,EAAE;AAAX,WALyC;AAAA;;AAO1D;AACJ;AACA;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKP,WAAL,GAAmB,IAAnB;AACA,eAAKC,aAAL,GAAqB,IAArB;AACA,eAAKC,UAAL,CAAgBM,OAAhB,CAAyBC,KAAD,IAAW;AAC/B,gBAAIA,KAAK,IAAIA,KAAK,CAACC,IAAnB,EAAyB;AACrBD,cAAAA,KAAK,CAACC,IAAN,CAAWC,OAAX;AACH;AACJ,WAJD;AAKA,eAAKT,UAAL,GAAkB,EAAlB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIU,QAAAA,SAAS,CAACC,EAAD,EAAa;AAClB,iBAAO,KAAKV,YAAL,CAAkBW,WAAlB,CAA8BD,EAA9B,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,cAAc,CAACN,KAAD,EAAa;AACvB;AAAA;AAAA,0CAAYO,EAAZ,CAAeD,cAAf,CAA8BN,KAA9B;AACA,eAAKP,UAAL,CAAgBe,IAAhB,CAAqBR,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,iBAAiB,CAACT,KAAD,EAAa;AAC1B,gBAAMU,KAAK,GAAG,KAAKjB,UAAL,CAAgBkB,OAAhB,CAAwBX,KAAxB,CAAd;;AACA,cAAIU,KAAK,IAAI,CAAb,EAAgB;AACZ,iBAAKjB,UAAL,CAAgBmB,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;AACH;;AACD,cAAIV,KAAK,IAAIA,KAAK,CAACC,IAAnB,EAAyB;AACrBD,YAAAA,KAAK,CAACC,IAAN,CAAWY,MAAX,GAAoB,IAApB;AACH;AACJ;AAKD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,MAAD,EAAeC,MAAf,EAAqC;AAC1C,iBAAOC,IAAI,CAACC,IAAL,CACH,CAACH,MAAM,CAACnB,CAAP,GAAWoB,MAAM,CAACpB,CAAnB,KAAyBmB,MAAM,CAACnB,CAAP,GAAWoB,MAAM,CAACpB,CAA3C,IACA,CAACmB,MAAM,CAAClB,CAAP,GAAWmB,MAAM,CAACnB,CAAnB,KAAyBkB,MAAM,CAAClB,CAAP,GAAWmB,MAAM,CAACnB,CAA3C,CAFG,CAAP;AAIH;;AAEDsB,QAAAA,6BAA6B,CAACC,UAAD,EAAuC;AAChE,gBAAMC,OAAoB,GAAG,EAA7B;AACA,gBAAMC,MAAmB,GAAG,EAA5B;AACA,gBAAMC,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAA7B;AACA,gBAAMC,YAAsB,GAAG,EAA/B;AAEA,cAAIC,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB,CANgE,CAOhE;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAArB,CAA4B7B,OAA5B,CAAqC8B,KAAD,IAAW;AAC3C,oBAAQA,KAAK,CAACC,IAAd;AACI,mBAAKJ,SAAS,CAACK,MAAf;AACA,mBAAKL,SAAS,CAACM,MAAf;AACI,sBAAMC,QAAQ,GAAGJ,KAAK,CAACK,OAAN;AAAA;AAAA,2CAAjB;AACA,sBAAMC,SAAS,GAAGF,QAAQ,CAACG,YAAT,EAAlB;;AACA,oBACID,SAAS,CAACvC,CAAV,GAAc,CAAC,GAAf,IACAuC,SAAS,CAACvC,CAAV,GAAc,GADd,IAEAuC,SAAS,CAACtC,CAAV,GAAc,CAFd,IAGAsC,SAAS,CAACtC,CAAV,GAAc,MAAM0B,UAHpB,IAIAU,QAAQ,CAACI,OAJT,IAKAJ,QAAQ,CAACK,MALT,IAMAL,QAAQ,CAACK,MAAT,CAAgBrC,IANhB,IAOAgC,QAAQ,CAACK,MAAT,CAAgBrC,IAAhB,CAAqBsC,MAPrB,IAQA,CAAC;AAAA;AAAA,oCAAMC,UAAN,CAAiBf,YAAjB,EAA+BI,KAAK,CAACY,QAArC,CATL,EAUE;AACEpB,kBAAAA,OAAO,CAACb,IAAR,CAAayB,QAAb;AACAR,kBAAAA,YAAY,CAACjB,IAAb,CAAkBqB,KAAK,CAACY,QAAxB;AACH;;AACD;AAnBR;AAqBH,WAtBD,EARgE,CAgChE;;AACApB,UAAAA,OAAO,CAACqB,IAAR,CAAa,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,CAACN,MAAF,CAASO,KAAT,GAAiBF,CAAC,CAACL,MAAF,CAASO,KAAjD,EAjCgE,CAmChE;;AACA,gBAAMC,UAAU,GAAGxB,MAAM,CAACyB,MAAP,CAAc1B,OAAd,CAAnB;AACA,iBAAOyB,UAAU,CAAC7B,IAAI,CAAC+B,GAAL,CAAS5B,UAAT,EAAqB0B,UAAU,CAACG,MAAX,GAAoB,CAAzC,CAAD,CAAV,IAA2D,IAAlE;AACH;;AAEDC,QAAAA,oBAAoB,GAAqB;AACrC,gBAAM3B,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAA7B;AACA,gBAAM2B,OAAoB,GAAG,EAA7B;AAEA,cAAIzB,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB,CAJqC,CAKrC;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAArB,CAA4B7B,OAA5B,CAAqC8B,KAAD,IAAW;AAC3C,oBAAQA,KAAK,CAACC,IAAd;AACI,mBAAKJ,SAAS,CAACK,MAAf;AACI,sBAAMqB,cAAc,GAAGvB,KAAK,CAACK,OAAN;AAAA;AAAA,2CAAvB;AACA,sBAAMmB,eAAe,GAAGD,cAAc,CAAChB,YAAf,EAAxB;;AACA,oBACIiB,eAAe,CAACzD,CAAhB,GAAoB,CAAC,GAArB,IACAyD,eAAe,CAACzD,CAAhB,GAAoB,GADpB,IAEAyD,eAAe,CAACxD,CAAhB,GAAoB,CAFpB,IAGAwD,eAAe,CAACxD,CAAhB,GAAoB,MAAM0B,UAJ9B,EAKE;AACE4B,kBAAAA,OAAO,CAAC3C,IAAR,CAAa4C,cAAb;AACH;;AACD;;AAEJ,mBAAK1B,SAAS,CAACM,MAAf;AACI,oBAAI,CAACH,KAAK,CAACU,MAAX,EAAmB;AACnB,sBAAMe,cAAc,GAAGzB,KAAK,CAACK,OAAN;AAAA;AAAA,2CAAvB;AACA,sBAAMqB,eAAe,GAAGD,cAAc,CAAClB,YAAf,EAAxB;;AACA,oBACImB,eAAe,CAAC3D,CAAhB,GAAoB,CAAC,GAArB,IACA2D,eAAe,CAAC3D,CAAhB,GAAoB,GADpB,IAEA2D,eAAe,CAAC1D,CAAhB,GAAoB,CAFpB,IAGA0D,eAAe,CAAC1D,CAAhB,GAAoB,MAAM0B,UAJ9B,EAKE;AACE4B,kBAAAA,OAAO,CAAC3C,IAAR,CAAa8C,cAAb;AACH;;AACD;AA1BR;AA4BH,WA7BD,EANqC,CAqCrC;;AACA,gBAAME,WAAW,GAAGvC,IAAI,CAACwC,KAAL,CAAWxC,IAAI,CAACyC,MAAL,KAAgBP,OAAO,CAACF,MAAnC,CAApB;AACA,iBAAOE,OAAO,CAACK,WAAD,CAAP,IAAwB,IAA/B;AACH;;AAEDG,QAAAA,cAAc,GAAS;AACnB,gBAAMC,YAAY,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,CAAmC7D,IAAnC,CAAwC8D,QAA7D;AACA,cAAIC,eAAe,GAAGC,QAAtB;AACA,cAAIC,YAAiB,GAAG,IAAxB;AACA,cAAIC,eAAiC,GAAG,IAAxC;AACA,gBAAM5C,UAAU,GAAG;AAAA;AAAA,sCAAUC,UAA7B;AAEA,cAAIE,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB,CAPmB,CAQnB;;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAArB,CAA4B7B,OAA5B,CAAqC8B,KAAD,IAAW;AAC3C,gBAAI,CAACA,KAAK,CAACuC,MAAX,EAAmB;AACf,sBAAQvC,KAAK,CAACC,IAAd;AACI,qBAAKJ,SAAS,CAACK,MAAf;AACI,sBAAIF,KAAK,CAACwC,WAAV,EAAuB;AACnB,0BAAMC,QAAQ,GAAGzC,KAAK,CAAC5B,IAAN,CAAW8D,QAA5B;;AACA,wBACIO,QAAQ,CAAC1E,CAAT,GAAa,CAAC,GAAd,IACA0E,QAAQ,CAAC1E,CAAT,GAAa,GADb,IAEA0E,QAAQ,CAACzE,CAAT,GAAa,MAAM0B,UAHvB,EAIE;AACE,4BAAMgD,QAAQ,GAAG,KAAKzD,SAAL,CAAe8C,YAAf,EAA6BU,QAA7B,CAAjB;;AACA,0BAAIC,QAAQ,GAAGP,eAAf,EAAgC;AAC5BA,wBAAAA,eAAe,GAAGO,QAAlB;AACAL,wBAAAA,YAAY,GAAGrC,KAAf;AACAsC,wBAAAA,eAAe,GAAGtC,KAAK,CAACK,OAAN;AAAA;AAAA,mDAAlB;AACH;AACJ;AACJ;;AACD;;AAEJ,qBAAKR,SAAS,CAACM,MAAf;AACI,sBAAIH,KAAK,CAACU,MAAV,EAAkB;AACd,0BAAMN,QAAQ,GAAGJ,KAAK,CAACK,OAAN;AAAA;AAAA,+CAAjB;AACA,0BAAMC,SAAS,GAAGF,QAAQ,CAACG,YAAT,EAAlB;;AACA,wBACID,SAAS,CAACvC,CAAV,GAAc,CAAC,GAAf,IACAuC,SAAS,CAACvC,CAAV,GAAc,GADd,IAEAuC,SAAS,CAACtC,CAAV,GAAc,MAAM0B,UAHxB,EAIE;AACE,4BAAMgD,QAAQ,GAAG,KAAKzD,SAAL,CAAe8C,YAAf,EAA6BzB,SAA7B,CAAjB;;AACA,0BAAIoC,QAAQ,GAAGP,eAAf,EAAgC;AAC5BA,wBAAAA,eAAe,GAAGO,QAAlB;AACAL,wBAAAA,YAAY,GAAGrC,KAAf;AACAsC,wBAAAA,eAAe,GAAGlC,QAAlB;AACH;AACJ;AACJ;;AACD;AApCR;AAwCH;AACJ,WA3CD,EATmB,CAsDnB;;AACA,cAAIiC,YAAJ,EAAkB;AACd,iBAAK3E,WAAL,GAAmB2E,YAAnB;AACA,iBAAKvE,cAAL,CAAoBC,CAApB,GAAwBsE,YAAY,CAACjE,IAAb,CAAkB8D,QAAlB,CAA2BnE,CAAnD;AACA,iBAAKD,cAAL,CAAoBE,CAApB,GAAwBqE,YAAY,CAACjE,IAAb,CAAkB8D,QAAlB,CAA2BlE,CAAnD;AACA,iBAAKL,aAAL,GAAqB2E,eAArB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,sCAAUC,QAAd,EAAwB;AACpB,iBAAKf,cAAL;AACH;AACJ;;AA9NyD,O", "sourcesContent": ["\r\nimport { Vec2, Vec3 } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport GameEnum from \"../const/GameEnum\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport FCollider from \"../collider-system/FCollider\";\r\n\r\nexport class PlaneManager extends SingletonBase<PlaneManager> {\r\n    enemyTarget: any = null;\r\n    enemyCollider: FCollider = null;\r\n    frienPlane: any[] = [];\r\n    m_planeTable: any;\r\n    enemyTargetPos = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * 清理数据\r\n     */\r\n    clear() {\r\n        this.enemyTarget = null;\r\n        this.enemyCollider = null;\r\n        this.frienPlane.forEach((plane) => {\r\n            if (plane && plane.node) {\r\n                plane.node.destroy();\r\n            }\r\n        });\r\n        this.frienPlane = [];\r\n    }\r\n\r\n    /**\r\n     * 获取飞机配置\r\n     * @param id 配置 ID\r\n     * @returns 配置数据\r\n     */\r\n    getConfig(id: number) {\r\n        return this.m_planeTable.getRecorder(id);\r\n    }\r\n\r\n    /**\r\n     * 添加友军飞机\r\n     * @param plane 飞机对象\r\n     */\r\n    addFriendPlane(plane: any) {\r\n        BattleLayer.me.addFriendPlane(plane);\r\n        this.frienPlane.push(plane);\r\n    }\r\n\r\n    /**\r\n     * 移除友军飞机\r\n     * @param plane 飞机对象\r\n     */\r\n    removeFriendPlane(plane: any) {\r\n        const index = this.frienPlane.indexOf(plane);\r\n        if (index >= 0) {\r\n            this.frienPlane.splice(index, 1);\r\n        }\r\n        if (plane && plane.node) {\r\n            plane.node.parent = null;\r\n        }\r\n    }\r\n\r\n\r\n\r\n\r\n    /**\r\n     * 获取两点之间的距离\r\n     * @param point1 点1\r\n     * @param point2 点2\r\n     * @returns 距离\r\n     */\r\n    getLength(point1: Vec3, point2: Vec3): number {\r\n        return Math.sqrt(\r\n            (point1.x - point2.x) * (point1.x - point2.x) +\r\n            (point1.y - point2.y) * (point1.y - point2.y)\r\n        );\r\n    }\r\n\r\n    getRandomTargetEnemyOrderByHp(maxTargets: number): FCollider | null {\r\n        const enemies: FCollider[] = [];\r\n        const bosses: FCollider[] = [];\r\n        const viewHeight = GameConst.ViewHeight;\r\n        const processedIds: number[] = [];\r\n\r\n        let EnemyType = GameEnum.EnemyType;\r\n        // 遍历敌机\r\n        GameIns.enemyManager.planes.forEach((enemy) => {\r\n            switch (enemy.type) {\r\n                case EnemyType.Normal:\r\n                case EnemyType.Turret:\r\n                    const collider = enemy.getComp(FCollider);\r\n                    const screenPos = collider.getScreenPos();\r\n                    if (\r\n                        screenPos.x > -360 &&\r\n                        screenPos.x < 360 &&\r\n                        screenPos.y < 0 &&\r\n                        screenPos.y > 300 - viewHeight &&\r\n                        collider.enabled &&\r\n                        collider.entity &&\r\n                        collider.entity.node &&\r\n                        collider.entity.node.active &&\r\n                        !Tools.arrContain(processedIds, enemy.new_uuid)\r\n                    ) {\r\n                        enemies.push(collider);\r\n                        processedIds.push(enemy.new_uuid);\r\n                    }\r\n                    break;\r\n            }\r\n        });\r\n\r\n        // 按照 HP 降序排序\r\n        enemies.sort((a, b) => b.entity.curHp - a.entity.curHp);\r\n\r\n        // 合并敌人和 Boss，返回目标\r\n        const allTargets = bosses.concat(enemies);\r\n        return allTargets[Math.min(maxTargets, allTargets.length - 1)] || null;\r\n    }\r\n\r\n    getRandomTargetEnemy(): FCollider | null {\r\n        const viewHeight = GameConst.ViewHeight;\r\n        const targets: FCollider[] = [];\r\n\r\n        let EnemyType = GameEnum.EnemyType;\r\n        // 遍历敌机\r\n        GameIns.enemyManager.planes.forEach((enemy) => {\r\n            switch (enemy.type) {\r\n                case EnemyType.Normal:\r\n                    const normalCollider = enemy.getComp(FCollider);\r\n                    const normalScreenPos = normalCollider.getScreenPos();\r\n                    if (\r\n                        normalScreenPos.x > -360 &&\r\n                        normalScreenPos.x < 360 &&\r\n                        normalScreenPos.y < 0 &&\r\n                        normalScreenPos.y > 300 - viewHeight\r\n                    ) {\r\n                        targets.push(normalCollider);\r\n                    }\r\n                    break;\r\n\r\n                case EnemyType.Turret:\r\n                    if (!enemy.active) break;\r\n                    const turretCollider = enemy.getComp(FCollider);\r\n                    const turretScreenPos = turretCollider.getScreenPos();\r\n                    if (\r\n                        turretScreenPos.x > -360 &&\r\n                        turretScreenPos.x < 360 &&\r\n                        turretScreenPos.y < 0 &&\r\n                        turretScreenPos.y > 300 - viewHeight\r\n                    ) {\r\n                        targets.push(turretCollider);\r\n                    }\r\n                    break;\r\n            }\r\n        });\r\n\r\n        // 随机选择一个目标\r\n        const randomIndex = Math.floor(Math.random() * targets.length);\r\n        return targets[randomIndex] || null;\r\n    }\r\n\r\n    getTargetEnemy(): void {\r\n        const mainPlanePos = GameIns.mainPlaneManager.mainPlane.node.position;\r\n        let closestDistance = Infinity;\r\n        let closestEnemy: any = null;\r\n        let closestCollider: FCollider | null = null;\r\n        const viewHeight = GameConst.ViewHeight;\r\n\r\n        let EnemyType = GameEnum.EnemyType;\r\n        // 遍历敌机\r\n        GameIns.enemyManager.planes.forEach((enemy) => {\r\n            if (!enemy.isDead) {\r\n                switch (enemy.type) {\r\n                    case EnemyType.Normal:\r\n                        if (enemy.collideAble) {\r\n                            const enemyPos = enemy.node.position;\r\n                            if (\r\n                                enemyPos.x > -360 &&\r\n                                enemyPos.x < 360 &&\r\n                                enemyPos.y > 300 - viewHeight\r\n                            ) {\r\n                                const distance = this.getLength(mainPlanePos, enemyPos);\r\n                                if (distance < closestDistance) {\r\n                                    closestDistance = distance;\r\n                                    closestEnemy = enemy;\r\n                                    closestCollider = enemy.getComp(FCollider);\r\n                                }\r\n                            }\r\n                        }\r\n                        break;\r\n\r\n                    case EnemyType.Turret:\r\n                        if (enemy.active) {\r\n                            const collider = enemy.getComp(FCollider);\r\n                            const screenPos = collider.getScreenPos();\r\n                            if (\r\n                                screenPos.x > -360 &&\r\n                                screenPos.x < 360 &&\r\n                                screenPos.y > 300 - viewHeight\r\n                            ) {\r\n                                const distance = this.getLength(mainPlanePos, screenPos);\r\n                                if (distance < closestDistance) {\r\n                                    closestDistance = distance;\r\n                                    closestEnemy = enemy;\r\n                                    closestCollider = collider;\r\n                                }\r\n                            }\r\n                        }\r\n                        break;\r\n\r\n                    \r\n                }\r\n            }\r\n        });\r\n\r\n        // 更新目标敌人和碰撞器\r\n        if (closestEnemy) {\r\n            this.enemyTarget = closestEnemy;\r\n            this.enemyTargetPos.x = closestEnemy.node.position.x;\r\n            this.enemyTargetPos.y = closestEnemy.node.position.y;\r\n            this.enemyCollider = closestCollider;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新函数\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    update(deltaTime: number) {\r\n        if (GameConst.GameAble) {\r\n            this.getTargetEnemy();\r\n        }\r\n    }\r\n}"]}