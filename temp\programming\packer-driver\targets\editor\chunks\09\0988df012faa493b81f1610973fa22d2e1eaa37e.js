System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BossUnitBase, _dec, _class, _crd, ccclass, property, BossCollider;

  function _reportPossibleCrUseOfBossUnitBase(extras) {
    _reporterNs.report("BossUnitBase", "./BossUnitBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BossUnitBase = _unresolved_2.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "88601dBGcBJgoGWU58Ah9bf", "BossCollider", undefined);

      __checkObsolete__(['_decorator', 'Component', 'v2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossCollider = (_dec = ccclass('BossCollider'), _dec(_class = class BossCollider extends (_crd && BossUnitBase === void 0 ? (_reportPossibleCrUseOfBossUnitBase({
        error: Error()
      }), BossUnitBase) : BossUnitBase) {
        create(owner, data) {// this.owner = owner;
          // if (!this.collideComp) {
          //     this.collideComp = this.addComp(ColliderComp, new ColliderComp());
          // }
          // this.collideComp.init(this);
          // this.collideComp.setData(data, this.owner, v2(this.node.position.x, this.node.position.y));
          // this.m_comps.forEach((comp) => {
          //     comp.init(this);
          // });
        }

        setCollidePosition(x, y) {// this.collideComp?.setPos(x, y);
        }

        setCollideAble(enabled) {
          if (this.collideComp) {
            this.collideComp.enabled = enabled;
          }
        }

        updateGameLogic(deltaTime) {
          this.m_comps.forEach(comp => {
            comp.update(deltaTime);
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0988df012faa493b81f1610973fa22d2e1eaa37e.js.map