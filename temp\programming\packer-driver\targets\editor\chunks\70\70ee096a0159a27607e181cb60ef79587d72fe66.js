System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, Tools, GameMapData, _crd;

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ca13fEBYWFN/Ln1bNimW/93", "GameMapData", undefined);

      __checkObsolete__(['Node', 'SpriteFrame', 'Vec2']);

      _export("default", GameMapData = class GameMapData {
        constructor() {
          this.loadSprite = new Map();
          // 加载的精灵
          this.loadImageSque = new Map();
          // 加载的图片队列
          this.speed = 0;
          // 当前速度
          this.speeds = [];
          // 不同层的速度
          this.layers = [];
          // 地图层
          this.PosInfo = [];
          // 位置信息
          this.nodeMove = [];
          // 节点移动信息
          this.nodeAngle = [];
          // 节点角度信息
          this.triggerTime = Vec2.ZERO;
          // 触发时间范围
          this.timeInterval = Vec2.ZERO;
          // 时间间隔范围
          this.frameTime = 0;
          // 帧时间
          this.loopTimes = 0;
          // 循环次数
          this.nowTimes = 0;
          // 当前循环次数
          this.nowTriggerTime = 0;
          // 当前触发时间
          this.tempY = 0;
          // 临时 Y 坐标
          this.tempH = 0;
          // 临时高度
          this.nowUseNode = [];
          // 当前使用的节点
          this.freeNode = [];
          // 空闲节点
          this.index = 0;
          // 当前索引
          this.itemIndex = 0;
          // 项目索引
          this.spriteName = [];
          // 精灵名称
          this.starPos = 0;
          // 起始位置
          this.turePosoffSet = 0;
          // 真实位置偏移
          this.ViewBot = 0;
          // 视图底部
          this.ViewMid = 0;
          // 视图中间
          this.ViewTop = 0;
          // 视图顶部
          this.scale = 0;
        }

        // 缩放比例

        /**
         * 清空地图数据
         */
        clear() {
          this.nodeAngle = [];
          this.nodeMove = [];
          this.ViewBot = 0;
          this.ViewTop = 0;
          this.loadSprite.clear();
          this.index = 0;
          this.speed = 0;
          this.nowUseNode.splice(0);
          this.freeNode.splice(0);
          this.itemIndex = 0;
          this.scale = 0;
          this.PosInfo = [];
          this.starPos = 0;
          this.speeds = [];
          this.spriteName = [];
          this.turePosoffSet = 0;
          this.triggerTime = Vec2.ZERO;
          this.timeInterval = Vec2.ZERO;
          this.frameTime = 0;
          this.loopTimes = 0;
          this.nowTimes = 0;
          this.nowTriggerTime = 0;
        }
        /**
         * 获取触发时间
         * @returns {number} 当前触发时间
         */


        getTiggerTime() {
          if (this.nowTriggerTime === 0) {
            this.nowTriggerTime = Math.random() * (this.triggerTime.y - this.triggerTime.x) + this.triggerTime.x;
          }

          return this.nowTriggerTime;
        }
        /**
         * 获取时间间隔
         * @returns {number} 随机时间间隔
         */


        getTimeInterval() {
          return (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).random_int(this.timeInterval.x, this.timeInterval.y);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=70ee096a0159a27607e181cb60ef79587d72fe66.js.map