{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"], "names": ["Boss", "Bullet", "Chapter", "ConsumeItem", "ConsumeMoney", "Enemy", "EnemyUI", "EquipProp", "GameMap", "GameMode", "GlobalAttr", "Level", "LevelGroup", "MainPlane", "MainPlaneLv", "PropInc", "ResEquip", "ResEquipUpgrade", "ResGM", "ResItem", "ResWeapon", "ResWhiteList", "Stage", "Track", "Unit", "Wave", "TbGM", "TbWeapon", "TbEquipUpgrade", "TbEquip", "TbItem", "TbGlobalAttr", "TbBoss", "TbBullet", "TbChapter", "TbEnemy", "TbEnemyUI", "TbGameMap", "TbGameMode", "TbLevel", "TbLevelGroup", "TbMainPlane", "TbMainPlaneLv", "TbStage", "TbTrack", "TbUnit", "TbWave", "Tables", "EquipClass", "res", "GMTabID", "ItemEffectType", "ItemUseType", "ModeType", "MoneyType", "PlayCycle", "PropName", "QualityType", "TargetScanStrategy", "constructor", "_json_", "id", "bId", "sId", "app", "ta", "ft", "leave", "exp", "rid", "sk", "blp", "us", "ua", "va", "sv", "fl", "loot", "adsorb", "lp0", "lp1", "dh", "atk", "col", "tway", "way", "wi", "sp", "ai", "ra", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "a11", "a12", "a13", "a14", "a15", "a16", "a17", "a18", "a19", "a20", "a21", "a22", "a100", "a101", "undefined", "Error", "resolve", "tables", "ConParam", "con", "param", "builtin", "randStrategy", "ID", "Weight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating", "vector2", "x", "y", "vector3", "z", "vector4", "w", "name", "am", "image", "bustyle", "angleSpeed", "waittime", "initialve", "spdiff", "scale", "retrieve", "disappear", "shiftingbody", "body", "exstyle1", "exstyle2", "time", "accnumber", "acc", "offset", "para", "_ele0", "_e0", "push", "levelCount", "levelGroupCount", "strategy", "damageBonus", "life<PERSON><PERSON><PERSON>", "strategyList", "_e", "num", "type", "uiId", "hp", "collideLevel", "turn", "hpShow", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "bMoveAttack", "bStayAttack", "attackInterval", "attackNum", "attackData", "dieShoot", "dieBullet", "isAm", "collider", "hpParam", "blastSound", "blastDurations", "blastShake", "damageParam", "extraParam0", "extraParam1", "skillResistUIDict", "lootParam0", "lootParam1", "showParam", "sneakAnim", "value", "level", "floorRes", "hideImg", "skyRes", "imageSqueRes", "floorSpeed", "skySpeed", "imageSqueSpeed", "floorLayer", "skyLayer", "imageSqueLayer", "imageSqueNodeMove", "imageSquePos", "skyNodeMove", "linkYDistance", "skyAngle", "skyLayout", "inMapItem", "startY", "totalRules", "floor_res", "hide_img", "sky_res", "imageSque_res", "floor_speed", "sky_speed", "imageSque_speed", "floor_layer", "sky_layer", "imageSque_layer", "imageSqueNode_move", "imageSque_pos", "skyNode_move", "link_y_distance", "sky_angle", "sky_layout", "in_map_item", "start_y", "total_rules", "modeType", "chapterID", "order", "resourceID", "description", "conList", "cycle", "times", "monType", "costParam1", "costParam2", "rebirthTimes", "rebirthCost", "power", "rogueID", "LevelLimit", "rogueFirst", "sweepLimit", "rewardID1", "rewardID2", "ratingList", "GoldProducion", "MaxEnergy", "EnergyRecoverInterval", "EnergyRecoverValue", "prefab", "forbidFire", "forbidNBomb", "forbidActSkill", "planeCollisionScaling", "levelType", "normLevelCount", "normLevelST", "normSTList", "bossLevelCount", "bossLevelST", "bossSTList", "transSrc", "transExt", "zjdmtxzb", "transatk1", "shiftingatk1", "inc", "quality", "qualitySub", "equipClass", "props", "consumeItems", "quality_sub", "equip_class", "consume_items", "levelFrom", "levelTo", "propInc", "consumeMoney", "level_from", "level_to", "prop_inc", "consume_money", "tabID", "tabName", "cmd", "desc", "useType", "effectId", "effectParam1", "effectParam2", "maxStack<PERSON>um", "use_type", "effect_id", "effect_param1", "effect_param2", "max_stack_num", "openid", "password", "status", "privilege", "mainStage", "subStage", "enemyGroupID", "delay", "enemyNorRate", "tpe", "uId", "im", "imp", "dam", "pos", "hpp", "sco", "hc", "hs", "bla", "so", "act", "mix", "planeType", "planeId", "interval", "offsetPos", "track", "trackParams", "rotatioSpeed", "FirstShootDelay", "_dataList", "_json2_", "_v", "getDataList", "get", "index", "data", "_dataMap", "Map", "set", "getDataMap", "key", "_data", "length", "getData", "_TbGM", "_TbWeapon", "_TbEquipUpgrade", "_TbEquip", "_TbItem", "_TbGlobalAttr", "_TbBoss", "_TbBullet", "_TbChapter", "_TbEnemy", "_TbEnemyUI", "_TbGameMap", "_TbGameMode", "_TbLevel", "_TbLevelGroup", "_TbMainPlane", "_TbMainPlaneLv", "_TbStage", "_TbTrack", "_TbUnit", "_TbWave", "loader"], "mappings": ";;;iBAgQaA,I,EAiiBAC,M,EAiKAC,O,EA+DAC,W,EAsBAC,Y,EA4BAC,K,EAqIAC,O,EAwIAC,S,EAsBAC,O,EAiKAC,Q,EAkKAC,U,EA0CAC,K,EA+DAC,U,EAyDAC,S,EAsEAC,W,EAsCAC,O,EA4BAC,Q,EA6CAC,e,EA+CAC,K,EAqCAC,O,EAqDAC,S,EAsBAC,Y,EA0CAC,K,EA+DAC,K,EAmCAC,I,EAmJAC,I,EA6GAC,I,EA+BAC,Q,EAkCAC,c,EA+BAC,O,EAkCAC,M,EA+BAC,Y,EAqCAC,M,EA+BAC,Q,EA+BAC,S,EA+BAC,O,EA+BAC,S,EA+BAC,S,EA+BAC,U,EA+BAC,O,EA+BAC,Y,EA+BAC,W,EA+BAC,a,EA+BAC,O,EA+BAC,O,EA+BAC,M,EA+BAC,M,EAiCAC,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAtqGDC,U,0BAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;iBAAAA,U;;;;SAJKC,G,mBAAAA,G;;;YAgCLC,O,0BAAAA,O;AAAAA,UAAAA,O,CAAAA,O;AAAAA,UAAAA,O,CAAAA,O;iBAAAA,O;;;;SAJKD,G,mBAAAA,G;;;YAoBLE,c,0BAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;iBAAAA,c;;;;SAJKF,G,mBAAAA,G;;;YAwCLG,W,0BAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;iBAAAA,W;;;;SAJKH,G,mBAAAA,G;;;YAwBLI,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;SAJKJ,G,mBAAAA,G;;;YAgCLK,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;SAJKL,G,mBAAAA,G;;;YAgCLM,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;SAJKN,G,mBAAAA,G;;;YAoBLO,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;SAJKP,G,mBAAAA,G;;;YAwBLQ,W,0BAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;iBAAAA,W;;;;SAJKR,G,mBAAAA,G;;oCAqCLS,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;sBAiBC1D,I,GAAN,MAAMA,IAAN,CAAW;AAEd2D,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+GzB;AACJ;AACA;AAjH6B,eAkHhBC,EAlHgB;;AAmHzB;AACJ;AACA;AArH6B,eAsHhBC,GAtHgB;;AAuHzB;AACJ;AACA;AAzH6B,eA0HhBC,GA1HgB;;AA2HzB;AACJ;AACA;AA7H6B,eA8HhBC,GA9HgB;;AA+HzB;AACJ;AACA;AAjI6B,eAkIhBC,EAlIgB;;AAmIzB;AACJ;AACA;AArI6B,eAsIhBC,EAtIgB;;AAuIzB;AACJ;AACA;AAzI6B,eA0IhBC,KA1IgB;;AA2IzB;AACJ;AACA;AA7I6B,eA8IhBC,GA9IgB;;AA+IzB;AACJ;AACA;AAjJ6B,eAkJhBC,GAlJgB;;AAmJzB;AACJ;AACA;AArJ6B,eAsJhBC,EAtJgB;;AAuJzB;AACJ;AACA;AAzJ6B,eA0JhBC,GA1JgB;;AA2JzB;AACJ;AACA;AA7J6B,eA8JhBC,EA9JgB;;AA+JzB;AACJ;AACA;AAjK6B,eAkKhBC,EAlKgB;;AAmKzB;AACJ;AACA;AArK6B,eAsKhBC,EAtKgB;;AAuKzB;AACJ;AACA;AAzK6B,eA0KhBC,EA1KgB;;AA2KzB;AACJ;AACA;AA7K6B,eA8KhBC,EA9KgB;;AA+KzB;AACJ;AACA;AAjL6B,eAkLhBC,IAlLgB;;AAmLzB;AACJ;AACA;AArL6B,eAsLhBC,MAtLgB;;AAuLzB;AACJ;AACA;AAzL6B,eA0LhBC,GA1LgB;;AA2LzB;AACJ;AACA;AA7L6B,eA8LhBC,GA9LgB;;AA+LzB;AACJ;AACA;AAjM6B,eAkMhBC,EAlMgB;;AAmMzB;AACJ;AACA;AArM6B,eAsMhBC,GAtMgB;;AAuMzB;AACJ;AACA;AAzM6B,eA0MhBC,GA1MgB;;AA2MzB;AACJ;AACA;AA7M6B,eA8MhBC,IA9MgB;;AA+MzB;AACJ;AACA;AAjN6B,eAkNhBC,GAlNgB;;AAmNzB;AACJ;AACA;AArN6B,eAsNhBC,EAtNgB;;AAuNzB;AACJ;AACA;AAzN6B,eA0NhBC,EA1NgB;;AA2NzB;AACJ;AACA;AA7N6B,eA8NhBC,EA9NgB;;AA+NzB;AACJ;AACA;AAjO6B,eAkOhBC,EAlOgB;;AAmOzB;AACJ;AACA;AArO6B,eAsOhBC,EAtOgB;;AAuOzB;AACJ;AACA;AAzO6B,eA0OhBC,EA1OgB;;AA2OzB;AACJ;AACA;AA7O6B,eA8OhBC,EA9OgB;;AA+OzB;AACJ;AACA;AAjP6B,eAkPhBC,EAlPgB;;AAmPzB;AACJ;AACA;AArP6B,eAsPhBC,EAtPgB;;AAuPzB;AACJ;AACA;AAzP6B,eA0PhBC,EA1PgB;;AA2PzB;AACJ;AACA;AA7P6B,eA8PhBC,EA9PgB;;AA+PzB;AACJ;AACA;AAjQ6B,eAkQhBC,EAlQgB;;AAmQzB;AACJ;AACA;AArQ6B,eAsQhBC,EAtQgB;;AAuQzB;AACJ;AACA;AAzQ6B,eA0QhBC,EA1QgB;;AA2QzB;AACJ;AACA;AA7Q6B,eA8QhBC,GA9QgB;;AA+QzB;AACJ;AACA;AAjR6B,eAkRhBC,GAlRgB;;AAmRzB;AACJ;AACA;AArR6B,eAsRhBC,GAtRgB;;AAuRzB;AACJ;AACA;AAzR6B,eA0RhBC,GA1RgB;;AA2RzB;AACJ;AACA;AA7R6B,eA8RhBC,GA9RgB;;AA+RzB;AACJ;AACA;AAjS6B,eAkShBC,GAlSgB;;AAmSzB;AACJ;AACA;AArS6B,eAsShBC,GAtSgB;;AAuSzB;AACJ;AACA;AAzS6B,eA0ShBC,GA1SgB;;AA2SzB;AACJ;AACA;AA7S6B,eA8ShBC,GA9SgB;;AA+SzB;AACJ;AACA;AAjT6B,eAkThBC,GAlTgB;;AAmTzB;AACJ;AACA;AArT6B,eAsThBC,GAtTgB;;AAuTzB;AACJ;AACA;AAzT6B,eA0ThBC,GA1TgB;;AA2TzB;AACJ;AACA;AA7T6B,eA8ThBC,GA9TgB;;AA+TzB;AACJ;AACA;AAjU6B,eAkUhBC,IAlUgB;;AAmUzB;AACJ;AACA;AArU6B,eAsUhBC,IAtUgB;;AACrB,cAAItD,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACE,GAAP,KAAeqD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKtD,GAAL,GAAWF,MAAM,CAACE,GAAlB;;AACA,cAAIF,MAAM,CAACG,GAAP,KAAeoD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKrD,GAAL,GAAWH,MAAM,CAACG,GAAlB;;AACA,cAAIH,MAAM,CAACI,GAAP,KAAemD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKpD,GAAL,GAAWJ,MAAM,CAACI,GAAlB;;AACA,cAAIJ,MAAM,CAACK,EAAP,KAAckD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnD,EAAL,GAAUL,MAAM,CAACK,EAAjB;;AACA,cAAIL,MAAM,CAACM,EAAP,KAAciD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKlD,EAAL,GAAUN,MAAM,CAACM,EAAjB;;AACA,cAAIN,MAAM,CAACO,KAAP,KAAiBgD,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKjD,KAAL,GAAaP,MAAM,CAACO,KAApB;;AACA,cAAIP,MAAM,CAACQ,GAAP,KAAe+C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKhD,GAAL,GAAWR,MAAM,CAACQ,GAAlB;;AACA,cAAIR,MAAM,CAACS,GAAP,KAAe8C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK/C,GAAL,GAAWT,MAAM,CAACS,GAAlB;;AACA,cAAIT,MAAM,CAACU,EAAP,KAAc6C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9C,EAAL,GAAUV,MAAM,CAACU,EAAjB;;AACA,cAAIV,MAAM,CAACW,GAAP,KAAe4C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK7C,GAAL,GAAWX,MAAM,CAACW,GAAlB;;AACA,cAAIX,MAAM,CAACY,EAAP,KAAc2C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK5C,EAAL,GAAUZ,MAAM,CAACY,EAAjB;;AACA,cAAIZ,MAAM,CAACa,EAAP,KAAc0C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK3C,EAAL,GAAUb,MAAM,CAACa,EAAjB;;AACA,cAAIb,MAAM,CAACc,EAAP,KAAcyC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK1C,EAAL,GAAUd,MAAM,CAACc,EAAjB;;AACA,cAAId,MAAM,CAACe,EAAP,KAAcwC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKzC,EAAL,GAAUf,MAAM,CAACe,EAAjB;;AACA,cAAIf,MAAM,CAACgB,EAAP,KAAcuC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKxC,EAAL,GAAUhB,MAAM,CAACgB,EAAjB;;AACA,cAAIhB,MAAM,CAACiB,IAAP,KAAgBsC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKvC,IAAL,GAAYjB,MAAM,CAACiB,IAAnB;;AACA,cAAIjB,MAAM,CAACkB,MAAP,KAAkBqC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKtC,MAAL,GAAclB,MAAM,CAACkB,MAArB;;AACA,cAAIlB,MAAM,CAACmB,GAAP,KAAeoC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKrC,GAAL,GAAWnB,MAAM,CAACmB,GAAlB;;AACA,cAAInB,MAAM,CAACoB,GAAP,KAAemC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKpC,GAAL,GAAWpB,MAAM,CAACoB,GAAlB;;AACA,cAAIpB,MAAM,CAACqB,EAAP,KAAckC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnC,EAAL,GAAUrB,MAAM,CAACqB,EAAjB;;AACA,cAAIrB,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;;AACA,cAAItB,MAAM,CAACuB,GAAP,KAAegC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKjC,GAAL,GAAWvB,MAAM,CAACuB,GAAlB;;AACA,cAAIvB,MAAM,CAACwB,IAAP,KAAgB+B,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKhC,IAAL,GAAYxB,MAAM,CAACwB,IAAnB;;AACA,cAAIxB,MAAM,CAACyB,GAAP,KAAe8B,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK/B,GAAL,GAAWzB,MAAM,CAACyB,GAAlB;;AACA,cAAIzB,MAAM,CAAC0B,EAAP,KAAc6B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9B,EAAL,GAAU1B,MAAM,CAAC0B,EAAjB;;AACA,cAAI1B,MAAM,CAAC2B,EAAP,KAAc4B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK7B,EAAL,GAAU3B,MAAM,CAAC2B,EAAjB;;AACA,cAAI3B,MAAM,CAAC4B,EAAP,KAAc2B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK5B,EAAL,GAAU5B,MAAM,CAAC4B,EAAjB;;AACA,cAAI5B,MAAM,CAAC6B,EAAP,KAAc0B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK3B,EAAL,GAAU7B,MAAM,CAAC6B,EAAjB;;AACA,cAAI7B,MAAM,CAAC8B,EAAP,KAAcyB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK1B,EAAL,GAAU9B,MAAM,CAAC8B,EAAjB;;AACA,cAAI9B,MAAM,CAAC+B,EAAP,KAAcwB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKzB,EAAL,GAAU/B,MAAM,CAAC+B,EAAjB;;AACA,cAAI/B,MAAM,CAACgC,EAAP,KAAcuB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKxB,EAAL,GAAUhC,MAAM,CAACgC,EAAjB;;AACA,cAAIhC,MAAM,CAACiC,EAAP,KAAcsB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvB,EAAL,GAAUjC,MAAM,CAACiC,EAAjB;;AACA,cAAIjC,MAAM,CAACkC,EAAP,KAAcqB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKtB,EAAL,GAAUlC,MAAM,CAACkC,EAAjB;;AACA,cAAIlC,MAAM,CAACmC,EAAP,KAAcoB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKrB,EAAL,GAAUnC,MAAM,CAACmC,EAAjB;;AACA,cAAInC,MAAM,CAACoC,EAAP,KAAcmB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKpB,EAAL,GAAUpC,MAAM,CAACoC,EAAjB;;AACA,cAAIpC,MAAM,CAACqC,EAAP,KAAckB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnB,EAAL,GAAUrC,MAAM,CAACqC,EAAjB;;AACA,cAAIrC,MAAM,CAACsC,EAAP,KAAciB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKlB,EAAL,GAAUtC,MAAM,CAACsC,EAAjB;;AACA,cAAItC,MAAM,CAACuC,EAAP,KAAcgB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKjB,EAAL,GAAUvC,MAAM,CAACuC,EAAjB;;AACA,cAAIvC,MAAM,CAACwC,GAAP,KAAee,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKhB,GAAL,GAAWxC,MAAM,CAACwC,GAAlB;;AACA,cAAIxC,MAAM,CAACyC,GAAP,KAAec,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKf,GAAL,GAAWzC,MAAM,CAACyC,GAAlB;;AACA,cAAIzC,MAAM,CAAC0C,GAAP,KAAea,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKd,GAAL,GAAW1C,MAAM,CAAC0C,GAAlB;;AACA,cAAI1C,MAAM,CAAC2C,GAAP,KAAeY,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKb,GAAL,GAAW3C,MAAM,CAAC2C,GAAlB;;AACA,cAAI3C,MAAM,CAAC4C,GAAP,KAAeW,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKZ,GAAL,GAAW5C,MAAM,CAAC4C,GAAlB;;AACA,cAAI5C,MAAM,CAAC6C,GAAP,KAAeU,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKX,GAAL,GAAW7C,MAAM,CAAC6C,GAAlB;;AACA,cAAI7C,MAAM,CAAC8C,GAAP,KAAeS,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKV,GAAL,GAAW9C,MAAM,CAAC8C,GAAlB;;AACA,cAAI9C,MAAM,CAAC+C,GAAP,KAAeQ,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKT,GAAL,GAAW/C,MAAM,CAAC+C,GAAlB;;AACA,cAAI/C,MAAM,CAACgD,GAAP,KAAeO,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKR,GAAL,GAAWhD,MAAM,CAACgD,GAAlB;;AACA,cAAIhD,MAAM,CAACiD,GAAP,KAAeM,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKP,GAAL,GAAWjD,MAAM,CAACiD,GAAlB;;AACA,cAAIjD,MAAM,CAACkD,GAAP,KAAeK,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKN,GAAL,GAAWlD,MAAM,CAACkD,GAAlB;;AACA,cAAIlD,MAAM,CAACmD,GAAP,KAAeI,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKL,GAAL,GAAWnD,MAAM,CAACmD,GAAlB;;AACA,cAAInD,MAAM,CAACoD,GAAP,KAAeG,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKJ,GAAL,GAAWpD,MAAM,CAACoD,GAAlB;;AACA,cAAIpD,MAAM,CAACqD,IAAP,KAAgBE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKH,IAAL,GAAYrD,MAAM,CAACqD,IAAnB;;AACA,cAAIrD,MAAM,CAACsD,IAAP,KAAgBC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKF,IAAL,GAAYtD,MAAM,CAACsD,IAAnB;AACH;;AA2NDG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuDtB;;AAjYa,O;;;AAwYX,cAAMC,QAAN,CAAe;AAElB5D,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhB4D,GAPgB;AAAA,iBAQhBC,KARgB;;AACrB,gBAAI7D,MAAM,CAAC4D,GAAP,KAAeL,SAAnB,EAA8B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,iBAAKI,GAAL,GAAW5D,MAAM,CAAC4D,GAAlB;;AACA,gBAAI5D,MAAM,CAAC6D,KAAP,KAAiBN,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,iBAAKK,KAAL,GAAa7D,MAAM,CAAC6D,KAApB;AACH;;AAKDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfiB;;;SADLI,O,uBAAAA,O;;;AA0BV,cAAMC,YAAN,CAAmB;AAEtBhE,UAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,iBAUhBgE,EAVgB;;AAWzB;AACJ;AACA;AAb6B,iBAchBC,MAdgB;;AACrB,gBAAIjE,MAAM,CAACgE,EAAP,KAAcT,SAAlB,EAA6B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,iBAAKQ,EAAL,GAAUhE,MAAM,CAACgE,EAAjB;;AACA,gBAAIhE,MAAM,CAACiE,MAAP,KAAkBV,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAKS,MAAL,GAAcjE,MAAM,CAACiE,MAArB;AACH;;AAWDR,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB;;;SAJTI,O,uBAAAA,O;;;AAgCV,cAAMI,WAAN,CAAkB;AAErBnE,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBmE,MAPgB;AAAA,iBAQhBN,KARgB;;AACrB,gBAAI7D,MAAM,CAACmE,MAAP,KAAkBZ,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAKW,MAAL,GAAcnE,MAAM,CAACmE,MAArB;;AACA,gBAAInE,MAAM,CAAC6D,KAAP,KAAiBN,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,iBAAKK,KAAL,GAAa7D,MAAM,CAAC6D,KAApB;AACH;;AAKDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB;;;SADRI,O,uBAAAA,O;;;AAuBV,cAAMM,OAAN,CAAc;AAEjBrE,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBqE,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAItE,MAAM,CAACqE,CAAP,KAAad,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKa,CAAL,GAASrE,MAAM,CAACqE,CAAhB;;AACA,gBAAIrE,MAAM,CAACsE,CAAP,KAAaf,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKc,CAAL,GAAStE,MAAM,CAACsE,CAAhB;AACH;;AAKDb,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJI,O,uBAAAA,O;;;AAuBV,cAAMS,OAAN,CAAc;AAEjBxE,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBqE,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBE,CAXgB;;AACrB,gBAAIxE,MAAM,CAACqE,CAAP,KAAad,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKa,CAAL,GAASrE,MAAM,CAACqE,CAAhB;;AACA,gBAAIrE,MAAM,CAACsE,CAAP,KAAaf,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKc,CAAL,GAAStE,MAAM,CAACsE,CAAhB;;AACA,gBAAItE,MAAM,CAACwE,CAAP,KAAajB,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKgB,CAAL,GAASxE,MAAM,CAACwE,CAAhB;AACH;;AAMDf,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJI,O,uBAAAA,O;;;AA2BV,cAAMW,OAAN,CAAc;AAEjB1E,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBqE,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBE,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAI1E,MAAM,CAACqE,CAAP,KAAad,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKa,CAAL,GAASrE,MAAM,CAACqE,CAAhB;;AACA,gBAAIrE,MAAM,CAACsE,CAAP,KAAaf,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKc,CAAL,GAAStE,MAAM,CAACsE,CAAhB;;AACA,gBAAItE,MAAM,CAACwE,CAAP,KAAajB,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKgB,CAAL,GAASxE,MAAM,CAACwE,CAAhB;;AACA,gBAAIxE,MAAM,CAAC0E,CAAP,KAAanB,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKkB,CAAL,GAAS1E,MAAM,CAAC0E,CAAhB;AACH;;AAODjB,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJI,O,uBAAAA,O;;wBA+BJzH,M,GAAN,MAAMA,MAAN,CAAa;AAEhB0D,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,EAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB0E,IApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,EAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,KA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,OAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,UApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,QAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,SA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,MAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,KApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,QAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,SA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,YAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,IApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,QAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,QA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,IAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,SApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,GAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,MA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,IAhIgB;;AACrB,cAAI9F,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2E,IAAP,KAAgBpB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmB,IAAL,GAAY3E,MAAM,CAAC2E,IAAnB;;AACA,cAAI3E,MAAM,CAAC4E,EAAP,KAAcrB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoB,EAAL,GAAU5E,MAAM,CAAC4E,EAAjB;;AACA,cAAI5E,MAAM,CAAC6E,KAAP,KAAiBtB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKqB,KAAL,GAAa7E,MAAM,CAAC6E,KAApB;;AACA,cAAI7E,MAAM,CAAC8E,OAAP,KAAmBvB,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKsB,OAAL,GAAe9E,MAAM,CAAC8E,OAAtB;;AACA,cAAI9E,MAAM,CAAC+E,UAAP,KAAsBxB,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuB,UAAL,GAAkB/E,MAAM,CAAC+E,UAAzB;;AACA,cAAI/E,MAAM,CAACgF,QAAP,KAAoBzB,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKwB,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIe,KAAR,IAAiB/F,MAAM,CAACgF,QAAxB,EAAkC;AAAE,kBAAIgB,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKf,QAAL,CAAciB,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC1G,cAAIhG,MAAM,CAACiF,SAAP,KAAqB1B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyB,SAAL,GAAiBjF,MAAM,CAACiF,SAAxB;;AACA,cAAIjF,MAAM,CAACkF,MAAP,KAAkB3B,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK0B,MAAL,GAAclF,MAAM,CAACkF,MAArB;;AACA,cAAIlF,MAAM,CAACmF,KAAP,KAAiB5B,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2B,KAAL,GAAanF,MAAM,CAACmF,KAApB;;AACA,cAAInF,MAAM,CAACoF,QAAP,KAAoB7B,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK4B,QAAL,GAAgBpF,MAAM,CAACoF,QAAvB;;AACA,cAAIpF,MAAM,CAACqF,SAAP,KAAqB9B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6B,SAAL,GAAiBrF,MAAM,CAACqF,SAAxB;;AACA,cAAIrF,MAAM,CAACsF,YAAP,KAAwB/B,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK8B,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIS,KAAR,IAAiB/F,MAAM,CAACsF,YAAxB,EAAsC;AAAE,kBAAIU,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKT,YAAL,CAAkBW,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;;AACtH,cAAIhG,MAAM,CAACuF,IAAP,KAAgBhC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK+B,IAAL,GAAYvF,MAAM,CAACuF,IAAnB;;AACA,cAAIvF,MAAM,CAACwF,QAAP,KAAoBjC,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgC,QAAL,GAAgBxF,MAAM,CAACwF,QAAvB;;AACA,cAAIxF,MAAM,CAACyF,QAAP,KAAoBlC,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiC,QAAL,GAAgBzF,MAAM,CAACyF,QAAvB;;AACA,cAAIzF,MAAM,CAAC0F,IAAP,KAAgBnC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKkC,IAAL,GAAY1F,MAAM,CAAC0F,IAAnB;;AACA,cAAI1F,MAAM,CAAC2F,SAAP,KAAqBpC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKmC,SAAL,GAAiB3F,MAAM,CAAC2F,SAAxB;;AACA,cAAI3F,MAAM,CAAC4F,GAAP,KAAerC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKoC,GAAL,GAAW5F,MAAM,CAAC4F,GAAlB;;AACA,cAAI5F,MAAM,CAAC6F,MAAP,KAAkBtC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKqC,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAIE,KAAR,IAAiB/F,MAAM,CAAC6F,MAAxB,EAAgC;AAAE,kBAAIG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKF,MAAL,CAAYI,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;;AACpG,cAAIhG,MAAM,CAAC8F,IAAP,KAAgBvC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD;AAAE,iBAAKsC,IAAL,GAAY,EAAZ;;AAAgB,iBAAI,IAAIC,KAAR,IAAiB/F,MAAM,CAAC8F,IAAxB,EAA8B;AAAE,kBAAIE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKD,IAAL,CAAUG,IAAV,CAAeD,GAAf;AAAqB;AAAC;AACjG;;AAuFDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAsBtB;;AA1Je,O;;yBAiKPpH,O,GAAN,MAAMA,OAAN,CAAc;AAEjByD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBiG,UAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,eA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,QAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,WApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,UAxCgB;AAAA,eAyChBC,YAzCgB;;AACrB,cAAIvG,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACkG,UAAP,KAAsB3C,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0C,UAAL,GAAkBlG,MAAM,CAACkG,UAAzB;;AACA,cAAIlG,MAAM,CAACmG,eAAP,KAA2B5C,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK2C,eAAL,GAAuBnG,MAAM,CAACmG,eAA9B;;AACA,cAAInG,MAAM,CAACoG,QAAP,KAAoB7C,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK4C,QAAL,GAAgBpG,MAAM,CAACoG,QAAvB;;AACA,cAAIpG,MAAM,CAACqG,WAAP,KAAuB9C,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6C,WAAL,GAAmBrG,MAAM,CAACqG,WAA1B;;AACA,cAAIrG,MAAM,CAACsG,UAAP,KAAsB/C,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8C,UAAL,GAAkBtG,MAAM,CAACsG,UAAzB;;AACA,cAAItG,MAAM,CAACuG,YAAP,KAAwBhD,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK+C,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIR,KAAR,IAAiB/F,MAAM,CAACuG,YAAxB,EAAsC;AAAE,kBAAIP,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlC,OAAO,CAACC,YAAZ,CAAyBgC,KAAzB,CAAN;AAAuC,mBAAKQ,YAAL,CAAkBN,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AACnJ;;AA4BDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAI8C,EAAT,IAAe,KAAKD,YAApB,EAAkC;AAAEC,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AArDgB,O;AA4DrB;AACA;AACA;;;6BACanH,W,GAAN,MAAMA,WAAN,CAAkB;AAErBwD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhBwG,GARgB;;AACrB,cAAIzG,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACyG,GAAP,KAAelD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiD,GAAL,GAAWzG,MAAM,CAACyG,GAAlB;AACH;;AAKDhD,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;8BAsBZlH,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBuD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhB0G,IAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBD,GAdgB;;AACrB,cAAIzG,MAAM,CAAC0G,IAAP,KAAgBnD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKkD,IAAL,GAAY1G,MAAM,CAAC0G,IAAnB;;AACA,cAAI1G,MAAM,CAACyG,GAAP,KAAelD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiD,GAAL,GAAWzG,MAAM,CAACyG,GAAlB;AACH;;AAWDhD,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;uBA4BbjH,K,GAAN,MAAMA,KAAN,CAAY;AAEfsD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB0G,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBrF,GAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBsF,EApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,YAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,IA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,MAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,aApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,YAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,WA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,WAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,cApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,SAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,UA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBzD,KAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhB0D,QApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAIxH,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2G,IAAP,KAAgBpD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmD,IAAL,GAAY3G,MAAM,CAAC2G,IAAnB;;AACA,cAAI3G,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;;AACA,cAAItB,MAAM,CAAC4G,EAAP,KAAcrD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoD,EAAL,GAAU5G,MAAM,CAAC4G,EAAjB;;AACA,cAAI5G,MAAM,CAAC6G,YAAP,KAAwBtD,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKqD,YAAL,GAAoB7G,MAAM,CAAC6G,YAA3B;;AACA,cAAI7G,MAAM,CAAC8G,IAAP,KAAgBvD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsD,IAAL,GAAY9G,MAAM,CAAC8G,IAAnB;;AACA,cAAI9G,MAAM,CAAC+G,MAAP,KAAkBxD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKuD,MAAL,GAAc/G,MAAM,CAAC+G,MAArB;;AACA,cAAI/G,MAAM,CAACgH,aAAP,KAAyBzD,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKwD,aAAL,GAAqBhH,MAAM,CAACgH,aAA5B;;AACA,cAAIhH,MAAM,CAACiH,YAAP,KAAwB1D,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKyD,YAAL,GAAoBjH,MAAM,CAACiH,YAA3B;;AACA,cAAIjH,MAAM,CAACkH,WAAP,KAAuB3D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0D,WAAL,GAAmBlH,MAAM,CAACkH,WAA1B;;AACA,cAAIlH,MAAM,CAACmH,WAAP,KAAuB5D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2D,WAAL,GAAmBnH,MAAM,CAACmH,WAA1B;;AACA,cAAInH,MAAM,CAACoH,cAAP,KAA0B7D,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4D,cAAL,GAAsBpH,MAAM,CAACoH,cAA7B;;AACA,cAAIpH,MAAM,CAACqH,SAAP,KAAqB9D,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6D,SAAL,GAAiBrH,MAAM,CAACqH,SAAxB;;AACA,cAAIrH,MAAM,CAACsH,UAAP,KAAsB/D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8D,UAAL,GAAkBtH,MAAM,CAACsH,UAAzB;;AACA,cAAItH,MAAM,CAAC6D,KAAP,KAAiBN,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKK,KAAL,GAAa7D,MAAM,CAAC6D,KAApB;;AACA,cAAI7D,MAAM,CAACuH,QAAP,KAAoBhE,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+D,QAAL,GAAgBvH,MAAM,CAACuH,QAAvB;;AACA,cAAIvH,MAAM,CAACwH,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKgE,SAAL,GAAiBxH,MAAM,CAACwH,SAAxB;AACH;;AAuED/D,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9Hc,O;;yBAqINhH,O,GAAN,MAAMA,OAAN,CAAc;AAEjBqD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB4E,KA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhB4C,IAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,OAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,UA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBjH,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBkH,cApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,WA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,WAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,WApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,iBAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,UA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,UAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,SApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAItI,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6E,KAAP,KAAiBtB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKqB,KAAL,GAAa7E,MAAM,CAAC6E,KAApB;;AACA,cAAI7E,MAAM,CAACyH,IAAP,KAAgBlE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiE,IAAL,GAAYzH,MAAM,CAACyH,IAAnB;;AACA,cAAIzH,MAAM,CAAC0H,QAAP,KAAoBnE,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkE,QAAL,GAAgB1H,MAAM,CAAC0H,QAAvB;;AACA,cAAI1H,MAAM,CAAC2H,OAAP,KAAmBpE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmE,OAAL,GAAe3H,MAAM,CAAC2H,OAAtB;;AACA,cAAI3H,MAAM,CAAC4H,UAAP,KAAsBrE,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoE,UAAL,GAAkB5H,MAAM,CAAC4H,UAAzB;;AACA,cAAI5H,MAAM,CAACW,GAAP,KAAe4C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK7C,GAAL,GAAWX,MAAM,CAACW,GAAlB;;AACA,cAAIX,MAAM,CAAC6H,cAAP,KAA0BtE,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKqE,cAAL,GAAsB7H,MAAM,CAAC6H,cAA7B;;AACA,cAAI7H,MAAM,CAAC8H,UAAP,KAAsBvE,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsE,UAAL,GAAkB9H,MAAM,CAAC8H,UAAzB;;AACA,cAAI9H,MAAM,CAAC+H,WAAP,KAAuBxE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuE,WAAL,GAAmB/H,MAAM,CAAC+H,WAA1B;;AACA,cAAI/H,MAAM,CAACgI,WAAP,KAAuBzE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwE,WAAL,GAAmBhI,MAAM,CAACgI,WAA1B;;AACA,cAAIhI,MAAM,CAACiI,WAAP,KAAuB1E,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyE,WAAL,GAAmBjI,MAAM,CAACiI,WAA1B;;AACA,cAAIjI,MAAM,CAACkI,iBAAP,KAA6B3E,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAK0E,iBAAL,GAAyBlI,MAAM,CAACkI,iBAAhC;;AACA,cAAIlI,MAAM,CAACmI,UAAP,KAAsB5E,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2E,UAAL,GAAkBnI,MAAM,CAACmI,UAAzB;;AACA,cAAInI,MAAM,CAACoI,UAAP,KAAsB7E,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4E,UAAL,GAAkBpI,MAAM,CAACoI,UAAzB;;AACA,cAAIpI,MAAM,CAACqI,SAAP,KAAqB9E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6E,SAAL,GAAiBrI,MAAM,CAACqI,SAAxB;;AACA,cAAIrI,MAAM,CAACsI,SAAP,KAAqB/E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK8E,SAAL,GAAiBtI,MAAM,CAACsI,SAAxB;AACH;;AAuED7E,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9HgB,O;AAqIrB;AACA;AACA;;;2BACa/G,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBoD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhBsI,KARgB;;AACrB,cAAIvI,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACuI,KAAP,KAAiBhF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK+E,KAAL,GAAavI,MAAM,CAACuI,KAApB;AACH;;AAKD9E,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;yBAsBV9G,O,GAAN,MAAMA,OAAN,CAAc;AAEjBmD,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,EAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBuI,KApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,QAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,OA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,MAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,YApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,QA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,cAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,UApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,QAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,cA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,iBAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,YApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,WAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,aA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,QAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,SApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,SAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,MA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,UAhIgB;;AACrB,cAAI3J,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACwI,KAAP,KAAiBjF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKgF,KAAL,GAAaxI,MAAM,CAACwI,KAApB;;AACA,cAAIxI,MAAM,CAAC4J,SAAP,KAAqBrG,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKiF,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI1C,KAAR,IAAiB/F,MAAM,CAAC4J,SAAxB,EAAmC;AAAE,kBAAI5D,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK0C,QAAL,CAAcxC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC3G,cAAIhG,MAAM,CAAC6J,QAAP,KAAoBtG,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKkF,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI3C,KAAR,IAAiB/F,MAAM,CAAC6J,QAAxB,EAAkC;AAAE,kBAAI7D,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK2C,OAAL,CAAazC,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACxG,cAAIhG,MAAM,CAAC8J,OAAP,KAAmBvG,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKmF,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAI5C,KAAR,IAAiB/F,MAAM,CAAC8J,OAAxB,EAAiC;AAAE,kBAAI9D,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK4C,MAAL,CAAY1C,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;;AACrG,cAAIhG,MAAM,CAAC+J,aAAP,KAAyBxG,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKoF,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI7C,KAAR,IAAiB/F,MAAM,CAAC+J,aAAxB,EAAuC;AAAE,kBAAI/D,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK6C,YAAL,CAAkB3C,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;;AACvH,cAAIhG,MAAM,CAACgK,WAAP,KAAuBzG,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKqF,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9C,KAAR,IAAiB/F,MAAM,CAACgK,WAAxB,EAAqC;AAAE,kBAAIhE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK8C,UAAL,CAAgB5C,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AACjH,cAAIhG,MAAM,CAACiK,SAAP,KAAqB1G,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKsF,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI/C,KAAR,IAAiB/F,MAAM,CAACiK,SAAxB,EAAmC;AAAE,kBAAIjE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK+C,QAAL,CAAc7C,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC3G,cAAIhG,MAAM,CAACkK,eAAP,KAA2B3G,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAKuF,cAAL,GAAsB,EAAtB;;AAA0B,iBAAI,IAAIhD,KAAR,IAAiB/F,MAAM,CAACkK,eAAxB,EAAyC;AAAE,kBAAIlE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKgD,cAAL,CAAoB9C,IAApB,CAAyBD,GAAzB;AAA+B;AAAC;;AAC7H,cAAIhG,MAAM,CAACmK,WAAP,KAAuB5G,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKwF,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIjD,KAAR,IAAiB/F,MAAM,CAACmK,WAAxB,EAAqC;AAAE,kBAAInE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKiD,UAAL,CAAgB/C,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AACjH,cAAIhG,MAAM,CAACoK,SAAP,KAAqB7G,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKyF,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIlD,KAAR,IAAiB/F,MAAM,CAACoK,SAAxB,EAAmC;AAAE,kBAAIpE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKkD,QAAL,CAAchD,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC3G,cAAIhG,MAAM,CAACqK,eAAP,KAA2B9G,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAK0F,cAAL,GAAsB,EAAtB;;AAA0B,iBAAI,IAAInD,KAAR,IAAiB/F,MAAM,CAACqK,eAAxB,EAAyC;AAAE,kBAAIrE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmD,cAAL,CAAoBjD,IAApB,CAAyBD,GAAzB;AAA+B;AAAC;;AAC7H,cAAIhG,MAAM,CAACsK,kBAAP,KAA8B/G,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE;AAAE,iBAAK2F,iBAAL,GAAyB,EAAzB;;AAA6B,iBAAI,IAAIpD,KAAR,IAAiB/F,MAAM,CAACsK,kBAAxB,EAA4C;AAAE,kBAAItE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKoD,iBAAL,CAAuBlD,IAAvB,CAA4BD,GAA5B;AAAkC;AAAC;;AACtI,cAAIhG,MAAM,CAACuK,aAAP,KAAyBhH,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK4F,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIrD,KAAR,IAAiB/F,MAAM,CAACuK,aAAxB,EAAuC;AAAE,kBAAIvE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKqD,YAAL,CAAkBnD,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;;AACvH,cAAIhG,MAAM,CAACwK,YAAP,KAAwBjH,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK6F,WAAL,GAAmB,EAAnB;;AAAuB,iBAAI,IAAItD,KAAR,IAAiB/F,MAAM,CAACwK,YAAxB,EAAsC;AAAE,kBAAIxE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKsD,WAAL,CAAiBpD,IAAjB,CAAsBD,GAAtB;AAA4B;AAAC;;AACpH,cAAIhG,MAAM,CAACyK,eAAP,KAA2BlH,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAK8F,aAAL,GAAqB,EAArB;;AAAyB,iBAAI,IAAIvD,KAAR,IAAiB/F,MAAM,CAACyK,eAAxB,EAAyC;AAAE,kBAAIzE,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKuD,aAAL,CAAmBrD,IAAnB,CAAwBD,GAAxB;AAA8B;AAAC;;AAC3H,cAAIhG,MAAM,CAAC0K,SAAP,KAAqBnH,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK+F,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIxD,KAAR,IAAiB/F,MAAM,CAAC0K,SAAxB,EAAmC;AAAE,kBAAI1E,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwD,QAAL,CAActD,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC3G,cAAIhG,MAAM,CAAC2K,UAAP,KAAsBpH,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKgG,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAIzD,KAAR,IAAiB/F,MAAM,CAAC2K,UAAxB,EAAoC;AAAE,kBAAI3E,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKyD,SAAL,CAAevD,IAAf,CAAoBD,GAApB;AAA0B;AAAC;;AAC9G,cAAIhG,MAAM,CAAC4K,WAAP,KAAuBrH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKiG,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAI1D,KAAR,IAAiB/F,MAAM,CAAC4K,WAAxB,EAAqC;AAAE,kBAAI5E,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK0D,SAAL,CAAexD,IAAf,CAAoBD,GAApB;AAA0B;AAAC;;AAC/G,cAAIhG,MAAM,CAAC6K,OAAP,KAAmBtH,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkG,MAAL,GAAc1J,MAAM,CAAC6K,OAArB;;AACA,cAAI7K,MAAM,CAAC8K,WAAP,KAAuBvH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmG,UAAL,GAAkB3J,MAAM,CAAC8K,WAAzB;AACH;;AAuFDrH,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAsBtB;;AA1JgB,O;;0BAiKR7G,Q,GAAN,MAAMA,QAAN,CAAe;AAElBkD,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBgE,EAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhB+G,QAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,SA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,KA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,UAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBC,WAtEgB;AAAA,eAuEhBC,OAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhBC,KA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhBC,KA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,OAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,UAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,UA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBC,YA/FgB;;AAgGzB;AACJ;AACA;AAlG6B,eAmGhBC,WAnGgB;;AAoGzB;AACJ;AACA;AAtG6B,eAuGhBC,KAvGgB;;AAwGzB;AACJ;AACA;AA1G6B,eA2GhBC,OA3GgB;;AA4GzB;AACJ;AACA;AA9G6B,eA+GhBC,UA/GgB;;AAgHzB;AACJ;AACA;AAlH6B,eAmHhBC,UAnHgB;;AAoHzB;AACJ;AACA;AAtH6B,eAuHhBC,UAvHgB;;AAwHzB;AACJ;AACA;AA1H6B,eA2HhBC,SA3HgB;;AA4HzB;AACJ;AACA;AA9H6B,eA+HhBC,SA/HgB;AAAA,eAgIhBC,UAhIgB;;AACrB,cAAInM,MAAM,CAACgE,EAAP,KAAcT,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKQ,EAAL,GAAUhE,MAAM,CAACgE,EAAjB;;AACA,cAAIhE,MAAM,CAAC+K,QAAP,KAAoBxH,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKuH,QAAL,GAAgB/K,MAAM,CAAC+K,QAAvB;;AACA,cAAI/K,MAAM,CAACgL,SAAP,KAAqBzH,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwH,SAAL,GAAiBhL,MAAM,CAACgL,SAAxB;;AACA,cAAIhL,MAAM,CAACiL,KAAP,KAAiB1H,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKyH,KAAL,GAAajL,MAAM,CAACiL,KAApB;;AACA,cAAIjL,MAAM,CAACkL,UAAP,KAAsB3H,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0H,UAAL,GAAkBlL,MAAM,CAACkL,UAAzB;;AACA,cAAIlL,MAAM,CAACmL,WAAP,KAAuB5H,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2H,WAAL,GAAmBnL,MAAM,CAACmL,WAA1B;;AACA,cAAInL,MAAM,CAACoL,OAAP,KAAmB7H,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK4H,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIrF,KAAR,IAAiB/F,MAAM,CAACoL,OAAxB,EAAiC;AAAE,kBAAIpF,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlC,OAAO,CAACH,QAAZ,CAAqBoC,KAArB,CAAN;AAAmC,mBAAKqF,OAAL,CAAanF,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AAC7H,cAAIhG,MAAM,CAACqL,KAAP,KAAiB9H,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6H,KAAL,GAAarL,MAAM,CAACqL,KAApB;;AACA,cAAIrL,MAAM,CAACsL,KAAP,KAAiB/H,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK8H,KAAL,GAAatL,MAAM,CAACsL,KAApB;;AACA,cAAItL,MAAM,CAACuL,OAAP,KAAmBhI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK+H,OAAL,GAAevL,MAAM,CAACuL,OAAtB;;AACA,cAAIvL,MAAM,CAACwL,UAAP,KAAsBjI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgI,UAAL,GAAkBxL,MAAM,CAACwL,UAAzB;;AACA,cAAIxL,MAAM,CAACyL,UAAP,KAAsBlI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKiI,UAAL,GAAkBzL,MAAM,CAACyL,UAAzB;;AACA,cAAIzL,MAAM,CAAC0L,YAAP,KAAwBnI,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKkI,YAAL,GAAoB1L,MAAM,CAAC0L,YAA3B;;AACA,cAAI1L,MAAM,CAAC2L,WAAP,KAAuBpI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmI,WAAL,GAAmB3L,MAAM,CAAC2L,WAA1B;;AACA,cAAI3L,MAAM,CAAC4L,KAAP,KAAiBrI,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoI,KAAL,GAAa5L,MAAM,CAAC4L,KAApB;;AACA,cAAI5L,MAAM,CAAC6L,OAAP,KAAmBtI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqI,OAAL,GAAe7L,MAAM,CAAC6L,OAAtB;;AACA,cAAI7L,MAAM,CAAC8L,UAAP,KAAsBvI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsI,UAAL,GAAkB9L,MAAM,CAAC8L,UAAzB;;AACA,cAAI9L,MAAM,CAAC+L,UAAP,KAAsBxI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuI,UAAL,GAAkB/L,MAAM,CAAC+L,UAAzB;;AACA,cAAI/L,MAAM,CAACgM,UAAP,KAAsBzI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwI,UAAL,GAAkBhM,MAAM,CAACgM,UAAzB;;AACA,cAAIhM,MAAM,CAACiM,SAAP,KAAqB1I,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyI,SAAL,GAAiBjM,MAAM,CAACiM,SAAxB;;AACA,cAAIjM,MAAM,CAACkM,SAAP,KAAqB3I,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0I,SAAL,GAAiBlM,MAAM,CAACkM,SAAxB;;AACA,cAAIlM,MAAM,CAACmM,UAAP,KAAsB5I,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK2I,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIpG,KAAR,IAAiB/F,MAAM,CAACmM,UAAxB,EAAoC;AAAE,kBAAInG,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlC,OAAO,CAACI,WAAZ,CAAwB6B,KAAxB,CAAN;AAAsC,mBAAKoG,UAAL,CAAgBlG,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AAC5I;;AAqFDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuBtB;;AA3JiB,O;;4BAkKT5G,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBiD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBoM,aAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,SAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,qBAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,kBA1BgB;;AACrB,cAAIvM,MAAM,CAACoM,aAAP,KAAyB7I,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK4I,aAAL,GAAqBpM,MAAM,CAACoM,aAA5B;;AACA,cAAIpM,MAAM,CAACqM,SAAP,KAAqB9I,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6I,SAAL,GAAiBrM,MAAM,CAACqM,SAAxB;;AACA,cAAIrM,MAAM,CAACsM,qBAAP,KAAiC/I,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK8I,qBAAL,GAA6BtM,MAAM,CAACsM,qBAApC;;AACA,cAAItM,MAAM,CAACuM,kBAAP,KAA8BhJ,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAK+I,kBAAL,GAA0BvM,MAAM,CAACuM,kBAAjC;AACH;;AAmBD9I,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAnCmB,O;;uBA0CX3G,K,GAAN,MAAMA,KAAN,CAAY;AAEfgD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBuM,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,UA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,WAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,cApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,qBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,SA5CgB;;AACrB,cAAI7M,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACwM,MAAP,KAAkBjJ,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgJ,MAAL,GAAcxM,MAAM,CAACwM,MAArB;;AACA,cAAIxM,MAAM,CAACyM,UAAP,KAAsBlJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKiJ,UAAL,GAAkBzM,MAAM,CAACyM,UAAzB;;AACA,cAAIzM,MAAM,CAAC0M,WAAP,KAAuBnJ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkJ,WAAL,GAAmB1M,MAAM,CAAC0M,WAA1B;;AACA,cAAI1M,MAAM,CAAC2M,cAAP,KAA0BpJ,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKmJ,cAAL,GAAsB3M,MAAM,CAAC2M,cAA7B;;AACA,cAAI3M,MAAM,CAAC4M,qBAAP,KAAiCrJ,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKoJ,qBAAL,GAA6B5M,MAAM,CAAC4M,qBAApC;;AACA,cAAI5M,MAAM,CAAC6M,SAAP,KAAqBtJ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqJ,SAAL,GAAiB7M,MAAM,CAAC6M,SAAxB;AACH;;AA+BDpJ,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDc,O;;4BA+DN1G,U,GAAN,MAAMA,UAAN,CAAiB;AAEpB+C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB6M,cAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,WA5BgB;AAAA,eA6BhBC,UA7BgB;;AA8BzB;AACJ;AACA;AAhC6B,eAiChBC,cAjCgB;;AAkCzB;AACJ;AACA;AApC6B,eAqChBC,WArCgB;AAAA,eAsChBC,UAtCgB;;AACrB,cAAInN,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC8M,cAAP,KAA0BvJ,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKsJ,cAAL,GAAsB9M,MAAM,CAAC8M,cAA7B;;AACA,cAAI9M,MAAM,CAAC+M,WAAP,KAAuBxJ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuJ,WAAL,GAAmB/M,MAAM,CAAC+M,WAA1B;;AACA,cAAI/M,MAAM,CAACgN,UAAP,KAAsBzJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKwJ,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIjH,KAAR,IAAiB/F,MAAM,CAACgN,UAAxB,EAAoC;AAAE,kBAAIhH,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlC,OAAO,CAACC,YAAZ,CAAyBgC,KAAzB,CAAN;AAAuC,mBAAKiH,UAAL,CAAgB/G,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AAC1I,cAAIhG,MAAM,CAACiN,cAAP,KAA0B1J,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKyJ,cAAL,GAAsBjN,MAAM,CAACiN,cAA7B;;AACA,cAAIjN,MAAM,CAACkN,WAAP,KAAuB3J,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK0J,WAAL,GAAmBlN,MAAM,CAACkN,WAA1B;;AACA,cAAIlN,MAAM,CAACmN,UAAP,KAAsB5J,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK2J,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIpH,KAAR,IAAiB/F,MAAM,CAACmN,UAAxB,EAAoC;AAAE,kBAAInH,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlC,OAAO,CAACC,YAAZ,CAAyBgC,KAAzB,CAAN;AAAuC,mBAAKoH,UAAL,CAAgBlH,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AAC7I;;AAyBDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAI8C,EAAT,IAAe,KAAKwG,UAApB,EAAgC;AAAExG,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;;AAGxD,eAAK,IAAI8C,EAAT,IAAe,KAAK2G,UAApB,EAAgC;AAAE3G,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAlDmB,O;;2BAyDXzG,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB8C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhByG,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBnB,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChB6H,QAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,SA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAIxN,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC0G,IAAP,KAAgBnD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKkD,IAAL,GAAY1G,MAAM,CAAC0G,IAAnB;;AACA,cAAI1G,MAAM,CAACuF,IAAP,KAAgBhC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD;AAAE,iBAAK+B,IAAL,GAAY,EAAZ;;AAAgB,iBAAI,IAAIQ,KAAR,IAAiB/F,MAAM,CAACuF,IAAxB,EAA8B;AAAE,kBAAIS,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKR,IAAL,CAAUU,IAAV,CAAeD,GAAf;AAAqB;AAAC;;AAC9F,cAAIhG,MAAM,CAACoN,QAAP,KAAoB7J,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK4J,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrH,KAAR,IAAiB/F,MAAM,CAACoN,QAAxB,EAAkC;AAAE,kBAAIpH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKqH,QAAL,CAAcnH,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC1G,cAAIhG,MAAM,CAACqN,QAAP,KAAoB9J,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK6J,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAItH,KAAR,IAAiB/F,MAAM,CAACqN,QAAxB,EAAkC;AAAE,kBAAIrH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKsH,QAAL,CAAcpH,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC1G,cAAIhG,MAAM,CAACsN,QAAP,KAAoB/J,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK8J,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvH,KAAR,IAAiB/F,MAAM,CAACsN,QAAxB,EAAkC;AAAE,kBAAItH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKuH,QAAL,CAAcrH,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC1G,cAAIhG,MAAM,CAACuN,SAAP,KAAqBhK,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK+J,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAIxH,KAAR,IAAiB/F,MAAM,CAACuN,SAAxB,EAAmC;AAAE,kBAAIvH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwH,SAAL,CAAetH,IAAf,CAAoBD,GAApB;AAA0B;AAAC;;AAC7G,cAAIhG,MAAM,CAACwN,YAAP,KAAwBjK,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAKgK,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIzH,KAAR,IAAiB/F,MAAM,CAACwN,YAAxB,EAAsC;AAAE,kBAAIxH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKyH,YAAL,CAAkBvH,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AACzH;;AAmCDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAStB;;AA/DkB,O;;6BAsEVxG,W,GAAN,MAAMA,WAAN,CAAkB;AAErB6C,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBC,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhB2G,EAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBtF,GApBgB;;AACrB,cAAItB,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC4G,EAAP,KAAcrD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoD,EAAL,GAAU5G,MAAM,CAAC4G,EAAjB;;AACA,cAAI5G,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;AACH;;AAeDmC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BoB,O;AAmCzB;AACA;AACA;;;yBACavG,O,GAAN,MAAMA,OAAN,CAAc;AAEjB4C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;;AAQzB;AACJ;AACA;AAV6B,eAWhBwN,GAXgB;;AACrB,cAAIzN,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACyN,GAAP,KAAelK,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiK,GAAL,GAAWzN,MAAM,CAACyN,GAAlB;AACH;;AAQDhK,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAlBgB,O;AAyBrB;AACA;AACA;;;0BACatG,Q,GAAN,MAAMA,QAAN,CAAe;AAElB2C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAiBhBC,EAjBgB;AAAA,eAkBhB0E,IAlBgB;AAAA,eAmBhB+I,OAnBgB;AAAA,eAoBhBC,UApBgB;AAAA,eAqBhBC,UArBgB;AAAA,eAsBhBC,KAtBgB;AAAA,eAuBhBC,YAvBgB;;AACrB,cAAI9N,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2E,IAAP,KAAgBpB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmB,IAAL,GAAY3E,MAAM,CAAC2E,IAAnB;;AACA,cAAI3E,MAAM,CAAC0N,OAAP,KAAmBnK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkK,OAAL,GAAe1N,MAAM,CAAC0N,OAAtB;;AACA,cAAI1N,MAAM,CAAC+N,WAAP,KAAuBxK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmK,UAAL,GAAkB3N,MAAM,CAAC+N,WAAzB;;AACA,cAAI/N,MAAM,CAACgO,WAAP,KAAuBzK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoK,UAAL,GAAkB5N,MAAM,CAACgO,WAAzB;;AACA,cAAIhO,MAAM,CAAC6N,KAAP,KAAiBtK,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKqK,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAI9H,KAAR,IAAiB/F,MAAM,CAAC6N,KAAxB,EAA+B;AAAE,kBAAI7H,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIrJ,SAAJ,CAAcoJ,KAAd,CAAN;AAA4B,mBAAK8H,KAAL,CAAW5H,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;;AAChH,cAAIhG,MAAM,CAACiO,aAAP,KAAyB1K,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKsK,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI/H,KAAR,IAAiB/F,MAAM,CAACiO,aAAxB,EAAuC;AAAE,kBAAIjI,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIzJ,WAAJ,CAAgBwJ,KAAhB,CAAN;AAA8B,mBAAK+H,YAAL,CAAkB7H,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAUDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAMnB,eAAK,IAAI8C,EAAT,IAAe,KAAKqH,KAApB,EAA2B;AAAErH,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;;AACnD,eAAK,IAAI8C,EAAT,IAAe,KAAKsH,YAApB,EAAkC;AAAEtH,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAnCiB,O;AA0CtB;AACA;AACA;;;iCACarG,e,GAAN,MAAMA,eAAN,CAAsB;AAEzB0C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhB4N,UAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBM,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;AAAA,eA2BhBC,OA3BgB;AAAA,eA4BhBC,YA5BgB;AAAA,eA6BhBP,YA7BgB;;AACrB,cAAI9N,MAAM,CAACgO,WAAP,KAAuBzK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoK,UAAL,GAAkB5N,MAAM,CAACgO,WAAzB;;AACA,cAAIhO,MAAM,CAACsO,UAAP,KAAsB/K,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0K,SAAL,GAAiBlO,MAAM,CAACsO,UAAxB;;AACA,cAAItO,MAAM,CAACuO,QAAP,KAAoBhL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2K,OAAL,GAAenO,MAAM,CAACuO,QAAtB;;AACA,cAAIvO,MAAM,CAACwO,QAAP,KAAoBjL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK4K,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIrI,KAAR,IAAiB/F,MAAM,CAACwO,QAAxB,EAAkC;AAAE,kBAAIxI,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAI7I,OAAJ,CAAY4I,KAAZ,CAAN;AAA0B,mBAAKqI,OAAL,CAAanI,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACrH,cAAIhG,MAAM,CAACyO,aAAP,KAAyBlL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK6K,YAAL,GAAoB,IAAI7R,YAAJ,CAAiBwD,MAAM,CAACyO,aAAxB,CAApB;;AACA,cAAIzO,MAAM,CAACiO,aAAP,KAAyB1K,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKsK,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI/H,KAAR,IAAiB/F,MAAM,CAACiO,aAAxB,EAAuC;AAAE,kBAAIjI,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIzJ,WAAJ,CAAgBwJ,KAAhB,CAAN;AAA8B,mBAAK+H,YAAL,CAAkB7H,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAkBDvC,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAInB,eAAK,IAAI8C,EAAT,IAAe,KAAK4H,OAApB,EAA6B;AAAE5H,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,qCAAK2K,YAAL,gCAAmB5K,OAAnB,CAA2BC,MAA3B;;AACA,eAAK,IAAI8C,EAAT,IAAe,KAAKsH,YAApB,EAAkC;AAAEtH,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE/C,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAxCwB,O;;uBA+ChBpG,K,GAAN,MAAMA,KAAN,CAAY;AAEfyC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAahB0O,KAbgB;AAAA,eAchBC,OAdgB;AAAA,eAehBhK,IAfgB;AAAA,eAgBhBiK,GAhBgB;AAAA,eAiBhBC,IAjBgB;;AACrB,cAAI7O,MAAM,CAAC0O,KAAP,KAAiBnL,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKkL,KAAL,GAAa1O,MAAM,CAAC0O,KAApB;;AACA,cAAI1O,MAAM,CAAC2O,OAAP,KAAmBpL,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmL,OAAL,GAAe3O,MAAM,CAAC2O,OAAtB;;AACA,cAAI3O,MAAM,CAAC2E,IAAP,KAAgBpB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmB,IAAL,GAAY3E,MAAM,CAAC2E,IAAnB;;AACA,cAAI3E,MAAM,CAAC4O,GAAP,KAAerL,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKoL,GAAL,GAAW5O,MAAM,CAAC4O,GAAlB;;AACA,cAAI5O,MAAM,CAAC6O,IAAP,KAAgBtL,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqL,IAAL,GAAY7O,MAAM,CAAC6O,IAAnB;AACH;;AAQDpL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA3Bc,O;AAkCnB;AACA;AACA;;;yBACanG,O,GAAN,MAAMA,OAAN,CAAc;AAEjBwC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAqBhBC,EArBgB;AAAA,eAsBhB0E,IAtBgB;AAAA,eAuBhB+I,OAvBgB;AAAA,eAwBhBC,UAxBgB;AAAA,eAyBhBmB,OAzBgB;AAAA,eA0BhBC,QA1BgB;AAAA,eA2BhBC,YA3BgB;AAAA,eA4BhBC,YA5BgB;AAAA,eA6BhBC,WA7BgB;;AACrB,cAAIlP,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2E,IAAP,KAAgBpB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmB,IAAL,GAAY3E,MAAM,CAAC2E,IAAnB;;AACA,cAAI3E,MAAM,CAAC0N,OAAP,KAAmBnK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkK,OAAL,GAAe1N,MAAM,CAAC0N,OAAtB;;AACA,cAAI1N,MAAM,CAAC+N,WAAP,KAAuBxK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmK,UAAL,GAAkB3N,MAAM,CAAC+N,WAAzB;;AACA,cAAI/N,MAAM,CAACmP,QAAP,KAAoB5L,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKsL,OAAL,GAAe9O,MAAM,CAACmP,QAAtB;;AACA,cAAInP,MAAM,CAACoP,SAAP,KAAqB7L,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuL,QAAL,GAAgB/O,MAAM,CAACoP,SAAvB;;AACA,cAAIpP,MAAM,CAACqP,aAAP,KAAyB9L,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKwL,YAAL,GAAoBhP,MAAM,CAACqP,aAA3B;;AACA,cAAIrP,MAAM,CAACsP,aAAP,KAAyB/L,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKyL,YAAL,GAAoBjP,MAAM,CAACsP,aAA3B;;AACA,cAAItP,MAAM,CAACuP,aAAP,KAAyBhM,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK0L,WAAL,GAAmBlP,MAAM,CAACuP,aAA1B;AACH;;AAYD9L,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAUtB;;AA3CgB,O;AAkDrB;AACA;AACA;;;2BACalG,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBuC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhB0E,IARgB;;AACrB,cAAI3E,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2E,IAAP,KAAgBpB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmB,IAAL,GAAY3E,MAAM,CAAC2E,IAAnB;AACH;;AAKDlB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;8BAsBVjG,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBsC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBwP,MAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,QAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,MAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,SA1BgB;;AACrB,cAAI3P,MAAM,CAACwP,MAAP,KAAkBjM,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgM,MAAL,GAAcxP,MAAM,CAACwP,MAArB;;AACA,cAAIxP,MAAM,CAACyP,QAAP,KAAoBlM,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiM,QAAL,GAAgBzP,MAAM,CAACyP,QAAvB;;AACA,cAAIzP,MAAM,CAAC0P,MAAP,KAAkBnM,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKkM,MAAL,GAAc1P,MAAM,CAAC0P,MAArB;;AACA,cAAI1P,MAAM,CAAC2P,SAAP,KAAqBpM,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKmM,SAAL,GAAiB3P,MAAM,CAAC2P,SAAxB;AACH;;AAmBDlM,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAnCqB,O;;uBA0CbhG,K,GAAN,MAAMA,KAAN,CAAY;AAEfqC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB2P,SAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,QA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBnJ,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBoJ,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAIhQ,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC4P,SAAP,KAAqBrM,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoM,SAAL,GAAiB5P,MAAM,CAAC4P,SAAxB;;AACA,cAAI5P,MAAM,CAAC6P,QAAP,KAAoBtM,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKqM,QAAL,GAAgB7P,MAAM,CAAC6P,QAAvB;;AACA,cAAI7P,MAAM,CAAC0G,IAAP,KAAgBnD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKkD,IAAL,GAAY1G,MAAM,CAAC0G,IAAnB;;AACA,cAAI1G,MAAM,CAAC8P,YAAP,KAAwBvM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKsM,YAAL,GAAoB9P,MAAM,CAAC8P,YAA3B;;AACA,cAAI9P,MAAM,CAAC+P,KAAP,KAAiBxM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuM,KAAL,GAAa/P,MAAM,CAAC+P,KAApB;;AACA,cAAI/P,MAAM,CAACgQ,YAAP,KAAwBzM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKwM,YAAL,GAAoBhQ,MAAM,CAACgQ,YAA3B;AACH;;AA+BDvM,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDc,O;;uBA+DN/F,K,GAAN,MAAMA,KAAN,CAAY;AAEfoC,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBC,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhBgQ,GAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhB1H,KApBgB;;AACrB,cAAIvI,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACiQ,GAAP,KAAe1M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKyM,GAAL,GAAWjQ,MAAM,CAACiQ,GAAlB;;AACA,cAAIjQ,MAAM,CAACuI,KAAP,KAAiBhF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK+E,KAAL,GAAavI,MAAM,CAACuI,KAApB;AACH;;AAeD9E,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5Bc,O;;sBAmCN9F,I,GAAN,MAAMA,IAAN,CAAW;AAEdmC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,EA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBiQ,GAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,EApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,GAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBxL,EA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhByL,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBzJ,EApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhB0J,GAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,GA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBhP,GAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBiP,GApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,EAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,EA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,GAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,EApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBlQ,EAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBmQ,GA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,GAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBhK,IApHgB;;AACrB,cAAI9G,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACkQ,GAAP,KAAe3M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK0M,GAAL,GAAWlQ,MAAM,CAACkQ,GAAlB;;AACA,cAAIlQ,MAAM,CAACmQ,EAAP,KAAc5M,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK2M,EAAL,GAAUnQ,MAAM,CAACmQ,EAAjB;;AACA,cAAInQ,MAAM,CAACoQ,GAAP,KAAe7M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK4M,GAAL,GAAWpQ,MAAM,CAACoQ,GAAlB;;AACA,cAAIpQ,MAAM,CAAC4E,EAAP,KAAcrB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoB,EAAL,GAAU5E,MAAM,CAAC4E,EAAjB;;AACA,cAAI5E,MAAM,CAACqQ,GAAP,KAAe9M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK6M,GAAL,GAAWrQ,MAAM,CAACqQ,GAAlB;;AACA,cAAIrQ,MAAM,CAAC4G,EAAP,KAAcrD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoD,EAAL,GAAU5G,MAAM,CAAC4G,EAAjB;;AACA,cAAI5G,MAAM,CAACsQ,GAAP,KAAe/M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK8M,GAAL,GAAWtQ,MAAM,CAACsQ,GAAlB;;AACA,cAAItQ,MAAM,CAACuQ,GAAP,KAAehN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK+M,GAAL,GAAWvQ,MAAM,CAACuQ,GAAlB;;AACA,cAAIvQ,MAAM,CAACuB,GAAP,KAAegC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKjC,GAAL,GAAWvB,MAAM,CAACuB,GAAlB;;AACA,cAAIvB,MAAM,CAACwQ,GAAP,KAAejN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgN,GAAL,GAAWxQ,MAAM,CAACwQ,GAAlB;;AACA,cAAIxQ,MAAM,CAACyQ,EAAP,KAAclN,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKiN,EAAL,GAAUzQ,MAAM,CAACyQ,EAAjB;;AACA,cAAIzQ,MAAM,CAAC0Q,EAAP,KAAcnN,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkN,EAAL,GAAU1Q,MAAM,CAAC0Q,EAAjB;;AACA,cAAI1Q,MAAM,CAAC2Q,GAAP,KAAepN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmN,GAAL,GAAW3Q,MAAM,CAAC2Q,GAAlB;;AACA,cAAI3Q,MAAM,CAAC4Q,EAAP,KAAcrN,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKoN,EAAL,GAAU5Q,MAAM,CAAC4Q,EAAjB;;AACA,cAAI5Q,MAAM,CAACU,EAAP,KAAc6C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9C,EAAL,GAAUV,MAAM,CAACU,EAAjB;;AACA,cAAIV,MAAM,CAAC6Q,GAAP,KAAetN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKqN,GAAL,GAAW7Q,MAAM,CAAC6Q,GAAlB;;AACA,cAAI7Q,MAAM,CAAC8Q,GAAP,KAAevN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKsN,GAAL,GAAW9Q,MAAM,CAAC8Q,GAAlB;;AACA,cAAI9Q,MAAM,CAAC8G,IAAP,KAAgBvD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsD,IAAL,GAAY9G,MAAM,CAAC8G,IAAnB;AACH;;AA+EDrD,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAoBtB;;AA5Ia,O;;sBAmJL7F,I,GAAN,MAAMA,IAAN,CAAW;AAEdkC,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChB6P,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBgB,SA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,SAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBzK,GA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhB6J,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBa,KApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,WAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,YA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,eAhFgB;;AACrB,cAAItR,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC8P,YAAP,KAAwBvM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKsM,YAAL,GAAoB9P,MAAM,CAAC8P,YAA3B;;AACA,cAAI9P,MAAM,CAAC+P,KAAP,KAAiBxM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuM,KAAL,GAAa/P,MAAM,CAAC+P,KAApB;;AACA,cAAI/P,MAAM,CAAC+Q,SAAP,KAAqBxN,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuN,SAAL,GAAiB/Q,MAAM,CAAC+Q,SAAxB;;AACA,cAAI/Q,MAAM,CAACgR,OAAP,KAAmBzN,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKwN,OAAL,GAAehR,MAAM,CAACgR,OAAtB;;AACA,cAAIhR,MAAM,CAACiR,QAAP,KAAoB1N,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKyN,QAAL,GAAgBjR,MAAM,CAACiR,QAAvB;;AACA,cAAIjR,MAAM,CAACkR,SAAP,KAAqB3N,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0N,SAAL,GAAiBlR,MAAM,CAACkR,SAAxB;;AACA,cAAIlR,MAAM,CAACyG,GAAP,KAAelD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiD,GAAL,GAAWzG,MAAM,CAACyG,GAAlB;;AACA,cAAIzG,MAAM,CAACsQ,GAAP,KAAe/M,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK8M,GAAL,GAAWtQ,MAAM,CAACsQ,GAAlB;;AACA,cAAItQ,MAAM,CAACmR,KAAP,KAAiB5N,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2N,KAAL,GAAanR,MAAM,CAACmR,KAApB;;AACA,cAAInR,MAAM,CAACoR,WAAP,KAAuB7N,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4N,WAAL,GAAmBpR,MAAM,CAACoR,WAA1B;;AACA,cAAIpR,MAAM,CAACqR,YAAP,KAAwB9N,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK6N,YAAL,GAAoBrR,MAAM,CAACqR,YAA3B;;AACA,cAAIrR,MAAM,CAACsR,eAAP,KAA2B/N,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK8N,eAAL,GAAuBtR,MAAM,CAACsR,eAA9B;AACH;;AAuDD7N,QAAAA,OAAO,CAACC,MAAD,EAAgB,CActB;;AAlGa,O;AA0GlB;AACA;AACA;;;sBACa5F,I,GAAN,MAAMA,IAAN,CAAW;AAGdiC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuR,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAInU,KAAJ,CAAUkU,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAEhDI,QAAAA,GAAG,CAACC,KAAD,EAAmC;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEtEnO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBa,O;AA4BlB;AACA;AACA;;;0BACa3F,Q,GAAN,MAAMA,QAAN,CAAe;AAGlBgC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIjU,SAAJ,CAAcgU,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1EzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;AA+BtB;AACA;AACA;;;gCACa1F,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB+B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBuR,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIpU,eAAJ,CAAoBmU,OAApB,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAsB;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE1DI,QAAAA,GAAG,CAACC,KAAD,EAA6C;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEhFnO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBuB,O;AA4B5B;AACA;AACA;;;yBACazF,O,GAAN,MAAMA,OAAN,CAAc;AAGjB8B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIrU,QAAJ,CAAaoU,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;AA+BrB;AACA;AACA;;;wBACaxF,M,GAAN,MAAMA,MAAN,CAAa;AAGhB6B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIlU,OAAJ,CAAYiU,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;8BA+BPvF,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB4B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjBmS,KACiB;AACrB,cAAInS,MAAM,CAACoS,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAI5O,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAK2O,KAAL,GAAa,IAAIrV,UAAJ,CAAekD,MAAM,CAAC,CAAD,CAArB,CAAb;AACH;;AAEDqS,QAAAA,OAAO,GAAe;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE5C;AACJ;AACA;;;AACsB,YAAb/F,aAAa,GAAW;AAAE,iBAAO,KAAK+F,KAAL,CAAW/F,aAAlB;AAAkC;AACjE;AACJ;AACA;;;AACkB,YAATC,SAAS,GAAW;AAAE,iBAAO,KAAK8F,KAAL,CAAW9F,SAAlB;AAA8B;AACzD;AACJ;AACA;;;AAC8B,YAArBC,qBAAqB,GAAW;AAAE,iBAAO,KAAK6F,KAAL,CAAW7F,qBAAlB;AAA0C;AACjF;AACJ;AACA;;;AAC2B,YAAlBC,kBAAkB,GAAW;AAAE,iBAAO,KAAK4F,KAAL,CAAW5F,kBAAlB;AAAuC;;AAE3E9I,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAKyO,KAAL,CAAW1O,OAAX,CAAmBC,MAAnB;AACH;;AA9BqB,O;;wBAqCbtF,M,GAAN,MAAMA,MAAN,CAAa;AAGhB2B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIrV,IAAJ,CAASoV,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;0BA+BPrF,Q,GAAN,MAAMA,QAAN,CAAe;AAGlB0B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIpV,MAAJ,CAAWmV,OAAX,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAwB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC3DJ,QAAAA,WAAW,GAAa;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAElDI,QAAAA,GAAG,CAACO,GAAD,EAAkC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEvEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;;2BA+BTpF,S,GAAN,MAAMA,SAAN,CAAgB;AAGnByB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAInV,OAAJ,CAAYkV,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;yBA+BVnF,O,GAAN,MAAMA,OAAN,CAAc;AAGjBwB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIhV,KAAJ,CAAU+U,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;2BA+BRlF,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBuB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI/U,OAAJ,CAAY8U,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;2BA+BVjF,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBsB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI7U,OAAJ,CAAY4U,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BVhF,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBqB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI5U,QAAJ,CAAa2U,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzN,EAArB,EAAyByN,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;yBA+BX/E,O,GAAN,MAAMA,OAAN,CAAc;AAGjBoB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI1U,KAAJ,CAAUyU,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;8BA+BR9E,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBmB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIzU,UAAJ,CAAewU,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DJ,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACO,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE3EzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;6BA+Bb7E,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBkB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIxU,SAAJ,CAAcuU,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1EzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;+BA+BZ5E,a,GAAN,MAAMA,aAAN,CAAoB;AAGvBiB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIvU,WAAJ,CAAgBsU,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEJ,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACO,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE5EzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;yBA+Bd3E,O,GAAN,MAAMA,OAAN,CAAc;AAGjBgB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI/T,KAAJ,CAAU8T,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;yBA+BR1E,O,GAAN,MAAMA,OAAN,CAAc;AAGjBe,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI9T,KAAJ,CAAU6T,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;wBA+BRzE,M,GAAN,MAAMA,MAAN,CAAa;AAGhBc,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI7T,IAAJ,CAAS4T,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;wBA+BPxE,M,GAAN,MAAMA,MAAN,CAAa;AAGhBa,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8R,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBxR,MAAnB,EAA2B;AACvB,gBAAIyR,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI5T,IAAJ,CAAS2T,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetL,IAAf,CAAoBwL,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACxR,EAArB,EAAyBwR,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEzO,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAKmO,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAACpO,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;wBAiCPvE,M,GAAN,MAAMA,MAAN,CAAa;AAEhB;AACJ;AACA;AACY,YAAJrB,IAAI,GAAU;AAAE,iBAAO,KAAKwU,KAAZ;AAAmB;;AAEvC;AACJ;AACA;AACgB,YAARvU,QAAQ,GAAc;AAAE,iBAAO,KAAKwU,SAAZ;AAAuB;;AAEnD;AACJ;AACA;AACsB,YAAdvU,cAAc,GAAoB;AAAE,iBAAO,KAAKwU,eAAZ;AAA6B;;AAErE;AACJ;AACA;AACe,YAAPvU,OAAO,GAAa;AAAE,iBAAO,KAAKwU,QAAZ;AAAsB;;AAEhD;AACJ;AACA;AACc,YAANvU,MAAM,GAAY;AAAE,iBAAO,KAAKwU,OAAZ;AAAqB;;AAE7B,YAAZvU,YAAY,GAAkB;AAAE,iBAAO,KAAKwU,aAAZ;AAA2B;;AAErD,YAANvU,MAAM,GAAY;AAAE,iBAAO,KAAKwU,OAAZ;AAAqB;;AAEjC,YAARvU,QAAQ,GAAc;AAAE,iBAAO,KAAKwU,SAAZ;AAAuB;;AAEtC,YAATvU,SAAS,GAAe;AAAE,iBAAO,KAAKwU,UAAZ;AAAwB;;AAE3C,YAAPvU,OAAO,GAAa;AAAE,iBAAO,KAAKwU,QAAZ;AAAsB;;AAEnC,YAATvU,SAAS,GAAe;AAAE,iBAAO,KAAKwU,UAAZ;AAAwB;;AAEzC,YAATvU,SAAS,GAAe;AAAE,iBAAO,KAAKwU,UAAZ;AAAwB;;AAExC,YAAVvU,UAAU,GAAgB;AAAE,iBAAO,KAAKwU,WAAZ;AAAyB;;AAE9C,YAAPvU,OAAO,GAAa;AAAE,iBAAO,KAAKwU,QAAZ;AAAsB;;AAEhC,YAAZvU,YAAY,GAAkB;AAAE,iBAAO,KAAKwU,aAAZ;AAA2B;;AAEhD,YAAXvU,WAAW,GAAiB;AAAE,iBAAO,KAAKwU,YAAZ;AAA0B;;AAE3C,YAAbvU,aAAa,GAAmB;AAAE,iBAAO,KAAKwU,cAAZ;AAA4B;;AAEvD,YAAPvU,OAAO,GAAa;AAAE,iBAAO,KAAKwU,QAAZ;AAAsB;;AAErC,YAAPvU,OAAO,GAAa;AAAE,iBAAO,KAAKwU,QAAZ;AAAsB;;AAEtC,YAANvU,MAAM,GAAY;AAAE,iBAAO,KAAKwU,OAAZ;AAAqB;;AAEnC,YAANvU,MAAM,GAAY;AAAE,iBAAO,KAAKwU,OAAZ;AAAqB;;AAE7C3T,QAAAA,WAAW,CAAC4T,MAAD,EAAqB;AAAA,eA1DxBrB,KA0DwB;AAAA,eArDxBC,SAqDwB;AAAA,eAhDxBC,eAgDwB;AAAA,eA3CxBC,QA2CwB;AAAA,eAtCxBC,OAsCwB;AAAA,eAjCxBC,aAiCwB;AAAA,eA/BxBC,OA+BwB;AAAA,eA7BxBC,SA6BwB;AAAA,eA3BxBC,UA2BwB;AAAA,eAzBxBC,QAyBwB;AAAA,eAvBxBC,UAuBwB;AAAA,eArBxBC,UAqBwB;AAAA,eAnBxBC,WAmBwB;AAAA,eAjBxBC,QAiBwB;AAAA,eAfxBC,aAewB;AAAA,eAbxBC,YAawB;AAAA,eAXxBC,cAWwB;AAAA,eATxBC,QASwB;AAAA,eAPxBC,QAOwB;AAAA,eALxBC,OAKwB;AAAA,eAHxBC,OAGwB;AAC5B,eAAKpB,KAAL,GAAa,IAAIxU,IAAJ,CAAS6V,MAAM,CAAC,MAAD,CAAf,CAAb;AACA,eAAKpB,SAAL,GAAiB,IAAIxU,QAAJ,CAAa4V,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKnB,eAAL,GAAuB,IAAIxU,cAAJ,CAAmB2V,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKlB,QAAL,GAAgB,IAAIxU,OAAJ,CAAY0V,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKjB,OAAL,GAAe,IAAIxU,MAAJ,CAAWyV,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKhB,aAAL,GAAqB,IAAIxU,YAAJ,CAAiBwV,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKf,OAAL,GAAe,IAAIxU,MAAJ,CAAWuV,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKd,SAAL,GAAiB,IAAIxU,QAAJ,CAAasV,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKb,UAAL,GAAkB,IAAIxU,SAAJ,CAAcqV,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKZ,QAAL,GAAgB,IAAIxU,OAAJ,CAAYoV,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKX,UAAL,GAAkB,IAAIxU,SAAJ,CAAcmV,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKV,UAAL,GAAkB,IAAIxU,SAAJ,CAAckV,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKT,WAAL,GAAmB,IAAIxU,UAAJ,CAAeiV,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKR,QAAL,GAAgB,IAAIxU,OAAJ,CAAYgV,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKP,aAAL,GAAqB,IAAIxU,YAAJ,CAAiB+U,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKN,YAAL,GAAoB,IAAIxU,WAAJ,CAAgB8U,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKL,cAAL,GAAsB,IAAIxU,aAAJ,CAAkB6U,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKJ,QAAL,GAAgB,IAAIxU,OAAJ,CAAY4U,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKH,QAAL,GAAgB,IAAIxU,OAAJ,CAAY2U,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKF,OAAL,GAAe,IAAIxU,MAAJ,CAAW0U,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKD,OAAL,GAAe,IAAIxU,MAAJ,CAAWyU,MAAM,CAAC,QAAD,CAAjB,CAAf;;AAEA,eAAKrB,KAAL,CAAW7O,OAAX,CAAmB,IAAnB;;AACA,eAAK8O,SAAL,CAAe9O,OAAf,CAAuB,IAAvB;;AACA,eAAK+O,eAAL,CAAqB/O,OAArB,CAA6B,IAA7B;;AACA,eAAKgP,QAAL,CAAchP,OAAd,CAAsB,IAAtB;;AACA,eAAKiP,OAAL,CAAajP,OAAb,CAAqB,IAArB;;AACA,eAAKkP,aAAL,CAAmBlP,OAAnB,CAA2B,IAA3B;;AACA,eAAKmP,OAAL,CAAanP,OAAb,CAAqB,IAArB;;AACA,eAAKoP,SAAL,CAAepP,OAAf,CAAuB,IAAvB;;AACA,eAAKqP,UAAL,CAAgBrP,OAAhB,CAAwB,IAAxB;;AACA,eAAKsP,QAAL,CAActP,OAAd,CAAsB,IAAtB;;AACA,eAAKuP,UAAL,CAAgBvP,OAAhB,CAAwB,IAAxB;;AACA,eAAKwP,UAAL,CAAgBxP,OAAhB,CAAwB,IAAxB;;AACA,eAAKyP,WAAL,CAAiBzP,OAAjB,CAAyB,IAAzB;;AACA,eAAK0P,QAAL,CAAc1P,OAAd,CAAsB,IAAtB;;AACA,eAAK2P,aAAL,CAAmB3P,OAAnB,CAA2B,IAA3B;;AACA,eAAK4P,YAAL,CAAkB5P,OAAlB,CAA0B,IAA1B;;AACA,eAAK6P,cAAL,CAAoB7P,OAApB,CAA4B,IAA5B;;AACA,eAAK8P,QAAL,CAAc9P,OAAd,CAAsB,IAAtB;;AACA,eAAK+P,QAAL,CAAc/P,OAAd,CAAsB,IAAtB;;AACA,eAAKgQ,OAAL,CAAahQ,OAAb,CAAqB,IAArB;;AACA,eAAKiQ,OAAL,CAAajQ,OAAb,CAAqB,IAArB;AACH;;AAvGe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\nexport namespace res { \n/**\n * 装备部位\n */\nexport enum EquipClass {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 火力核心\n     */\n    WEAPON = 1,\n    /**\n     * 副武器\n     */\n    SUB_WEAPON = 2,\n    /**\n     * 装甲核心\n     */\n    ARMOR = 3,\n    /**\n     * 科技核心\n     */\n    TECHNIC = 4,\n}\n\n} \nexport namespace res { \n/**\n * GM命令页签\n */\nexport enum GMTabID {\n    /**\n     * 通用\n     */\n    COMMON = 0,\n    /**\n     * 战斗\n     */\n    BATTLE = 1,\n}\n\n} \nexport namespace res { \n/**\n * 道具的使用效果\n */\nexport enum ItemEffectType {\n    /**\n     * 无效果\n     */\n    NONE = 0,\n    /**\n     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID\n     */\n    DROP = 1,\n    /**\n     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币\n     */\n    GEN_GOLD = 2,\n    /**\n     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石\n     */\n    GEN_DIAMOND = 3,\n    /**\n     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值\n     */\n    GEN_XP = 4,\n    /**\n     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值\n     */\n    GEN_ENERGY = 5,\n    /**\n     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具\n     */\n    GEN_ITEM = 6,\n}\n\n} \nexport namespace res { \n/**\n * 道具的使用类型\n */\nexport enum ItemUseType {\n    /**\n     * 不可直接从背包来使用的道具\n     */\n    NONE = 0,\n    /**\n     * 先放入背包内，然后由玩家从背包手动选择后使用\n     */\n    MANUAL = 1,\n    /**\n     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子\n     */\n    AUTO = 2,\n}\n\n} \nexport namespace res { \n/**\n * 模式类型\n */\nexport enum ModeType {\n    /**\n     * 无尽\n     */\n    ENDLESS = 0,\n    /**\n     * 剧情\n     */\n    STORY = 1,\n    /**\n     * 远征\n     */\n    EXPEDITION = 2,\n    /**\n     * 无尽PK\n     */\n    ENDLESSPK = 3,\n    /**\n     * 好友PK\n     */\n    FRIENDPK = 4,\n}\n\n} \nexport namespace res { \n/**\n * 货币类型\n */\nexport enum MoneyType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 金币\n     */\n    GOLD = 1,\n    /**\n     * 钻石\n     */\n    DIAMOND = 2,\n    /**\n     * 体力\n     */\n    POWER = 3,\n    /**\n     * 道具\n     */\n    ITEM = 4,\n}\n\n} \nexport namespace res { \n/**\n * 模式类型\n */\nexport enum PlayCycle {\n    /**\n     * 每日\n     */\n    DAY = 0,\n    /**\n     * 每周\n     */\n    WEEK = 1,\n}\n\n} \nexport namespace res { \n/**\n * 装备属性名称\n */\nexport enum PropName {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 攻击力\n     */\n    HURT = 1,\n    /**\n     * 生命值\n     */\n    HP = 2,\n}\n\n} \nexport namespace res { \n/**\n * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)\n */\nexport enum QualityType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 普通\n     */\n    COMMON = 1,\n    /**\n     * 精良\n     */\n    UNCOMMON = 2,\n    /**\n     * 稀有\n     */\n    RACE = 3,\n    /**\n     * 史诗\n     */\n    EPIC = 4,\n    /**\n     * 传说\n     */\n    LEGENDARY = 5,\n    /**\n     * 神话\n     */\n    MYTHIC = 6,\n}\n\n} \n \nexport enum TargetScanStrategy {\n    /**\n     * 更新\n     */\n    Refresh = 0,\n    /**\n     * 保持\n     */\n    Keep = 1,\n}\n\n \n\n\n\n\n\nexport class Boss {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.bId === undefined) { throw new Error() }\n        this.bId = _json_.bId\n        if (_json_.sId === undefined) { throw new Error() }\n        this.sId = _json_.sId\n        if (_json_.app === undefined) { throw new Error() }\n        this.app = _json_.app\n        if (_json_.ta === undefined) { throw new Error() }\n        this.ta = _json_.ta\n        if (_json_.ft === undefined) { throw new Error() }\n        this.ft = _json_.ft\n        if (_json_.leave === undefined) { throw new Error() }\n        this.leave = _json_.leave\n        if (_json_.exp === undefined) { throw new Error() }\n        this.exp = _json_.exp\n        if (_json_.rid === undefined) { throw new Error() }\n        this.rid = _json_.rid\n        if (_json_.sk === undefined) { throw new Error() }\n        this.sk = _json_.sk\n        if (_json_.blp === undefined) { throw new Error() }\n        this.blp = _json_.blp\n        if (_json_.us === undefined) { throw new Error() }\n        this.us = _json_.us\n        if (_json_.ua === undefined) { throw new Error() }\n        this.ua = _json_.ua\n        if (_json_.va === undefined) { throw new Error() }\n        this.va = _json_.va\n        if (_json_.sv === undefined) { throw new Error() }\n        this.sv = _json_.sv\n        if (_json_.fl === undefined) { throw new Error() }\n        this.fl = _json_.fl\n        if (_json_.loot === undefined) { throw new Error() }\n        this.loot = _json_.loot\n        if (_json_.adsorb === undefined) { throw new Error() }\n        this.adsorb = _json_.adsorb\n        if (_json_.lp0 === undefined) { throw new Error() }\n        this.lp0 = _json_.lp0\n        if (_json_.lp1 === undefined) { throw new Error() }\n        this.lp1 = _json_.lp1\n        if (_json_.dh === undefined) { throw new Error() }\n        this.dh = _json_.dh\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.col === undefined) { throw new Error() }\n        this.col = _json_.col\n        if (_json_.tway === undefined) { throw new Error() }\n        this.tway = _json_.tway\n        if (_json_.way === undefined) { throw new Error() }\n        this.way = _json_.way\n        if (_json_.wi === undefined) { throw new Error() }\n        this.wi = _json_.wi\n        if (_json_.sp === undefined) { throw new Error() }\n        this.sp = _json_.sp\n        if (_json_.ai === undefined) { throw new Error() }\n        this.ai = _json_.ai\n        if (_json_.ra === undefined) { throw new Error() }\n        this.ra = _json_.ra\n        if (_json_.a0 === undefined) { throw new Error() }\n        this.a0 = _json_.a0\n        if (_json_.a1 === undefined) { throw new Error() }\n        this.a1 = _json_.a1\n        if (_json_.a2 === undefined) { throw new Error() }\n        this.a2 = _json_.a2\n        if (_json_.a3 === undefined) { throw new Error() }\n        this.a3 = _json_.a3\n        if (_json_.a4 === undefined) { throw new Error() }\n        this.a4 = _json_.a4\n        if (_json_.a5 === undefined) { throw new Error() }\n        this.a5 = _json_.a5\n        if (_json_.a6 === undefined) { throw new Error() }\n        this.a6 = _json_.a6\n        if (_json_.a7 === undefined) { throw new Error() }\n        this.a7 = _json_.a7\n        if (_json_.a8 === undefined) { throw new Error() }\n        this.a8 = _json_.a8\n        if (_json_.a9 === undefined) { throw new Error() }\n        this.a9 = _json_.a9\n        if (_json_.a10 === undefined) { throw new Error() }\n        this.a10 = _json_.a10\n        if (_json_.a11 === undefined) { throw new Error() }\n        this.a11 = _json_.a11\n        if (_json_.a12 === undefined) { throw new Error() }\n        this.a12 = _json_.a12\n        if (_json_.a13 === undefined) { throw new Error() }\n        this.a13 = _json_.a13\n        if (_json_.a14 === undefined) { throw new Error() }\n        this.a14 = _json_.a14\n        if (_json_.a15 === undefined) { throw new Error() }\n        this.a15 = _json_.a15\n        if (_json_.a16 === undefined) { throw new Error() }\n        this.a16 = _json_.a16\n        if (_json_.a17 === undefined) { throw new Error() }\n        this.a17 = _json_.a17\n        if (_json_.a18 === undefined) { throw new Error() }\n        this.a18 = _json_.a18\n        if (_json_.a19 === undefined) { throw new Error() }\n        this.a19 = _json_.a19\n        if (_json_.a20 === undefined) { throw new Error() }\n        this.a20 = _json_.a20\n        if (_json_.a21 === undefined) { throw new Error() }\n        this.a21 = _json_.a21\n        if (_json_.a22 === undefined) { throw new Error() }\n        this.a22 = _json_.a22\n        if (_json_.a100 === undefined) { throw new Error() }\n        this.a100 = _json_.a100\n        if (_json_.a101 === undefined) { throw new Error() }\n        this.a101 = _json_.a101\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 飞机id\n     */\n    readonly bId: number\n    /**\n     * 子类型\n     */\n    readonly sId: number\n    /**\n     * 出场参数\n     */\n    readonly app: string\n    /**\n     * inAudio\n     */\n    readonly ta: string\n    /**\n     * 死亡掉落延迟\n     */\n    readonly ft: number\n    /**\n     * leave\n     */\n    readonly leave: number\n    /**\n     * exp\n     */\n    readonly exp: number\n    /**\n     * rid\n     */\n    readonly rid: string\n    /**\n     * 爆炸震动参数\n     */\n    readonly sk: string\n    /**\n     * 爆炸参数\n     */\n    readonly blp: string\n    /**\n     * us\n     */\n    readonly us: string\n    /**\n     * ua\n     */\n    readonly ua: string\n    /**\n     * va\n     */\n    readonly va: string\n    /**\n     * sv\n     */\n    readonly sv: string\n    /**\n     * fl\n     */\n    readonly fl: string\n    /**\n     * loot\n     */\n    readonly loot: string\n    /**\n     * adsorb\n     */\n    readonly adsorb: string\n    /**\n     * lp0\n     */\n    readonly lp0: string\n    /**\n     * lp1\n     */\n    readonly lp1: string\n    /**\n     * dh\n     */\n    readonly dh: string\n    /**\n     * atk\n     */\n    readonly atk: number\n    /**\n     * col\n     */\n    readonly col: number\n    /**\n     * tway\n     */\n    readonly tway: string\n    /**\n     * way\n     */\n    readonly way: string\n    /**\n     * wi\n     */\n    readonly wi: string\n    /**\n     * sp\n     */\n    readonly sp: string\n    /**\n     * ai\n     */\n    readonly ai: string\n    /**\n     * ra\n     */\n    readonly ra: string\n    /**\n     * a0\n     */\n    readonly a0: string\n    /**\n     * a1\n     */\n    readonly a1: string\n    /**\n     * a2\n     */\n    readonly a2: string\n    /**\n     * a3\n     */\n    readonly a3: string\n    /**\n     * a4\n     */\n    readonly a4: string\n    /**\n     * a5\n     */\n    readonly a5: string\n    /**\n     * a6\n     */\n    readonly a6: string\n    /**\n     * a7\n     */\n    readonly a7: string\n    /**\n     * a8\n     */\n    readonly a8: string\n    /**\n     * a9\n     */\n    readonly a9: string\n    /**\n     * a10\n     */\n    readonly a10: string\n    /**\n     * a11\n     */\n    readonly a11: string\n    /**\n     * a12\n     */\n    readonly a12: string\n    /**\n     * a13\n     */\n    readonly a13: string\n    /**\n     * a14\n     */\n    readonly a14: string\n    /**\n     * a15\n     */\n    readonly a15: string\n    /**\n     * a16\n     */\n    readonly a16: string\n    /**\n     * a17\n     */\n    readonly a17: string\n    /**\n     * a18\n     */\n    readonly a18: string\n    /**\n     * a19\n     */\n    readonly a19: string\n    /**\n     * a20\n     */\n    readonly a20: string\n    /**\n     * a21\n     */\n    readonly a21: string\n    /**\n     * a22\n     */\n    readonly a22: string\n    /**\n     * a100\n     */\n    readonly a100: string\n    /**\n     * a101\n     */\n    readonly a101: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\nexport namespace builtin {\nexport class ConParam {\n\n    constructor(_json_: any) {\n        if (_json_.con === undefined) { throw new Error() }\n        this.con = _json_.con\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly con: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\n/**\n * 随机策略\n */\nexport class randStrategy {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.Weight === undefined) { throw new Error() }\n        this.Weight = _json_.Weight\n    }\n\n    /**\n     * 随机策略ID\n     */\n    readonly ID: number\n    /**\n     * ID的权重\n     */\n    readonly Weight: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class RatingParam {\n\n    constructor(_json_: any) {\n        if (_json_.rating === undefined) { throw new Error() }\n        this.rating = _json_.rating\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly rating: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class Bullet {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.am === undefined) { throw new Error() }\n        this.am = _json_.am\n        if (_json_.image === undefined) { throw new Error() }\n        this.image = _json_.image\n        if (_json_.bustyle === undefined) { throw new Error() }\n        this.bustyle = _json_.bustyle\n        if (_json_.angleSpeed === undefined) { throw new Error() }\n        this.angleSpeed = _json_.angleSpeed\n        if (_json_.waittime === undefined) { throw new Error() }\n        { this.waittime = []; for(let _ele0 of _json_.waittime) { let _e0; _e0 = _ele0; this.waittime.push(_e0);}}\n        if (_json_.initialve === undefined) { throw new Error() }\n        this.initialve = _json_.initialve\n        if (_json_.spdiff === undefined) { throw new Error() }\n        this.spdiff = _json_.spdiff\n        if (_json_.scale === undefined) { throw new Error() }\n        this.scale = _json_.scale\n        if (_json_.retrieve === undefined) { throw new Error() }\n        this.retrieve = _json_.retrieve\n        if (_json_.disappear === undefined) { throw new Error() }\n        this.disappear = _json_.disappear\n        if (_json_.shiftingbody === undefined) { throw new Error() }\n        { this.shiftingbody = []; for(let _ele0 of _json_.shiftingbody) { let _e0; _e0 = _ele0; this.shiftingbody.push(_e0);}}\n        if (_json_.body === undefined) { throw new Error() }\n        this.body = _json_.body\n        if (_json_.exstyle1 === undefined) { throw new Error() }\n        this.exstyle1 = _json_.exstyle1\n        if (_json_.exstyle2 === undefined) { throw new Error() }\n        this.exstyle2 = _json_.exstyle2\n        if (_json_.time === undefined) { throw new Error() }\n        this.time = _json_.time\n        if (_json_.accnumber === undefined) { throw new Error() }\n        this.accnumber = _json_.accnumber\n        if (_json_.acc === undefined) { throw new Error() }\n        this.acc = _json_.acc\n        if (_json_.offset === undefined) { throw new Error() }\n        { this.offset = []; for(let _ele0 of _json_.offset) { let _e0; _e0 = _ele0; this.offset.push(_e0);}}\n        if (_json_.para === undefined) { throw new Error() }\n        { this.para = []; for(let _ele0 of _json_.para) { let _e0; _e0 = _ele0; this.para.push(_e0);}}\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * name\n     */\n    readonly name: string\n    /**\n     * am\n     */\n    readonly am: string\n    /**\n     * image\n     */\n    readonly image: string\n    /**\n     * 子弹类型\n     */\n    readonly bustyle: number\n    /**\n     * 旋转角度\n     */\n    readonly angleSpeed: number\n    /**\n     * 等待时间\n     */\n    readonly waittime: number[]\n    /**\n     * 速度\n     */\n    readonly initialve: number\n    /**\n     * 速度随机变量\n     */\n    readonly spdiff: number\n    /**\n     * 缩放\n     */\n    readonly scale: number\n    /**\n     * 子弹存活时间\n     */\n    readonly retrieve: number\n    /**\n     * 是否穿透\n     */\n    readonly disappear: number\n    /**\n     * 碰撞宽高\n     */\n    readonly shiftingbody: number[]\n    /**\n     * 碰撞\n     */\n    readonly body: number\n    /**\n     * 伤害粒子效果\n     */\n    readonly exstyle1: string\n    /**\n     * 伤害粒子缩放\n     */\n    readonly exstyle2: string\n    /**\n     * time\n     */\n    readonly time: number\n    /**\n     * accnumber\n     */\n    readonly accnumber: number\n    /**\n     * acc\n     */\n    readonly acc: number\n    /**\n     * offset\n     */\n    readonly offset: number[]\n    /**\n     * para\n     */\n    readonly para: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Chapter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.levelCount === undefined) { throw new Error() }\n        this.levelCount = _json_.levelCount\n        if (_json_.levelGroupCount === undefined) { throw new Error() }\n        this.levelGroupCount = _json_.levelGroupCount\n        if (_json_.strategy === undefined) { throw new Error() }\n        this.strategy = _json_.strategy\n        if (_json_.damageBonus === undefined) { throw new Error() }\n        this.damageBonus = _json_.damageBonus\n        if (_json_.lifeBounus === undefined) { throw new Error() }\n        this.lifeBounus = _json_.lifeBounus\n        if (_json_.strategyList === undefined) { throw new Error() }\n        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.strategyList.push(_e0);}}\n    }\n\n    /**\n     * 章节ID\n     */\n    readonly id: number\n    /**\n     * 章节关卡数量\n     */\n    readonly levelCount: number\n    /**\n     * 章节关卡组数量\n     */\n    readonly levelGroupCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly strategy: number\n    /**\n     * 章节伤害加成\n     */\n    readonly damageBonus: number\n    /**\n     * 章节生命加成\n     */\n    readonly lifeBounus: number\n    readonly strategyList: builtin.randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.strategyList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 消耗的材料\n */\nexport class ConsumeItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    readonly id: number\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ConsumeMoney {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    /**\n     * 货币类型\n     */\n    readonly type: res.MoneyType\n    /**\n     * 货币数量\n     */\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class Enemy {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.uiId === undefined) { throw new Error() }\n        this.uiId = _json_.uiId\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.collideLevel === undefined) { throw new Error() }\n        this.collideLevel = _json_.collideLevel\n        if (_json_.turn === undefined) { throw new Error() }\n        this.turn = _json_.turn\n        if (_json_.hpShow === undefined) { throw new Error() }\n        this.hpShow = _json_.hpShow\n        if (_json_.collideAttack === undefined) { throw new Error() }\n        this.collideAttack = _json_.collideAttack\n        if (_json_.bCollideDead === undefined) { throw new Error() }\n        this.bCollideDead = _json_.bCollideDead\n        if (_json_.bMoveAttack === undefined) { throw new Error() }\n        this.bMoveAttack = _json_.bMoveAttack\n        if (_json_.bStayAttack === undefined) { throw new Error() }\n        this.bStayAttack = _json_.bStayAttack\n        if (_json_.attackInterval === undefined) { throw new Error() }\n        this.attackInterval = _json_.attackInterval\n        if (_json_.attackNum === undefined) { throw new Error() }\n        this.attackNum = _json_.attackNum\n        if (_json_.attackData === undefined) { throw new Error() }\n        this.attackData = _json_.attackData\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n        if (_json_.dieShoot === undefined) { throw new Error() }\n        this.dieShoot = _json_.dieShoot\n        if (_json_.dieBullet === undefined) { throw new Error() }\n        this.dieBullet = _json_.dieBullet\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 敌机的显示id\n     */\n    readonly uiId: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 碰撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 是否改变方向\n     */\n    readonly turn: number\n    /**\n     * 是否显示血条\n     */\n    readonly hpShow: number\n    /**\n     * 碰撞伤害值\n     */\n    readonly collideAttack: number\n    /**\n     * 碰撞后是否死亡\n     */\n    readonly bCollideDead: number\n    /**\n     * 移动时是否攻击\n     */\n    readonly bMoveAttack: number\n    /**\n     * 静止时是否攻击\n     */\n    readonly bStayAttack: number\n    /**\n     * 攻击间隔时间\n     */\n    readonly attackInterval: number\n    /**\n     * 攻击次数\n     */\n    readonly attackNum: number\n    /**\n     * 攻击点位置数据(x,y;间隔,子弹id,子弹数量,子弹间隔,子弹攻击力百分比(100为1倍);)\n     */\n    readonly attackData: string\n    /**\n     * 自定义参数\n     */\n    readonly param: string\n    /**\n     * 死亡时发射的子弹数据\n     */\n    readonly dieShoot: string\n    /**\n     * 死亡时是否发射子弹\n     */\n    readonly dieBullet: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class EnemyUI {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.image === undefined) { throw new Error() }\n        this.image = _json_.image\n        if (_json_.isAm === undefined) { throw new Error() }\n        this.isAm = _json_.isAm\n        if (_json_.collider === undefined) { throw new Error() }\n        this.collider = _json_.collider\n        if (_json_.hpParam === undefined) { throw new Error() }\n        this.hpParam = _json_.hpParam\n        if (_json_.blastSound === undefined) { throw new Error() }\n        this.blastSound = _json_.blastSound\n        if (_json_.blp === undefined) { throw new Error() }\n        this.blp = _json_.blp\n        if (_json_.blastDurations === undefined) { throw new Error() }\n        this.blastDurations = _json_.blastDurations\n        if (_json_.blastShake === undefined) { throw new Error() }\n        this.blastShake = _json_.blastShake\n        if (_json_.damageParam === undefined) { throw new Error() }\n        this.damageParam = _json_.damageParam\n        if (_json_.extraParam0 === undefined) { throw new Error() }\n        this.extraParam0 = _json_.extraParam0\n        if (_json_.extraParam1 === undefined) { throw new Error() }\n        this.extraParam1 = _json_.extraParam1\n        if (_json_.skillResistUIDict === undefined) { throw new Error() }\n        this.skillResistUIDict = _json_.skillResistUIDict\n        if (_json_.lootParam0 === undefined) { throw new Error() }\n        this.lootParam0 = _json_.lootParam0\n        if (_json_.lootParam1 === undefined) { throw new Error() }\n        this.lootParam1 = _json_.lootParam1\n        if (_json_.showParam === undefined) { throw new Error() }\n        this.showParam = _json_.showParam\n        if (_json_.sneakAnim === undefined) { throw new Error() }\n        this.sneakAnim = _json_.sneakAnim\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 图片\n     */\n    readonly image: string\n    /**\n     * 是否动画\n     */\n    readonly isAm: number\n    /**\n     * 碰撞器数据\n     */\n    readonly collider: string\n    /**\n     * 血量参数\n     */\n    readonly hpParam: string\n    /**\n     * 爆炸音效 ID\n     */\n    readonly blastSound: number\n    /**\n     * 爆炸次数和爆炸参数\n     */\n    readonly blp: string\n    /**\n     * 爆炸持续时间\n     */\n    readonly blastDurations: string\n    /**\n     * 爆炸震动参数\n     */\n    readonly blastShake: string\n    /**\n     * 伤害参数\n     */\n    readonly damageParam: string\n    /**\n     * 额外参数\n     */\n    readonly extraParam0: string\n    /**\n     * 额外参数 1\n     */\n    readonly extraParam1: string\n    /**\n     * 技能抗性字典\n     */\n    readonly skillResistUIDict: string\n    /**\n     * 掉落参数 0\n     */\n    readonly lootParam0: string\n    /**\n     * 掉落参数 1\n     */\n    readonly lootParam1: string\n    /**\n     * 显示参数\n     */\n    readonly showParam: string\n    /**\n     * 潜行动画\n     */\n    readonly sneakAnim: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备属性\n */\nexport class EquipProp {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    readonly id: res.PropName\n    readonly value: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class GameMap {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.level === undefined) { throw new Error() }\n        this.level = _json_.level\n        if (_json_.floor_res === undefined) { throw new Error() }\n        { this.floorRes = []; for(let _ele0 of _json_.floor_res) { let _e0; _e0 = _ele0; this.floorRes.push(_e0);}}\n        if (_json_.hide_img === undefined) { throw new Error() }\n        { this.hideImg = []; for(let _ele0 of _json_.hide_img) { let _e0; _e0 = _ele0; this.hideImg.push(_e0);}}\n        if (_json_.sky_res === undefined) { throw new Error() }\n        { this.skyRes = []; for(let _ele0 of _json_.sky_res) { let _e0; _e0 = _ele0; this.skyRes.push(_e0);}}\n        if (_json_.imageSque_res === undefined) { throw new Error() }\n        { this.imageSqueRes = []; for(let _ele0 of _json_.imageSque_res) { let _e0; _e0 = _ele0; this.imageSqueRes.push(_e0);}}\n        if (_json_.floor_speed === undefined) { throw new Error() }\n        { this.floorSpeed = []; for(let _ele0 of _json_.floor_speed) { let _e0; _e0 = _ele0; this.floorSpeed.push(_e0);}}\n        if (_json_.sky_speed === undefined) { throw new Error() }\n        { this.skySpeed = []; for(let _ele0 of _json_.sky_speed) { let _e0; _e0 = _ele0; this.skySpeed.push(_e0);}}\n        if (_json_.imageSque_speed === undefined) { throw new Error() }\n        { this.imageSqueSpeed = []; for(let _ele0 of _json_.imageSque_speed) { let _e0; _e0 = _ele0; this.imageSqueSpeed.push(_e0);}}\n        if (_json_.floor_layer === undefined) { throw new Error() }\n        { this.floorLayer = []; for(let _ele0 of _json_.floor_layer) { let _e0; _e0 = _ele0; this.floorLayer.push(_e0);}}\n        if (_json_.sky_layer === undefined) { throw new Error() }\n        { this.skyLayer = []; for(let _ele0 of _json_.sky_layer) { let _e0; _e0 = _ele0; this.skyLayer.push(_e0);}}\n        if (_json_.imageSque_layer === undefined) { throw new Error() }\n        { this.imageSqueLayer = []; for(let _ele0 of _json_.imageSque_layer) { let _e0; _e0 = _ele0; this.imageSqueLayer.push(_e0);}}\n        if (_json_.imageSqueNode_move === undefined) { throw new Error() }\n        { this.imageSqueNodeMove = []; for(let _ele0 of _json_.imageSqueNode_move) { let _e0; _e0 = _ele0; this.imageSqueNodeMove.push(_e0);}}\n        if (_json_.imageSque_pos === undefined) { throw new Error() }\n        { this.imageSquePos = []; for(let _ele0 of _json_.imageSque_pos) { let _e0; _e0 = _ele0; this.imageSquePos.push(_e0);}}\n        if (_json_.skyNode_move === undefined) { throw new Error() }\n        { this.skyNodeMove = []; for(let _ele0 of _json_.skyNode_move) { let _e0; _e0 = _ele0; this.skyNodeMove.push(_e0);}}\n        if (_json_.link_y_distance === undefined) { throw new Error() }\n        { this.linkYDistance = []; for(let _ele0 of _json_.link_y_distance) { let _e0; _e0 = _ele0; this.linkYDistance.push(_e0);}}\n        if (_json_.sky_angle === undefined) { throw new Error() }\n        { this.skyAngle = []; for(let _ele0 of _json_.sky_angle) { let _e0; _e0 = _ele0; this.skyAngle.push(_e0);}}\n        if (_json_.sky_layout === undefined) { throw new Error() }\n        { this.skyLayout = []; for(let _ele0 of _json_.sky_layout) { let _e0; _e0 = _ele0; this.skyLayout.push(_e0);}}\n        if (_json_.in_map_item === undefined) { throw new Error() }\n        { this.inMapItem = []; for(let _ele0 of _json_.in_map_item) { let _e0; _e0 = _ele0; this.inMapItem.push(_e0);}}\n        if (_json_.start_y === undefined) { throw new Error() }\n        this.startY = _json_.start_y\n        if (_json_.total_rules === undefined) { throw new Error() }\n        this.totalRules = _json_.total_rules\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * level\n     */\n    readonly level: number\n    /**\n     * floor_res\n     */\n    readonly floorRes: string[]\n    /**\n     * hide_img\n     */\n    readonly hideImg: string[]\n    /**\n     * sky_res\n     */\n    readonly skyRes: string[]\n    /**\n     * imageSque_res\n     */\n    readonly imageSqueRes: string[]\n    /**\n     * floor_speed\n     */\n    readonly floorSpeed: number[]\n    /**\n     * sky_speed\n     */\n    readonly skySpeed: number[]\n    /**\n     * imageSque_speed\n     */\n    readonly imageSqueSpeed: number[]\n    /**\n     * floor_layer\n     */\n    readonly floorLayer: number[]\n    /**\n     * sky_layer\n     */\n    readonly skyLayer: number[]\n    /**\n     * imageSque_layer\n     */\n    readonly imageSqueLayer: number[]\n    /**\n     * imageSqueNode_move\n     */\n    readonly imageSqueNodeMove: number[]\n    /**\n     * imageSque_pos\n     */\n    readonly imageSquePos: number[]\n    /**\n     * skyNode_move\n     */\n    readonly skyNodeMove: number[]\n    /**\n     * link_y_distance\n     */\n    readonly linkYDistance: string[]\n    /**\n     * sky_angle\n     */\n    readonly skyAngle: number[]\n    /**\n     * sky_layout\n     */\n    readonly skyLayout: number[]\n    /**\n     * in_map_item\n     */\n    readonly inMapItem: number[]\n    /**\n     * start_y\n     */\n    readonly startY: number\n    /**\n     * total_rules\n     */\n    readonly totalRules: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class GameMode {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.modeType === undefined) { throw new Error() }\n        this.modeType = _json_.modeType\n        if (_json_.chapterID === undefined) { throw new Error() }\n        this.chapterID = _json_.chapterID\n        if (_json_.order === undefined) { throw new Error() }\n        this.order = _json_.order\n        if (_json_.resourceID === undefined) { throw new Error() }\n        this.resourceID = _json_.resourceID\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.conList === undefined) { throw new Error() }\n        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new builtin.ConParam(_ele0); this.conList.push(_e0);}}\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.times === undefined) { throw new Error() }\n        this.times = _json_.times\n        if (_json_.monType === undefined) { throw new Error() }\n        this.monType = _json_.monType\n        if (_json_.costParam1 === undefined) { throw new Error() }\n        this.costParam1 = _json_.costParam1\n        if (_json_.costParam2 === undefined) { throw new Error() }\n        this.costParam2 = _json_.costParam2\n        if (_json_.rebirthTimes === undefined) { throw new Error() }\n        this.rebirthTimes = _json_.rebirthTimes\n        if (_json_.rebirthCost === undefined) { throw new Error() }\n        this.rebirthCost = _json_.rebirthCost\n        if (_json_.power === undefined) { throw new Error() }\n        this.power = _json_.power\n        if (_json_.rogueID === undefined) { throw new Error() }\n        this.rogueID = _json_.rogueID\n        if (_json_.LevelLimit === undefined) { throw new Error() }\n        this.LevelLimit = _json_.LevelLimit\n        if (_json_.rogueFirst === undefined) { throw new Error() }\n        this.rogueFirst = _json_.rogueFirst\n        if (_json_.sweepLimit === undefined) { throw new Error() }\n        this.sweepLimit = _json_.sweepLimit\n        if (_json_.rewardID1 === undefined) { throw new Error() }\n        this.rewardID1 = _json_.rewardID1\n        if (_json_.rewardID2 === undefined) { throw new Error() }\n        this.rewardID2 = _json_.rewardID2\n        if (_json_.ratingList === undefined) { throw new Error() }\n        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new builtin.RatingParam(_ele0); this.ratingList.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly ID: number\n    /**\n     * 模式类型\n     */\n    readonly modeType: res.ModeType\n    /**\n     * 章节ID\n     */\n    readonly chapterID: number\n    /**\n     * 排序\n     */\n    readonly order: number\n    /**\n     * 入口资源\n     */\n    readonly resourceID: number\n    /**\n     * 文本介绍\n     */\n    readonly description: string\n    readonly conList: builtin.ConParam[]\n    /**\n     * 进入周期\n     */\n    readonly cycle: res.PlayCycle\n    /**\n     * 进入次数\n     */\n    readonly times: number\n    /**\n     * 消耗类型\n     */\n    readonly monType: res.MoneyType\n    /**\n     * 消耗参数1\n     */\n    readonly costParam1: number\n    /**\n     * 消耗参数2\n     */\n    readonly costParam2: number\n    /**\n     * 复活次数\n     */\n    readonly rebirthTimes: number\n    /**\n     * 复活消耗\n     */\n    readonly rebirthCost: number\n    /**\n     * 战力评估\n     */\n    readonly power: number\n    /**\n     * 肉鸽组\n     */\n    readonly rogueID: number\n    /**\n     * 局内等级上限\n     */\n    readonly LevelLimit: number\n    /**\n     * 初始肉鸽选择\n     */\n    readonly rogueFirst: number\n    /**\n     * 扫荡次数\n     */\n    readonly sweepLimit: number\n    /**\n     * 奖励ID1\n     */\n    readonly rewardID1: number\n    /**\n     * 奖励ID2\n     */\n    readonly rewardID2: number\n    readonly ratingList: builtin.RatingParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class GlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }\n        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval\n        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }\n        this.EnergyRecoverValue = _json_.EnergyRecoverValue\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n    /**\n     * 体力上限值\n     */\n    readonly MaxEnergy: number\n    /**\n     * 体力恢复的间隔时间（秒）\n     */\n    readonly EnergyRecoverInterval: number\n    /**\n     * 体力恢复的值\n     */\n    readonly EnergyRecoverValue: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Level {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.forbidFire === undefined) { throw new Error() }\n        this.forbidFire = _json_.forbidFire\n        if (_json_.forbidNBomb === undefined) { throw new Error() }\n        this.forbidNBomb = _json_.forbidNBomb\n        if (_json_.forbidActSkill === undefined) { throw new Error() }\n        this.forbidActSkill = _json_.forbidActSkill\n        if (_json_.planeCollisionScaling === undefined) { throw new Error() }\n        this.planeCollisionScaling = _json_.planeCollisionScaling\n        if (_json_.levelType === undefined) { throw new Error() }\n        this.levelType = _json_.levelType\n    }\n\n    /**\n     * 关卡id\n     */\n    readonly id: number\n    /**\n     * 关卡prefab\n     */\n    readonly prefab: string\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidFire: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidNBomb: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidActSkill: boolean\n    /**\n     * 0到1（1表示正常碰撞）\n     */\n    readonly planeCollisionScaling: number\n    /**\n     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关\n     */\n    readonly levelType: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class LevelGroup {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.normLevelCount === undefined) { throw new Error() }\n        this.normLevelCount = _json_.normLevelCount\n        if (_json_.normLevelST === undefined) { throw new Error() }\n        this.normLevelST = _json_.normLevelST\n        if (_json_.normSTList === undefined) { throw new Error() }\n        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.normSTList.push(_e0);}}\n        if (_json_.bossLevelCount === undefined) { throw new Error() }\n        this.bossLevelCount = _json_.bossLevelCount\n        if (_json_.bossLevelST === undefined) { throw new Error() }\n        this.bossLevelST = _json_.bossLevelST\n        if (_json_.bossSTList === undefined) { throw new Error() }\n        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.bossSTList.push(_e0);}}\n    }\n\n    /**\n     * 关卡组ID\n     */\n    readonly id: number\n    /**\n     * 常规关卡数量\n     */\n    readonly normLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly normLevelST: number\n    readonly normSTList: builtin.randStrategy[]\n    /**\n     * BOSS关卡数量\n     */\n    readonly bossLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly bossLevelST: number\n    readonly bossSTList: builtin.randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.normSTList) { _e?.resolve(tables); }\n        \n        \n        for (let _e of this.bossSTList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class MainPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.body === undefined) { throw new Error() }\n        { this.body = []; for(let _ele0 of _json_.body) { let _e0; _e0 = _ele0; this.body.push(_e0);}}\n        if (_json_.transSrc === undefined) { throw new Error() }\n        { this.transSrc = []; for(let _ele0 of _json_.transSrc) { let _e0; _e0 = _ele0; this.transSrc.push(_e0);}}\n        if (_json_.transExt === undefined) { throw new Error() }\n        { this.transExt = []; for(let _ele0 of _json_.transExt) { let _e0; _e0 = _ele0; this.transExt.push(_e0);}}\n        if (_json_.zjdmtxzb === undefined) { throw new Error() }\n        { this.zjdmtxzb = []; for(let _ele0 of _json_.zjdmtxzb) { let _e0; _e0 = _ele0; this.zjdmtxzb.push(_e0);}}\n        if (_json_.transatk1 === undefined) { throw new Error() }\n        { this.transatk1 = []; for(let _ele0 of _json_.transatk1) { let _e0; _e0 = _ele0; this.transatk1.push(_e0);}}\n        if (_json_.shiftingatk1 === undefined) { throw new Error() }\n        { this.shiftingatk1 = []; for(let _ele0 of _json_.shiftingatk1) { let _e0; _e0 = _ele0; this.shiftingatk1.push(_e0);}}\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * type\n     */\n    readonly type: number\n    /**\n     * 碰撞范围\n     */\n    readonly body: number[]\n    /**\n     * 变现\n     */\n    readonly transSrc: string[]\n    /**\n     * 变形参数\n     */\n    readonly transExt: string[]\n    /**\n     * 火焰位置\n     */\n    readonly zjdmtxzb: number[]\n    /**\n     * transatk1\n     */\n    readonly transatk1: number[]\n    /**\n     * shiftingatk1\n     */\n    readonly shiftingatk1: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class MainPlaneLv {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 属性增幅\n */\nexport class PropInc {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.inc === undefined) { throw new Error() }\n        this.inc = _json_.inc\n    }\n\n    readonly id: res.PropName\n    /**\n     * 万分比\n     */\n    readonly inc: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备\n */\nexport class ResEquip {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.props === undefined) { throw new Error() }\n        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    readonly id: number\n    readonly name: string\n    readonly quality: res.QualityType\n    readonly qualitySub: number\n    readonly equipClass: res.EquipClass\n    readonly props: EquipProp[]\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        for (let _e of this.props) { _e?.resolve(tables); }\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 装备位升级\n */\nexport class ResEquipUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.level_from === undefined) { throw new Error() }\n        this.levelFrom = _json_.level_from\n        if (_json_.level_to === undefined) { throw new Error() }\n        this.levelTo = _json_.level_to\n        if (_json_.prop_inc === undefined) { throw new Error() }\n        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}\n        if (_json_.consume_money === undefined) { throw new Error() }\n        this.consumeMoney = new ConsumeMoney(_json_.consume_money)\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * 装备槽位的类型\n     */\n    readonly equipClass: res.EquipClass\n    /**\n     * 等级下限\n     */\n    readonly levelFrom: number\n    /**\n     * 等级上限\n     */\n    readonly levelTo: number\n    readonly propInc: PropInc[]\n    readonly consumeMoney: ConsumeMoney\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.propInc) { _e?.resolve(tables); }\n        this.consumeMoney?.resolve(tables);\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResGM {\n\n    constructor(_json_: any) {\n        if (_json_.tabID === undefined) { throw new Error() }\n        this.tabID = _json_.tabID\n        if (_json_.tabName === undefined) { throw new Error() }\n        this.tabName = _json_.tabName\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.cmd === undefined) { throw new Error() }\n        this.cmd = _json_.cmd\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    readonly tabID: res.GMTabID\n    readonly tabName: string\n    readonly name: string\n    readonly cmd: string\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 道具\n */\nexport class ResItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.use_type === undefined) { throw new Error() }\n        this.useType = _json_.use_type\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n        if (_json_.effect_param1 === undefined) { throw new Error() }\n        this.effectParam1 = _json_.effect_param1\n        if (_json_.effect_param2 === undefined) { throw new Error() }\n        this.effectParam2 = _json_.effect_param2\n        if (_json_.max_stack_num === undefined) { throw new Error() }\n        this.maxStackNum = _json_.max_stack_num\n    }\n\n    readonly id: number\n    readonly name: string\n    readonly quality: res.QualityType\n    readonly qualitySub: number\n    readonly useType: res.ItemUseType\n    readonly effectId: res.ItemEffectType\n    readonly effectParam1: number\n    readonly effectParam2: number\n    readonly maxStackNum: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 卡牌\n */\nexport class ResWeapon {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n    }\n\n    readonly id: number\n    readonly name: string\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResWhiteList {\n\n    constructor(_json_: any) {\n        if (_json_.openid === undefined) { throw new Error() }\n        this.openid = _json_.openid\n        if (_json_.password === undefined) { throw new Error() }\n        this.password = _json_.password\n        if (_json_.status === undefined) { throw new Error() }\n        this.status = _json_.status\n        if (_json_.privilege === undefined) { throw new Error() }\n        this.privilege = _json_.privilege\n    }\n\n    /**\n     * 第一行默认是主键\n     */\n    readonly openid: string\n    /**\n     * 密码的MD5\n     */\n    readonly password: string\n    /**\n     * account  status: normal/disable\n     */\n    readonly status: number\n    /**\n     * 可以访问的内容\n     */\n    readonly privilege: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Stage {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.mainStage === undefined) { throw new Error() }\n        this.mainStage = _json_.mainStage\n        if (_json_.subStage === undefined) { throw new Error() }\n        this.subStage = _json_.subStage\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.enemyNorRate === undefined) { throw new Error() }\n        this.enemyNorRate = _json_.enemyNorRate\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 关卡\n     */\n    readonly mainStage: number\n    /**\n     * 阶段\n     */\n    readonly subStage: number\n    /**\n     * 类型0:普通敌机 100：boss\n     */\n    readonly type: number\n    /**\n     * 波次id\n     */\n    readonly enemyGroupID: string\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 属性倍率（血量，攻击，碰撞攻击）\n     */\n    readonly enemyNorRate: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Track {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.tpe === undefined) { throw new Error() }\n        this.tpe = _json_.tpe\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 类型\n     */\n    readonly tpe: number\n    /**\n     * 值(不同的轨迹类型，数据代表的信息不一样)\n     */\n    readonly value: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Unit {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.uId === undefined) { throw new Error() }\n        this.uId = _json_.uId\n        if (_json_.im === undefined) { throw new Error() }\n        this.im = _json_.im\n        if (_json_.imp === undefined) { throw new Error() }\n        this.imp = _json_.imp\n        if (_json_.am === undefined) { throw new Error() }\n        this.am = _json_.am\n        if (_json_.dam === undefined) { throw new Error() }\n        this.dam = _json_.dam\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.hpp === undefined) { throw new Error() }\n        this.hpp = _json_.hpp\n        if (_json_.col === undefined) { throw new Error() }\n        this.col = _json_.col\n        if (_json_.sco === undefined) { throw new Error() }\n        this.sco = _json_.sco\n        if (_json_.hc === undefined) { throw new Error() }\n        this.hc = _json_.hc\n        if (_json_.hs === undefined) { throw new Error() }\n        this.hs = _json_.hs\n        if (_json_.bla === undefined) { throw new Error() }\n        this.bla = _json_.bla\n        if (_json_.so === undefined) { throw new Error() }\n        this.so = _json_.so\n        if (_json_.sk === undefined) { throw new Error() }\n        this.sk = _json_.sk\n        if (_json_.act === undefined) { throw new Error() }\n        this.act = _json_.act\n        if (_json_.mix === undefined) { throw new Error() }\n        this.mix = _json_.mix\n        if (_json_.turn === undefined) { throw new Error() }\n        this.turn = _json_.turn\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * uId\n     */\n    readonly uId: number\n    /**\n     * im\n     */\n    readonly im: string\n    /**\n     * imp\n     */\n    readonly imp: string\n    /**\n     * am\n     */\n    readonly am: string\n    /**\n     * dam\n     */\n    readonly dam: string\n    /**\n     * hp\n     */\n    readonly hp: number\n    /**\n     * pos\n     */\n    readonly pos: string\n    /**\n     * hpp\n     */\n    readonly hpp: string\n    /**\n     * col\n     */\n    readonly col: string\n    /**\n     * sco\n     */\n    readonly sco: number\n    /**\n     * hc\n     */\n    readonly hc: string\n    /**\n     * hs\n     */\n    readonly hs: string\n    /**\n     * bla\n     */\n    readonly bla: string\n    /**\n     * so\n     */\n    readonly so: string\n    /**\n     * sk\n     */\n    readonly sk: string\n    /**\n     * act\n     */\n    readonly act: string\n    /**\n     * mix\n     */\n    readonly mix: string\n    /**\n     * turn\n     */\n    readonly turn: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Wave {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.planeType === undefined) { throw new Error() }\n        this.planeType = _json_.planeType\n        if (_json_.planeId === undefined) { throw new Error() }\n        this.planeId = _json_.planeId\n        if (_json_.interval === undefined) { throw new Error() }\n        this.interval = _json_.interval\n        if (_json_.offsetPos === undefined) { throw new Error() }\n        this.offsetPos = _json_.offsetPos\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.track === undefined) { throw new Error() }\n        this.track = _json_.track\n        if (_json_.trackParams === undefined) { throw new Error() }\n        this.trackParams = _json_.trackParams\n        if (_json_.rotatioSpeed === undefined) { throw new Error() }\n        this.rotatioSpeed = _json_.rotatioSpeed\n        if (_json_.FirstShootDelay === undefined) { throw new Error() }\n        this.FirstShootDelay = _json_.FirstShootDelay\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 波次 ID\n     */\n    readonly enemyGroupID: number\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 0 表示普通敌机\n     */\n    readonly planeType: number\n    /**\n     * 敌机id\n     */\n    readonly planeId: number\n    /**\n     * 生成间隔时间\n     */\n    readonly interval: number\n    /**\n     * 根据敌机数量设置偏移位置\n     */\n    readonly offsetPos: string\n    /**\n     * 生成的敌机数量\n     */\n    readonly num: number\n    /**\n     * 初始位置\n     */\n    readonly pos: string\n    /**\n     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)\n     */\n    readonly track: string\n    /**\n     * 轨迹参数\n     */\n    readonly trackParams: string\n    /**\n     * 旋转速度\n     */\n    readonly rotatioSpeed: number\n    /**\n     * 首次射击延迟\n     */\n    readonly FirstShootDelay: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n\n/**\n * GM命令表\n */\nexport class TbGM {\n    private _dataList: ResGM[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResGM\n            _v = new ResGM(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResGM[] { return this._dataList }\n\n    get(index: number): ResGM | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 武器\n */\nexport class TbWeapon {\n    private _dataMap: Map<number, ResWeapon>\n    private _dataList: ResWeapon[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResWeapon>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResWeapon\n            _v = new ResWeapon(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResWeapon> { return this._dataMap; }\n    getDataList(): ResWeapon[] { return this._dataList; }\n\n    get(key: number): ResWeapon | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 装备位升级\n */\nexport class TbEquipUpgrade {\n    private _dataList: ResEquipUpgrade[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquipUpgrade\n            _v = new ResEquipUpgrade(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResEquipUpgrade[] { return this._dataList }\n\n    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 装备表\n */\nexport class TbEquip {\n    private _dataMap: Map<number, ResEquip>\n    private _dataList: ResEquip[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEquip>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquip\n            _v = new ResEquip(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEquip> { return this._dataMap; }\n    getDataList(): ResEquip[] { return this._dataList; }\n\n    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 道具表\n */\nexport class TbItem {\n    private _dataMap: Map<number, ResItem>\n    private _dataList: ResItem[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResItem>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResItem\n            _v = new ResItem(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResItem> { return this._dataMap; }\n    getDataList(): ResItem[] { return this._dataList; }\n\n    get(key: number): ResItem | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: GlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new GlobalAttr(_json_[0])\n    }\n\n    getData(): GlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n    /**\n     * 体力上限值\n     */\n    get  MaxEnergy(): number { return this._data.MaxEnergy; }\n    /**\n     * 体力恢复的间隔时间（秒）\n     */\n    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }\n    /**\n     * 体力恢复的值\n     */\n    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\nexport class TbBoss {\n    private _dataMap: Map<number, Boss>\n    private _dataList: Boss[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Boss>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Boss\n            _v = new Boss(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Boss> { return this._dataMap; }\n    getDataList(): Boss[] { return this._dataList; }\n\n    get(key: number): Boss | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbBullet {\n    private _dataMap: Map<number, Bullet>\n    private _dataList: Bullet[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Bullet>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Bullet\n            _v = new Bullet(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Bullet> { return this._dataMap; }\n    getDataList(): Bullet[] { return this._dataList; }\n\n    get(key: number): Bullet | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbChapter {\n    private _dataMap: Map<number, Chapter>\n    private _dataList: Chapter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Chapter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Chapter\n            _v = new Chapter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Chapter> { return this._dataMap; }\n    getDataList(): Chapter[] { return this._dataList; }\n\n    get(key: number): Chapter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbEnemy {\n    private _dataMap: Map<number, Enemy>\n    private _dataList: Enemy[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Enemy>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Enemy\n            _v = new Enemy(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Enemy> { return this._dataMap; }\n    getDataList(): Enemy[] { return this._dataList; }\n\n    get(key: number): Enemy | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbEnemyUI {\n    private _dataMap: Map<number, EnemyUI>\n    private _dataList: EnemyUI[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, EnemyUI>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: EnemyUI\n            _v = new EnemyUI(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, EnemyUI> { return this._dataMap; }\n    getDataList(): EnemyUI[] { return this._dataList; }\n\n    get(key: number): EnemyUI | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGameMap {\n    private _dataMap: Map<number, GameMap>\n    private _dataList: GameMap[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, GameMap>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GameMap\n            _v = new GameMap(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, GameMap> { return this._dataMap; }\n    getDataList(): GameMap[] { return this._dataList; }\n\n    get(key: number): GameMap | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGameMode {\n    private _dataMap: Map<number, GameMode>\n    private _dataList: GameMode[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, GameMode>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GameMode\n            _v = new GameMode(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.ID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, GameMode> { return this._dataMap; }\n    getDataList(): GameMode[] { return this._dataList; }\n\n    get(key: number): GameMode | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbLevel {\n    private _dataMap: Map<number, Level>\n    private _dataList: Level[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Level>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Level\n            _v = new Level(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Level> { return this._dataMap; }\n    getDataList(): Level[] { return this._dataList; }\n\n    get(key: number): Level | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbLevelGroup {\n    private _dataMap: Map<number, LevelGroup>\n    private _dataList: LevelGroup[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, LevelGroup>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: LevelGroup\n            _v = new LevelGroup(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, LevelGroup> { return this._dataMap; }\n    getDataList(): LevelGroup[] { return this._dataList; }\n\n    get(key: number): LevelGroup | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbMainPlane {\n    private _dataMap: Map<number, MainPlane>\n    private _dataList: MainPlane[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, MainPlane>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: MainPlane\n            _v = new MainPlane(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, MainPlane> { return this._dataMap; }\n    getDataList(): MainPlane[] { return this._dataList; }\n\n    get(key: number): MainPlane | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbMainPlaneLv {\n    private _dataMap: Map<number, MainPlaneLv>\n    private _dataList: MainPlaneLv[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, MainPlaneLv>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: MainPlaneLv\n            _v = new MainPlaneLv(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, MainPlaneLv> { return this._dataMap; }\n    getDataList(): MainPlaneLv[] { return this._dataList; }\n\n    get(key: number): MainPlaneLv | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbStage {\n    private _dataMap: Map<number, Stage>\n    private _dataList: Stage[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Stage>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Stage\n            _v = new Stage(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Stage> { return this._dataMap; }\n    getDataList(): Stage[] { return this._dataList; }\n\n    get(key: number): Stage | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbTrack {\n    private _dataMap: Map<number, Track>\n    private _dataList: Track[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Track>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Track\n            _v = new Track(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Track> { return this._dataMap; }\n    getDataList(): Track[] { return this._dataList; }\n\n    get(key: number): Track | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbUnit {\n    private _dataMap: Map<number, Unit>\n    private _dataList: Unit[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Unit>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Unit\n            _v = new Unit(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Unit> { return this._dataMap; }\n    getDataList(): Unit[] { return this._dataList; }\n\n    get(key: number): Unit | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbWave {\n    private _dataMap: Map<number, Wave>\n    private _dataList: Wave[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Wave>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Wave\n            _v = new Wave(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Wave> { return this._dataMap; }\n    getDataList(): Wave[] { return this._dataList; }\n\n    get(key: number): Wave | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbGM: TbGM\n    /**\n     * GM命令表\n     */\n    get TbGM(): TbGM  { return this._TbGM;}\n    private _TbWeapon: TbWeapon\n    /**\n     * 武器\n     */\n    get TbWeapon(): TbWeapon  { return this._TbWeapon;}\n    private _TbEquipUpgrade: TbEquipUpgrade\n    /**\n     * 装备位升级\n     */\n    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}\n    private _TbEquip: TbEquip\n    /**\n     * 装备表\n     */\n    get TbEquip(): TbEquip  { return this._TbEquip;}\n    private _TbItem: TbItem\n    /**\n     * 道具表\n     */\n    get TbItem(): TbItem  { return this._TbItem;}\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n    private _TbBoss: TbBoss\n    get TbBoss(): TbBoss  { return this._TbBoss;}\n    private _TbBullet: TbBullet\n    get TbBullet(): TbBullet  { return this._TbBullet;}\n    private _TbChapter: TbChapter\n    get TbChapter(): TbChapter  { return this._TbChapter;}\n    private _TbEnemy: TbEnemy\n    get TbEnemy(): TbEnemy  { return this._TbEnemy;}\n    private _TbEnemyUI: TbEnemyUI\n    get TbEnemyUI(): TbEnemyUI  { return this._TbEnemyUI;}\n    private _TbGameMap: TbGameMap\n    get TbGameMap(): TbGameMap  { return this._TbGameMap;}\n    private _TbGameMode: TbGameMode\n    get TbGameMode(): TbGameMode  { return this._TbGameMode;}\n    private _TbLevel: TbLevel\n    get TbLevel(): TbLevel  { return this._TbLevel;}\n    private _TbLevelGroup: TbLevelGroup\n    get TbLevelGroup(): TbLevelGroup  { return this._TbLevelGroup;}\n    private _TbMainPlane: TbMainPlane\n    get TbMainPlane(): TbMainPlane  { return this._TbMainPlane;}\n    private _TbMainPlaneLv: TbMainPlaneLv\n    get TbMainPlaneLv(): TbMainPlaneLv  { return this._TbMainPlaneLv;}\n    private _TbStage: TbStage\n    get TbStage(): TbStage  { return this._TbStage;}\n    private _TbTrack: TbTrack\n    get TbTrack(): TbTrack  { return this._TbTrack;}\n    private _TbUnit: TbUnit\n    get TbUnit(): TbUnit  { return this._TbUnit;}\n    private _TbWave: TbWave\n    get TbWave(): TbWave  { return this._TbWave;}\n\n    constructor(loader: JsonLoader) {\n        this._TbGM = new TbGM(loader('tbgm'))\n        this._TbWeapon = new TbWeapon(loader('tbweapon'))\n        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))\n        this._TbEquip = new TbEquip(loader('tbequip'))\n        this._TbItem = new TbItem(loader('tbitem'))\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n        this._TbBoss = new TbBoss(loader('tbboss'))\n        this._TbBullet = new TbBullet(loader('tbbullet'))\n        this._TbChapter = new TbChapter(loader('tbchapter'))\n        this._TbEnemy = new TbEnemy(loader('tbenemy'))\n        this._TbEnemyUI = new TbEnemyUI(loader('tbenemyui'))\n        this._TbGameMap = new TbGameMap(loader('tbgamemap'))\n        this._TbGameMode = new TbGameMode(loader('tbgamemode'))\n        this._TbLevel = new TbLevel(loader('tblevel'))\n        this._TbLevelGroup = new TbLevelGroup(loader('tblevelgroup'))\n        this._TbMainPlane = new TbMainPlane(loader('tbmainplane'))\n        this._TbMainPlaneLv = new TbMainPlaneLv(loader('tbmainplanelv'))\n        this._TbStage = new TbStage(loader('tbstage'))\n        this._TbTrack = new TbTrack(loader('tbtrack'))\n        this._TbUnit = new TbUnit(loader('tbunit'))\n        this._TbWave = new TbWave(loader('tbwave'))\n\n        this._TbGM.resolve(this)\n        this._TbWeapon.resolve(this)\n        this._TbEquipUpgrade.resolve(this)\n        this._TbEquip.resolve(this)\n        this._TbItem.resolve(this)\n        this._TbGlobalAttr.resolve(this)\n        this._TbBoss.resolve(this)\n        this._TbBullet.resolve(this)\n        this._TbChapter.resolve(this)\n        this._TbEnemy.resolve(this)\n        this._TbEnemyUI.resolve(this)\n        this._TbGameMap.resolve(this)\n        this._TbGameMode.resolve(this)\n        this._TbLevel.resolve(this)\n        this._TbLevelGroup.resolve(this)\n        this._TbMainPlane.resolve(this)\n        this._TbMainPlaneLv.resolve(this)\n        this._TbStage.resolve(this)\n        this._TbTrack.resolve(this)\n        this._TbUnit.resolve(this)\n        this._TbWave.resolve(this)\n    }\n}\n\n"]}