{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts"], "names": ["LevelDataTerrain", "LevelDataElem", "LevelDataWave", "LevelDataEvent", "LevelDataLayer", "LevelDataBackgroundLayer", "LevelData", "Vec2", "newCondition", "newTrigger", "uuid", "position", "scale", "rotation", "elemID", "name", "waveUUID", "planeID", "params", "fromJSON", "json", "wave", "Object", "assign", "conditions", "triggers", "event", "map", "condition", "trigger", "speed", "terrains", "waves", "events", "terrain", "layer", "backgrounds", "totalTime", "background<PERSON>ayer", "floorLayers", "skyLayers", "levelData"], "mappings": ";;;+GAMaA,gB,EAOAC,a,EAMAC,a,EAaAC,c,EAoBAC,c,EA2BAC,wB,EAaAC,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5FEC,MAAAA,I,OAAAA,I;;AAGNC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAJwB;;;kCAMpBT,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,eACnBU,IADmB,GACJ,EADI;AAAA,eAEnBC,QAFmB,GAEF,IAAIJ,IAAJ,EAFE;AAAA,eAGnBK,KAHmB,GAGL,IAAIL,IAAJ,EAHK;AAAA,eAInBM,QAJmB,GAIA,CAJA;AAAA;;AAAA,O;;+BAOjBZ,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAChBa,MADgB,GACC,EADD;AAAA,eAEhBH,QAFgB,GAEC,IAAIJ,IAAJ,EAFD;AAAA,eAGhBQ,IAHgB,GAGD,SAHC;AAAA;;AAAA,O;;+BAMdb,a,GAAN,MAAMA,aAAN,SAA4BD,aAA5B,CAA0C;AAAA;AAAA;AAAA,eACtCe,QADsC,GACnB,EADmB;AAAA,eAEtCC,OAFsC,GAEpB,CAFoB;AAAA,eAGtCC,MAHsC,GAG7B,EAH6B;AAAA;;AAK9B,eAARC,QAAQ,CAACC,IAAD,EAA2B;AACtC,gBAAMC,IAAI,GAAG,IAAInB,aAAJ,EAAb;AACA,cAAI,CAACkB,IAAL,EAAW,OAAOC,IAAP;AACXC,UAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACA,iBAAOC,IAAP;AACH;;AAV4C,O;;gCAapClB,c,GAAN,MAAMA,cAAN,SAA6BF,aAA7B,CAA2C;AAAA;AAAA;AAAA,eACvCuB,UADuC,GACA,EADA;AAAA,eAEvCC,QAFuC,GAEH,EAFG;AAAA;;AAI/B,eAARN,QAAQ,CAACC,IAAD,EAA4B;AAAA;;AACvC,gBAAMM,KAAK,GAAG,IAAIvB,cAAJ,EAAd;AACA,cAAI,CAACiB,IAAL,EAAW,OAAOM,KAAP;AAEXJ,UAAAA,MAAM,CAACC,MAAP,CAAcG,KAAd,EAAqBN,IAArB;AACAM,UAAAA,KAAK,CAACF,UAAN,GAAmB,qBAAAJ,IAAI,CAACI,UAAL,sCAAiBG,GAAjB,CAAsBC,SAAD,IAAoB;AACxD,mBAAO;AAAA;AAAA,8CAAaA,SAAb,CAAP;AACH,WAFkB,MAEb,EAFN;AAGAF,UAAAA,KAAK,CAACD,QAAN,GAAiB,mBAAAL,IAAI,CAACK,QAAL,oCAAeE,GAAf,CAAoBE,OAAD,IAAkB;AAClD,mBAAO;AAAA;AAAA,0CAAWA,OAAX,CAAP;AACH,WAFgB,MAEX,EAFN;AAIA,iBAAOH,KAAP;AACH;;AAjB6C,O;;gCAoBrCtB,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjB0B,KADiB,GACD,GADC;AAAA,eAEjBC,QAFiB,GAEc,EAFd;AAAA,eAGjBC,KAHiB,GAGQ,EAHR;AAAA,eAIjBC,MAJiB,GAIU,EAJV;AAAA;;AAMdV,QAAAA,MAAM,CAACH,IAAD,EAAiB;AAAA;;AAC7BE,UAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,IAApB;AAEA,eAAKW,QAAL,GAAgB,mBAAAX,IAAI,CAACW,QAAL,oCAAeJ,GAAf,CAAoBO,OAAD,IAC/BZ,MAAM,CAACC,MAAP,CAAc,IAAIvB,gBAAJ,EAAd,EAAsCkC,OAAtC,CADY,MACuC,EADvD;AAEA,eAAKF,KAAL,GAAa,gBAAAZ,IAAI,CAACY,KAAL,iCAAYL,GAAZ,CAAiBN,IAAD,IACzBnB,aAAa,CAACiB,QAAd,CAAuBE,IAAvB,CADS,MACwB,EADrC;AAEA,eAAKY,MAAL,GAAc,iBAAAb,IAAI,CAACa,MAAL,kCAAaN,GAAb,CAAkBD,KAAD,IAC3BvB,cAAc,CAACgB,QAAf,CAAwBO,KAAxB,CADU,MACyB,EADvC;AAEH;;AAEc,eAARP,QAAQ,CAACC,IAAD,EAA4B;AACvC,gBAAMe,KAAK,GAAG,IAAI/B,cAAJ,EAAd;AACA,cAAI,CAACgB,IAAL,EAAW,OAAOe,KAAP;AAEXA,UAAAA,KAAK,CAACZ,MAAN,CAAaH,IAAb;AAEA,iBAAOe,KAAP;AACH;;AAxBuB,O;;0CA2Bf9B,wB,GAAN,MAAMA,wBAAN,SAAuCD,cAAvC,CAAsD;AAAA;AAAA;AAAA,eAClDgC,WADkD,GAC1B,EAD0B;AAAA;;AAG1C,eAARjB,QAAQ,CAACC,IAAD,EAAsC;AACjD,gBAAMe,KAAK,GAAG,IAAI9B,wBAAJ,EAAd;AACA,cAAI,CAACe,IAAL,EAAW,OAAOe,KAAP;AAEXA,UAAAA,KAAK,CAACZ,MAAN,CAAaH,IAAb;AAEA,iBAAOe,KAAP;AACH;;AAVwD,O;;2BAahD7B,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZS,IADY,GACG,EADH;AAAA,eAEZsB,SAFY,GAEQ,EAFR;AAAA,eAGZC,eAHY,GAGgC,IAHhC;AAAA,eAIZC,WAJY,GAIoB,EAJpB;AAAA,eAKZC,SALY,GAKkB,EALlB;AAAA;;AAOJ,eAARrB,QAAQ,CAACC,IAAD,EAAuB;AAAA;;AAClC,gBAAMqB,SAAS,GAAG,IAAInC,SAAJ,EAAlB;AACA,cAAI,CAACc,IAAL,EAAW,OAAOqB,SAAP;AAEXnB,UAAAA,MAAM,CAACC,MAAP,CAAckB,SAAd,EAAyBrB,IAAzB;AACAqB,UAAAA,SAAS,CAACH,eAAV,GAA4BjC,wBAAwB,CAACc,QAAzB,CAAkCC,IAAI,CAACkB,eAAvC,CAA5B;AACAG,UAAAA,SAAS,CAACF,WAAV,GAAwB,sBAAAnB,IAAI,CAACmB,WAAL,uCAAkBZ,GAAlB,CAAuBQ,KAAD,IAC1C/B,cAAc,CAACe,QAAf,CAAwBgB,KAAxB,CADoB,MACe,EADvC;AAEAM,UAAAA,SAAS,CAACD,SAAV,GAAsB,oBAAApB,IAAI,CAACoB,SAAL,qCAAgBb,GAAhB,CAAqBQ,KAAD,IACtC/B,cAAc,CAACe,QAAf,CAAwBgB,KAAxB,CADkB,MACiB,EADvC;AAGA,iBAAOM,SAAP;AACH;;AAnBkB,O", "sourcesContent": ["import { Enum, Vec2 } from \"cc\"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的\r\nimport { LevelDataEventCondtion } from \"./condition/LevelDataEventCondtion\";\r\nimport { LevelDataEventTrigger } from \"./trigger/LevelDataEventTrigger\";\r\nimport { newCondition } from \"./condition/newCondition\";\r\nimport { newTrigger } from \"./trigger/newTrigger\";\r\n\r\nexport class LevelDataTerrain {\r\n    public uuid: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public scale: Vec2 = new Vec2();\r\n    public rotation: number = 0;\r\n}\r\n\r\nexport class LevelDataElem {\r\n    public elemID: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public name: string = \"default\";\r\n}\r\n\r\nexport class LevelDataWave extends LevelDataElem {\r\n    public waveUUID: string = \"\";\r\n    public planeID: number = 0;\r\n    public params = {};\r\n\r\n    static fromJSON(json: any): LevelDataWave {\r\n        const wave = new LevelDataWave();\r\n        if (!json) return wave;\r\n        Object.assign(wave, json);\r\n        return wave;\r\n    }\r\n}\r\n\r\nexport class LevelDataEvent extends LevelDataElem {\r\n    public conditions: LevelDataEventCondtion[] = [];\r\n    public triggers: LevelDataEventTrigger[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataEvent {\r\n        const event = new LevelDataEvent();\r\n        if (!json) return event;\r\n        \r\n        Object.assign(event, json);\r\n        event.conditions = json.conditions?.map((condition: any) => {\r\n            return newCondition(condition);\r\n        }) || [];\r\n        event.triggers = json.triggers?.map((trigger: any) => {\r\n            return newTrigger(trigger);\r\n        }) || [];\r\n        \r\n        return event;\r\n    }\r\n}\r\n\r\nexport class LevelDataLayer {\r\n    public speed: number = 200;\r\n    public terrains: LevelDataTerrain[] = [];\r\n    public waves: LevelDataWave[] = [];\r\n    public events: LevelDataEvent[] = [];\r\n\r\n    protected assign(json: any):void {\r\n        Object.assign(this, json);\r\n\r\n        this.terrains = json.terrains?.map((terrain: any) => \r\n            Object.assign(new LevelDataTerrain(), terrain)) || [];\r\n        this.waves = json.waves?.map((wave: any) => \r\n            LevelDataWave.fromJSON(wave)) || [];\r\n        this.events = json.events?.map((event: any) => \r\n            LevelDataEvent.fromJSON(event)) || [];\r\n    }\r\n\r\n    static fromJSON(json: any): LevelDataLayer {\r\n        const layer = new LevelDataLayer();\r\n        if (!json) return layer;\r\n\r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelDataBackgroundLayer extends LevelDataLayer {\r\n    public backgrounds: string[] = [];\r\n    \r\n    static fromJSON(json: any): LevelDataBackgroundLayer {\r\n        const layer = new LevelDataBackgroundLayer();\r\n        if (!json) return layer;\r\n       \r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelData {\r\n    public name: string = \"\";\r\n    public totalTime: number = 59;\r\n    public backgroundLayer: LevelDataBackgroundLayer = null;\r\n    public floorLayers: LevelDataLayer[] = [];\r\n    public skyLayers: LevelDataLayer[] = [];\r\n\r\n    static fromJSON(json: any): LevelData {\r\n        const levelData = new LevelData();\r\n        if (!json) return levelData;\r\n        \r\n        Object.assign(levelData, json);\r\n        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);\r\n        levelData.floorLayers = json.floorLayers?.map((layer: any) =>\r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        levelData.skyLayers = json.skyLayers?.map((layer: any) => \r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        \r\n        return levelData;\r\n    }\r\n}\r\n"]}