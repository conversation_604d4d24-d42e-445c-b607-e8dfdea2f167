
import {Size, v2, Vec2, view } from "cc";

class _GameConst {

    Cache: boolean = false;
    ColliderDraw: boolean = false;
    ActionFrameTime: number = 0.0333;

    // 敌人相关
    EnemyPos: Vec2 = Vec2.ZERO;
    battleConfigUrl: string = "Game/jsons/normal/chapter_";

    GameAble: boolean = true;


    // /**
    //  * 初始化视图相关数据
    //  */
    // constructor() {
    //     this.ViewSize = view.getVisibleSize();
    //     this.ViewHeight = this.ViewSize.height;
    //     this.ViewWidth = this.ViewSize.width;
    //     this.ViewCenter = v2(this.ViewWidth >> 1, this.ViewHeight >> 1);
    // }

    get ViewHeight(){
        return view.getVisibleSize().height
    }
    get ViewSize(){
        return view.getVisibleSize()
    }
    get ViewWidth(){
        return view.getVisibleSize().width
    }
    get ViewCenter(){
        return v2(this.ViewWidth/2, this.ViewHeight/2)
    }
}

export const GameConst = new _GameConst();