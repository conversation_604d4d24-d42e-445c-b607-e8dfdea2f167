{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts"], "names": ["_decorator", "Component", "Node", "assetManager", "instantiate", "Vec2", "LevelDataEvent", "LevelDataWave", "LevelEditorUtils", "LevelEditorWaveUI", "LevelEditorEventUI", "ccclass", "property", "executeInEditMode", "TerrainsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelEditorLayerUI", "terrainsNode", "dynamicNode", "wavesNode", "eventsNode", "onLoad", "getOrAddNode", "node", "initByLevelData", "data", "console", "log", "terrains", "for<PERSON>ach", "terrain", "loadAny", "uuid", "err", "prefab", "error", "terrainNode", "setPosition", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "waves", "wave", "waveUIComp", "addComponent", "events", "event", "eventUIComp", "fillLevelData", "children", "push", "_prefab", "asset", "_uuid", "z", "waveNode", "getComponent", "eventNode", "tick", "progress", "totalTime", "speed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqBC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,I,OAAAA,I;AAA8EC,MAAAA,Y,OAAAA,Y;AAAoEC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAE1MC,MAAAA,c,iBAAAA,c;AAAgCC,MAAAA,a,iBAAAA,a;;AAChCC,MAAAA,gB,iBAAAA,gB;;AACsBC,MAAAA,iB,iBAAAA,iB;;AACtBC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAJH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2Cb,U;AAM3Cc,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;oCAITC,kB,WAFZP,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaK,kBAFb,SAEwCjB,SAFxC,CAEkD;AAAA;AAAA;AAAA,eACtCkB,YADsC,GACZ,IADY;AAAA,eAEtCC,WAFsC,GAEb,IAFa;AAAA,eAGtCC,SAHsC,GAGf,IAHe;AAAA,eAItCC,UAJsC,GAId,IAJc;AAAA;;AAM9CC,QAAAA,MAAM,GAAS;AACX,eAAKJ,YAAL,GAAoB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCX,gBAAzC,CAApB;AACA,eAAKM,WAAL,GAAmB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCV,eAAzC,CAAnB;AACA,eAAKM,SAAL,GAAiB;AAAA;AAAA,oDAAiBG,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCT,YAAzC,CAAjB;AACA,eAAKM,UAAL,GAAkB;AAAA;AAAA,oDAAiBE,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCR,aAAzC,CAAlB;AACH;;AAEMS,QAAAA,eAAe,CAACC,IAAD,EAA4B;AAAA;;AAC9CC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AACH;;AACD,4BAAAA,IAAI,CAACG,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChC7B,YAAAA,YAAY,CAAC8B,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,aAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,kBAAIG,WAAW,GAAGlC,WAAW,CAACgC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACC,WAAZ,CAAwBP,OAAO,CAACQ,QAAR,CAAiBC,CAAzC,EAA4CT,OAAO,CAACQ,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAJ,cAAAA,WAAW,CAACK,QAAZ,CAAqBX,OAAO,CAACY,KAAR,CAAcH,CAAnC,EAAsCT,OAAO,CAACY,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAJ,cAAAA,WAAW,CAACO,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCb,OAAO,CAACc,QAA/C;AACA,mBAAK3B,YAAL,CAAkB4B,QAAlB,CAA2BT,WAA3B;AACH,aAVD;AAWH,WAZD;AAaA,yBAAAX,IAAI,CAACqB,KAAL,yBAAYjB,OAAZ,CAAqBkB,IAAD,IAAQ;AACxB,gBAAIxB,IAAI,GAAG,IAAIvB,IAAJ,EAAX;AACA,gBAAIgD,UAAU,GAAGzB,IAAI,CAAC0B,YAAL;AAAA;AAAA,uDAAjB;AACAD,YAAAA,UAAU,CAACxB,eAAX,CAA2BuB,IAA3B;AACA,iBAAK5B,SAAL,CAAe0B,QAAf,CAAwBtB,IAAxB;AACH,WALD;AAMA,0BAAAE,IAAI,CAACyB,MAAL,0BAAarB,OAAb,CAAsBsB,KAAD,IAAS;AAC1B,gBAAI5B,IAAI,GAAG,IAAIvB,IAAJ,EAAX;AACA,gBAAIoD,WAAW,GAAG7B,IAAI,CAAC0B,YAAL;AAAA;AAAA,yDAAlB;AACAG,YAAAA,WAAW,CAAC5B,eAAZ,CAA4B2B,KAA5B;AACA,iBAAK/B,UAAL,CAAgByB,QAAhB,CAAyBtB,IAAzB;AACH,WALD;AAMH;;AAEM8B,QAAAA,aAAa,CAAC5B,IAAD,EAA4B;AAC5CC,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;AACAF,UAAAA,IAAI,CAACG,QAAL,GAAgB,EAAhB;AACA,eAAKX,YAAL,CAAkBqC,QAAlB,CAA2BzB,OAA3B,CAAoCO,WAAD,IAAiB;AAChDX,YAAAA,IAAI,CAACG,QAAL,CAAc2B,IAAd,CAAmB;AACf;AACAvB,cAAAA,IAAI,EAAEI,WAAW,CAACoB,OAAZ,CAAoBC,KAApB,CAA0BC,KAFjB;AAGfpB,cAAAA,QAAQ,EAAE,IAAInC,IAAJ,CAASiC,WAAW,CAACE,QAAZ,CAAqBC,CAA9B,EAAiCH,WAAW,CAACE,QAAZ,CAAqBE,CAAtD,CAHK;AAIfE,cAAAA,KAAK,EAAE,IAAIvC,IAAJ,CAASiC,WAAW,CAACM,KAAZ,CAAkBH,CAA3B,EAA8BH,WAAW,CAACM,KAAZ,CAAkBF,CAAhD,CAJQ;AAKfI,cAAAA,QAAQ,EAAER,WAAW,CAACQ,QAAZ,CAAqBe;AALhB,aAAnB;AAOH,WARD;AASAlC,UAAAA,IAAI,CAACqB,KAAL,GAAa,EAAb;AACA,eAAK3B,SAAL,CAAemC,QAAf,CAAwBzB,OAAxB,CAAiC+B,QAAD,IAAc;AAC1C,gBAAIb,IAAI,GAAG;AAAA;AAAA,iDAAX;AACA,gBAAIC,UAAU,GAAGY,QAAQ,CAACC,YAAT;AAAA;AAAA,uDAAjB;AACAb,YAAAA,UAAU,CAACK,aAAX,CAAyBN,IAAzB;AACAtB,YAAAA,IAAI,CAACqB,KAAL,CAAWS,IAAX,CAAgBR,IAAhB;AACH,WALD;AAMAtB,UAAAA,IAAI,CAACyB,MAAL,GAAc,EAAd;AACA,eAAK9B,UAAL,CAAgBkC,QAAhB,CAAyBzB,OAAzB,CAAkCiC,SAAD,IAAe;AAC5C,gBAAIX,KAAK,GAAG;AAAA;AAAA,mDAAZ;AACA,gBAAIC,WAAW,GAAGU,SAAS,CAACD,YAAV;AAAA;AAAA,yDAAlB;AACAT,YAAAA,WAAW,CAACC,aAAZ,CAA0BF,KAA1B;AACA1B,YAAAA,IAAI,CAACyB,MAAL,CAAYK,IAAZ,CAAiBJ,KAAjB;AACH,WALD;AAMH;;AAEMY,QAAAA,IAAI,CAACC,QAAD,EAAmBC,SAAnB,EAAqCC,KAArC,EAAwD;AAC/D,eAAK3C,IAAL,CAAUc,WAAV,CAAsB,CAAtB,EAAyB,CAAE2B,QAAF,GAAaC,SAAb,GAAyBC,KAAlD,EAAyD,CAAzD;AACH;;AA3E6C,O", "sourcesContent": ["import { _decorator, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LevelDataEvent, LevelDataLayer, LevelDataWave } from '../../scripts/leveldata/leveldata';\r\nimport { LevelEditorUtils } from './utils';\r\nimport { LevelEditorWaveParam, LevelEditorWaveUI } from './LevelEditorWaveUI';\r\nimport { LevelEditorEventUI } from './LevelEditorEventUI';\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelEditorLayerUI')\r\n@executeInEditMode()\r\nexport class LevelEditorLayerUI extends Component {\r\n    private terrainsNode: Node|null = null;\r\n    private dynamicNode: Node|null = null;\r\n    private wavesNode: Node|null = null;\r\n    private eventsNode: Node|null = null;\r\n\r\n    onLoad(): void {\r\n        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);\r\n        this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);\r\n        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer):void {\r\n        console.log(\"LevelEditorLayerUI initByLevelData\")\r\n        if (!data) {\r\n            return;\r\n        }\r\n        data.terrains?.forEach((terrain) => {\r\n            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load terrain prefab err\", err);\r\n                    return\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode.addChild(terrainNode);                \r\n            });\r\n        });\r\n        data.waves?.forEach((wave)=>{\r\n            var node = new Node();\r\n            var waveUIComp = node.addComponent(LevelEditorWaveUI);\r\n            waveUIComp.initByLevelData(wave);\r\n            this.wavesNode.addChild(node);\r\n        })\r\n        data.events?.forEach((event)=>{\r\n            var node = new Node();\r\n            var eventUIComp = node.addComponent(LevelEditorEventUI);\r\n            eventUIComp.initByLevelData(event);\r\n            this.eventsNode.addChild(node);\r\n        })\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataLayer):void {\r\n        console.log(\"LevelEditorLayerUI fillLevelData\")\r\n        data.terrains = []\r\n        this.terrainsNode.children.forEach((terrainNode) => {\r\n            data.terrains.push({\r\n                // @ts-ignore\r\n                uuid: terrainNode._prefab.asset._uuid,\r\n                position: new Vec2(terrainNode.position.x, terrainNode.position.y),\r\n                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),\r\n                rotation: terrainNode.rotation.z\r\n            })\r\n        })\r\n        data.waves = []\r\n        this.wavesNode.children.forEach((waveNode) => {\r\n            var wave = new LevelDataWave()\r\n            var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);\r\n            waveUIComp.fillLevelData(wave)\r\n            data.waves.push(wave)\r\n        })\r\n        data.events = []\r\n        this.eventsNode.children.forEach((eventNode) => {\r\n            var event = new LevelDataEvent()\r\n            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);\r\n            eventUIComp.fillLevelData(event)\r\n            data.events.push(event)\r\n        })\r\n    }\r\n\r\n    public tick(progress: number, totalTime:number, speed:number):void {\r\n        this.node.setPosition(0, - progress * totalTime * speed, 0);\r\n    }\r\n}"]}