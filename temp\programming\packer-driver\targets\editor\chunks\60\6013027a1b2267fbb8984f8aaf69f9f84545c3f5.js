System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, _dec, _class, _crd, ccclass, property, EnemyEffectComp;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2ae2bcku2RJDLtcBScIvvNC", "EnemyEffectComp", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'tween', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyEffectComp = (_dec = ccclass("EnemyEffectComp"), _dec(_class = class EnemyEffectComp extends Component {
        constructor(...args) {
          super(...args);
          this._buffNodePool = [];
          // 冰冻效果节点池
          this._buffAnimNodePool = [];
          // 火焰效果节点池
          this._buffEffectMap = new Map();
          // Buff 效果映射
          this._buffFrameAnimPool = [];
        }

        // 治疗效果节点池

        /**
         * 初始化组件
         */
        init() {
          this.removeAllBuffEffect();
        }
        /**
         * 移除所有 Buff 效果
         */


        removeAllBuffEffect() {// this._buffEffectMap.forEach((effects, buffType) => {
          //     if (effects) {
          //         for (const effect of effects) {
          //             switch (buffType) {
          //                 case GameEnum.EnemyBuff.Ice:
          //                     effect.active = false;
          //                     this._buffNodePool.push(effect);
          //                     break;
          //                 case GameEnum.EnemyBuff.Fire:
          //                     const fireComp = effect.getComponent(ImageSequence);
          //                     if (fireComp) fireComp.stop();
          //                     effect.active = false;
          //                     this._buffAnimNodePool.push(effect);
          //                     break;
          //                 case GameEnum.EnemyBuff.Treat:
          //                     const treatComp = effect.getComponent(PfFrameAnim);
          //                     if (treatComp) treatComp.stop();
          //                     effect.active = false;
          //                     this._buffFrameAnimPool.push(effect);
          //                     break;
          //             }
          //         }
          //     }
          // });
          // this._buffEffectMap.clear();
        }
        /**
         * 移除指定 Buff 效果
         * @param {number} buffType Buff 类型
         */


        removeBuff(buffType) {// const effects = this._buffEffectMap.get(buffType);
          // if (effects) {
          //     for (const effect of effects) {
          //         switch (buffType) {
          //             case GameEnum.EnemyBuff.Fire:
          //                 const fireComp = effect.getComponent(ImageSequence);
          //                 if (fireComp) fireComp.stop();
          //                 effect.active = false;
          //                 this._buffAnimNodePool.push(effect);
          //                 break;
          //             case GameEnum.EnemyBuff.Ice:
          //                 HurtEffectManager.me.playAHurtEffect('icebreak', this.node);
          //                 tween(effect)
          //                     .to(5 / 30, { opacity: 0 })
          //                     .call(() => {
          //                         effect.active = false;
          //                         this._buffNodePool.push(effect);
          //                     })
          //                     .start();
          //                 break;
          //             case GameEnum.EnemyBuff.Treat:
          //                 const treatComp = effect.getComponent(PfFrameAnim);
          //                 if (treatComp) treatComp.stop();
          //                 effect.active = false;
          //                 this._buffFrameAnimPool.push(effect);
          //                 break;
          //         }
          //     }
          //     this._buffEffectMap.delete(buffType);
          //     if (buffType === GameEnum.EnemyBuff.Ice) {
          //         // GameIns.audioManager.playEffect('b_ice_over');
          //     }
          // }
        }
        /**
         * 添加 Buff 效果
         * @param {number} buffType Buff 类型
         * @param {Array} params Buff 参数
         */


        addBuff(buffType, params) {// if (!this._buffEffectMap.get(buffType)) {
          //     try {
          //         switch (buffType) {
          //             case GameEnum.EnemyBuff.Ice:
          //                 GameIns.audioManager.playEffect('b_ice_atk');
          //                 const iceEffects = [];
          //                 for (const param of params) {
          //                     let iceNode = this._buffNodePool.pop();
          //                     if (!iceNode) {
          //                         iceNode = new Node();
          //                         this.node.addChild(iceNode);
          //                         iceNode.addComponent(Sprite);
          //                         iceNode.zIndex = 10;
          //                     }
          //                     iceNode.active = true;
          //                     iceNode.opacity = 255;
          //                     const sprite = iceNode.getComponent(Sprite);
          //                     EnemyManager.EnemyMgr.setPlaneFrame(sprite, `ice_${param[0]}`);
          //                     iceNode.position = v2(param[1], param[2]);
          //                     iceNode.angle = param[3];
          //                     iceNode.scale = param[4];
          //                     iceEffects.push(iceNode);
          //                 }
          //                 this._buffEffectMap.set(buffType, iceEffects);
          //                 break;
          //             case GameEnum.EnemyBuff.Fire:
          //                 const fireEffects = [];
          //                 const scale = params ? params[0][0] : 0.5;
          //                 let fireNode = this._buffAnimNodePool.pop();
          //                 if (!fireNode) {
          //                     fireNode = instantiate(GameConst.hurtEffect);
          //                     const fireComp = fireNode.getComponent(ImageSequence);
          //                     const frames = [];
          //                     for (let i = 0; i <= 8; i++) {
          //                         frames[i] = GameIns.loadManager.getImage(
          //                             `burn_${i}`,
          //                             EnemyManager.EnemyMgr.enemyAtlas.name.split('.')[0]
          //                         );
          //                     }
          //                     fireComp.setData(frames);
          //                 }
          //                 fireNode.parent = this.node;
          //                 fireNode.position = v2(0, 0);
          //                 fireNode.scale = scale;
          //                 fireNode.active = true;
          //                 fireNode.getComponent(ImageSequence).play(0);
          //                 fireEffects.push(fireNode);
          //                 this._buffEffectMap.set(buffType, fireEffects);
          //                 break;
          //             case GameEnum.EnemyBuff.Treat:
          //                 const treatEffects = [];
          //                 let treatNode = this._buffFrameAnimPool.pop();
          //                 if (!treatNode) {
          //                     treatNode = instantiate(GameConst.GConst.frameAnim);
          //                     this.node.addChild(treatNode, 1);
          //                     treatNode.getComponent(PfFrameAnim).init(
          //                         EnemyManager.EnemyMgr.enemyAtlas,
          //                         'a_doctor_',
          //                         15,
          //                         GameConst.ActionFrameTime
          //                     );
          //                 }
          //                 treatNode.active = true;
          //                 treatNode.position = v2(0, 0);
          //                 treatNode.getComponent(PfFrameAnim).reset();
          //                 treatEffects.push(treatNode);
          //                 this._buffEffectMap.set(buffType, treatEffects);
          //                 break;
          //         }
          //     } catch (error) {
          //         Tools.log('enemy effect error:', buffType, error);
          //     }
          // }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6013027a1b2267fbb8984f8aaf69f9f84545c3f5.js.map