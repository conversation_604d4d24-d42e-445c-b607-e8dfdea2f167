{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts"], "names": ["_decorator", "SpriteAtlas", "tween", "ImageSequence", "MyApp", "ccclass", "UIAnimMethods", "fromTo", "from", "to", "frameToTime", "doubleBTNAnim", "buttonNode", "atlasName", "atlas", "resMgr", "loadAsync", "effectLight", "frames", "i", "frameName", "frame", "getSpriteFrame", "push", "effectNode", "getChildByName", "getComponent", "setData", "scale", "call", "node", "active", "play", "delay", "union", "repeatF<PERSON><PERSON>", "start"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AAC3BC,MAAAA,a;;AAEEC,MAAAA,K,iBAAAA,K;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcL,U;;yBAGCM,a,WADpBD,OAAO,CAAC,eAAD,C,2BAAR,MACqBC,aADrB,CACmC;AACF;;AAE7B;AACJ;AACA;AACA;AACA;AACA;AACiB,eAANC,MAAM,CAACC,IAAD,EAAOC,EAAP,EAAW;AACpB,iBAAOA,EAAE,GAAGD,IAAL,GAAY,CAAZ,GAAgB,CAACC,EAAE,GAAGD,IAAN,IAAc,KAAKE,WAA1C;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACuB,cAAbC,aAAa,CAACC,UAAD,EAAaC,SAAb,EAAwB;AACvC,gBAAMC,KAAK,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,SAAvB,EAAiCZ,WAAjC,CAApB;;AACA,cAAIa,KAAJ,EAAW;AACP,gBAAIG,WAAW,GAAG,IAAlB;AACA,kBAAMC,MAAM,GAAG,EAAf;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,oBAAMC,SAAS,GAAI,aAAYD,CAAE,EAAjC;AACA,oBAAME,KAAK,GAAGP,KAAK,CAACQ,cAAN,CAAqBF,SAArB,CAAd;;AACA,kBAAIC,KAAJ,EAAW;AACPH,gBAAAA,MAAM,CAACK,IAAP,CAAYF,KAAZ;AACH;AACJ;;AAED,kBAAMG,UAAU,GAAGZ,UAAU,CAACa,cAAX,CAA0B,cAA1B,CAAnB;;AACA,gBAAID,UAAJ,EAAgB;AACZP,cAAAA,WAAW,GAAGO,UAAU,CAACE,YAAX;AAAA;AAAA,iDAAd;;AACA,kBAAIT,WAAJ,EAAiB;AACbA,gBAAAA,WAAW,CAACU,OAAZ,CAAoBT,MAApB,EAA4B,EAA5B;AACH;AACJ;;AAEDhB,YAAAA,KAAK,CAACU,UAAD,CAAL,CACKH,EADL,CACQH,aAAa,CAACC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CADR,EACoC;AAAEqB,cAAAA,KAAK,EAAE;AAAT,aADpC,EAEKnB,EAFL,CAEQH,aAAa,CAACC,MAAd,CAAqB,CAArB,EAAwB,CAAxB,CAFR,EAEoC;AAAEqB,cAAAA,KAAK,EAAE;AAAT,aAFpC,EAGKC,IAHL,CAGU,YAAY;AACd,kBAAIZ,WAAJ,EAAiB;AACbA,gBAAAA,WAAW,CAACa,IAAZ,CAAiBC,MAAjB,GAA0B,IAA1B;AACA,sBAAMd,WAAW,CAACe,IAAZ,CAAiB,CAAjB,CAAN;AACAf,gBAAAA,WAAW,CAACa,IAAZ,CAAiBC,MAAjB,GAA0B,KAA1B;AACH;AACJ,aATL,EAUKtB,EAVL,CAUQH,aAAa,CAACC,MAAd,CAAqB,CAArB,EAAwB,EAAxB,CAVR,EAUqC;AAAEqB,cAAAA,KAAK,EAAE;AAAT,aAVrC,EAWKnB,EAXL,CAWQH,aAAa,CAACC,MAAd,CAAqB,EAArB,EAAyB,EAAzB,CAXR,EAWsC;AAAEqB,cAAAA,KAAK,EAAE;AAAT,aAXtC,EAYKK,KAZL,CAYW,GAZX,EAaKC,KAbL,GAcKC,aAdL,GAeKC,KAfL;AAgBH;AACJ;;AAzD8B,O,UACxB1B,W,GAAc,IAAI,E", "sourcesContent": ["import { _decorator, SpriteAtlas, tween } from 'cc';\r\nimport ImageSequence from './ImageSequence';\r\nimport { GameIns } from '../../GameIns';\r\nimport { MyApp } from '../../../MyApp';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\n@ccclass('UIAnimMethods')\r\nexport default class UIAnimMethods {\r\n    static frameToTime = 1 / 30; // 帧数转换为时间\r\n\r\n    /**\r\n     * 根据帧数计算时间\r\n     * @param {number} from 起始帧\r\n     * @param {number} to 结束帧\r\n     * @returns {number} 时间（秒）\r\n     */\r\n    static fromTo(from, to) {\r\n        return to < from ? 0 : (to - from) * this.frameToTime;\r\n    }\r\n\r\n    /**\r\n     * 双按钮动画\r\n     * @param {Node} buttonNode 按钮节点\r\n     * @param {string} atlasName 图集名称\r\n     * @returns {Promise<void>}\r\n     */\r\n    async doubleBTNAnim(buttonNode, atlasName) {\r\n        const atlas = await MyApp.resMgr.loadAsync(atlasName,SpriteAtlas);\r\n        if (atlas) {\r\n            let effectLight = null;\r\n            const frames = [];\r\n            for (let i = 0; i < 8; i++) {\r\n                const frameName = `effectRun_${i}`;\r\n                const frame = atlas.getSpriteFrame(frameName);\r\n                if (frame) {\r\n                    frames.push(frame);\r\n                }\r\n            }\r\n\r\n            const effectNode = buttonNode.getChildByName('effect_light');\r\n            if (effectNode) {\r\n                effectLight = effectNode.getComponent(ImageSequence);\r\n                if (effectLight) {\r\n                    effectLight.setData(frames, 15);\r\n                }\r\n            }\r\n\r\n            tween(buttonNode)\r\n                .to(UIAnimMethods.fromTo(1, 4), { scale: 1.15 })\r\n                .to(UIAnimMethods.fromTo(4, 7), { scale: 0.9 })\r\n                .call(async () => {\r\n                    if (effectLight) {\r\n                        effectLight.node.active = true;\r\n                        await effectLight.play(1);\r\n                        effectLight.node.active = false;\r\n                    }\r\n                })\r\n                .to(UIAnimMethods.fromTo(7, 11), { scale: 1.05 })\r\n                .to(UIAnimMethods.fromTo(11, 15), { scale: 1 })\r\n                .delay(0.8)\r\n                .union()\r\n                .repeatForever()\r\n                .start();\r\n        }\r\n    }\r\n}"]}