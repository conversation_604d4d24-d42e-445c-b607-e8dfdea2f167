{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts"], "names": ["_GameConst", "v2", "Vec2", "view", "<PERSON><PERSON>", "ColliderDraw", "ActionFrameTime", "EnemyPos", "ZERO", "battleConfigUrl", "GameAble", "ViewHeight", "getVisibleSize", "height", "ViewSize", "ViewWidth", "width", "ViewCenter", "GameConst"], "mappings": ";;;kFAGMA,U;;;;;;;AAFQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;;;;;;;;AAElBH,MAAAA,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eAEbI,KAFa,GAEI,KAFJ;AAAA,eAGbC,YAHa,GAGW,KAHX;AAAA,eAIbC,eAJa,GAIa,MAJb;AAMb;AANa,eAObC,QAPa,GAOIL,IAAI,CAACM,IAPT;AAAA,eAQbC,eARa,GAQa,4BARb;AAAA,eAUbC,QAVa,GAUO,IAVP;AAAA;;AAab;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEc,YAAVC,UAAU,GAAE;AACZ,iBAAOR,IAAI,CAACS,cAAL,GAAsBC,MAA7B;AACH;;AACW,YAARC,QAAQ,GAAE;AACV,iBAAOX,IAAI,CAACS,cAAL,EAAP;AACH;;AACY,YAATG,SAAS,GAAE;AACX,iBAAOZ,IAAI,CAACS,cAAL,GAAsBI,KAA7B;AACH;;AACa,YAAVC,UAAU,GAAE;AACZ,iBAAOhB,EAAE,CAAC,KAAKc,SAAL,GAAe,CAAhB,EAAmB,KAAKJ,UAAL,GAAgB,CAAnC,CAAT;AACH;;AAlCY,O;;2BAqCJO,S,GAAY,IAAIlB,UAAJ,E", "sourcesContent": ["\r\nimport {Size, v2, Vec2, view } from \"cc\";\r\n\r\nclass _GameConst {\r\n\r\n    Cache: boolean = false;\r\n    ColliderDraw: boolean = false;\r\n    ActionFrameTime: number = 0.0333;\r\n\r\n    // 敌人相关\r\n    EnemyPos: Vec2 = Vec2.ZERO;\r\n    battleConfigUrl: string = \"Game/jsons/normal/chapter_\";\r\n\r\n    GameAble: boolean = true;\r\n\r\n\r\n    // /**\r\n    //  * 初始化视图相关数据\r\n    //  */\r\n    // constructor() {\r\n    //     this.ViewSize = view.getVisibleSize();\r\n    //     this.ViewHeight = this.ViewSize.height;\r\n    //     this.ViewWidth = this.ViewSize.width;\r\n    //     this.ViewCenter = v2(this.ViewWidth >> 1, this.ViewHeight >> 1);\r\n    // }\r\n\r\n    get ViewHeight(){\r\n        return view.getVisibleSize().height\r\n    }\r\n    get ViewSize(){\r\n        return view.getVisibleSize()\r\n    }\r\n    get ViewWidth(){\r\n        return view.getVisibleSize().width\r\n    }\r\n    get ViewCenter(){\r\n        return v2(this.ViewWidth/2, this.ViewHeight/2)\r\n    }\r\n}\r\n\r\nexport const GameConst = new _GameConst();"]}