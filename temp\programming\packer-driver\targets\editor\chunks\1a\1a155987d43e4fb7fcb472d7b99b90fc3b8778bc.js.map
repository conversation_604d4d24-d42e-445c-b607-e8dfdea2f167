{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/main/PlaneShowUI.ts"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "PlaneShowUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;6BAGjBM,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACJ,KAAD,C,UAERI,QAAQ,CAACJ,KAAD,C,UAERI,QAAQ,CAACJ,KAAD,C,2BAPb,MACaK,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAShB,eAANC,MAAM,GAAW;AAAE,iBAAO,qBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACnC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CAExB;;AAEW,cAANC,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AAzBmC,O;;;;;iBAGjB,I;;;;;;;iBAEC,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Label } from 'cc';\r\nimport { BaseUI, UILayer, UIOpt } from '../UIMgr';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PlaneShowUI')\r\nexport class PlaneShowUI extends BaseUI {\r\n\r\n    @property(Label)\r\n    planeName: Label = null;\r\n    @property(Label)\r\n    planePower: Label = null;\r\n    @property(Label)\r\n    planeType: Label = null;\r\n\r\n    public static getUrl(): string { return \"ui/main/PlaneShowUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        \r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n\r\n}\r\n"]}