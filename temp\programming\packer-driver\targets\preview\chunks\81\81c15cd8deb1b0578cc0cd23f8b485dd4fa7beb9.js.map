{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "Component", "EDITOR", "Bullet", "EmitterData", "ObjectPool", "BulletSystem", "eEmitterActionType", "ccclass", "executeInEditMode", "property", "playOnFocus", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "Emitter", "type", "displayName", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "updateInEditor", "_isActive", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "_perEmitStartTime", "_perEmitBulletQueue", "isActive", "status", "isEmitting", "statusElapsedTime", "totalElapsedTime", "start", "onCreateEmitter", "resetProperties", "update", "dt", "console", "log", "tick", "tickBullets", "tickActionRunners", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "emitterData", "changeStatus", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "i", "j", "targetTime", "push", "index", "perEmitIndex", "sort", "a", "b", "emitSingle", "processPerEmitQueue", "length", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "bulletPrefab", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "applyAction", "actType", "actValue", "Emitter_Active", "Emitter_InitialDelay", "Emitter_Prewarm", "Emitter_PrewarmDuration", "Emitter_Duration", "Emitter_ElapsedTime", "Emitter_Loop", "Emitter_LoopInterval", "Emitter_PerEmitInterval", "Emitter_PerEmitCount", "Emitter_PerEmitOffsetX", "Emitter_Angle", "Emitter_Count", "isInScreen", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDb,U;OACxD;AAAEc,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCd,I;;gCAEnCe,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZP,OAAO,CAAC,SAAD,C,UAOHE,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEhB,MAAP;AAAeiB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRP,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBC,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,gBATZR,iB,UACAE,W,gCAFD,MAGaI,OAHb,SAG6Bd,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAUnC;AAVmC,eAW5BiB,cAX4B,GAWD,IAXC;AAWO;AAXP,eAY5BC,SAZ4B,GAYN,KAZM;AAYO;AAZP,eAa5BC,MAb4B,GAaT,IAbS;AAaO;AAbP,eAe5BC,YAf4B,GAeJ,GAfI;AAeO;AAfP,eAgB5BC,eAhB4B,GAgBD,GAhBC;AAgBO;AAhBP,eAkB5BC,YAlB4B,GAkBJ,GAlBI;AAkBO;AAlBP,eAmB5BC,YAnB4B,GAmBJ,GAnBI;AAmBO;AAnBP,eAoB5BC,SApB4B,GAoBP,GApBO;AAoBO;AApBP,eAqB5BC,YArB4B,GAqBJ,GArBI;AAqBO;AArBP,eAuB5BC,YAvB4B,GAuBJ,CAvBI;AAuBO;AAvBP,eAwB5BC,eAxB4B,GAwBD,GAxBC;AAwBO;AAxBP,eAyB5BC,cAzB4B,GAyBF,GAzBE;AAyBO;AAzBP,eA2B5BC,KA3B4B,GA2BX,CAAC,EA3BU;AA2BO;AA3BP,eA4B5BC,KA5B4B,GA4BX,CA5BW;AA4BO;AA5BP,eA6B5BC,GA7B4B,GA6BX,EA7BW;AA6BO;AA7BP,eA8B5BC,MA9B4B,GA8BV,GA9BU;AA8BO;AA9BP,eAgC5BC,cAhC4B,GAgCD,KAhCC;AAgCM;AAhCN,eAiCzBC,SAjCyB,GAiCJ,KAjCI;AAAA,eAkCzBC,OAlCyB,GAkCCtB,cAAc,CAACuB,IAlChB;AAAA,eAmCzBC,kBAnCyB,GAmCI,CAnCJ;AAAA,eAoCzBC,iBApCyB,GAoCG,CApCH;AAAA,eAqCzBC,WArCyB,GAqCF,KArCE;AAAA,eAsCzBC,aAtCyB,GAsCD,CAtCC;AAwCnC;AAxCmC,eAyCzBC,iBAzCyB,GAyCG,CAzCH;AAAA,eA0CzBC,mBA1CyB,GA0C+D,EA1C/D;AAAA;;AA4CvB,YAARC,QAAQ,GAAY;AACpB,iBAAO,KAAKT,SAAZ;AACH;;AAES,YAANU,MAAM,GAAmB;AACzB,iBAAO,KAAKT,OAAZ;AACH;;AAEa,YAAVU,UAAU,GAAY;AACtB,iBAAO,KAAKN,WAAZ;AACH;;AAEoB,YAAjBO,iBAAiB,GAAW;AAC5B,iBAAO,KAAKT,kBAAZ;AACH;;AAEmB,YAAhBU,gBAAgB,GAAW;AAC3B,iBAAO,KAAKT,iBAAZ;AACH;;AAESU,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACA,eAAKf,SAAL,GAAiB,IAAjB;AACA,eAAKgB,eAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChCC,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC,KAAKrB,cAA1C;;AACA,cAAIhC,MAAM,IAAI,KAAKgC,cAAnB,EAAmC;AAC/B,iBAAKsB,IAAL,CAAUH,EAAV;AACA;AAAA;AAAA,8CAAaI,WAAb,CAAyBJ,EAAzB;AACA;AAAA;AAAA,8CAAaK,iBAAb,CAA+BL,EAA/B;AACH;AACJ;;AAEMM,QAAAA,aAAa,GAAG;AACnB,eAAKzB,cAAL,GAAsB,IAAtB;AACH;;AAEM0B,QAAAA,eAAe,GAAG;AACrB,eAAK1B,cAAL,GAAsB,IAAtB;AACA,eAAKiB,eAAL;AACH;;AAEMU,QAAAA,mBAAmB,GAAG;AACzB,eAAK3B,cAAL,GAAsB,KAAtB;;AACA,cAAI;AAAA;AAAA,4CAAa4B,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ,SA7FkC,CA+FnC;;;AACUb,QAAAA,eAAe,GAAG;AACxB,eAAKjC,cAAL,GAAsB,KAAK+C,WAAL,CAAiB/C,cAAvC;AACA,eAAKC,SAAL,GAAiB,KAAK8C,WAAL,CAAiB9C,SAAlC;AACA,eAAKC,MAAL,GAAc,KAAK6C,WAAL,CAAiB7C,MAA/B;AACA,eAAKC,YAAL,GAAoB,KAAK4C,WAAL,CAAiB5C,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAK2C,WAAL,CAAiB3C,eAAxC;AACA,eAAKC,YAAL,GAAoB,KAAK0C,WAAL,CAAiB1C,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKyC,WAAL,CAAiBzC,YAArC;AACA,eAAKC,SAAL,GAAiB,KAAKwC,WAAL,CAAiBxC,SAAlC;AACA,eAAKC,YAAL,GAAoB,KAAKuC,WAAL,CAAiBvC,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKsC,WAAL,CAAiBtC,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAKqC,WAAL,CAAiBrC,eAAxC;AACA,eAAKC,cAAL,GAAsB,KAAKoC,WAAL,CAAiBpC,cAAvC;AACA,eAAKC,KAAL,GAAa,KAAKmC,WAAL,CAAiBnC,KAA9B;AACA,eAAKC,KAAL,GAAa,KAAKkC,WAAL,CAAiBlC,KAA9B;AACA,eAAKC,GAAL,GAAW,KAAKiC,WAAL,CAAiBjC,GAA5B;AACA,eAAKC,MAAL,GAAc,KAAKgC,WAAL,CAAiBhC,MAA/B;AACH;AACD;AACJ;AACA;;;AACIiC,QAAAA,YAAY,CAACrB,MAAD,EAAyB;AACjC,eAAKT,OAAL,GAAeS,MAAf;AACA,eAAKP,kBAAL,GAA0B,CAA1B,CAFiC,CAGjC;;AACA,eAAKK,mBAAL,GAA2B,EAA3B;AACH;;AAESwB,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAK1B,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKd,YAApD;AACH;;AAES4C,QAAAA,aAAa,GAAG;AACtB,eAAK5B,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAES6B,QAAAA,YAAY,GAAG;AACrB,eAAK7B,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAK8B,sBAAL,GAHqB,CAIrB;;AACA,eAAK3B,mBAAL,GAA2B,EAA3B;AACH;;AAES4B,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,cAAI,KAAK5C,eAAL,GAAuB,CAA3B,EAA8B;AAC1B;AACA,iBAAKe,mBAAL,GAA2B,EAA3B;AACA,iBAAKD,iBAAL,GAAyB,KAAKJ,kBAA9B,CAH0B,CAK1B;;AACA,iBAAK,IAAImC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1C,KAAzB,EAAgC0C,CAAC,EAAjC,EAAqC;AACjC,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/C,YAAzB,EAAuC+C,CAAC,EAAxC,EAA4C;AACxC,oBAAMC,UAAU,GAAG,KAAKjC,iBAAL,GAA0B,KAAKd,eAAL,GAAuB8C,CAApE;;AACA,qBAAK/B,mBAAL,CAAyBiC,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEJ,CADmB;AAE1BK,kBAAAA,YAAY,EAAEJ,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ,aAfyB,CAiB1B;;;AACA,iBAAKhC,mBAAL,CAAyBoC,IAAzB,CAA8B,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACL,UAAF,GAAeM,CAAC,CAACN,UAAzD;AACH,WAnBD,MAoBK;AACD;AACA,iBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAK1C,KAAzB,EAAgC0C,EAAC,EAAjC,EAAqC;AACjC,mBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAK/C,YAAzB,EAAuC+C,EAAC,EAAxC,EAA4C;AACxC,qBAAKQ,UAAL,CAAgBT,EAAhB,EAAmBC,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESS,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKxC,mBAAL,CAAyByC,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAMC,UAAU,GAAG,KAAK1C,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKL,kBAAL,IAA2B+C,UAAU,CAACV,UAA1C,EAAsD;AAClD;AACA,mBAAKhC,mBAAL,CAAyB2C,KAAzB;;AACA,mBAAKJ,UAAL,CAAgBG,UAAU,CAACR,KAA3B,EAAkCQ,UAAU,CAACP,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESS,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKhB,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESU,QAAAA,UAAU,CAACL,KAAD,EAAeC,YAAf,EAAqC;AACrDxB,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA,cAAMiC,SAAS,GAAG,KAAKC,iBAAL,CAAuBZ,KAAvB,CAAlB;AACA,cAAMa,QAAQ,GAAG,KAAKC,gBAAL,CAAsBd,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKc,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACZ,KAAD,EAA0C;AACvD;AACA,cAAMgB,WAAW,GAAG,KAAK9D,KAAL,GAAa,CAAb,GAAkB,KAAKC,GAAL,IAAY,KAAKD,KAAL,GAAa,CAAzB,CAAD,GAAgC8C,KAAhC,GAAwC,KAAK7C,GAAL,GAAW,CAApE,GAAwE,CAA5F;AACA,cAAM8D,MAAM,GAAGlF,gBAAgB,CAAC,KAAKkB,KAAL,GAAa+D,WAAd,CAA/B;AACA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACd,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,cAAMjD,cAAc,GAAI,KAAKF,YAAL,GAAoB,CAApB,GAAyB,KAAKE,cAAL,IAAuB,KAAKF,YAAL,GAAoB,CAA3C,CAAD,GAAkDmD,YAAlD,GAAiE,KAAKjD,cAAL,GAAsB,CAA/G,GAAmH,CAA3I;;AACA,cAAI,KAAKI,MAAL,IAAe,CAAnB,EAAsB;AAClB,mBAAO;AAAE8D,cAAAA,CAAC,EAAElE,cAAL;AAAqBqE,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBZ,KAAvB,CAAlB;AACA,iBAAO;AACHkB,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAK9D,MAAnB,GAA4BJ,cAD5B;AAEHqE,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKjE;AAFnB,WAAP;AAIH;;AAED2D,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAKU,YAAV,EAAwB;AACpB9C,YAAAA,OAAO,CAAC+C,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,cAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAazC,YAAhC,EAA8C,KAAKsC,YAAnD,CAAnB;;AACA,cAAI,CAACE,UAAL,EAAiB;AACbhD,YAAAA,OAAO,CAACkD,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,cAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACTnD,YAAAA,OAAO,CAACkD,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIzG,MAAJ,EAAY;AACRoG,YAAAA,UAAU,CAACM,IAAX,GAAkB7F,OAAO,CAAC8F,mBAA1B;AACH,WAtBiF,CAwBlF;;;AACA,cAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAV,UAAAA,UAAU,CAACW,gBAAX,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4BV,MAA5B,EAhCkF,CAkClF;;AACAA,UAAAA,MAAM,CAACW,KAAP,CAAaC,UAAb,GAA0BxG,gBAAgB,CAACmF,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAU,UAAAA,MAAM,CAACW,KAAP,CAAaG,KAAb,IAAsB,KAAK9F,SAA3B,CApCkF,CAqClF;;AAEA,iBAAO6E,UAAP;AACH;;AAEMkB,QAAAA,WAAW,CAACC,OAAD,EAA8BC,QAA9B,EAAgD;AAC9D,kBAAQD,OAAR;AACI,iBAAK;AAAA;AAAA,0DAAmBE,cAAxB;AACI,mBAAKxF,SAAL,GAAiBuF,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBE,oBAAxB;AACI,mBAAKvG,YAAL,GAAoBqG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBG,eAAxB;AACI,mBAAK1G,SAAL,GAAiBuG,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBI,uBAAxB;AACI,mBAAKxG,eAAL,GAAuBoG,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBK,gBAAxB;AACI,mBAAKxG,YAAL,GAAoBmG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBM,mBAAxB;AACI,mBAAK1F,kBAAL,GAA0BoF,QAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBO,YAAxB;AACI,mBAAK7G,MAAL,GAAcsG,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAtC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBQ,oBAAxB;AACI,mBAAKxG,YAAL,GAAoBgG,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBS,uBAAxB;AACI,mBAAKvG,eAAL,GAAuB8F,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBU,oBAAxB;AACI,mBAAKzG,YAAL,GAAoB+F,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBW,sBAAxB;AACI,mBAAKxG,cAAL,GAAsB6F,QAAtB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBY,aAAxB;AACI,mBAAKxG,KAAL,GAAa4F,QAAb;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBa,aAAxB;AACI,mBAAKxG,KAAL,GAAa2F,QAAb;AACA;AACJ;;AACA;AAAS;AAzCb;AA2CH;AAED;AACJ;AACA;;;AACcc,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMhF,QAAAA,IAAI,CAACiF,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKtG,SAAV,EAAqB;AACjB;AACH;;AAED,eAAKG,kBAAL,IAA2BmG,SAA3B;AACA,eAAKlG,iBAAL,IAA0BkG,SAA1B;;AAEA,kBAAQ,KAAKrG,OAAb;AAEI,iBAAKtB,cAAc,CAACuB,IAApB;AACI,mBAAKqG,gBAAL;AACA;;AACJ,iBAAK5H,cAAc,CAAC6H,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAK9H,cAAc,CAAC+H,QAApB;AACI,mBAAKC,oBAAL,CAA0BL,SAA1B;AACA;;AACJ,iBAAK3H,cAAc,CAACiI,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKlI,cAAc,CAACmI,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKpG,kBAAL,IAA2B,KAAKjB,YAApC,EAAkD;AAC9C,iBAAK6C,YAAL,CAAkBpD,cAAc,CAAC6H,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKzH,SAAV,EACI,KAAK+C,YAAL,CAAkBpD,cAAc,CAAC+H,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKvG,kBAAL,IAA2B,KAAKhB,eAApC,EAAqD;AACjD,mBAAK4C,YAAL,CAAkBpD,cAAc,CAAC+H,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAACL,SAAD,EAAoB;AAC9C,cAAI,KAAKnG,kBAAL,GAA0B,KAAKf,YAAnC,EAAiD;AAC7C,iBAAK8C,YAAL;AACA,gBAAI,KAAKjD,MAAT,EACI,KAAK8C,YAAL,CAAkBpD,cAAc,CAACiI,cAAjC,EADJ,KAGI,KAAK7E,YAAL,CAAkBpD,cAAc,CAACmI,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAKzG,WAAV,EAAuB;AACnB,iBAAK4B,aAAL;AACH,WAFD,MAGK,IAAI,KAAK5B,WAAL,IAAoB,KAAKF,kBAAL,IAA2B,KAAKG,aAAxD,EAAuE;AACxE;AACA,iBAAK8C,OAAL;AACA,iBAAKpB,gBAAL;AACH,WAlB6C,CAoB9C;;;AACA,eAAKgB,mBAAL;AACH;;AAES6D,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAK1G,kBAAL,IAA2B,KAAKZ,YAApC,EAAkD;AAC9C,iBAAKwC,YAAL,CAAkBpD,cAAc,CAAC+H,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AApakC,O,UAE5BrC,mB,GAA6B,U;;;;;iBAGb,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/EmitterData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { eEmitterActionType } from '../data/EventActionData';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property({type: EmitterData, displayName: \"Emitter Data\"})\r\n    emitterData: EmitterData = null;\r\n\r\n    // 以下属性重新定义作为可修改的属性\r\n    public isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n    public isPreWarm : boolean = false;       // 是否预热\r\n    public isLoop : boolean = true;           // 是否循环\r\n\r\n    public initialDelay : number = 0.0;       // 初始延迟\r\n    public preWarmDuration : number = 0.0;    // 预热持续时长\r\n\r\n    public emitDuration : number = 1.0;       // 发射器持续时间\r\n    public emitInterval : number = 1.0;       // 发射间隔\r\n    public emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)\r\n    public loopInterval : number = 0.0;       // 循环间隔\r\n\r\n    public perEmitCount : number = 1;         // 单次发射数量\r\n    public perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔\r\n    public perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移\r\n\r\n    public angle : number = -90;              // 发射角度: -90朝下\r\n    public count : number = 1;                // 发射条数(弹道数量)\r\n    public arc   : number = 60;               // 发射范围(弧度范围)\r\n    public radius : number = 1.0;             // 发射半径\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n    protected _isActive: boolean = false;\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitStartTime: number = 0;\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n\r\n    get isActive(): boolean {\r\n        return this._isActive;\r\n    }\r\n\r\n    get status(): eEmitterStatus {\r\n        return this._status;\r\n    }\r\n\r\n    get isEmitting(): boolean {\r\n        return this._isEmitting;\r\n    }\r\n\r\n    get statusElapsedTime(): number {\r\n        return this._statusElapsedTime;\r\n    }\r\n\r\n    get totalElapsedTime(): number {\r\n        return this._totalElapsedTime;\r\n    }\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n        this._isActive = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        console.log(\"lost focus in editor \", this.updateInEditor);\r\n        if (EDITOR && this.updateInEditor) {\r\n            this.tick(dt);\r\n            BulletSystem.tickBullets(dt);\r\n            BulletSystem.tickActionRunners(dt);\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        this.isOnlyInScreen = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm = this.emitterData.isPreWarm;\r\n        this.isLoop = this.emitterData.isLoop;\r\n        this.initialDelay = this.emitterData.initialDelay;\r\n        this.preWarmDuration = this.emitterData.preWarmDuration;\r\n        this.emitDuration = this.emitterData.emitDuration;\r\n        this.emitInterval = this.emitterData.emitInterval;\r\n        this.emitPower = this.emitterData.emitPower;\r\n        this.loopInterval = this.emitterData.loopInterval;\r\n        this.perEmitCount = this.emitterData.perEmitCount;\r\n        this.perEmitInterval = this.emitterData.perEmitInterval;\r\n        this.perEmitOffsetX = this.emitterData.perEmitOffsetX;\r\n        this.angle = this.emitterData.angle;\r\n        this.count = this.emitterData.count;\r\n        this.arc = this.emitterData.arc;\r\n        this.radius = this.emitterData.radius;\r\n    }\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.perEmitInterval > 0) {\r\n            // Clear any existing queue\r\n            this._perEmitBulletQueue = [];\r\n            this._perEmitStartTime = this._statusElapsedTime;\r\n\r\n            // Queue all bullets with their target emission times\r\n            for (let i = 0; i < this.count; i++) {\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    const targetTime = this._perEmitStartTime + (this.perEmitInterval * j);\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n\r\n            // Sort by target time to ensure proper order\r\n            this._perEmitBulletQueue.sort((a, b) => a.targetTime - b.targetTime);\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count; i++) {\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        console.log(\"emit a bullet\");\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;\r\n        const radian = degreesToRadians(this.angle + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.perEmitCount > 1 ? (this.perEmitOffsetX / (this.perEmitCount - 1)) * perEmitIndex - this.perEmitOffsetX / 2 : 0);\r\n        if (this.radius <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius + perEmitOffsetX,\r\n            y: direction.y * this.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitPower;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    public applyAction(actType: eEmitterActionType, actValue: number) {\r\n        switch (actType) {\r\n            case eEmitterActionType.Emitter_Active:\r\n                this._isActive = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_InitialDelay:\r\n                this.initialDelay = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Prewarm:\r\n                this.isPreWarm = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_PrewarmDuration:\r\n                this.preWarmDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Duration:\r\n                this.emitDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_ElapsedTime:\r\n                this._statusElapsedTime = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Loop:\r\n                this.isLoop = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_LoopInterval:\r\n                this.loopInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitInterval:\r\n                this.perEmitInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitCount:\r\n                this.perEmitCount = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitOffsetX:\r\n                this.perEmitOffsetX = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Angle:\r\n                this.angle = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Count:\r\n                this.count = actValue;\r\n                break;\r\n            // TODO: 补充更多的行为实现\r\n            default: break;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this._isActive) {\r\n            return;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this._statusElapsedTime > this.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}