System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v2, BaseComp, GameIns, Tools, EnemyBase, BossBase, BattleLayer, _dec, _class, _crd, ccclass, property, BaseScreen;

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyBase(extras) {
    _reporterNs.report("EnemyBase", "../plane/enemy/EnemyBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossBase(extras) {
    _reporterNs.report("BossBase", "../plane/boss/BossBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../layer/BattleLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      BaseComp = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      EnemyBase = _unresolved_5.default;
    }, function (_unresolved_6) {
      BossBase = _unresolved_6.default;
    }, function (_unresolved_7) {
      BattleLayer = _unresolved_7.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "923a73L3M9MbaXl/55HePIQ", "BaseScreen", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Tween', 'Node', 'Animation', 'Vec2', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BaseScreen = (_dec = ccclass('BaseScreen'), _dec(_class = class BaseScreen extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor(...args) {
          super(...args);
          this.m_enemy = true;
          this.m_mainEntity = null;
          this.m_bulletState = {
            attack: 0,
            through: false,
            cirt: 0,
            atkChallenge: 0
          };
          this.m_config = void 0;
          this.m_count = 0;
        }

        /**
         * 开始射击
         * @param count 射击次数
         */
        async toFire(count) {
          this.m_count = count;
          await this.fire();
        }

        async fire() {}
        /**
         * 设置数据
         * @param config 配置数据
         * @param enemy 敌人实体
         */


        setData(config, enemy) {
          this.m_config = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.getConfig(config);
          this.m_enemy = enemy;
          this.m_count = 0;
        }
        /**
         * 设置子弹状态
         * @param bulletState 子弹状态
         * @param mainEntity 主实体
         */


        setBulletState(bulletState, mainEntity) {
          this.m_mainEntity = mainEntity || this.entity;

          for (const key in bulletState) {
            this.m_bulletState[key] = bulletState[key];
          }
        }
        /**
         * 获取子弹暴击率
         * @returns 暴击率
         */


        getBulletCirt() {
          return this.m_bulletState.cirt ? this.m_bulletState.cirt[0] : 0;
        }
        /**
         * 获取子弹攻击力
         * @returns 攻击力
         */


        getBulletAttack() {
          return this.m_bulletState.atkChallenge || 0;
        }
        /**
         * 获取攻击点
         * @returns 攻击点的坐标
         */


        getAttackPoint() {
          if (!this.m_mainEntity) {
            return v2(0, 0);
          }

          const scenePos = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).sceneManager.getScenePos(this.m_mainEntity);
          let position = this.m_entity.node.position;

          if (this.m_mainEntity.getFireBulletAngle && this.m_mainEntity.getFireBulletAngle() !== 0) {
            position = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getPositionByAngle(this.m_entity.node.position, this.m_mainEntity.getFireBulletAngle());
          }

          return v2(position.x + scenePos.x, position.y + scenePos.y);
        }
        /**
         * 获取攻击角度
         * @returns 攻击角度
         */


        getAttackAngle() {
          let angle = this.m_entity.node.angle;

          if (this.m_entity.getAttackPointAngle) {
            angle -= this.m_entity.getAttackPointAngle();
          }

          let fireAngle = 0;

          try {
            if (this.m_mainEntity && this.m_mainEntity.getFireBulletAngle) {
              fireAngle = this.m_mainEntity.getFireBulletAngle();
            }
          } catch (error) {}

          return angle - fireAngle;
        }
        /**
         * 获取瞄准角度
         * @returns 瞄准角度
         */


        getAimAngle() {
          let angle = 0;
          const attackPoint = this.getAttackPoint();

          if (this.m_enemy) {
            const mainPlaneNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane.node;
            angle = mainPlaneNode ? (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getAngle(attackPoint, v2(mainPlaneNode.position.x, mainPlaneNode.position.y)) : 0;
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).planeManager.enemyTarget && (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).planeManager.enemyCollider) {// const enemyTarget = GameIns.planeManager.enemyTarget;
            // const enemyColliderPos =
            //     GameIns.planeManager.enemyCollider.getScreenPos();
            // angle = enemyTarget
            //     ? Tools.getAngle(attackPoint, enemyColliderPos)
            //     : 0;
          }

          return angle;
        }
        /**
         * 创建子弹
         * @returns 创建的子弹
         */


        async createBullet() {
          const bulletId = this.m_config.id;
          const bullet = await (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.getBullet(bulletId, this.m_enemy);

          if (bullet) {
            // if (GameIns.bulletManager.fireShellUINode) {
            //     bullet.node.parent = GameIns.bulletManager.fireShellUINode;
            // }
            //  else if (MainGameIns.planeManager.isShow) {
            //     const mainPlaneUI = frameWork.uiManager.getDialogByName(
            //         "MainPlaneUI"
            //     );
            //     bullet.node.parent = mainPlaneUI.fireNode;
            // } 
            // else {
            (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me.addBullet(bullet); // }

            if (this.m_enemy && (this.m_mainEntity instanceof (_crd && EnemyBase === void 0 ? (_reportPossibleCrUseOfEnemyBase({
              error: Error()
            }), EnemyBase) : EnemyBase) || this.m_mainEntity instanceof (_crd && BossBase === void 0 ? (_reportPossibleCrUseOfBossBase({
              error: Error()
            }), BossBase) : BossBase))) {
              this.m_mainEntity.addBullet(bullet);
            }
          }

          return bullet;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a9aaa221055ef92a4c110c9dedc69d3556bfc974.js.map