{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/wave/Wave.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "Vec2", "ccclass", "property", "executeInEditMode", "WaveTrack", "WaveTrackGroup", "Wave", "play", "value"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;;;;;;;;OACzD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CR,U;;2BAGpCS,S,WADZH,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACJ,SAAD,C,UAERI,QAAQ,CAACL,OAAD,C,UAERK,QAAQ,CAACL,OAAD,C,UAERK,QAAQ,CAACL,OAAD,C,2BARb,MACaO,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZJ,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACJ,SAAD,C,UAERI,QAAQ,CAACJ,SAAD,C,UAERI,QAAQ,CAACJ,SAAD,C,WAERI,QAAQ,CAAC,CAACE,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAKpBC,I,aAFZL,OAAO,CAAC,MAAD,C,WACPE,iBAAiB,E,WAEbD,QAAQ,CAACN,SAAD,C,WAORM,QAAQ,CAACJ,SAAD,C,WAERI,QAAQ,CAACL,OAAD,C,WAGRK,QAAQ,CAACJ,SAAD,C,WAERI,QAAQ,CAACJ,SAAD,C,WAERI,QAAQ,CAACL,OAAD,C,WAERK,QAAQ,CAACJ,SAAD,C,WAERI,QAAQ,CAACL,OAAD,C,WAERK,QAAQ,CAAEF,IAAF,C,WAGRE,QAAQ,CAAC,CAACG,cAAD,CAAD,C,WAGRH,QAAQ,CAAC,CAACL,OAAD,CAAD,C,+CA/Bb,MAEaS,IAFb,SAE0BP,SAF1B,CAEoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAEjB,YAAJQ,IAAI,CAACC,KAAD,EAAiB,CAC/B;;AACc,YAAJD,IAAI,GAAW;AACtB,iBAAO,KAAP;AACH;;AAN+B,O;;;;;iBASV,C;;;;;;;iBAEP,C;;;;;;;iBAGE,C;;;;;;;iBAEE,C;;;;;;;iBAEO,C;;;;;;;iBAEL,C;;;;;;;iBAEQ,C;;;;;;;iBAEL,IAAIP,IAAJ,E;;;;;;;iBAGe,E;;;;;;;iBAGJ,E", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property(CCBoolean)\r\n    public set play(value: boolean) {\r\n    }\r\n    public get play():boolean {\r\n        return false;\r\n    }\r\n\r\n    @property(CCInteger)\r\n    public enemyGroupID = 0;\r\n    @property(CCFloat)\r\n    public delay = 0;\r\n\r\n    @property(CCInteger)\r\n    public planeID = 0;\r\n    @property(CCInteger)\r\n    public planeType = 0;\r\n    @property(CCFloat)\r\n    public interval: number = 0;\r\n    @property(CCInteger)\r\n    public num: number = 0;\r\n    @property(CCFloat)\r\n    public rotateSpeed: number = 0;\r\n    @property (Vec2)\r\n    public startPos: Vec2 = new Vec2();\r\n\r\n    @property([WaveTrackGroup])\r\n    public trackGroups: WaveTrackGroup[] = [];\r\n\r\n    @property([CCFloat])\r\n    public firstShootDelay: number[] = [];\r\n}"]}