{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts"], "names": ["_decorator", "instantiate", "Node", "tween", "UITransform", "v3", "BossBase", "AttackPoint", "BossUnit", "TrackComponent", "PfFrameAnim", "GameEnum", "Tools", "GameIns", "TrackGroup", "EffectLayer", "GameConst", "ccclass", "property", "BossEntity", "_datas", "_data", "_atkPointsPool", "_attacks", "_uiNode", "_topAnimNode", "_idleName", "_units", "Map", "_colUnitsIndex", "_colUnits", "_deadUnitIds", "_atkUnits", "_atkUnitSounds", "_atkUnitSoundIds", "_formIndex", "_formNum", "_nextForm", "_prePosX", "_prePosY", "_posX", "_posY", "_trackCom", "_curTrackType", "_curTrack", "_trackTime", "_trackOffX", "_trackOffY", "_moveToX", "_moveToY", "_moveSpeed", "_bArriveDes", "_transFormMove", "_nextWayPointTime", "_nextWayPointX", "_nextWayPointY", "_nextWayPointInterval", "_nextWaySpeed", "_shootAble", "_atkActions", "_nextAttackInterval", "_nextAttackTime", "_bOrde<PERSON><PERSON><PERSON>ck", "_orderIndex", "_attackID", "_atkPointDatas", "_attackPoints", "_orderAtkArr", "_action", "_bDamageable", "_b<PERSON><PERSON>ckMove", "_bFirstWayPoint", "transformBattle", "_bRemoveable", "_shadow", "wingmanPlanes", "_cloakeAnim", "init", "datas", "length", "_initUI", "_initProperty", "_initTrack", "setFormIndex", "active", "setShadow", "shadow", "index", "_collideAtk", "collide<PERSON><PERSON><PERSON>", "_initUnits", "setAction", "BossAction", "Appear", "i", "attackActions", "push", "point", "attackPoints", "data", "bAvailable", "unitArr", "enterNextForm", "action", "Normal", "_playSkel", "setDamangeable", "_startAppearTrack", "Transform", "transformEnd", "AttackPrepare", "_checkAtkAnim", "scheduleOnce", "AttackIng", "AttackOver", "Blast", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startBattle", "_startNormalTrack", "_checkNextCollideUnits", "currentUnits", "unitsOrder", "hasAliveUnits", "unitId", "arrC<PERSON>ain", "unit", "get", "setCollideAble", "nextUnits", "updateGameLogic", "deltaTime", "isDead", "for<PERSON>ach", "wingman", "node", "angle", "enemyRotate", "_processNextWayPoint", "_updateMove", "_processNextAttack", "_udpateShoot", "to<PERSON><PERSON>", "_playDieAnim", "id", "allBossesDead", "boss", "boss<PERSON><PERSON><PERSON>", "bosses", "checkLoot", "nextBoss", "battleManager", "isGameType", "GameType", "Boss", "onDie", "unitDestroyed", "arr<PERSON><PERSON><PERSON>", "allUnitsDead", "hasActivePoints", "pointId", "atkPointId", "pointData", "atkUnitId", "splice", "getAtkUnitId", "sound<PERSON>ey", "delete", "soundId", "setBody<PERSON><PERSON>", "_checkNextForm", "unitDestroyAnimEnd", "attack", "setPropertyRate", "rates", "updateHp", "m_totalHp", "m_curHp", "propertyRate", "maxHp", "bossData", "getBossDatas", "units", "unitData", "getUnitData", "hp", "formNum", "formIndex", "getColliderAtk", "posX", "posY", "isDamageable", "removeAble", "value", "getAllColUnits", "damageable", "<PERSON><PERSON><PERSON><PERSON>", "removeChildByName", "unitsNode", "addComponent", "unitNode", "setCollideAtk", "set", "uId", "pointNode", "attackPoint", "skinIndex", "isBody", "<PERSON><PERSON><PERSON>", "addScript", "setTrackGroupStartCall", "trackGroup", "trackIndex", "trackType", "setTrackGroupOverCall", "setTrackAble", "setTrackOverCall", "setTrackLeaveCall", "setTrackStartCall", "track", "setTrackType", "loopNum", "trackIDs", "<PERSON><PERSON><PERSON><PERSON>", "speeds", "trackIntervals", "startTrack", "trackGroups", "x", "y", "type", "_playCloakeHideAnim", "_playCloakeShowAnim", "moveToPos", "speed", "transformMove", "setPosition", "pos", "arrived", "setPos", "update", "getRandomInArray", "wayPointIntervals", "random_int", "wayPointXs", "wayPointYs", "deltaX", "deltaY", "distance", "Math", "sqrt", "moveX", "moveY", "abs", "attackIntervals", "attackAction", "randomIndex", "bAtkMove", "atkActId", "initForBoss", "allAttacksOver", "isAttackOver", "hasAnimation", "anim<PERSON><PERSON>", "loop", "callback", "playSkel", "_checkNextBoss", "playDieWhiteAnim", "me", "showWhiteScreen", "ActionFrameTime", "hideSmoke", "_playFallAnim", "frameTime", "_playShakeAnim", "to", "position", "delay", "start", "_playFallShake", "_playCloakeAnim", "animNode", "gameResManager", "frameAnim", "getComponent", "enemyManager", "enemyAtlas", "setScale", "reset", "nextBossId", "createBossById", "call", "hpChange", "delta", "getHpPercent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AAChEC,MAAAA,Q;;AACAC,MAAAA,W;;AACAC,MAAAA,Q;;AACAC,MAAAA,c;;AACAC,MAAAA,W;;AACAC,MAAAA,Q;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,kBAAAA,U;;AACFC,MAAAA,W;;AACEC,MAAAA,S,kBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;yBAGTmB,U,WADpBF,OAAO,CAAC,YAAD,C,gBAAR,MACqBE,UADrB;AAAA;AAAA,gCACiD;AAAA;AAAA;AAAA,eAC7CC,MAD6C,GAC7B,EAD6B;AAAA,eAE7CC,KAF6C,GAEhC,IAFgC;AAAA,eAG7CC,cAH6C,GAGb,EAHa;AAAA,eAI7CC,QAJ6C,GAIxB,EAJwB;AAAA,eAK7CC,OAL6C,GAK7B,IAL6B;AAAA,eAM7CC,YAN6C,GAMxB,IANwB;AAAA,eAO7CC,SAP6C,GAOzB,OAPyB;AAAA,eAQ7CC,MAR6C,GAQb,IAAIC,GAAJ,EARa;AAAA,eAS7CC,cAT6C,GASpB,CAToB;AAAA,eAU7CC,SAV6C,GAUrB,EAVqB;AAAA,eAW7CC,YAX6C,GAWpB,EAXoB;AAAA,eAY7CC,SAZ6C,GAYrB,EAZqB;AAAA,eAa7CC,cAb6C,GAaP,IAAIL,GAAJ,EAbO;AAAA,eAc7CM,gBAd6C,GAcL,IAAIN,GAAJ,EAdK;AAAA,eAe7CO,UAf6C,GAexB,CAAC,CAfuB;AAAA,eAgB7CC,QAhB6C,GAgB1B,CAhB0B;AAAA,eAiB7CC,SAjB6C,GAiBxB,KAjBwB;AAAA,eAkB7CC,QAlB6C,GAkB1B,CAlB0B;AAAA,eAmB7CC,QAnB6C,GAmB1B,CAnB0B;AAAA,eAoB7CC,KApB6C,GAoB7B,CApB6B;AAAA,eAqB7CC,KArB6C,GAqB7B,CArB6B;AAAA,eAsB7CC,SAtB6C,GAsBjB,IAtBiB;AAAA,eAuB7CC,aAvB6C,GAuBrB,CAAC,CAvBoB;AAAA,eAwB7CC,SAxB6C,GAwB5B,IAxB4B;AAAA,eAyB7CC,UAzB6C,GAyBxB,CAzBwB;AAAA,eA0B7CC,UA1B6C,GA0BxB,CA1BwB;AAAA,eA2B7CC,UA3B6C,GA2BxB,CA3BwB;AAAA,eA4B7CC,QA5B6C,GA4B1B,CA5B0B;AAAA,eA6B7CC,QA7B6C,GA6B1B,CA7B0B;AAAA,eA8B7CC,UA9B6C,GA8BxB,CA9BwB;AAAA,eA+B7CC,WA/B6C,GA+BtB,KA/BsB;AAAA,eAgC7CC,cAhC6C,GAgCnB,KAhCmB;AAAA,eAiC7CC,iBAjC6C,GAiCjB,CAjCiB;AAAA,eAkC7CC,cAlC6C,GAkCpB,CAlCoB;AAAA,eAmC7CC,cAnC6C,GAmCpB,CAnCoB;AAAA,eAoC7CC,qBApC6C,GAoCb,CApCa;AAAA,eAqC7CC,aArC6C,GAqCrB,CArCqB;AAAA,eAsC7CC,UAtC6C,GAsCvB,IAtCuB;AAAA,eAuC7CC,WAvC6C,GAuCxB,EAvCwB;AAAA,eAwC7CC,mBAxC6C,GAwCf,CAxCe;AAAA,eAyC7CC,eAzC6C,GAyCnB,CAzCmB;AAAA,eA0C7CC,aA1C6C,GA0CpB,KA1CoB;AAAA,eA2C7CC,WA3C6C,GA2CvB,CA3CuB;AAAA,eA4C7CC,SA5C6C,GA4CzB,CA5CyB;AAAA,eA6C7CC,cA7C6C,GA6CrB,EA7CqB;AAAA,eA8C7CC,aA9C6C,GA8Cd,EA9Cc;AAAA,eA+C7CC,YA/C6C,GA+CpB,EA/CoB;AAAA,eAgD7CC,OAhD6C,GAgD3B,CAAC,CAhD0B;AAAA,eAiD7CC,YAjD6C,GAiDrB,KAjDqB;AAAA,eAkD7CC,YAlD6C,GAkDrB,KAlDqB;AAAA,eAmD7CC,eAnD6C,GAmDlB,KAnDkB;AAAA,eAoD7CC,eApD6C,GAoDlB,IApDkB;AAAA,eAqD7CC,YArD6C,GAqDrB,KArDqB;AAAA,eAsD7CC,OAtD6C,GAsD9B,IAtD8B;AAAA,eAuD7CC,aAvD6C,GAuDtB,EAvDsB;AAAA,eAwD7CC,WAxD6C,GAwDlB,IAxDkB;AAAA;;AA2D7C;AACJ;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,KAAD,EAAe;AACf,eAAK1D,MAAL,GAAc0D,KAAd;AACA,eAAK1C,QAAL,GAAgB,KAAKhB,MAAL,CAAY2D,MAA5B;AACA,eAAKR,eAAL,GAAuB,IAAvB;;AACA,eAAKS,OAAL;;AACA,eAAKC,aAAL;;AACA,eAAKC,UAAL;;AACA,eAAKC,YAAL,CAAkB,CAAlB;AACA,eAAKC,MAAL,GAAc,IAAd;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,MAAD,EAAc;AACnB,eAAKZ,OAAL,GAAeY,MAAf;AACH;AAED;AACJ;AACA;AACA;;;AACIH,QAAAA,YAAY,CAACI,KAAD,EAAgB;AACxB,cAAI,KAAKpD,UAAL,KAAoBoD,KAAxB,EAA+B;AAC3B,iBAAKpD,UAAL,GAAkBoD,KAAlB;AACA,iBAAKzB,aAAL,GAAqB,IAArB;AACA,iBAAKC,WAAL,GAAmB,CAAnB;AACA,iBAAK1C,KAAL,GAAa,KAAKD,MAAL,CAAY,KAAKe,UAAjB,CAAb;AACA,iBAAKT,SAAL,GAAkB,OAAM,KAAKS,UAAL,GAAkB,CAAE,EAA5C;AACA,iBAAKqD,WAAL,GAAmB,KAAKnE,KAAL,CAAWoE,aAA9B;;AAEA,gBAAIF,KAAK,KAAK,CAAd,EAAiB;AACb,mBAAKG,UAAL;;AACA,mBAAKC,SAAL,CAAe;AAAA;AAAA,wCAASC,UAAT,CAAoBC,MAAnC;AACH,aAX0B,CAY3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,iBAAK1B,YAAL,GAAoB,EAApB;;AACA,iBAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzE,KAAL,CAAW0E,aAAX,CAAyBhB,MAA7C,EAAqDe,CAAC,EAAtD,EAA0D;AACtD,mBAAK3B,YAAL,CAAkB6B,IAAlB,CAAuBF,CAAvB;AACH;;AAED,iBAAK7B,cAAL,GAAsB,EAAtB;;AACA,iBAAK,MAAMgC,KAAX,IAAoB,KAAK5E,KAAL,CAAW6E,YAA/B,EAA6C;AACzC,oBAAMC,IAAI,GAAG,CAACF,KAAK,CAACG,UAAP,EAAmBH,KAAnB,CAAb;;AACA,mBAAKhC,cAAL,CAAoB+B,IAApB,CAAyBG,IAAzB;AACH;;AAED,iBAAKxC,WAAL,GAAmB,CAAC,GAAG,KAAKtC,KAAL,CAAW0E,aAAf,CAAnB;AACA,iBAAKlE,cAAL,GAAsB,CAAtB;AACA,iBAAKC,SAAL,GAAiB,EAAjB;AACA,iBAAKC,YAAL,GAAoB,EAApB;AACA,iBAAKsE,OAAL,GAAe,EAAf;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKnE,UAAL,GAAkB,KAAKf,MAAL,CAAY2D,MAAZ,GAAqB,CAA3C,EAA8C;AAC1C,iBAAK5C,UAAL;AACA,iBAAKgD,YAAL,CAAkB,KAAKhD,UAAvB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIwD,QAAAA,SAAS,CAACY,MAAD,EAAiB;AACtB,cAAI,KAAKnC,OAAL,KAAiBmC,MAArB,EAA6B;AACzB,iBAAKnC,OAAL,GAAemC,MAAf;AAEA,gBAAIX,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAKxB,OAAb;AACI,mBAAKwB,UAAU,CAACY,MAAhB;AACI,qBAAKC,SAAL,CAAe,KAAK/E,SAApB,EAA+B,IAA/B;;AACA,qBAAKgF,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKd,UAAU,CAACC,MAAhB;AACI,qBAAKY,SAAL,CAAgB,QAAO,KAAKtE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,IAA9C;;AACA,qBAAKuE,cAAL,CAAoB,KAApB;;AACA,qBAAKC,iBAAL;;AACA;;AAEJ,mBAAKf,UAAU,CAACgB,SAAhB;AACI,qBAAKH,SAAL,CAAgB,QAAO,KAAKtE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,KAA9C,EAAqD,MAAM;AACvD,uBAAKqC,eAAL,IAAwB,KAAKqC,YAAL,EAAxB;AACH,iBAFD;;AAGA,qBAAKH,cAAL,CAAoB,KAApB;AACA;;AAEJ,mBAAKd,UAAU,CAACkB,aAAhB;AACI,qBAAKC,aAAL,MAAwB,KAAKC,YAAL,CAAkB,MAAM;AAC5C,uBAAKrB,SAAL,CAAeC,UAAU,CAACqB,SAA1B;AACH,iBAFuB,CAAxB;AAGA,qBAAKP,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKd,UAAU,CAACqB,SAAhB;AACA,mBAAKrB,UAAU,CAACsB,UAAhB;AACI,qBAAKR,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKd,UAAU,CAACuB,KAAhB;AACI,qBAAKT,cAAL,CAAoB,KAApB;AACA;;AAEJ;AACI,qBAAKA,cAAL,CAAoB,IAApB;AApCR;AAsCH;AACJ,SAzL4C,CA2L7C;;AAEA;AACJ;AACA;;;AACIU,QAAAA,gBAAgB,GAAG,CACf;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,eAAKjC,MAAL,GAAc,IAAd;;AACA,eAAKkC,iBAAL;;AACA,eAAK3B,SAAL,CAAe;AAAA;AAAA,oCAASC,UAAT,CAAoBY,MAAnC;;AACA,eAAKe,sBAAL;;AACA,eAAKH,gBAAL;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,sBAAsB,GAAY;AAC9B,gBAAMC,YAAY,GAAG,KAAKnG,KAAL,CAAWoG,UAAX,CAAsB,KAAK5F,cAA3B,CAArB;AACA,cAAI6F,aAAa,GAAG,KAApB;;AAEA,eAAK,MAAMC,MAAX,IAAqBH,YAArB,EAAmC;AAC/B,gBAAI,CAAC;AAAA;AAAA,gCAAMI,UAAN,CAAiB,KAAK7F,YAAtB,EAAoC4F,MAApC,CAAL,EAAkD;AAC9CD,cAAAA,aAAa,GAAG,IAAhB;AACA;AACH;AACJ;;AAED,cAAIA,aAAJ,EAAmB;AACf,gBAAI,KAAK5F,SAAL,CAAeiD,MAAf,KAA0B,CAA9B,EAAiC;AAC7B,mBAAK,MAAM4C,MAAX,IAAqBH,YAArB,EAAmC;AAC/B,sBAAMK,IAAI,GAAG,KAAKlG,MAAL,CAAYmG,GAAZ,CAAgBH,MAAhB,CAAb;;AACAE,gBAAAA,IAAI,CAACE,cAAL,CAAoB,IAApB;;AACA,qBAAKjG,SAAL,CAAekE,IAAf,CAAoB6B,IAApB;;AACA,qBAAKxB,OAAL,CAAaL,IAAb,CAAkB6B,IAAlB;AACH;AACJ;AACJ,WATD,MASO;AACH,iBAAKhG,cAAL;;AACA,gBAAI,KAAKA,cAAL,IAAuB,KAAKR,KAAL,CAAWoG,UAAX,CAAsB1C,MAAjD,EAAyD;AACrD,qBAAO,KAAP;AACH;;AAED,kBAAMiD,SAAS,GAAG,KAAK3G,KAAL,CAAWoG,UAAX,CAAsB,KAAK5F,cAA3B,CAAlB;AACA,iBAAKC,SAAL,GAAiB,EAAjB;AACA,iBAAKuE,OAAL,GAAe,EAAf;;AAEA,iBAAK,MAAMsB,MAAX,IAAqBK,SAArB,EAAgC;AAC5B,oBAAMH,IAAI,GAAG,KAAKlG,MAAL,CAAYmG,GAAZ,CAAgBH,MAAhB,CAAb;;AACAE,cAAAA,IAAI,CAACE,cAAL,CAAoB,IAApB;;AACA,mBAAKjG,SAAL,CAAekE,IAAf,CAAoB6B,IAApB;;AACA,mBAAKxB,OAAL,CAAaL,IAAb,CAAkB6B,IAAlB;AACH;AACJ;;AAED,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACII,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,KAAK9C,MAAL,IAAe,CAAC,KAAK+C,MAArB,IAA+B,CAAC,KAAK9F,SAAzC,EAAoD;AAChD,iBAAKsC,aAAL,CAAmByD,OAAnB,CAA4BC,OAAD,IAAa;AACpCA,cAAAA,OAAO,CAACC,IAAR,CAAaC,KAAb,IAAsB,KAAKlH,KAAL,CAAWmH,WAAX,GAAyBN,SAA/C;AACH,aAFD;AAIA,gBAAItC,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAKxB,OAAb;AACI,mBAAKwB,UAAU,CAACY,MAAhB;AACI,qBAAKiC,oBAAL,CAA0BP,SAA1B;;AACA,qBAAKQ,WAAL,CAAiBR,SAAjB;;AACA,qBAAKS,kBAAL,CAAwBT,SAAxB;;AACA;;AAEJ,mBAAKtC,UAAU,CAACC,MAAhB;AACI,qBAAK6C,WAAL,CAAiBR,SAAjB;;AACA,oBAAI,KAAK/E,WAAT,EAAsB;AAClB,uBAAKwC,SAAL,CAAeC,UAAU,CAACgB,SAA1B;AACH;;AACD;;AAEJ,mBAAKhB,UAAU,CAACgB,SAAhB;AACI,oBAAI,KAAKxD,cAAT,EAAyB;AACrB,uBAAKsF,WAAL,CAAiBR,SAAjB;AACH;;AACD;;AAEJ,mBAAKtC,UAAU,CAACkB,aAAhB;AACI,qBAAK2B,oBAAL,CAA0BP,SAA1B;;AACA,oBAAI,KAAK5D,YAAT,EAAuB;AACnB,uBAAKoE,WAAL,CAAiBR,SAAjB;AACH;;AACD;;AAEJ,mBAAKtC,UAAU,CAACqB,SAAhB;AACI,qBAAKwB,oBAAL,CAA0BP,SAA1B;;AACA,oBAAI,KAAK5D,YAAT,EAAuB;AACnB,uBAAKoE,WAAL,CAAiBR,SAAjB;AACH;;AACD,qBAAKU,YAAL,CAAkBV,SAAlB;;AACA;;AAEJ,mBAAKtC,UAAU,CAACsB,UAAhB;AACI,qBAAKuB,oBAAL,CAA0BP,SAA1B;;AACA,oBAAI,KAAK5D,YAAT,EAAuB;AACnB,uBAAKoE,WAAL,CAAiBR,SAAjB;AACH;;AACD,qBAAKvC,SAAL,CAAeC,UAAU,CAACY,MAA1B;AACA;;AAEJ,mBAAKZ,UAAU,CAACuB,KAAhB;AACI;AA5CR;;AA+CA,iBAAKxF,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1BA,cAAAA,IAAI,CAACI,eAAL,CAAqBC,SAArB;AACH,aAFD;AAGH;AACJ;AAED;AACJ;AACA;;;AACIW,QAAAA,KAAK,GAAG;AACJ,cAAI,CAAC,KAAKV,MAAV,EAAkB;AACd,iBAAKA,MAAL,GAAc,IAAd;AACA,iBAAKxC,SAAL,CAAe;AAAA;AAAA,sCAASC,UAAT,CAAoBuB,KAAnC;;AACA,iBAAK2B,YAAL;;AAEA,gBAAI,KAAKzH,KAAL,CAAW0H,EAAX,IAAiB,GAAjB,IAAwB,KAAK1H,KAAL,CAAW0H,EAAX,GAAgB,GAA5C,EAAiD;AAC7C,kBAAIC,aAAa,GAAG,IAApB;;AACA,mBAAK,MAAMC,IAAX,IAAmB;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,MAAvC,EAA+C;AAC3C,oBAAIF,IAAI,KAAK,IAAT,IAAiB,CAACA,IAAI,CAACd,MAA3B,EAAmC;AAC/Ba,kBAAAA,aAAa,GAAG,KAAhB;AACA;AACH;AACJ;;AAED,kBAAIA,aAAJ,EAAmB;AACf,qBAAKI,SAAL;AACH;AACJ,aAZD,MAYO,IAAI,KAAK/H,KAAL,CAAWgI,QAAX,CAAoBtE,MAApB,GAA6B,CAA7B,IAAkC;AAAA;AAAA,oCAAQuE,aAAR,CAAsBC,UAAtB,CAAiC;AAAA;AAAA,sCAASC,QAAT,CAAkBC,IAAnD,CAAtC,EAAgG,CACnG;AACH,aAFM,MAEA;AACH,mBAAKL,SAAL;AACH;;AAED,iBAAKM,KAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,aAAa,CAAC9B,IAAD,EAAY;AACrB,eAAK9F,YAAL,CAAkBiE,IAAlB,CAAuB6B,IAAI,CAACF,MAA5B;;AACA;AAAA;AAAA,8BAAMiC,SAAN,CAAgB,KAAK9H,SAArB,EAAgC+F,IAAhC;AAEA,cAAIgC,YAAY,GAAG,IAAnB;;AACA,eAAKlI,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1B,gBAAI,CAACA,IAAI,CAACM,MAAV,EAAkB;AACd0B,cAAAA,YAAY,GAAG,KAAf;AACH;AACJ,WAJD;;AAMA,eAAK,IAAI/D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnC,WAAL,CAAiBoB,MAArC,EAA6Ce,CAAC,EAA9C,EAAkD;AAC9C,gBAAIgE,eAAe,GAAG,KAAtB;;AACA,iBAAK,MAAMC,OAAX,IAAsB,KAAKpG,WAAL,CAAiBmC,CAAjB,EAAoBkE,UAA1C,EAAsD;AAClD,oBAAMC,SAAS,GAAG,KAAKhG,cAAL,CAAoB8F,OAApB,CAAlB;;AACA,kBAAIE,SAAS,CAAC,CAAD,CAAb,EAAkB;AACd,oBAAIpC,IAAI,CAACF,MAAL,KAAgBsC,SAAS,CAAC,CAAD,CAAT,CAAaC,SAAjC,EAA4C;AACxCD,kBAAAA,SAAS,CAAC,CAAD,CAAT,GAAe,KAAf;AACH,iBAFD,MAEO;AACHH,kBAAAA,eAAe,GAAG,IAAlB;AACH;AACJ;AACJ;;AAED,gBAAI,CAACA,eAAL,EAAsB;AAClB,mBAAKnG,WAAL,CAAiBwG,MAAjB,CAAwBrE,CAAxB,EAA2B,CAA3B;;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,eAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,aAAL,CAAmBa,MAAvC,EAA+Ce,CAAC,EAAhD,EAAoD;AAChD,gBAAI,KAAK5B,aAAL,CAAmB4B,CAAnB,EAAsBsE,YAAtB,OAAyCvC,IAAI,CAACF,MAAlD,EAA0D;AACtD,mBAAKzD,aAAL,CAAmBiG,MAAnB,CAA0BrE,CAA1B,EAA6B,CAA7B;;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,gBAAMuE,QAAQ,GAAG,KAAKpI,cAAL,CAAoB6F,GAApB,CAAwBD,IAAI,CAACF,MAA7B,CAAjB;;AACA,cAAI0C,QAAJ,EAAc;AACV,iBAAKpI,cAAL,CAAoBqI,MAApB,CAA2BzC,IAAI,CAACF,MAAhC;AACH;;AAED,gBAAM4C,OAAO,GAAG,KAAKrI,gBAAL,CAAsB4F,GAAtB,CAA0BD,IAAI,CAACF,MAA/B,CAAhB;;AACA,cAAI4C,OAAO,KAAK,IAAhB,EAAsB;AAClB,iBAAKrI,gBAAL,CAAsBoI,MAAtB,CAA6BzC,IAAI,CAACF,MAAlC,EADkB,CAElB;;AACH;;AAED,eAAK6C,WAAL,CAAiB,KAAKzI,YAAL,CAAkBgD,MAAnC;;AAEA,cAAI8E,YAAJ,EAAkB;AACd,iBAAK1H,UAAL;;AACA,gBAAI,KAAKsI,cAAL,EAAJ,EAA2B;AACvB,mBAAKpI,SAAL,GAAiB,IAAjB;AACH,aAFD,MAEO;AACH,mBAAKF,UAAL;AACA,mBAAK0G,KAAL;AACH;AACJ,WARD,MAQO;AACH,iBAAKtB,sBAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACImD,QAAAA,kBAAkB,CAAC7C,IAAD,EAAY;AAC1B,cAAI,KAAKxF,SAAT,EAAoB;AAChB,iBAAKA,SAAL,GAAiB,KAAjB;AACA,iBAAK8C,YAAL,CAAkB,KAAKhD,UAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACIsI,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAKtI,UAAL,GAAkB,KAAKf,MAAL,CAAY2D,MAArC;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,aAAa,GAAG;AACZ,eAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1E,MAAL,CAAY2D,MAAhC,EAAwCe,CAAC,EAAzC,EAA6C;AACzC,kBAAMK,IAAI,GAAG,KAAK/E,MAAL,CAAY0E,CAAZ,CAAb;;AACA,iBAAKvE,QAAL,CAAcyE,IAAd,CAAmBG,IAAI,CAACwE,MAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,KAAD,EAAkBC,QAAiB,GAAG,KAAtC,EAA6C;AACxD,gBAAMF,eAAN,CAAsBC,KAAtB;AAEA,eAAKE,SAAL,GAAiB,CAAjB;AACA,eAAKC,OAAL,GAAe,CAAf;;AAEA,cAAIH,KAAK,CAAC9F,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvE,QAAL,CAAcwD,MAAlC,EAA0Ce,CAAC,EAA3C,EAA+C;AAC3C,mBAAKvE,QAAL,CAAcuE,CAAd,KAAoB+E,KAAK,CAAC,CAAD,CAAzB;AACH;AACJ;;AAED,cAAIA,KAAK,CAAC9F,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAKS,WAAL,IAAoBqF,KAAK,CAAC,CAAD,CAAzB;AACH;;AAED,eAAKlJ,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1BA,YAAAA,IAAI,CAAC+C,eAAL,CAAqB,KAAKK,YAA1B;AACA,iBAAKF,SAAL,IAAkBlD,IAAI,CAACqD,KAAvB;AACA,iBAAKF,OAAL,IAAgBnD,IAAI,CAACqD,KAArB;AACH,WAJD;;AAMA,cAAI,KAAK7J,KAAL,CAAWgI,QAAX,CAAoBtE,MAApB,GAA6B,CAAjC,EAAoC;AAChC,iBAAK,IAAIe,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzE,KAAL,CAAWgI,QAAX,CAAoBtE,MAAxC,EAAgDe,CAAC,EAAjD,EAAqD;AACjD,oBAAMqF,QAAQ,GAAG;AAAA;AAAA,sCAAQjC,WAAR,CAAoBkC,YAApB,CAAiC,KAAK/J,KAAL,CAAWgI,QAAX,CAAoBvD,CAApB,CAAjC,CAAjB;;AACA,kBAAIqF,QAAQ,CAACpG,MAAT,GAAkB,CAAtB,EAAyB;AACrB,qBAAK,MAAM4C,MAAX,IAAqBwD,QAAQ,CAAC,CAAD,CAAR,CAAYE,KAAjC,EAAwC;AACpC,wBAAMC,QAAQ,GAAG;AAAA;AAAA,0CAAQpC,WAAR,CAAoBqC,WAApB,CAAgC5D,MAAhC,CAAjB;;AACA,sBAAI2D,QAAJ,EAAc;AACV,yBAAKP,SAAL,IAAkBO,QAAQ,CAACE,EAAT,IAAe,KAAKP,YAAL,CAAkB,CAAlB,KAAwB,CAAvC,CAAlB;AACA,yBAAKD,OAAL,IAAgBM,QAAQ,CAACE,EAAT,IAAe,KAAKP,YAAL,CAAkB,CAAlB,KAAwB,CAAvC,CAAhB;AACH;AACJ;AACJ;AACJ;AACJ;;AAED,cAAI,CAACH,QAAL,EAAe,CACX;AACA;AACH;AACJ;AAED;AACJ;AACA;;;AACe,YAAPW,OAAO,GAAW;AAClB,iBAAO,KAAKrJ,QAAZ;AACH;AAED;AACJ;AACA;;;AACiB,YAATsJ,SAAS,GAAW;AACpB,iBAAO,KAAKvJ,UAAZ;AACH;AAED;AACJ;AACA;;;AACc,YAANwI,MAAM,GAAW;AACjB,iBAAO,KAAKpJ,QAAL,CAAc,KAAKY,UAAnB,CAAP;AACH;AAED;AACJ;AACA;;;AACIwJ,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKnG,WAAZ;AACH;AAED;AACJ;AACA;;;AACY,YAAJoG,IAAI,GAAW;AACf,iBAAO,KAAKpJ,KAAZ;AACH;AAED;AACJ;AACA;;;AACY,YAAJqJ,IAAI,GAAW;AACf,iBAAO,KAAKpJ,KAAZ;AACH;AAED;AACJ;AACA;;;AACIqJ,QAAAA,YAAY,GAAY;AACpB,iBAAO,KAAKzH,YAAZ;AACH;AAED;AACJ;AACA;;;AACkB,YAAV0H,UAAU,GAAY;AACtB,iBAAO,KAAKtH,YAAZ;AACH;;AAEa,YAAVsH,UAAU,CAACC,KAAD,EAAiB;AAC3B,eAAKvH,YAAL,GAAoBuH,KAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAU;AACpB,iBAAO,KAAKnK,SAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACI4E,QAAAA,cAAc,CAACwF,UAAD,EAAsB;AAChC,eAAK7H,YAAL,GAAoB6H,UAApB;;AACA,eAAKvK,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1B,gBAAI,CAACA,IAAI,CAACM,MAAV,EAAkB;AACdN,cAAAA,IAAI,CAACqE,UAAL,GAAkBA,UAAlB;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACIlH,QAAAA,OAAO,GAAG;AACN,eAAKxD,OAAL,GAAe,IAAItB,IAAJ,CAAS,QAAT,CAAf;AACA,eAAKoI,IAAL,CAAU6D,QAAV,CAAmB,KAAK3K,OAAxB;AAEA,eAAKC,YAAL,GAAoB,IAAIvB,IAAJ,CAAS,aAAT,CAApB;AACA,eAAKoI,IAAL,CAAU6D,QAAV,CAAmB,KAAK1K,YAAxB;AACH;AAED;AACJ;AACA;;;AACIiE,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,8BAAM0G,iBAAN,CAAwB,KAAK5K,OAA7B,EAAsC,OAAtC;AAEA,gBAAM6K,SAAS,GAAG,IAAInM,IAAJ,CAAS,OAAT,CAAlB;AACAmM,UAAAA,SAAS,CAACC,YAAV,CAAuBlM,WAAvB;;AACA,eAAKoB,OAAL,CAAa2K,QAAb,CAAsBE,SAAtB;;AAEA,eAAK,MAAM1E,MAAX,IAAqB,KAAKtG,KAAL,CAAWgK,KAAhC,EAAuC;AACnC,kBAAMC,QAAQ,GAAG;AAAA;AAAA,oCAAQpC,WAAR,CAAoBqC,WAApB,CAAgC5D,MAAhC,CAAjB;;AACA,gBAAI2D,QAAJ,EAAc;AACV,oBAAMiB,QAAQ,GAAG,IAAIrM,IAAJ,EAAjB;AACAqM,cAAAA,QAAQ,CAACD,YAAT,CAAsBlM,WAAtB;AACAiM,cAAAA,SAAS,CAACF,QAAV,CAAmBI,QAAnB;AAEA,oBAAM1E,IAAI,GAAG0E,QAAQ,CAACD,YAAT;AAAA;AAAA,uCAAb;AACAzE,cAAAA,IAAI,CAAChD,IAAL,CAAUyG,QAAV,EAAoB,IAApB;AACAzD,cAAAA,IAAI,CAAC2E,aAAL,CAAmB,KAAKnL,KAAL,CAAWoE,aAA9B;AACAoC,cAAAA,IAAI,CAAC+C,eAAL,CAAqB,KAAKK,YAA1B;;AAEA,mBAAKtJ,MAAL,CAAY8K,GAAZ,CAAgBnB,QAAQ,CAACoB,GAAzB,EAA8B7E,IAA9B;AACH;AACJ;;AAED,eAAK,IAAI/B,CAAC,GAAG,KAAKxE,cAAL,CAAoByD,MAApB,GAA6B,CAA1C,EAA6Ce,CAAC,GAAG,KAAK7B,cAAL,CAAoBc,MAArE,EAA6Ee,CAAC,EAA9E,EAAkF;AAC9E,kBAAM6G,SAAS,GAAG,IAAIzM,IAAJ,CAAS,eAAe4F,CAAxB,CAAlB;AACA,iBAAKwC,IAAL,CAAU6D,QAAV,CAAmBQ,SAAnB;AACAA,YAAAA,SAAS,CAACpE,KAAV,GAAkB,CAAC,GAAnB;AAEA,kBAAMqE,WAAW,GAAGD,SAAS,CAACL,YAAV;AAAA;AAAA,2CAApB;;AACA,iBAAKhL,cAAL,CAAoB0E,IAApB,CAAyB4G,WAAzB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIpC,QAAAA,WAAW,CAACqC,SAAD,EAAoB;AAC3B,eAAKlL,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1B,gBAAIA,IAAI,CAACiF,MAAL,EAAJ,EAAmB;AACfjF,cAAAA,IAAI,CAACkF,OAAL,CAAaF,SAAb;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACI3H,QAAAA,UAAU,GAAG;AACT,eAAKxC,SAAL,GAAiB;AAAA;AAAA,8BAAMsK,SAAN,CAAgB,KAAK1E,IAArB;AAAA;AAAA,+CAAjB;;AACA,eAAK5F,SAAL,CAAeuK,sBAAf,CAAsC,CAACC,UAAD,EAAaC,UAAb,EAAyBC,SAAzB,KAAuC,CAAG,CAAhF;;AACA,eAAK1K,SAAL,CAAe2K,qBAAf,CAAsCH,UAAD,IAAgB;AACjD,gBAAI,KAAK9I,OAAL,KAAiB;AAAA;AAAA,sCAASwB,UAAT,CAAoBC,MAAzC,EAAiD;AAC7C,mBAAKnD,SAAL,CAAe4K,YAAf,CAA4B,KAA5B;;AACA,mBAAK3H,SAAL,CAAe;AAAA;AAAA,wCAASC,UAAT,CAAoBgB,SAAnC;AACH;AACJ,WALD;;AAMA,eAAKlE,SAAL,CAAe6K,gBAAf,CAAgC,MAAM,CAAG,CAAzC;;AACA,eAAK7K,SAAL,CAAe8K,iBAAf,CAAiC,MAAM,CAAG,CAA1C;;AACA,eAAK9K,SAAL,CAAe+K,iBAAf,CAAkCC,KAAD,IAAW;AACxC,iBAAKC,YAAL,CAAkBD,KAAlB;AACH,WAFD;AAGH;AAED;AACJ;AACA;;;AACI/G,QAAAA,iBAAiB,GAAG;AAChB,gBAAMuG,UAAU,GAAG;AAAA;AAAA,yCAAnB;AACAA,UAAAA,UAAU,CAACU,OAAX,GAAqB,CAArB;AACAV,UAAAA,UAAU,CAACW,QAAX,GAAsB,CAAC,KAAKxM,KAAL,CAAWyM,WAAX,CAAuB,CAAvB,CAAD,CAAtB;AACAZ,UAAAA,UAAU,CAACa,MAAX,GAAoB,CAAC,KAAK1M,KAAL,CAAWyM,WAAX,CAAuB,CAAvB,CAAD,CAApB;AACAZ,UAAAA,UAAU,CAACc,cAAX,GAA4B,CAAC,CAAD,CAA5B;;AAEA,eAAKtL,SAAL,CAAemC,IAAf,CAAoB,IAApB,EAA0B,CAACqI,UAAD,CAA1B,EAAwC,EAAxC,EAA4C,KAAK7L,KAAL,CAAWyM,WAAX,CAAuB,CAAvB,CAA5C,EAAuE,KAAKzM,KAAL,CAAWyM,WAAX,CAAuB,CAAvB,CAAvE;;AACA,eAAKpL,SAAL,CAAe4K,YAAf,CAA4B,IAA5B;;AACA,eAAK5K,SAAL,CAAeuL,UAAf;AACH;AAED;AACJ;AACA;;;AACI3G,QAAAA,iBAAiB,GAAG;AAChB,eAAK5E,SAAL,CAAemC,IAAf,CAAoB,IAApB,EAA0B,KAAKxD,KAAL,CAAW6M,WAArC,EAAkD,EAAlD,EAAsD,KAAK5F,IAAL,CAAU6F,CAAhE,EAAmE,KAAK7F,IAAL,CAAU8F,CAA7E;;AACA,eAAK1L,SAAL,CAAe4K,YAAf,CAA4B,IAA5B;;AACA,eAAK5K,SAAL,CAAeuL,UAAf;;AACA,eAAKtI,SAAL,CAAe;AAAA;AAAA,oCAASC,UAAT,CAAoBY,MAAnC;AACH;AAED;AACJ;AACA;AACA;;;AACImH,QAAAA,YAAY,CAACD,KAAD,EAAa;AACrB,cAAIA,KAAJ,EAAW;AACP,oBAAQA,KAAK,CAACW,IAAd;AACI,mBAAK,CAAL;AACA,mBAAK,CAAL;AACI,oBAAI,KAAK1L,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,uBAAKD,SAAL,CAAe4K,YAAf,CAA4B,KAA5B;;AACA,uBAAK5J,UAAL,GAAkB,KAAlB;;AACA,uBAAK5B,SAAL,CAAesG,OAAf,CAAwBP,IAAD,IAAUA,IAAI,CAACE,cAAL,CAAoB,KAApB,CAAjC;;AACA,uBAAKuG,mBAAL,CAAyB,MAAM;AAC3B,yBAAK5L,SAAL,CAAe4K,YAAf,CAA4B,IAA5B;AACH,mBAFD;AAGH;;AACD;;AAEJ;AACI,oBAAI,KAAK3K,aAAL,KAAuB,CAAvB,IAA4B,KAAKA,aAAL,KAAuB,CAAvD,EAA0D;AACtD,uBAAKe,UAAL,GAAkB,KAAlB;;AACA,uBAAKhB,SAAL,CAAe4K,YAAf,CAA4B,KAA5B;;AACA,uBAAKiB,mBAAL,CAAyB,MAAM;AAC3B,yBAAK7K,UAAL,GAAkB,IAAlB;;AACA,yBAAKhB,SAAL,CAAe4K,YAAf,CAA4B,IAA5B;;AACA,yBAAKxL,SAAL,CAAesG,OAAf,CAAwBP,IAAD,IAAUA,IAAI,CAACE,cAAL,CAAoB,IAApB,CAAjC;AACH,mBAJD;;AAKA,uBAAKtB,SAAL,CAAe,QAAf,EAAyB,IAAzB;AACH;;AAvBT;;AAyBA,iBAAK9D,aAAL,GAAqB+K,KAAK,CAACW,IAA3B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,SAAS,CAACL,CAAD,EAAYC,CAAZ,EAAuBK,KAAvB,EAAsCC,aAAsB,GAAG,KAA/D,EAAsE;AAC3E,eAAK1L,QAAL,GAAgBmL,CAAhB;AACA,eAAKlL,QAAL,GAAgBmL,CAAhB;AACA,eAAKlL,UAAL,GAAkBuL,KAAlB;AACA,eAAKtL,WAAL,GAAmB,KAAnB;AACA,eAAKC,cAAL,GAAsBsL,aAAtB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,WAAW,CAACC,GAAD,EAAmBC,OAAgB,GAAG,KAAtC,EAA6C;AACpD,eAAKrM,KAAL,GAAaoM,GAAG,CAACT,CAAjB;AACA,eAAK1L,KAAL,GAAamM,GAAG,CAACR,CAAjB;AACA,eAAKU,MAAL,CAAY,KAAKtM,KAAjB,EAAwB,KAAKC,KAA7B;AACA,eAAKU,WAAL,GAAmB0L,OAAnB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACX,CAAD,EAAYC,CAAZ,EAAuBW,MAAe,GAAG,IAAzC,EAA+C;AACjD,gBAAMD,MAAN,CAAaX,CAAb,EAAgBC,CAAhB,EAAmBW,MAAnB;AACA,eAAKvM,KAAL,GAAa2L,CAAb;AACA,eAAK1L,KAAL,GAAa2L,CAAb;AACH;AAED;AACJ;AACA;AACA;;;AACI3F,QAAAA,oBAAoB,CAACP,SAAD,EAAoB;AACpC,cAAI,KAAK/E,WAAL,IAAoB,KAAK9B,KAAL,CAAW6M,WAAX,CAAuBnJ,MAAvB,KAAkC,CAA1D,EAA6D;AACzD,iBAAK1B,iBAAL,IAA0B6E,SAA1B;;AACA,gBAAI,KAAK7E,iBAAL,GAAyB,KAAKG,qBAAlC,EAAyD;AACrD,mBAAKA,qBAAL,GAA6B;AAAA;AAAA,kCAAMwL,gBAAN,CAAuB,KAAK3N,KAAL,CAAW4N,iBAAlC,CAA7B;AACA,mBAAK5L,iBAAL,GAAyB,CAAzB;;AAEA,kBAAI,KAAKkB,eAAT,EAA0B;AACtB,qBAAKA,eAAL,GAAuB,KAAvB;AACH,eAFD,MAEO;AACH,sBAAMgB,KAAK,GAAG;AAAA;AAAA,oCAAM2J,UAAN,CAAiB,CAAjB,EAAoB,KAAK7N,KAAL,CAAW8N,UAAX,CAAsBpK,MAAtB,GAA+B,CAAnD,CAAd;AACA,qBAAKzB,cAAL,GAAsB,KAAKjC,KAAL,CAAW8N,UAAX,CAAsB5J,KAAtB,CAAtB;AACA,qBAAKhC,cAAL,GAAsB,KAAKlC,KAAL,CAAW+N,UAAX,CAAsB7J,KAAtB,CAAtB;AACA,qBAAK9B,aAAL,GAAqB;AAAA;AAAA,oCAAMuL,gBAAN,CAAuB,KAAK3N,KAAL,CAAW0M,MAAlC,CAArB;AACA,qBAAKS,SAAL,CAAe,KAAKlL,cAApB,EAAoC,KAAKC,cAAzC,EAAyD,KAAKE,aAA9D;AACH;AACJ;AACJ;AACJ;;AAGDiF,QAAAA,WAAW,CAACR,SAAD,EAAoB;AAC3B,cAAI,KAAK9D,OAAL,KAAiB;AAAA;AAAA,oCAASwB,UAAT,CAAoBC,MAArC,IAA+C,KAAKxE,KAAL,CAAW6M,WAAX,CAAuBnJ,MAAvB,GAAgC,CAAnF,EAAsF;AAClF;AACA,iBAAKrC,SAAL,CAAeuF,eAAf,CAA+BC,SAA/B;AACH,WAHD,MAGO,IAAI,CAAC,KAAK/E,WAAV,EAAuB;AAC1B;AACA,iBAAKb,QAAL,GAAgB,KAAKE,KAArB;AACA,iBAAKD,QAAL,GAAgB,KAAKE,KAArB;AAEA,kBAAM4M,MAAM,GAAG,KAAKrM,QAAL,GAAgB,KAAKR,KAApC;AACA,kBAAM8M,MAAM,GAAG,KAAKrM,QAAL,GAAgB,KAAKR,KAApC;AACA,kBAAM8M,QAAQ,GAAGC,IAAI,CAACC,IAAL,CAAUJ,MAAM,GAAGA,MAAT,GAAkBC,MAAM,GAAGA,MAArC,CAAjB;AAEA,gBAAII,KAAK,GAAG,CAAZ;AACA,gBAAIC,KAAK,GAAG,CAAZ,CAV0B,CAY1B;;AACA,gBAAIJ,QAAQ,IAAI,KAAKrM,UAArB,EAAiC;AAC7BwM,cAAAA,KAAK,GAAGL,MAAR;AACAM,cAAAA,KAAK,GAAGL,MAAR;AACH,aAHD,CAIA;AAJA,iBAKK;AACDI,cAAAA,KAAK,GAAG,KAAKxM,UAAL,GAAkBmM,MAAlB,GAA2BE,QAAnC;AACAI,cAAAA,KAAK,GAAG,KAAKzM,UAAL,GAAkBoM,MAAlB,GAA2BC,QAAnC;AACH,aArByB,CAuB1B;;;AACA,iBAAK/M,KAAL,IAAckN,KAAd;AACA,iBAAKjN,KAAL,IAAckN,KAAd;AACA,iBAAKb,MAAL,CAAY,KAAKtM,KAAjB,EAAwB,KAAKC,KAA7B,EA1B0B,CA4B1B;;AACA,iBAAKU,WAAL,GAAoBqM,IAAI,CAACI,GAAL,CAASF,KAAT,IAAkB,GAAlB,IAAyBF,IAAI,CAACI,GAAL,CAASD,KAAT,IAAkB,GAA/D;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIhH,QAAAA,kBAAkB,CAACT,SAAD,EAAoB;AAClC,cAAI,KAAKxE,UAAL,IAAmB,KAAKU,OAAL,KAAiB;AAAA;AAAA,oCAASwB,UAAT,CAAoBY,MAA5D,EAAoE;AAChE,iBAAK3C,eAAL,IAAwBqE,SAAxB;;AACA,gBAAI,KAAKrE,eAAL,GAAuB,KAAKD,mBAAhC,EAAqD;AACjD,mBAAKA,mBAAL,GAA2B;AAAA;AAAA,kCAAMoL,gBAAN,CAAuB,KAAK3N,KAAL,CAAWwO,eAAlC,CAA3B;AACA,mBAAKhM,eAAL,GAAuB,CAAvB;AAEA,kBAAIiM,YAAY,GAAG,IAAnB;;AACA,kBAAI,KAAKhM,aAAT,EAAwB;AACpB,sBAAMiM,WAAW,GAAG;AAAA;AAAA,oCAAMf,gBAAN,CAAuB,KAAK7K,YAA5B,CAApB;AACA;AAAA;AAAA,oCAAMyF,SAAN,CAAgB,KAAKzF,YAArB,EAAmC4L,WAAnC;AACAD,gBAAAA,YAAY,GAAG,KAAKnM,WAAL,CAAiBoM,WAAjB,CAAf;AACA,qBAAKhM,WAAL;;AACA,oBAAI,KAAKA,WAAL,GAAmB,KAAKJ,WAAL,CAAiBoB,MAAjB,GAA0B,CAAjD,EAAoD;AAChD,uBAAKjB,aAAL,GAAqB,KAArB;AACH;AACJ,eARD,MAQO;AACHgM,gBAAAA,YAAY,GAAG;AAAA;AAAA,oCAAMd,gBAAN,CAAuB,KAAKrL,WAA5B,CAAf;AACH;;AAED,kBAAImM,YAAJ,EAAkB;AACd,qBAAKxL,YAAL,GAAoBwL,YAAY,CAACE,QAAjC;AACA,qBAAKhM,SAAL,GAAiB8L,YAAY,CAACG,QAA9B;;AACA,qBAAK/L,aAAL,CAAmBiG,MAAnB,CAA0B,CAA1B;;AAEA,qBAAK,MAAMJ,OAAX,IAAsB+F,YAAY,CAAC9F,UAAnC,EAA+C;AAC3C,wBAAMC,SAAS,GAAG,KAAKhG,cAAL,CAAoB8F,OAApB,CAAlB;;AACA,sBAAIE,SAAS,CAAC,CAAD,CAAb,EAAkB;AACd,wBAAI2C,WAAW,GAAG,KAAKtL,cAAL,CAAoByI,OAApB,CAAlB;;AACA,wBAAI,CAAC6C,WAAL,EAAkB;AACd,4BAAMD,SAAS,GAAG,IAAIzM,IAAJ,EAAlB;AACA,2BAAKoI,IAAL,CAAU6D,QAAV,CAAmBQ,SAAnB;AACAC,sBAAAA,WAAW,GAAGD,SAAS,CAACL,YAAV;AAAA;AAAA,qDAAd;;AACA,2BAAKhL,cAAL,CAAoB0E,IAApB,CAAyB4G,WAAzB;AACH;;AACDA,oBAAAA,WAAW,CAACsD,WAAZ,CAAwBjG,SAAS,CAAC,CAAD,CAAjC,EAAsC,IAAtC;;AACA,yBAAK/F,aAAL,CAAmB8B,IAAnB,CAAwB4G,WAAxB;AACH;AACJ;;AAED,oBAAI,KAAK1I,aAAL,CAAmBa,MAAnB,GAA4B,CAAhC,EAAmC;AAC/B,uBAAKY,SAAL,CAAe;AAAA;AAAA,4CAASC,UAAT,CAAoBkB,aAAnC;AACH;AACJ;AACJ;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACsB,cAAZ8B,YAAY,CAACV,SAAD,EAAoB;AAClC,cAAI,KAAKxE,UAAT,EAAqB;AACjB,gBAAIyM,cAAc,GAAG,IAArB;;AAEA,iBAAK,MAAMvD,WAAX,IAA0B,KAAK1I,aAA/B,EAA8C;AAC1C,oBAAM0I,WAAW,CAAC3E,eAAZ,CAA4BC,SAA5B,CAAN;;AACA,kBAAI,CAAC0E,WAAW,CAACwD,YAAZ,EAAL,EAAiC;AAC7BD,gBAAAA,cAAc,GAAG,KAAjB;AACH;AACJ;;AAED,gBAAIA,cAAJ,EAAoB;AAChB,mBAAKxK,SAAL,CAAe;AAAA;AAAA,wCAASC,UAAT,CAAoBsB,UAAnC;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIH,QAAAA,aAAa,GAAY;AACrB,cAAIsJ,YAAY,GAAG,KAAnB,CADqB,CAGrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAOA,YAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI5J,QAAAA,SAAS,CAAC6J,QAAD,EAAmBC,IAAnB,EAAkCC,QAAkB,GAAG,IAAvD,EAA6D7I,MAAc,GAAG,CAAC,CAA/E,EAAkF;AACvF,eAAKhG,MAAL,CAAYyG,OAAZ,CAAoB,CAACP,IAAD,EAAOkB,EAAP,KAAc;AAC9B,gBAAIpB,MAAM,KAAK,CAAC,CAAZ,IAAiBoB,EAAE,KAAKpB,MAA5B,EAAoC;AAChCE,cAAAA,IAAI,CAAC4I,QAAL,CAAcH,QAAd,EAAwBC,IAAxB,EAA8BC,QAA9B;AACH;AACJ,WAJD;AAKH;AAED;AACJ;AACA;;;AACI1H,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKzH,KAAL,CAAWgI,QAAX,CAAoBtE,MAApB,GAA6B,CAAjC,EAAoC;AAChC,iBAAK2L,cAAL;AACH,WAFD,MAEO;AACH,iBAAKjM,YAAL,GAAoB,IAApB;AACH;AACJ;AAED;AACJ;AACA;;;AACIkM,QAAAA,gBAAgB,GAAG;AACf,eAAK3J,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,4CAAY4J,EAAZ,CAAeC,eAAf,CAA+B,IAAI;AAAA;AAAA,wCAAUC,eAA7C,EAA8D,GAA9D,EADoB,CAEpB;;AACA,iBAAKnP,MAAL,CAAYyG,OAAZ,CAAqBP,IAAD,IAAU;AAC1BA,cAAAA,IAAI,CAACkJ,SAAL;AACH,aAFD;AAGH,WAND,EAMG,KAAK;AAAA;AAAA,sCAAUD,eANlB;AAOH;AAED;AACJ;AACA;;;AACIE,QAAAA,aAAa,GAAG;AACZ,gBAAMC,SAAS,GAAG;AAAA;AAAA,sCAAUH,eAA5B,CADY,CAGZ;AACA;AACA;AACA;AAEA;AACA;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,cAAc,GAAG;AACb,gBAAMD,SAAS,GAAG;AAAA;AAAA,sCAAUH,eAA5B;AAEA3Q,UAAAA,KAAK,CAAC,KAAKqB,OAAN,CAAL,CACK2P,EADL,CACQ,IAAIF,SADZ,EACuB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WADvB,EAEK8Q,EAFL,CAEQ,IAAIF,SAFZ,EAEuB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAC,EAAN,CAAd;AAAyBkI,YAAAA,KAAK,EAAE;AAAhC,WAFvB,EAGK4I,EAHL,CAGQ,IAAIF,SAHZ,EAGuB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAHvB,EAIK8Q,EAJL,CAIQ,IAAIF,SAJZ,EAIuB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN,CAAd;AAAwBkI,YAAAA,KAAK,EAAE;AAA/B,WAJvB,EAKK4I,EALL,CAKQ,IAAIF,SALZ,EAKuB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WALvB,EAMK8Q,EANL,CAMQF,SANR,EAMmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAC,CAAN;AAAd,WANnB,EAOK8Q,EAPL,CAOQF,SAPR,EAOmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WAPnB,EAQK8Q,EARL,CAQQF,SARR,EAQmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAC,CAAL;AAAd,WARnB,EASK8Q,EATL,CASQF,SATR,EASmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,EAAD,EAAK,CAAL;AAAd,WATnB,EAUK8Q,EAVL,CAUQF,SAVR,EAUmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAVnB,EAWK8Q,EAXL,CAWQF,SAXR,EAWmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAXnB,EAYK8Q,EAZL,CAYQF,SAZR,EAYmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAZnB,EAaK8Q,EAbL,CAaQF,SAbR,EAamB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAbnB,EAcK8Q,EAdL,CAcQF,SAdR,EAcmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAdnB,EAeK8Q,EAfL,CAeQF,SAfR,EAemB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAfnB,EAgBK8Q,EAhBL,CAgBQF,SAhBR,EAgBmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAC,CAAF,EAAK,CAAC,CAAN;AAAd,WAhBnB,EAiBKgR,KAjBL,CAiBWJ,SAjBX,EAkBKE,EAlBL,CAkBQF,SAlBR,EAkBmB;AAAEG,YAAAA,QAAQ,EAAE/Q,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAd,WAlBnB,EAmBKiR,KAnBL;AAoBH;AAED;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb,gBAAMN,SAAS,GAAG;AAAA;AAAA,sCAAUH,eAA5B,CADa,CAGb;AACA;AACA;AACA;AAEA;AACH;AAED;AACJ;AACA;;;AACIU,QAAAA,eAAe,GAAG;AACd,cAAI,CAAC,KAAK5M,WAAV,EAAuB;AACnB,kBAAM6M,QAAQ,GAAGxR,WAAW,CAAC;AAAA;AAAA,oCAAQyR,cAAR,CAAuBC,SAAxB,CAA5B;AACA,iBAAKrJ,IAAL,CAAU6D,QAAV,CAAmBsF,QAAnB;AAEA,iBAAK7M,WAAL,GAAmB6M,QAAQ,CAACG,YAAT;AAAA;AAAA,2CAAnB;;AACA,iBAAKhN,WAAL,CAAiBC,IAAjB,CACI;AAAA;AAAA,oCAAQgN,YAAR,CAAqBC,UADzB,EAEI,IAFJ,EAGI,EAHJ,EAII;AAAA;AAAA,wCAAUhB,eAJd;;AAMAW,YAAAA,QAAQ,CAACrM,MAAT,GAAkB,KAAlB;AACH;;AAED,eAAKR,WAAL,CAAiB0D,IAAjB,CAAsByJ,QAAtB,CAA+B,GAA/B,EAAoC,GAApC;;AACA,eAAKnN,WAAL,CAAiB0D,IAAjB,CAAsBlD,MAAtB,GAA+B,IAA/B;;AACA,eAAKR,WAAL,CAAiBoN,KAAjB,CAAuB,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACI1D,QAAAA,mBAAmB,CAACkC,QAAkB,GAAG,IAAtB,EAA4B;AAC3C,gBAAMS,SAAS,GAAG;AAAA;AAAA,sCAAUH,eAA5B,CAD2C,CAG3C;;AACA,eAAKU,eAAL,GAJ2C,CAM3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACH;AAED;AACJ;AACA;AACA;;;AACIjD,QAAAA,mBAAmB,CAACiC,QAAkB,GAAG,IAAtB,EAA4B;AAC3C,gBAAMS,SAAS,GAAG;AAAA;AAAA,sCAAUH,eAA5B,CAD2C,CAG3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AACD;AACJ;AACA;;;AACIJ,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKrP,KAAL,CAAW0H,EAAX,KAAkB,GAAtB,EAA2B;AACvB,iBAAKtC,SAAL,CAAe,MAAf,EAAuB,KAAvB,EAA8B,MAAM;AAChC,mBAAK,MAAMwL,UAAX,IAAyB,KAAK5Q,KAAL,CAAWgI,QAApC,EAA8C;AAC1C,sBAAM8B,QAAQ,GAAG;AAAA;AAAA,wCAAQjC,WAAR,CAAoBkC,YAApB,CAAiC6G,UAAjC,EAA6C,CAA7C,CAAjB;AACA,sBAAMhJ,IAAI,GAAG;AAAA;AAAA,wCAAQC,WAAR,CAAoBgJ,cAApB,CAAmCD,UAAnC,CAAb;AACAhJ,gBAAAA,IAAI,CAAC0F,WAAL,CAAiB,KAAKrG,IAAL,CAAU8I,QAA3B,EAAqC,IAArC;AACAnI,gBAAAA,IAAI,CAAC7D,MAAL,GAAc,KAAd;AACA6D,gBAAAA,IAAI,CAAC2B,eAAL,CAAqB,KAAKK,YAA1B,EAAwC,IAAxC;AAEA9K,gBAAAA,KAAK,CAAC8I,IAAI,CAACX,IAAN,CAAL,CACK6I,EADL,CACQ,CADR,EACW;AAAEC,kBAAAA,QAAQ,EAAE/Q,EAAE,CAAC8K,QAAQ,CAAC2C,WAAT,CAAqB,CAArB,CAAD,EAA0B3C,QAAQ,CAAC2C,WAAT,CAAqB,CAArB,CAA1B;AAAd,iBADX,EAEKqE,IAFL,CAEU,MAAM;AACRlJ,kBAAAA,IAAI,CAAC0F,WAAL,CAAiB1F,IAAI,CAACX,IAAL,CAAU8I,QAA3B,EAAqC,IAArC;AACAnI,kBAAAA,IAAI,CAAC5B,WAAL;AACH,iBALL,EAMKiK,KANL;AAOH;;AAED,mBAAKhJ,IAAL,CAAUlD,MAAV,GAAmB,KAAnB;AACA,mBAAKX,YAAL,GAAoB,IAApB;AACH,aAnBD;AAoBH;AACJ;AAED;AACJ;AACA;AACA;;;AACI2N,QAAAA,QAAQ,CAACC,KAAD,EAAgB;AACpB,eAAKrH,OAAL,IAAgBqH,KAAhB;;AACA,cAAI,KAAKrH,OAAL,GAAe,CAAnB,EAAsB;AAClB,iBAAKA,OAAL,GAAe,CAAf;AACH,WAJmB,CAKpB;;AACH;AAED;AACJ;AACA;;;AACIsH,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKtH,OAAL,GAAe,KAAKD,SAA3B;AACH;;AAxmC4C,O", "sourcesContent": ["import { _decorator, Component, instantiate, Node, tween, UITransform, v3, Vec2, Vec3 } from 'cc';\r\nimport BossBase from './BossBase';\r\nimport AttackPoint from '../../base/AttackPoint';\r\nimport BossUnit from './BossUnit';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport PfFrameAnim from '../../base/PfFrameAnim';\r\nimport GameEnum from '../../../const/GameEnum';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\nimport EffectLayer from '../../layer/EffectLayer';\r\nimport { GameConst } from '../../../const/GameConst';\r\nimport EnemyEffectLayer from '../../layer/EnemyEffectLayer';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BossEntity\")\r\nexport default class BossEntity extends BossBase {\r\n    _datas: any[] = [];\r\n    _data: any = null;\r\n    _atkPointsPool: AttackPoint[] = [];\r\n    _attacks: number[] = [];\r\n    _uiNode: Node = null;\r\n    _topAnimNode: Node = null;\r\n    _idleName: string = \"idle1\";\r\n    _units: Map<number, BossUnit> = new Map();\r\n    _colUnitsIndex: number = 0;\r\n    _colUnits: BossUnit[] = [];\r\n    _deadUnitIds: number[] = [];\r\n    _atkUnits: BossUnit[] = [];\r\n    _atkUnitSounds: Map<number, string> = new Map();\r\n    _atkUnitSoundIds: Map<number, number> = new Map();\r\n    _formIndex: number = -1;\r\n    _formNum: number = 0;\r\n    _nextForm: boolean = false;\r\n    _prePosX: number = 0;\r\n    _prePosY: number = 0;\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n    _trackCom: TrackComponent = null;\r\n    _curTrackType: number = -1;\r\n    _curTrack: any = null;\r\n    _trackTime: number = 0;\r\n    _trackOffX: number = 0;\r\n    _trackOffY: number = 0;\r\n    _moveToX: number = 0;\r\n    _moveToY: number = 0;\r\n    _moveSpeed: number = 0;\r\n    _bArriveDes: boolean = false;\r\n    _transFormMove: boolean = false;\r\n    _nextWayPointTime: number = 0;\r\n    _nextWayPointX: number = 0;\r\n    _nextWayPointY: number = 0;\r\n    _nextWayPointInterval: number = 0;\r\n    _nextWaySpeed: number = 0;\r\n    _shootAble: boolean = true;\r\n    _atkActions: any[] = [];\r\n    _nextAttackInterval: number = 0;\r\n    _nextAttackTime: number = 0;\r\n    _bOrderAttack: boolean = false;\r\n    _orderIndex: number = 0;\r\n    _attackID: number = 0;\r\n    _atkPointDatas: any[] = [];\r\n    _attackPoints: AttackPoint[] = [];\r\n    _orderAtkArr: number[] = [];\r\n    _action: number = -1;\r\n    _bDamageable: boolean = false;\r\n    _bAttackMove: boolean = false;\r\n    _bFirstWayPoint: boolean = false;\r\n    transformBattle: boolean = true;\r\n    _bRemoveable: boolean = false;\r\n    _shadow: any = null;\r\n    wingmanPlanes: any[] = [];\r\n    _cloakeAnim: PfFrameAnim = null;\r\n\r\n\r\n    /**\r\n     * 初始化 Boss 数据\r\n     * @param datas Boss 数据数组\r\n     */\r\n    init(datas: any[]) {\r\n        this._datas = datas;\r\n        this._formNum = this._datas.length;\r\n        this._bFirstWayPoint = true;\r\n        this._initUI();\r\n        this._initProperty();\r\n        this._initTrack();\r\n        this.setFormIndex(0);\r\n        this.active = true;\r\n    }\r\n\r\n    /**\r\n     * 设置影子\r\n     * @param shadow 影子对象\r\n     */\r\n    setShadow(shadow: any) {\r\n        this._shadow = shadow;\r\n    }\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param index 形态索引\r\n     */\r\n    setFormIndex(index: number) {\r\n        if (this._formIndex !== index) {\r\n            this._formIndex = index;\r\n            this._bOrderAttack = true;\r\n            this._orderIndex = 0;\r\n            this._data = this._datas[this._formIndex];\r\n            this._idleName = `idle${this._formIndex + 1}`;\r\n            this._collideAtk = this._data.collideAttack;\r\n\r\n            if (index === 0) {\r\n                this._initUnits();\r\n                this.setAction(GameEnum.BossAction.Appear);\r\n            }\r\n            //  else {\r\n            //     this._units.forEach((unit) => {\r\n            //         unit.init(GameIns.bossManager.getUnitData(unit.id), this);\r\n            //         unit.setCollideAtk(this._data.collideAttack);\r\n            //         unit.setPropertyRate(this.propertyRate);\r\n            //     });\r\n            //     this.setAction(BossAction.Transform);\r\n            // }\r\n\r\n            this._orderAtkArr = [];\r\n            for (let i = 0; i < this._data.attackActions.length; i++) {\r\n                this._orderAtkArr.push(i);\r\n            }\r\n\r\n            this._atkPointDatas = [];\r\n            for (const point of this._data.attackPoints) {\r\n                const data = [point.bAvailable, point];\r\n                this._atkPointDatas.push(data);\r\n            }\r\n\r\n            this._atkActions = [...this._data.attackActions];\r\n            this._colUnitsIndex = 0;\r\n            this._colUnits = [];\r\n            this._deadUnitIds = [];\r\n            this.unitArr = [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 进入下一形态\r\n     */\r\n    enterNextForm() {\r\n        if (this._formIndex < this._datas.length - 1) {\r\n            this._formIndex++;\r\n            this.setFormIndex(this._formIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置 Boss 的行为\r\n     * @param action 行为类型\r\n     */\r\n    setAction(action: number) {\r\n        if (this._action !== action) {\r\n            this._action = action;\r\n\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._playSkel(this._idleName, true);\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._playSkel(`enter${this._formIndex + 1}`, true);\r\n                    this.setDamangeable(false);\r\n                    this._startAppearTrack();\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {\r\n                        this.transformBattle && this.transformEnd();\r\n                    });\r\n                    this.setDamangeable(false);\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this._checkAtkAnim() || this.scheduleOnce(() => {\r\n                        this.setAction(BossAction.AttackIng);\r\n                    });\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                case BossAction.AttackOver:\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    this.setDamangeable(false);\r\n                    break;\r\n\r\n                default:\r\n                    this.setDamangeable(true);\r\n            }\r\n        }\r\n    }\r\n\r\n    //     // ...前面的代码...\r\n\r\n    /**\r\n     * 检查并生成 Boss 的僚机\r\n     */\r\n    checkBossWingman() {\r\n        // if (this._data.enemyId > 0 && this._data.enemyPos.length > 0) {\r\n        //     for (let i = 0; i < this._data.enemyPos.length; i++) {\r\n        //         const wingman = EnemyManager.EnemyMgr.addPlane(this._data.enemyId);\r\n        //         wingman.setExp(0);\r\n        //         wingman.setScaleType(EnemyScale.None);\r\n        //         wingman.attack = this._data.attack;\r\n        //         wingman.node.position = this._data.enemyPos[i];\r\n\r\n        //         const trackGroup = i % 2 === 0 ? this._data.enemyTrackGroup1 : this._data.enemyTrackGroup2;\r\n        //         wingman.initTrack(trackGroup, [-1, 0, 0], wingman.node.position.x, wingman.node.position.y);\r\n        //         wingman.startBattle();\r\n        //         wingman.collideAble = false;\r\n\r\n        //         this.wingmanPlanes.push(wingman);\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this.active = true;\r\n        this._startNormalTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n        this._checkNextCollideUnits();\r\n        this.checkBossWingman();\r\n    }\r\n\r\n    /**\r\n     * 检查下一个碰撞单元\r\n     */\r\n    _checkNextCollideUnits(): boolean {\r\n        const currentUnits = this._data.unitsOrder[this._colUnitsIndex];\r\n        let hasAliveUnits = false;\r\n\r\n        for (const unitId of currentUnits) {\r\n            if (!Tools.arrContain(this._deadUnitIds, unitId)) {\r\n                hasAliveUnits = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (hasAliveUnits) {\r\n            if (this._colUnits.length === 0) {\r\n                for (const unitId of currentUnits) {\r\n                    const unit = this._units.get(unitId);\r\n                    unit.setCollideAble(true);\r\n                    this._colUnits.push(unit);\r\n                    this.unitArr.push(unit);\r\n                }\r\n            }\r\n        } else {\r\n            this._colUnitsIndex++;\r\n            if (this._colUnitsIndex >= this._data.unitsOrder.length) {\r\n                return false;\r\n            }\r\n\r\n            const nextUnits = this._data.unitsOrder[this._colUnitsIndex];\r\n            this._colUnits = [];\r\n            this.unitArr = [];\r\n\r\n            for (const unitId of nextUnits) {\r\n                const unit = this._units.get(unitId);\r\n                unit.setCollideAble(true);\r\n                this._colUnits.push(unit);\r\n                this.unitArr.push(unit);\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (this.active && !this.isDead && !this._nextForm) {\r\n            this.wingmanPlanes.forEach((wingman) => {\r\n                wingman.node.angle += this._data.enemyRotate * deltaTime;\r\n            });\r\n\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    this._updateMove(deltaTime);\r\n                    this._processNextAttack(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._updateMove(deltaTime);\r\n                    if (this._bArriveDes) {\r\n                        this.setAction(BossAction.Transform);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    if (this._transFormMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this._udpateShoot(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.AttackOver:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this.setAction(BossAction.Normal);\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    break;\r\n            }\r\n\r\n            this._units.forEach((unit) => {\r\n                unit.updateGameLogic(deltaTime);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Boss 死亡逻辑\r\n     */\r\n    toDie() {\r\n        if (!this.isDead) {\r\n            this.isDead = true;\r\n            this.setAction(GameEnum.BossAction.Blast);\r\n            this._playDieAnim();\r\n\r\n            if (this._data.id >= 250 && this._data.id < 300) {\r\n                let allBossesDead = true;\r\n                for (const boss of GameIns.bossManager.bosses) {\r\n                    if (boss !== this && !boss.isDead) {\r\n                        allBossesDead = false;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                if (allBossesDead) {\r\n                    this.checkLoot();\r\n                }\r\n            } else if (this._data.nextBoss.length > 0 && GameIns.battleManager.isGameType(GameEnum.GameType.Boss)) {\r\n                // Do nothing, next boss will be handled\r\n            } else {\r\n                this.checkLoot();\r\n            }\r\n\r\n            this.onDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 单元销毁逻辑\r\n     * @param unit 被销毁的单元\r\n     */\r\n    unitDestroyed(unit: any) {\r\n        this._deadUnitIds.push(unit.unitId);\r\n        Tools.arrRemove(this._colUnits, unit);\r\n\r\n        let allUnitsDead = true;\r\n        this._units.forEach((unit) => {\r\n            if (!unit.isDead) {\r\n                allUnitsDead = false;\r\n            }\r\n        });\r\n\r\n        for (let i = 0; i < this._atkActions.length; i++) {\r\n            let hasActivePoints = false;\r\n            for (const pointId of this._atkActions[i].atkPointId) {\r\n                const pointData = this._atkPointDatas[pointId];\r\n                if (pointData[0]) {\r\n                    if (unit.unitId === pointData[1].atkUnitId) {\r\n                        pointData[0] = false;\r\n                    } else {\r\n                        hasActivePoints = true;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (!hasActivePoints) {\r\n                this._atkActions.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._attackPoints.length; i++) {\r\n            if (this._attackPoints[i].getAtkUnitId() === unit.unitId) {\r\n                this._attackPoints.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n\r\n        const soundKey = this._atkUnitSounds.get(unit.unitId);\r\n        if (soundKey) {\r\n            this._atkUnitSounds.delete(unit.unitId);\r\n        }\r\n\r\n        const soundId = this._atkUnitSoundIds.get(unit.unitId);\r\n        if (soundId !== null) {\r\n            this._atkUnitSoundIds.delete(unit.unitId);\r\n            // GameIns.default.audioManager.stopEffect(soundId);\r\n        }\r\n\r\n        this.setBodySkin(this._deadUnitIds.length);\r\n\r\n        if (allUnitsDead) {\r\n            this._formIndex++;\r\n            if (this._checkNextForm()) {\r\n                this._nextForm = true;\r\n            } else {\r\n                this._formIndex--;\r\n                this.toDie();\r\n            }\r\n        } else {\r\n            this._checkNextCollideUnits();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 单元销毁动画结束\r\n     * @param unit 被销毁的单元\r\n     */\r\n    unitDestroyAnimEnd(unit: any) {\r\n        if (this._nextForm) {\r\n            this._nextForm = false;\r\n            this.setFormIndex(this._formIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查是否有下一形态\r\n     */\r\n    _checkNextForm(): boolean {\r\n        return this._formIndex < this._datas.length;\r\n    }\r\n\r\n    /**\r\n     * 初始化属性\r\n     */\r\n    _initProperty() {\r\n        for (let i = 0; i < this._datas.length; i++) {\r\n            const data = this._datas[i];\r\n            this._attacks.push(data.attack);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置属性倍率\r\n     * @param rates 属性倍率数组\r\n     * @param updateHp 是否更新血量\r\n     */\r\n    setPropertyRate(rates: number[], updateHp: boolean = false) {\r\n        super.setPropertyRate(rates);\r\n\r\n        this.m_totalHp = 0;\r\n        this.m_curHp = 0;\r\n\r\n        if (rates.length > 1) {\r\n            for (let i = 0; i < this._attacks.length; i++) {\r\n                this._attacks[i] *= rates[1];\r\n            }\r\n        }\r\n\r\n        if (rates.length > 2) {\r\n            this._collideAtk *= rates[2];\r\n        }\r\n\r\n        this._units.forEach((unit) => {\r\n            unit.setPropertyRate(this.propertyRate);\r\n            this.m_totalHp += unit.maxHp;\r\n            this.m_curHp += unit.maxHp;\r\n        });\r\n\r\n        if (this._data.nextBoss.length > 0) {\r\n            for (let i = 0; i < this._data.nextBoss.length; i++) {\r\n                const bossData = GameIns.bossManager.getBossDatas(this._data.nextBoss[i]);\r\n                if (bossData.length > 0) {\r\n                    for (const unitId of bossData[0].units) {\r\n                        const unitData = GameIns.bossManager.getUnitData(unitId);\r\n                        if (unitData) {\r\n                            this.m_totalHp += unitData.hp * (this.propertyRate[0] || 1);\r\n                            this.m_curHp += unitData.hp * (this.propertyRate[0] || 1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!updateHp) {\r\n            // BossBattleManager.setCurHp(this.m_curHp);\r\n            // BossBattleManager.setTotalHp(this.m_totalHp);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取形态数量\r\n     */\r\n    get formNum(): number {\r\n        return this._formNum;\r\n    }\r\n\r\n    /**\r\n     * 获取当前形态索引\r\n     */\r\n    get formIndex(): number {\r\n        return this._formIndex;\r\n    }\r\n\r\n    /**\r\n     * 获取当前形态的攻击力\r\n     */\r\n    get attack(): number {\r\n        return this._attacks[this._formIndex];\r\n    }\r\n\r\n    /**\r\n     * 获取碰撞攻击力\r\n     */\r\n    getColliderAtk(): number {\r\n        return this._collideAtk;\r\n    }\r\n\r\n    /**\r\n     * 获取 X 坐标\r\n     */\r\n    get posX(): number {\r\n        return this._posX;\r\n    }\r\n\r\n    /**\r\n     * 获取 Y 坐标\r\n     */\r\n    get posY(): number {\r\n        return this._posY;\r\n    }\r\n\r\n    /**\r\n     * 是否可被攻击\r\n     */\r\n    isDamageable(): boolean {\r\n        return this._bDamageable;\r\n    }\r\n\r\n    /**\r\n     * 是否可移除\r\n     */\r\n    get removeAble(): boolean {\r\n        return this._bRemoveable;\r\n    }\r\n\r\n    set removeAble(value: boolean) {\r\n        this._bRemoveable = value;\r\n    }\r\n\r\n    /**\r\n     * 获取所有碰撞单元\r\n     */\r\n    getAllColUnits(): any[] {\r\n        return this._colUnits;\r\n    }\r\n\r\n    /**\r\n     * 设置是否可被攻击\r\n     * @param damageable 是否可被攻击\r\n     */\r\n    setDamangeable(damageable: boolean) {\r\n        this._bDamageable = damageable;\r\n        this._units.forEach((unit) => {\r\n            if (!unit.isDead) {\r\n                unit.damageable = damageable;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化 UI\r\n     */\r\n    _initUI() {\r\n        this._uiNode = new Node(\"uiNode\");\r\n        this.node.addChild(this._uiNode);\r\n\r\n        this._topAnimNode = new Node(\"topAnimNode\");\r\n        this.node.addChild(this._topAnimNode);\r\n    }\r\n\r\n    /**\r\n     * 初始化单元\r\n     */\r\n    _initUnits() {\r\n        Tools.removeChildByName(this._uiNode, \"units\");\r\n\r\n        const unitsNode = new Node(\"units\");\r\n        unitsNode.addComponent(UITransform);\r\n        this._uiNode.addChild(unitsNode);\r\n\r\n        for (const unitId of this._data.units) {\r\n            const unitData = GameIns.bossManager.getUnitData(unitId);\r\n            if (unitData) {\r\n                const unitNode = new Node();\r\n                unitNode.addComponent(UITransform);\r\n                unitsNode.addChild(unitNode);\r\n\r\n                const unit = unitNode.addComponent(BossUnit);\r\n                unit.init(unitData, this);\r\n                unit.setCollideAtk(this._data.collideAttack);\r\n                unit.setPropertyRate(this.propertyRate);\r\n\r\n                this._units.set(unitData.uId, unit);\r\n            }\r\n        }\r\n\r\n        for (let i = this._atkPointsPool.length - 1; i < this._atkPointDatas.length; i++) {\r\n            const pointNode = new Node(\"pointNode_\" + i);\r\n            this.node.addChild(pointNode);\r\n            pointNode.angle = -180;\r\n\r\n            const attackPoint = pointNode.addComponent(AttackPoint);\r\n            this._atkPointsPool.push(attackPoint);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置身体皮肤\r\n     * @param skinIndex 皮肤索引\r\n     */\r\n    setBodySkin(skinIndex: number) {\r\n        this._units.forEach((unit) => {\r\n            if (unit.isBody()) {\r\n                unit.setSkin(skinIndex);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n * 初始化轨迹\r\n */\r\n    _initTrack() {\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._trackCom.setTrackGroupStartCall((trackGroup, trackIndex, trackType) => { });\r\n        this._trackCom.setTrackGroupOverCall((trackGroup) => {\r\n            if (this._action === GameEnum.BossAction.Appear) {\r\n                this._trackCom.setTrackAble(false);\r\n                this.setAction(GameEnum.BossAction.Transform);\r\n            }\r\n        });\r\n        this._trackCom.setTrackOverCall(() => { });\r\n        this._trackCom.setTrackLeaveCall(() => { });\r\n        this._trackCom.setTrackStartCall((track) => {\r\n            this.setTrackType(track);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 开始出现轨迹\r\n     */\r\n    _startAppearTrack() {\r\n        const trackGroup = new TrackGroup();\r\n        trackGroup.loopNum = 1;\r\n        trackGroup.trackIDs = [this._data.appearParam[2]];\r\n        trackGroup.speeds = [this._data.appearParam[3]];\r\n        trackGroup.trackIntervals = [0];\r\n\r\n        this._trackCom.init(this, [trackGroup], [], this._data.appearParam[0], this._data.appearParam[1]);\r\n        this._trackCom.setTrackAble(true);\r\n        this._trackCom.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 开始正常轨迹\r\n     */\r\n    _startNormalTrack() {\r\n        this._trackCom.init(this, this._data.trackGroups, [], this.node.x, this.node.y);\r\n        this._trackCom.setTrackAble(true);\r\n        this._trackCom.startTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n    }\r\n\r\n    /**\r\n     * 设置轨迹类型\r\n     * @param track 当前轨迹\r\n     */\r\n    setTrackType(track: any) {\r\n        if (track) {\r\n            switch (track.type) {\r\n                case 4:\r\n                case 5:\r\n                    if (this._curTrackType !== 4 && this._curTrackType !== 5) {\r\n                        this._trackCom.setTrackAble(false);\r\n                        this._shootAble = false;\r\n                        this._colUnits.forEach((unit) => unit.setCollideAble(false));\r\n                        this._playCloakeHideAnim(() => {\r\n                            this._trackCom.setTrackAble(true);\r\n                        });\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    if (this._curTrackType === 4 || this._curTrackType === 5) {\r\n                        this._shootAble = false;\r\n                        this._trackCom.setTrackAble(false);\r\n                        this._playCloakeShowAnim(() => {\r\n                            this._shootAble = true;\r\n                            this._trackCom.setTrackAble(true);\r\n                            this._colUnits.forEach((unit) => unit.setCollideAble(true));\r\n                        });\r\n                        this._playSkel(\"cloake\", true);\r\n                    }\r\n            }\r\n            this._curTrackType = track.type;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移动到指定位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param speed 移动速度\r\n     * @param transformMove 是否为变形移动\r\n     */\r\n    moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {\r\n        this._moveToX = x;\r\n        this._moveToY = y;\r\n        this._moveSpeed = speed;\r\n        this._bArriveDes = false;\r\n        this._transFormMove = transformMove;\r\n    }\r\n\r\n    /**\r\n     * 设置位置\r\n     * @param pos 位置\r\n     * @param arrived 是否到达目标\r\n     */\r\n    setPosition(pos: Vec2 | Vec3, arrived: boolean = false) {\r\n        this._posX = pos.x;\r\n        this._posY = pos.y;\r\n        this.setPos(this._posX, this._posY);\r\n        this._bArriveDes = arrived;\r\n    }\r\n\r\n    /**\r\n     * 设置位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param update 是否更新\r\n     */\r\n    setPos(x: number, y: number, update: boolean = true) {\r\n        super.setPos(x, y, update);\r\n        this._posX = x;\r\n        this._posY = y;\r\n    }\r\n\r\n    /**\r\n     * 处理下一个路径点\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextWayPoint(deltaTime: number) {\r\n        if (this._bArriveDes && this._data.trackGroups.length === 0) {\r\n            this._nextWayPointTime += deltaTime;\r\n            if (this._nextWayPointTime > this._nextWayPointInterval) {\r\n                this._nextWayPointInterval = Tools.getRandomInArray(this._data.wayPointIntervals);\r\n                this._nextWayPointTime = 0;\r\n\r\n                if (this._bFirstWayPoint) {\r\n                    this._bFirstWayPoint = false;\r\n                } else {\r\n                    const index = Tools.random_int(0, this._data.wayPointXs.length - 1);\r\n                    this._nextWayPointX = this._data.wayPointXs[index];\r\n                    this._nextWayPointY = this._data.wayPointYs[index];\r\n                    this._nextWaySpeed = Tools.getRandomInArray(this._data.speeds);\r\n                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    _updateMove(deltaTime: number) {\r\n        if (this._action === GameEnum.BossAction.Appear || this._data.trackGroups.length > 0) {\r\n            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑\r\n            this._trackCom.updateGameLogic(deltaTime);\r\n        } else if (!this._bArriveDes) {\r\n            // 如果未到达目标位置，则更新移动逻辑\r\n            this._prePosX = this._posX;\r\n            this._prePosY = this._posY;\r\n\r\n            const deltaX = this._moveToX - this._posX;\r\n            const deltaY = this._moveToY - this._posY;\r\n            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n            let moveX = 0;\r\n            let moveY = 0;\r\n\r\n            // 如果距离小于等于移动速度，则直接到达目标点\r\n            if (distance <= this._moveSpeed) {\r\n                moveX = deltaX;\r\n                moveY = deltaY;\r\n            }\r\n            // 否则按比例移动\r\n            else {\r\n                moveX = this._moveSpeed * deltaX / distance;\r\n                moveY = this._moveSpeed * deltaY / distance;\r\n            }\r\n\r\n            // 更新位置\r\n            this._posX += moveX;\r\n            this._posY += moveY;\r\n            this.setPos(this._posX, this._posY);\r\n\r\n            // 检查是否到达目的地（当移动量很小时认为已到达）\r\n            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理下一次攻击\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextAttack(deltaTime: number) {\r\n        if (this._shootAble && this._action === GameEnum.BossAction.Normal) {\r\n            this._nextAttackTime += deltaTime;\r\n            if (this._nextAttackTime > this._nextAttackInterval) {\r\n                this._nextAttackInterval = Tools.getRandomInArray(this._data.attackIntervals);\r\n                this._nextAttackTime = 0;\r\n\r\n                let attackAction = null;\r\n                if (this._bOrderAttack) {\r\n                    const randomIndex = Tools.getRandomInArray(this._orderAtkArr);\r\n                    Tools.arrRemove(this._orderAtkArr, randomIndex);\r\n                    attackAction = this._atkActions[randomIndex];\r\n                    this._orderIndex++;\r\n                    if (this._orderIndex > this._atkActions.length - 1) {\r\n                        this._bOrderAttack = false;\r\n                    }\r\n                } else {\r\n                    attackAction = Tools.getRandomInArray(this._atkActions);\r\n                }\r\n\r\n                if (attackAction) {\r\n                    this._bAttackMove = attackAction.bAtkMove;\r\n                    this._attackID = attackAction.atkActId;\r\n                    this._attackPoints.splice(0);\r\n\r\n                    for (const pointId of attackAction.atkPointId) {\r\n                        const pointData = this._atkPointDatas[pointId];\r\n                        if (pointData[0]) {\r\n                            let attackPoint = this._atkPointsPool[pointId]\r\n                            if (!attackPoint) {\r\n                                const pointNode = new Node();\r\n                                this.node.addChild(pointNode);\r\n                                attackPoint = pointNode.addComponent(AttackPoint);\r\n                                this._atkPointsPool.push(attackPoint);\r\n                            }\r\n                            attackPoint.initForBoss(pointData[1], this);\r\n                            this._attackPoints.push(attackPoint);\r\n                        }\r\n                    }\r\n\r\n                    if (this._attackPoints.length > 0) {\r\n                        this.setAction(GameEnum.BossAction.AttackPrepare);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    async _udpateShoot(deltaTime: number) {\r\n        if (this._shootAble) {\r\n            let allAttacksOver = true;\r\n\r\n            for (const attackPoint of this._attackPoints) {\r\n                await attackPoint.updateGameLogic(deltaTime);\r\n                if (!attackPoint.isAttackOver()) {\r\n                    allAttacksOver = false;\r\n                }\r\n            }\r\n\r\n            if (allAttacksOver) {\r\n                this.setAction(GameEnum.BossAction.AttackOver);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查攻击动画\r\n     */\r\n    _checkAtkAnim(): boolean {\r\n        let hasAnimation = false;\r\n\r\n        // for (const attackPoint of this._attackPoints) {\r\n        //     for (const anim of attackPoint.getAtkAnims()) {\r\n        //         const unit = this._units.get(anim[0]);\r\n        //         if (unit && !Tools.arrContain(this._deadUnitIds, unit.unitId)) {\r\n        //             hasAnimation = true;\r\n        //             unit.playSkel(anim[1], false, () => {\r\n        //                 this.setAction(GameEnum.BossAction.AttackIng);\r\n        //             });\r\n        //         }\r\n        //     }\r\n        // }\r\n\r\n        return hasAnimation;\r\n    }\r\n\r\n    /**\r\n     * 播放骨骼动画\r\n     * @param animName 动画名称\r\n     * @param loop 是否循环\r\n     * @param callback 动画结束回调\r\n     * @param unitId 单元 ID\r\n     */\r\n    _playSkel(animName: string, loop: boolean, callback: Function = null, unitId: number = -1) {\r\n        this._units.forEach((unit, id) => {\r\n            if (unitId === -1 || id === unitId) {\r\n                unit.playSkel(animName, loop, callback);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    _playDieAnim() {\r\n        if (this._data.nextBoss.length > 0) {\r\n            this._checkNextBoss();\r\n        } else {\r\n            this._bRemoveable = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放白屏死亡动画\r\n     */\r\n    playDieWhiteAnim() {\r\n        this.scheduleOnce(() => {\r\n            EffectLayer.me.showWhiteScreen(4 * GameConst.ActionFrameTime, 255);\r\n            // this._uiNode.opacity = 0;\r\n            this._units.forEach((unit) => {\r\n                unit.hideSmoke();\r\n            });\r\n        }, 41 * GameConst.ActionFrameTime);\r\n    }\r\n\r\n    /**\r\n     * 播放坠落动画\r\n     */\r\n    _playFallAnim() {\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        // const fallSequence = sequence(\r\n        //     moveTo(2 * frameTime, v2(-1, 6)),\r\n        //     moveTo(frameTime, v2(3, -2))\r\n        // );\r\n\r\n        // this._uiNode.runAction(repeatForever(fallSequence));\r\n        // this._uiNode.runAction(scaleTo(60 * frameTime, 0.5));\r\n    }\r\n\r\n    /**\r\n     * 播放震动动画\r\n     */\r\n    _playShakeAnim() {\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        tween(this._uiNode)\r\n            .to(2 * frameTime, { position: v3(-3, -2) })\r\n            .to(2 * frameTime, { position: v3(11, -14), angle: 1 })\r\n            .to(2 * frameTime, { position: v3(7, 4) })\r\n            .to(2 * frameTime, { position: v3(20, -9), angle: 0 })\r\n            .to(2 * frameTime, { position: v3(29, 7) })\r\n            .to(frameTime, { position: v3(13, -5) })\r\n            .to(frameTime, { position: v3(17, 2) })\r\n            .to(frameTime, { position: v3(4, -6) })\r\n            .to(frameTime, { position: v3(14, 4) })\r\n            .to(frameTime, { position: v3(-1, -4) })\r\n            .to(frameTime, { position: v3(5, 6) })\r\n            .to(frameTime, { position: v3(-3, -5) })\r\n            .to(frameTime, { position: v3(1, 3) })\r\n            .to(frameTime, { position: v3(-7, -6) })\r\n            .to(frameTime, { position: v3(0, 2) })\r\n            .to(frameTime, { position: v3(-3, -4) })\r\n            .delay(frameTime)\r\n            .to(frameTime, { position: v3(0, 0) })\r\n            .start();\r\n    }\r\n\r\n    /**\r\n     * 播放坠落震动动画\r\n     */\r\n    _playFallShake() {\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        // const fallShakeSequence = sequence(\r\n        //     moveTo(2 * frameTime, v2(-1, 6)),\r\n        //     moveTo(frameTime, v2(3, -2))\r\n        // );\r\n\r\n        // this._uiNode.runAction(repeatForever(fallShakeSequence));\r\n    }\r\n\r\n    /**\r\n     * 播放隐身动画\r\n     */\r\n    _playCloakeAnim() {\r\n        if (!this._cloakeAnim) {\r\n            const animNode = instantiate(GameIns.gameResManager.frameAnim);\r\n            this.node.addChild(animNode);\r\n\r\n            this._cloakeAnim = animNode.getComponent(PfFrameAnim);\r\n            this._cloakeAnim.init(\r\n                GameIns.enemyManager.enemyAtlas,\r\n                \"a_\",\r\n                12,\r\n                GameConst.ActionFrameTime\r\n            );\r\n            animNode.active = false;\r\n        }\r\n\r\n        this._cloakeAnim.node.setScale(1.3, 1.3);\r\n        this._cloakeAnim.node.active = true;\r\n        this._cloakeAnim.reset(1);\r\n    }\r\n\r\n    /**\r\n     * 播放隐身消失动画\r\n     * @param callback 动画结束回调\r\n     */\r\n    _playCloakeHideAnim(callback: Function = null) {\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        // GameIns.audioManager.playEffect(\"cloake\");\r\n        this._playCloakeAnim();\r\n\r\n        // tween(this.node)\r\n        //     .to(5 * frameTime, { opacity: 90 })\r\n        //     .to(2 * frameTime, { opacity: 0 })\r\n        //     .call(() => {\r\n        //         this._playSkel(\"cloake\", true);\r\n        //     })\r\n        //     .to(6 * frameTime, { opacity: 255 })\r\n        //     .call(() => {\r\n        //         if (callback) callback();\r\n        //     })\r\n        //     .start();\r\n    }\r\n\r\n    /**\r\n     * 播放隐身显现动画\r\n     * @param callback 动画结束回调\r\n     */\r\n    _playCloakeShowAnim(callback: Function = null) {\r\n        const frameTime = GameConst.ActionFrameTime;\r\n\r\n        // tween(this.node)\r\n        //     .to(4 * frameTime, { opacity: 102 })\r\n        //     .to(2 * frameTime, { opacity: 255 })\r\n        //     .to(4 * frameTime, { opacity: 102 })\r\n        //     .to(2 * frameTime, { opacity: 255 })\r\n        //     .to(3 * frameTime, { opacity: 102 })\r\n        //     .to(frameTime, { opacity: 0 })\r\n        //     .call(() => {\r\n        //         this._playSkel(this._idleName, true);\r\n        //         this._playCloakeAnim();\r\n        //     })\r\n        //     .to(7 * frameTime, { opacity: 255 })\r\n        //     .call(() => {\r\n        //         if (callback) callback();\r\n        //     })\r\n        //     .start();\r\n    }\r\n    /**\r\n     * 检查并生成下一个 Boss\r\n     */\r\n    _checkNextBoss() {\r\n        if (this._data.id === 200) {\r\n            this._playSkel(\"next\", false, () => {\r\n                for (const nextBossId of this._data.nextBoss) {\r\n                    const bossData = GameIns.bossManager.getBossDatas(nextBossId)[0];\r\n                    const boss = GameIns.bossManager.createBossById(nextBossId);\r\n                    boss.setPosition(this.node.position, true);\r\n                    boss.active = false;\r\n                    boss.setPropertyRate(this.propertyRate, true);\r\n\r\n                    tween(boss.node)\r\n                        .to(1, { position: v3(bossData.appearParam[0], bossData.appearParam[1]) })\r\n                        .call(() => {\r\n                            boss.setPosition(boss.node.position, true);\r\n                            boss.startBattle();\r\n                        })\r\n                        .start();\r\n                }\r\n\r\n                this.node.active = false;\r\n                this._bRemoveable = true;\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 改变血量\r\n     * @param delta 血量变化值\r\n     */\r\n    hpChange(delta: number) {\r\n        this.m_curHp += delta;\r\n        if (this.m_curHp < 0) {\r\n            this.m_curHp = 0;\r\n        }\r\n        // BossBattleManager.hpChange(delta, this.node);\r\n    }\r\n\r\n    /**\r\n     * 获取血量百分比\r\n     */\r\n    getHpPercent(): number {\r\n        return this.m_curHp / this.m_totalHp;\r\n    }\r\n}"]}