{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"], "names": ["ResLoadingTips", "_decorator", "ccenum", "Component", "director", "loader", "LabelComponent", "ProgressBar", "WECHAT", "DevLoginUI", "BattleUI", "BottomUI", "TopUI", "UIMgr", "logDebug", "ccclass", "property", "Progress", "Context", "warnCustom", "console", "warn", "res", "indexOf", "groupCustom", "group", "GameLogLevel", "ResUpdate", "start", "initializeLayers", "THIS", "preloadScene", "OnLoadProgress", "bind", "loadUI", "loadScene", "completedCount", "totalCount", "item", "progress", "toFixed", "id", "node", "per<PERSON><PERSON><PERSON>", "string", "<PERSON><PERSON><PERSON><PERSON>", "loadingBar", "onLoad", "onDestroy", "onProgress", "update", "deltaTime"], "mappings": ";;;6NAWMA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXGC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,c,OAAAA,c;AAAgBC,MAAAA,W,OAAAA,W;;AACjEC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;AAExBD,MAAAA,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACViB,QADU;AAAA,eAEVC,OAFU;AAAA;;AAAA,O;;AAKrB,UAAIV,MAAJ,EAAY;AACJW,QAAAA,UADI,GACSC,OAAO,CAACC,IADjB;;AAERD,QAAAA,OAAO,CAACC,IAAR,GAAe,UAAUC,GAAV,EAAe;AAC1B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,gBAAZ,IAAgC,CAAC,CAA/D,EAAkE;AAC9D;AACH,WAFD,MAEO;AACHJ,YAAAA,UAAU,CAACG,GAAD,CAAV;AACH;AAGJ,SARD;;AASIE,QAAAA,WAXI,GAWUJ,OAAO,CAACK,KAXlB;;AAYRL,QAAAA,OAAO,CAACK,KAAR,GAAgB,UAAUH,GAAV,EAAe;AAC3B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,YAAZ,IAA4B,CAAC,CAA3D,EAA8D;AAC1D;AACH,WAFD,MAEO;AACHC,YAAAA,WAAW,CAACF,GAAD,CAAX;AACH;AACJ,SAND;AAOH;;AAEII,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAQLxB,MAAAA,MAAM,CAACwB,YAAD,CAAN;;2BAGaC,S,WADZZ,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACV,cAAD,C,UAERU,QAAQ,CAACV,cAAD,C,UAERU,QAAQ,CAACV,cAAD,C,UAERU,QAAQ,CAACT,WAAD,C,4BAXb,MACaoB,SADb,SAC+BxB,SAD/B,CACyC;AAAA;AAAA;;AACrC;AACA;AAFqC;;AAAA;;AAAA;;AAAA;AAAA;;AAarCyB,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,8BAAMC,gBAAN,GADI,CAEJ;;AACA,cAAIC,IAAI,GAAG,IAAX;AACA1B,UAAAA,QAAQ,CAAC2B,YAAT,CAAsB,MAAtB,EAA8B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA9B,EAA8D,YAAY;AACtE;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,yCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,qCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,qCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,+BAAN;AACA9B,YAAAA,QAAQ,CAAC+B,SAAT,CAAmB,MAAnB;AACH,WAPD;AAQH;;AAEDH,QAAAA,cAAc,CAACI,cAAD,EAAyBC,UAAzB,EAA6CC,IAA7C,EAAmD;AAC7D,cAAIC,QAAQ,GAAIH,cAAc,GAAGC,UAAjC;AACA;AAAA;AAAA,oCAAS,WAAT,EAAuB,oBAAmBD,cAAe,KAAIC,UAAW,KAAI,CAACE,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,CAA4B,KAAIF,IAAI,CAACG,EAAG,EAApH;;AACA,cAAI,KAAKC,IAAL,IAAa,IAAjB,EAAuB;AACnB;AACH;;AACD,eAAKC,QAAL,CAAcC,MAAd,GAAuB,CAACL,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,IAA8B,GAArD;AACA,eAAKK,UAAL,CAAgBD,MAAhB,GAAyB,YAAYR,cAAZ,GAA6B,GAA7B,GAAmCC,UAAnC,GAAgD,GAAzE;AACA,eAAKS,UAAL,CAAgBP,QAAhB,GAA2BA,QAA3B;AACH;;AAEDQ,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,SAAS,GAAG;AACR3C,UAAAA,MAAM,CAAC4C,UAAP,GAAoB,IAApB;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACtB;AACA;AACA;AACA;AACA;AACH;;AAlDoC,O;;;;;iBAKA,I;;;;;;;iBAEF,I;;;;;;;iBAEI,I;;;;;;;iBAEL,I", "sourcesContent": ["import { _decorator, ccenum, Component, director, loader, LabelComponent, ProgressBar } from 'cc';\r\nimport { WECHAT } from \"cc/env\";\r\nimport \"../AAA/init_cs_proto.js\";\r\nimport { DevLoginUI } from '../ui/DevLoginUI';\r\nimport { BattleUI } from '../ui/main/BattleUI';\r\nimport { BottomUI } from '../ui/main/BottomUI';\r\nimport { TopUI } from '../ui/main/TopUI';\r\nimport { UIMgr } from \"../ui/UIMgr\";\r\nimport { logDebug } from \"../Utils/Logger\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nclass ResLoadingTips {\r\n    public Progress: number;\r\n    public Context: string;\r\n}\r\n\r\nif (WECHAT) {\r\n    var warnCustom = console.warn;\r\n    console.warn = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"文件路径在真机上可能无法读取\") > -1) {\r\n            return;\r\n        } else {\r\n            warnCustom(res)\r\n        }\r\n\r\n\r\n    }\r\n    var groupCustom = console.group;\r\n    console.group = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"读取文件/文件夹警告\") > -1) {\r\n            return;\r\n        } else {\r\n            groupCustom(res)\r\n        }\r\n    }\r\n}\r\n\r\nenum GameLogLevel {\r\n    TRACE = 0,\r\n    DEBUG = 1,\r\n    LOG = 2,\r\n    INFO = 3,\r\n    WARN = 4,\r\n    ERROR = 5,\r\n}\r\nccenum(GameLogLevel)\r\n\r\n@ccclass(\"ResUpdate\")\r\nexport class ResUpdate extends Component {\r\n    /* class member could be defined like this */\r\n    // dummy = '';\r\n\r\n    @property(LabelComponent)\r\n    private countLabel: LabelComponent = null;\r\n    @property(LabelComponent)\r\n    private perLabel: LabelComponent = null;\r\n    @property(LabelComponent)\r\n    private versionLabel: LabelComponent = null;\r\n    @property(ProgressBar)\r\n    private loadingBar: ProgressBar = null;\r\n\r\n    start() {\r\n        UIMgr.initializeLayers();\r\n        // Your initialization goes here.\r\n        let THIS = this;\r\n        director.preloadScene(\"Main\", this.OnLoadProgress.bind(this), async () => {\r\n            // dev 先load login UI\r\n            await UIMgr.loadUI(DevLoginUI)\r\n            await UIMgr.loadUI(BattleUI)\r\n            await UIMgr.loadUI(BottomUI)\r\n            await UIMgr.loadUI(TopUI)\r\n            director.loadScene(\"Main\")\r\n        })\r\n    }\r\n\r\n    OnLoadProgress(completedCount: number, totalCount: number, item) {\r\n        let progress = (completedCount / totalCount)\r\n        logDebug(\"ResUpdate\", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}, ${item.id}`)\r\n        if (this.node == null) {\r\n            return;\r\n        }\r\n        this.perLabel.string = (progress * 100).toFixed(2) + \"%\"\r\n        this.countLabel.string = '加载中...(' + completedCount + '/' + totalCount + ')'\r\n        this.loadingBar.progress = progress\r\n    }\r\n\r\n    onLoad() {\r\n    }\r\n    onDestroy() {\r\n        loader.onProgress = null;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        //if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)\r\n        //{\r\n        //    director.loadScene(\"MainScene\")\r\n        //}\r\n        // Your update function goes here.\r\n    }\r\n}\r\n"]}