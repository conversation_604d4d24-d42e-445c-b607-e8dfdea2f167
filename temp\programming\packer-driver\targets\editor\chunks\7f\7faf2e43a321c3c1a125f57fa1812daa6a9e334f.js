System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, LevelDataBackgroundLayer, LevelDataLayer, LevelEditorLayerUI, LevelEditorUtils, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _dec4, _dec5, _class4, _class5, _descriptor3, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _class7, _class8, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _crd, ccclass, property, executeInEditMode, BackgroundsNode<PERSON><PERSON>, LevelLayer, LevelBackgroundLayer, LevelEditorBaseUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorLayerUI(extras) {
    _reporterNs.report("LevelEditorLayerUI", "./LevelEditorLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Node = _cc.Node;
      CCString = _cc.CCString;
      Prefab = _cc.Prefab;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      LevelDataBackgroundLayer = _unresolved_2.LevelDataBackgroundLayer;
      LevelDataLayer = _unresolved_2.LevelDataLayer;
    }, function (_unresolved_3) {
      LevelEditorLayerUI = _unresolved_3.LevelEditorLayerUI;
    }, function (_unresolved_4) {
      LevelEditorUtils = _unresolved_4.LevelEditorUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a4bf2J2KGJJV7RbX1jwoQWJ", "LevelEditorBaseUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Node', 'CCString', 'Prefab', 'assetManager', 'instantiate']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      BackgroundsNodeName = "backgrounds";
      LevelLayer = (_dec = ccclass('EditorLevelLayer'), _dec2 = property(Node), _dec3 = property(CCFloat), _dec(_class = (_class2 = class LevelLayer {
        constructor() {
          _initializerDefineProperty(this, "node", _descriptor, this);

          _initializerDefineProperty(this, "speed", _descriptor2, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "node", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 10;
        }
      })), _class2)) || _class);
      LevelBackgroundLayer = (_dec4 = ccclass('EditorLevelBackgroundLayer'), _dec5 = property([Prefab]), _dec4(_class4 = (_class5 = class LevelBackgroundLayer extends LevelLayer {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "backgrounds", _descriptor3, this);

          this.backgroundsNode = null;
        }

      }, (_descriptor3 = _applyDecoratedDescriptor(_class5.prototype, "backgrounds", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4);

      _export("LevelEditorBaseUI", LevelEditorBaseUI = (_dec6 = ccclass('LevelEditorBaseUI'), _dec7 = executeInEditMode(), _dec8 = property(CCString), _dec9 = property(CCFloat), _dec10 = property(LevelBackgroundLayer), _dec11 = property([LevelLayer]), _dec12 = property([LevelLayer]), _dec6(_class7 = _dec7(_class7 = (_class8 = class LevelEditorBaseUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "levelname", _descriptor4, this);

          _initializerDefineProperty(this, "totalTime", _descriptor5, this);

          _initializerDefineProperty(this, "backgroundLayer", _descriptor6, this);

          _initializerDefineProperty(this, "floorLayers", _descriptor7, this);

          _initializerDefineProperty(this, "skyLayers", _descriptor8, this);

          this.backgroundLayerNode = null;
          this.floorLayersNode = null;
          this.skyLayersNode = null;
        }

        onLoad() {
          var _this$floorLayersNode;

          console.log(`LevelEditorBaseUI start.`);
          this.backgroundLayerNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "BackgroundLayer");
          this.floorLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "FloorLayers");
          this.skyLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "SkyLayers");
          console.log(`LevelEditorBaseUI start ${(_this$floorLayersNode = this.floorLayersNode) == null ? void 0 : _this$floorLayersNode.uuid}`);
        }

        update(dt) {
          this.checkLayerNode(this.floorLayersNode, this.floorLayers);
          this.checkLayerNode(this.skyLayersNode, this.skyLayers);
        }

        tick(progress) {
          var _this$backgroundLayer;

          var bgCount = Math.ceil(this.totalTime * this.backgroundLayer.speed / 1280);

          while (this.backgroundLayer.backgrounds.length > 0 && bgCount > this.backgroundLayer.backgroundsNode.children.length) {
            var bg = instantiate(this.backgroundLayer.backgrounds[this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length]);
            bg.setPosition(0, this.backgroundLayer.backgroundsNode.children.length * 1280, 0);
            this.backgroundLayer.backgroundsNode.addChild(bg);
          }

          while (bgCount < this.backgroundLayer.backgroundsNode.children.length) {
            this.backgroundLayer.backgroundsNode.children[this.backgroundLayer.backgroundsNode.children.length - 1].removeFromParent();
          }

          (_this$backgroundLayer = this.backgroundLayer.node) == null || _this$backgroundLayer.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, this.backgroundLayer.speed);
          this.floorLayers.forEach(layer => {
            var _layer$node;

            (_layer$node = layer.node) == null || _layer$node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
          this.skyLayers.forEach(layer => {
            var _layer$node2;

            (_layer$node2 = layer.node) == null || _layer$node2.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
        }

        static addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        checkLayerNode(parentNode, layers) {
          var removeLayerNodes = [];
          parentNode.children.forEach(node => {
            var layerCom = node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layerCom == null) {
              console.log(`Level checkLayerNode remove ${node.name} because layerCom == null"`);
              removeLayerNodes.push(node);
              return;
            }

            if (layers.find(layer => layer.node == node) == null) {
              console.log(`Level checkLayerNode remove ${node.name} because not in layers"`);
              removeLayerNodes.push(node);
              return;
            }
          });
          removeLayerNodes.forEach(element => {
            element.removeFromParent();
          });
          layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
              console.log(`Level checkLayerNode add because layer == null`);
              layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            }
          });
        }

        initByLevelData(data) {
          var _data$backgroundLayer, _data$backgroundLayer2;

          this.levelname = data.name;
          this.totalTime = data.totalTime;
          this.backgroundLayerNode.removeAllChildren();
          this.backgroundLayer = new LevelBackgroundLayer();
          this.backgroundLayer.backgrounds = [];
          (_data$backgroundLayer = data.backgroundLayer) == null || (_data$backgroundLayer = _data$backgroundLayer.backgrounds) == null || _data$backgroundLayer.forEach(background => {
            console.log("LevelEditorBaseUI initByLevelData load background");
            assetManager.loadAny({
              uuid: background
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                return;
              }

              this.backgroundLayer.backgrounds.push(prefab);
            });
          });
          this.backgroundLayer.speed = (_data$backgroundLayer2 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer2.speed;
          this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, "layer").node;
          this.backgroundLayer.backgroundsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
          this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).initByLevelData(data.backgroundLayer);
          this.floorLayers = [];
          this.skyLayers = [];
          LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);
        }

        static initLayers(parentNode, layers, dataLayers) {
          parentNode.removeAllChildren();
          dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).initByLevelData(layer);
            layers.push(levelLayer);
          });
        }

        static fillLevelLayerData(layer, dataLayer) {
          dataLayer.speed = layer.speed;
          layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).fillLevelData(dataLayer);
        }

        static fillLevelLayersData(layers, dataLayers) {
          layers.forEach(layer => {
            var levelLayer = new (_crd && LevelDataLayer === void 0 ? (_reportPossibleCrUseOfLevelDataLayer({
              error: Error()
            }), LevelDataLayer) : LevelDataLayer)();
            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
          });
        }

        fillLevelData(data) {
          data.name = this.levelname;
          data.totalTime = this.totalTime;
          data.backgroundLayer = new (_crd && LevelDataBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelDataBackgroundLayer({
            error: Error()
          }), LevelDataBackgroundLayer) : LevelDataBackgroundLayer)();
          data.backgroundLayer.backgrounds = this.backgroundLayer.backgrounds.map(prefab => prefab.uuid);
          LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);
          data.floorLayers = [];
          data.skyLayers = [];
          LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);
        }

      }, (_descriptor4 = _applyDecoratedDescriptor(_class8.prototype, "levelname", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class8.prototype, "totalTime", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 10;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class8.prototype, "backgroundLayer", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new LevelBackgroundLayer();
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class8.prototype, "floorLayers", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class8.prototype, "skyLayers", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class8)) || _class7) || _class7));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js.map