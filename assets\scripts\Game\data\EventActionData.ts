import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
const { ccclass, property } = _decorator;

/**
 * ActionType对应要修改的属性
 * 以下是发射器的行为
 */
export enum eEmitterActionType {
    Emitter_Active = 1,         // 发射器是否启用
    Emitter_InitialDelay,       // 发射器当前的初始延迟
    Emitter_Prewarm,            // 发射器是否启用预热
    Emitter_PrewarmDuration,    // 发射器预热的持续时间
    Emitter_Duration,           // 发射器配置的持续时间
    Emitter_ElapsedTime,        // 发射器已运行的时间
    Emitter_Loop,               // 发射器是否循环
    Emitter_LoopInterval,       // 发射器循环的间隔时间

    Emitter_PerEmitInterval,   // 发射器开火间隔
    Emitter_PerEmitCount,      // 发射器开火次数
    Emitter_PerEmitOffsetX,    // 发射器开火偏移

    Emitter_Angle,             // 发射器弹道角度
    Emitter_Count,             // 发射器弹道数量

    Bullet_Duration,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_ColorA,
    Bullet_FaceMovingDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
    
    Unit_Life,
    Unit_LifePercent,
    Unit_PosX,
    Unit_PosY,
    Unit_Speed,
    Unit_SpeedAngle,
    Unit_Acceleration,
    Unit_AccelerationAngle,
}

// const map:[] = {
//     {eEmitter_Active, "active", ValueType.Boolean },
// }

// class PropertyContainer {
//     [key: string]: any;
// }

// class Emitter {
//     container: PropertyContainer;

//     init(): void {
//         this.container = new PropertyContainer();
//         this.container["active"] = true;
//     }
// }

/**
 * ActionType对应要修改的属性
 * 以下是子弹的行为
 */
export enum eBulletActionType {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_ColorA,
    Bullet_FaceMovingDir,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export enum eEasing {
    Linear,
    InSine, OutSine, InOutSine,
    InQuad, OutQuad, InOutQuad
}

export class EmitterActionMapping {
    public type: eEmitterActionType;
    public name: string;

}

/**
 * 发射器行为数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("EmitterActionData")
export class EmitterActionData {
    @property({ type: Enum(eEmitterActionType), displayName: '行为类型' })
    actionType : eEmitterActionType;

    @property({ displayName: '是否随机' })
    isRandom : boolean = false;

    @property({ displayName: '最小值' })
    minValue : number = 0;

    @property({ displayName: '最大值' })
    maxValue : number = 0;

    @property({ displayName: 'bool值' })
    boolValue : boolean = false;

    @property({ displayName: '持续时间' })
    duration : number = 0; // 持续时间: 0表示立即执行

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    easing : eEasing = eEasing.Linear;
}

/**
 * 子弹行为数据
 * 所有时间相关的，单位都是秒(s)
 */
@ccclass("BulletActionData")
export class BulletActionData {
    @property({ type: Enum(eBulletActionType), displayName: '行为类型' })
    actionType : eBulletActionType;

    @property({ displayName: '是否随机' })
    isRandom : boolean = false;

    @property({ displayName: '最小值' })
    minValue : number = 0;

    @property({ displayName: '最大值' })
    maxValue : number = 0;

    @property({ displayName: 'bool值' })
    boolValue : boolean = false;

    @property({ displayName: '持续时间' })
    duration : number = 0; // 持续时间: 0表示立即执行

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    easing : eEasing = eEasing.Linear;
}