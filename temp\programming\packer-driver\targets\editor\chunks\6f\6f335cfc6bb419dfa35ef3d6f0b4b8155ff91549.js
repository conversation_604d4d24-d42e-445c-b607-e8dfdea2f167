System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, Component, instantiate, Node, Vec2, MyApp, GameIns, Wave, LevelDataEventTriggerType, _dec, _class, _crd, ccclass, TerrainsNodeName, DynamicNodeName, WaveNodeName, EventNodeName, LevelLayerUI;

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataWave(extras) {
    _reporterNs.report("LevelDataWave", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../../wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "../../../leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "../../../leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "../../../leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Wave = _unresolved_4.Wave;
    }, function (_unresolved_5) {
      LevelDataEventTriggerType = _unresolved_5.LevelDataEventTriggerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Component', 'instantiate', 'Node', 'Prefab', 'Vec2']);

      ({
        ccclass
      } = _decorator);
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      WaveNodeName = "waves";
      EventNodeName = "events";

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends Component {
        constructor(...args) {
          super(...args);
          this.backgrounds = [];
          this._offSetY = 0;
          // 当前关卡的偏移量
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.waves = [];
          this.events = [];
          this.enableEvents = [];
        }

        onLoad() {}

        initByLevelData(data, offSetY) {
          var _data$terrains;

          this._offSetY = offSetY;
          this.node.setPosition(0, offSetY, 0);
          this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
          this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);
          console.log('LevelLayerUI', " initByLevelData");
          this.backgrounds = [];
          (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
            assetManager.loadAny({
              uuid: terrain.uuid
            }, (err, prefab) => {
              if (err) {
                console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
              terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
              this.terrainsNode.addChild(terrainNode);
            });
          });
          this.waves = [...data.waves];
          this.waves.sort((a, b) => a.position.y - b.position.y);
          this.events = [...data.events];
          this.events.sort((a, b) => a.position.y - b.position.y);
        }

        tick(deltaTime, speed) {
          const prePosY = this.node.getPosition().y;
          this.node.setPosition(0, prePosY - deltaTime * speed, 0);

          while (this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {
            const wave = this.waves[0];
            this.waves.shift();
            const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, wave.waveUUID);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
              if (err) {
                console.error('LevelLayerUI', " tick load wave prefab err", err);
                return;
              }

              const waveComp = instantiate(prefab).getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                error: Error()
              }), Wave) : Wave);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).waveManager.addWaveByLevel(waveComp, wave.position);
            });
          }

          while (this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {
            const event = this.events[0];
            this.events.shift();
            this.enableEvents.push(event);
          }

          for (let i = this.enableEvents.length - 1; i >= 0; i--) {
            const event = this.enableEvents[i];
            let condResult = true;

            for (let cond of event.conditions) {}

            if (condResult) {
              this.enableEvents.splice(i, 1);

              for (let trigger of event.triggers) {
                switch (trigger._type) {
                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log:
                    console.log("LevelLayerUI", "trigger log", trigger.message);
                    break;

                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio:
                    break;

                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave:
                    const waveTriger = trigger;
                    const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, waveTriger.waveUUID);
                    (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                      if (err) {
                        console.error('LevelLayerUI', " tick load wave prefab err", err);
                        return;
                      }

                      const waveComp = instantiate(prefab).getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                        error: Error()
                      }), Wave) : Wave);
                      (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                        error: Error()
                      }), GameIns) : GameIns).waveManager.addWaveByLevel(waveComp, new Vec2(event.position.x, this.node.position.y));
                    });
                    break;
                }
              }
            }
          }
        }

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6f335cfc6bb419dfa35ef3d6f0b4b8155ff91549.js.map