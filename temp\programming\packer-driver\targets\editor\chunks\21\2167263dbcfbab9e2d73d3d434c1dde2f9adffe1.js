System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Prefab, CCString, assetManager, CCInteger, LevelEditorElemUI, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _dec4, _dec5, _dec6, _dec7, _dec8, _class4, _class5, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, executeInEditMode, LevelEditorWaveParam, LevelEditorWaveUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLevelDataWave(extras) {
    _reporterNs.report("LevelDataWave", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorElemUI(extras) {
    _reporterNs.report("LevelEditorElemUI", "./LevelEditorElemUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Prefab = _cc.Prefab;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      CCInteger = _cc.CCInteger;
    }, function (_unresolved_2) {
      LevelEditorElemUI = _unresolved_2.LevelEditorElemUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "537288oh9NMn5Aqmf567TyI", "LevelEditorWaveUI", undefined);

      __checkObsolete__(['_decorator', 'Enum', 'CCFloat', 'Component', 'JsonAsset', 'Node', 'Prefab', 'Slider', 'Vec3', 'ValueType', 'CCBoolean', 'CCString', 'Asset', 'resources', 'assetManager', 'AssetManager', 'Sprite', 'SpriteFrame', 'SpriteAtlas', 'math', 'instantiate', 'Vec2', 'CCInteger']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("LevelEditorWaveParam", LevelEditorWaveParam = (_dec = ccclass('LevelEditorWaveParam'), _dec2 = property(CCString), _dec3 = property(CCFloat), _dec(_class = (_class2 = class LevelEditorWaveParam {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor, this);

          _initializerDefineProperty(this, "value", _descriptor2, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "name", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "value", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));

      _export("LevelEditorWaveUI", LevelEditorWaveUI = (_dec4 = ccclass('LevelEditorWaveUI'), _dec5 = executeInEditMode(), _dec6 = property(Prefab), _dec7 = property(CCInteger), _dec8 = property([LevelEditorWaveParam]), _dec4(_class4 = _dec5(_class4 = (_class5 = class LevelEditorWaveUI extends (_crd && LevelEditorElemUI === void 0 ? (_reportPossibleCrUseOfLevelEditorElemUI({
        error: Error()
      }), LevelEditorElemUI) : LevelEditorElemUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "wavePrefab", _descriptor3, this);

          _initializerDefineProperty(this, "planeID", _descriptor4, this);

          // map property name to value
          _initializerDefineProperty(this, "params", _descriptor5, this);
        }

        initByLevelData(data) {
          super.initByLevelData(data);
          this.planeID = data.planeID;
          this.params = [];

          if (data.params) {
            for (let k in data.params) {
              var param = new LevelEditorWaveParam();
              param.name = k;
              param.value = data.params[k];
              this.params.push(param);
            }
          }

          if (data.waveUUID != "") {
            assetManager.loadAny({
              uuid: data.waveUUID
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorLayerUI initByLevelData load wave prefab err", err);
                return;
              }

              this.wavePrefab = prefab;
            });
          }
        }

        fillLevelData(data) {
          var _this$wavePrefab$uuid, _this$wavePrefab;

          super.fillLevelData(data);
          data.planeID = this.planeID;
          data.waveUUID = (_this$wavePrefab$uuid = (_this$wavePrefab = this.wavePrefab) == null ? void 0 : _this$wavePrefab.uuid) != null ? _this$wavePrefab$uuid : "";
          data.params = {};
          this.params.forEach(param => {
            data.params[param.name] = param.value;
          });
        }

      }, (_descriptor3 = _applyDecoratedDescriptor(_class5.prototype, "wavePrefab", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class5.prototype, "planeID", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "params", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2167263dbcfbab9e2d73d3d434c1dde2f9adffe1.js.map