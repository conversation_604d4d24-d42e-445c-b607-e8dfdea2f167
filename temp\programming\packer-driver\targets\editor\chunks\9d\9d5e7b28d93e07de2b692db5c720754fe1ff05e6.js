System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, DataMgr, EventMgr, MyApp, ButtonPlus, PlaneUIEvent, TabStatus, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, BagItem;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/scripts/Data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/scripts/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../PlaneEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }, function (_unresolved_6) {
      PlaneUIEvent = _unresolved_6.PlaneUIEvent;
    }, function (_unresolved_7) {
      TabStatus = _unresolved_7.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ba4ccxmS+JKWLAx1Q7EdsGk", "BagItem", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BagItem", BagItem = (_dec = ccclass('BagItem'), _dec2 = property(Node), _dec3 = property(Node), _dec(_class = (_class2 = class BagItem extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "selectedIcon", _descriptor, this);

          _initializerDefineProperty(this, "mask", _descriptor2, this);

          this._planeEquipInfo = null;
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
        }

        onLoad() {
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onClick, this);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onClick() {
          if (!this._planeEquipInfo) return;

          if (this._tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Merge) {
            if (this.mask.active && !this.selectedIcon) {
              return;
            }

            if (!this.mask.active && (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.isFull()) {
              return;
            }
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).BagItemClick, this._planeEquipInfo);
        }

        updateMergeStatus() {
          if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.size() > 0) {
            const info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.getByGuid(this._planeEquipInfo.guid);

            if (info) {
              this.selectedIcon.active = true;
              this.mask.active = true;
            } else {
              this.selectedIcon.active = false;
              this.mask.active = !(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isCanCombineWith(this._planeEquipInfo);
            }
          } else {
            this.selectedIcon.active = false;
            this.mask.active = false;
          }
        }

        renderPlanePart(planeEquipInfo, tabStatus) {
          this._tabStatus = tabStatus;
          this._planeEquipInfo = planeEquipInfo;
          this.selectedIcon.active = false;
          this.mask.active = false;

          if (tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Merge) {
            this.updateMergeStatus();
          }

          const itemCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEquip.get(this._planeEquipInfo.item_id);
          this.getComponentInChildren(Label).string = (itemCfg == null ? void 0 : itemCfg.name) + `(品质:${itemCfg == null ? void 0 : itemCfg.quality})`;
        }

        onRenderItem(item) {
          var _lubanTables$TbItem$g;

          this.selectedIcon.active = false;
          this.mask.active = false;
          this.getComponentInChildren(Label).string = (_lubanTables$TbItem$g = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbItem.get(item.item_id)) == null ? void 0 : _lubanTables$TbItem$g.name;
          this._planeEquipInfo = null;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "selectedIcon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "mask", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9d5e7b28d93e07de2b692db5c720754fe1ff05e6.js.map