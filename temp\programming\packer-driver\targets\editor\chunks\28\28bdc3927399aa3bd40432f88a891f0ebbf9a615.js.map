{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts"], "names": ["_decorator", "Component", "Sprite", "Node", "tween", "UITransform", "Tween", "v3", "instantiate", "UIOpacity", "GameIns", "Tools", "EnemyAnim", "GameConst", "ccclass", "property", "EnemyPlaneRole", "_data", "_curUId", "_anim", "_curAnim", "preLoadUI", "data", "_initUI", "init", "target", "param", "_reset", "isAm", "playAnim", "node", "getComponent", "opacity", "white", "pedestal", "spriteFrame", "stopAnim", "updateGameLogic", "dt", "isPreload", "role", "id", "destroy", "pf", "enemyManager", "getPlaneRole", "image", "animNode", "<PERSON><PERSON><PERSON><PERSON>", "extraParam", "active", "setPlaneFrame", "frameTime", "ActionFrameTime", "i", "length", "tailFire", "tailFireArr", "fireNode", "addComponent", "anchorY", "push", "stopAllByTarget", "setPosition", "setContentSize", "angle", "to", "scale", "repeatF<PERSON><PERSON>", "start", "setEventCallback", "eventName", "callback", "setAnimEventCall", "startAttack", "attackOver", "anim<PERSON><PERSON>", "log", "pauseAnim", "resumeAnim"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;;AACnGC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,S;;AACEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAGTgB,c,WAChBD,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACb,MAAD,C,UAGRa,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAAC,CAACZ,IAAD,CAAD,C,EAXZW,O,qBAAD,MACqBE,cADrB,SAC4Cf,SAD5C,CACsD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAalDgB,KAbkD,GAa9B,IAb8B;AAAA,eAelDC,OAfkD,GAexC,CAAC,CAfuC;AAAA,eAgBlDC,KAhBkD,GAgB1C,IAhB0C;AAAA,eAiBlDC,QAjBkD,GAiBvC,EAjBuC;AAAA;;AAmBlD;AACJ;AACA;AACA;AACIC,QAAAA,SAAS,CAACC,IAAD,EAAO;AACZ,cAAI,KAAKL,KAAL,GAAaK,IAAjB,EAAuB;AACnB,iBAAKC,OAAL,CAAa,IAAb;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACc,cAAJC,IAAI,CAACF,IAAD,EAAOG,MAAP,EAAeC,KAAK,GAAG,EAAvB,EAA2B;AACjC,eAAKC,MAAL;;AACA,eAAKV,KAAL,GAAaK,IAAb;;AAEA,cAAI,CAAC,KAAKL,KAAL,CAAWW,IAAhB,EAAsB;AAClB,kBAAM,KAAKL,OAAL,EAAN;AACH,WAFD,MAEO;AACH,gBAAI,KAAKJ,KAAT,EAAgB;AACZ,mBAAKU,QAAL,CAAc,OAAd;AACA,mBAAKV,KAAL,CAAWW,IAAX,CAAgBC,YAAhB,CAA6BtB,SAA7B,EAAwCuB,OAAxC,GAAkD,GAAlD;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIL,QAAAA,MAAM,GAAG;AACL,eAAKM,KAAL,CAAWH,IAAX,CAAgBC,YAAhB,CAA6BtB,SAA7B,EAAwCuB,OAAxC,GAAkD,CAAlD;AACA,eAAKE,QAAL,CAAcC,WAAd,GAA4B,IAA5B;AACA,eAAKf,QAAL,GAAgB,EAAhB;AACA,eAAKgB,QAAL;AACA,cAAI,KAAKjB,KAAT,EAAgB,KAAKA,KAAL,CAAWW,IAAX,CAAgBC,YAAhB,CAA6BtB,SAA7B,EAAwCuB,OAAxC,GAAkD,CAAlD;AACnB;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,eAAe,CAACC,EAAD,EAAK,CAEnB;AAED;AACJ;AACA;AACA;;;AACiB,cAAPf,OAAO,CAACgB,SAAS,GAAG,KAAb,EAAoB;AAC7B,cAAI,KAAKtB,KAAL,CAAWW,IAAf,EAAqB;AACjB,iBAAKY,IAAL,CAAUL,WAAV,GAAwB,IAAxB;AACA,iBAAKF,KAAL,CAAWE,WAAX,GAAyB,IAAzB,CAFiB,CAGjB;;AAEA,gBAAI,KAAKjB,OAAL,KAAiB,KAAKD,KAAL,CAAWwB,EAA5B,IAAkC,KAAKtB,KAA3C,EAAkD;AAC9C,mBAAKA,KAAL,CAAWW,IAAX,CAAgBC,YAAhB,CAA6BtB,SAA7B,EAAwCuB,OAAxC,GAAkD,GAAlD;AACH,aAFD,MAEO;AACH,mBAAKd,OAAL,GAAe,KAAKD,KAAL,CAAWwB,EAA1B;;AACA,kBAAI,KAAKtB,KAAT,EAAgB;AACZ,qBAAKA,KAAL,CAAWW,IAAX,CAAgBY,OAAhB;;AACA,qBAAKvB,KAAL,GAAa,IAAb;AACH;;AACD,kBAAIwB,EAAE,GAAG,MAAM;AAAA;AAAA,sCAAQC,YAAR,CAAqBC,YAArB,CAAkC,KAAK5B,KAAL,CAAW6B,KAA7C,CAAf;AACA,oBAAMC,QAAQ,GAAGvC,WAAW,CAACmC,EAAD,CAA5B;AACA,mBAAKb,IAAL,CAAUkB,QAAV,CAAmBD,QAAnB;AACA,mBAAK5B,KAAL,GAAa4B,QAAQ,CAAChB,YAAT;AAAA;AAAA,yCAAb;;AACA,mBAAKZ,KAAL,CAAWK,IAAX,CAAgB,KAAKP,KAAL,CAAWgC,UAA3B;AACH;AACJ,WAnBD,MAmBO;AACH,gBAAI,KAAK9B,KAAT,EAAgB,KAAKA,KAAL,CAAWW,IAAX,CAAgBoB,MAAhB,GAAyB,KAAzB;AAChB;AAAA;AAAA,oCAAQN,YAAR,CAAqBO,aAArB,CAAmC,KAAKX,IAAxC,EAA8C,KAAKvB,KAAL,CAAW6B,KAAzD;AACA;AAAA;AAAA,oCAAQF,YAAR,CAAqBO,aAArB,CAAmC,KAAKlB,KAAxC,EAA+C,KAAKhB,KAAL,CAAW6B,KAA1D;AAEA,kBAAMM,SAAS,GAAG;AAAA;AAAA,wCAAUC,eAA5B;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrC,KAAL,CAAWgC,UAAX,CAAsBM,MAA1C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,oBAAM5B,KAAK,GAAG,KAAKT,KAAL,CAAWgC,UAAX,CAAsBK,CAAtB,CAAd;AACA,kBAAIE,QAAa,GAAG,KAAKC,WAAL,CAAiBH,CAAjB,CAApB;;AACA,kBAAI,CAACE,QAAL,EAAe;AACXA,gBAAAA,QAAQ,GAAG,IAAIrD,IAAJ,EAAX;AACA,qBAAKuD,QAAL,CAAcV,QAAd,CAAuBQ,QAAvB;AACAA,gBAAAA,QAAQ,CAACG,YAAT,CAAsBzD,MAAtB;AACAsD,gBAAAA,QAAQ,CAACzB,YAAT,CAAsB1B,WAAtB,EAAmCuD,OAAnC,GAA6C,CAA7C;AACA,qBAAKH,WAAL,CAAiBI,IAAjB,CAAsBL,QAAtB;AACH;;AACDlD,cAAAA,KAAK,CAACwD,eAAN,CAAsBN,QAAtB;AACAA,cAAAA,QAAQ,CAACN,MAAT,GAAkB,IAAlB;AACA;AAAA;AAAA,sCAAQN,YAAR,CAAqBO,aAArB,CAAmCK,QAAQ,CAACzB,YAAT,CAAsB7B,MAAtB,CAAnC,EAAkE,SAASwB,KAAK,CAAC,CAAD,CAAhF;AACA8B,cAAAA,QAAQ,CAACO,WAAT,CAAqBrC,KAAK,CAAC,CAAD,CAA1B,EAA+BA,KAAK,CAAC,CAAD,CAApC;AACA8B,cAAAA,QAAQ,CAACzB,YAAT,CAAsB1B,WAAtB,EAAmC2D,cAAnC,CAAkDtC,KAAK,CAAC,CAAD,CAAvD,EAA4DA,KAAK,CAAC,CAAD,CAAjE;AACA8B,cAAAA,QAAQ,CAACS,KAAT,GAAiBvC,KAAK,CAAC,CAAD,CAAL,IAAY,CAA7B;AAIAtB,cAAAA,KAAK,CAACoD,QAAD,CAAL,CACKU,EADL,CACQd,SAAS,GAAG1B,KAAK,CAAC,CAAD,CADzB,EAC8B;AAAEyC,gBAAAA,KAAK,EAAE5D,EAAE,CAACmB,KAAK,CAAC,CAAD,CAAN,EAAUA,KAAK,CAAC,CAAD,CAAf;AAAX,eAD9B,EAEKwC,EAFL,CAEQd,SAAS,GAAG1B,KAAK,CAAC,CAAD,CAFzB,EAE8B;AAAEyC,gBAAAA,KAAK,EAAE5D,EAAE,CAAC,CAAD,EAAG,CAAH;AAAX,eAF9B,EAGK6D,aAHL,GAIKC,KAJL;AAKH;;AAED,iBAAK,IAAIf,CAAC,GAAG,KAAKrC,KAAL,CAAWgC,UAAX,CAAsBM,MAAnC,EAA2CD,CAAC,GAAG,KAAKG,WAAL,CAAiBF,MAAhE,EAAwED,CAAC,EAAzE,EAA6E;AACzE,oBAAME,QAAQ,GAAG,KAAKC,WAAL,CAAiBH,CAAjB,CAAjB;AACAhD,cAAAA,KAAK,CAACwD,eAAN,CAAsBN,QAAtB;AACAA,cAAAA,QAAQ,CAACN,MAAT,GAAkB,KAAlB;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIoB,QAAAA,gBAAgB,CAACC,SAAD,EAAYC,QAAZ,EAAsB;AAClC,eAAKrD,KAAL,IAAc,KAAKA,KAAL,CAAWsD,gBAAX,CAA4BF,SAA5B,EAAuCC,QAAvC,CAAd;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,WAAW,GAAG,CAEb;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG,CAEZ;AAID;AACJ;AACA;AACA;AACA;AACA;;;AACI9C,QAAAA,QAAQ,CAAC+C,QAAD,EAAWJ,QAAQ,GAAG,IAAtB,EAA4B;AAChC,cAAI,CAAC,KAAKvD,KAAL,CAAWW,IAAhB,EAAsB,OAAO,KAAP;;AACtB,cAAI,KAAKR,QAAL,KAAkBwD,QAAtB,EAAgC;AAC5B;AAAA;AAAA,gCAAMC,GAAN,CAAU,WAAV,EAAuBD,QAAvB;AACA,mBAAO,IAAP;AACH;;AACD,cAAI,KAAKzD,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWU,QAAX,CAAoB+C,QAApB,EAA8BJ,QAA9B;;AACA,mBAAO,IAAP;AACH;;AACD;AAAA;AAAA,8BAAMK,GAAN,CAAU,cAAV,EAA0BD,QAA1B;AACA,iBAAO,KAAP;AACH;AAED;AACJ;AACA;;;AACIE,QAAAA,SAAS,GAAG;AACR,eAAK3D,KAAL,IAAc,KAAKA,KAAL,CAAW2D,SAAX,EAAd;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAK5D,KAAL,IAAc,KAAKA,KAAL,CAAW4D,UAAX,EAAd;AACH;AAED;AACJ;AACA;;;AACI3C,QAAAA,QAAQ,GAAG;AACP,eAAKjB,KAAL,IAAc,KAAKA,KAAL,CAAWiB,QAAX,EAAd;AACH;;AApMiD,O;;;;;iBAEhC,I;;;;;;;iBAEJ,I;;;;;;;iBAEC,I;;;;;;;iBAGC,I;;;;;;;iBAEK,E", "sourcesContent": ["import { _decorator, Component, Sprite, Vec2, Node, tween, v2, UITransform, Tween, v3, Prefab, instantiate, UIOpacity} from \"cc\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { Tools } from \"../../../utils/Tools\";\r\nimport EnemyAnim from \"./EnemyAnim\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport { EnemyUIData } from \"../../../data/EnemyData\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass\r\nexport default class EnemyPlaneRole extends Component {\r\n    @property(Sprite)\r\n    pedestal:Sprite = null;\r\n    @property(Sprite)\r\n    role:Sprite = null;\r\n    @property(Sprite)\r\n    white:Sprite = null;\r\n\r\n    @property(Node)\r\n    fireNode:Node = null;\r\n    @property([Node])\r\n    tailFireArr:Node[] = [];\r\n\r\n    _data:EnemyUIData = null;\r\n\r\n    _curUId = -1;\r\n    _anim = null;\r\n    _curAnim = \"\";\r\n\r\n    /**\r\n     * 预加载 UI\r\n     * @param {Object} data 敌机数据\r\n     */\r\n    preLoadUI(data) {\r\n        if (this._data = data) {\r\n            this._initUI(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化敌机\r\n     * @param {Object} data 敌机数据\r\n     * @param {Object} target 目标对象\r\n     * @param {string} param 参数\r\n     */\r\n    async init(data, target, param = \"\") {\r\n        this._reset();\r\n        this._data = data;\r\n\r\n        if (!this._data.isAm) {\r\n            await this._initUI();\r\n        } else {\r\n            if (this._anim) {\r\n                this.playAnim(\"idle1\");\r\n                this._anim.node.getComponent(UIOpacity).opacity = 255;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 重置敌机状态\r\n     */\r\n    _reset() {\r\n        this.white.node.getComponent(UIOpacity).opacity = 0;\r\n        this.pedestal.spriteFrame = null;\r\n        this._curAnim = \"\";\r\n        this.stopAnim();\r\n        if (this._anim) this._anim.node.getComponent(UIOpacity).opacity = 0;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 时间增量\r\n     */\r\n    updateGameLogic(dt) {\r\n\r\n    }\r\n\r\n    /**\r\n     * 初始化 UI\r\n     * @param {boolean} isPreload 是否预加载\r\n     */\r\n    async _initUI(isPreload = false) {\r\n        if (this._data.isAm) {\r\n            this.role.spriteFrame = null;\r\n            this.white.spriteFrame = null;\r\n            // this._winkAct = tween().to(0, { opacity: 204 }).to(3 * GameConst.ActionFrameTime, { opacity: 0 });\r\n\r\n            if (this._curUId === this._data.id && this._anim) {\r\n                this._anim.node.getComponent(UIOpacity).opacity = 255;\r\n            } else {\r\n                this._curUId = this._data.id;\r\n                if (this._anim) {\r\n                    this._anim.node.destroy();\r\n                    this._anim = null;\r\n                }\r\n                let pf = await GameIns.enemyManager.getPlaneRole(this._data.image);\r\n                const animNode = instantiate(pf);\r\n                this.node.addChild(animNode);\r\n                this._anim = animNode.getComponent(EnemyAnim);\r\n                this._anim.init(this._data.extraParam);\r\n            }\r\n        } else {\r\n            if (this._anim) this._anim.node.active = false;\r\n            GameIns.enemyManager.setPlaneFrame(this.role, this._data.image);\r\n            GameIns.enemyManager.setPlaneFrame(this.white, this._data.image);\r\n\r\n            const frameTime = GameConst.ActionFrameTime;\r\n            for (let i = 0; i < this._data.extraParam.length; i++) {\r\n                const param = this._data.extraParam[i];\r\n                let tailFire:Node = this.tailFireArr[i];\r\n                if (!tailFire) {\r\n                    tailFire = new Node();\r\n                    this.fireNode.addChild(tailFire);\r\n                    tailFire.addComponent(Sprite);\r\n                    tailFire.getComponent(UITransform).anchorY = 0;\r\n                    this.tailFireArr.push(tailFire);\r\n                }\r\n                Tween.stopAllByTarget(tailFire);\r\n                tailFire.active = true;\r\n                GameIns.enemyManager.setPlaneFrame(tailFire.getComponent(Sprite), \"fire\" + param[0]);\r\n                tailFire.setPosition(param[1], param[2]);\r\n                tailFire.getComponent(UITransform).setContentSize(param[3], param[4]);\r\n                tailFire.angle = param[7] || 0;\r\n\r\n\r\n            \r\n                tween(tailFire)\r\n                    .to(frameTime * param[5], { scale: v3(param[6],param[6]) })\r\n                    .to(frameTime * param[5], { scale: v3(1,1) })\r\n                    .repeatForever()\r\n                    .start();\r\n            }\r\n\r\n            for (let i = this._data.extraParam.length; i < this.tailFireArr.length; i++) {\r\n                const tailFire = this.tailFireArr[i];\r\n                Tween.stopAllByTarget(tailFire);\r\n                tailFire.active = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置动画事件回调\r\n     * @param {string} eventName 事件名称\r\n     * @param {Function} callback 回调函数\r\n     */\r\n    setEventCallback(eventName, callback) {\r\n        this._anim && this._anim.setAnimEventCall(eventName, callback);\r\n    }\r\n\r\n    /**\r\n     * 开始攻击\r\n     */\r\n    startAttack() {\r\n        \r\n    }\r\n\r\n    /**\r\n     * 攻击结束\r\n     */\r\n    attackOver() {\r\n        \r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 播放动画\r\n     * @param {string} animName 动画名称\r\n     * @param {Function} [callback] 动画结束回调\r\n     * @returns {boolean} 是否成功播放\r\n     */\r\n    playAnim(animName, callback = null) {\r\n        if (!this._data.isAm) return false;\r\n        if (this._curAnim === animName) {\r\n            Tools.log(\"save anim\", animName);\r\n            return true;\r\n        }\r\n        if (this._anim) {\r\n            this._anim.playAnim(animName, callback);\r\n            return true;\r\n        }\r\n        Tools.log(\"anim is null\", animName);\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 暂停动画\r\n     */\r\n    pauseAnim() {\r\n        this._anim && this._anim.pauseAnim();\r\n    }\r\n\r\n    /**\r\n     * 恢复动画\r\n     */\r\n    resumeAnim() {\r\n        this._anim && this._anim.resumeAnim();\r\n    }\r\n\r\n    /**\r\n     * 停止动画\r\n     */\r\n    stopAnim() {\r\n        this._anim && this._anim.stopAnim();\r\n    }\r\n}"]}