{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts"], "names": ["BossManager", "NodePool", "sp", "SpriteAtlas", "Node", "SingletonBase", "GameIns", "GameConst", "Tools", "BossData", "UnitData", "BattleLayer", "BossEntity", "GameFunc", "MyApp", "GameResourceList", "constructor", "_boss<PERSON><PERSON>s", "Map", "_unitDatas", "_bossUnitDatas", "_bossDataMap", "bossAtlasMap", "unitAtlas", "_bossArr", "fireParticle", "skelDataMap", "boss<PERSON><PERSON><PERSON><PERSON>sh", "_fireParticlePool", "_isBossWarning", "_smokeSkelData", "_mainStage", "_subStage", "m_bossResStage", "initConfig", "bossTbDatas", "lubanTables", "TbBoss", "getDataList", "bossTbData", "bossData", "loadJson", "bossList", "get", "id", "set", "push", "unitTbDatas", "TbUnit", "unitTbData", "unitData", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "spine_boss_smoke", "SkeletonData", "error", "data", "checkLoadFinish", "loadAsync", "atlas_boss_unit", "mainReset", "subReset", "<PERSON><PERSON>", "releaseAssetByForce", "boss", "<PERSON><PERSON><PERSON><PERSON>", "node", "parent", "setTimeout", "destroy", "for<PERSON>ach", "key", "clear", "atlas", "loadBossRes", "bossType", "bossId", "callback", "isPreload", "warn", "bossDatas", "getBossDatas", "unitId", "units", "getUnitData", "anim", "skelData", "updateGameLogic", "deltaTime", "i", "length", "removeAble", "remove<PERSON>oss", "bossFightStart", "isDead", "startBattle", "addBoss", "_createBoss", "y", "arr<PERSON><PERSON><PERSON>", "getBossAtlas", "atlasName", "setBossFrame", "sprite", "frameName", "spriteFrame", "getSpriteFrame", "showBossWarning", "bosses", "isBossOver", "isBossDead", "smokeSkelData", "createBossById", "me", "addEnemy", "addComponent", "init", "new_uuid", "uuid", "pause<PERSON><PERSON>", "pause", "<PERSON><PERSON><PERSON>", "resume"], "mappings": ";;;0OAca<PERSON>,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdOC,MAAAA,Q,OAAAA,Q;AAAqBC,MAAAA,E,OAAAA,E;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACzDC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;AAAwBC,MAAAA,Q,iBAAAA,Q;;AAC1BC,MAAAA,W;;AACAC,MAAAA,U;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,kBAAAA,K;;AACFC,MAAAA,gB;;;;;;;;;6BAGMf,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAoD;AAkBvDgB,QAAAA,WAAW,GAAG;AACV;AADU,eAjBdC,UAiBc,GAjBwB,IAAIC,GAAJ,EAiBxB;AAAA,eAhBdC,UAgBc,GAhBsB,IAAID,GAAJ,EAgBtB;AAAA,eAfdE,cAec,GAf8B,IAAIF,GAAJ,EAe9B;AAAA,eAddG,YAcc,GAdmB,IAAIH,GAAJ,EAcnB;AAAA,eAbdI,YAac,GAb2B,IAAIJ,GAAJ,EAa3B;AAAA,eAZdK,SAYc,GAZU,IAYV;AAAA,eAXdC,QAWc,GAXS,EAWT;AAAA,eAVdC,YAUc,GAVM,IAUN;AAAA,eATdC,WASc,GAT8B,IAAIR,GAAJ,EAS9B;AAAA,eARdS,aAQc,GARW,KAQX;AAAA,eAPdC,iBAOc,GAPgB,IAAI3B,QAAJ,EAOhB;AAAA,eANd4B,cAMc,GANY,KAMZ;AAAA,eALdC,cAKc,GALmB,IAKnB;AAAA,eAJdC,UAIc,GAJO,CAAC,CAIR;AAAA,eAHdC,SAGc,GAHM,CAAC,CAGP;AAAA,eAFdC,cAEc,GAFW,CAEX;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAE;AACR,cAAIC,WAAW,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,WAAzB,EAAlB;;AACA,eAAK,IAAIC,UAAT,IAAuBJ,WAAvB,EAAoC;AAChC,kBAAMK,QAAQ,GAAG;AAAA;AAAA,uCAAjB;AACAA,YAAAA,QAAQ,CAACC,QAAT,CAAkBF,UAAlB;;AACA,gBAAIG,QAAQ,GAAG,KAAKzB,UAAL,CAAgB0B,GAAhB,CAAoBH,QAAQ,CAACI,EAA7B,CAAf;;AACA,gBAAI,CAACF,QAAL,EAAe;AACXA,cAAAA,QAAQ,GAAG,EAAX;;AACA,mBAAKzB,UAAL,CAAgB4B,GAAhB,CAAoBL,QAAQ,CAACI,EAA7B,EAAiCF,QAAjC;AACH;;AACDA,YAAAA,QAAQ,CAACI,IAAT,CAAcN,QAAd;AACH;;AAED,cAAIO,WAAW,GAAG;AAAA;AAAA,8BAAMX,WAAN,CAAkBY,MAAlB,CAAyBV,WAAzB,EAAlB;;AACA,eAAK,IAAIW,UAAT,IAAuBF,WAAvB,EAAoC;AAChC,kBAAMG,QAAQ,GAAG;AAAA;AAAA,uCAAjB;AACAA,YAAAA,QAAQ,CAACT,QAAT,CAAkBQ,UAAlB;;AACA,iBAAK9B,UAAL,CAAgB0B,GAAhB,CAAoBK,QAAQ,CAACN,EAA7B,EAAiCM,QAAjC;AACH;AACJ;;AAGY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,oDAAiBC,gBAAnC,EAAqDtD,EAAE,CAACuD,YAAxD,EAAsE,CAACC,KAAD,EAAOC,IAAP,KAAgB;AAClF,iBAAK7B,cAAL,GAAsB6B,IAAtB;AACA;AAAA;AAAA,oCAAQP,aAAR,CAAsBQ,eAAtB;AACH,WAHD;AAKA;AAAA;AAAA,kCAAQR,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,eAAK9B,SAAL,GAAiB,MAAM;AAAA;AAAA,8BAAM+B,MAAN,CAAaO,SAAb,CAAuB;AAAA;AAAA,oDAAiBC,eAAxC,EAAwD3D,WAAxD,CAAvB;AACA;AAAA;AAAA,kCAAQiD,aAAR,CAAsBQ,eAAtB;AACH;;AAGDG,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL;AACA,eAAKjC,UAAL,GAAkB,CAAC,CAAnB;;AAEA,cAAI,CAAC;AAAA;AAAA,sCAAUkC,KAAf,EAAsB;AAClB,gBAAI,KAAKnC,cAAT,EAAyB;AACrB;AAAA;AAAA,kCAAMwB,MAAN,CAAaY,mBAAb,CAAiC,KAAKpC,cAAtC;AACA,mBAAKA,cAAL,GAAsB,IAAtB;AACH;;AACD,gBAAI,KAAKP,SAAT,EAAoB;AAChB;AAAA;AAAA,kCAAM+B,MAAN,CAAaY,mBAAb,CAAiC,KAAK3C,SAAtC;AACA,mBAAKA,SAAL,GAAiB,IAAjB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIyC,QAAAA,QAAQ,GAAG;AACP,eAAK,MAAMG,IAAX,IAAmB,KAAK3C,QAAxB,EAAkC;AAC9B2C,YAAAA,IAAI,CAACC,WAAL;AACAD,YAAAA,IAAI,CAACE,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAC,YAAAA,UAAU,CAAC,MAAM;AACbJ,cAAAA,IAAI,CAACE,IAAL,CAAUG,OAAV;AACH,aAFS,EAEP,IAFO,CAAV;AAGH;;AACD,eAAKhD,QAAL,GAAgB,EAAhB,CARO,CAUP;;AACA,eAAKE,WAAL,CAAiB+C,OAAjB,CAAyB,CAACd,IAAD,EAAOe,GAAP,KAAe;AACpC;AAAA;AAAA,gCAAMpB,MAAN,CAAaY,mBAAb,CAAiCP,IAAjC;AACH,WAFD;AAGA,eAAKjC,WAAL,CAAiBiD,KAAjB,GAdO,CAgBP;;AACA,eAAKrD,YAAL,CAAkBmD,OAAlB,CAA0B,CAACG,KAAD,EAAQF,GAAR,KAAgB;AACtC;AAAA;AAAA,gCAAMpB,MAAN,CAAaY,mBAAb,CAAiCU,KAAjC;AACH,WAFD;AAGA,eAAKtD,YAAL,CAAkBqD,KAAlB;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACqB,cAAXE,WAAW,CAACC,QAAD,EAAmBC,MAAnB,EAAmCC,QAAyB,GAAG,IAA/D,EAAqEC,SAAkB,GAAG,KAA1F,EAAmH;AAChI,cAAI;AACA;AAAA;AAAA,gCAAMC,IAAN,CAAW,wBAAX,EAAqCJ,QAArC,EAA+CC,MAA/C;;AAEA,gBAAI,CAACE,SAAL,EAAgB;AACZ,mBAAKtD,aAAL,GAAqB,KAArB,CADY,CAEZ;AACH;;AAED,oBAAQmD,QAAR;AACI,mBAAK,GAAL;AACI;AACA,sBAAMK,SAAS,GAAG,KAAKC,YAAL,CAAkBL,MAAlB,CAAlB;;AACA,qBAAK,MAAMvC,QAAX,IAAuB2C,SAAvB,EAAkC;AAC9B,uBAAK,MAAME,MAAX,IAAqB7C,QAAQ,CAAC8C,KAA9B,EAAqC;AACjC,0BAAMpC,QAAQ,GAAG,KAAKqC,WAAL,CAAiBF,MAAjB,CAAjB;;AACA,wBAAInC,QAAQ,CAACsC,IAAb,EAAmB;AACf,4BAAMC,QAAQ,GAAG,MAAM;AAAA;AAAA,0CAAMnC,MAAN,CAAaO,SAAb,CAAuB,gBAAcX,QAAQ,CAACsC,IAA9C,EAAmDtF,EAAE,CAACuD,YAAtD,CAAvB;;AACA,0BAAI,CAACgC,QAAL,EAAe;AACX,4BAAI,CAACR,SAAL,EAAgB;AACZ;AACI,+BAAKJ,WAAL,CAAiBC,QAAjB,EAA2BC,MAA3B,EAAmCC,QAAnC,EAFQ,CAGZ;AACH;;AACD,+BAAO,KAAP;AACH;;AACD,0BAAI,CAACC,SAAL,EAAgB;AACZ,6BAAKvD,WAAL,CAAiBmB,GAAjB,CAAqBK,QAAQ,CAACsC,IAA9B,EAAoCC,QAApC;AACH;AACJ;AACJ;AACJ;;AACD;;AAEJ;AACI;AAAA;AAAA,oCAAM/B,KAAN,CAAY,oBAAZ,EAAkCoB,QAAlC;AACA,uBAAO,KAAP;AA3BR;;AA8BA,gBAAI,CAACG,SAAL,EAAgB;AACZ,mBAAKtD,aAAL,GAAqB,IAArB,CADY,CAEZ;AACH;;AAED,gBAAIqD,QAAJ,EAAc;AACVA,cAAAA,QAAQ;AACX;;AAED,mBAAO,IAAP;AACH,WAhDD,CAgDE,OAAOtB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAY,+BAAZ,EAA6CA,KAA7C;;AACA,gBAAI,CAACuB,SAAL,EAAgB;AACZ;AACI,mBAAKJ,WAAL,CAAiBC,QAAjB,EAA2BC,MAA3B,EAAmCC,QAAnC,EAFQ,CAGZ;AACH;;AACD,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIU,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpE,QAAL,CAAcqE,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC3C,kBAAMzB,IAAI,GAAG,KAAK3C,QAAL,CAAcoE,CAAd,CAAb;;AACA,gBAAIzB,IAAI,CAAC2B,UAAT,EAAqB;AACjB,mBAAKC,UAAL,CAAgB5B,IAAhB;AACAyB,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHzB,cAAAA,IAAI,CAACuB,eAAL,CAAqBC,SAArB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIK,QAAAA,cAAc,GAAG;AACb,eAAK,MAAM7B,IAAX,IAAmB,KAAK3C,QAAxB,EAAkC;AAC9B,gBAAI,CAAC2C,IAAI,CAAC8B,MAAV,EAAkB;AACd9B,cAAAA,IAAI,CAAC+B,WAAL;AACA;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,OAAO,CAACrB,QAAD,EAAmBC,MAAnB,EAAoD;AACvD,kBAAQD,QAAR;AACI,iBAAK,GAAL;AACI,oBAAMK,SAAS,GAAG,KAAKC,YAAL,CAAkBL,MAAlB,CAAlB;AACA,qBAAO,KAAKqB,WAAL,CAAiBjB,SAAjB,CAAP;;AAEJ;AACI;AAAA;AAAA,kCAAMzB,KAAN,CAAY,oBAAZ,EAAkCoB,QAAlC;AACA,qBAAO,IAAP;AAPR;AASH;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,UAAU,CAAC5B,IAAD,EAAiB;AACvBA,UAAAA,IAAI,CAACE,IAAL,CAAUgC,CAAV,GAAc,IAAd;AACA;AAAA;AAAA,8BAAMC,SAAN,CAAgB,KAAK9E,QAArB,EAA+B2C,IAA/B;AACAA,UAAAA,IAAI,CAACE,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAH,UAAAA,IAAI,CAACE,IAAL,CAAUG,OAAV;AACH;AAED;AACJ;AACA;AACA;;;AACsB,cAAZ+B,YAAY,CAACC,SAAD,EAAiD;AAC/D,cAAI5B,KAAK,GAAG,KAAKtD,YAAL,CAAkBqB,GAAlB,CAAsB6D,SAAtB,CAAZ;;AACA,cAAI,CAAC5B,KAAL,EAAY;AACRA,YAAAA,KAAK,GAAG,MAAM;AAAA;AAAA,gCAAMtB,MAAN,CAAaO,SAAb,CAAuB2C,SAAvB,EAAkCrG,WAAlC,CAAd;;AACA,gBAAIyE,KAAJ,EAAW;AACP,mBAAKtD,YAAL,CAAkBuB,GAAlB,CAAsB2D,SAAtB,EAAiC5B,KAAjC;AACH;AACJ;;AACD,iBAAOA,KAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACI6B,QAAAA,YAAY,CAACC,MAAD,EAAiBC,SAAjB,EAAoC;AAC5C,cAAID,MAAM,IAAI,KAAKnF,SAAnB,EAA8B;AAC1BmF,YAAAA,MAAM,CAACE,WAAP,GAAqB,KAAKrF,SAAL,CAAesF,cAAf,CAA8BF,SAA9B,CAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACIG,QAAAA,eAAe,GAAG;AACd,eAAKjF,cAAL,GAAsB,IAAtB;AACH;AAED;AACJ;AACA;;;AACc,YAANkF,MAAM,GAAe;AACrB,iBAAO,KAAKvF,QAAZ;AACH;AAED;AACJ;AACA;;;AACIwF,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKxF,QAAL,CAAcqE,MAAd,KAAyB,CAAhC;AACH;AAED;AACJ;AACA;;;AACIoB,QAAAA,UAAU,GAAY;AAClB,eAAK,MAAM9C,IAAX,IAAmB,KAAK3C,QAAxB,EAAkC;AAC9B,gBAAI,CAAC2C,IAAI,CAAC8B,MAAV,EAAkB;AACd,qBAAO,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACqB,YAAbiB,aAAa,GAA2B;AACxC,iBAAO,KAAKpF,cAAZ;AACH;AAGD;AACJ;AACA;AACA;;;AACIyD,QAAAA,WAAW,CAACF,MAAD,EAAuC;AAC9C,iBAAO,KAAKlE,UAAL,CAAgBwB,GAAhB,CAAoB0C,MAApB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACID,QAAAA,YAAY,CAACL,MAAD,EAAyC;AACjD,iBAAO,KAAK9D,UAAL,CAAgB0B,GAAhB,CAAoBoC,MAApB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIoC,QAAAA,cAAc,CAACpC,MAAD,EAAoC;AAC9C,gBAAMI,SAAS,GAAG,KAAKC,YAAL,CAAkBL,MAAlB,CAAlB;;AACA,cAAII,SAAJ,EAAe;AACX,mBAAO,KAAKiB,WAAL,CAAiBjB,SAAjB,CAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,WAAW,CAACjB,SAAD,EAAoC;AAC3C,gBAAMd,IAAI,GAAG,IAAIjE,IAAJ,CAAS,MAAT,CAAb;AACA;AAAA;AAAA,0CAAYgH,EAAZ,CAAeC,QAAf,CAAwBhD,IAAxB;AAEA,gBAAMF,IAAI,GAAGE,IAAI,CAACiD,YAAL;AAAA;AAAA,uCAAb;AACAnD,UAAAA,IAAI,CAACoD,IAAL,CAAUpC,SAAV;AACAhB,UAAAA,IAAI,CAACqD,QAAL,GAAgB;AAAA;AAAA,oCAASC,IAAzB;;AACA,eAAKjG,QAAL,CAAcsB,IAAd,CAAmBqB,IAAnB;;AAEA,iBAAOA,IAAP;AACH;AAED;AACJ;AACA;;;AACIuD,QAAAA,SAAS,GAAG;AACR,eAAK,MAAMvD,IAAX,IAAmB,KAAK3C,QAAxB,EAAkC;AAC9B,gBAAI,CAAC2C,IAAI,CAAC8B,MAAN,IAAgB9B,IAAI,CAACwD,KAAzB,EAAgC;AAC5BxD,cAAAA,IAAI,CAACwD,KAAL;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAK,MAAMzD,IAAX,IAAmB,KAAK3C,QAAxB,EAAkC;AAC9B,gBAAI,CAAC2C,IAAI,CAAC8B,MAAN,IAAgB9B,IAAI,CAAC0D,MAAzB,EAAiC;AAC7B1D,cAAAA,IAAI,CAAC0D,MAAL;AACH;AACJ;AACJ;;AAnWsD,O", "sourcesContent": ["import { JsonAsset, NodePool, resources, sp, Sprite, SpriteAtlas, Node} from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport BossBase from \"../ui/plane/boss/BossBase\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { BossData, BossUnitData, UnitData } from \"../data/BossData\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport BossEntity from \"../ui/plane/boss/BossEntity\";\r\nimport { GameFunc } from \"../GameFunc\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\n\r\n\r\nexport class BossManager extends SingletonBase<BossManager>{\r\n    _bossDatas: Map<number, BossData[]> = new Map();\r\n    _unitDatas: Map<number, UnitData> = new Map();\r\n    _bossUnitDatas: Map<number, BossUnitData> = new Map();\r\n    _bossDataMap: Map<number, any> = new Map();\r\n    bossAtlasMap: Map<string, SpriteAtlas> = new Map();\r\n    unitAtlas: SpriteAtlas= null;\r\n    _bossArr: BossBase[] = [];\r\n    fireParticle: Node= null;\r\n    skelDataMap: Map<string, sp.SkeletonData> = new Map();\r\n    bossResFinish: boolean = false;\r\n    _fireParticlePool: NodePool = new NodePool();\r\n    _isBossWarning: boolean = false;\r\n    _smokeSkelData: sp.SkeletonData= null;\r\n    _mainStage: number = -1;\r\n    _subStage: number = -1;\r\n    m_bossResStage: number = 0;\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig(){\r\n        let bossTbDatas = MyApp.lubanTables.TbBoss.getDataList();\r\n        for (let bossTbData of bossTbDatas) {\r\n            const bossData = new BossData();\r\n            bossData.loadJson(bossTbData);\r\n            let bossList = this._bossDatas.get(bossData.id);\r\n            if (!bossList) {\r\n                bossList = [];\r\n                this._bossDatas.set(bossData.id, bossList);\r\n            }\r\n            bossList.push(bossData);\r\n        }\r\n\r\n        let unitTbDatas = MyApp.lubanTables.TbUnit.getDataList();\r\n        for (let unitTbData of unitTbDatas) {\r\n            const unitData = new UnitData();\r\n            unitData.loadJson(unitTbData);\r\n            this._unitDatas.set(unitData.id, unitData);\r\n        }\r\n    }\r\n\r\n\r\n    async preLoad() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.spine_boss_smoke, sp.SkeletonData, (error,data) => {\r\n            this._smokeSkelData = data;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        this.unitAtlas = await MyApp.resMgr.loadAsync(GameResourceList.atlas_boss_unit,SpriteAtlas);\r\n        GameIns.battleManager.checkLoadFinish();\r\n    }\r\n\r\n\r\n    mainReset() {\r\n        this.subReset();\r\n        this._mainStage = -1;\r\n\r\n        if (!GameConst.Cache) {\r\n            if (this._smokeSkelData) {\r\n                MyApp.resMgr.releaseAssetByForce(this._smokeSkelData);\r\n                this._smokeSkelData = null;\r\n            }\r\n            if (this.unitAtlas) {\r\n                MyApp.resMgr.releaseAssetByForce(this.unitAtlas);\r\n                this.unitAtlas = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        for (const boss of this._bossArr) {\r\n            boss.willDestroy();\r\n            boss.node.parent = null;\r\n            setTimeout(() => {\r\n                boss.node.destroy();\r\n            }, 1000);\r\n        }\r\n        this._bossArr = [];\r\n\r\n        // 清理骨骼动画资源\r\n        this.skelDataMap.forEach((data, key) => {\r\n            MyApp.resMgr.releaseAssetByForce(data);\r\n        });\r\n        this.skelDataMap.clear();\r\n\r\n        // 清理图集资源\r\n        this.bossAtlasMap.forEach((atlas, key) => {\r\n            MyApp.resMgr.releaseAssetByForce(atlas);\r\n        });\r\n        this.bossAtlasMap.clear();\r\n    }\r\n\r\n\r\n    /**\r\n     * 加载 Boss 资源\r\n     * @param bossType Boss 类型\r\n     * @param bossId Boss ID\r\n     * @param callback 回调函数\r\n     * @param isPreload 是否为预加载\r\n     */\r\n    async loadBossRes(bossType: number, bossId: number, callback: Function | null = null, isPreload: boolean = false): Promise<boolean> {\r\n        try {\r\n            Tools.warn(\"Loading Boss Resources\", bossType, bossId);\r\n\r\n            if (!isPreload) {\r\n                this.bossResFinish = false;\r\n                // GameFunc.showUILoadingForDelay(5);\r\n            }\r\n\r\n            switch (bossType) {\r\n                case 100:\r\n                    // 加载普通 Boss\r\n                    const bossDatas = this.getBossDatas(bossId);\r\n                    for (const bossData of bossDatas) {\r\n                        for (const unitId of bossData.units) {\r\n                            const unitData = this.getUnitData(unitId);\r\n                            if (unitData.anim) {\r\n                                const skelData = await MyApp.resMgr.loadAsync(\"Game/spine/\"+unitData.anim,sp.SkeletonData);\r\n                                if (!skelData) {\r\n                                    if (!isPreload) {\r\n                                        // GameFunc.showUILoadErr(() => {\r\n                                            this.loadBossRes(bossType, bossId, callback);\r\n                                        // });\r\n                                    }\r\n                                    return false;\r\n                                }\r\n                                if (!isPreload) {\r\n                                    this.skelDataMap.set(unitData.anim, skelData);\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    break;\r\n\r\n                default:\r\n                    Tools.error(\"Unknown Boss Type:\", bossType);\r\n                    return false;\r\n            }\r\n\r\n            if (!isPreload) {\r\n                this.bossResFinish = true;\r\n                // GameFunc.hideUILoading();\r\n            }\r\n\r\n            if (callback) {\r\n                callback();\r\n            }\r\n\r\n            return true;\r\n        } catch (error) {\r\n            Tools.error(\"Error loading Boss resources:\", error);\r\n            if (!isPreload) {\r\n                // GameFunc.showUILoadErr(() => {\r\n                    this.loadBossRes(bossType, bossId, callback);\r\n                // });\r\n            }\r\n            return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._bossArr.length; i++) {\r\n            const boss = this._bossArr[i];\r\n            if (boss.removeAble) {\r\n                this.removeBoss(boss);\r\n                i--;\r\n            } else {\r\n                boss.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始 Boss 战斗\r\n     */\r\n    bossFightStart() {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                boss.startBattle();\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加 Boss\r\n     * @param bossType Boss 类型\r\n     * @param bossId Boss ID\r\n     */\r\n    addBoss(bossType: number, bossId: number): BossBase | null {\r\n        switch (bossType) {\r\n            case 100:\r\n                const bossDatas = this.getBossDatas(bossId);\r\n                return this._createBoss(bossDatas);\r\n\r\n            default:\r\n                Tools.error(\"Unknown Boss Type:\", bossType);\r\n                return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除 Boss\r\n     * @param boss 要移除的 Boss\r\n     */\r\n    removeBoss(boss: BossBase) {\r\n        boss.node.y = 1000;\r\n        Tools.arrRemove(this._bossArr, boss);\r\n        boss.node.parent = null;\r\n        boss.node.destroy();\r\n    }\r\n\r\n    /**\r\n     * 获取 Boss 图集\r\n     * @param atlasName 图集名称\r\n     */\r\n    async getBossAtlas(atlasName: string): Promise<SpriteAtlas | null> {\r\n        let atlas = this.bossAtlasMap.get(atlasName);\r\n        if (!atlas) {\r\n            atlas = await MyApp.resMgr.loadAsync(atlasName, SpriteAtlas);\r\n            if (atlas) {\r\n                this.bossAtlasMap.set(atlasName, atlas);\r\n            }\r\n        }\r\n        return atlas;\r\n    }\r\n\r\n    /**\r\n     * 设置 Boss 的精灵帧\r\n     * @param sprite 精灵组件\r\n     * @param frameName 精灵帧名称\r\n     */\r\n    setBossFrame(sprite: Sprite, frameName: string) {\r\n        if (sprite && this.unitAtlas) {\r\n            sprite.spriteFrame = this.unitAtlas.getSpriteFrame(frameName);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 显示 Boss 警告\r\n     */\r\n    showBossWarning() {\r\n        this._isBossWarning = true;\r\n    }\r\n\r\n    /**\r\n     * 获取所有 Boss\r\n     */\r\n    get bosses(): BossBase[] {\r\n        return this._bossArr;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已结束\r\n     */\r\n    isBossOver(): boolean {\r\n        return this._bossArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已死亡\r\n     */\r\n    isBossDead(): boolean {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * 获取烟雾骨骼数据\r\n     */\r\n    get smokeSkelData(): sp.SkeletonData | null {\r\n        return this._smokeSkelData;\r\n    }\r\n\r\n\r\n    /**\r\n     * 获取单位数据\r\n     * @param unitId 单位 ID\r\n     */\r\n    getUnitData(unitId: number): UnitData | undefined {\r\n        return this._unitDatas.get(unitId);\r\n    }\r\n\r\n    /**\r\n     * 获取 Boss 数据\r\n     * @param bossId Boss ID\r\n     */\r\n    getBossDatas(bossId: number): BossData[] | undefined {\r\n        return this._bossDatas.get(bossId);\r\n    }\r\n\r\n    /**\r\n     * 根据 ID 创建 Boss\r\n     * @param bossId Boss ID\r\n     */\r\n    createBossById(bossId: number): BossEntity | null {\r\n        const bossDatas = this.getBossDatas(bossId);\r\n        if (bossDatas) {\r\n            return this._createBoss(bossDatas);\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * 创建普通 Boss\r\n     * @param bossDatas Boss 数据\r\n     */\r\n    _createBoss(bossDatas: BossData[]): BossEntity {\r\n        const node = new Node(\"boss\");\r\n        BattleLayer.me.addEnemy(node);\r\n\r\n        const boss = node.addComponent(BossEntity);\r\n        boss.init(bossDatas);\r\n        boss.new_uuid = GameFunc.uuid;\r\n        this._bossArr.push(boss);\r\n\r\n        return boss;\r\n    }\r\n\r\n    /**\r\n     * 暂停所有 Boss\r\n     */\r\n    pauseBoss() {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead && boss.pause) {\r\n                boss.pause();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 恢复所有 Boss\r\n     */\r\n    resumeBoss() {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead && boss.resume) {\r\n                boss.resume();\r\n            }\r\n        }\r\n    }\r\n}"]}