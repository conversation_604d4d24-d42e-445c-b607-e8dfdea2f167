System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, GameIns, SingletonBase, MyApp, GameResourceList, _dec, _class, _crd, ccclass, GameResManager;

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      SingletonBase = _unresolved_3.SingletonBase;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      GameResourceList = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6f5366ikdNAN4RZ9Iw8WxM3", "GameResManager", undefined);

      __checkObsolete__(['_decorator', 'NodePool', 'instantiate', 'Prefab', 'Node']);

      ({
        ccclass
      } = _decorator);

      _export("default", GameResManager = (_dec = ccclass('GameResManager'), _dec(_class = class GameResManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.frameAnim = null;
        }

        async preload() {
          if (!this.frameAnim) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            this.frameAnim = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).FrameAnim, Prefab);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9e4d7139f19064cd0f72f8ed0fc1be371cdd966d.js.map