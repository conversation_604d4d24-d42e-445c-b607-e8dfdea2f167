import { _decorator, CCInteger, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCFloat } from 'cc';
import { LevelEditorBaseUI } from './LevelEditorBaseUI';
import { LevelDataElem } from '../../scripts/leveldata/leveldata';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('LevelEditorElemUI')
@executeInEditMode()
export class LevelEditorElemUI extends Component {
    @property(CCFloat)
    public get time(): number {
        const layerNode = this.node.parent?.parent
        if (!layerNode) {
            return 0;
        }
        const rootNode = layerNode.parent.parent
        if (!rootNode) {
            return 0;
        }
        const baseUI = rootNode.getComponent(LevelEditorBaseUI)
        if (!baseUI) {
            return 0;
        }
        const layer = baseUI.floorLayers.find((layer) => layer.node == layerNode) || baseUI.skyLayers.find((layer) => layer.node == layerNode)
        if (!layer) {
            return 0;
        }
        return this.node.position.y / layer.speed;
    }
    public elemID = "";
    protected onLoad(): void {
        if (this.elemID == "") {
            this.elemID = this.uuid;
        }
    }
    public initByLevelData(data: LevelDataElem) {
        this.node.setPosition(data.position.x, data.position.y);
        this.elemID = data.elemID;
        this.node.name = data.name;
    }
    public fillLevelData(data: LevelDataElem) {
        data.position = new Vec2(this.node.position.x, this.node.position.y);
        data.elemID = this.elemID;
        data.name = this.node.name;
    }
}