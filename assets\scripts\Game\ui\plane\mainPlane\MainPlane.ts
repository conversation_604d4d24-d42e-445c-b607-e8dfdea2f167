import { _decorator, Component, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, v2, SpriteFrame, size, SpriteAtlas } from "cc";
import Plane from "../Plane";
import { GameConst } from "../../../const/GameConst";
import { GameIns } from "../../../GameIns";
import { MainData } from "../../../data/MainData";
import GameEnum from "../../../const/GameEnum";
import BattleLayer from "../../layer/BattleLayer";
import GameMapRun from "../../map/GameMapRun";
import { GameFunc } from "../../../GameFunc";
import FireShells from "./FireShells";
import Bullet from "../../bullet/Bullet";
import EnemyEntity from "../enemy/EnemyEntity";
import BossUnit from "../boss/BossUnit";
import EffectLayer from "../../layer/EffectLayer";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import { ColliderGroupType } from "../../../collider-system/FCollider";
import { MyApp } from "db://assets/scripts/MyApp";
import GameResourceList from "../../../const/GameResourceList";

const { ccclass, property } = _decorator;


/**
 * 动画状态枚举
 */
enum AnimState {
    idle = 0,             // 空闲状态
    crazy = 1,            // 疯狂状态
    willCancelCrazy = 2   // 即将取消疯狂状态
}

@ccclass("MainPlane")
export class MainPlane extends Plane {

    static PrefabName = "MainPlane";

    @property(Sprite)
    streak: Sprite = null;

    @property(Node)
    skin: Node = null;
    @property(Node)
    hpBar: Node = null;

    @property(sp.Skeleton)
    blast: sp.Skeleton = null;

    @property(Animation)
    attspeAnim: Animation = null;

    @property(Animation)
    scrAnim: Animation = null;

    @property(Animation)
    auxAnim: Animation = null;

    // 血条相关
    hpbarBack = null; // 血条背景
    hpbarMid = null; // 血条中间部分
    hpbarFont = null; // 血条前景
    hpfont = null; // 血量文字

    hpMidActin = null; // 血条动画

    m_fireAnim: Node[] = []; // 射击动画数组

    // // 飞机状态
    m_screenDatas = []; // 屏幕数据
    m_moveEnable = true; // 是否允许移动
    m_config; // 飞机配置
    m_collideComp: FBoxCollider = null; // 碰撞组件
    mainData: MainData;
    m_fires = []; // 射击点数组

    // // 飞机状态
    m_fireState = null; // 射击状态

    m_animState: AnimState = AnimState.idle; // 动画状态

    _hurtActTime = 0; // 受伤动画时间
    _hurtActDuration = 0.5; // 受伤动画持续时间
    /**
     * 生命周期方法：onLoad
     * 初始化主飞机的配置和数据
     */
    onLoad() {
        this.m_config = GameIns.mainPlaneManager._mainConfig; // 获取主飞机的配置记录
        this.mainData = GameIns.mainPlaneManager.mainData; // 获取主飞机的数据


        this.hpbarBack = this.hpBar.getChildByName("hpbarback").getComponent(Sprite);
        this.hpbarMid = this.hpBar.getChildByName("hpbarmid").getComponent(Sprite);
        this.hpbarFont = this.hpBar.getChildByName("hpbarfont").getComponent(Sprite);
        this.hpfont = this.hpBar.getChildByName("hpfont").getComponent(Label);

        this.m_collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.m_collideComp.init(this,size(40,40)); // 初始化碰撞组件
        this.m_collideComp.groupType = ColliderGroupType.PLAYER;
    }

    /**
     * 生命周期方法：start
     * 初始化主飞机的组件和状态
     */
    start() {
        this.enemy = false;
        GameIns.gameDataManager.battlePlaneActive = true;

        this.m_collideComp.isEnable = true;


        // 初始化动画状态
        this.m_animState = AnimState.idle;

        // 初始化所有组件
        this.m_comps.forEach((comp) => {
            comp.init(this);
        });

        // 初始化飞机
        this.initPlane();

        // 禁用射击
        this.setFireEnable(false);
    }

    update(dt) {
        if (!GameConst.GameAble) return;

        // 限制帧率
        if (dt > 0.2) dt = 0.016666666666667;

        // 游戏状态为战斗时更新逻辑
        if (
            GameIns.gameRuleManager.gameState === GameEnum.GameState.Battle &&
            GameIns.mainPlaneManager.fireEnable
        ) {
            // 更新所有组件
            this.m_comps.forEach((comp) => {
                comp.update(dt);
            });
        }
        // 更新受伤动画时间
        this._hurtActTime += dt;
    }

    /**
     * 初始化主飞机
     */
    async initPlane() {
        // 加载飞机资源
        await MyApp.resMgr.loadAsync(GameResourceList.atlas_mainPlane + this.m_config.type,SpriteAtlas);

        // 添加火焰动画
        await this.addFireAnim();

        // 获取火力状态
        this.getFireState();

        // 根据屏幕等级更改屏幕数据
        this.changeScreenLv(this.mainData.screenLv);

        // 初始化图片
        this.initPic();
    }

    /**
 * 初始化变形图片
 */
    async initPic() {
        const attspe = this.attspeAnim.node.getChildByName("attspe");
        const atlas = await MyApp.resMgr.loadAsync(GameResourceList.atlas_mainPlane + this.m_config.type, SpriteAtlas);
        attspe.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[1]}1`);


        const position = this.m_config.transExt[0].split(",");
        attspe.setPosition(Number(position[0]), Number(position[1]))

        this.scrAnim.node.children.forEach(async (child) => {
            const scr = child.getChildByName("scr");
            scr.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[2]}1`);
            const pos = this.m_config.transExt[1].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]))
        });

        this.auxAnim.node.children.forEach(async (child) => {
            const aux = child.getChildByName("aux");
            aux.getComponent(Sprite).spriteFrame = atlas.getSpriteFrame(`${this.m_config.transSrc[3]}1`);
            const pos = this.m_config.transExt[2].split(",");
            child.setPosition(Number(pos[0]), Number(pos[1]))
        });
    }

    /**
 * 改变屏幕等级
 * @param {number} level 屏幕等级
 */
    changeScreenLv(level) {
        if (level === 0) return;

        this.m_screenDatas = [];
        const attackKey = "shiftingatk";
        const attackData = this.m_config[`${attackKey}${level}`];

        for (let i = 0; i < attackData.length; i += 8) {
            const screenData = attackData.slice(i, i + 8);
            this.m_screenDatas.push(screenData);
        }


        this.m_screenDatas.forEach((data, index) => {
            if (this.m_fires[index] == null) {
                this.createAttackPoint(data);
            } else {
                this.changeScreen(index, data);
            }
        });

        for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {
            this.changeScreen(i, null);
        }
    }

    /**
     * 添加发射火焰动画
     */
    async addFireAnim() {
        for (let i = 0; i < this.m_config.zjdmtxzb.length; i += 2) {
            const x = this.m_config.zjdmtxzb[i];
            const y = this.m_config.zjdmtxzb[i + 1];

            const fireNode = new Node("fireNode");
            fireNode.addComponent(UITransform);
            fireNode.addComponent(UIOpacity)
            fireNode.setPosition(Number(x), Number(y));
            fireNode.parent = this.skin;
            this.m_fireAnim.push(fireNode);
            fireNode.getComponent(UIOpacity).opacity = 0;

            const skeletonData = await MyApp.resMgr.loadAsync(GameResourceList.spine_mainfire,sp.SkeletonData);
            const skeleton = fireNode.addComponent(sp.Skeleton);
            skeleton.skeletonData = skeletonData;
            skeleton.setAnimation(0, "play", true);
        }
    }

    initBattle() {
        this.node.active = true;
        // 获取火力状态
        this.getFireState();

        this.m_collideComp.isEnable = false;
        this.UpdateHp();
    }

    /**
 * 主飞机入场动画
 */
    planeIn(): void {
        const self = this;

        const frameTime = GameConst.ActionFrameTime;

        // 播放入场音效
        // frameWork.audioManager.playEffect("planeFristIn");

        // 设置初始位置和状态
        this.node.getComponent(UIOpacity).opacity = 255;
        this.node.parent = BattleLayer.me.selfPlaneLayer;
        this.skin.getComponent(UIOpacity).opacity = 255;
        this.skin.setScale(1, 1)
        let posY = -view.getVisibleSize().height - (this.m_config.type === 711 ? 1000 : 80);
        this.node.setPosition(0, posY)
        this.node.setScale(GameIns.battleManager.getRatio(), GameIns.battleManager.getRatio());
        this.stopFire();
        this.m_moveEnable = false;
        Tween.stopAllByTarget(this.node)

        // 地图加速 TODO

        // 飞机入场动画
        this.scheduleOnce(() => {
            const targetY = -view.getVisibleSize().height * 0.7;
            const targetX = this.node.position.x;
            tween(this.node)
                .to(20 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY - 17) })
                .to(11 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 57) })
                .to(10 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY + 76) })
                .to(27 * frameTime / GameIns.battleManager.animSpeed, { position: v3(targetX, targetY) })
                .call(() => {
                    self.begine();
                })
                .start();

            tween(this.skin)
                .to(20 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.9, 1.9) })
                .to(11 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1.4, 1.4) })
                .to(10 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })
                .to(27 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })
                .start();

            if (this.hpBar) {
                tween(this.hpBar.getComponent(UIOpacity))
                    .to(0, { opacity: 0 })
                    .delay(31 * frameTime / GameIns.battleManager.animSpeed)
                    .to(10 * frameTime / GameIns.battleManager.animSpeed, { opacity: 255 })
                    .start();
            }

            this.scheduleOnce(() => {
                tween(this.streak.node)
                    .to(9 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 4) })
                    .to(7 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 2) })
                    .to(5 * frameTime / GameIns.battleManager.animSpeed, { scale: v3(1, 1) })
                    .call(() => {
                        self.addStreak();
                    })
                    .start();
            }, 2 * frameTime / GameIns.battleManager.animSpeed);
        }, 7 * frameTime / GameIns.battleManager.animSpeed);
    }

    /**
     * 退出战斗
     */
    battleQuit() {
        this.mainData.die = false;
        // GameIns.gameDataManager.battlePlaneActive = true;

        // 重置飞机状态
        this.skin.getComponent(UIOpacity).opacity = 255;
        // this.suitEffect.active = false;
        // if (this.downSuitCall) this.downSuitCall();
        // GameIns.mainPlaneManager.hideSkillNode();
        // this.quitGameSetPic();
        // this.clearMechaOverPic(true);

        // 重置动画节点位置
        // this.skinAnim.node.y = this.initPosSkinAnim;
        // this.mechaAnimNode.y = this.initPosMechaAnim;
        // this.suitAnimBot.y = this.initPosSuitBot;
        // this.suitAnimTop.y = this.initPosSuitTop;
    }


    /**
     * 减少血量
     * @param {number} damage 受到的伤害值
     */
    cutHp(damage) {
        GameIns.mainPlaneManager.hurtTotal += damage;
        const newHp = GameIns.mainPlaneManager.mainData.hp - damage;
        GameIns.mainPlaneManager.mainData.hp = Math.max(0, newHp);

        if (newHp < 0) {
            GameIns.mainPlaneManager.hurtTotal += newHp;
        }

        this.UpdateHp();;

        if (GameIns.mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {
            this.toDie();
        }
    }

    /**
     * 增加血量
     * @param {number} heal 恢复的血量值
     */
    addHp(heal) {
        GameIns.mainPlaneManager.mainData.hp = Math.min(
            GameIns.mainPlaneManager.mainData.maxhp,
            GameIns.mainPlaneManager.mainData.hp + heal
        );
        this.UpdateHp();;
    }

    /**
     * 碰撞处理
     * @param {Object} collision 碰撞对象
     */
    onCollide(collision) {
        // if (this.m_skill && this.m_skill.invincible) return;

        let damage = 0;
        if (collision.entity instanceof Bullet) {
            damage = collision.entity.getAttack(this);
        } else if (
            collision.entity instanceof EnemyEntity ||
            collision.entity instanceof BossUnit
        ) {
            damage = collision.entity.getColliderAtk();
        }

        if (damage > 0) {
            this.cutHp(damage);
            this._playHurtAnim();
            if (GameIns.mainPlaneManager.mainData.hp <= 0 && !this.mainData.die) {
                this.toDie();
            }
        }
    }

    private _playHurtAnim() {
        if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0;
            // 显示红屏效果
            EffectLayer.me.showRedScreen();
        }
    }

    /**
     * 控制飞机移动
     * @param {number} moveX 水平方向的移动量
     * @param {number} moveY 垂直方向的移动量
     */
    onControl(posX, posY) {
        if (!this.mainData.die && this.m_moveEnable) {
            // 限制飞机移动范围
            posX = Math.min(360, posX);
            posX = Math.max(-360, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-GameConst.ViewHeight, posY);
            this.node.setPosition(posX, posY);
        }
    }

    /**
     * 更新血量显示
     */
    UpdateHp() {
        if (this.hpBar && this.hpbarFont && this.hpbarMid) {
            // 停止当前血条动画
            if (this.hpMidActin) {
                this.hpMidActin.stop();
                this.hpMidActin = null;
            }

            // 更新血条前景的填充范围
            this.hpbarFont.fillRange = GameIns.mainPlaneManager.mainData.hp / GameIns.mainPlaneManager.mainData.maxhp;

            // 计算血条动画时间
            const duration = Math.abs(this.hpbarMid.fillRange - this.hpbarFont.fillRange);

            // 血条中间部分的动画
            this.hpMidActin = tween(this.hpbarMid)
                .to(duration, { fillRange: this.hpbarFont.fillRange })
                .call(() => {
                    this.hpMidActin = null;
                })
                .start();

            // 更新血量文字
            this.hpfont.string = GameIns.mainPlaneManager.mainData.hp.toFixed(0);
        }
    }

    /**
     * 处理复活逻辑
     */
    onRelife() {
        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数
        this.relife(1); // 调用复活方法
    }

    /**
     * 执行复活
     * @param {number} reviveType 复活类型
     */
    relife(reviveType) {

        // this.playRelifeAim(); // 播放复活动画
        this.mainData.die = false; // 设置飞机为非死亡状态
        this.mainData.revive = true; // 设置复活状态
        this.scheduleOnce(() => {
            this.mainData.revive = false;
        }, 0.5);

        GameIns.gameDataManager.battlePlaneActive = true; // 激活主飞机
        GameIns.mainPlaneManager.mainData.hp = GameIns.mainPlaneManager.mainData.maxhp; // 恢复满血
        this.UpdateHp();; // 触发血量更新事件
    }
    /**
     * 获取火力状态
     */
    getFireState() {
        const atkChallenge = this.mainData.atkAddRatio;

        this.m_fireState = {
            attack: this.mainData.attack,
            attackLv: 0,
            hpAttackLv: 0,
            speedLv: 0,
            cirtLv: 0,//cirtLevel,
            catapultLv: 0,//SkillManager.me.getLv(SkillType.catapult),
            atkChallenge: atkChallenge,
            fireIntensify: this.mainData.intensifyAtk,
        };
    }

    /**
     * 获取攻击力
     * @returns {number} 当前攻击力
     */
    getAttack() {
        return this.m_fireState.attack;
    }

    toDie() {

        // 设置玩家状态为死亡
        this.mainData.die = true;

        // 禁用碰撞组件
        this.m_collideComp.isEnable = false;

        // 停止血条动画并设置血条为 0
        if (this.hpbarMid) {
            tween(this.hpbarMid).stop();
            this.hpbarMid.fillRange = 0;
        }

        // 播放死亡动画
        this._playDieAnim();

        // 设置战斗状态为不可用
        GameIns.gameDataManager.battlePlaneActive = false;
    }

    /**
     * 播放死亡动画
     */
    _playDieAnim() {
        this.blast.node.getComponent(UIOpacity).opacity = 255; // 显示爆炸效果
        this.blast.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调
        this.blast.setAnimation(0, "play", false); // 播放爆炸动画
    }

    /**
     * 主飞机死亡动画结束后的处理逻辑
     */
    _dieAnimEnd(): void {
        // 隐藏爆炸动画节点
        this.blast.node.getComponent(UIOpacity).opacity = 0;

        // // 如果主飞机还有剩余生命次数
        // if (this.mainData.lifeNum > 0) {
        //     // 减少生命次数
        //     this.mainData.lifeNum--;

        //     // 更新剩余生命次数到全局数据
        //     // GameIns.gameDataManager.setLifeNum(this.mainData.lifeNum);

        //     // 触发复活逻辑
        //     this.relife(0);
        // } else {
        // 如果没有剩余生命次数，检查是否可以复活
        // const reviveCount = GameIns.gameDataManager.reviveCount;
        // if (this.mainData.relifeNum - reviveCount <= 0) {
        // // 如果是远征模式，结束当前章节
        // if (ExpeditionManager.isExpedition) {
        //     ExpeditionManager.sectionOver(false);
        // } else {
        //     // 否则触发战斗失败逻辑
        GameIns.battleManager.battleFail();
        // }
        // } else {
        //     // 如果可以复活，触发战斗死亡逻辑
        //     GameIns.battleManager.battleDie();
        // }
        // }
    }

    /**
     * 启用或禁用火力
     * @param {boolean} enable 是否启用火力
     */
    setFireEnable(enable) {
        if (this.m_config && this.m_config.type === 710) return;

        if (enable) {
            this.m_fireAnim.forEach((anim) => {
                anim.getComponent(UIOpacity).opacity = 255; // 显示火力动画
            });
        } else {
            this.m_fireAnim.forEach((anim) => {
                anim.getComponent(UIOpacity).opacity = 0; // 隐藏火力动画
            });
        }
    }

    /**
     * 设置飞机是否可移动
     * @param {boolean} enable 是否可移动
     */
    setMoveAble(enable) {
        this.m_moveEnable = enable;
    }

    /**
     * 设置碰撞是否可用
     * @param {boolean} enable 是否启用碰撞
     */
    setColAble(enable) {
        this.m_collideComp.isEnable = enable;
    }

    /**
     * 开始射击
     */
    beginFire() {
        GameIns.mainPlaneManager.fireEnable = true;
    }

    /**
     * 停止射击
     */
    stopFire() {
        GameIns.mainPlaneManager.fireEnable = false;
    }

    /**
     * 开始战斗
     * @param {boolean} isContinue 是否继续战斗
     */
    begine(isContinue = false) {
        if (isContinue) {
            this.beginFire();
            this.m_moveEnable = true;
            if (this.m_collideComp) {
                this.m_collideComp.isEnable = true;
            }
        } else {
            GameIns.battleManager.onPlaneIn();
        }
    }


    /**
     * 改变屏幕上的火力点
     * @param {number} index 火力点索引
     * @param {Array|null} data 火力点数据
     */
    changeScreen(index, data) {
        if (data == null) {
            if (index < this.m_fires.length) {
                const fire = this.m_fires[index];
                fire.setData(null, this.m_fireState, false, this);
                fire.node.active = false;
            }
        } else {
            if (index < this.m_fires.length) {
                const fire = this.m_fires[index];
                fire.node.active = true;
                fire.setData(data, this.m_fireState, false, this);
            } else {
                this.createAttackPoint(data);
            }
        }
    }

    /**
     * 移除所有火力点
     */
    removeAllFire() {
        this.m_fires.forEach((fire) => {
            fire.setData(null, this.m_fireState, false, this);
        });
    }

    /**
     * 创建攻击点
     * @param {Array} data 攻击点数据
     * @returns {FireShells} 创建的攻击点
     */
    createAttackPoint(data) {
        const fireNode = new Node("fire");
        fireNode.parent = this.node;

        const fire = fireNode.addComponent(FireShells);
        fire.setData(data, this.m_fireState, false, this);

        this.m_fires.push(fire);
        return fire;
    }
    /**
     * 添加拖尾效果
     */
    addStreak() {
        // Tween.stopAllByTarget(this.streak.node)
        // const ani = new Tween(this.streak.node)
        //     .to(1, { opacity: 255, scale: v3(1 })
        //     .to(3, { opacity: 204, scale: 0.88 })
        //     .to(6, { opacity: 255, scale: 1 });

        // tween(this.streak.node).repeatForever(ani).start();
    }
}