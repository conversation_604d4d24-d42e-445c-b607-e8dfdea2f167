{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts"], "names": ["CircleZoomFly", "misc", "Vec2", "GameConst", "GameIns", "BaseComp", "constructor", "props", "m_nowR", "index", "initR", "trueR", "offsetR", "dir", "speedVec", "ZERO", "useSpeed", "m_angle", "m_mainEntity", "m_atkPos", "m_maxR", "m_speed", "m_beginTime", "m_changeTime", "paras", "para", "r", "difV", "maxR", "XV", "YV", "counts", "face", "onInit", "m_entity", "node", "scaleY", "initialve", "Math", "random", "spdiff", "setData", "angle", "mainEntity", "atkPos", "rotate", "degreesToRadians", "calculateSpeed", "deltaTime", "time", "accnumber", "layerSpeed", "sceneManager", "getLayerSpeed", "x", "y", "calculateAngle", "atkX", "atkY", "angleStep", "PI", "direction", "pow", "radius", "rotatedPos", "pos", "add", "setPosition", "angToDeg", "getTrueR", "targetPos", "sqrt", "calculateXYMove", "update", "GameAble", "abs"], "mappings": ";;;yHAQqBA,a;;;;;;;;;;;;;;;;;;;;;;;AAPZC,MAAAA,I,OAAAA,I;AAAUC,MAAAA,I,OAAAA,I;;AACVC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;;;;;;;;yBAIcL,a,GAAN,MAAMA,aAAN;AAAA;AAAA,gCAAqC;AA2BhDM,QAAAA,WAAW,CAACC,KAAD,EAAa;AACpB;AADoB,eA1BhBC,MA0BgB,GA1BC,CA0BD;AAAA,eAzBhBC,KAyBgB,GAzBA,CAyBA;AAAA,eAxBhBC,KAwBgB,GAxBA,CAwBA;AAAA,eAvBhBC,KAuBgB,GAvBA,CAuBA;AAAA,eAtBhBC,OAsBgB,GAtBE,CAsBF;AAAA,eArBhBC,GAqBgB,GArBF,CAqBE;AAAA,eApBhBC,QAoBgB,GApBCZ,IAAI,CAACa,IAoBN;AAAA,eAnBhBC,QAmBgB,GAnBCd,IAAI,CAACa,IAmBN;AAAA,eAlBhBE,OAkBgB,GAlBE,CAkBF;AAAA,eAjBhBC,YAiBgB;AAAA,eAhBhBC,QAgBgB,GAhBCjB,IAAI,CAACa,IAgBN;AAAA,eAfhBK,MAegB,GAfC,CAeD;AAAA,eAdhBC,OAcgB,GAdE,CAcF;AAAA,eAbhBC,WAagB,GAbM,CAaN;AAAA,eAZhBC,YAYgB,GAZO,CAYP;AAAA,eAXhBC,KAWgB;AAAA,eAFxBjB,KAEwB;AAEpB,eAAKA,KAAL,GAAaA,KAAb;AACA,gBAAMkB,IAAI,GAAGlB,KAAK,CAACkB,IAAnB;AACA,eAAKD,KAAL,GAAa;AACTE,YAAAA,CAAC,EAAED,IAAI,CAAC,CAAD,CADE;AAETE,YAAAA,IAAI,EAAEF,IAAI,CAAC,CAAD,CAFD;AAGTG,YAAAA,IAAI,EAAEH,IAAI,CAAC,CAAD,CAHD;AAITI,YAAAA,EAAE,EAAEJ,IAAI,CAAC,CAAD,CAJC;AAKTK,YAAAA,EAAE,EAAEL,IAAI,CAAC,CAAD,CALC;AAMTM,YAAAA,MAAM,EAAEN,IAAI,CAAC,CAAD,CANH;AAOTO,YAAAA,IAAI,EAAEP,IAAI,CAAC,CAAD;AAPD,WAAb;AASH;;AAEDQ,QAAAA,MAAM,GAAG;AACL,eAAKC,QAAL,CAAcC,IAAd,CAAmBC,MAAnB,GAA4B,CAAC,CAA7B;AACA,eAAKd,WAAL,GAAmB,CAAnB;AACA,eAAKC,YAAL,GAAoB,CAApB;AACA,eAAKF,OAAL,GAAe,KAAKd,KAAL,CAAW8B,SAAX,GAAuBC,IAAI,CAACC,MAAL,KAAgB,KAAKhC,KAAL,CAAWiC,MAAjE;AACA,eAAK1B,QAAL,GAAgB,IAAIZ,IAAJ,CAAS,CAAT,EAAY,KAAKmB,OAAjB,CAAhB;AACH;;AAEDoB,QAAAA,OAAO,CAACC,KAAD,EAAgBC,UAAhB,EAAiCjC,KAAjC,EAAgDkB,IAAhD,EAA8DjB,KAA9D,EAA6EiC,MAA7E,EAA2F;AAC9F,eAAK1B,YAAL,GAAoByB,UAApB;AACA,eAAK1B,OAAL,GAAeyB,KAAf;AACA,eAAK1B,QAAL,GAAgB,KAAKF,QAAL,CAAc+B,MAAd,CAAqB5C,IAAI,CAAC6C,gBAAL,CAAsB,KAAK7B,OAA3B,CAArB,CAAhB;AACA,eAAKP,KAAL,GAAaA,KAAb;AACA,eAAKU,MAAL,GAAcQ,IAAd;AACA,eAAKjB,KAAL,GAAaA,KAAb;AACA,eAAKQ,QAAL,GAAgByB,MAAhB;AACA,eAAKnC,KAAL,GAAa,CAAb;AACA,eAAKG,OAAL,GAAe,KAAKF,KAAL,GAAa,KAAKC,KAAjC;AACH;;AAEDoC,QAAAA,cAAc,CAACC,SAAD,EAAoB;AAC9B,cAAI,KAAK1B,WAAL,IAAoB,KAAKf,KAAL,CAAW0C,IAA/B,IAAuC,KAAK1B,YAAL,GAAoB,KAAKhB,KAAL,CAAW2C,SAA1E,EAAqF;AACjF,iBAAK3B,YAAL,IAAqByB,SAArB;AACA,iBAAKhC,QAAL,GAAgB,KAAKF,QAAL,CAAc+B,MAAd,CAAqB5C,IAAI,CAAC6C,gBAAL,CAAsB,KAAK7B,OAA3B,CAArB,CAAhB;AACH;;AAED,cAAIkC,UAAU,GAAG,CAAjB;;AACA,cAAI,KAAKjC,YAAT,EAAuB;AACnBiC,YAAAA,UAAU,GAAG;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,aAArB,CAAmC,KAAKnC,YAAxC,CAAb;AACH;;AAED,eAAKgB,QAAL,CAAcC,IAAd,CAAmBmB,CAAnB,IAAwB,KAAKtC,QAAL,CAAcsC,CAAd,GAAkBN,SAA1C;AACA,eAAKd,QAAL,CAAcC,IAAd,CAAmBoB,CAAnB,IAAwB,KAAKvC,QAAL,CAAcuC,CAAd,GAAkBP,SAAlB,GAA8BG,UAAU,GAAGH,SAAnE;AACH;;AAEDQ,QAAAA,cAAc,CAACR,SAAD,EAAoB;AAC9BA,UAAAA,SAAS,GAAG,iBAAZ,CAD8B,CACC;;AAC/B,gBAAMS,IAAI,GAAG,KAAKtC,QAAL,CAAcmC,CAA3B;AACA,gBAAMI,IAAI,GAAG,KAAKvC,QAAL,CAAcoC,CAA3B;AAEA,eAAK1C,GAAL,GAAW,KAAKH,KAAL,GAAa,KAAKU,MAAlB,GAA2B,CAAC,CAA5B,GAAgC,CAA3C;AACA,eAAKZ,MAAL,GAAc,KAAKG,KAAL,GAAa,KAAKF,KAAlB,GAA0B,KAAKI,GAA/B,GAAqC,KAAKF,KAAxD;AAEA,gBAAMgD,SAAS,GAAI,KAAKtC,OAAL,GAAe,KAAKG,KAAL,CAAWG,IAA1B,GAAiCqB,SAAlC,GAA+C,KAAKrC,KAApD,IAA6D,MAAM2B,IAAI,CAACsB,EAAxE,CAAlB;AACA,gBAAMC,SAAS,GAAGvB,IAAI,CAACwB,GAAL,CAAS,CAAC,CAAV,EAAa,KAAKtC,KAAL,CAAWQ,IAAxB,CAAlB;AAEA,eAAKf,OAAL,IAAgB0C,SAAS,GAAGE,SAA5B;AACA,eAAK5C,OAAL,IAAgB,IAAhB,CAZ8B,CAYR;;AAEtB,gBAAM8C,MAAM,GAAG,KAAKvD,MAAL,GAAc,KAAKI,OAAlC;AACA,gBAAMoD,UAAU,GAAG,IAAI9D,IAAJ,CAAS6D,MAAT,EAAiB,CAAjB,EAAoBlB,MAApB,CAA2B5C,IAAI,CAAC6C,gBAAL,CAAsB,KAAK7B,OAA3B,CAA3B,CAAnB;AAEA,cAAIgD,GAAG,GAAGD,UAAU,CAACE,GAAX,CAAe,IAAIhE,IAAJ,CAASuD,IAAT,EAAeC,IAAf,CAAf,CAAV;AACA,eAAKxB,QAAL,CAAcC,IAAd,CAAmBgC,WAAnB,CAA+BF,GAAG,CAACX,CAAnC,EAAsCW,GAAG,CAACV,CAA1C;AACA,eAAKrB,QAAL,CAAcC,IAAd,CAAmBO,KAAnB,GAA4B,KAAKzB,OAAL,GAAe,GAAhB,GAAuB,MAAM,KAAKO,KAAL,CAAWQ,IAAnE;AAEA,eAAKvB,KAAL,IAAc,KAAKe,KAAL,CAAWG,IAAX,GAAkBqB,SAAhC;AACA,eAAKhC,QAAL,GAAgB,KAAKF,QAAL,CAAc+B,MAAd,CAAqB5C,IAAI,CAAC6C,gBAAL,CAAsB,KAAK7B,OAA3B,CAArB,CAAhB;AACH;;AAEDmD,QAAAA,QAAQ,CAAC1B,KAAD,EAAwB;AAC5B,iBAAOA,KAAK,IAAIJ,IAAI,CAACsB,EAAL,GAAU,GAAd,CAAZ;AACH;;AAEDS,QAAAA,QAAQ,CAACC,SAAD,EAA0B;AAC9B,iBAAOhC,IAAI,CAACiC,IAAL,CACHjC,IAAI,CAACwB,GAAL,CAAS,KAAK5B,QAAL,CAAcC,IAAd,CAAmBmB,CAAnB,GAAuBgB,SAAS,CAAChB,CAA1C,EAA6C,CAA7C,IACAhB,IAAI,CAACwB,GAAL,CAAS,KAAK5B,QAAL,CAAcC,IAAd,CAAmBoB,CAAnB,GAAuBe,SAAS,CAACf,CAA1C,EAA6C,CAA7C,CAFG,CAAP;AAIH;;AAEDiB,QAAAA,eAAe,CAACxB,SAAD,EAAoB;AAC/B,eAAK7B,QAAL,CAAcoC,CAAd,IAAmB,KAAK/B,KAAL,CAAWM,EAAX,GAAgB,iBAAnC;AACA,eAAKX,QAAL,CAAcmC,CAAd,IAAmB,KAAK9B,KAAL,CAAWK,EAAX,GAAgB,iBAAnC;AACH;;AAED4C,QAAAA,MAAM,CAACzB,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,sCAAU0B,QAAd,EAAwB;AACpB,iBAAKpD,WAAL,IAAoB0B,SAAS,GAAGA,SAAS,GAAG,GAAZ,GAAkB,iBAAlB,GAAsCA,SAAtE;;AAEA,gBAAIV,IAAI,CAACqC,GAAL,CAAS,KAAKnE,MAAL,GAAc,KAAKY,MAA5B,IAAsC,CAA1C,EAA6C;AACzC,mBAAKoC,cAAL,CAAoBR,SAApB;AACH,aAFD,MAEO;AACH,mBAAKD,cAAL,CAAoBC,SAApB;AACH;;AAED,iBAAKwB,eAAL,CAAqBxB,SAArB;AACH;AACJ;;AAlI+C,O", "sourcesContent": ["\r\nimport { misc, v2, Vec2 } from \"cc\";\r\nimport { GameConst } from \"../../const/GameConst\";\r\nimport { GameIns } from \"../../GameIns\";\r\nimport BaseComp from \"../base/BaseComp\";\r\n\r\n\r\n\r\nexport default class CircleZoomFly extends BaseComp {\r\n    private m_nowR: number = 0;\r\n    private index: number = 1;\r\n    private initR: number = 0;\r\n    private trueR: number = 0;\r\n    private offsetR: number = 0;\r\n    private dir: number = 1;\r\n    private speedVec: Vec2 = Vec2.ZERO;\r\n    private useSpeed: Vec2 = Vec2.ZERO;\r\n    private m_angle: number = 0;\r\n    private m_mainEntity: any;\r\n    private m_atkPos: Vec2 = Vec2.ZERO;\r\n    private m_maxR: number = 0;\r\n    private m_speed: number = 0;\r\n    private m_beginTime: number = 0;\r\n    private m_changeTime: number = 0;\r\n    private paras: {\r\n        r: number;\r\n        difV: number;\r\n        maxR: number;\r\n        XV: number;\r\n        YV: number;\r\n        counts: number;\r\n        face: number;\r\n    };\r\n    props: any;\r\n\r\n    constructor(props: any) {\r\n        super();\r\n        this.props = props;\r\n        const para = props.para;\r\n        this.paras = {\r\n            r: para[0],\r\n            difV: para[1],\r\n            maxR: para[2],\r\n            XV: para[3],\r\n            YV: para[4],\r\n            counts: para[5],\r\n            face: para[6],\r\n        };\r\n    }\r\n\r\n    onInit() {\r\n        this.m_entity.node.scaleY = -1;\r\n        this.m_beginTime = 0;\r\n        this.m_changeTime = 0;\r\n        this.m_speed = this.props.initialve + Math.random() * this.props.spdiff;\r\n        this.speedVec = new Vec2(0, this.m_speed);\r\n    }\r\n\r\n    setData(angle: number, mainEntity: any, initR: number, maxR: number, trueR: number, atkPos: Vec2) {\r\n        this.m_mainEntity = mainEntity;\r\n        this.m_angle = angle;\r\n        this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));\r\n        this.initR = initR;\r\n        this.m_maxR = maxR;\r\n        this.trueR = trueR;\r\n        this.m_atkPos = atkPos;\r\n        this.index = 0;\r\n        this.offsetR = this.initR - this.trueR;\r\n    }\r\n\r\n    calculateSpeed(deltaTime: number) {\r\n        if (this.m_beginTime >= this.props.time && this.m_changeTime < this.props.accnumber) {\r\n            this.m_changeTime += deltaTime;\r\n            this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));\r\n        }\r\n\r\n        let layerSpeed = 0;\r\n        if (this.m_mainEntity) {\r\n            layerSpeed = GameIns.sceneManager.getLayerSpeed(this.m_mainEntity);\r\n        }\r\n\r\n        this.m_entity.node.x += this.useSpeed.x * deltaTime;\r\n        this.m_entity.node.y += this.useSpeed.y * deltaTime - layerSpeed * deltaTime;\r\n    }\r\n\r\n    calculateAngle(deltaTime: number) {\r\n        deltaTime = 0.016666666666667; // Fixed time step\r\n        const atkX = this.m_atkPos.x;\r\n        const atkY = this.m_atkPos.y;\r\n\r\n        this.dir = this.initR > this.m_maxR ? -1 : 1;\r\n        this.m_nowR = this.trueR * this.index * this.dir + this.trueR;\r\n\r\n        const angleStep = (this.m_speed * this.paras.difV * deltaTime) / this.trueR * (180 / Math.PI);\r\n        const direction = Math.pow(-1, this.paras.face);\r\n\r\n        this.m_angle += angleStep * direction;\r\n        this.m_angle += 3600; // Prevent negative angles\r\n\r\n        const radius = this.m_nowR + this.offsetR;\r\n        const rotatedPos = new Vec2(radius, 0).rotate(misc.degreesToRadians(this.m_angle));\r\n\r\n        let pos = rotatedPos.add(new Vec2(atkX, atkY));\r\n        this.m_entity.node.setPosition(pos.x, pos.y);\r\n        this.m_entity.node.angle = (this.m_angle % 360) - 180 * this.paras.face;\r\n\r\n        this.index += this.paras.difV * deltaTime;\r\n        this.useSpeed = this.speedVec.rotate(misc.degreesToRadians(this.m_angle));\r\n    }\r\n\r\n    angToDeg(angle: number): number {\r\n        return angle * (Math.PI / 180);\r\n    }\r\n\r\n    getTrueR(targetPos: Vec2): number {\r\n        return Math.sqrt(\r\n            Math.pow(this.m_entity.node.x - targetPos.x, 2) +\r\n            Math.pow(this.m_entity.node.y - targetPos.y, 2)\r\n        );\r\n    }\r\n\r\n    calculateXYMove(deltaTime: number) {\r\n        this.m_atkPos.y += this.paras.YV * 0.016666666666667;\r\n        this.m_atkPos.x += this.paras.XV * 0.016666666666667;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameConst.GameAble) {\r\n            this.m_beginTime += deltaTime = deltaTime > 0.2 ? 0.016666666666667 : deltaTime;\r\n\r\n            if (Math.abs(this.m_nowR - this.m_maxR) > 1) {\r\n                this.calculateAngle(deltaTime);\r\n            } else {\r\n                this.calculateSpeed(deltaTime);\r\n            }\r\n\r\n            this.calculateXYMove(deltaTime);\r\n        }\r\n    }\r\n}"]}