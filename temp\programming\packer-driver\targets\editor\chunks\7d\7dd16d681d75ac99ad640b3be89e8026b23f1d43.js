System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, v2, Vec2, view, _GameConst, _crd, GameConst;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
      view = _cc.view;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "86dea8H4flPMosr9fpMflgS", "GameConst", undefined);

      __checkObsolete__(['Size', 'v2', 'Vec2', 'view']);

      _GameConst = class _GameConst {
        constructor() {
          this.Cache = false;
          this.ColliderDraw = false;
          this.ActionFrameTime = 0.0333;
          // 敌人相关
          this.EnemyPos = Vec2.ZERO;
          this.battleConfigUrl = "Game/jsons/normal/chapter_";
          this.GameAble = true;
        }

        // /**
        //  * 初始化视图相关数据
        //  */
        // constructor() {
        //     this.ViewSize = view.getVisibleSize();
        //     this.ViewHeight = this.ViewSize.height;
        //     this.ViewWidth = this.ViewSize.width;
        //     this.ViewCenter = v2(this.ViewWidth >> 1, this.ViewHeight >> 1);
        // }
        get ViewHeight() {
          return view.getVisibleSize().height;
        }

        get ViewSize() {
          return view.getVisibleSize();
        }

        get ViewWidth() {
          return view.getVisibleSize().width;
        }

        get ViewCenter() {
          return v2(this.ViewWidth / 2, this.ViewHeight / 2);
        }

      };

      _export("GameConst", GameConst = new _GameConst());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7dd16d681d75ac99ad640b3be89e8026b23f1d43.js.map